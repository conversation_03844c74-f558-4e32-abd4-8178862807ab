from enum import IntEnum
# Owner
HEJIABEI = "hejiabei.oxep"
HANXIN = "hanxin.leslie"
LIULEI = "liulei.32"

# BYTEIO
BYTEIO_AUTHORIZATION = "M0YzNTQyODVGMTc0N0Y0MDVENkE4QzIxOTQzOTVCOTU="
APP_ID = 1233
BYTEIO_LOG_TYPE = ["mario_event"]
BYTEIO_EVENT_NAME = ["play_session_events"]

# DS性能监控
BYTEST_TOKEN = "d6f6520671da265214347824dafdd426"

# BYTEST配置
BYTEST_API_TOKEN = "ff4d4c901eb2f538cf8bdd6b151fd33b"

# 直播trace namespace
LIVE_TRACE_NAMESPACE = "live-trace"
# 直播trace性能指标 注意:请不要使用  event_key:*xxxx  类似的模糊查询!
LIVE_TRACE_PERF_INDEX = {
    "in_cap_fps": "event_key: push_stream AND user_id: *_0",
    "out_cap_fps": "event_key: push_stream AND user_id: *_0",
    "encode_fps": "event_key: push_stream AND user_id: *_0",
    "real_video_framerate": "event_key: push_stream AND user_id: *_0",
    "rtc_in_cap_fps": "event_key: rtc_push_stream AND user_id: *_0",
    "rtc_out_cap_fps": "event_key: rtc_push_stream AND user_id: *_0",
    "rtc_encode_fps": "event_key: rtc_push_stream AND user_id: *_0",
    "rtc_real_video_framerate": "event_key: rtc_push_stream AND user_id: *_0",
}

# 音视频trace namespace
RTC_TRACE_NAMESPACE = "abase_rtc_common_monitor_event_aggregation_dim"
# 音视频trace性能指标 注意:请不要使用  event_key:*xxxx  类似的模糊查询!
RTC_TRACE_PERF_INDEX = {
    "frame_rate_sent": "event_key: rtc_media_statistics AND direction: up AND media_type: video AND user_id: *_0",
    "fraction_lost": "event_key: rtc_media_statistics AND direction: up AND user_id: *_0",
    "rtt": "event_key: rtc_media_statistics AND user_id: *_0",
    "mediaBitratebps": "event_key: rtc_media_statistics AND direction: up AND media_type: video AND user_id: *_0",
    "frame_size_width": "event_key: rtc_media_statistics AND direction: up AND media_type: video AND user_id: *_0",
    "frame_size_height": "event_key: rtc_media_statistics AND direction: up AND media_type: video AND user_id: *_0"
}

class PlatformType(IntEnum):
    """平台类型"""
    UNKNOWN = 0
    ANDROID = 1
    IOS = 2
    WINDOWS = 3
    MACOS = 4
    LINUX = 5

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取平台名称"""
        try:
            return {
                cls.UNKNOWN: "Unknown",
                cls.ANDROID: "Android",
                cls.IOS: "iOS",
                cls.WINDOWS: "Windows",
                cls.MACOS: "macOS",
                cls.LINUX: "Linux"
            }[cls(value)]
        except ValueError:
            return "Unknown"


class PerfCollectMode(IntEnum):
    """性能采集方式"""
    WIRE_COLLECT = 1  # 有线连接采集
    WIRELESS_COLLECT = 2  # 无线连接采集
    
    @classmethod
    def get_name(cls, value: int) -> str:
        """获取采集模式名称"""
        try:
            return {
                cls.WIRE_COLLECT: "有线连接采集",
                cls.WIRELESS_COLLECT: "无线连接采集",
            }[cls(value)]
        except ValueError:
            return "Unknown"
        
class TaskType(IntEnum):
    """任务类型"""
    VERSION_REGRESSION = 1  # 版本回归
    LIBRA_EXPERIMENT = 2  # Libra实验

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取任务类型名称"""
        return {
            cls.VERSION_REGRESSION: "版本回归",
            cls.LIBRA_EXPERIMENT: "Libra实验"
        }.get(value, "Unknown")

class PerfToolType(IntEnum):
    """性能采集工具类型"""
    DS = 1  # DS性能采集工具
    GAMEPERF = 2  # GamePerf性能采集工具

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取性能工具类型名称"""
        try:
            return {
                cls.DS: "DS",
                cls.GAMEPERF: "GamePerf"
            }[cls(value)]
        except ValueError:
            return "Unknown"
    
class LibraHitType(IntEnum):
    """Libra命中类型"""
    DID = 1
    UID = 2
    
    @classmethod
    def get_name(cls, value: int) -> str:
        """获取命中类型名称"""
        try:
            return {
                cls.DID: "DID",
                cls.UID: "UID",
            }[cls(value)]
        except ValueError:
            return "Unknown"

class LibraGroupType(IntEnum):
    """Libra实验组类型"""
    EXPERIMENT = 1  # 实验组
    CONTROL = 2     # 对照组

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取实验组类型名称"""
        try:
            return {
                cls.EXPERIMENT: "实验组",
                cls.CONTROL: "对照组",
            }[cls(value)]
        except ValueError:
            return "Unknown"

class MetricCategory(IntEnum):
    """指标分类"""
    DS = 1          # DS性能数据
    GAMEPERF = 2    # GamePerf性能指标
    TRACE = 3       # Trace性能指标
    BYTEIO = 4      # ByteIO数据

    @classmethod
    def get_name(cls, value: int) -> str:
        """获取指标名称"""
        try:
            return {
                cls.DS: "DS性能数据",
                cls.GAMEPERF: "GamePerf性能指标",
                cls.TRACE: "VQoS Trace性能指标",
                cls.BYTEIO: "ByteIO数据"
            }[cls(value)]
        except ValueError:
            return "Unknown"
    
