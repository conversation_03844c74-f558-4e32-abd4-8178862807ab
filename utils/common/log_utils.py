import sys
from loguru import logger
from pathlib import Path
import traceback
from functools import wraps
import os


def level_color_patcher(record):
    level = record["level"].name
    color = {
        "INFO": "\033[34m",      # Blue
        "DEBUG": "\033[90m",     # Gray
        "WARNING": "\033[38;5;208m",  # Orange
        "ERROR": "\033[91m",     # Red
        "CRITICAL": "\033[95m"   # Magenta
    }.get(level, "")

    location = f"{record['module']}.{record['function']}:{record['line']}"
    record["extra"].update({
        "color_time":     f"{color}{record['time'].strftime('%Y-%m-%d %H:%M:%S')}\033[0m",
        "color_level":    f"{color}{level:<8}\033[0m",
        "color_location": f"{color}{location:<40}\033[0m",
        "color_message":  f"{color}{record['message']}\033[0m"
    })


class LoguruLogger:
    def __init__(self, log_dir: str = None):
        if log_dir is None:
            log_dir = os.getcwd()
        self.log_dir = os.path.realpath(log_dir)
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
        self._setup_logger()

    def _setup_logger(self):
        # 移除默认的sink以避免重复输出
        logger.remove()

        log_file = os.path.join(self.log_dir, "case.log")
        logger.configure(patcher=level_color_patcher)

        # 统一的彩色格式
        common_format = (
            "{extra[color_time]} "
            "[{extra[color_level]}] "
            "[{extra[color_location]}] - {extra[color_message]}"
        )

        # 添加文件sink
        logger.add(
            log_file,
            level="DEBUG",
            encoding="utf-8",
            rotation="1 day",    # 每天轮转一次
            retention="7 days",  # 保留7天的日志
            format=common_format,
            enqueue=True
        )

        # 添加控制台sink
        logger.add(
            sink=lambda msg: print(msg, end=""),
            level="DEBUG",
            colorize=True,
            format=common_format
        )


loguru_logger = LoguruLogger()
logger = logger


def log_exception(func):
    """
    Decorator to log any exception raised by the function and re-raise it.

    Args:
        func (Callable): The function to wrap.

    Returns:
        Callable: Wrapped function.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Exception occurred in {func.__name__}:\n{traceback.format_exc()}")
            raise
    return wrapper