from enum import Enum

import requests

from utils.common.log_utils import logger


class RequestMethod(Enum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    OPTIONS = "OPTIONS"
    HEAD = "HEAD"


class RequestBase:

    def send_request(self, method: str, url: str, headers: dict = None, data: dict = None, json: dict = None,
                     params: dict = None, cookies: dict = None, proxies: dict = None,
                     auth: tuple = None, timeout: float = None, verify: bool = None) -> requests.Response:
        """
        发送 HTTP 请求的方法。

        参数:
            url (str): 请求的 URL 地址。
            method (str): 请求的方法，例如 GET、POST、PUT、DELETE 等。
            headers (dict): 请求的头部信息。
            data (dict): 请求的数据（非 JSON 格式）。
            json (dict): 请求的数据（JSON 格式）。
            params (dict): 请求的参数。
            cookies (dict): 请求的 cookies。
            proxies (dict): 请求的代理服务器。
            auth (tuple): 请求的认证信息，格式为 (username, password)。
            timeout (float): 请求的超时时间（秒）。
            verify (bool): 是否验证 SSL 证书。

        返回:
            requests.Response: 包含了服务器响应的对象。
        """
        logger.debug(f"正在发送请求: {method} {url}")

        try:
            response = requests.request(
                method=method, url=url, headers=headers, data=data, json=json, params=params,
                cookies=cookies, proxies=proxies, auth=auth, timeout=timeout, verify=verify
            )

            if response.status_code == 200:
                logger.debug(f"请求成功 - 状态码: {response.status_code}")
            elif 0 <= response.status_code < 100:
                logger.debug(f"请求响应为无效状态码 - 状态码: {response.status_code}")
            elif 100 <= response.status_code < 200:
                logger.debug(f"请求响应为信息响应 - 状态码: {response.status_code}")
            elif 200 <= response.status_code < 300:
                logger.debug(f"请求响应为成功响应 - 状态码: {response.status_code}")
            elif 400 <= response.status_code < 500:
                logger.warning(f"客户端错误 - 状态码: {response.status_code}")
            elif 500 <= response.status_code < 600:
                logger.error(f"服务器错误 - 状态码: {response.status_code}")
            else:
                logger.debug(f"请求返回未知状态码 - 状态码: {response.status_code}")

            # logger.debug(f"响应详细信息 - Headers: {response.headers}, Content: {response.text[:1000]}")  # 截取前500字符

        except requests.RequestException as e:
            # 请求失败记录异常类型和状态码
            logger.error(f"请求失败 - URL: {url}, 错误类型: {type(e).__name__}, 错误信息: {e}")
            raise  # 重新抛出异常，供调用者处理

        return response


request_base = RequestBase()
