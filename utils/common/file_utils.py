import fnmatch
import importlib.util
import yaml
import json
import os
import platform
import shutil
import sys
import configparser
from pathlib import Path
from defines import BYTEST_API_TOKEN
from typing import Optional, Dict
from settings import PROJECT_ROOT
from utils.common.log_utils import logger


def get_task_json(task_json_path: Optional[str] = None, encoding: str = 'utf-8') -> Dict:
    """
    获取任务配置JSON数据
    Args:
        task_json_path: JSON文件路径，如果为None则使用默认路径
        encoding: 文件编码，默认utf-8
    Returns:
        任务配置数据字典
    """
    try:
        if not task_json_path:
            task_json_path = os.path.join(PROJECT_ROOT, "task.json")
            logger.debug(f"使用默认任务配置文件路径: {task_json_path}")

        if not os.path.isabs(task_json_path):
            logger.debug(f"文件路径 {task_json_path} 不是绝对路径")
            raise ValueError("文件路径必须是绝对路径")

        if not os.path.exists(task_json_path):
            logger.debug(f"文件 {task_json_path} 不存在")
            raise FileNotFoundError(f"文件不存在: {task_json_path}")

        logger.debug(f"开始读取文件: {task_json_path}")
        with open(task_json_path, 'r', encoding=encoding) as file:
            data = json.load(file)

        logger.debug(f"成功从文件 {task_json_path} 中加载数据")
        return data

    except (FileNotFoundError, json.JSONDecodeError, ValueError) as e:
        logger.error(f"读取任务配置文件失败: {str(e)}")
        return {}
    except Exception as e:
        logger.error(f"未知错误: {str(e)}")
        return {}
    

def yaml_reader(yaml_path, key):
    """
    读取 YAML 文件中的指定键值

    :param yaml_path: YAML 文件路径
    :param key: 要获取的键名
    :return: 键对应的值
    """
    try:
        logger.debug(f"开始读取YAML文件: {yaml_path}, 键: {key}")
        # 打开并读取 YAML 文件
        with open(yaml_path, "r", encoding="utf-8") as file:
            data = yaml.safe_load(file)
            value = data.get(key)
            if value is None:
                logger.warning(f"键 {key} 在文件 {yaml_path} 中不存在")
                return None
            else:
                logger.debug(f"成功读取键 {key} 的值：{value}")
                return value
    except FileNotFoundError:
        logger.error(f"文件 {yaml_path} 不存在")
        return None
    except IOError as e:
        logger.error(f"读取文件时发生错误: {e}")
        return None
    except Exception as e:
        logger.error(f"未知错误：{e}")
        return None


def create_proj_dirs(folder_path):
    """
    创建指定的文件夹。

    :param folder_path: 目标文件夹路径
    :return: {'success': bool, 'message': str}
    """
    logger.debug(f"准备创建文件夹: {folder_path}")
    if not folder_path or not isinstance(folder_path, str):
        logger.error("无效的文件夹路径")
        return None

    try:
        if os.path.exists(folder_path):
            logger.debug(f"文件夹 {folder_path} 已存在")
            return folder_path
        os.makedirs(folder_path)
        logger.debug(f"文件夹 {folder_path} 创建成功")
        return folder_path
    except PermissionError:
        logger.error(f"创建文件夹 {folder_path} 时权限被拒绝")
        return None
    except Exception as e:
        logger.error(f"创建文件夹 {folder_path} 失败: {str(e)}")
        return None


def find_files_by_extension(folder_path, extensions: list):
    """
    获取文件夹下一种或多种格式的所有文件（所有子文件夹）
    
    @param folder_path: 文件夹路径
    @param extensions: 后缀名列表
    @return: 符合条件的文件列表
    """
    logger.debug(f"开始在 {folder_path} 中查找后缀为 {extensions} 的文件")
    if not os.path.exists(folder_path):
        logger.debug(f"文件夹 {folder_path} 不存在")
        raise ValueError(f"指定的文件夹路径不存在：{folder_path}")

    file_list = []

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if not extensions or any(file.endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                logger.debug(f"找到符合条件的文件: {file_path}")
                file_list.append(file_path)

    file_list.sort()
    logger.debug(f"共找到 {len(file_list)} 个符合条件的文件")
    return file_list


def get_class_objects_from_file(file_path):
    """
    从给定的Python文件中获取所有定义的类对象。

    :param file_path: 字符串, Python文件的路径
    :return: 类对象列表
    """
    logger.debug(f"开始从文件 {file_path} 中获取类对象")
    # 用于存储所有类对象的列表
    class_objects = []

    # 动态导入模块
    spec = importlib.util.spec_from_file_location("module_name", file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)

    # 获取模块中的所有类对象
    for name in dir(module):
        obj = getattr(module, name)
        if isinstance(obj, type):
            logger.debug(f"找到类对象: {name}")
            class_objects.append(obj)

    logger.debug(f"共找到 {len(class_objects)} 个类对象")
    return class_objects


def clean_file(folder_path, patterns=None):
    """
    清除指定文件夹中的文件（包括所有子文件夹）。

    :param folder_path: 文件夹路径
    :param patterns: 匹配模式列表
    :return: 无返回值
    """
    logger.debug(f"开始清理文件夹 {folder_path}, 匹配模式: {patterns}")
    if patterns is None:
        patterns = []

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if any(fnmatch.fnmatch(file, pattern) for pattern in patterns):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    logger.debug(f"文件已删除: {file_path}")
                except FileNotFoundError:
                    logger.warning(f"文件不存在: {file_path}")
                except PermissionError:
                    logger.error(f"权限错误: {file_path}")
                except Exception as e:
                    logger.error(f"删除文件失败: {file_path} - 错误: {e}")


def get_python_path():
    logger.debug(f"获取Python路径: {sys.executable}")
    return sys.executable


def get_venv_path():
    """
    获取虚拟环境的路径。
    :return:
    """
    logger.debug(f"获取虚拟环境路径: {sys.prefix}")
    return sys.prefix


def get_adb_path():
    """
    获取adb工具的路径。
    :return:
    """
    try:
        logger.debug("开始查找adb路径")
        # 尝试使用shutil.which获取adb路径
        adb_path = shutil.which('adb')
        if adb_path and os.path.exists(adb_path):
            logger.debug(f"找到adb路径: {adb_path}")
            return adb_path

        system = platform.system().lower()
        logger.debug(f"当前操作系统: {system}")
        if system == 'windows':
            adb_path = os.path.join(get_venv_path(), 'android_device', 'adb', 'tools', 'windows', 'adb.exe')
        elif system == 'linux':
            adb_path = os.path.join(get_venv_path(), 'android_device', 'adb', 'tools', 'linux', 'adb')
        elif system == 'darwin':  # MacOS
            adb_path = os.path.join(get_venv_path(), 'android_device', 'adb', 'tools', 'darwin', 'adb')
        else:
            raise RuntimeError("不支持的操作系统")

        if os.path.exists(adb_path):
            logger.debug(f"找到adb路径: {adb_path}")
            return adb_path

    except FileNotFoundError:
        logger.error("请确保已安装adb工具")
    except RuntimeError as re:
        logger.error(f"错误: {re}")
    except Exception as e:
        logger.error(f"未知错误: {e}")

    raise FileNotFoundError("无法找到adb路径，请检查环境配置")


def get_bdc_path():
    """
    获取bdc工具的路径。
    :return:
    """
    try:
        logger.debug("开始查找bdc路径")
        bdc_path = shutil.which('bdc')
        if os.path.exists(bdc_path):
            logger.debug(f"找到bdc路径: {bdc_path}")
            return bdc_path

        venv_path = get_venv_path()
        bdc_path = os.path.join(venv_path, 'lib', f'python{sys.version_info.major}.{sys.version_info.minor}',
                                'site-packages', 'shoots_ios', 'tools', 'bdc', 'mac', 'bdc')

        if os.path.exists(bdc_path):
            logger.debug(f"找到bdc路径: {bdc_path}")
            return bdc_path

    except FileNotFoundError as e:
        logger.error(f"找不到文件或目录: {e}")

    except Exception as e:
        logger.error(f"获取bdc路径时发生错误: {e}")

    return None


def get_work_dir():
    work_dir = os.getcwd()
    logger.debug(f"获取当前工作目录: {work_dir}")
    return work_dir


def del_folder(folder_path):
    """
    删除指定文件夹及其下的所有文件和子文件夹。

    :param folder_path: 要删除的文件夹路径
    :return: 无返回值
    """
    try:
        logger.debug(f"准备删除文件夹: {folder_path}")
        shutil.rmtree(folder_path)
        logger.debug(f"文件夹 {folder_path} 已删除")
    except FileNotFoundError:
        logger.warning(f"文件夹 {folder_path} 不存在")
    except PermissionError:
        logger.error(f"权限错误: {folder_path}")
    except Exception as e:
        print(f"删除文件夹时发生错误: {e}")


def rename_file(old_name, new_name):
    """
    重命名文件
    :param old_name:
    :param new_name:
    :return:
    """
    try:
        logger.debug(f"准备将文件 {old_name} 重命名为 {new_name}")
        os.rename(old_name, new_name)
        logger.debug(f"文件 {old_name} 已成功重命名为 {new_name}")
    except FileNotFoundError:
        logger.error(f"文件 {old_name} 不存在")
    except FileExistsError:
        logger.error(f"文件 {new_name} 已存在")
    except Exception as e:
        logger.error(f"重命名文件时发生错误: {e}")

def move_file(src_path, dst_path):
    """
    移动文件
    """
    logger.debug(f"准备将文件从 {src_path} 移动到 {dst_path}")
    shutil.move(src_path, dst_path)
    logger.debug(f"文件 {src_path} 已成功移动到 {dst_path}")
    return dst_path


def write_bytest_config(url: str = None, token: str = None) -> None:
    """写入bytest配置文件
    
    将API配置信息写入到~/.bytest/bytest.ini文件中
    
    Args:
        url: API基础URL
        token: 认证token
    """
    if not url:
        url = "https://bytest.bytedance.net/auto_test/api/v1/"
    if not token:
        token = BYTEST_API_TOKEN
    
    # 创建配置解析器
    config = configparser.ConfigParser()
    config["api"] = {
        "url": url,
        "token": token
    }
    
    # 确保目录存在
    config_dir = os.path.expanduser("~/.bytest")
    Path(config_dir).mkdir(parents=True, exist_ok=True)
    
    # 写入配置文件
    config_path = os.path.join(config_dir, "bytest.ini")
    with open(config_path, "w", encoding="utf-8") as f:
        config.write(f)