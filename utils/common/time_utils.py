"""
时间工具模块

提供时间相关的工具函数
"""

from typing import Any


def convert_timestamp_to_digits(timestamp: Any, target_digits: int) -> int:
    """将时间戳转换为指定位数

    Args:
        timestamp: 原始时间戳（可能是秒、毫秒、微秒、纳秒级）
        target_digits: 目标时间戳位数（10=秒级, 13=毫秒级, 16=微秒级, 19=纳秒级）

    Returns:
        int: 转换后的指定位数时间戳

    Raises:
        ValueError: 当目标位数不支持时抛出异常
    """
    if timestamp is None:
        return 0

    timestamp = int(timestamp)

    # 支持的时间戳位数和对应的倍数
    scale_factors = {10: 1, 13: 1000, 16: 1000000, 19: 1000000000}

    if target_digits not in scale_factors:
        raise ValueError(f"不支持的目标位数: {target_digits}，支持的位数: {list(scale_factors.keys())}")

    # 获取当前时间戳位数并确定对应的倍数
    current_digits = len(str(timestamp))
    current_scale = next((scale for digits, scale in scale_factors.items() if current_digits <= digits), 1)

    if current_digits > 19:
        # 时间戳格式未知，按秒级处理
        current_scale = 1

    # 转换公式：(原始时间戳 / 当前倍数) * 目标倍数
    return (timestamp // current_scale) * scale_factors[target_digits]