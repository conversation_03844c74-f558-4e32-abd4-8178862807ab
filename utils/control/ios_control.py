import json
import os
import subprocess
import time

from requests import RequestException

from utils.common.file_utils import get_bdc_path, get_python_path
from utils.common.log_utils import logger
from utils.common.req_utils import request_base, RequestMethod


class iOSControl:
    def __init__(self, bdc_path=None):
        if not bdc_path:
            self.bdc_path = get_bdc_path()
        self.python_path = get_python_path()

    """共有方法"""

    def is_ios_device_online(self, udid: str) -> bool:
        """
        检查 iOS 设备是否在线。

        Args:
            udid (str): iOS 设备的唯一标识符。

        Returns:
            bool: 如果设备在列表中找到且已连接，返回 True；否则返回 False。
        """
        try:
            result = subprocess.run(
                [self.bdc_path, "list-devices"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode != 0:
                logger.warning(f"bdc command failed: {result.stderr.strip()}")
                return False

            for line in result.stdout.strip().splitlines():
                # 确保是非空且包含目标udid
                if udid in line:
                    if "connect_code:0" in line and "connect_msg:success" in line:
                        return True
                    else:
                        logger.info(f"[{udid}] 设备找到但未完全连接: {line}")
                        return False

            logger.info(f"[{udid}] 设备不在线")
            return False

        except subprocess.TimeoutExpired:
            logger.error("bdc 命令超时。")
            return False
        except FileNotFoundError:
            logger.error("bdc 未找到。请确保已安装并将其添加到 PATH。")
            return False
        except Exception as e:
            logger.exception(f"检查设备状态时发生意外错误: {e}")
            return False


    def inspect_app(self, ipa_path: str):
        """
        检查 IPA 文件并返回检查结果。

        :param ipa_path: IPA 文件路径
        :return:
        """
        logger.info("暂不支持 inspect_app")

    def get_package_name_by_app(self, ipa_path: str):
        """
        从 IPA 文件中获取包名。

        :param ipa_path: IPA 文件路径
        :return:
        """
        logger.info("暂不支持 get_package_name_by_app")

    def get_activity_name_by_app(self, ipa_path: str):
        """
        从 IPA 文件中获取启动活动名称。

        :param ipa_path: IPA 文件路径
        :return:
        """
        logger.info("暂不支持 get_activity_name_by_app")

    def get_app_version_by_app(self, ipa_path: str):
        """
        从 IPA 文件中获取应用版本号。

        :param ipa_path: IPA 文件路径
        :return:
        """
        logger.info("暂不支持 get_app_version_by_app")

    def repack_app(self, ipa_path: str, save_dir: str, **kwargs):
        """
        重打包 IPA 文件
        @param ipa_path: 原始 IPA 文件路径
        @param save_dir: 保存目录
        @return: 重打包后的 IPA 文件路径
        """
        if not os.path.exists(ipa_path) or not os.path.isfile(ipa_path) or not ipa_path.endswith(".ipa"):
            logger.error(f"IPA 文件不存在或不是有效的 IPA 文件: {ipa_path}")
            return None

        repack_ipa_path = ipa_path.replace(".ipa", "-repacked-signed.ipa")
        repack_app_name = os.path.basename(repack_ipa_path)

        if repack_app_name in os.listdir(save_dir):
            logger.info("重打包文件已存在")
            return repack_ipa_path

        provision_path = os.path.join(save_dir, "musically.mobileprovision")
        if "musically.mobileprovision" not in os.listdir(os.path.dirname(save_dir)):
            logger.info("未找到 musically.mobileprovision 文件，正在下载...")
            default_repackage_certificate = "https://tosv.boe.byted.org/obj/global-rtc-test-platform/certificates/com.zhiliaoapp.musically/musically.mobileprovision"
            repackage_certificate = kwargs.get("repackage_certificate") if not kwargs.get(
                "repackage_certificate") else default_repackage_certificate
            try:
                response = request_base.send_request(method=RequestMethod.GET.value, url=repackage_certificate)
                with open(provision_path, "wb") as f:
                    f.write(response.content)
            except RequestException as e:
                logger.error(f"下载 mobileprovision 失败: {e}")
                return None

        cmd = [self.python_path, "-m", "shoots_ios", "repack", ipa_path, "--sign-exclude",
               "AwemeBroadcastExtension.appex", "-p", provision_path]
        res = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
        if "repacking ok" in res.stdout:
            logger.info(f"重打包成功: {repack_ipa_path}")
            return repack_ipa_path

        logger.error(f"重打包失败: {res.stdout}")
        return None

    def get_current_wifi_name(self, udid: str):
        """
        获取当前设备连接的 WiFi 名称。

        :param udid: 设备唯一标识符
        :return: 当前 WiFi 名称或"未找到 SSID"
        """
        logger.info("暂不支持 get_current_wifi_name")

    def wireless_connect(self, udid: str):
        """
        无线连接设备
        @param udid: 设备的唯一标识符
        @return: 连接是否成功
        """
        if not self.ui_test_init(udid):
            logger.error(f"[{udid}] UI测试环境初始化失败，无法进行无线连接")
            return False
        try:
            subprocess.run([self.bdc_path, 'list-devices'], shell=False, check=True)
            set_connect_mode_cmd = [self.bdc_path, 'set-connect-mode', 'network', '-u', udid]
            set_connect_mode_result = subprocess.run(set_connect_mode_cmd, shell=False, capture_output=True, text=True,
                                                     check=True)
            get_connect_mode_cmd = [self.bdc_path, 'get-connect-mode', '-u', udid]
            get_connect_mode_result = subprocess.run(get_connect_mode_cmd, shell=False, capture_output=True, text=True,
                                                     check=True)

            if "network" in get_connect_mode_result.stdout:
                logger.info(f"[{udid}] 成功切换到网络连接模式")
                return udid
            else:
                logger.error(f"[{udid}] 切换到网络连接模式失败，当前模式: {get_connect_mode_result.stdout.strip()}, 设置命令输出: {set_connect_mode_result.stderr}")
                return False
        except Exception as e:
            logger.error(f"[{udid}] 设置网络连接模式时发生系统异常，错误类型: {type(e).__name__}, 错误信息: {str(e)}")
            return False

    def wireless_disconnect(self, udid: str) -> bool:
        """
        取消无线连接设备
        @param udid: 设备的唯一标识符
        @return: 连接是否成功
        """
        try:
            subprocess.run([self.bdc_path, 'list-devices'], shell=False, check=True)
            set_connect_mode_cmd = [self.bdc_path, 'set-connect-mode', 'usb', '-u', udid]
            set_connect_mode_result = subprocess.run(set_connect_mode_cmd, shell=False, capture_output=True, text=True)
            get_connect_mode_cmd = [self.bdc_path, 'get-connect-mode', '-u', udid]
            get_connect_mode_result = subprocess.run(get_connect_mode_cmd, shell=False, capture_output=True, text=True)

            if "usb" in get_connect_mode_result.stdout:
                logger.info(f"[{udid}] 成功切换回USB连接模式")
                return True
            else:
                logger.error(f"[{udid}] 切换到USB模式失败，当前模式: {get_connect_mode_result.stdout.strip()}, 设置命令输出: {set_connect_mode_result.stdout}")
                return False
        except Exception as e:
            logger.error(f"[{udid}] 切换USB连接模式时发生系统异常，错误类型: {type(e).__name__}, 错误信息: {str(e)}")
            return False

    def install_app(self, udid: str, ipa_path: str, **kwargs):
        """
        在指定设备上安装重打包后的应用
        @param ipa_path: 重打包后的 IPA 文件路径
        @param udid: 设备唯一标识符
        @return: None
        """
        if not ipa_path:
            logger.warning("IPA 路径为空，无法安装应用")
            return

        bundle_ids = ["com.zhiliaoapp.musically", "com.zhiliaoapp.musically.ep"]

        for _ in range(3):
            app_list_cmd = [self.bdc_path, "list-apps", "-u", udid, "-t", "user"]
            try:
                app_list_result = subprocess.run(app_list_cmd, capture_output=True, text=True, check=True, shell=False)
            except subprocess.CalledProcessError as e:
                logger.error(f"[{udid}] 获取应用列表失败: {e}")
                logger.error(f"[{udid}] {e.stderr}")
                continue

            for bundle_id in bundle_ids:
                if bundle_id in app_list_result.stdout:
                    uninstall_cmd = [self.bdc_path, "uninstall", "-u", udid, "-b", bundle_id]
                    try:
                        subprocess.run(uninstall_cmd, capture_output=True, text=True, check=True, shell=False)
                        logger.info(f"[{udid}] 卸载 {bundle_id} 成功")
                    except subprocess.CalledProcessError as e:
                        logger.error(f"[{udid}] 卸载 {bundle_id} 失败: {e}")
                        logger.error(f"[{udid}] {e.stderr}")

            install_cmd = [self.bdc_path, "install", ipa_path, "-u", udid]
            try:
                install_result = subprocess.run(install_cmd, capture_output=True, text=True, check=True, shell=False)
                if "successful" in install_result.stdout:
                    logger.info(f"[{udid}] 应用安装成功: {ipa_path}")
                    return
            except subprocess.CalledProcessError as e:
                logger.error(f"[{udid}] 安装应用失败: {e}")
                logger.error(f"[{udid}] {e.stderr}")

        logger.error(f"[{udid}] 应用安装尝试三次均失败: {ipa_path}")

    def get_battery_level(self, udid: str) -> int:
        """
        获取电量
        @param udid: 设备唯一标识符
        @return: 电量百分比
        """
        try:
            cmd = [self.bdc_path, "get-batteryinfo", "-u", udid]
            get_power_level_result = subprocess.run(cmd, capture_output=True, text=True, check=True, shell=False)
            stdout_lines = get_power_level_result.stdout.splitlines()
            for line in stdout_lines:
                if "BatteryCurrentCapacity:" in line:
                    power_str = line.split("BatteryCurrentCapacity:")[1].strip().split("%")[0]
                    power = int(power_str)
                    logger.info(f"[{udid}] 当前电量: {power}%")
                    return power

            raise ValueError("未找到电量信息")

        except subprocess.CalledProcessError as e:
            logger.error(f"[{udid}] 执行命令失败：{e}")
            return -1

        except ValueError as e:
            logger.error(f"[{udid}] 数据解析错误：{e}")
            return -1

    def check_battery_level(self, udid: str, target_power_level: int, wait_interval: int = None,
                            max_wait_time: int = None) -> bool:
        """
        检测电量是否达到指定水平
        :param udid: 设备的唯一标识符
        :param target_power_level: 目标电量百分比
        :param wait_interval:
        :param max_wait_time:
        :return: 电量是否达到目标水平
        """
        if wait_interval is None:
            wait_interval = 3 * 60
        if max_wait_time is None:
            max_wait_time = 30 * 60

        start_time = time.time()
        while True:
            current_power_level = self.get_battery_level(udid)
            if current_power_level == -1:
                logger.error(f"[{udid}] 无法获取电池电量")
                return False

            if current_power_level >= target_power_level:
                logger.info(f"[{udid}] 电量已达到 {target_power_level}%")
                return True

            elapsed_time = time.time() - start_time
            if elapsed_time >= max_wait_time:
                logger.error(f"[{udid}] 电量未达到 {target_power_level}%，已等待 {elapsed_time / 60:.2f} 分钟")
                return False

            logger.info(f"[{udid}] 当前电量为 {current_power_level}%，等待 {wait_interval / 60:.2f} 分钟")
            time.sleep(wait_interval)
            
    def get_installed_apps(self, udid: str):
        """查看安装的app
        
        Args:
            udid: 设备ID
            
        Returns:
            list: 已安装应用的列表，每个应用包含 bundle_id, version, name, executable 信息
        """
        cmd = [self.bdc_path, "list-apps", "-u", udid, "-t", "user"]
        try:
            app_list_result = subprocess.run(cmd, capture_output=True, text=True, check=True, shell=False)
            
            apps = []
            for line in app_list_result.stdout.splitlines():
                if not line.strip():
                    continue
                    
                try:
                    # 解析格式: bundle_id, "version", "name", executable
                    parts = line.split(',', 3)
                    if len(parts) == 4:
                        bundle_id = parts[0].strip()
                        version = parts[1].strip(' "')
                        name = parts[2].strip(' "')
                        executable = parts[3].strip()
                        
                        apps.append({
                            'bundle_id': bundle_id,
                            'version': version,
                            'name': name,
                            'executable': executable
                        })
                except Exception as e:
                    logger.warning(f"[{udid}] 解析应用信息失败: {line} - {e}")
                    continue

            return apps

        except subprocess.CalledProcessError as e:
            logger.error(f"[{udid}] 获取应用列表失败: {e.stderr}")
            return []

    """非共有方法"""

    def ui_test_init(self, udid: str) -> bool:
        """
        初始化 UI 测试环境。尝试三次：
        1. 正常初始化
        2. pkill bdc 后重试
        3. bdc reboot 重启设备后重试

        Args:
            udid (str): 设备 UDID。

        Returns:
            bool: 初始化是否成功。
        """
        try:
            # 尝试 1：常规初始化
            if self._try_bdc_init(udid):
                return True

            # 尝试 2：pkill bdc 后重试
            logger.warning(f"[{udid}] 初始化失败，尝试 pkill bdc 后重试")
            os.system("pkill bdc")
            if self._try_bdc_init(udid):
                return True

            # 尝试 3：bdc reboot 后等待上线再重试
            logger.warning(f"[{udid}] 仍初始化失败，尝试使用 bdc reboot 重启设备后重试")
            if not self._reboot_device_with_bdc(udid):
                logger.error(f"[{udid}] 重启命令执行失败")
                return False

            if not self._wait_for_device_online(udid, timeout=60):
                logger.error(f"[{udid}] 重启后未上线，放弃初始化")
                return False

            time.sleep(5)
            return self._try_bdc_init(udid)

        except Exception as e:
            logger.error(f"[{udid}] 初始化 UI 测试环境失败: {str(e)}")
            return False


    def _try_bdc_init(self, udid: str) -> bool:
        """尝试使用 bdc 初始化 UI 测试环境（不带 -m 参数）"""
        try:
            process = subprocess.Popen(
                [self.bdc_path, "ui-test", "-u", udid],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
            )
            stdout, stderr = process.communicate(timeout=60)
            logger.debug(f"[{udid}] 初始化命令输出: {stdout}")

            if stderr:
                logger.error(f"[{udid}] 初始化命令错误: {stderr}")

            success = "prepare success" in stdout or "already authorize success" in stdout
            if success:
                logger.info(f"[{udid}] UI 测试环境初始化成功")
            else:
                logger.error(f"[{udid}] UI 测试环境初始化失败")

            return success

        except subprocess.TimeoutExpired:
            process.kill()
            logger.error(f"[{udid}] 初始化命令执行超时")
            return False


    def _reboot_device_with_bdc(self, udid: str) -> bool:
        """使用 bdc reboot 命令重启设备"""
        try:
            result = subprocess.run(
                [self.bdc_path, "reboot", "-u", udid],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=15
            )
            if result.returncode != 0:
                logger.error(f"[{udid}] reboot 命令失败: {result.stderr}")
                return False
            logger.info(f"[{udid}] reboot 命令执行成功")
            return True
        except subprocess.TimeoutExpired:
            logger.error(f"[{udid}] reboot 命令超时")
            return False


    def _wait_for_device_online(self, udid: str, timeout: int = 60) -> bool:
        """等待指定设备上线，最多等待 timeout 秒"""
        logger.debug(f"[{udid}] 等待设备上线...")
        start = time.time()
        while time.time() - start < timeout:
            if self.is_ios_device_online(udid):
                logger.info(f"设备 [{udid}] 已上线")
                return True
            time.sleep(2)
        return False


    def start_app(self, udid, bundle_id):
        """启动应用
        
        Args:
            udid: 设备ID
            bundle_id: 应用包名
            
        Returns:
            bool: 启动是否成功
        """
        start_app_cmd = [self.bdc_path, "launch", "-b", bundle_id, "-u", udid]
        try:
            subprocess.run(start_app_cmd, capture_output=True, text=True, check=True, shell=False)
            logger.info(f"[{udid}] 启动应用成功: {bundle_id}")
            return True
        except subprocess.CalledProcessError as e:
            logger.warning(f"[{udid}] 应用程序的配置文件不受信任，请在设备设置中信任该配置文件")
            return False

    def get_popups(self, udid: str) -> list:
        """获取弹窗信息
        
        Args:
            udid: 设备ID
            
        Returns:
            list: 弹窗信息列表
        """
        popup_info_cmd = [self.bdc_path, "get-sysalert-text", "-u", udid]
        try:
            popup_info_result = subprocess.run(popup_info_cmd, shell=False, capture_output=True, text=True, check=True)
            return json.loads(popup_info_result.stdout.strip())
        except subprocess.CalledProcessError as e:
            logger.error(f"[{udid}] 获取弹窗信息失败: {e}")
            return []

    def popup_handle(self, udid: str, popups: list, operate: bool):
        """处理 iOS 弹窗
        
        Args:
            udid: 设备ID
            popups: 弹窗信息
            operate: True表示接受，False表示取消
            
        Returns:
            bool: 是否成功处理弹窗
        """
        if popups:
            if operate:
                accept_alert_cmd = [self.bdc_path, "accept-alert", "-u", udid]
                subprocess.run(
                    accept_alert_cmd, capture_output=True, text=True, check=True, shell=False
                )
                logger.info(f"[{udid}] 接受弹窗 {popups}")
                return True
            else:
                dismiss_alert_cmd = [self.bdc_path, "dismiss-alert", "-u", udid]
                subprocess.run(
                    dismiss_alert_cmd, capture_output=True, text=True, check=True, shell=False
                )
                logger.info(f"[{udid}] 取消弹窗 {popups}")
                return True
        else:
            return False

ios_control = iOSControl()

if __name__ == '__main__':
    
    # ios_control.ui_test_init("00008030-001558E91A88802E")
    ios_control.wireless_disconnect("00008110-00014D4E01E2801E")
