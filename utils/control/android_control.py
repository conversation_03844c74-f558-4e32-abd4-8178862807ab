'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/utils/control/android_control.py
Description: 
'''
import os
import subprocess
import time
from typing import Union

import defines
from utils.common.file_utils import get_adb_path, get_python_path
from utils.common.log_utils import logger
from utils.common.req_utils import request_base, RequestMethod


class AndroidControl:
    def __init__(self, adb_path=None):
        if not adb_path:
            self.adb_path = get_adb_path()
        self.python_path = get_python_path()

    def is_android_device_online(self, udid: str) -> bool:
        """
        检查 Android 设备是否在线。

        Args:
            udid (str): Android 设备的唯一标识符。

        Returns:
            bool: 如果设备在线且响应正常，返回 True；否则返回 False。
        """
        try:
            result = subprocess.run(
                [self.adb_path, "devices"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode != 0:
                logger.warning(f"ADB command failed: {result.stderr.strip()}")
                return False

            for line in result.stdout.strip().splitlines()[1:]:  # 跳过第一行标题
                parts = line.strip().split()
                if len(parts) == 2 and parts[0] == udid and parts[1] == "device":
                    return True

            logger.info(f"{udid} 设备不在线")
            return False

        except subprocess.TimeoutExpired:
            logger.error("ADB 命令超时。")
            return False
        except FileNotFoundError:
            logger.error("ADB 未找到。请确保已安装并将其添加到 PATH。")
            return False
        except Exception as e:
            logger.exception(f"检查设备状态时发生意外错误: {e}")
            return False

    def inspect_app(self, apk_path):
        """
        检查 APK 文件并返回检查结果。

        :param apk_path: APK 文件路径
        :return: 检查结果的 stdout 或空字符串
        """
        cmd = [self.python_path, "-m", "shoots_android", "inspect", apk_path]
        try:
            result = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            logger.error(f"检查 APK 信息失败: {e.stderr}")
            return None

    def get_package_name_by_app(self, apk_path):
        """
        从 APK 文件中获取包名。

        :param apk_path: APK 文件路径
        :return: 包名或 None
        """
        if not isinstance(apk_path, str) or not os.path.exists(apk_path):
            logger.error("无效的APK路径")
            return
        try:
            inspect_output = self.inspect_app(apk_path)
            package_info = inspect_output.split("Package name:")
            if len(package_info) < 2:
                logger.error("APK信息中未找到包名")
                return None
            package_name = package_info[1].split("\n")[0].strip()
            logger.info(f"成功解析包名为: {package_name}")
            return package_name
        except Exception as e:
            logger.error(f"获取包名失败: {e}")
            return None

    def get_activity_name_by_app(self, apk_path):
        """
        从 APK 文件中获取启动活动名称。

        :param apk_path: APK 文件路径
        :return: 启动活动名称或 None
        """
        if not isinstance(apk_path, str) or not os.path.exists(apk_path):
            logger.error("无效的APK路径")
            return None
        try:
            inspect_output = self.inspect_app(apk_path)
            activity_info = inspect_output.split("Start activity:")
            if len(activity_info) < 2:
                logger.error("APK信息中未找到启动活动名称")
                return None
            activity_name = activity_info[1].split("\n")[0].strip()
            logger.info(f"成功解析活动名称为: {activity_name}")
            return activity_name
        except Exception as e:
            logger.error(f"获取活动名称失败: {e}")
            return None

    def get_app_version_by_app(self, apk_path):
        """
        从 APK 文件中获取应用版本号。

        :param apk_path: APK 文件路径
        :return: 应用版本号或 None
        """
        if not os.path.exists(apk_path) or not os.path.isfile(apk_path):
            logger.error("无效的APK路径")
            return None
        try:
            output = self.inspect_app(apk_path)
            if not output:
                logger.warning("inspect_app 返回为空")
                return None
            parts = output.split("Version:")
            if len(parts) < 2:
                logger.warning("无法找到版本号")
                return None
            app_version = parts[1].split("\n")[0].strip()
            logger.info(f"成功获取版本号: {app_version}")
            return app_version
        except Exception as e:
            logger.error(f"获取版本号失败: {e}")
            return None

    def repack_app(self, apk_path: str, save_dir: str, **kwargs):
        """
        重打包 APK 文件。

        :param apk_path: 原始 APK 文件路径
        :param save_dir: 保存重打包后 APK 文件的目录
        :return: 重打包后的 APK 文件路径或 None
        """
        if not os.path.exists(apk_path) or not os.path.isfile(apk_path) or not apk_path.endswith(".apk"):
            logger.error(f"APK 文件不存在: {apk_path}")
            return None

        app_name = os.path.basename(apk_path)
        repack_app_name = f"{os.path.splitext(app_name)[0]}-repack-signed.apk"
        repack_app_path = os.path.join(save_dir, repack_app_name)

        if os.path.exists(repack_app_path):
            logger.info("重打包文件已存在")
            return repack_app_path

        cmd = [self.python_path, "-m", "shoots_android", "repack", apk_path]
        try:
            res = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
            if "Repack apk(s) completely" in res.stdout:
                logger.info(f"重打包成功: {repack_app_path}")
                return repack_app_path
            logger.error(f"重打包失败: {res.stdout}")
            return None
        except subprocess.CalledProcessError as e:
            logger.error(f"重打包失败: {e.stderr}")
            return None

    def get_current_wifi_name(self, udid):
        """
        获取当前设备连接的 WiFi 名称。

        :param udid: 设备唯一标识符
        :return: 当前 WiFi 名称或"未找到 SSID"
        """
        cmd = [self.adb_path, "-s", udid, "shell", "dumpsys", "wifi", "|", "grep", "-i", "ssid"]
        try:
            res = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
            ssid_start = res.stdout.find("SSID: ")
            if ssid_start != -1:
                ssid_end = res.stdout.find("\n", ssid_start)
                ssid = res.stdout[ssid_start + len("SSID: "):ssid_end].strip()
                logger.info(f"{udid} 当前WiFi名称: {ssid}")
                return ssid
            else:
                logger.warning(f"{udid} 未找到 SSID")
                return "未找到 SSID"
        except subprocess.CalledProcessError as e:
            logger.error(f"{udid} 获取WiFi名称失败: {e.stderr}")
            return "未找到 SSID"

    def wireless_connect(self, uuid: str, device_ip: str, device_port: str = "5555", max_retries: int = 3):
        """
        无线连接设备。

        :param uuid: 设备唯一标识符
        :param device_ip: 设备 IP 地址
        :param device_port: 设备端口号
        :param max_retries: 最大重试次数
        :return: 连接成功的 udid 或 False
        """
        wireless_udid = f"{device_ip}:{device_port}"
        
        # 检查设备当前连接状态
        devices_cmd = [self.adb_path, "devices"]
        try:
            devices_result = subprocess.run(devices_cmd, shell=False, capture_output=True, text=True, check=True)
            if not devices_result.stdout:
                logger.error(f"{uuid} 设备列表获取失败：adb devices 命令返回为空")
                return False
            if wireless_udid in devices_result.stdout:
                logger.info(f"{wireless_udid} 已经是无线连接状态")
                return wireless_udid
        except subprocess.CalledProcessError as e:
            logger.error(f"{uuid} 获取设备列表异常，错误类型: {type(e).__name__}, 错误信息: {e.stderr}")
            return False

        for attempt in range(max_retries):
            try:
                # 确保USB连接正常
                usb_check_cmd = [self.adb_path, "-s", uuid, "shell", "echo", "test"]
                subprocess.run(usb_check_cmd, shell=False, capture_output=True, text=True, check=True)
                
                # 设置 TCP/IP 模式
                tcpip_cmd = [self.adb_path, "-s", uuid, "tcpip", device_port]
                tcpip_result = subprocess.run(tcpip_cmd, shell=False, capture_output=True, text=True, check=True)
                if tcpip_result.stderr:
                    logger.warning(f"{uuid} TCP/IP模式设置警告: {tcpip_result.stderr}")

                # 等待设备重启TCP模式，增加等待时间
                time.sleep(3)

                # 连接到设备
                connect_cmd = [self.adb_path, "connect", wireless_udid]
                connect_res = subprocess.run(connect_cmd, shell=False, capture_output=True, text=True, check=True)

                if "connected" in connect_res.stdout and "error" not in connect_res.stdout.lower():
                    # 再次验证设备是否真的连接成功
                    time.sleep(2)  # 给一些时间让连接稳定
                    devices_result = subprocess.run(devices_cmd, shell=False, capture_output=True, text=True, check=True)
                    if wireless_udid in devices_result.stdout:
                        logger.info(f"{uuid} 无线连接成功，新设备ID: {wireless_udid}")
                        return wireless_udid
                    else:
                        logger.warning(f"{uuid} 无线连接后设备验证失败，尝试第 {attempt + 1}/{max_retries} 次重试")
                        continue
                else:
                    logger.warning(f"{uuid} 无线连接命令执行失败，尝试第 {attempt + 1}/{max_retries} 次重试，错误信息: {connect_res.stdout}")
                    time.sleep(3)
                    continue

            except subprocess.CalledProcessError as e:
                logger.warning(f"{uuid} 无线连接第 {attempt + 1}/{max_retries} 次尝试失败，错误类型: {type(e).__name__}, 错误信息: {e.stderr}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                    continue
                else:
                    logger.error(f"{uuid} 无线连接失败，已达到最大重试次数 {max_retries}")
                    return False

        return False

    def wireless_disconnect(self, udid: str):
        """
        无线断开连接。

        :param udid: 设备唯一标识符
        :return: bool 是否成功断开连接
        """
        # 检查是否是无线连接（通过检查设备ID是否包含IP地址格式）
        if not ("." in udid and ":" in udid):
            logger.info(f"{udid} 不是无线连接设备，无需断开")
            return True

        cmd = [self.adb_path, "-s", udid, "disconnect"]
        try:
            result = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
            if "disconnected" in result.stdout and "error" not in result.stdout.lower():
                # 验证设备是否真的断开了
                devices_cmd = [self.adb_path, "devices"]
                devices_result = subprocess.run(devices_cmd, shell=False, capture_output=True, text=True, check=True)
                if udid not in devices_result.stdout:
                    logger.info(f"{udid} 无线连接断开成功，设备已从列表中移除")
                    return True
                else:
                    logger.error(f"{udid} 无线断开连接验证失败：设备仍然存在于设备列表中")
                    return False
            logger.error(f"{udid} 无线断开连接命令执行失败，输出信息: {result.stdout}")
            return False
        except subprocess.CalledProcessError as e:
            logger.error(f"{udid} 无线断开连接时发生系统异常，错误类型: {type(e).__name__}, 错误信息: {e.stderr}")
            return False

    def install_app(self, udid: Union[str, list[str]], apk_path: str, **kwargs) -> bool:
        """
        安装APP到设备。

        :param udid: 设备唯一标识符列表
        :param apk_path: APK 文件路径
        :return: 安装是否成功
        """
        if not apk_path:
            logger.warning("APK 路径为空，无法安装应用")
            return False
        package_name = kwargs.get("package_name")
        if package_name is None:
            package_name = self.get_package_name_by_app(apk_path)
        if isinstance(udid, str):
            udid = [udid]
        success_count = 0

        install_mode = kwargs.get("install_mode")
        for single_udid in udid:
            if install_mode == defines.APP_UNINSTALL_INSTALL_MODE:
                uninstall_cmd = [self.adb_path, "-s", single_udid, "uninstall", package_name]
                try:
                    subprocess.run(uninstall_cmd, shell=False, capture_output=True, text=True, check=True)
                    logger.info(f"{single_udid} 卸载 {package_name} 成功")
                except subprocess.CalledProcessError as e:
                    logger.error(f"{single_udid} 卸载 {package_name} 失败: {e}")
                    logger.error(f"{single_udid} {e.stderr}")

                install_cmd = [self.adb_path, "-s", single_udid, "install", apk_path]
                try:
                    subprocess.run(install_cmd, shell=False, capture_output=True, text=True, check=True)
                    logger.info(f"{single_udid} 安装 {apk_path} 成功")
                    success_count += 1
                except subprocess.CalledProcessError as e:
                    logger.error(f"{single_udid} 卸载 {package_name} 失败: {e}")
                    logger.error(f"{single_udid} {e.stderr}")
                    continue

            elif install_mode == defines.APP_OVERRIDE_INSTALL_MODE:
                install_cmd = [self.adb_path, "-s", single_udid, "install", "-r", apk_path]
                try:
                    subprocess.run(install_cmd, shell=False, capture_output=True, text=True, check=True)
                    logger.info(f"{single_udid} 覆盖安装成功")
                    success_count += 1
                except subprocess.CalledProcessError as e:
                    logger.error(f"{single_udid} 覆盖安装失败: {e}")
                    logger.error(f"{single_udid} {e.stderr}")
                    continue

            else:
                logger.error(f"未知的安装模式: {install_mode}")
                raise ValueError("未知的安装模式")

        return success_count == len(udid)

    def get_battery_level(self, udid: str):
        """
        获取指定设备的电量水平。

        :param udid: 设备的唯一标识符
        :return: 电量百分比（整数）
        """
        cmd = [self.adb_path, '-s', udid, 'shell', 'dumpsys', 'battery', '|', 'grep', 'level']

        try:
            result = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
            power = int(result.stdout.split("level:")[-1].strip())
            logger.info(f"{udid} 电量为 {power}%")
            return power
        except subprocess.CalledProcessError as e:
            logger.error(f"{udid} 获取电量失败: {e.stderr}")
            return -1

    def check_battery_level(self, udid: str, target_power_level: int, wait_interval: int = None,
                            max_wait_time: int = None) -> bool:
        """
        检测电量是否达到指定水平
        :param udid: 设备的唯一标识符
        :param target_power_level: 目标电量百分比
        :param wait_interval:
        :param max_wait_time:
        :return: 电量是否达到目标水平
        """
        if wait_interval is None:
            wait_interval = 3 * 60
        if max_wait_time is None:
            max_wait_time = 30 * 60

        start_time = time.time()
        while True:
            current_power_level = self.get_battery_level(udid)
            if current_power_level == -1:
                logger.error(f"{udid} 无法获取电池电量")
                return False

            if current_power_level >= target_power_level:
                logger.info(f"{udid} 电量已达到 {target_power_level}%")
                return True

            elapsed_time = time.time() - start_time
            if elapsed_time >= max_wait_time:
                logger.error(f"{udid} 电量未达到 {target_power_level}%，已等待 {elapsed_time / 60:.2f} 分钟")
                return False

            logger.info(f"{udid} 当前电量为 {current_power_level}%，等待 {wait_interval / 60:.2f} 分钟")
            time.sleep(wait_interval)

    """非共有方法"""

    def push_mapping_files(self, udid: Union[str, list[str]], jenkins_build_result_url: str, package_name: str,
                           save_dir: str):
        """
        从Jenkins构建结果URL下载映射文件，并推送到设备。

        :param udid: 设备唯一标识符或标识符列表
        :param jenkins_build_result_url: Jenkins构建结果的URL
        :param package_name: 包名
        :param save_dir: 保存映射文件的目录
        """
        if isinstance(udid, str):
            udid = [udid]
        mapping_url, class_mapping_url, resource_mapping_url = ["", "", ""]
        mapping_path, class_mapping_path, resource_mapping_path = ["", "", ""]

        # 获取Jenkins构建结果
        jenkins_build_result_res = request_base.send_request(method=RequestMethod.GET.value,
                                                             url=jenkins_build_result_url)
        if jenkins_build_result_res.status_code == 200:
            res_json = jenkins_build_result_res.json()
            if "mapping_url" in res_json:
                mapping_url = res_json["mapping_url"]
            if "extra_archive_file_map" in res_json:
                if "layout_view_class_mapping_txt" in res_json["extra_archive_file_map"]:
                    class_mapping_url = res_json["extra_archive_file_map"]["layout_view_class_mapping_txt"]
                if "resource_mapping_txt" in res_json["extra_archive_file_map"]:
                    resource_mapping_url = res_json["extra_archive_file_map"]["resource_mapping_txt"]

        if mapping_url:
            mapping_name = mapping_url.split("/")[-1]
            mapping_path = os.path.join(save_dir, mapping_name)
            if not os.path.exists(mapping_path):
                mapping_res = request_base.send_request(method=RequestMethod.GET.value, url=mapping_url)
                if mapping_res.status_code == 200:
                    with open(mapping_path, "wb") as f:
                        f.write(mapping_res.content)
        if class_mapping_url:
            class_mapping_name = class_mapping_url.split("/")[-1]
            class_mapping_path = os.path.join(save_dir, class_mapping_name)
            if not os.path.exists(class_mapping_path):
                class_mapping_res = request_base.send_request(method=RequestMethod.GET.value, url=class_mapping_url)
                if class_mapping_res.status_code == 200:
                    with open(class_mapping_path, "wb") as f:
                        f.write(class_mapping_res.content)
        if resource_mapping_url:
            resource_mapping_name = resource_mapping_url.split("/")[-1]
            resource_mapping_path = os.path.join(save_dir, resource_mapping_name)
            if not os.path.exists(resource_mapping_path):
                resource_mapping_res = request_base.send_request(method=RequestMethod.GET.value,
                                                                 url=resource_mapping_url)
                if resource_mapping_res.status_code == 200:
                    with open(resource_mapping_path, "wb") as f:
                        f.write(resource_mapping_res.content)

        # 推送映射文件到设备
        for single_udid in udid:
            if mapping_url and os.path.exists(mapping_path):
                mapping_command = [self.adb_path, '-s', single_udid, 'push', mapping_path,
                                   f'/data/local/tmp/{package_name}_map.txt']
                try:
                    subprocess.run(mapping_command, shell=False, capture_output=True, text=True, check=True)
                    logger.info(f"{single_udid} 推送映射文件成功")
                except subprocess.CalledProcessError as e:
                    logger.error(f"{single_udid} 推送映射文件失败: {e.stderr}")
                    continue

            if class_mapping_url and os.path.exists(class_mapping_path):
                class_mapping_command = [self.adb_path, '-s', single_udid, 'push', class_mapping_path,
                                         f'/data/local/tmp/{package_name}_classmap.txt']
                try:
                    subprocess.run(class_mapping_command, shell=False, capture_output=True, text=True, check=True)
                    logger.info(f"{single_udid} 推送类映射文件成功")
                except subprocess.CalledProcessError as e:
                    logger.error(f"{single_udid} 推送类映射文件失败: {e.stderr}")
                    continue

            if resource_mapping_url and os.path.exists(resource_mapping_path):
                resource_mapping_command = [self.adb_path, '-s', single_udid, 'push', resource_mapping_path,
                                            f'/data/local/tmp/{package_name}_resmap.txt']
                try:
                    subprocess.run(resource_mapping_command, shell=False, capture_output=True, text=True, check=True)
                    logger.info(f"{single_udid} 推送资源映射文件成功")
                except subprocess.CalledProcessError as e:
                    logger.error(f"{single_udid} 推送资源映射文件失败: {e.stderr}")
                    continue

    def close_auto_rotate_screen(self, udid: str):
        """
        关闭指定设备的自动旋转屏幕功能。

        :param udid: 设备的唯一标识符
        :return: None
        """
        cmd = [self.adb_path, '-s', udid, 'shell', 'settings', 'put', 'system', 'accelerometer_rotation', '0']
        try:
            subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)
            logger.info(f"{udid} 关闭自动旋转屏幕成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"{udid} 关闭自动旋转屏幕失败: {e.stderr}")

    def cold_boot(self, udid: str, package_name: str, activity_name: str):
        """
        冷启动
        @param udid: 设备的唯一标识符
        @param package_name: 应用程序的包名
        @param activity_name: 应用程序的活动名称
        @return: 无返回值
        """
        time.sleep(3)
        force_stop_command = [self.adb_path, '-s', udid, 'shell', 'am', 'force-stop', package_name]
        try:
            subprocess.run(force_stop_command, shell=False, check=True, capture_output=True, text=True)
            logger.info(f"{udid} 强制停止应用成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"{udid} 强制停止 {package_name} 失败: {e.stderr}")
            return

        start_command = [self.adb_path, '-s', udid, 'shell', 'am', 'start', f'{package_name}/{activity_name}']
        try:
            subprocess.run(start_command, shell=False, check=True, capture_output=True, text=True)
            logger.info(f"{udid} 启动应用成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"{udid} 启动 {package_name}/{activity_name} 失败: {e.stderr}")
            return

        logger.info(f"{udid} 冷启动成功")

android_control = AndroidControl()

if __name__ == '__main__':
    print(android_control.wireless_connect('e6d62b77', '100.84.192.23'))
