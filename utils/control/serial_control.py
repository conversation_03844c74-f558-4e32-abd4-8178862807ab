import binascii
import subprocess
import sys
import time
from typing import List, Optional

import serial

from utils.common.log_utils import logger

# 定义数据包常量
RELAY_ON_PACKET = "A0 01 01 A2"
RELAY_OFF_PACKET = "A0 01 00 A1"


class SerialControl:
    """
    串口控制类,用于控制USB继电器开关
    
    通讯协议说明:
    - 指令通过16进制形式发送
    - 数据包格式:
        - 数据(1): 启始标识(默认0xA0)
        - 数据(2): 开关地址码(0x01表示第1路,0x02表示第2路,依次类推) 
        - 数据(3): 操作数据
            - 0x00: 关闭不反馈
            - 0x01: 打开不反馈
            - 0x02: 关闭并反馈
            - 0x03: 打开并反馈
            - 0x04: 取反并反馈
            - 0x05: 查询状态
            - 0x06: 闪断并反馈
        - 数据(4): 校验码(前三个数据之和)
    """

    def get_all_serial(self) -> List[str]:
        """
        获取所有可用的串口设备
        
        Returns:
            List[str]: 串口设备名称列表
        """
        if sys.platform.startswith("linux"):
            cmd = "ls /dev/ttyCH341*"
        elif sys.platform.startswith("darwin"):
            cmd = "ls /dev/cu.wchusbserial*"
        else:
            logger.warning("[串口] 不支持的操作系统平台")
            return []
            
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=True) # ignore_security_alert
            serial_ports = [port for port in result.stdout.split("\n") if port]
            logger.info(f"[串口] 成功获取串口设备: {serial_ports}")
            return serial_ports
        except subprocess.CalledProcessError as e:
            logger.error(f"[串口] 执行命令失败: {e}")
            return []
        except Exception as e:
            logger.error(f"[串口] 获取串口设备异常: {e}")
            return []

    def connect_serial(self, serial_port: str, baud_rate: int = 9600) -> Optional[serial.Serial]:
        """
        连接指定的串口设备
        
        Args:
            serial_port: 串口设备名称,如 "COM12"
            baud_rate: 波特率,默认9600
            
        Returns:
            Optional[serial.Serial]: 串口连接对象,连接失败返回None
        """
        if not isinstance(serial_port, str) or not serial_port:
            logger.error("[串口] serial_port必须是非空字符串")
            return None
            
        if not isinstance(baud_rate, int) or baud_rate <= 0:
            logger.error("[串口] baud_rate必须是正整数")
            return None
            
        try:
            ser = serial.Serial(serial_port, baud_rate, timeout=0.5)
            logger.info(f"[串口] 成功连接串口 {serial_port}, 波特率 {baud_rate}")
            return ser
        except Exception as e:
            logger.error(f"[串口] 连接串口失败: {e}")
            return None

    def relay_switch(self, serial_port: str, switch_status: bool, baud_rate: int = 9600) -> Optional[int]:
        """
        控制继电器开关状态
        
        Args:
            serial_port: 串口设备名称
            switch_status: 开关状态(True表示打开,False表示关闭)
            baud_rate: 波特率,默认9600
            
        Returns:
            Optional[int]: 写入的字节数,操作失败返回None
        """
        ser = self.connect_serial(serial_port, baud_rate)
        if not ser:
            return None
            
        try:
            with ser:
                data_packet = bytes.fromhex(RELAY_ON_PACKET if switch_status else RELAY_OFF_PACKET)
                logger.info(f"[串口] {'打开' if switch_status else '关闭'}继电器开关")
                
                res = ser.write(data_packet)
                
                if ser.in_waiting:
                    data = str(binascii.b2a_hex(ser.read(ser.in_waiting)))[2:-1]
                    logger.info(f"[串口] 串口 {serial_port} 返回数据: {data}")
                    
                time.sleep(1)
                return res
        except Exception as e:
            logger.error(f"[串口] 控制继电器异常: {e}")
            return None


serial_control = SerialControl()

if __name__ == '__main__':
    serial_ports = serial_control.get_all_serial()
    
    for port in serial_ports:
        serial_control.relay_switch(port, True)
