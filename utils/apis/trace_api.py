import time
from utils.common.log_utils import logger
from utils.common.req_utils import RequestMethod, request_base


class VQoSTrace:
    def get_trace(self, namespace: str, start: int, end: int, offset: int, limit: int,
                  query_filter: list, query_string: str):
        """
        视频架构VQoS Trace 查询示例
        注意
        Trace接口对外提供的查询QPS非常有限，使用前务必与@孙佳豪 沟通使用目的与QPS。
        QPS请保持小于等于1
        查询时请务必带上Owner字段
        请不要使用  event_key:*xxxx  类似的模糊查询
        @param namespace:
        @param start:
        @param end:
        @param offset:
        @param limit:
        @param query_filter:
        @param query_string:
        @return:
        """
        time.sleep(1)
        method = RequestMethod.POST.value
        headers = {
            "specialData": "true"
        }
        url = "http://rtc-maliva.tiktok-row.org" + "/live_trace/v1/mget"
        json = {
            "Namespace": namespace,
            "Start": start,
            "End": end,
            "Offset": offset,
            "Limit": limit,
            "Filter": query_filter,
            "QueryString": query_string,
            "Owner": "hejiabei.oxep",
        }
        res = request_base.send_request(method=method, url=url, headers=headers, json=json)
        return res.json()


vqos_trace = VQoSTrace()

if __name__ == '__main__':
    live_trace_data = vqos_trace.get_trace(
        namespace="live-trace",
        start=1732759339975,
        end=1732773739975,
        offset=0,
        limit=200,
        query_filter=[],
        query_string="user_id: 7327133310998938642 AND rtc_channel_id: ?*"
    )
    logger.debug(live_trace_data)
    live_result_list = live_trace_data["Result"]["List"]
    rtc_channel_id = live_result_list[0]["rtc_channel_id"]
    logger.debug(rtc_channel_id)
