"""
WLTC Token API 模块
用于处理风控豁免相关的 Token 获取和管理功能
"""
import datetime
from utils.common.log_utils import logger
from shoots.config import settings
import time


def get_base_url_for_sg():
    """
    获取新加坡区域的基础 URL
    
    Returns:
        str: 根据环境返回对应的基础 URL
    """
    try:
        is_online = getattr(settings, 'TIKTOK_ONLINE_EXECUTION_ENV', False)
        if is_online:
            return "https://bits-sg-gw.bytedance.net"
        else:
            return "https://bits-sg-gw.tiktok-row.net"
    except Exception as e:
        logger.warning(f"获取TIKTOK_ONLINE_EXECUTION_ENV配置失败: {str(e)}")
        return "https://bits-sg-gw.tiktok-row.net"


def get_wltc_token_url_for_sg():
    """
    获取新加坡区域的 WLTC Token URL
    
    Returns:
        str: 根据环境返回对应的 Token URL
    """
    try:
        is_online = getattr(settings, 'TIKTOK_ONLINE_EXECUTION_ENV', False)
        if is_online:
            return "https://aqua-maliva.bytedance.net"
        else:
            return "http://aqua.tiktok-row.net"
    except Exception as e:
        logger.warning(f"获取TIKTOK_ONLINE_EXECUTION_ENV配置失败: {str(e)}")
        return "http://aqua.tiktok-row.net"


class WltcToken:
    """
    WLTC Token 管理类
    处理风控豁免 Token 的获取、更新和管理
    """
    _base_url_for_sg: str = get_base_url_for_sg()
    _wltc_token_url: str = get_wltc_token_url_for_sg()

    @classmethod
    def get_jwt_token(cls):
        """
        获取 JWT Token
        
        Returns:
            str: JWT Token 字符串
        """
        import requests

        PRODUCT_ID = "d185acce16cdfdfab5792fc1411aa0c3"
        URL_JWT = "https://cloud-i18n.bytedance.net/auth/api/v1/jwt"

        payload = {}
        headers = {
            'Authorization': 'Bearer {product}'.format(product=PRODUCT_ID)
        }

        response = requests.request("GET", URL_JWT, headers=headers, data=payload)

        headers_res = response.headers
        logger.info(f"jwt token api response: {response.text}")
        return headers_res["X-Jwt-Token"]


    @classmethod
    def get_wltc_token(cls, did: str):
        """
        获取 WLTC Token
        
        Args:
            did (str): 设备 ID
            
        Returns:
            dict: WLTC Token 响应数据
        """
        url = cls._wltc_token_url + "/exempt/open_api/v1/wltc_token"

        header = {
            "Content-Type": "application/json; charset=utf-8",
            "X-Jwt-Token": WltcToken.get_jwt_token()
        }

        body = {
            "applicant": "risk_control_exemption",
            "secret": "ea54abf835353e7120c0c081f4223536",
            "apply_duration": "",
            "expired_at": int(time.mktime(datetime.datetime.now().timetuple())) + 259200,
            "aid": 1233,
            "event_map": {
                "whale": [
                    "all"
                ],
                "shark": [
                    "all"
                ]
            },
            "reason": "Automated testing exempts risk control!",
            "data_map": {
                "did": [
                    did
                ]
            }
        }

        import requests, json

        response = requests.post(url=url, headers=header, data=json.dumps(body))
        cnt = 0
        while response.status_code != 200 and cnt < 3:
            logger.info(f"The WLTC Token is obtained successfully. The status code is: {response.status_code}, The Did is: {did}, result: {response.text}")
            cnt += 1
            response = requests.post(url=url, headers=header, data=json.dumps(body))
        return json.loads(response.text)['data']


    @classmethod
    def get_did_risk_control_exemption_info(cls, did: str):
        """
        获取设备风控豁免信息
        
        Args:
            did (str): 设备 ID
            
        Returns:
            dict: 风控豁免信息
        """
        url = cls._base_url_for_sg + "/api/v1/pipe_openapi/pool/resource/consume"

        header = {
            "Authorization": "Bearer c44576d9dc63362051a0bd02bbd25ae0",
            "Content-Type": "application/json; charset=utf-8"
        }

        body = {
            "pool_id": 100440,
            "field_search_filter": {
                "field_search": [
                    {
                        "data": did,
                        "field_name": "did",
                        "operation": "="
                    }
                ]
            },
            "require_field": ["create_time", "update_time", "did", "token", "expired_time"],
            "resource_filter": {
                "status_list": [
                    "available"
                ]
            }
        }

        import requests, json
        result = None
        cnt = 3
        while not result or (cnt > 0 and "data" not in result.keys()):
            response = requests.post(url=url, headers=header, data=json.dumps(body))
            result = json.loads(response.text)
            cnt -= 1
        logger.info(f"get did response text: {response.text}")
        return result


    @classmethod
    def add_did_risk_control_exemption_info(cls, did: str, token: str):
        """
        添加设备风控豁免信息
        
        Args:
            did (str): 设备 ID
            token (str): WLTC Token
        """
        url = cls._base_url_for_sg + "/api/v1/pipe_openapi/pool/resources"

        header = {
            "Authorization": "Bearer c44576d9dc63362051a0bd02bbd25ae0",
            "Content-Type": "application/json; charset=utf-8"
        }

        body = {
            "data": [{
                "resource_id": str(did),
                "status": "available",
                'user_define': {
                    "did": did,
                    "token": token,
                    "expired_time": str(
                        (datetime.datetime.now() + datetime.timedelta(days=3)).strftime("%Y-%m-%d %H:%M:%S")),

                }
            }
            ],
            "pool_id": 100440
        }

        import requests, json

        response = requests.post(url=url, headers=header, data=json.dumps(body))
        logger.info(f"The Did is: {did}")
        print(response)


    @classmethod
    def update_did_risk_control_exemption_info(cls, case_id: str, did: str, token: str):
        """
        更新设备风控豁免信息
        
        Args:
            case_id (str): 用例 ID
            did (str): 设备 ID
            token (str): WLTC Token
        """
        url = cls._base_url_for_sg + "/api/v1/pipe_openapi/pool/resources"

        header = {
            "Authorization": "Bearer c44576d9dc63362051a0bd02bbd25ae0",
            "Content-Type": "application/json; charset=utf-8"
        }

        body = {
            "data": [{
                "resource_id": case_id,
                "status": "available",
                'user_define': {
                    "did": did,
                    "token": token,
                    "expired_time": str(
                        (datetime.datetime.now() + datetime.timedelta(days=3)).strftime("%Y-%m-%d %H:%M:%S")),
                }
            }
            ],
            "pool_id": 100440
        }

        import requests, json

        response = requests.put(url=url, headers=header, data=json.dumps(body))
        print(response)
        
wltc_token_api = WltcToken()