#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
from websocket import create_connection
import time
from typing import Dict, List, Any, Optional, Callable
from utils.common.log_utils import logger
from defines import PlatformType


class PerfServiceAPI:
    """
    PerfService API封装类，提供与GamePerf性能测试服务交互的全面功能
    """
    
    def __init__(self, host: str = "127.0.0.1", port: Optional[int] = None):
        """
        初始化PerfService API客户端

        Args:
            host: PerfService服务主机名
            port: PerfService服务端口，必须提供实际的服务端口

        Raises:
            ValueError: 当端口未提供时抛出异常
        """
        if port is None:
            raise ValueError(
                "端口号是必需的。请在GamePerf服务启动后，使用实际分配的HTTP端口号初始化API客户端。"
                "不要使用硬编码的默认端口号。"
            )

        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.ws_base_url = f"ws://{host}:{port}"
        self.perf_ws = None
        self.capture_ws = None
        self._perf_callback = None
        self._capture_callback = None
        self._perf_thread = None
        self._capture_thread = None
        self._perf_thread_running = False
        self._capture_thread_running = False
        
        # 存储数据
        self.current_time = -1  # 当前采集的性能数据所对应的时间
        self.current_device_info = {}  # 当前选中设备的设备信息
        self.perf_data = []  # 存放已采集的所有性能数据
        self.capture_data = []  # 存放截图保存路径
        self.devices = []  # 电脑上连接的所有设备
        self.apps = []  # 选中设备上的所有应用信息
        self.subscenes = []  # 子场景列表
        self.stop_record_results = []  # 存储stop_record操作的结果

        # WebSocket命令定义
        self.data_stop = {"action": "stopCollect"}
        self.cap_start = {"action": "startCapture"}
        self.cap_stop = {"action": "stopCapture"}

    # ==================== 预设回调函数 ====================

    @staticmethod
    def _default_progress_callback(current_time: int):
        """
        默认进度回调函数

        每5秒显示一次采集进度

        Args:
            current_time: 当前时间（秒）
        """
        if current_time % 5 == 0:
            logger.info(f"采集进度: {current_time}秒")

    # ==================== HTTP API 部分 ====================
    
    def get_all_devices(self) -> List[Dict[str, Any]]:
        """
        获取所有可用设备列表

        Returns:
            list: 设备信息列表，包含设备基本信息和硬件规格

        Raises:
            RuntimeError: 当请求失败时抛出异常
        """
        url = f"{self.base_url}/getAllDevices"
        try:
            response = requests.get(url, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("getAllDevices", False):
                logger.error(f"获取设备列表失败: {response}")
                raise RuntimeError("获取设备列表失败")
                
            self.devices = response.get("data", [])

            # 只打印设备的重要字段信息
            device_summary = []
            for device in self.devices:
                summary = {
                    'UDID': device.get('UDID', ''),
                    'DeviceName': device.get('DeviceName', ''),
                    'DeviceSystem': device.get('DeviceSystem', ''),
                    'DeviceBrand': device.get('DeviceBrand', ''),
                    'DeviceType': device.get('DeviceType', ''),
                    'ConnectType': device.get('ConnectType', '')
                }
                device_summary.append(summary)

            logger.debug(f"获取到 {len(self.devices)} 个设备: {device_summary}")
            return self.devices
        except requests.exceptions.RequestException as e:
            logger.error(f"获取设备列表错误: {e}")
            raise RuntimeError(f"获取设备列表错误: {e}")
    
    def connect_device(self, udid: str) -> Dict[str, Any]:
        """
        连接到指定设备

        Args:
            udid: 设备ID

        Returns:
            dict: 设备详细信息，包含设备基本信息和硬件规格

        Raises:
            RuntimeError: 当连接设备失败时抛出异常
        """
        url = f"{self.base_url}/connectDevice"
        try:
            response = requests.get(url, params={"udid": udid}, timeout=30).json()

            if not isinstance(response, dict) or not response.get("connectDevice", False):
                logger.error(f"连接设备失败: {response}")
                raise RuntimeError("连接设备失败")

            self.current_device_info = response.get("data", {})
            logger.debug(f"连接设备返回信息: {self.current_device_info}")
            logger.info(f"已连接设备: {udid}")
            return self.current_device_info
        except requests.exceptions.RequestException as e:
            logger.error(f"连接设备错误: {e}")
            raise RuntimeError(f"连接设备错误: {e}")

    def get_current_device_platform(self) -> PlatformType:
        """
        获取当前连接设备的平台类型

        Returns:
            PlatformType: 设备平台类型枚举值

        Raises:
            RuntimeError: 当没有设备连接或无法确定平台类型时抛出异常
        """
        if not self.current_device_info:
            raise RuntimeError("没有设备连接。请先连接设备。")

        # 尝试多种字段来检测平台类型
        device_type = self.current_device_info.get("Device Type", "").upper()
        device_system = self.current_device_info.get("DeviceSystem", "").upper()
        device_brand = self.current_device_info.get("DeviceBrand", "").upper()

        # iOS设备检测
        if ("IOS" in device_system or "IPHONE" in device_type or
            "APPLE" in device_brand or "IPAD" in device_type):
            logger.debug(f"检测到iOS设备 (DeviceSystem: {device_system}, DeviceType: {device_type}, DeviceBrand: {device_brand})")
            return PlatformType.IOS

        # Android设备检测
        if ("ANDROID" in device_system or "ANDROID" in device_type or
            device_brand in ["SAMSUNG", "HUAWEI", "XIAOMI", "OPPO", "VIVO", "ONEPLUS"]):
            logger.debug(f"检测到Android设备 (DeviceSystem: {device_system}, DeviceType: {device_type}, DeviceBrand: {device_brand})")
            return PlatformType.ANDROID

        # 传统的Device Type字段检测
        platform_mapping = {
            "IOS": PlatformType.IOS,
            "ANDROID": PlatformType.ANDROID,
            "WINDOWS": PlatformType.WINDOWS,
            "MACOS": PlatformType.MACOS,
            "LINUX": PlatformType.LINUX
        }

        platform = platform_mapping.get(device_type)
        if platform is not None:
            logger.debug(f"检测到设备平台类型: {PlatformType.get_name(platform)} (基于Device Type: {device_type})")
            return platform

        # 如果都无法检测，抛出异常
        raise RuntimeError(f"无法确定设备平台类型。设备类型: {device_type}, 设备系统: {device_system}, 设备品牌: {device_brand}, 设备信息: {self.current_device_info}")


    def connect_wireless_device(self, udid: str, ip: str) -> Dict[str, Any]:
        """
        连接到指定的无线设备

        Args:
            udid: 设备ID
            ip: 设备IP地址

        Returns:
            dict: 设备详细信息，包含设备基本信息和硬件规格

        Raises:
            RuntimeError: 当连接无线设备失败时抛出异常
        """
        url = f"{self.base_url}/connectWirelessDevice"
        params = {
            "udid": udid,
            "ip": ip
        }
        try:
            response = requests.get(url, params=params, timeout=30).json()

            if not isinstance(response, dict) or not response.get("connectDevice", False):
                logger.error(f"连接无线设备失败: {response}")
                raise RuntimeError("连接无线设备失败")

            self.current_device_info = response.get("data", {})
            logger.debug(f"连接无线设备返回信息: {self.current_device_info}")
            logger.info(f"已连接无线设备: {udid} (IP: {ip})")
            return self.current_device_info
        except requests.exceptions.RequestException as e:
            logger.error(f"连接无线设备错误: {e}")
            raise RuntimeError(f"连接无线设备错误: {e}")
    
    def get_all_apps(self) -> List[Dict[str, Any]]:
        """
        获取当前连接设备上的所有应用

        Returns:
            list: 应用信息列表，包含应用基本信息和进程状态

        Raises:
            RuntimeError: 当获取应用列表失败时抛出异常
        """
        # 检查设备连接状态
        if not self.current_device_info:
            logger.error("获取应用列表失败: 没有设备连接。请先连接设备。")
            raise RuntimeError("没有设备连接。请先连接设备。")

        url = f"{self.base_url}/getAllApps"
        try:
            response = requests.get(url, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("getAllApps", False):
                logger.error(f"获取应用列表失败: {response}")
                raise RuntimeError("获取应用列表失败")
                
            self.apps = response.get("data", [])

            # 只打印应用的重要字段信息
            app_summary = []
            for app in self.apps:
                summary = {
                    'appName': app.get('appName', ''),
                    'packageName': app.get('packageName', ''),
                    'processName': app.get('processName', ''),
                    'pid': app.get('pid', -1),
                    'subProcess': len(app.get('subProcess', []))
                }
                app_summary.append(summary)

            logger.debug(f"获取到 {len(self.apps)} 个应用")

            return self.apps
        except requests.exceptions.RequestException as e:
            logger.error(f"获取应用列表错误: {e}")
            raise RuntimeError(f"获取应用列表错误: {e}")
    
    def regain_all_apps(self) -> List[Dict[str, Any]]:
        """
        刷新并重新获取当前连接设备上的所有应用

        Returns:
            list: 应用信息列表，包含应用基本信息和进程状态

        Raises:
            RuntimeError: 当刷新应用列表失败时抛出异常
        """
        url = f"{self.base_url}/regainAllApps"
        try:
            response = requests.get(url, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("getAllApps", False):
                logger.error(f"刷新应用列表失败: {response}")
                raise RuntimeError("刷新应用列表失败")
                
            self.apps = response.get("data", [])

            # 只打印应用的重要字段信息
            app_summary = []
            for app in self.apps:
                summary = {
                    'appName': app.get('appName', ''),
                    'packageName': app.get('packageName', ''),
                    'processName': app.get('processName', ''),
                    'pid': app.get('pid', -1),
                    'subProcess': len(app.get('subProcess', []))
                }
                app_summary.append(summary)

            logger.debug(f"刷新获取到 {len(self.apps)} 个应用")
            
            return self.apps
        except requests.exceptions.RequestException as e:
            logger.error(f"刷新应用列表错误: {e}")
            raise RuntimeError(f"刷新应用列表错误: {e}")
    
    def set_scene(self, scene_name: str) -> bool:
        """
        设置场景标记
        
        Args:
            scene_name: 场景名称
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            RuntimeError: 当设置场景失败时抛出异常
        """
        url = f"{self.base_url}/setScene"
        params = {"sceneName": scene_name}
        try:
            response = requests.get(url, params=params, timeout=10).json()
            
            if not isinstance(response, dict) or "setScene" not in response:
                logger.error(f"设置场景失败: {response}")
                raise RuntimeError("设置场景失败")

            logger.debug(f"设置场景返回结果: {response}")
            logger.info(f"场景已设置: {scene_name}")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"设置场景错误: {e}")
            raise RuntimeError(f"设置场景错误: {e}")
    
    def start_record(self) -> bool:
        """
        开始性能数据记录
        
        Returns:
            bool: 操作是否成功
            
        Raises:
            RuntimeError: 当开始记录失败时抛出异常
        """
        url = f"{self.base_url}/startRecord"
        try:
            response = requests.get(url, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("startRecord", False):
                logger.error(f"开始记录失败: {response}")
                raise RuntimeError("开始记录失败")

            logger.debug(f"开始记录返回结果: {response}")
            logger.info("性能数据记录已开始")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"开始记录错误: {e}")
            raise RuntimeError(f"开始记录错误: {e}")
    
    def stop_record(self, file_export: int = 1, file_upload: int = 0) -> Dict[str, Any]:
        """
        停止性能数据记录
        
        Args:
            file_export: 是否导出文件，1表示是，0表示否
            file_upload: 是否上传文件，1表示是，0表示否
            
        Returns:
            Dict[str, Any]: 包含以下字段：
            - fileExport: 是否已导出文件
            - fileUpload: 是否已上传文件
            - url: 上传后平台生成的报告链接
            - path: 生成的json文件路径
            
        Raises:
            RuntimeError: 当停止记录失败时抛出异常
        """
        url = f"{self.base_url}/stopRecord"
        params = {
            'fileExport': file_export,
            'fileUpload': file_upload
        }
        try:
            response = requests.get(url, params=params, timeout=10).json()

            if not isinstance(response, dict) or not response.get("stopRecord", False):
                logger.error(f"停止记录失败: {response}")
                raise RuntimeError("停止记录失败")

            result = response.get("data", {})
            logger.debug(f"停止记录返回完整响应: {response}")
            logger.info(f"性能数据记录已停止，结果: {result}")
            return result
        except requests.exceptions.RequestException as e:
            logger.error(f"停止记录错误: {e}")
            raise RuntimeError(f"停止记录错误: {e}")
    
    def start_subscene(self, subscene_name: str) -> bool:
        """
        开始子场景记录
        
        Args:
            subscene_name: 子场景名称
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            RuntimeError: 当开始子场景失败时抛出异常
        """
        url = f"{self.base_url}/subSceneStart"
        params = {"subSceneName": subscene_name}
        try:
            response = requests.get(url, params=params, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("SubSceneStart"):
                logger.error(f"开始子场景失败: {response}")
                raise RuntimeError("开始子场景失败")
            
            self.subscenes.append(subscene_name)
            logger.debug(f"开始子场景返回结果: {response}")
            logger.info(f"子场景已开始: {subscene_name}")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"开始子场景错误: {e}")
            raise RuntimeError(f"开始子场景错误: {e}")
    
    def stop_subscene(self) -> bool:
        """
        结束当前子场景记录
        
        Returns:
            bool: 操作是否成功
            
        Raises:
            RuntimeError: 当结束子场景失败时抛出异常
        """
        url = f"{self.base_url}/subSceneEnd"
        try:
            response = requests.get(url, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("SubSceneEnd", False):
                logger.error(f"结束子场景失败: {response}")
                raise RuntimeError("结束子场景失败")
            
            logger.debug(f"结束子场景返回结果: {response}")
            logger.info("当前子场景已结束")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"结束子场景错误: {e}")
            raise RuntimeError(f"结束子场景错误: {e}")
    
    def rename_sub_scene(self, old_name: str, new_name: str) -> bool:
        """
        重命名子场景
        
        Args:
            old_name: 原子场景名称
            new_name: 新子场景名称
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            RuntimeError: 当重命名子场景失败时抛出异常
        """
        url = f"{self.base_url}/renameSubScene"
        params = {
            "oldName": old_name,
            "newName": new_name
        }
        try:
            response = requests.get(url, params=params, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("renameSubScene", False):
                logger.error(f"重命名子场景失败: {response}")
                raise RuntimeError("重命名子场景失败")
            
            # 更新子场景列表
            if old_name in self.subscenes:
                self.subscenes[self.subscenes.index(old_name)] = new_name

            logger.debug(f"重命名子场景返回结果: {response}")
            logger.info(f"子场景已重命名: {old_name} -> {new_name}")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"重命名子场景错误: {e}")
            raise RuntimeError(f"重命名子场景错误: {e}")
    
    def remove_sub_scene(self, sub_scene_name: str) -> bool:
        """
        删除子场景
        
        Args:
            sub_scene_name: 要删除的子场景名称
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            RuntimeError: 当删除子场景失败时抛出异常
        """
        url = f"{self.base_url}/removeSubScene"
        params = {"subSceneName": sub_scene_name}
        try:
            response = requests.get(url, params=params, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("removeSubScene", False):
                logger.error(f"删除子场景失败: {response}")
                raise RuntimeError("删除子场景失败")
            
            # 从列表中移除子场景
            if sub_scene_name in self.subscenes:
                self.subscenes.remove(sub_scene_name)

            logger.debug(f"删除子场景返回结果: {response}")
            logger.info(f"子场景已删除: {sub_scene_name}")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"删除子场景错误: {e}")
            raise RuntimeError(f"删除子场景错误: {e}")
    
    def get_screenshot_once(self) -> str:
        """
        获取当前屏幕截图
        
        Returns:
            str: 截图文件路径
            
        Raises:
            RuntimeError: 当获取截图失败时抛出异常
        """
        url = f"{self.base_url}/getScreenShotOnce"
        try:
            response = requests.get(url, timeout=10).json()
            
            if not isinstance(response, dict) or not response.get("getScreenShotOnce", False):
                logger.error(f"获取截图失败: {response}")
                raise RuntimeError("获取截图失败")
            
            screenshot_path = response.get("data", "")
            logger.debug(f"获取截图返回结果: {response}")
            logger.info(f"截图已获取: {screenshot_path}")
            return screenshot_path
        except requests.exceptions.RequestException as e:
            logger.error(f"获取截图错误: {e}")
            raise RuntimeError(f"获取截图错误: {e}")
    
    def upload_data(self, json_path: str) -> bool:
        """
        上传记录的性能数据到服务器
        
        Args:
            json_path: JSON数据文件路径
            
        Returns:
            bool: 操作是否成功
            
        Raises:
            RuntimeError: 当上传数据失败时抛出异常
        """
        url = f"{self.base_url}/uploadData"
        params = {"jsonPath": json_path}
        try:
            response = requests.get(url, params=params, timeout=60).json()
            
            if not isinstance(response, dict) or not response.get("uploadFile", False):
                logger.error(f"上传数据失败: {response}")
                raise RuntimeError("上传数据失败")
            
            logger.debug(f"上传数据返回结果: {response}")
            logger.info(f"数据已上传: {json_path}")
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"上传数据错误: {e}")
            raise RuntimeError(f"上传数据错误: {e}")
    
    def share_report(self) -> Dict:
        """
        分享性能报告
        
        Returns:
            分享结果信息，包含报告链接
        """
        url = f"{self.base_url}/shareReport"
        try:
            response = requests.post(url, timeout=10).json()
            logger.debug(f"分享报告返回结果: {response}")
            logger.info(f"报告已分享: {response}")
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"分享报告错误: {e}")
            raise RuntimeError(f"分享报告错误: {e}")
    
    # ==================== WebSocket API 部分 ====================

    def collect_perf_data(self,
                         app_name: str,
                         time_actions: Dict[int, List[Dict[str, Any]]],
                         process_name: Optional[str] = None,
                         duration: int = 20,
                         on_time_callback: Optional[Callable[[int], None]] = None) -> None:
        """
        采集性能数据（WebSocket方式）

        Args:
            app_name: 应用名称
            time_actions: 时间点操作配置字典，必须提供
            process_name: 进程名称（可选）
            duration: 采集持续时间（秒），默认20秒
            on_time_callback: 时间回调函数（可选）

        Raises:
            RuntimeError: 当WebSocket连接失败或采集过程中出现错误时抛出异常
            ValueError: 当time_actions为空或无效时抛出异常
        """
        # 验证必需的时间点操作配置
        if not time_actions:
            raise ValueError("time_actions参数是必需的，必须提供有效的时间点操作配置")

        # 如果没有提供回调函数，使用默认进度回调
        if on_time_callback is None:
            on_time_callback = self._default_progress_callback

        perf_data_uri = f"{self.ws_base_url}/CollectPerfData"

        try:
            # 创建WebSocket连接，增加超时时间以应对并发场景
            self.perf_ws = create_connection(perf_data_uri, timeout=15)
            logger.info(f"性能数据WebSocket连接已建立: {perf_data_uri}")

            # 准备启动命令 - 尝试不同的格式
            data_start = {"action": "startCollect", "appName": app_name}
            if process_name:
                data_start["processName"] = process_name

            # 开始采集
            data = True
            self.perf_ws.send(json.dumps(data_start))
            logger.info(f"开始采集性能数据: {app_name}")

            # 设置WebSocket接收超时
            self.perf_ws.settimeout(10)

            first_response_received = False

            # 添加连接状态检查
            consecutive_errors = 0
            max_consecutive_errors = 5
            data_count = 0
            empty_response_count = 0

            while data:
                try:
                    data = self.perf_ws.recv()
                    data = json.loads(data)
                    data_count += 1

                    # 特殊处理第一个响应
                    if not first_response_received:
                        first_response_received = True

                        # 检查第一个响应是否为空对象或错误响应
                        if data == {} or (isinstance(data, dict) and len(data) == 0):
                            empty_response_count += 1
                            logger.warning(f"收到空响应，可能是应用进程状态异常或GamePerf服务未能开始采集")

                            # 如果连续收到多个空响应，可能需要重新检查配置
                            if empty_response_count >= 3:
                                logger.error(f"连续收到{empty_response_count}个空响应，可能配置有误")
                                break
                            continue
                        elif isinstance(data, dict) and "error" in data:
                            logger.error(f"收到错误响应: {data}")
                            break

                    # 检查停止信号
                    if isinstance(data, dict) and "stopCollect" in data.keys():
                        if data["stopCollect"] == True:
                            self.current_time = -1
                            data = False
                            logger.info(f"性能数据采集已停止")
                    elif 'time' in data:
                        self.current_time = int(data['time'])

                        # 调用时间回调函数
                        if on_time_callback:
                            try:
                                on_time_callback(self.current_time)
                            except Exception as e:
                                logger.error(f"时间回调函数执行失败: {e}")

                        # 执行配置的时间点操作
                        if self.current_time in time_actions:
                            for action_config in time_actions[self.current_time]:
                                action_result = self._execute_action(action_config)
                                # 如果是stop_record操作，保存结果
                                if action_result and action_result.get("action") == "stop_record":
                                    self.stop_record_results.append(action_result.get("result"))

                        # 检查是否达到指定的持续时间，自动停止采集
                        if self.current_time >= duration:
                            self.perf_ws.send(json.dumps(self.data_stop))
                            logger.info(f"达到指定持续时间 {duration}秒，自动停止采集")

                        # 存储性能数据
                        self.perf_data.append(data)
                    else:
                        pass  # 忽略非时间数据

                except json.JSONDecodeError as e:
                    consecutive_errors += 1
                    logger.error(f"[端口:{self.port}] 解析性能数据JSON失败: {e}, 原始数据: {data}")
                    if consecutive_errors >= max_consecutive_errors:
                        logger.error(f"[端口:{self.port}] 连续JSON解析错误达到{max_consecutive_errors}次，停止采集")
                        break
                    continue
                except ConnectionResetError as e:
                    consecutive_errors += 1
                    logger.warning(f"[端口:{self.port}] WebSocket连接被重置: {e}")
                    if consecutive_errors >= max_consecutive_errors:
                        logger.error(f"[端口:{self.port}] 连续连接错误达到{max_consecutive_errors}次，停止采集")
                        break
                    time.sleep(1)
                    continue
                except Exception as e:
                    consecutive_errors += 1
                    if consecutive_errors >= max_consecutive_errors:
                        logger.error(f"[端口:{self.port}] 连续处理错误达到{max_consecutive_errors}次，停止采集")
                        break
                    time.sleep(1)
                    continue

                # 如果成功处理数据，重置错误计数
                consecutive_errors = 0

        except Exception as e:
            logger.error(f"性能数据采集失败: {e}")
            raise RuntimeError(f"性能数据采集失败: {e}")
        finally:
            # 清理WebSocket连接
            if self.perf_ws:
                try:
                    self.perf_ws.close()
                    logger.info("性能数据WebSocket连接已关闭")
                except:
                    pass
                self.perf_ws = None

    def _execute_action(self, action_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        执行指定的操作

        Args:
            action_config: 操作配置字典

        Returns:
            Optional[Dict[str, Any]]: 操作结果，对于stop_record操作返回包含文件信息的字典
        """
        action = action_config.get("action")

        try:
            if action == "set_scene":
                scene_name = action_config.get("scene_name", "默认场景")
                self.set_scene(scene_name)
                logger.info(f"执行操作: 设置场景 - {scene_name}")
                return {"action": "set_scene", "scene_name": scene_name}

            elif action == "start_record":
                self.start_record()
                logger.info("执行操作: 开始记录")
                return {"action": "start_record"}

            elif action == "stop_record":
                file_export = action_config.get("file_export", 1)
                file_upload = action_config.get("file_upload", 0)
                result = self.stop_record(file_export, file_upload)
                logger.info(f"执行操作: 停止记录 (导出:{file_export}, 上传:{file_upload})")
                return {"action": "stop_record", "result": result}

            elif action == "start_subscene":
                subscene_name = action_config.get("subscene_name", "默认子场景")
                self.start_subscene(subscene_name)
                logger.info(f"执行操作: 开始子场景 - {subscene_name}")
                return {"action": "start_subscene", "subscene_name": subscene_name}

            elif action == "stop_subscene":
                self.stop_subscene()
                logger.info("执行操作: 停止子场景")
                return {"action": "stop_subscene"}

            elif action == "stop_collect":
                self.perf_ws.send(json.dumps(self.data_stop))
                logger.info("执行操作: 停止采集")
                return {"action": "stop_collect"}

            else:
                logger.warning(f"未知操作类型: {action}")
                return {"action": action, "error": "未知操作类型"}

        except Exception as e:
            logger.error(f"执行操作失败 {action}: {e}")
            return {"action": action, "error": str(e)}

    @staticmethod
    def create_time_actions() -> 'TimeActionsBuilder':
        """
        创建时间点操作配置构建器

        Returns:
            TimeActionsBuilder实例，用于构建时间点操作配置
        """
        return TimeActionsBuilder()

    def collect_cap_data(self) -> None:
        """
        采集截图数据（WebSocket方式）

        该方法会建立WebSocket连接并开始采集截图数据。
        会等待性能数据采集开始后再启动截图采集，并在时间达到10秒后停止。

        Raises:
            RuntimeError: 当WebSocket连接失败或采集过程中出现错误时抛出异常
        """
        capture_data_uri = f"{self.ws_base_url}/CollectCaptureData"

        try:
            # 创建WebSocket连接
            self.capture_ws = create_connection(capture_data_uri)
            logger.info(f"截图数据WebSocket连接已建立: {capture_data_uri}")

            # 等待性能数据采集开始
            data = True
            while self.current_time < 0:
                logger.debug("等待性能数据采集开始...")
                time.sleep(1)

            # 开始截图采集
            self.capture_ws.send(json.dumps(self.cap_start))
            logger.info("开始采集截图数据")

            while data:
                try:
                    data = self.capture_ws.recv()
                    data = json.loads(data)
                    logger.debug(f"收到截图数据: {data}")

                    # 检查停止信号
                    if 'stopCapture' in data:
                        data = None
                        logger.info("截图数据采集已停止")

                    # 在时间达到10秒后停止截图采集
                    if self.current_time >= 10:
                        self.capture_ws.send(json.dumps(self.cap_stop))
                        logger.info("发送停止截图采集命令")

                    # 存储截图数据
                    if data:
                        self.capture_data.append(data)

                    time.sleep(1)

                except json.JSONDecodeError as e:
                    logger.error(f"解析截图数据JSON失败: {e}")
                    continue
                except Exception as e:
                    logger.error(f"处理截图数据时出错: {e}")
                    break

        except Exception as e:
            logger.error(f"截图数据采集失败: {e}")
            raise RuntimeError(f"截图数据采集失败: {e}")
        finally:
            # 清理WebSocket连接
            if self.capture_ws:
                try:
                    self.capture_ws.close()
                    logger.info("截图数据WebSocket连接已关闭")
                except:
                    pass
                self.capture_ws = None

    def select_target_process(self, app_name: str, app_package: str, process_name: str = None) -> tuple:
        """
        智能进程选择策略

        Args:
            app_name: 应用显示名称
            app_package: 应用包名
            process_name: 用户指定的进程名称（可选）

        Returns:
            tuple: (进程名称, 进程PID, 选择原因, 完整应用名称)
        """
        # 步骤1: 使用应用查找策略定位目标应用
        target_app = self._find_target_app(app_name, app_package)

        # 步骤2: 使用进程选择策略选择最佳进程
        result = self._select_best_process(target_app, app_package, process_name)

        # 返回结果包含完整应用名称
        final_result = result + (target_app.get('appName', ''),)
        logger.debug(f"智能进程选择结果: {final_result}")
        return final_result

    def _find_target_app(self, app_name: str, app_package: str) -> dict:
        """
        应用查找策略

        Args:
            app_name: 应用显示名称
            app_package: 应用包名

        Returns:
            dict: 找到的目标应用信息

        Raises:
            RuntimeError: 当未找到匹配应用时抛出异常
        """
        apps = self.get_all_apps()

        # 检测当前设备平台类型
        try:
            current_platform = self.get_current_device_platform()
        except Exception as e:
            logger.warning(f"无法检测设备平台类型，默认为未知平台: {e}")
            current_platform = PlatformType.UNKNOWN

        if self._is_ios_platform(current_platform):
            logger.info("检测到iOS设备，将使用iOS兼容性匹配策略")

        # 定义匹配策略列表
        strategies = ["精确", "系统进程", "名称", "模糊"]

        # 如果是iOS设备，添加包名匹配策略
        if self._is_ios_platform(current_platform) and app_package:
            strategies.append("包名")

        # 依次尝试各种匹配策略
        for strategy in strategies:
            target_app = self._match_app_by_strategy(apps, app_name, app_package, strategy, current_platform)
            if target_app:
                return target_app

        # 未找到匹配应用，抛出异常
        available_apps = [app.get('appName', '') for app in apps]
        platform_info = f" (iOS设备)" if self._is_ios_platform(current_platform) else ""
        logger.error(f"未找到应用{platform_info}: {app_name}({app_package}). 可用应用: {available_apps}")
        raise RuntimeError(f"未找到应用{platform_info}: {app_name}({app_package}). 可用应用: {available_apps}")

    def _match_app_by_strategy(self, apps: list, app_name: str, app_package: str, strategy: str, current_platform: PlatformType) -> dict:
        """
        统一的应用匹配策略方法

        Args:
            apps: 应用列表
            app_name: 应用显示名称
            app_package: 应用包名
            strategy: 匹配策略类型
            current_platform: 当前设备平台类型

        Returns:
            dict: 匹配到的应用信息，未找到返回None
        """
        if strategy == "精确":
            if not app_package:
                return None
            full_app_name = f"{app_name}({app_package})"
            for app in apps:
                if app.get('appName', '') == full_app_name:
                    return app

        elif strategy == "系统进程":
            for app in apps:
                app_name_in_list = app.get('appName', '')
                if '(' in app_name_in_list and ')' in app_name_in_list:
                    name_part = app_name_in_list.split('(')[0]
                    bracket_content = app_name_in_list.split('(')[1].split(')')[0]

                    if name_part == app_name:
                        # 检查括号内容是否为数字（进程ID）或包名
                        try:
                            int(bracket_content)
                            return app
                        except ValueError:
                            if app_package and bracket_content == app_package:
                                return app

        elif strategy == "名称":
            for app in apps:
                if app.get('appName', '') == app_name:
                    return app

        elif strategy == "模糊":
            for app in apps:
                app_name_in_list = app.get('appName', '')
                # 原有的精确模糊匹配
                if app_name_in_list.startswith(f"{app_name}(") and app_name_in_list.endswith(')'):
                    return app

                # 新增：检查应用名称是否包含目标名称（忽略大小写）
                if app_name.lower() in app_name_in_list.lower():
                    # 进一步检查包名是否匹配
                    if '(' in app_name_in_list and ')' in app_name_in_list:
                        bracket_content = app_name_in_list.split('(')[1].split(')')[0]
                        if bracket_content == app_package:
                            logger.debug(f"通过模糊匹配找到应用: {app_name_in_list} (目标: {app_name})")
                            return app

        elif strategy == "包名" and self._is_ios_platform(current_platform):
            for app in apps:
                # 检查多个字段进行包名匹配
                if (app.get('packageName') == app_package or
                    app.get('processName') == app_package):
                    logger.debug(f"iOS设备通过{strategy}字段匹配到应用: {app.get('appName', '')}")
                    return app

                # 检查appName字段中的括号内容
                app_name_in_list = app.get('appName', '')
                if '(' in app_name_in_list and ')' in app_name_in_list:
                    bracket_content = app_name_in_list.split('(')[1].split(')')[0]
                    if bracket_content == app_package:
                        logger.debug(f"iOS设备通过appName括号内容匹配到应用: {app_name_in_list}")
                        return app

        return None

    def _select_best_process(self, target_app: dict, app_package: str, process_name: str) -> tuple:
        """
        进程选择策略

        Args:
            target_app: 目标应用信息
            app_package: 应用包名
            process_name: 用户指定的进程名称

        Returns:
            tuple: (进程名称, 进程PID, 选择原因)
        """
        # 检测当前设备平台类型
        try:
            current_platform = self.get_current_device_platform()
        except Exception as e:
            logger.warning(f"无法检测设备平台类型，默认为未知平台: {e}")
            current_platform = PlatformType.UNKNOWN

        # 获取应用进程信息
        main_pid = self._get_main_pid(target_app)
        sub_processes = target_app.get('subProcess', [])

        # 判断用户是否明确指定了进程名称
        user_specified_process = (process_name and
                                process_name != app_package and
                                process_name.strip())

        if user_specified_process:
            # 策略1: 用户指定策略
            return self._select_process_by_strategy(
                "user_specified", main_pid, sub_processes, app_package, target_app,
                current_platform, process_name)
        else:
            # 策略2: 智能选择策略
            return self._select_process_by_strategy(
                "intelligent", main_pid, sub_processes, app_package, target_app, current_platform)

    def _get_main_pid(self, target_app: dict) -> int:
        """获取主进程PID"""
        try:
            return int(target_app.get('pid', -1))
        except (ValueError, TypeError):
            return -1


    def _select_process_by_strategy(self, strategy_type: str, main_pid: int, sub_processes: list,
                                   app_package: str, target_app: dict, current_platform: PlatformType,
                                   specified_process_name: str = None) -> tuple:
        """
        统一的进程选择策略方法

        Args:
            strategy_type: 策略类型
            main_pid: 主进程PID
            sub_processes: 子进程列表
            app_package: 应用包名
            target_app: 目标应用信息
            current_platform: 当前设备平台类型
            specified_process_name: 用户指定的进程名称（可选）

        Returns:
            tuple: (进程名称, 进程PID, 选择原因)
        """
        platform_prefix = self._get_platform_prefix(current_platform)

        if strategy_type == "user_specified":
            logger.info(f"{platform_prefix}用户指定进程选择: {specified_process_name}")

            # 在子进程中查找
            for subprocess_info in sub_processes:
                process_name = subprocess_info.get('processName', '')
                pid = self._get_process_pid(subprocess_info)

                if process_name == specified_process_name and self._is_pid_valid(pid, current_platform):
                    logger.info(f"{platform_prefix}找到匹配的指定进程: {process_name} (PID: {pid})")
                    return (process_name, pid, f'{platform_prefix}用户指定进程匹配: {specified_process_name}')

            # 检查是否指定的是主进程
            main_process_name = target_app.get('processName', '') or app_package
            if main_process_name == specified_process_name:
                if self._is_ios_platform(current_platform) or main_pid > 0:
                    logger.info(f"{platform_prefix}用户指定的是主进程: {main_process_name} (PID: {main_pid})")
                    return (main_process_name, main_pid, f'{platform_prefix}用户指定主进程匹配: {specified_process_name}')

            # 未找到匹配进程
            available_processes = self._get_available_processes_list(sub_processes, target_app, main_pid)

            logger.error(f"{platform_prefix}未找到用户指定的进程: {specified_process_name}. 可用进程: {available_processes}")
            raise RuntimeError(f"{platform_prefix}未找到用户指定的进程: {specified_process_name}. 可用进程: {available_processes}")

        elif strategy_type == "intelligent":
            logger.info(f"{platform_prefix}智能进程选择，主进程PID: {main_pid}, 子进程数: {len(sub_processes)}")

            # 根据平台确定策略优先级
            if self._is_ios_platform(current_platform):
                strategies = ["包名匹配", "主进程", "第一个可用"]
            else:
                strategies = ["主进程", "包名匹配", "活跃进程"]

            # 依次尝试各种策略
            for strategy in strategies:
                result = self._try_process_strategy(strategy, main_pid, sub_processes, app_package, target_app, current_platform)
                if result:
                    return result

            # 所有策略都失败
            available_processes = self._get_available_processes_list(sub_processes, target_app, main_pid)

            logger.error(f"{platform_prefix}未找到任何可用进程，主进程PID: {main_pid}, 子进程数: {len(sub_processes)}. 可用进程: {available_processes}")
            raise RuntimeError(f"{platform_prefix}未找到任何可用进程. 可用进程: {available_processes}")

    def _try_process_strategy(self, strategy: str, main_pid: int, sub_processes: list,
                             app_package: str, target_app: dict, current_platform: PlatformType) -> tuple:
        """
        尝试特定的进程选择策略

        Args:
            strategy: 策略名称
            main_pid: 主进程PID
            sub_processes: 子进程列表
            app_package: 应用包名
            target_app: 目标应用信息
            current_platform: 当前设备平台类型

        Returns:
            tuple: 成功时返回(进程名称, PID, 原因)，失败时返回None
        """
        platform_prefix = self._get_platform_prefix(current_platform)
        is_ios = self._is_ios_platform(current_platform)

        if strategy == "主进程":
            # iOS设备允许pid为-1，其他设备要求pid > 0
            if not is_ios and main_pid <= 0:
                return None

            # 确定进程名称
            process_name = app_package or target_app.get('processName', '')
            if not process_name and target_app:
                app_name_full = target_app.get('appName', '')
                process_name = app_name_full.split('(')[0] if '(' in app_name_full else app_name_full

            logger.debug(f"{platform_prefix}选择主进程: {process_name} (PID: {main_pid})")
            logger.info(f"选择的进程名: {process_name}")
            reason = f'{platform_prefix}主进程策略: {process_name}' if is_ios else '主进程活跃，优先选择主进程'
            return (process_name, main_pid, reason)

        elif strategy == "包名匹配":
            if not app_package:
                return None

            for subprocess_info in sub_processes:
                process_name = subprocess_info.get('processName', '')
                pid = self._get_process_pid(subprocess_info)

                if process_name == app_package and self._is_pid_valid(pid, current_platform):
                    logger.info(f"{platform_prefix}找到包名匹配的子进程: {process_name} (PID: {pid})")
                    reason = f'{platform_prefix}包名匹配的子进程: {process_name}' if is_ios else f'主进程不活跃，选择包名匹配的子进程: {process_name}'
                    return (process_name, pid, reason)

        elif strategy in ["活跃进程", "第一个可用"]:
            for subprocess_info in sub_processes:
                process_name = subprocess_info.get('processName', '')
                pid = self._get_process_pid(subprocess_info)

                if is_ios:
                    if process_name:  # iOS只要有进程名就可以
                        logger.info(f"iOS设备选择第一个可用子进程: {process_name} (PID: {pid})")
                        return (process_name, pid, f'iOS设备第一个可用子进程: {process_name}')
                else:
                    if pid > 0:  # 其他设备要求活跃进程
                        logger.info(f"选择第一个活跃子进程: {process_name} (PID: {pid})")
                        return (process_name, pid, f'主进程不活跃且无包名匹配进程，选择第一个活跃子进程: {process_name}')

        return None

    def _get_process_pid(self, process_info: dict) -> int:
        """获取进程PID"""
        try:
            raw_pid = process_info.get('pid', -1)
            return int(raw_pid) if raw_pid is not None else -1
        except (ValueError, TypeError):
            return -1

    def _is_ios_platform(self, platform: PlatformType) -> bool:
        """检查是否为iOS平台"""
        return platform == PlatformType.IOS

    def _get_platform_prefix(self, platform: PlatformType) -> str:
        """获取平台前缀字符串"""
        return "iOS设备" if self._is_ios_platform(platform) else "设备"

    def _is_pid_valid(self, pid: int, platform: PlatformType) -> bool:
        """检查PID是否有效（考虑平台差异）"""
        return True if self._is_ios_platform(platform) else pid > 0

    def _get_available_processes_list(self, sub_processes: list, target_app: dict = None, main_pid: int = None) -> list:
        """生成可用进程列表"""
        available_processes = [f"'{p.get('processName', '')}' (PID: {p.get('pid', -1)})" for p in sub_processes]

        if target_app and main_pid is not None:
            main_process_name = target_app.get('processName', '')
            if main_process_name:
                available_processes.insert(0, f"'{main_process_name}' (主进程, PID: {main_pid})")

        return available_processes

class TimeActionsBuilder:
    """
    时间点操作配置构建器

    提供链式调用方式来构建时间点操作配置，使配置更加直观和易用。
    """

    def __init__(self):
        self.actions = {}

    def at_time(self, time_seconds: int) -> 'TimeActionBuilder':
        """
        指定时间点

        Args:
            time_seconds: 时间点（秒）

        Returns:
            TimeActionBuilder实例，用于添加该时间点的操作
        """
        if time_seconds not in self.actions:
            self.actions[time_seconds] = []
        return TimeActionBuilder(self, time_seconds)

    def build(self) -> Dict[int, List[Dict[str, Any]]]:
        """
        构建最终的时间点操作配置

        Returns:
            时间点操作配置字典
        """
        return self.actions.copy()


class TimeActionBuilder:
    """
    单个时间点的操作构建器
    """

    def __init__(self, parent: TimeActionsBuilder, time_seconds: int):
        self.parent = parent
        self.time_seconds = time_seconds

    def set_scene(self, scene_name: str) -> 'TimeActionBuilder':
        """添加设置场景操作"""
        self.parent.actions[self.time_seconds].append({
            "action": "set_scene",
            "scene_name": scene_name
        })
        return self

    def start_record(self) -> 'TimeActionBuilder':
        """添加开始记录操作"""
        self.parent.actions[self.time_seconds].append({
            "action": "start_record"
        })
        return self

    def stop_record(self, file_export: int = 1, file_upload: int = 0) -> 'TimeActionBuilder':
        """添加停止记录操作"""
        self.parent.actions[self.time_seconds].append({
            "action": "stop_record",
            "file_export": file_export,
            "file_upload": file_upload
        })
        return self

    def start_subscene(self, subscene_name: str) -> 'TimeActionBuilder':
        """添加开始子场景操作"""
        self.parent.actions[self.time_seconds].append({
            "action": "start_subscene",
            "subscene_name": subscene_name
        })
        return self

    def stop_subscene(self) -> 'TimeActionBuilder':
        """添加停止子场景操作"""
        self.parent.actions[self.time_seconds].append({
            "action": "stop_subscene"
        })
        return self

    def stop_collect(self) -> 'TimeActionBuilder':
        """添加停止采集操作"""
        self.parent.actions[self.time_seconds].append({
            "action": "stop_collect"
        })
        return self

    def at_time(self, time_seconds: int) -> 'TimeActionBuilder':
        """
        切换到另一个时间点

        Args:
            time_seconds: 新的时间点（秒）

        Returns:
            新时间点的TimeActionBuilder实例
        """
        return self.parent.at_time(time_seconds)

    def build(self) -> Dict[int, List[Dict[str, Any]]]:
        """
        构建最终的时间点操作配置

        Returns:
            时间点操作配置字典
        """
        return self.parent.build()
