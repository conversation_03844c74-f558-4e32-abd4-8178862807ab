import time
from typing import List, Dict, Any, Optional
from utils.common.log_utils import logger
from utils.common.req_utils import RequestMethod, request_base
from defines import BYTEIO_AUTHORIZATION


class ByteIOAPI:
    def __init__(self):
        """
        初始化ByteIO API客户端
        
        注意:
        1. 验证会话一次后有效期为24小时
        2. 获取事件接口前必须先验证会话
        3. 请合理控制调用频率,避免对服务造成压力
        
        Args:
            authorization: API认证token,默认使用defines.py中的BYTEIO_AUTHORIZATION
        """
        self.authorization = BYTEIO_AUTHORIZATION
        self.headers = {
            'authorization': self.authorization,
            'Content-Type': 'application/json'
        }
        self.verify_session_url = 'https://openapi-alisg.tiktok-row.org/openapi/byteio-sg/open/v2/log_verify/verify_session'
        self.query_event_url = 'https://io-sg.tiktok-row.net/byteio/open/log_verify/query/bytedoc/event/'
        self.last_verify_time = 0
        self.verify_valid_duration = 24 * 60 * 60  # 24小时有效期

    def verify_session(self, app_id: int, device_ids: List[int], user_unique_ids: List[str] = None) -> Dict[str, Any]:
        """
        验证会话
        
        Args:
            app_id: 应用ID
            device_ids: 设备ID列表
            user_unique_ids: 用户唯一ID列表
            
        Returns:
            验证结果
        """
        if user_unique_ids is None:
            user_unique_ids = []
            
        data = {
            "app_id": app_id,
            "device_ids": device_ids,
            "user_unique_ids": user_unique_ids
        }
        
        logger.debug(f"正在验证会话, app_id: {app_id}, device_ids: {device_ids}")
        response = request_base.send_request(
            method=RequestMethod.POST.value,
            url=self.verify_session_url,
            headers=self.headers,
            json=data
        )
        
        if response.status_code == 200:
            self.last_verify_time = time.time()
            logger.debug("会话验证成功")
        else:
            logger.error(f"会话验证失败: {response.text}")
            
        return response.json()

    def is_session_valid(self) -> bool:
        """
        检查会话是否有效
        
        Returns:
            会话是否有效
        """
        if self.last_verify_time == 0:
            return False
            
        is_valid = (time.time() - self.last_verify_time) < self.verify_valid_duration
        if not is_valid:
            logger.warning("会话已过期")
        return is_valid

    def query_events(
        self,
        app_id: int,
        device_id: int,
        start_time: int,
        end_time: int,
        log_types: List[str],
        event_names: List[str]
    ) -> Dict[str, Any]:
        """
        查询事件
        
        Args:
            app_id: 应用ID
            device_id: 设备ID
            start_time: 开始时间戳
            end_time: 结束时间戳
            log_types: 日志类型列表
            event_names: 事件名称列表
            
        Returns:
            事件查询结果
        """
        if not self.is_session_valid():
            raise Exception("会话无效或已过期,请先验证会话")
            
        data = {
            "app_id": app_id,
            "device_id": device_id,
            "start_time": start_time,
            "end_time": end_time,
            "log_types": log_types,
            "event_names": event_names
        }
        
        logger.debug(f"正在查询事件, app_id: {app_id}, device_id: {device_id}, start_time: {start_time}, end_time: {end_time}, log_types: {log_types}, event_names: {event_names}")
        response = request_base.send_request(
            method=RequestMethod.POST.value,
            url=self.query_event_url,
            headers=self.headers,
            json=data
        )
        
        return response.json()


byteio_api = ByteIOAPI()

if __name__ == '__main__':
    # 验证会话
    verify_result = byteio_api.verify_session(
        app_id=1233,
        device_ids=[7306110349106185774]
    )
    logger.debug(verify_result)

    # 查询事件
    events = byteio_api.query_events(
        app_id=1233,
        device_id=7306110349106185774,
        start_time=1745737200,
        end_time=1745737500,
        log_types=["mario_event"],
        event_names=["play_session_events"]
    )
    logger.debug(events)
