import time
import os
import json
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Union, Any, List, Dict
from bytedance.ds import PerfDeviceType, PerfDevice, PerfClient
from bytedance.ds.perf_config import PerfType, PerfApp
from utils.common.log_utils import logger
from defines import PerfCollectMode, BYTEST_TOKEN


class DsPerfManager:
    """DS性能数据采集管理器"""

    def __init__(self, **kwargs):
        """初始化DS采集管理器

        Args:
            **kwargs: 全局配置参数：
                - bytest_token: ByteTest令牌（必需）
                - max_workers: 最大工作线程数（默认：10）
        """
        self.global_config = {
            "bytest_token": BYTEST_TOKEN,
            "max_workers": 10,
            **kwargs
        }

        if not self.global_config.get("bytest_token"):
            raise ValueError("必须提供bytest_token参数")

        self.clients = {}
        self.target_combinations = []
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=self.global_config["max_workers"])

    def _log(self, client_key: str, level: str, message: str):
        """带客户端标识的日志输出"""
        formatted_message = f"[{client_key}] {message}"
        getattr(logger, level)(formatted_message)

    def prepare_targets(self, configs: List[Dict[str, Any]]):
        """准备目标组合

        Args:
            configs: 配置列表，每个配置项包含以下字段：
                - device_id: 设备ID（必需）
                - app_name: 应用显示名称（必需）
                - package_name: 应用包名（必需）
                - scene_name: 场景名称（必需）
                - duration: 采集持续时间（必需）
                - connection_type: 设备连接方式（可选，默认：PerfCollectMode.WIRE_COLLECT）
                - device_ip: 设备IP地址（无线连接时必需）
                - device_port: 设备端口（无线连接时必需）
                - device_type: 设备类型（可选，默认：PerfDeviceType.Android）
                - interval: 采集间隔（可选，默认：1000ms）
                - collect_metrics: 采集指标列表（可选，默认：全部指标）
        """
        if not configs or not isinstance(configs, list):
            raise ValueError("configs必须是非空的配置列表")

        for i, config in enumerate(configs):
            required_fields = ["device_id", "app_name", "package_name", "scene_name", "duration"]
            for field in required_fields:
                if field not in config:
                    raise ValueError(f"配置项 {i} 缺少必需字段: {field}")

            connection_type = config.get("connection_type", PerfCollectMode.WIRE_COLLECT)
            if connection_type not in [PerfCollectMode.WIRE_COLLECT, PerfCollectMode.WIRELESS_COLLECT]:
                raise ValueError(f"配置项 {i} 的 connection_type 必须是 PerfCollectMode.WIRE_COLLECT 或 PerfCollectMode.WIRELESS_COLLECT")

            if connection_type == PerfCollectMode.WIRELESS_COLLECT:
                if not config.get("device_ip"):
                    raise ValueError(f"配置项 {i} 使用无线连接时必须提供 device_ip")
                if not config.get("device_port"):
                    raise ValueError(f"配置项 {i} 使用无线连接时必须提供 device_port")

            collect_metrics = config.get("collect_metrics")
            if collect_metrics is not None:
                if not isinstance(collect_metrics, list):
                    raise ValueError(f"配置项 {i} 的 collect_metrics 必须是列表类型")
                for metric in collect_metrics:
                    if not isinstance(metric, PerfType):
                        raise ValueError(f"配置项 {i} 的 collect_metrics 中包含无效的指标类型: {metric}，必须是 PerfType 枚举值")

        self.target_combinations = []
        for config in configs:
            target = {
                "device_id": config["device_id"],
                "app_name": config["app_name"],
                "package_name": config["package_name"],
                "scene_name": config["scene_name"],
                "duration": config["duration"],
                "connection_type": config.get("connection_type", PerfCollectMode.WIRE_COLLECT),
                "device_ip": config.get("device_ip"),
                "device_port": config.get("device_port"),
                "device_type": config.get("device_type", PerfDeviceType.Android),
                "interval": config.get("interval", 1000),
                "collect_metrics": config.get("collect_metrics"),
                "unique_key": f"{config['device_id']}_{config['package_name']}"
            }
            self.target_combinations.append(target)
        logger.info(f"准备了 {len(self.target_combinations)} 个目标组合")
        return self.target_combinations

    def init_clients(self) -> bool:
        """初始化DS客户端"""
        if not self.target_combinations:
            raise ValueError("必须先调用 prepare_targets 方法")

        logger.info(f"初始化 {len(self.target_combinations)} 个DS客户端")

        success_count = 0
        for target in self.target_combinations:
            try:
                client_key = target["unique_key"]
                connection_type = target["connection_type"]
                device_type = target["device_type"]

                if connection_type == PerfCollectMode.WIRELESS_COLLECT:
                    device = PerfDevice(
                        device_type=device_type,
                        device_ip=target["device_ip"],
                        device_port=target["device_port"],
                        wireless_connect=True
                    )
                    self._log(client_key, "info", f"使用无线连接: {target['device_ip']}:{target['device_port']}")
                else:
                    device = PerfDevice(
                        device_type=device_type,
                        udid=target["device_id"]
                    )
                    self._log(client_key, "info", f"使用有线连接: {target['device_id']}")

                perf_client = PerfClient(
                    perf_device=device,
                    bytest_token=self.global_config["bytest_token"]
                )

                self._add_collect_metrics(perf_client, target["package_name"], target["interval"], target["collect_metrics"])

                self.clients[client_key] = {
                    "client": perf_client,
                    "target": target,
                    "status": "initialized"
                }

                self._log(client_key, "info", "客户端初始化成功")
                success_count += 1

            except Exception as e:
                self._log(client_key, "error", f"客户端初始化失败: {e}")

        success = success_count == len(self.target_combinations)
        logger.info(f"客户端初始化完成，成功率: {success_count}/{len(self.target_combinations)}")
        return success

    def _add_collect_metrics(self, perf_client: PerfClient, package_name: str, interval: int, collect_metrics: list = None):
        """添加采集指标"""
        default_perf_types = [
            PerfType.FPS, PerfType.CPU, PerfType.GPU, PerfType.MEM,
            PerfType.DISK, PerfType.NET, PerfType.BATTERY, PerfType.ENERGY
        ]

        if collect_metrics is None:
            perf_types = default_perf_types
        else:
            perf_types = collect_metrics

        for perf_type in perf_types:
            perf_client.add_perf_type(perf_type)

        perf_client.set_interval(interval=interval)
        perf_client.add_target_app(PerfApp(package_name=package_name))

    def start_collection(self) -> List[threading.Thread]:
        """启动批量性能采集"""
        if not self.clients:
            logger.error("没有可用的客户端")
            return []

        collection_threads = []
        for client_key, client_info in self.clients.items():
            if client_info["status"] != "initialized":
                continue

            thread = self._create_collection_thread(client_info)
            collection_threads.append(thread)
            thread.start()

            self._log(client_key, "info", f"启动采集线程: {client_info['target']['scene_name']}")
            time.sleep(0.5)

        logger.info(f"批量启动采集，线程数: {len(collection_threads)}")
        return collection_threads

    def _create_collection_thread(self, client_info: Dict[str, Any]) -> threading.Thread:
        """创建采集线程"""
        def collection_task():
            client_key = client_info["target"]["unique_key"]
            perf_client = client_info["client"]
            target = client_info["target"]

            try:
                self._log(client_key, "info", f"开始性能采集: {target['scene_name']}")

                # 开始采集
                if not perf_client.start_collect():
                    self._log(client_key, "error", "开始采集失败")
                    return

                client_info["status"] = "collecting"
                self._log(client_key, "info", f"采集进行中，持续时间: {target['duration']}秒")

                # 等待采集完成
                time.sleep(target["duration"])

                # 停止采集
                if not perf_client.stop_collect():
                    self._log(client_key, "error", "停止采集失败")
                    return

                # 获取性能数据
                perf_data = perf_client.get_perf_data()
                if not perf_data:
                    self._log(client_key, "error", "获取性能数据失败")
                    return

                # 记录采集结果
                self._record_collection_result(client_info, perf_data)

                client_info["status"] = "completed"
                self._log(client_key, "info", "性能采集完成")

            except Exception as e:
                self._log(client_key, "error", f"采集过程异常: {e}")
                client_info["status"] = "error"
            finally:
                # 关闭DS服务
                try:
                    perf_client.ds_exit()
                    self._log(client_key, "info", "DS服务已关闭")
                except Exception as e:
                    self._log(client_key, "warning", f"关闭DS服务失败: {e}")

        return threading.Thread(target=collection_task, name=f"DS-{client_info['target']['unique_key']}")

    def _record_collection_result(self, client_info: Dict[str, Any], perf_data: List[dict]):
        """记录采集结果并保存原始性能数据"""
        target = client_info["target"]
        client_key = target["unique_key"]
        save_path = None

        try:
            # 创建简化的保存目录：data/{device_id}/{package_name}/
            save_dir = os.path.join(
                "data",
                target["device_id"],
                target["package_name"]
            )

            # 确保目录存在
            try:
                os.makedirs(save_dir, exist_ok=True)
                self._log(client_key, "info", f"创建保存目录: {save_dir}")
            except Exception as e:
                self._log(client_key, "error", f"创建目录失败: {e}")
                # 目录创建失败，但不中断采集流程
                save_dir = None

            # 保存原始性能数据
            if save_dir:
                try:
                    data_file_path = os.path.join(save_dir, "ds_raw_perf_data.json")
                    with open(data_file_path, "w", encoding="utf-8") as f:
                        json.dump(perf_data, f, ensure_ascii=False, indent=4)

                    save_path = data_file_path
                    self._log(client_key, "info", f"原始性能数据已保存: {data_file_path}")

                except Exception as e:
                    self._log(client_key, "error", f"保存原始数据失败: {e}")
                    # 文件保存失败，但不影响采集状态

            # 记录采集结果到客户端信息
            client_info["results"] = {
                "data_count": len(perf_data),
                "scene_name": target["scene_name"],
                "duration": target["duration"],
                "package_name": target["package_name"],
                "save_path": save_path
            }

            self._log(client_key, "info", f"性能数据采集完成，数据条数: {len(perf_data)}")

        except Exception as e:
            self._log(client_key, "error", f"记录采集结果失败: {e}")

    def stop_all(self):
        """停止所有客户端"""
        logger.info("开始停止所有DS客户端")

        # 停止线程池
        if hasattr(self, '_executor'):
            self._executor.shutdown(wait=True)

        # 清理客户端
        for client_key, client_info in self.clients.items():
            try:
                if "client" in client_info:
                    client_info["client"].ds_exit()
                    self._log(client_key, "info", "客户端已清理")
            except Exception as e:
                self._log(client_key, "warning", f"清理客户端失败: {e}")

        self.clients.clear()
        logger.info("所有客户端已停止")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_all()



    def run_performance_test(self, configs: List[Dict[str, Any]], **kwargs) -> bool:
        """
        执行性能数据采集测试（仅采集，不进行数据处理）

        Args:
            configs: 配置列表，每个配置项包含以下字段：
                - device_id: 设备ID（必需）
                - app_name: 应用显示名称（必需）
                - package_name: 应用包名（必需）
                - scene_name: 场景名称（必需）
                - duration: 采集持续时间（必需）
                - connection_type: 设备连接方式（可选，默认：PerfCollectMode.WIRE_COLLECT）
                - device_ip: 设备IP地址（无线连接时必需）
                - device_port: 设备端口（无线连接时必需）
                - device_type: 设备类型（可选，默认：PerfDeviceType.Android）
                - interval: 采集间隔（可选，默认：1000ms）
                - collect_metrics: 采集指标列表（可选，默认：全部指标）
                  支持值：PerfType枚举值列表，如[PerfType.FPS, PerfType.CPU]
            **kwargs: 全局配置参数：
                - bytest_token: ByteTest令牌（必需）
                - max_workers: 最大工作线程数（默认：10）

        Returns:
            bool: 采集是否成功完成
        """
        try:
            with DsPerfManager(**kwargs) as manager:
                # 准备目标组合
                manager.prepare_targets(configs)

                # 初始化DS客户端
                if not manager.init_clients():
                    logger.error("初始化客户端失败")
                    return False

                # 开始性能采集
                threads = manager.start_collection()
                if not threads:
                    logger.error("启动采集失败")
                    return False

                # 等待采集完成
                for thread in threads:
                    thread.join()

                logger.info("DS性能测试完成")
                return True

        except Exception as e:
            logger.error(f"DS性能测试执行失败: {e}")
            return False


class DsPerfMonitor:
    """DS性能监控器"""

    def __init__(self, **kwargs):
        """初始化DS性能监控器

        Args:
            **kwargs: 全局配置参数：
                - bytest_token: ByteTest令牌（必需）
        """
        self.global_config = {
            "bytest_token": None,
            **kwargs
        }

        if not self.global_config.get("bytest_token"):
            raise ValueError("必须提供bytest_token参数")

        logger.info("DS监控器初始化完成")

    def connect_wire_device(self, perf_device_type: PerfDeviceType, udid: str = None) -> PerfDevice:
        """本地有线连接设备"""
        logger.info(f"[基础性能] 开始有线连接设备 设备:{udid}")
        target_device = None
        if perf_device_type == PerfDeviceType.Android or perf_device_type == PerfDeviceType.iOS:
            target_device = PerfDevice(device_type=perf_device_type, udid=udid)
        else:
            logger.error(f"[基础性能] 不支持的设备类型: {perf_device_type}")
            raise ValueError(f"不支持的设备类型: {perf_device_type}")

        logger.info(f"[基础性能] 设备连接成功 设备:{udid}")
        return target_device

    def connect_wireless_device(self, perf_device_type: PerfDeviceType, udid: str = None, device_ip: str = None,
                                port: str = None) -> PerfDevice:
        """本地无线连接设备"""
        logger.info(f"[基础性能] 开始无线连接设备")

        if perf_device_type == PerfDeviceType.Android:
            if not device_ip or not port:
                logger.error("[基础性能] Android设备需要提供device_ip和device_port参数")
            target_device = PerfDevice(device_type=perf_device_type, device_ip=device_ip, device_port=port,
                                       wireless_connect=True)
        elif perf_device_type == PerfDeviceType.iOS:
            if udid is None:
                logger.error("[基础性能] iOS设备需要提供udid参数")
            target_device = PerfDevice(device_type=perf_device_type, udid=udid, wireless_connect=True)
        else:
            logger.error("[基础性能] 不支持的设备类型")
            raise ValueError("不支持的设备类型")

        logger.info(f"[基础性能] 无线设备连接成功")
        return target_device

    def init_perf_client(self, target_device: PerfDevice, bytest_token: str = BYTEST_TOKEN) -> Union[list[Any], PerfClient]:
        """初始化perfClient"""
        logger.info("[基础性能] 开始初始化perfClient")

        if target_device is None:
            logger.error("[基础性能] 获取设备失败")
            return []

        perf_client = PerfClient(perf_device=target_device, bytest_token=bytest_token)
        logger.info("[基础性能] perfClient初始化成功")
        return perf_client

    def add_collect_metrics(self, perf_client: PerfClient, package_name: str, collect_metrics: list[int] = None,
                            interval: int = 1000) -> None:
        """添加采集指标"""
        logger.info("[基础性能] 开始添加采集指标")

        default_perf_types = [PerfType.FPS, PerfType.CPU, PerfType.GPU, PerfType.MEM, PerfType.DISK, PerfType.NET,
                              PerfType.BATTERY, PerfType.ENERGY]

        if collect_metrics is None:
            perf_types = default_perf_types
        else:
            perf_types = [PerfType(value) for value in collect_metrics]

        for perf_type in perf_types:
            perf_client.add_perf_type(perf_type)

        perf_client.set_interval(interval=interval)
        perf_client.add_target_app(PerfApp(package_name=package_name))

        logger.info(f"[基础性能] 采集指标添加成功 指标:{perf_types}")

    def start_collect(self, perf_client: PerfClient) -> bool:
        """开始采集"""
        logger.info("[基础性能] 准备开始采集")

        success = perf_client.start_collect()
        if not success:
            path = perf_client.pull_log()
            logger.error(f"[基础性能] 开始采集失败 日志路径:{path}")
            return False

        logger.info("[基础性能] 采集已开始")
        return True

    def stop_collect(self, perf_client: PerfClient) -> bool:
        """结束采集"""
        logger.info("[基础性能] 准备结束采集")

        success = perf_client.stop_collect()
        if not success:
            path = perf_client.pull_log()
            logger.error(f"[基础性能] 结束采集失败 日志路径:{path}")
            return False

        logger.info("[基础性能] 采集已结束")
        return True

    def get_perf_data(self, perf_client: PerfClient) -> list:
        """获取性能数据"""
        logger.info("[基础性能] 开始获取性能数据")

        perf_data = perf_client.get_perf_data()
        if perf_data is None:
            path = perf_client.pull_log()
            logger.error(f"[基础性能] 获取性能数据失败 日志路径:{path}")
            return []

        logger.info("[基础性能] 性能数据获取成功")
        return perf_data

    def close_ds_server(self, perf_client: PerfClient) -> None:
        """关闭ds服务"""
        logger.info("[基础性能] 准备关闭ds服务")
        perf_client.ds_exit()
        logger.info("[基础性能] ds服务已关闭")

    def build_ds_save_path(self, device_id: str, package_name: str) -> str:
        """构建DS性能数据保存路径

        Args:
            device_id: 设备ID
            package_name: 应用包名

        Returns:
            str: DS数据保存路径，格式为 data/{device_id}/{package_name}/
        """
        try:
            # DS工具简化目录结构：data/{device_id}/{package_name}/
            save_dir = os.path.join("data", device_id, package_name)

            # 确保目录存在
            os.makedirs(save_dir, exist_ok=True)
            logger.info(f"[DS性能监控] 构建保存路径: {save_dir}")
            return save_dir

        except Exception as e:
            logger.error(f"[DS性能监控] 构建保存路径失败: {e}")
            raise

    def start_collection_for_app(self, app, perf_collect_mode, perf_device_ip: str = None,
                                collect_metrics: list = None, collect_interval: int = 1000,
                                collect_duration: int = 30) -> Any:
        """开始DS性能数据采集

        Args:
            app: Android或iOS TikTok应用实例
            perf_collect_mode: 采集模式（有线/无线）
            perf_device_ip: 设备IP（无线连接时需要）
            collect_metrics: 采集指标列表
            collect_interval: 采集间隔（毫秒）

        Returns:
            DS性能客户端对象
        """
        from defines import PerfCollectMode

        udid = app.get_device().udid
        logger.info(f"[DS性能] 开始性能数据采集 设备:{udid}")

        # 直接根据平台获取包名和设备类型
        if hasattr(app, 'package_name'):  # Android应用
            package_name = app.package_name
            perf_device_type = PerfDeviceType.Android
        else:  # iOS应用
            package_name = app.bundle_id
            perf_device_type = PerfDeviceType.iOS

        # 使用全局DS监控器实例
        monitor = ds_perf_monitor

        # 连接设备
        if perf_collect_mode == PerfCollectMode.WIRE_COLLECT:
            target_device = monitor.connect_wire_device(perf_device_type, udid)
        else:
            target_device = monitor.connect_wireless_device(
                perf_device_type, udid=udid, device_ip=perf_device_ip, port=5555
            )

        # 初始化并启动性能采集
        perf_client = monitor.init_perf_client(target_device)
        monitor.add_collect_metrics(perf_client, package_name, collect_metrics, collect_interval)
        monitor.start_collect(perf_client)

        # 等待采集完成
        import time
        logger.info(f"[DS性能] 开始等待 {collect_duration} 秒采集完成")
        time.sleep(collect_duration)

        # 停止采集
        monitor.stop_collect(perf_client)
        logger.info(f"[DS性能] 性能数据采集完成 设备:{udid}")
        return perf_client



if __name__ == "__main__":
    # DS性能测试配置示例
    configs = [
        # DS-Android-有线连接-TikTok
        # {
        #     "device_id": "R5CWB30CMWK",
        #     "app_name": "TikTok",
        #     "package_name": "com.zhiliaoapp.musically",
        #     "scene_name": "TikTok性能测试",
        #     "duration": 30,
        #     "connection_type": PerfCollectMode.WIRE_COLLECT,  # 有线连接
        #     "device_type": PerfDeviceType.Android,  # 设备类型
        #     "interval": 1000,
        #     "collect_metrics": [PerfType.FPS, PerfType.CPU, PerfType.GPU, PerfType.MEM, PerfType.NET]  # 采集指标：帧率、CPU、GPU、内存、网络
        # },
        # DS-Android-有线连接-飞书
        # {
        #     "device_id": "R5CWB30CMWK",
        #     "app_name": "飞书",
        #     "package_name": "com.ss.android.lark",
        #     "scene_name": "飞书性能测试",
        #     "duration": 30,
        #     "connection_type": PerfCollectMode.WIRE_COLLECT,
        #     "device_type": PerfDeviceType.Android,
        #     "interval": 1000,
        #     "collect_metrics": [PerfType.FPS, PerfType.CPU, PerfType.MEM, PerfType.BATTERY]  # 采集指标：帧率、CPU、内存、电池
        # },
        # DS-Android-有线连接-飞连
        # {
        #     "device_id": "R5CWB30CMWK",
        #     "app_name": "飞连",
        #     "package_name": "com.volcengine.corplink",
        #     "scene_name": "飞连性能测试",
        #     "duration": 30,
        #     "connection_type": PerfCollectMode.WIRE_COLLECT,
        #     "device_type": PerfDeviceType.Android,
        #     "interval": 1000,
        #     "collect_metrics": [PerfType.FPS, PerfType.CPU, PerfType.GPU, PerfType.MEM, PerfType.NET]  # 采集指标：帧率、CPU、GPU、内存、网络
        # },
        # DS-iOS-有线连接-飞书
        {
            "device_id": "00008101-001865592650001E",
            "app_name": "飞书",
            "package_name": "com.bytedance.ee.lark",
            "scene_name": "飞书性能测试",
            "duration": 30,
            "connection_type": PerfCollectMode.WIRE_COLLECT, 
            "device_type": PerfDeviceType.iOS, 
            "interval": 1000,
            "collect_metrics": [PerfType.FPS, PerfType.CPU, PerfType.MEM, PerfType.BATTERY]  # 采集指标：帧率、CPU、内存、电池
        },
        # DS-iOS-有线连接-飞连
        {
            "device_id": "00008101-001865592650001E",
            "app_name": "飞连",
            "package_name": "com.volcengine.corplink",
            "scene_name": "飞连性能测试",
            "duration": 30,
            "connection_type": PerfCollectMode.WIRE_COLLECT,
            "device_type": PerfDeviceType.iOS,
            "interval": 1000,
            "collect_metrics": [PerfType.FPS, PerfType.CPU, PerfType.GPU, PerfType.MEM, PerfType.NET]  # 采集指标：帧率、CPU、GPU、内存、网络
        }
    ]

    # 全局配置
    kwargs = {
        "bytest_token": BYTEST_TOKEN,  # 使用defines.py中的统一配置
        "max_workers": 5
    }

    # 一键执行性能采集
    manager = DsPerfManager(**kwargs)
    manager.run_performance_test(configs, **kwargs)


# 创建全局DS性能管理器和监控器实例
ds_perf_manager = DsPerfManager()
ds_perf_monitor = DsPerfMonitor(bytest_token=BYTEST_TOKEN)