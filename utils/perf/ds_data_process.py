"""DS性能数据处理模块

专门处理DS工具采集的性能数据，支持Android/iOS平台的DS数据处理，
包括数据验证、指标计算、文件操作等功能。
"""

import json
import os
import re
from typing import List, Optional, Dict, Mapping, Tuple, Any, Union
from dataclasses import dataclass
from statistics import mean

from defines import PlatformType
from utils.common.log_utils import logger
from utils.perf.trace_data_manager import trace_data_manager
from utils.perf.byteio_data_manager import byteio_data_manager
from utils.perf.ds_perf_monitor import ds_perf_monitor
from utils.perf.profile_collector import profile_collector
from utils.common.time_utils import convert_timestamp_to_digits


def camel_to_snake(name: str) -> str:
    """将驼峰命名转换为蛇形命名"""
    if '_' in name:
        return name
    name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()


@dataclass
class DsPerfMetrics:
    """DS性能指标数据类"""
    platform_type: str
    metrics_values: Dict[str, List[float]]
    list_metrics_values: Dict[str, List[List[float]]]  # 新增：存储列表类型字段的数据

    def calculate_averages(self) -> Mapping[str, Union[Optional[float], List[Optional[float]]]]:
        """计算所有指标的平均值"""
        result: Dict[str, Union[Optional[float], List[Optional[float]]]] = {}

        # 处理普通数值字段
        for name, values in self.metrics_values.items():
            snake_name = camel_to_snake(name)
            result[snake_name] = self._calculate_metric_average(values)

        # 处理列表字段
        for name, list_values in self.list_metrics_values.items():
            snake_name = camel_to_snake(name)
            result[snake_name] = self._calculate_list_metric_average(list_values)

        return result

    def _calculate_metric_average(self, values: List[float]) -> Optional[float]:
        """计算单个指标的平均值"""
        if not values:
            return None

        valid_values = [float(v) for v in values
                       if self._is_valid_number(v) and float(v) != 0]

        if not valid_values:
            return 0 if any(self._is_valid_number(v) and float(v) == 0 for v in values) else None

        return round(mean(valid_values), 2)

    def _calculate_list_metric_average(self, list_values: List[List[float]]) -> Optional[List[Optional[float]]]:
        """计算列表字段的平均值

        Args:
            list_values: 每个时间点的列表数据，例如：
                [[1.0, 2.0, 3.0], [1.5, 2.5, 3.5], [2.0, 3.0, 4.0]]

        Returns:
            每个索引位置的平均值列表，例如：[1.5, 2.5, 3.5]
        """
        if not list_values:
            return None

        # 过滤掉空列表
        valid_lists = [lst for lst in list_values if lst and isinstance(lst, list)]
        if not valid_lists:
            return None

        # 确定列表的最大长度
        max_length = max(len(lst) for lst in valid_lists)
        if max_length == 0:
            return None

        # 计算每个索引位置的平均值
        result = []
        for i in range(max_length):
            index_values = []
            for lst in valid_lists:
                if i < len(lst) and self._is_valid_number(lst[i]):
                    index_values.append(float(lst[i]))

            if index_values:
                # 过滤掉0值，如果有非0值的话
                non_zero_values = [v for v in index_values if v != 0]
                if non_zero_values:
                    result.append(round(mean(non_zero_values), 2))
                else:
                    # 如果都是0，则平均值为0
                    result.append(0.0)
            else:
                result.append(None)

        return result

    def _is_valid_number(self, value: Any) -> bool:
        """检查值是否为有效数字"""
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False

class DsPerfMetricsCalculator:
    """DS性能指标计算工具类"""

    def _is_valid_number(self, value: Any) -> bool:
        """检查值是否为有效数字"""
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False

    # DS数据平台独有指标字段（基于实际数据格式）
    ANDROID_EXCLUSIVE_FIELDS = {
        # Android独有的系统级指标
        "cpuNormalizedTotalUsage", "cpuNormalizedCoreUsage", "cpuFreq", "cpuTemp",
        "gpuUsage", "gpuClk", "gpuLoad", "batteryCurrent", "batteryVoltage", "batteryPower",
        # Mali GPU指标
        "maliGpuActive", "maliFragmentActive", "maliNonFragmentActive", "maliTilerActive",
        "maliOverdraw", "maliPixelThroughput", "maliL2Texture", "maliL2LoadStore",
        "maliBusRead", "maliBusWrite", "maliCulledPrimitives", "maliInputPrimitives", "maliVisiblePrimitives",
        # Adreno GPU指标（高通骁龙）
        "adrenoFragmentInstructions", "adrenoFragmentsShaded", "adrenoReadTotal", "adrenoWriteTotal",
        "adrenoTextureMemoryReadBW", "adrenoTimeCompute", "adrenoTimeShadingFragments",
        "adrenoTimeShadingVertices", "adrenoVertexInstructions", "adrenoVertexMemoryRead", "adrenoVerticesShaded",
        # PVR GPU指标
        "pvrComputerActive", "pvrRendererActive", "pvrTilerActive", "pvrSpmActive",
        "pvrHSREfficiency", "pvrMemoryTotalRate", "pvrMemoryReadRate", "pvrMemoryWriteRate",
        "pvrInterfaceLoad", "pvrShadedVertices", "pvrShadedPixels", "pvrOverDraw", "pvrPixelThroughput",
        # Android详细内存指标
        "memArtMmap", "memCode", "memDexMmap", "memEglMtrack", "memHeap", "memNative",
        "memNativeHeapSummary", "memOatMmap", "memOtherMmap", "memPrivateOther",
        "memSWAP", "memStack", "memSystem", "memTtfMmap",
        # Android电池详细功耗
        "batteryAudioPower", "batteryBluetoothPower", "batteryCameraPower", "batteryCpuPower",
        "batteryFlashlightPower", "batteryGpsPower", "batteryGpuPower", "batteryQuantity",
        "batteryRadioPower", "batterySensorPower", "batteryVideoPower", "batteryWakePower", "batteryWifiPower",
        # Android独有的进程级指标
        "cpuNormalizedProcUsage"
    }

    IOS_EXCLUSIVE_FIELDS = {
        # iOS独有的系统级指标
        "gpuDevice", "gpuRender", "gpuTiler", "memTotalFootprint",
        "batteryTemp", "batteryAmperage", "batteryCapacity", "batteryPercent",
        "lightStumbleCount", "mediumStumbleCount", "heavyStumbleCount",
        # iOS独有的进程级指标
        "memResident", "memFootprint", "memVirtual", "ctxSwitch", "intWakeups",
        "energyCost", "energyCPUCost", "energyGPUCost", "energyNetCost"
    }

    # 时间相关字段（不应计算平均值的字段）
    TIME_FIELDS = {
        "time", "pcTime", "deviceTime", "timestamp", "timeStamp",
        "device_time", "pc_time", "startTime", "endTime"
    }

    # 元数据字段（不应计算平均值的字段）
    METADATA_FIELDS = {
        # 标识字段
        "pid", "processName", "processData",
        # 时间戳字段
        "batteryHardWareTimestamp",
        # JSON格式重复字段
        "pvrMemoryReadRateJson", "pvrMemoryTotalRateJson", "pvrMemoryWriteRateJson",
        "pvrShadedPixelsJson", "pvrShadedVerticesJson"
    }

    # 过滤字段（需要完全过滤掉的字段）
    FILTERED_FIELDS = {
        "frameTime", "label"
    }

    # 列表类型字段（需要特殊处理的字段）
    LIST_FIELDS = {
        # CPU相关列表字段
        "cpuCoreUsage", "cpuNormalizedCoreUsage", "cpuFreq",
        # 帧时间字段（可能是复杂对象列表）
        "frameTime"
    }

    # 通用字段（两个平台都有的字段，排除时间字段）
    COMMON_FIELDS = {
        # 基础性能指标
        "cpuTotalUsage", "cpuCoreUsage", "fps", "cpuProcUsage",
        # 内存相关指标（Android和iOS都有）
        "memPSS", "memUSS", "memDalvikHeap", "memDalvikOther", "memAshmem",
        "memGfx", "memGL", "memUnknown", "memGraphics", "memGfxDev", "memOtherDev",
        "memSoMmap", "memJarMmap", "memApkMmap",
        # 网络指标（Android和iOS都有）
        "netTotalReceive", "netTotalSent", "netProcReceive", "netProcSent",
        # 系统指标（Android和iOS都有）
        "threadCount", "fdCount", "diskRead", "diskWritten"
    }

    def calculate_avg_perf_data(self, perf_data: List[dict]) -> Mapping[str, Optional[float]]:
        """计算性能数据的平均值"""
        try:
            logger.info("[性能数据处理] 开始计算性能数据平均值")

            platform_type = self._detect_platform_type(perf_data)
            if not platform_type:
                raise ValueError("无法确定平台类型：未找到平台独有的性能指标字段")

            # 获取所有相关指标
            all_metrics = self._get_all_metrics(perf_data, platform_type)

            # 初始化并收集指标数据
            metrics = DsPerfMetrics(
                platform_type=str(platform_type.value),
                metrics_values={field_name: [] for field_name in all_metrics if field_name not in self.LIST_FIELDS},
                list_metrics_values={field_name: [] for field_name in all_metrics if field_name in self.LIST_FIELDS}
            )

            self._collect_metrics_data(perf_data, metrics, all_metrics)

            result = metrics.calculate_averages()
            logger.info(f"[性能数据处理] 平均值计算完成 结果:{result}")
            return result

        except Exception as e:
            logger.error(f"[性能数据处理] 计算平均值失败: {str(e)}")
            return {}

    def _detect_platform_type(self, perf_data: List[dict]) -> Optional[PlatformType]:
        """基于独有指标字段检测平台类型"""
        return self._detect_by_exclusive_fields(perf_data)

    def _detect_by_exclusive_fields(self, perf_data: List[dict]) -> Optional[PlatformType]:
        """通过平台独有字段检测平台类型

        使用每个平台独有的性能指标字段来判断平台类型，
        这种方式比基于进程名的检测更加通用和可靠。
        """
        android_exclusive_count = 0
        ios_exclusive_count = 0

        for item in perf_data:
            if not isinstance(item, dict):
                continue

            # 检查系统级独有字段
            item_keys = set(item.keys())
            android_exclusive_count += len(self.ANDROID_EXCLUSIVE_FIELDS & item_keys)
            ios_exclusive_count += len(self.IOS_EXCLUSIVE_FIELDS & item_keys)

            # 检查进程数据中的独有字段
            if "processData" in item and isinstance(item["processData"], list):
                for process in item["processData"]:
                    if isinstance(process, dict):
                        process_keys = set(process.keys())
                        android_exclusive_count += len(self.ANDROID_EXCLUSIVE_FIELDS & process_keys)
                        ios_exclusive_count += len(self.IOS_EXCLUSIVE_FIELDS & process_keys)

        # 基于独有字段数量判断平台
        if android_exclusive_count > ios_exclusive_count:
            logger.info(f"[性能数据处理] 检测到Android平台 (独有字段数: {android_exclusive_count} vs {ios_exclusive_count})")
            return PlatformType.ANDROID
        elif ios_exclusive_count > android_exclusive_count:
            logger.info(f"[性能数据处理] 检测到iOS平台 (独有字段数: {ios_exclusive_count} vs {android_exclusive_count})")
            return PlatformType.IOS
        else:
            logger.warning(f"[性能数据处理] 无法确定平台类型 (Android独有字段: {android_exclusive_count}, iOS独有字段: {ios_exclusive_count})")
            return None

    def _get_all_metrics(self, perf_data: List[dict], platform_type: PlatformType) -> List[str]:
        """获取所有相关的DS性能指标（排除时间字段）"""
        # 根据平台类型获取相关字段（独有字段 + 通用字段）
        if platform_type == PlatformType.ANDROID:
            platform_fields = self.ANDROID_EXCLUSIVE_FIELDS | self.COMMON_FIELDS
        else:
            platform_fields = self.IOS_EXCLUSIVE_FIELDS | self.COMMON_FIELDS

        # 收集实际存在的字段，排除时间字段、元数据字段和过滤字段
        all_metrics = set()
        excluded_fields = self.TIME_FIELDS | self.METADATA_FIELDS | self.FILTERED_FIELDS

        for item in perf_data:
            if isinstance(item, dict):
                # 收集系统级指标
                for field in item.keys():
                    if field in platform_fields and field not in excluded_fields:
                        all_metrics.add(field)

                # 收集进程指标
                if "processData" in item and isinstance(item["processData"], list):
                    for process in item["processData"]:
                        if isinstance(process, dict):
                            for field in process.keys():
                                if field in platform_fields and field not in excluded_fields:
                                    all_metrics.add(field)

        logger.debug(f"[性能数据处理] 收集到的性能指标字段（已排除时间、元数据和过滤字段）: {sorted(all_metrics)}")
        return list(all_metrics)

    def _collect_metrics_data(self, perf_data: List[dict], metrics: DsPerfMetrics, all_metrics: List[str]):
        """收集性能指标数据"""
        for item in perf_data:
            if not isinstance(item, dict):
                continue

            # 收集系统级指标和trace指标
            self._collect_system_metrics(item, metrics, all_metrics)

            # 收集进程指标
            if "processData" in item:
                self._collect_process_metrics(item["processData"], metrics, all_metrics)

    def _collect_system_metrics(self, item: dict, metrics: DsPerfMetrics, all_metrics: List[str]):
        """收集系统级指标"""
        for field_name, value in item.items():
            if field_name not in all_metrics:
                continue

            if field_name in self.LIST_FIELDS:
                # 处理列表字段
                self._collect_list_field(field_name, value, metrics)
            elif self._is_valid_number(value):
                # 处理普通数值字段
                metrics.metrics_values[field_name].append(float(value))

    def _collect_list_field(self, field_name: str, value: Any, metrics: DsPerfMetrics):
        """收集列表类型字段的数据"""
        if not isinstance(value, list):
            return

        if field_name == "frameTime":
            # frameTime是复杂对象列表，提取time字段
            time_values = []
            for frame in value:
                if isinstance(frame, dict) and "time" in frame:
                    time_val = frame["time"]
                    if self._is_valid_number(time_val):
                        time_values.append(float(time_val))
            if time_values:
                metrics.list_metrics_values[field_name].append(time_values)
        else:
            # 其他列表字段（如cpuCoreUsage, cpuFreq等）
            numeric_values = []
            for item in value:
                if self._is_valid_number(item):
                    numeric_values.append(float(item))
            if numeric_values:
                metrics.list_metrics_values[field_name].append(numeric_values)

    def _collect_process_metrics(self, process_data: List[dict], metrics: DsPerfMetrics, all_metrics: List[str]):
        """收集进程指标

        收集所有进程的指标数据，不再基于进程名过滤，
        而是收集所有包含相关指标的进程数据。
        """
        for process in process_data:
            if isinstance(process, dict):
                for field_name, value in process.items():
                    if field_name in all_metrics and self._is_valid_number(value):
                        metrics.metrics_values[field_name].append(float(value))

class DsPerfDataProcessor:
    """DS性能数据处理工具类

    专门处理DS工具采集的性能数据，支持：
    - Android和iOS平台的DS数据处理
    - DS原始数据的加载、验证和修复
    - 性能指标的计算和平均值处理
    - 数据文件的保存和管理
    - 与ds_perf_monitor.py的集成
    """

    # 文件命名规范
    FILE_NAMES = {
        "ds_raw": "ds_raw_perf_data.json",
        "ds_processed": "ds_processed_perf_data.json",
        "ds_avg": "ds_avg_perf_data.json"
    }

    def __init__(self):
        self.metrics_calculator = DsPerfMetricsCalculator()

    def load_ds_raw_data(self, data_path: str) -> Optional[List[dict]]:
        """加载DS原始性能数据

        Args:
            data_path: DS数据文件路径或包含DS数据的目录路径

        Returns:
            Optional[List[dict]]: DS原始数据列表，加载失败时返回None
        """
        try:
            file_path = self._resolve_data_file_path(data_path, "ds_raw_perf_data.json")
            if not file_path:
                logger.error(f"[性能数据处理] 未找到DS原始数据文件: {data_path}")
                return None

            raw_data = self._load_json_file(file_path)
            if not isinstance(raw_data, list):
                logger.error(f"[性能数据处理] DS原始数据格式错误，期望列表类型: {type(raw_data)}")
                return None

            if not raw_data:
                logger.warning("[性能数据处理] DS原始数据为空")
                return raw_data

            logger.info(f"[性能数据处理] DS原始数据加载成功，数据条数: {len(raw_data)}")
            return raw_data

        except Exception as e:
            logger.error(f"[性能数据处理] DS原始数据加载失败: {e}")
            return None



    def _resolve_data_file_path(self, data_path: str, filename: str) -> Optional[str]:
        """解析数据文件路径"""
        if os.path.isdir(data_path):
            file_path = os.path.join(data_path, filename)
        else:
            file_path = data_path

        if not os.path.exists(file_path):
            logger.error(f"[性能数据处理] 数据文件不存在: {file_path}")
            return None

        return file_path

    def _load_json_file(self, file_path: str) -> Any:
        """加载JSON文件"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            logger.error(f"[性能数据处理] JSON解析失败: {e}")
            raise
        except Exception as e:
            logger.error(f"[性能数据处理] 文件读取失败: {e}")
            raise

    def _save_json_file(self, data: Any, file_path: str, description: str = "数据"):
        """保存JSON文件"""
        try:
            safe_path = os.path.realpath(os.path.dirname(file_path))
            os.makedirs(safe_path, exist_ok=True)

            logger.info(f"[性能数据处理] 开始保存{description} 路径:{file_path}")
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            logger.info(f"[性能数据处理] {description}保存成功")
        except Exception as e:
            logger.error(f"[性能数据处理] {description}保存失败: {e}")
            raise

    def _ensure_safe_path(self, path: Optional[str]) -> str:
        """确保路径安全并存在"""
        if not path:
            path = os.getcwd()

        safe_path = os.path.realpath(path)
        os.makedirs(safe_path, exist_ok=True)
        return safe_path

    def process_ds_raw_data(
        self,
        ds_data_path: str,
        save_local_path: Optional[str] = None
    ) -> tuple[List[dict], Mapping[str, Optional[float]]]:
        """处理DS原始性能数据

        Args:
            ds_data_path: DS原始数据文件路径或目录路径
            save_local_path: 本地保存路径，可选

        Returns:
            tuple: (性能数据列表, 平均值数据)
        """
        try:
            # 加载DS原始数据
            ds_raw_data = self.load_ds_raw_data(ds_data_path)
            if not ds_raw_data:
                logger.error("[性能数据处理] 无法加载DS原始数据")
                return [], {}

            # 使用现有的处理流程
            return self.handle_perf_data(
                basic_perf_data=ds_raw_data,
                save_local_path=save_local_path
            )

        except Exception as e:
            logger.error(f"[性能数据处理] 处理DS原始数据失败: {e}")
            return [], {}

    def process_ds_perf_data(
        self,
        data_source: Union[str, List[dict]],
        save_local_path: Optional[str] = None
    ) -> tuple[List[dict], Mapping[str, Optional[float]]]:
        """DS性能数据处理接口

        Args:
            data_source: DS数据源，可以是：
                - 文件路径（DS原始数据文件）
                - 目录路径（包含DS原始数据的目录）
                - DS性能数据列表
            save_local_path: 本地保存路径，可选

        Returns:
            tuple: (性能数据列表, 平均值数据)
        """
        try:
            logger.info("[性能数据处理] 开始DS性能数据处理")

            if isinstance(data_source, str):
                # 字符串类型，作为DS数据路径处理
                return self.process_ds_raw_data(
                    ds_data_path=data_source,
                    save_local_path=save_local_path
                )
            elif isinstance(data_source, list):
                # 列表类型，直接作为DS数据处理
                return self.handle_perf_data(
                    basic_perf_data=data_source,
                    save_local_path=save_local_path
                )
            else:
                logger.error("[性能数据处理] 不支持的DS数据源类型")
                return [], {}

        except Exception as e:
            logger.error(f"[性能数据处理] DS性能数据处理失败: {e}")
            return [], {}

    def get_time_range(self, perf_data: List[dict]) -> Tuple[int, int]:
        """从性能数据中获取时间范围

        Args:
            perf_data: 性能数据列表

        Returns:
            Tuple[int, int]: (开始时间, 结束时间)

        Raises:
            IndexError: 如果性能数据为空
        """
        if not perf_data:
            raise IndexError("性能数据为空")

        start_time = int(perf_data[0]["pcTime"])
        end_time = int(perf_data[-1]["pcTime"])

        logger.info(f"[性能数据处理] 获取时间范围 开始:{start_time} 结束:{end_time}")
        return start_time, end_time

    def handle_ds_perf_data_with_context(
        self,
        basic_perf_data: List[dict],
        cpu_profile_path: Optional[str],
        perf_account: dict,
        perf_device: Any,
        perf_app: Any,
        vqos_trace_configs: Optional[list] = None,
        byteio_configs: Optional[list] = None
    ) -> Tuple[Optional[List[dict]], Optional[List[dict]], Optional[str], Optional[List[dict]]]:
        """处理DS性能数据（带上下文信息）

        这个方法从 common/tiktok/base.py 移动而来，用于处理DS性能数据的完整流程，
        包括trace数据和byteio数据的处理。

        Args:
            basic_perf_data: DS基础性能数据
            cpu_profile_path: CPU Profile文件路径
            perf_account: 性能测试账号信息
            perf_device: 性能测试设备信息
            perf_app: 性能测试应用信息
            vqos_trace_configs: Trace配置信息
            byteio_configs: ByteIO字段列表

        Returns:
            Tuple[基础性能数据, trace数据, CPU Profile路径, ByteIO数据]
        """
        trace_data = None
        byteio_data = None

        # 直接调用ds_perf_data_processor方法获取时间范围
        start_time, end_time = self.get_time_range(basic_perf_data)

        # 构建保存路径
        if hasattr(perf_app, 'package_name'):  # Android应用
            package_name = perf_app.package_name
        else:  # iOS应用
            package_name = perf_app.bundle_id
        save_path = ds_perf_monitor.build_ds_save_path(perf_device.udid, package_name)

        # 处理trace数据并立即保存原始数据到与DS相同的目录
        if vqos_trace_configs:
            trace_data = trace_data_manager.get_trace_metrics_data_with_raw(
                start_time=start_time,
                end_time=end_time,
                user_id=perf_account.get("account_uid"),
                device_id=perf_app.get_did(),
                vqos_trace_configs=vqos_trace_configs,
                save_raw_data_path=save_path  # 保存到与DS相同的目录：data/{device_id}/{package_name}/
            )

        # 处理ByteIO数据并立即保存原始数据到与DS相同的目录
        if byteio_configs:
            byteio_data = byteio_data_manager.get_byteio_metrics_data(
                device_id=perf_app.get_did(),
                start_time=start_time,
                end_time=end_time,
                byteio_configs=byteio_configs,
                save_raw_data_path=save_path  # 保存到与DS相同的目录：data/{device_id}/{package_name}/
            )

        # 保存DS原始数据（补充缺失的原始数据保存）
        self.save_ds_raw_data(basic_perf_data, save_path)

        # 直接调用ds_perf_data_processor处理最终结果
        perf_data, perf_avg_data = self.handle_perf_data(
            basic_perf_data=basic_perf_data,
            trace_data=trace_data,
            cpu_profile_path=cpu_profile_path,
            byteio_data=byteio_data,
            save_local_path=save_path
        )

        # 返回4个值以保持接口一致性
        return perf_data, perf_avg_data, cpu_profile_path, byteio_data

    def handle_perf_data(
        self,
        basic_perf_data: List[dict],
        trace_data: Optional[List[dict]] = None,
        cpu_profile_path: Optional[str] = None,
        byteio_data: Optional[List[dict]] = None,
        save_local_path: Optional[str] = None
    ) -> tuple[List[dict], Mapping[str, Optional[float]]]:
        """处理DS性能数据并计算平均值

        Args:
            basic_perf_data: DS基础性能数据
            trace_data: Trace性能数据，可选
            cpu_profile_path: CPU Profile文件路径，可选
            byteio_data: ByteIO数据，可选
            save_local_path: 本地保存路径，可选

        Returns:
            tuple: (性能数据列表, 平均值数据)
        """
        try:
            logger.info("[性能数据处理] 开始处理DS性能数据")

            # 修复数据
            fixed_data = self._fix_perf_data(basic_perf_data)

            # 根据要求，不进行数据合并，直接使用处理后的DS数据
            enhanced_data = fixed_data

            # 确保保存路径
            safe_path = self._ensure_safe_path(save_local_path)

            # 获取测试用例主目录
            main_dir = os.getcwd()

            # 处理CPU Profile文件 - 保存到主目录
            if cpu_profile_path:
                profile_collector.handle_cpu_profile(cpu_profile_path, main_dir)

            # 保存处理后的额外数据到与DS相同的目录（原始数据已在获取时保存）
            if trace_data:
                # trace 处理后数据和平均值保存到与DS相同的目录：data/{device_id}/{package_name}/
                trace_data_manager.save_trace_processed_and_avg_data(trace_data, safe_path)
            if byteio_data:
                # byteio 处理后数据和平均值保存到与DS相同的目录：data/{device_id}/{package_name}/
                byteio_data_manager.save_byteio_processed_and_avg_data(byteio_data, safe_path)

            # 计算平均值（基于增强后的数据）
            avg_data = self._calculate_enhanced_avg_data(enhanced_data)

            # 如果提供了保存路径，保存处理后的数据
            if safe_path:
                try:
                    # 确保目录存在
                    os.makedirs(safe_path, exist_ok=True)

                    # 保存处理后数据
                    processed_file = os.path.join(safe_path, self.FILE_NAMES["ds_processed"])
                    self._save_json_file(enhanced_data, processed_file, "处理后性能数据")

                    # 保存平均值数据
                    avg_file = os.path.join(safe_path, self.FILE_NAMES["ds_avg"])
                    self._save_json_file(avg_data, avg_file, "性能数据平均值")
                    logger.info(f"[性能数据处理] DS数据已保存到: {safe_path}")
                except Exception as e:
                    logger.warning(f"[性能数据处理] 保存DS数据失败: {str(e)}")

            logger.info(f"[性能数据处理] DS性能数据处理完成，数据条数: {len(enhanced_data)}, 平均值指标数量: {len(avg_data)}")
            return enhanced_data, avg_data

        except Exception as e:
            logger.error(f"[性能数据处理] 处理DS性能数据失败: {str(e)}")
            return [], {}

    def _fix_perf_data(self, data: List[dict]) -> List[dict]:
        """修复性能数据，统一时间字段格式"""
        if not data:
            return data

        # 检查是否需要修复时间字段格式
        needs_fix = False
        for i, item in enumerate(data):
            # 检查time字段是否为从0开始的秒级递增，以及是否缺少timestamp字段
            if ("time" in item and item["time"] != i) or "timestamp" not in item:
                needs_fix = True
                break

        if needs_fix:
            logger.info("[性能数据处理] 检测到时间字段需要统一格式，开始修复")
            return self._fix_time_field(data)
        else:
            logger.info("[性能数据处理] 时间字段格式已统一，无需修复")
            return data

    def _fix_time_field(self, perf_data: List[dict]) -> List[dict]:
        """修复time字段的值，使用全局时间对齐"""
        if not perf_data:
            return perf_data

        # 按pcTime排序
        sorted_data = sorted(perf_data, key=lambda x: x["pcTime"])

        logger.info("[性能数据处理] DS数据时间字段对齐完成")
        return sorted_data

    def _save_processed_data(self, data: List[dict], save_path: str) -> List[dict]:
        """保存处理后的性能数据"""
        if save_path:
            file_path = os.path.join(save_path, self.FILE_NAMES["ds_processed"])
            self._save_json_file(data, file_path, "处理后性能数据")
        return data

    def _save_avg_data(self, data: List[dict], save_path: str) -> Mapping[str, Optional[float]]:
        """计算并保存平均值数据"""
        avg_data = self.metrics_calculator.calculate_avg_perf_data(data)

        if save_path:
            file_path = os.path.join(save_path, self.FILE_NAMES["ds_avg"])
            self._save_json_file(avg_data, file_path, "性能数据平均值")

        return avg_data







    def _calculate_enhanced_avg_data(
        self,
        enhanced_data: List[dict]
    ) -> Mapping[str, Optional[float]]:
        """计算DS性能数据的平均值

        Args:
            enhanced_data: DS性能数据

        Returns:
            Mapping[str, Optional[float]]: 平均值数据
        """
        try:
            if not enhanced_data:
                logger.warning("[性能数据处理] 增强数据为空")
                return {}

            # 直接使用增强数据计算平均值
            result = self.metrics_calculator.calculate_avg_perf_data(enhanced_data)

            logger.info(f"[性能数据处理] 增强平均值计算完成，指标数量: {len(result)}")
            return result

        except Exception as e:
            logger.error(f"[性能数据处理] 计算增强平均值失败: {str(e)}")
            return {}

    def save_ds_raw_data(self, perf_data: List[dict], save_path: str) -> bool:
        """保存DS原始性能数据

        Args:
            perf_data: DS原始性能数据
            save_path: 保存路径

        Returns:
            bool: 保存是否成功
        """
        try:
            if not perf_data or not save_path:
                logger.warning("[性能数据处理] DS原始数据或保存路径为空，跳过保存")
                return False

            # 使用类中定义的文件名
            raw_file_path = os.path.join(save_path, self.FILE_NAMES["ds_raw"])

            # 使用统一的保存方法
            self._save_json_file(perf_data, raw_file_path, "DS原始性能数据")

            return True

        except Exception as e:
            logger.error(f"[性能数据处理] 保存DS原始数据失败: {e}")
            return False

ds_perf_data_processor = DsPerfDataProcessor()
