#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import shutil
import time
import subprocess
import threading
import queue
from typing import Dict, List, Optional, Callable, Any
from concurrent.futures import ThreadPoolExecutor

from utils.common.log_utils import logger
from utils.apis.gameperf_api import PerfServiceAPI
from defines import PerfCollectMode, PlatformType


class GamePerfManager:

    def __init__(self, **kwargs):
        """
        初始化GamePerf管理器

        Args:
            kwargs: 全局配置参数：
                - gameperf_path: GamePerf应用路径
                - base_http_port: HTTP基础端口（默认：3000）
                - base_service_port: 服务基础端口（默认：56340）
                - startup_timeout: 启动超时时间（默认：90秒）
                - max_workers: 最大工作线程数（默认：10）
                - on_time_callback: 时间回调函数（可选）
        """
        # 设置全局配置
        self.global_config = {
            "gameperf_path": "/Applications/GamePerf.app/Contents/MacOS/GamePerf",
            "base_http_port": 3000,
            "base_service_port": 56340,
            "startup_timeout": 90,
            "max_workers": 10,
            "on_time_callback": None,
            **kwargs  # 用户配置覆盖默认配置
        }

        # 内部状态
        self.instances = {}  # 实例字典：{instance_id: instance_info}
        self.target_combinations = []  # 目标组合列表
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=self.global_config["max_workers"])

        logger.info("GamePerf管理器初始化完成")

    def _generate_time_actions(self, config: Dict[str, Any], config_index: int):
        """
        基于简化配置自动生成time_actions

        Args:
            config: 配置项
            config_index: 配置项索引（用于错误提示）
        """
        logger.info(f"配置项 {config_index}: 自动生成time_actions配置")

        duration = config["duration"]
        scene_name = config.get("scene_name", f"{config['app_name']}性能测试")
        subscenes = config.get("subscenes", [])
        file_export = config.get("file_export", 1)
        file_upload = config.get("file_upload", 0)

        # 验证子场景配置
        self._validate_subscenes(subscenes, duration, config_index)

        # 自动生成标准time_actions
        config["time_actions"] = self._generate_standard_time_actions(
            duration=duration,
            scene_name=scene_name,
            subscenes=subscenes,
            file_export=file_export,
            file_upload=file_upload
        )

    def _validate_subscenes(self, subscenes: List[Dict[str, Any]], duration: int, config_index: int):
        """
        验证子场景配置

        Args:
            subscenes: 子场景列表
            duration: 采集持续时间
            config_index: 配置项索引
        """
        for i, subscene in enumerate(subscenes):
            if not isinstance(subscene, dict):
                raise ValueError(f"配置项 {config_index} 的subscenes[{i}]必须是字典格式")

            required_fields = ["name", "start", "end"]
            for field in required_fields:
                if field not in subscene:
                    raise ValueError(f"配置项 {config_index} 的subscenes[{i}]缺少必需字段: {field}")

            start_time = subscene["start"]
            end_time = subscene["end"]
            subscene_name = subscene["name"]

            # 验证时间类型
            if not isinstance(start_time, int) or not isinstance(end_time, int):
                raise ValueError(f"配置项 {config_index} 的subscenes[{i}]的start和end必须是整数")

            # 验证时间范围
            if start_time >= end_time:
                raise ValueError(f"配置项 {config_index} 的subscenes[{i}]的start时间({start_time})必须小于end时间({end_time})")

            if start_time < 1 or end_time > duration:
                raise ValueError(f"配置项 {config_index} 的subscenes[{i}]的时间范围({start_time}-{end_time})必须在1到{duration}秒之间")

            # 验证场景名称
            if not isinstance(subscene_name, str) or not subscene_name.strip():
                raise ValueError(f"配置项 {config_index} 的subscenes[{i}]的name必须是非空字符串")

    def _generate_standard_time_actions(self, duration: int, scene_name: str, subscenes: List[Dict[str, Any]], file_export: int, file_upload: int) -> Dict[int, List[Dict[str, Any]]]:
        """
        生成标准的time_actions配置

        Args:
            duration: 采集持续时间
            scene_name: 场景名称
            subscenes: 子场景列表
            file_export: 是否导出文件
            file_upload: 是否上传文件

        Returns:
            Dict[int, List[Dict[str, Any]]]: 标准time_actions配置
        """
        # 确保stop_record在自动停止之前执行
        # 计算stop_record的执行时间，确保不与start_record冲突
        if duration == 1:
            # 对于1秒的极短测试，将stop_collect延迟到2秒，stop_record在1秒执行
            stop_record_time = 1
            stop_collect_time = 2
        elif duration == 2:
            # 对于2秒的短测试，在start_record的同一时间点添加stop_record
            stop_record_time = 1
            stop_collect_time = duration
        else:
            # 对于正常测试，在duration-2秒执行stop_record
            stop_record_time = duration - 2
            stop_collect_time = duration

        time_actions = {
            0: [{"action": "set_scene", "scene_name": scene_name}],
            1: [{"action": "start_record"}],
            stop_collect_time: [{"action": "stop_collect"}]
        }

        # 添加stop_record操作
        if stop_record_time in time_actions:
            # 如果时间点已存在，添加到现有操作列表
            time_actions[stop_record_time].append({"action": "stop_record", "file_export": file_export, "file_upload": file_upload})
        else:
            # 创建新的时间点
            time_actions[stop_record_time] = [{"action": "stop_record", "file_export": file_export, "file_upload": file_upload}]

        # 添加子场景
        for subscene in subscenes:
            start_time = subscene.get("start")
            end_time = subscene.get("end")
            subscene_name = subscene.get("name")

            if not all([start_time is not None, end_time is not None, subscene_name]):
                logger.warning(f"子场景配置不完整，跳过: {subscene}")
                continue

            if start_time >= end_time or start_time < 1 or end_time > duration:
                logger.warning(f"子场景时间配置无效，跳过: {subscene}")
                continue

            # 添加子场景开始和结束操作
            if start_time not in time_actions:
                time_actions[start_time] = []
            time_actions[start_time].append({"action": "start_subscene", "subscene_name": subscene_name})

            if end_time not in time_actions:
                time_actions[end_time] = []
            time_actions[end_time].append({"action": "stop_subscene"})

        return time_actions

    def _log(self, instance_id: str, level: str, message: str):
        """带实例标识的日志输出"""
        formatted_message = f"[{instance_id}] {message}"
        getattr(logger, level)(formatted_message)
    
    def prepare_targets(self, configs: List[Dict[str, Any]]):
        """准备目标组合

        Args:
            configs: 配置列表，每个配置项包含以下字段：
                - device_id: 设备ID（必需）
                - app_name: 应用显示名称（必需）
                - app_package: 应用包名（可选，用于普通应用，系统进程可不提供）
                - process_name: 进程名称（可选，None表示使用智能选择策略）
                - duration: 采集持续时间（必需）
                - scene_name: 场景名称（可选，默认使用app_name + "性能测试"）
                - subscenes: 子场景列表（可选），格式：[{"name": "场景名", "start": 开始秒数, "end": 结束秒数}]
                - file_export: 是否导出文件（可选，默认1）
                - file_upload: 是否上传文件（可选，默认0）
                - connection_type: 设备连接方式（可选，默认：PerfCollectMode.WIRE_COLLECT）
                  必须使用枚举值：PerfCollectMode.WIRE_COLLECT（有线连接）、PerfCollectMode.WIRELESS_COLLECT（无线连接）
                - device_type: 设备类型（可选，默认：PlatformType.ANDROID）
                  必须使用枚举值：PlatformType.ANDROID、PlatformType.IOS
                - device_ip: 设备IP地址（无线连接时必需）

            注意：time_actions将根据duration和场景配置自动生成，遵循标准时序：
                - set_scene: 0秒
                - start_record: 1秒
                - stop_record: duration-2秒（最小为1秒）
                - stop_collect: duration秒
        """
        # 验证配置列表
        if not configs or not isinstance(configs, list):
            raise ValueError("configs必须是非空的配置列表")

        # 处理和验证每个配置项
        for i, config in enumerate(configs):
            # 验证基本必需字段
            required_fields = ["device_id", "app_name", "duration"]
            for field in required_fields:
                if field not in config:
                    raise ValueError(f"配置项 {i} 缺少必需字段: {field}")

            # 验证duration是否为正整数
            duration = config.get("duration")
            if not isinstance(duration, int) or duration <= 0:
                raise ValueError(f"配置项 {i} 的duration必须是正整数，当前值: {duration}")

            # 自动生成time_actions配置
            self._generate_time_actions(config, i)

            # 验证和标准化连接类型字段
            raw_connection_type = config.get("connection_type", PerfCollectMode.WIRE_COLLECT)
            if isinstance(raw_connection_type, PerfCollectMode):
                connection_type = raw_connection_type
            elif isinstance(raw_connection_type, int):
                try:
                    connection_type = PerfCollectMode(raw_connection_type)
                except ValueError:
                    raise ValueError(f"配置项 {i} 的连接类型配置错误: 无效的连接类型数值: {raw_connection_type}，必须是有效的 PerfCollectMode 枚举值\n"
                                   f"请使用 PerfCollectMode.WIRE_COLLECT 或 PerfCollectMode.WIRELESS_COLLECT")
            else:
                raise ValueError(f"配置项 {i} 的连接类型配置错误: 无效的连接类型: {raw_connection_type}，必须是 PerfCollectMode 枚举值\n"
                               f"请使用 PerfCollectMode.WIRE_COLLECT 或 PerfCollectMode.WIRELESS_COLLECT")
            config["connection_type"] = connection_type

            # 验证和标准化设备类型字段
            raw_device_type = config.get("device_type", PlatformType.ANDROID)
            if isinstance(raw_device_type, PlatformType):
                device_type = raw_device_type
            elif isinstance(raw_device_type, int):
                try:
                    device_type = PlatformType(raw_device_type)
                except ValueError:
                    raise ValueError(f"配置项 {i} 的设备类型配置错误: 无效的设备类型数值: {raw_device_type}，必须是有效的 PlatformType 枚举值\n"
                                   f"请使用 PlatformType.ANDROID 或 PlatformType.IOS")
            else:
                raise ValueError(f"配置项 {i} 的设备类型配置错误: 无效的设备类型: {raw_device_type}，必须是 PlatformType 枚举值\n"
                               f"请使用 PlatformType.ANDROID 或 PlatformType.IOS")
            config["device_type"] = device_type

            # 对于无线连接，验证必需的网络参数
            if connection_type == PerfCollectMode.WIRELESS_COLLECT:
                if not config.get("device_ip"):
                    raise ValueError(f"配置项 {i} 使用无线连接时必须提供 device_ip")

        self.target_combinations = []

        for config in configs:
            app_package = config.get("app_package")
            target = {
                "device_id": config["device_id"],
                "app_name": config["app_name"],
                "app_package": app_package,
                "process_name": config.get("process_name"),
                "time_actions": config["time_actions"],
                "duration": config.get("duration", 30),
                "unique_key": f"{config['device_id']}_{app_package or config['app_name']}"
            }
            self.target_combinations.append(target)

        logger.info(f"准备了 {len(self.target_combinations)} 个目标组合")
        return self.target_combinations

    def start_instances(self) -> bool:
        """启动GamePerf实例"""
        if not self.target_combinations:
            raise ValueError("必须先调用 prepare_targets 方法")

        instance_count = len(self.target_combinations)
        logger.info(f"启动 {instance_count} 个GamePerf实例")
        
        # 启动GamePerf进程
        if not self._start_gameperf_process(instance_count):
            return False
        
        # 初始化API客户端
        if not self._init_api_clients():
            return False

        logger.info("所有实例启动成功")

        return True

    def _start_gameperf_process(self, instance_count: int) -> bool:
        """启动GamePerf进程 - 严格要求收集到足够的实际端口"""
        gameperf_path = self.global_config["gameperf_path"]

        # 验证gameperf_path是否存在
        if not os.path.exists(gameperf_path):
            logger.error(f"GamePerf路径不存在: {gameperf_path}")
            logger.error("请前往 https://gtl.bytedance.com/intro/tool?type=gamePerf 安装GamePerf工具")
            return False

        cmd = [gameperf_path, "-service", "-n", str(instance_count)]
        logger.info(f"启动GamePerf进程: {' '.join(cmd)}")

        try:
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                text=True, bufsize=1, universal_newlines=True
            )
            logger.info(f"GamePerf进程已启动，PID: {process.pid}")

            # 收集端口信息
            http_ports, service_ports = self._collect_ports(process, instance_count)

            # 验证端口收集结果
            if len(http_ports) < instance_count:
                logger.error(f"HTTP端口收集失败: 需要{instance_count}个，只收集到{len(http_ports)}个")
                return False

            if len(service_ports) == 0:
                logger.error("服务端口收集失败: 未从GamePerf进程输出中解析到服务端口")
                return False

            # 创建实例（只有在端口收集成功时才会执行）
            self._create_instances(http_ports, service_ports, instance_count)

            logger.info(f"GamePerf进程启动完成，创建了 {len(self.instances)} 个实例")
            return True

        except Exception as e:
            logger.error(f"启动GamePerf进程失败: {e}")
            return False

    def _collect_ports(self, process: subprocess.Popen, instance_count: int) -> tuple:
        """收集端口信息 - 严格要求收集到足够的实际端口"""
        http_ports = []
        service_ports = []
        start_time = time.time()

        logger.info(f"开始收集端口信息，需要 {instance_count} 个HTTP端口")
        logger.info(f"最大等待时间: {self.global_config['startup_timeout']}秒")

        # 启动输出读取线程
        self._start_output_reader(process)

        # 主循环：收集端口直到超时或收集完成
        while time.time() - start_time < self.global_config["startup_timeout"]:
            # 检查进程状态
            if process.poll() is not None:
                logger.error(f"GamePerf进程已退出，退出码: {process.poll()}")
                break

            # 读取输出并解析端口
            line = self._read_process_output()
            if line:
                # 解析并收集端口
                self._parse_and_collect_ports(line, http_ports, service_ports, instance_count)

                # 检查是否收集完成
                if len(http_ports) >= instance_count and len(service_ports) >= 1:
                    logger.info(f"端口收集完成: HTTP端口={http_ports}, 服务端口={service_ports}")
                    return http_ports, service_ports

            time.sleep(0.1)

        # 收集失败
        elapsed_time = time.time() - start_time
        logger.error(f"端口收集失败，耗时: {elapsed_time:.2f}秒")
        logger.error(f"HTTP端口收集结果: 需要{instance_count}个，实际收集到{len(http_ports)}个")
        logger.error(f"服务端口收集结果: 需要1个，实际收集到{len(service_ports)}个")
        return http_ports, service_ports

    def _start_output_reader(self, process: subprocess.Popen):
        """启动输出读取线程"""
        if hasattr(self, '_output_queue'):
            return  # 已经启动

        def read_output(proc, q):
            try:
                while proc.poll() is None:
                    line = proc.stdout.readline()
                    if line:
                        q.put(line.strip())
                    else:
                        time.sleep(0.01)
            except Exception as e:
                q.put(f"ERROR: {e}")

        self._output_queue = queue.Queue()
        self._reader_thread = threading.Thread(
            target=read_output,
            args=(process, self._output_queue),
            daemon=True
        )
        self._reader_thread.start()

    def _read_process_output(self) -> str:
        """从队列中读取进程输出"""
        try:
            line = self._output_queue.get_nowait()
            if line.startswith("ERROR:"):
                logger.warning(f"读取输出时发生错误: {line}")
                return ""
            return line
        except queue.Empty:
            return ""

    def _parse_and_collect_ports(self, line: str, http_ports: List[int], service_ports: List[int], instance_count: int):
        """解析并收集端口"""
        # 解析HTTP端口
        http_match = re.search(r'Server running at http://0\.0\.0\.0:(\d+)/', line)
        if http_match:
            port = int(http_match.group(1))
            if port not in http_ports:
                http_ports.append(port)
                logger.info(f"发现HTTP端口: {port} (总计: {len(http_ports)}/{instance_count})")

        # 解析服务端口
        service_match = re.search(r'GamePerfService runs on port (\d+)', line)
        if service_match:
            port = int(service_match.group(1))
            if port not in service_ports:
                service_ports.append(port)
                logger.info(f"发现服务端口: {port}")

    def _create_instances(self, http_ports: List[int], service_ports: List[int], instance_count: int):
        """创建实例对象 - 严禁使用默认计算端口，只使用实际解析的端口"""
        # 严格检查端口收集结果
        if len(http_ports) < instance_count:
            logger.error(f"HTTP端口收集不足: 需要{instance_count}个，实际收集到{len(http_ports)}个")
            raise RuntimeError(f"端口收集失败：需要{instance_count}个HTTP端口，只收集到{len(http_ports)}个")

        if len(service_ports) == 0:
            logger.error("未收集到任何服务端口")
            raise RuntimeError("服务端口收集失败：未从GamePerf进程输出中解析到服务端口")

        # 使用实际解析的端口
        shared_service_port = service_ports[0]
        logger.info(f"使用实际解析的服务端口: {shared_service_port}")

        # 只创建有实际端口的实例
        for i in range(len(http_ports)):
            http_port = http_ports[i]
            instance_id = f"gameperf_{http_port}_{shared_service_port}"

            instance = {
                "instance_id": instance_id,
                "http_port": http_port,
                "service_port": shared_service_port,
                "status": "starting",
                "api_client": None,
                "assigned_target": None
            }

            self.instances[instance_id] = instance

        logger.info(f"成功创建 {len(self.instances)} 个实例")

    def _init_api_clients(self) -> bool:
        """初始化API客户端"""
        success_count = 0

        for instance in self.instances.values():
            try:
                api_client = PerfServiceAPI(host="127.0.0.1", port=instance["http_port"])
                instance["api_client"] = api_client
                instance["status"] = "running"
                success_count += 1
                self._log(instance["instance_id"], "info", "API客户端初始化成功")
            except Exception as e:
                self._log(instance["instance_id"], "error", f"API客户端初始化失败: {e}")
                instance["status"] = "error"

        return success_count == len(self.instances)

    def connect_devices(self, configs: List[Dict[str, Any]]) -> bool:
        """连接设备（基于实例分配的目标连接对应设备）

        Args:
            configs: 配置列表，包含设备连接信息
        """
        if not self.target_combinations:
            raise ValueError("必须先调用 prepare_targets 方法")

        available_instances = [inst for inst in self.instances.values()
                             if inst["status"] == "running" and inst["api_client"]]

        if not available_instances:
            logger.error("没有可用的实例进行设备连接")
            return False

        # 构建设备配置映射
        device_configs = {}
        for config in configs:
            device_id = config["device_id"]
            device_configs[device_id] = {
                "device_id": device_id,
                "connection_type": config["connection_type"],
                "device_type": config["device_type"],
                "device_ip": config.get("device_ip")
            }

        # 为每个实例分配目标并连接对应设备
        total_connections = 0
        successful_connections = 0
        instance_device_map = {}

        logger.info("开始基于任务分配连接设备")

        for i, target in enumerate(self.target_combinations):
            instance = available_instances[i % len(available_instances)]
            device_id = target["device_id"]

            # 记录实例与设备的映射关系
            instance_id = instance["instance_id"]
            if instance_id not in instance_device_map:
                instance_device_map[instance_id] = set()

            # 如果该实例还没有连接过这个设备，则连接
            if device_id not in instance_device_map[instance_id]:
                device_config = device_configs[device_id]
                total_connections += 1

                self._log(instance_id, "info", f"分配设备连接: {device_id}")
                if self._connect_single_device(instance, device_config):
                    successful_connections += 1
                    instance_device_map[instance_id].add(device_id)
                    self._log(instance_id, "info", f"设备 {device_id} 连接成功")
                else:
                    self._log(instance_id, "error", f"设备 {device_id} 连接失败")

                # 增加等待时间以减少设备连接竞争
                time.sleep(1)
            else:
                self._log(instance_id, "info", f"设备 {device_id} 已连接，跳过重复连接")

        # 记录最终的设备分配情况
        logger.info("设备分配情况:")
        for instance_id, devices in instance_device_map.items():
            logger.info(f"  实例 {instance_id}: 设备 {list(devices)}")

        # 评估连接成功率
        success_rate = successful_connections / total_connections if total_connections > 0 else 0
        success = success_rate >= 0.8

        status = "成功" if success else "失败"
        logger.info(f"设备连接整体{status} (成功率: {success_rate:.1%}, {successful_connections}/{total_connections})")
        return success

    def _connect_single_device(self, instance: Dict[str, Any], device_config: Dict[str, Any]) -> bool:
        """连接单个设备到指定实例"""
        try:
            device_id = device_config["device_id"]
            connection_type = device_config["connection_type"]
            device_type = device_config["device_type"]

            # 使用枚举值进行判断
            if connection_type == PerfCollectMode.WIRELESS_COLLECT:
                device_ip = device_config["device_ip"]
                self._log(instance["instance_id"], "info",
                         f"使用无线连接设备 {device_id} (IP: {device_ip}, 类型: {PlatformType.get_name(device_type)})")
                # 使用正确的无线连接方法
                instance["api_client"].connect_wireless_device(device_id, device_ip)
            else:  # PerfCollectMode.WIRE_COLLECT
                self._log(instance["instance_id"], "info",
                         f"使用有线连接设备 {device_id} (类型: {PlatformType.get_name(device_type)})")
                instance["api_client"].connect_device(device_id)

            self._log(instance["instance_id"], "info", f"设备 {device_id} 连接成功")
            return True
        except Exception as e:
            device_id = device_config["device_id"]
            self._log(instance["instance_id"], "error", f"设备 {device_id} 连接失败: {e}")
            return False

    def start_collection(self) -> List[threading.Thread]:
        """启动批量性能采集（公共接口）"""
        return self._start_collection_threads()

    def _start_collection_threads(self) -> List[threading.Thread]:
        """启动批量性能采集（串行化WebSocket连接建立）"""
        if not self.target_combinations:
            raise ValueError("必须先调用 prepare_targets 方法")

        available_instances = [inst for inst in self.instances.values() if inst["status"] == "running"]
        if not available_instances:
            logger.error("没有可用的实例")
            return []

        collection_threads = []

        # 为每个目标分配实例并启动采集线程
        for i, target in enumerate(self.target_combinations):
            instance = available_instances[i % len(available_instances)]
            instance["assigned_target"] = target

            thread = self._create_collection_thread(target, instance)
            collection_threads.append(thread)
            thread.start()

            self._log(instance["instance_id"], "info", f"启动采集线程: {target['unique_key']}")

            # 串行化启动避免WebSocket连接冲突，增加等待时间
            if i < len(self.target_combinations) - 1:
                time.sleep(2)

        logger.info(f"批量启动采集，线程数: {len(collection_threads)}")
        return collection_threads

    def _create_collection_thread(self, target: Dict[str, Any], instance: Dict[str, Any]) -> threading.Thread:
        """创建采集线程"""
        return threading.Thread(
            target=self._execute_collection,
            args=(target, instance),
            name=f"collection_{target['unique_key']}"
        )

    def _execute_collection(self, target: Dict[str, Any], instance: Dict[str, Any]):
        """执行性能采集"""
        api_client = instance["api_client"]
        instance_id = instance["instance_id"]

        try:
            self._log(instance_id, "info", f"开始采集: {target['unique_key']}")

            # 选择目标进程
            process_info = self._select_target_process(api_client, target, instance_id)

            # 执行性能数据采集 - 使用GamePerf识别的完整应用名称格式
            gameperf_app_name = process_info.get('full_app_name', '')
            if not gameperf_app_name:
                # 如果没有完整应用名称，使用传统方式构建
                app_package = target['app_package']
                if app_package:
                    gameperf_app_name = f"{target['app_name']}({app_package})"
                else:
                    gameperf_app_name = target['app_name']

            progress_callback = self._create_progress_callback(instance_id, target["duration"])

            self._log(instance_id, "info", f"开始性能数据采集: {gameperf_app_name}")
            self._log(instance_id, "info", f"采集参数 - 应用: {gameperf_app_name}, 进程: {process_info['name']}, 持续时间: {target['duration']}秒")

            try:
                api_client.collect_perf_data(
                    app_name=gameperf_app_name,
                    time_actions=target["time_actions"],
                    process_name=process_info["name"],
                    duration=target["duration"],
                    on_time_callback=progress_callback
                )

                self._log(instance_id, "info", f"性能数据采集方法执行完成")

            except Exception as collect_error:
                self._log(instance_id, "error", f"性能数据采集过程中发生异常: {collect_error}")
                raise

            # 记录采集结果
            data_count = len(api_client.perf_data) if hasattr(api_client, 'perf_data') else 0
            self._log(instance_id, "info", f"采集完成: {target['unique_key']}, 数据条数: {data_count}")

            # 分析采集结果
            if data_count == 0:
                self._log(instance_id, "warning", f"采集到0条数据，可能是应用进程状态异常或配置问题")
            else:
                self._log(instance_id, "info", f"采集成功，共获得 {data_count} 条性能数据")

            # 移动GamePerf数据文件到主目录
            try:
                main_dir = os.getcwd()  # 获取主目录
                moved_files = self.move_gameperf_data_files(
                    stop_record_results=api_client.stop_record_results,
                    target_dir=main_dir,
                    device_id=target["device_id"],
                    app_name=target["app_name"],
                    app_package=target["app_package"],
                    process_name=process_info.get("name")
                )

                if moved_files:
                    self._log(instance_id, "info", f"成功移动 {len(moved_files)} 个GamePerf数据文件到主目录")
                    for file_info in moved_files:
                        self._log(instance_id, "info", f"文件移动: {file_info['original_path']} -> {file_info['new_path']}")
                else:
                    self._log(instance_id, "warning", "没有找到需要移动的GamePerf数据文件")

            except Exception as move_error:
                self._log(instance_id, "error", f"移动GamePerf数据文件失败: {move_error}")

        except Exception as e:
            self._log(instance_id, "error", f"采集失败 {target['unique_key']}: {e}")

    def _select_target_process(self, api_client, target: Dict[str, Any], instance_id: str) -> Dict[str, Any]:
        """选择目标进程"""
        # 执行进程选择
        selected_process_name, selected_pid, selection_reason, full_app_name = api_client.select_target_process(
            app_name=target["app_name"],
            app_package=target["app_package"],
            process_name=target["process_name"]
        )

        # 合并日志记录，减少重复输出
        self._log(instance_id, "info", f"选择进程: {selected_process_name} (PID: {selected_pid}) - {selection_reason}")

        return {"name": selected_process_name, "pid": selected_pid, "reason": selection_reason, "full_app_name": full_app_name}

    def _create_progress_callback(self, instance_id: str, duration: int) -> Callable[[int], None]:
        """创建进度回调函数"""
        _ = duration  # 忽略未使用的参数
        def progress_callback(current_time: int):
            # 执行全局回调
            if self.global_config["on_time_callback"]:
                try:
                    self.global_config["on_time_callback"](current_time)
                except Exception as e:
                    self._log(instance_id, "warning", f"时间回调执行失败: {e}")

        return progress_callback

    def stop_all(self):
        """停止所有实例"""
        logger.info("开始停止所有GamePerf实例")

        # 停止线程池
        if hasattr(self, '_executor'):
            self._executor.shutdown(wait=True)

        # 清理实例
        self.instances.clear()

        # 清理GamePerf进程
        self.cleanup_gameperf_processes()

        logger.info("所有实例已停止")

    def cleanup_gameperf_processes(self):
        """
        清理GamePerf相关进程

        执行以下操作：
        1. 查看当前运行的GamePerf相关进程
        2. 识别需要终止的GamePerf进程的PID
        3. 强制终止这些进程
        4. 验证进程已完全清理
        """
        logger.info("开始清理GamePerf相关进程")

        # 第一步：查看当前运行的GamePerf相关进程
        initial_processes = self._get_gameperf_processes()
        if not initial_processes:
            logger.info("未发现运行中的GamePerf进程")
            return

        logger.info(f"发现 {len(initial_processes)} 个GamePerf相关进程:")

        # 第二步：识别并终止GamePerf进程
        terminated_pids = []

        # 方法1：使用pkill命令批量终止
        try:
            logger.info("使用pkill命令批量终止GamePerf进程")
            result = subprocess.run(['pkill', '-9', '-f', 'GamePerf'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("pkill命令执行成功")
            else:
                logger.warning(f"pkill命令返回码: {result.returncode}")
        except subprocess.TimeoutExpired:
            logger.warning("pkill命令执行超时")
        except Exception as e:
            logger.warning(f"pkill命令执行失败: {e}")

        # 方法2：逐个终止进程（备用方案）
        for proc in initial_processes:
            pid = proc['pid']
            try:
                subprocess.run(['kill', '-9', str(pid)],
                             capture_output=True, text=True, timeout=5)
                terminated_pids.append(pid)
            except Exception as e:
                logger.warning(f"终止进程 {pid} 失败: {e}")

        # 等待进程完全终止
        time.sleep(2)

        # 第三步：验证进程已完全清理
        remaining_processes = self._get_gameperf_processes()
        if not remaining_processes:
            logger.info("所有GamePerf进程已成功清理")
        else:
            logger.warning(f"仍有 {len(remaining_processes)} 个GamePerf进程未清理:")
            for proc in remaining_processes:
                logger.warning(f"  PID: {proc['pid']}, 命令: {proc['cmd']}")

            # 最后尝试：强制清理残留进程
            logger.info("尝试强制清理残留进程")
            for proc in remaining_processes:
                try:
                    subprocess.run(['kill', '-9', str(proc['pid'])],
                                 capture_output=True, text=True, timeout=5)
                except Exception as e:
                    logger.error(f"强制清理进程 {proc['pid']} 失败: {e}")

            # 最终验证
            time.sleep(1)
            final_processes = self._get_gameperf_processes()
            if not final_processes:
                logger.info("残留进程已成功清理")
            else:
                logger.error(f"仍有 {len(final_processes)} 个进程无法清理")

    def _get_gameperf_processes(self) -> List[Dict[str, str]]:
        """
        获取当前运行的GamePerf相关进程列表

        Returns:
            List[Dict[str, str]]: 进程信息列表，每个元素包含 pid 和 cmd
        """
        try:
            # 使用ps命令查找GamePerf相关进程
            result = subprocess.run(['ps', '-ef'], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                logger.error(f"ps命令执行失败，返回码: {result.returncode}")
                return []

            processes = []
            lines = result.stdout.strip().split('\n')

            for line in lines:
                # 过滤包含GamePerf的进程行（排除grep本身）
                if 'GamePerf' in line and 'grep' not in line:
                    # 解析进程信息
                    parts = line.split()
                    if len(parts) >= 8:  # 确保有足够的字段
                        pid = parts[1]  # 第二个字段是PID
                        cmd = ' '.join(parts[7:])  # 从第8个字段开始是命令

                        processes.append({
                            'pid': pid,
                            'cmd': cmd
                        })

            return processes

        except subprocess.TimeoutExpired:
            logger.error("ps命令执行超时")
            return []
        except Exception as e:
            logger.error(f"获取GamePerf进程列表失败: {e}")
            return []

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.stop_all()

    def run_collection(self, configs: List[Dict[str, Any]]) -> bool:
        """运行完整的GamePerf性能采集流程

        Args:
            configs: 配置列表，每个配置项包含设备和应用信息

        Returns:
            bool: 采集是否成功完成
        """
        try:
            # 准备目标应用
            self.prepare_targets(configs)

            # 启动GamePerf实例
            if not self.start_instances():
                logger.error("启动实例失败")
                return False

            # 等待服务完全启动
            logger.info("等待GamePerf服务完全启动...")
            time.sleep(3)

            # 连接设备
            if not self.connect_devices(configs):
                logger.error("连接设备失败")
                return False

            # 开始性能采集
            threads = self._start_collection_threads()
            if not threads:
                logger.error("启动采集失败")
                return False

            # 等待采集完成
            for thread in threads:
                thread.join()

            logger.info("GamePerf性能采集完成")
            return True

        except Exception as e:
            logger.error(f"GamePerf性能采集执行失败: {e}")
            return False

        finally:
            # 确保资源被正确清理
            self.stop_all()

    def move_gameperf_data_files(self, stop_record_results: List[Dict[str, Any]], target_dir: str, device_id: str, app_name: str, app_package: Optional[str] = None, process_name: Optional[str] = None) -> List[Dict[str, str]]:
        """
        移动GamePerf性能数据文件到指定目录并重命名

        Args:
            stop_record_results: stop_record操作的结果列表
            target_dir: 目标主目录路径
            device_id: 设备ID
            app_name: 应用显示名称
            app_package: 应用包名（可选）
            process_name: 进程名称（可选）

        Returns:
            List[Dict[str, str]]: 移动后的文件信息列表，包含原路径和新路径
        """
        moved_files = []

        if not stop_record_results:
            logger.warning("没有找到stop_record操作的结果，无法移动数据文件")
            return moved_files

        for result in stop_record_results:
            if not result or not result.get('path'):
                logger.warning("stop_record结果中没有找到文件路径")
                continue

            original_path = result.get('path')
            if not os.path.exists(original_path):
                logger.warning(f"原始文件不存在: {original_path}")
                continue

            try:
                # 使用传入的包名，如果没有则尝试从app_name中提取
                package_name = app_package or self._extract_package_name(app_name)
                final_process_name = process_name or self._extract_process_name_from_path(original_path)

                # 使用优化的路径构建逻辑
                if not final_process_name or final_process_name == package_name:
                    # 简化路径格式：data/{device_id}/{package_name}/
                    target_subdir = os.path.join(target_dir, "data", device_id, package_name)
                else:
                    # 多进程路径格式：data/{device_id}/{package_name}/{process_name}/
                    target_subdir = os.path.join(target_dir, "data", device_id, package_name, final_process_name)

                # 创建目标目录
                os.makedirs(target_subdir, exist_ok=True)

                # 构建新文件路径
                new_file_path = os.path.join(target_subdir, "gameperf_raw_perf_data.json")

                # 移动文件
                shutil.move(original_path, new_file_path)

                moved_info = {
                    "original_path": original_path,
                    "new_path": new_file_path,
                    "url": result.get('url', ''),
                    "file_export": result.get('fileExport', False),
                    "file_upload": result.get('fileUpload', False)
                }
                moved_files.append(moved_info)

                logger.info(f"GamePerf数据文件已移动: {original_path} -> {new_file_path}")

            except Exception as e:
                logger.error(f"移动GamePerf数据文件失败 {original_path}: {e}")

        return moved_files

    def build_gameperf_save_path(self, device_id: str, package_name: str, process_name: Optional[str] = None) -> str:
        """构建GamePerf性能数据保存路径

        Args:
            device_id: 设备ID
            package_name: 应用包名
            process_name: 进程名称（可选）

        Returns:
            str: GamePerf数据保存路径
            - 当process_name为空或与package_name相同时：data/{device_id}/{package_name}/
            - 当process_name不同时：data/{device_id}/{package_name}/{process_name}/
        """
        try:
            # 路径简化逻辑：当进程名未指定或与包名相同时，使用简化路径
            if not process_name or process_name == package_name:
                # 简化路径格式：data/{device_id}/{package_name}/
                save_dir = os.path.join("data", device_id, package_name)
                logger.info(f"[GamePerf性能监控] 使用简化路径格式: {save_dir}")
            else:
                # 多进程路径格式：data/{device_id}/{package_name}/{process_name}/
                save_dir = os.path.join("data", device_id, package_name, process_name)
                logger.info(f"[GamePerf性能监控] 使用多进程路径格式: {save_dir}")

            # 确保目录存在
            os.makedirs(save_dir, exist_ok=True)
            return save_dir

        except Exception as e:
            logger.error(f"[GamePerf性能监控] 构建保存路径失败: {e}")
            raise

    def _extract_package_name(self, app_name: str) -> str:
        """
        从应用名称中提取包名

        Args:
            app_name: 应用名称，格式可能为"显示名称(包名)"或"显示名称"

        Returns:
            str: 包名
        """
        # 尝试从括号中提取包名
        match = re.search(r'\(([^)]+)\)', app_name)
        if match:
            return match.group(1)

        # 如果没有括号，直接使用应用名称
        return app_name

    def _extract_app_display_name(self, app_name: str) -> str:
        """
        从应用名称中提取显示名称（去掉包名部分）

        Args:
            app_name: 应用名称，格式可能为"显示名称(包名)"或"显示名称"

        Returns:
            str: 应用显示名称
        """
        # 如果包含括号，提取括号前的部分
        match = re.search(r'^([^(]+)', app_name)
        if match:
            return match.group(1).strip()

        # 如果没有括号，直接返回应用名称
        return app_name

    def _extract_process_name_from_path(self, file_path: str) -> Optional[str]:
        """
        从文件路径中提取进程名称

        Args:
            file_path: 文件路径

        Returns:
            Optional[str]: 进程名称
        """
        try:
            # 从路径中提取文件夹名称，通常包含进程信息
            dir_name = os.path.basename(os.path.dirname(file_path))

            # 尝试从目录名中提取进程名（通常在时间戳之前）
            # 例如: "system_server性能测试_2025_06_25_17_21_04"
            # 或者: "飞书comssandroidlark_2025_06_25_17_21_04"

            # 先尝试按时间戳模式分割（查找连续的数字模式）
            # 查找时间戳模式 _YYYY_MM_DD_HH_MM_SS
            timestamp_pattern = r'_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}$'
            match = re.search(timestamp_pattern, dir_name)

            if match:
                # 移除时间戳部分
                process_part = dir_name[:match.start()]

                # 对于system_server这种情况，保留完整的进程名
                if 'system_server' in process_part:
                    return 'system_server'
                elif process_part:
                    return process_part

            # 如果没有找到时间戳模式，尝试简单的下划线分割
            parts = dir_name.split('_')
            if len(parts) >= 2:
                # 对于system_server这种情况
                if len(parts) >= 2 and parts[0] == 'system' and parts[1] == 'server':
                    return 'system_server'
                # 其他情况取第一部分
                return parts[0]

        except Exception as e:
            logger.warning(f"从路径提取进程名失败: {e}")

        return None

    def start_collection_for_app(self, app, perf_collect_mode, perf_device_ip: str = None,
                                 collect_duration: int = 30, title: str = None) -> bool:
        """开始GamePerf性能数据采集

        Args:
            app: Android或iOS TikTok应用实例
            perf_collect_mode: 采集模式（有线/无线）
            perf_device_ip: 设备IP（无线连接时需要）
            collect_duration: 采集时间（秒）
            title: 测试用例标题

        Returns:
            bool: 采集是否成功
        """
        try:
            # 构建GamePerf配置
            device_name = getattr(app.get_device(), 'name', 'Unknown')
            platform_name = "Android" if hasattr(app, 'package_name') else "iOS"
            case_name = title or "性能测试"
            scene_name = f"{device_name}_{platform_name}_{case_name}"

            # 获取正确的应用信息
            if hasattr(app, 'package_name'):  # Android应用
                app_name = "TikTok"  # GamePerf中显示的应用名称
                package_name = app.package_name  # com.zhiliaoapp.musically
                device_type = PlatformType.ANDROID
            else:  # iOS应用
                app_name = "TikTok"  # iOS版本的TikTok
                package_name = app.bundle_id
                device_type = PlatformType.IOS

            config = {
                "device_id": app.get_device().udid,
                "app_name": app_name,
                "app_package": package_name,  # 使用app_package而不是package_name
                "scene_name": scene_name,
                "duration": collect_duration,
                "connection_type": perf_collect_mode,
                "device_type": device_type
            }

            # 添加无线连接配置
            if perf_collect_mode == PerfCollectMode.WIRELESS_COLLECT:
                config["device_ip"] = perf_device_ip

            # 先清理现有的GamePerf进程
            gameperf_manager.cleanup_gameperf_processes()

            return gameperf_manager.run_collection([config])

        except Exception as e:
            logger.error(f"[GamePerf性能] 执行采集失败: {str(e)}")
            return False


if __name__ == "__main__":
    # 创建测试用管理器实例
    manager = GamePerfManager()

    configs = [
        # GamePerf-Android-有线连接-飞书
        # {
        #     "device_id": "R5CWB30CMWK",
        #     "app_name": "飞书",
        #     "app_package": "com.ss.android.lark",
        #     "process_name": None,
        #     "duration": 30,
        #     "scene_name": "飞书性能测试",  # 可选，默认为"{app_name}性能测试"
        #     "subscenes": [  # 可选，子场景配置
        #         {"name": "应用启动", "start": 5, "end": 12},
        #         {"name": "核心功能", "start": 15, "end": 25}
        #     ],
        #     "file_export": 1,  # 可选，默认1
        #     "file_upload": 0,  # 可选，默认0
        #     "connection_type": PerfCollectMode.WIRE_COLLECT,
        #     "device_type": PlatformType.ANDROID
        # },

        # GamePerf-Android-有线连接-飞连
        # {
        #     "device_id": "R5CWB30CMWK",
        #     "app_name": "飞连",
        #     "app_package": "com.volcengine.corplink",
        #     "duration": 30,
        #     "connection_type": PerfCollectMode.WIRE_COLLECT,
        #     "device_type": PlatformType.ANDROID
        # },

        # GamePerf-iOS-有线连接-飞书
        # {
        #     "device_id": "00008101-001865592650001E",
        #     "app_name": "飞书",
        #     "app_package": "com.bytedance.ee.lark",
        #     "process_name": None,
        #     "duration": 30,
        #     "scene_name": "飞书性能测试",  # 可选，默认为"{app_name}性能测试"
        #     "subscenes": [  # 可选，子场景配置
        #         {"name": "应用启动", "start": 5, "end": 12},
        #         {"name": "核心功能", "start": 15, "end": 25}
        #     ],
        #     "file_export": 1,  # 可选，默认1
        #     "file_upload": 0,  # 可选，默认0
        #     "connection_type": PerfCollectMode.WIRE_COLLECT,
        #     "device_type": PlatformType.IOS,
        # },

        # GamePerf-iOS-有线连接-飞连
        {
            "device_id": "00008101-001865592650001E",
            "app_name": "飞连",
            "app_package": "com.volcengine.corplink",
            "process_name": None,
            "duration": 30,
            "connection_type": PerfCollectMode.WIRE_COLLECT,
            "device_type": PlatformType.IOS
        }

    ]

    # 执行采集
    if configs:
        try:
            success = manager.run_collection(configs)
            if success:
                logger.info("GamePerf性能采集测试完成")
            else:
                logger.error("GamePerf性能采集测试失败")
        except Exception as e:
            logger.error(f"GamePerf性能采集测试异常: {e}")
    else:
        logger.info("没有配置项，跳过测试")


# 创建全局GamePerf管理器实例
gameperf_manager = GamePerfManager()
