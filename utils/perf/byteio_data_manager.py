import json
import os
from typing import List, Optional, Dict, Any
from defines import *
from utils.apis.byteio_api import byteio_api
from utils.common.log_utils import logger
from utils.common.time_utils import convert_timestamp_to_digits


class ByteIODataManager:
    """ByteIO数据管理类"""

    # 文件命名规范
    FILE_NAMES = {
        "byteio_raw": "byteio_raw_data.json",
        "byteio_processed": "byteio_processed_data.json",
        "byteio_avg": "byteio_avg_data.json"
    }

    def __init__(self):
        self.byteio_cache = {}

    def get_byteio_data(
        self,
        app_id: str,
        device_id: int,
        start_time: int,
        end_time: int,
        log_types: List[str],
        event_names: List[str]
    ) -> Optional[List[Dict[str, Any]]]:
        """获取ByteIO原始数据的核心方法"""
        try:
            start_time = convert_timestamp_to_digits(start_time, 10)
            end_time = convert_timestamp_to_digits(end_time, 10)
            logger.info(f"[性能数据处理] 开始获取ByteIO数据 app_id:{app_id} device_id:{device_id}")

            cache_key = f"{app_id}_{device_id}_{start_time}_{end_time}_{'_'.join(log_types)}_{'_'.join(event_names)}"
            if cache_key in self.byteio_cache:
                raw_data = self.byteio_cache[cache_key]
                logger.info(f"[性能数据处理] 使用缓存数据 {cache_key}")
            else:
                byteio_api.verify_session(app_id=app_id, device_ids=[device_id])
                result = byteio_api.query_events(app_id, device_id, start_time, end_time, log_types, event_names)
                raw_data = result.get("data", [])
                self.byteio_cache[cache_key] = raw_data
                logger.info(f"[性能数据处理] 从API获取数据 {cache_key}")

            if not raw_data:
                logger.warning(f"[性能数据处理] {cache_key}未获取到ByteIO原始数据")
                return None

            logger.info(f"[性能数据处理] ByteIO原始数据获取成功，共{len(raw_data)}条记录")
            return raw_data

        except Exception as e:
            logger.error(f"[性能数据处理] ByteIO数据获取失败: {str(e)}")
            return None

    def get_byteio_metrics_data(
        self,
        device_id: int,
        start_time: int,
        end_time: int,
        byteio_configs: Optional[List[Dict[str, Any]]] = None,
        save_raw_data_path: Optional[str] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """获取ByteIO数据，支持多配置并行处理，自动优化重复请求"""
        try:
            if byteio_configs is None:
                byteio_configs = [{}]

            all_processed_data = []
            config_groups = {}

            for i, byteio_config in enumerate(byteio_configs):
                # 从新结构中提取配置信息
                query_conditions = byteio_config.get('query_conditions', {})

                app_id = query_conditions.get('app_id', APP_ID)
                log_types = query_conditions.get('log_types', BYTEIO_LOG_TYPE)
                event_names = query_conditions.get('event_names', BYTEIO_EVENT_NAME)
                field_name = query_conditions.get('field_name', None)
                required_fields = query_conditions.get('required_fields', None)

                group_key = (
                    app_id,
                    device_id,
                    start_time,
                    end_time,
                    tuple(log_types),
                    tuple(event_names)
                )

                if group_key not in config_groups:
                    config_groups[group_key] = []

                config_groups[group_key].append({
                    'index': i,
                    'config': byteio_config,
                    'app_id': app_id,
                    'log_types': log_types,
                    'event_names': event_names,
                    'metric_key': byteio_config.get('metric_key'),
                    'field_name': field_name,
                    'required_fields': required_fields
                })

            for group_key, configs in config_groups.items():
                group_app_id, group_device_id, group_start_time, group_end_time, log_types_tuple, event_names_tuple = group_key
                log_types = list(log_types_tuple)
                event_names = list(event_names_tuple)

                logger.info(f"[性能数据处理] 处理配置组 app_id:{group_app_id} device_id:{group_device_id} 包含{len(configs)}个配置")

                raw_data = self.get_byteio_data(
                    app_id=group_app_id,
                    device_id=group_device_id,
                    start_time=group_start_time,
                    end_time=group_end_time,
                    log_types=log_types,
                    event_names=event_names
                )

                if not raw_data:
                    logger.warning(f"[性能数据处理] app_id:{group_app_id} device_id:{group_device_id} 未获取到ByteIO原始数据，跳过该组的所有配置")
                    continue

                # 立即保存原始数据（如果提供了保存路径）
                if save_raw_data_path and raw_data:
                    try:
                        self.save_byteio_raw_data(raw_data, save_raw_data_path)
                        logger.info(f"[性能数据处理] ByteIO原始数据已保存，数据条数: {len(raw_data)}")
                    except Exception as e:
                        logger.error(f"[性能数据处理] 保存ByteIO原始数据失败: {str(e)}")

                # 添加调试日志：检查数据结构
                if raw_data:
                    first_item = raw_data[0]
                    logger.info(f"[性能数据处理] 第一条数据的键: {list(first_item.keys())}")
                    if 'raw_event' in first_item:
                        raw_event = first_item['raw_event']
                        logger.info(f"[性能数据处理] raw_event类型: {type(raw_event)}, 长度: {len(raw_event) if isinstance(raw_event, str) else 'N/A'}")
                        # 尝试解析第一层JSON
                        if isinstance(raw_event, str):
                            try:
                                parsed_raw_event = json.loads(raw_event)
                                logger.info(f"[性能数据处理] 解析后的raw_event键: {list(parsed_raw_event.keys())}")
                                if 'params' in parsed_raw_event:
                                    params = parsed_raw_event['params']
                                    logger.info(f"[性能数据处理] params类型: {type(params)}")
                            except json.JSONDecodeError as e:
                                logger.warning(f"[性能数据处理] 解析raw_event失败: {e}")
                    else:
                        logger.warning("[性能数据处理] 第一条数据中没有raw_event字段")

                for config_info in configs:
                    metric_key = config_info['metric_key']
                    field_name = config_info['field_name']
                    required_fields = config_info['required_fields']

                    if field_name is not None:
                        # 使用 field_name 参数处理数据
                        processed_data = self.get_field_values(
                            data_list=raw_data,
                            field_name=field_name,
                            required_fields=required_fields
                        )

                        config_results = []
                        for j, item in enumerate(processed_data):
                            if not isinstance(item, dict):
                                continue

                            # 添加时间字段（从对应的原始数据中获取server_time）
                            result_item = {}
                            if j < len(raw_data) and isinstance(raw_data[j], dict):
                                # 使用server_time作为时间戳
                                time_value = raw_data[j].get("server_time")
                                if time_value is None:
                                    # 如果没有server_time，尝试其他时间字段
                                    time_value = raw_data[j].get("datetime") or raw_data[j].get("local_time_ms") or 0
                                result_item["time"] = int(time_value)

                            # 添加处理后的字段值
                            result_item.update(item)
                            config_results.append(result_item)
                    else:
                        # 使用原有的 metric_key 处理方式
                        config_results = []
                        for data_item in raw_data:
                            if not isinstance(data_item, dict) or metric_key not in data_item:
                                continue

                            # 使用server_time作为时间戳
                            time_value = data_item.get("server_time")
                            if time_value is None:
                                # 如果没有server_time，尝试其他时间字段
                                time_value = data_item.get("datetime") or data_item.get("local_time_ms") or 0

                            config_results.append({
                                "time": int(time_value),
                                metric_key: data_item[metric_key]
                            })

                    if config_results:
                        all_processed_data.extend(config_results)
                        field_info = f"field_name:{field_name}" if field_name else f"metric_key:{metric_key}"
                        logger.info(f"[性能数据处理] 配置{config_info['index']} {field_info} 处理成功，获取{len(config_results)}条记录")
                    else:
                        field_info = f"field_name:{field_name}" if field_name else f"metric_key:{metric_key}"
                        logger.warning(f"[性能数据处理] 配置{config_info['index']} {field_info} 未获取到有效的字段值")

            if not all_processed_data:
                logger.warning("[性能数据处理] 所有配置都未获取到有效数据")
                return None

            logger.info(f"[性能数据处理] ByteIO数据获取成功，共{len(all_processed_data)}条记录")
            return all_processed_data

        except Exception as e:
            logger.error(f"[性能数据处理] ByteIO数据获取失败: {str(e)}")
            return None

    def get_field_values(
        self,
        data_list: List[Any],
        field_name: Optional[str] = None,
        required_fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """获取指定字段的值列表，支持通过点号访问嵌套字段

        注意：不再使用required_fields进行严格检查，每个字段独立处理
        """
        def get_nested_value(data: Any, keys: List[str]) -> Any:
            if not keys:
                return data

            current_key = keys[0]

            # 如果数据是字符串，尝试解析为JSON
            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except json.JSONDecodeError:
                    return None

            if isinstance(data, dict):
                if current_key in data:
                    next_value = data[current_key]
                    # 递归处理下一层，如果下一层也是JSON字符串会在递归中处理
                    return get_nested_value(next_value, keys[1:])

            return None

        result = []
        logger.info(f"[数据处理] 开始处理字段提取，输入数据条数: {len(data_list)}, 字段名: {field_name}")

        for i, data in enumerate(data_list):
            if not isinstance(data, dict):
                logger.debug(f"[数据处理] 跳过非字典数据，索引: {i}")
                continue

            if field_name is None:
                result.append(data)
                continue

            item = {}
            field_path = field_name.split('.')
            value = get_nested_value(data, field_path)

            result_key = field_path[-1]
            item[result_key] = value

            # 只要能提取到字段（即使值为None或0），都添加到结果中
            result.append(item)

        logger.info(f"[数据处理] 获取字段值列表，共 {len(result)} 条记录")
        if result:
            non_none_count = sum(1 for item in result if any(v is not None for v in item.values()))
            logger.info(f"[数据处理] 其中非None值记录数: {non_none_count}")
        return result

    def _process_byteio_data(self, metrics_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理ByteIO指标数据，确保格式与trace处理后数据一致，并合并相同时间戳的数据

        Args:
            metrics_data: ByteIO指标数据（需要添加时间戳）

        Returns:
            List[Dict[str, Any]]: 处理后的ByteIO数据（时间序列格式，相同时间戳已合并）
        """
        try:
            if not metrics_data:
                logger.warning("[性能数据处理] ByteIO指标数据为空")
                return []

            # 第一步：为每个数据项添加时间戳
            timestamped_data = []

            for item in metrics_data:
                if isinstance(item, dict):
                    new_item = item.copy()

                    # 检查是否已经有time字段
                    if "time" not in new_item:
                        # 从原始数据中获取server_time作为时间戳
                        if "server_time" in new_item:
                            new_item["time"] = new_item["server_time"]
                        else:
                            # 如果没有server_time，尝试从其他时间字段获取
                            time_value = None
                            for time_field in ["datetime", "local_time_ms"]:
                                if time_field in new_item:
                                    time_value = new_item[time_field]
                                    break

                            if time_value is not None:
                                new_item["time"] = time_value
                            else:
                                # 最后兜底，使用当前时间戳
                                import time
                                new_item["time"] = int(time.time())
                                logger.warning(f"[性能数据处理] 数据缺少时间戳字段，使用当前时间: {new_item['time']}")

                    timestamped_data.append(new_item)
                else:
                    timestamped_data.append(item)

            # 第二步：按时间戳合并数据
            merged_data = {}

            for item in timestamped_data:
                if isinstance(item, dict) and "time" in item:
                    time_key = item["time"]

                    if time_key not in merged_data:
                        merged_data[time_key] = {"time": time_key}

                    # 合并除time字段外的所有字段
                    for key, value in item.items():
                        if key != "time":
                            # 如果字段已存在且值不同，优先保留非None和非负值
                            if key in merged_data[time_key]:
                                existing_value = merged_data[time_key][key]
                                # 优先保留有效值（非None且非负）
                                if value is not None and (existing_value is None or (isinstance(value, (int, float)) and value >= 0 and (existing_value is None or existing_value < 0))):
                                    merged_data[time_key][key] = value
                            else:
                                merged_data[time_key][key] = value

            # 第三步：转换为列表并按时间排序
            processed_data = list(merged_data.values())
            processed_data.sort(key=lambda x: x.get("time", 0))

            # 第四步：统一转换数值为浮点值
            processed_data = self._convert_numeric_values_to_float(processed_data)

            logger.info(f"[性能数据处理] ByteIO数据处理完成，原始数据条数: {len(timestamped_data)}，合并后数据条数: {len(processed_data)}")
            return processed_data

        except Exception as e:
            logger.error(f"[性能数据处理] 处理ByteIO数据失败: {str(e)}")
            return metrics_data

    def calculate_byteio_avg_data(self, byteio_data: List[Dict[str, Any]]) -> Dict[str, Optional[float]]:
        """计算ByteIO数据的平均值

        Args:
            byteio_data: ByteIO数据列表

        Returns:
            Dict[str, Optional[float]]: 平均值数据
        """
        try:
            if not byteio_data:
                return {}

            # 只计算关键性能指标的平均值，排除时间戳和ID字段
            excluded_fields = {'time', 'timestamp', 'server_time', 'datetime', 'local_time_ms', 'event_id',
                             'app_id', 'device_id', 'user_unique_id'}

            # 收集数值字段
            numeric_fields = set()
            for item in byteio_data:
                if isinstance(item, dict):
                    for key, value in item.items():
                        if isinstance(value, (int, float)) and key not in excluded_fields:
                            numeric_fields.add(key)

            # 计算每个字段的平均值（排除值为0的数据）
            avg_data = {}
            for field in numeric_fields:
                values = []
                for item in byteio_data:
                    if isinstance(item, dict) and field in item:
                        value = item[field]
                        # 排除值为0的数据，只计算有效值（非0且非None）
                        if isinstance(value, (int, float)) and value != 0:
                            values.append(float(value))

                if values:
                    avg_data[field] = round(sum(values) / len(values), 2)
                else:
                    avg_data[field] = None

            logger.info(f"[性能数据处理] ByteIO平均值计算完成，指标数量: {len(avg_data)}")
            return avg_data

        except Exception as e:
            logger.error(f"[性能数据处理] 计算ByteIO平均值失败: {str(e)}")
            return {}

    def save_byteio_avg_data(self, byteio_data: List[Dict[str, Any]], save_local_path: str) -> bool:
        """计算并保存ByteIO平均值数据"""
        try:
            if not byteio_data:
                logger.warning("[性能数据处理] ByteIO数据为空，跳过平均值计算")
                return False

            # 计算平均值
            avg_data = self.calculate_byteio_avg_data(byteio_data)
            if not avg_data:
                logger.warning("[性能数据处理] ByteIO平均值计算结果为空")
                return False

            # 确保目录存在
            safe_path = os.path.realpath(save_local_path)
            os.makedirs(safe_path, exist_ok=True)

            # 保存平均值数据
            avg_file_path = os.path.join(safe_path, self.FILE_NAMES["byteio_avg"])
            with open(avg_file_path, "w", encoding="utf-8") as f:
                json.dump(avg_data, f, ensure_ascii=False, indent=2)

            logger.info(f"[性能数据处理] ByteIO平均值数据保存成功 路径:{avg_file_path} 指标数量:{len(avg_data)}")
            return True

        except Exception as e:
            logger.error(f"[性能数据处理] 保存ByteIO平均值数据失败: {str(e)}")
            return False

    def save_byteio_raw_data(self, data: List[Dict[str, Any]], save_local_path: str) -> Optional[str]:
        """仅保存ByteIO原始数据"""
        if not data:
            logger.warning("[性能数据处理] 没有ByteIO数据需要保存")
            return None

        try:
            safe_path = os.path.realpath(save_local_path)
            os.makedirs(safe_path, exist_ok=True)

            # 保存原始ByteIO数据 (raw)
            byteio_raw_path = os.path.join(safe_path, self.FILE_NAMES["byteio_raw"])
            with open(byteio_raw_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"[性能数据处理] ByteIO原始数据保存成功 路径:{byteio_raw_path}")

            return byteio_raw_path

        except Exception as e:
            logger.error(f"[性能数据处理] ByteIO原始数据保存失败: {str(e)}")
            return None

    def save_byteio_processed_and_avg_data(self, data: List[Dict[str, Any]], save_local_path: str) -> bool:
        """保存ByteIO处理后数据和平均值数据"""
        try:
            if not data:
                logger.warning("[性能数据处理] 没有ByteIO数据需要处理")
                return False

            safe_path = os.path.realpath(save_local_path)
            os.makedirs(safe_path, exist_ok=True)

            # 处理数据并保存 (processed)
            processed_data = self._process_byteio_data(data)
            byteio_processed_path = os.path.join(safe_path, self.FILE_NAMES["byteio_processed"])
            with open(byteio_processed_path, "w", encoding="utf-8") as f:
                json.dump(processed_data, f, ensure_ascii=False, indent=2)
            logger.info(f"[性能数据处理] ByteIO处理后数据保存成功 路径:{byteio_processed_path}")

            # 计算并保存平均值数据 (avg)
            self.save_byteio_avg_data(processed_data, safe_path)

            return True

        except Exception as e:
            logger.error(f"[性能数据处理] 保存ByteIO处理后数据失败: {str(e)}")
            return False

    def _convert_numeric_values_to_float(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """统一转换数值为浮点值

        将数据中的字符串格式数值转换为浮点数，保持其他类型不变

        Args:
            data: 待转换的数据列表

        Returns:
            List[Dict[str, Any]]: 转换后的数据列表
        """
        try:
            converted_data = []

            for item in data:
                if isinstance(item, dict):
                    converted_item = {}

                    for key, value in item.items():
                        # 跳过时间相关字段，保持整数类型
                        if key in ["time", "timestamp"]:
                            converted_item[key] = value
                        elif value is None:
                            converted_item[key] = value
                        elif isinstance(value, str):
                            # 尝试转换字符串为浮点数
                            try:
                                # 检查是否为数值字符串
                                if value.replace('.', '').replace('-', '').replace('+', '').isdigit():
                                    converted_item[key] = float(value)
                                else:
                                    converted_item[key] = value
                            except (ValueError, TypeError):
                                converted_item[key] = value
                        else:
                            # 其他类型保持不变
                            converted_item[key] = value

                    converted_data.append(converted_item)
                else:
                    converted_data.append(item)

            logger.debug(f"[数值转换] 完成数值转换，处理了 {len(converted_data)} 条记录")
            return converted_data

        except Exception as e:
            logger.error(f"[数值转换] 转换数值为浮点值失败: {str(e)}")
            return data


# 创建全局ByteIO数据管理器实例
byteio_data_manager = ByteIODataManager()

if __name__ == "__main__":
    def _create_test_byteio_configs() -> List[Dict[str, Any]]:
        """创建测试ByteIO配置"""
        return [
            {
                "metric_key": "field",
                "query_conditions": {
                    "app_id": 1233,  # 应用ID（可选，默认使用APP_ID）
                    "log_types": ["mario_event"],  # 日志类型（可选，默认使用BYTEIO_LOG_TYPE）
                    "event_names": ["play_session_events"],  # 事件名称（可选，默认使用BYTEIO_EVENT_NAME）
                    "field_name": "some.nested.field",  # 字段名（可选，支持点号访问嵌套字段）
                    "required_fields": ["field1", "field2"]  # 必需字段列表（可选）
                }
            }
        ]

    # 测试ByteIO数据获取功能
    test_configs = _create_test_byteio_configs()
    result = byteio_data_manager.get_byteio_metrics_data(
        device_id=7306110349106185774,
        start_time=1745737200,
        end_time=1745737500,
        byteio_configs=test_configs
    )

    if result:
        logger.info(f"测试成功，获取到{len(result)}条数据")
        logger.info(result)
    else:
        logger.warning("测试失败，未获取到数据")
