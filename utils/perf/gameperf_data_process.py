"""GamePerf性能数据处理模块"""

import os
import json
import statistics
import glob
from typing import List, Dict, Optional, Mapping, Any, Tuple, Union
from dataclasses import dataclass, field
from defines import PlatformType
from utils.common.log_utils import logger

from utils.perf.gameperf_monitor import gameperf_manager
from utils.perf.trace_data_manager import trace_data_manager
from utils.perf.byteio_data_manager import byteio_data_manager
from utils.perf.profile_collector import profile_collector
from utils.common.time_utils import convert_timestamp_to_digits


# GamePerf列表字段定义（字符串格式的列表字段）
LIST_FIELDS = {
    "all_cpus", "all_cpus_freq", "all_frame_times", "normalized_all_cpus"
}


@dataclass
class GamePerfMetrics:
    """GamePerf性能指标数据类"""
    platform_type: str
    metrics_values: Dict[str, List[float]] = field(default_factory=dict)
    list_metrics_values: Dict[str, List[List[float]]] = field(default_factory=dict)  # 新增：存储列表类型字段的数据

    def add_metric_value(self, metric_name: str, value: float):
        """添加指标值"""
        if metric_name not in self.metrics_values:
            self.metrics_values[metric_name] = []
        if isinstance(value, (int, float)) and value >= 0:
            self.metrics_values[metric_name].append(float(value))

    def add_list_metric_value(self, metric_name: str, values: List[float]):
        """添加列表类型指标值"""
        if metric_name not in self.list_metrics_values:
            self.list_metrics_values[metric_name] = []
        if isinstance(values, list) and values:
            self.list_metrics_values[metric_name].append(values)

    def calculate_averages(self) -> Dict[str, Union[Optional[float], List[Optional[float]]]]:
        """计算平均值"""
        averages = {}

        # 处理普通数值字段
        for metric_name, values in self.metrics_values.items():
            if values:
                try:
                    avg_value = statistics.mean(values)
                    averages[metric_name] = round(avg_value, 2)
                except statistics.StatisticsError:
                    averages[metric_name] = None
            else:
                averages[metric_name] = None

        # 处理列表字段
        for metric_name, list_values in self.list_metrics_values.items():
            averages[metric_name] = self._calculate_list_metric_average(list_values)

        return averages

    def _calculate_list_metric_average(self, list_values: List[List[float]]) -> Optional[List[Optional[float]]]:
        """计算列表字段的平均值"""
        if not list_values:
            return None

        # 找到最大长度
        max_length = max(len(values) for values in list_values)
        if max_length == 0:
            return None

        # 计算每个索引位置的平均值
        result = []
        for i in range(max_length):
            position_values = []
            for values in list_values:
                if i < len(values):
                    position_values.append(values[i])

            if position_values:
                try:
                    avg_value = statistics.mean(position_values)
                    result.append(round(avg_value, 2))
                except statistics.StatisticsError:
                    result.append(None)
            else:
                result.append(None)

        return result if result else None

class GamePerfMetricsCalculator:
    """GamePerf性能指标计算工具类"""

    ANDROID_EXCLUSIVE_FIELDS = {
        # CPU相关指标
        "normalized_device_cpu", "normalized_cpu", "all_cpus_freq", "normalized_all_cpus",
        # GPU相关指标
        "gpuClk", "gpu_load",
        # Mali GPU专用指标
        "mali_gpu_active", "mali_fragment_active", "mali_non_fragment_active",
        "mali_tiler_active", "mali_fragment_utilization", "mali_non_fragment_utilization",
        "mali_tiler_utilization", "mali_overdraw", "mali_pixel_throughput",
        "mali_l2_texture", "mali_l2_load_stona wo gere", "mali_bus_read", "mali_bus_write",
        "mali_culled_primitives", "mali_visible_primitives", "mali_input_primitives"
    }

    IOS_EXCLUSIVE_FIELDS = {
        "all_frame_times",
        "context_switch", "wakeup", "render_gpu", "tiler_gpu",
        "rmem", "disk_read", "disk_written",
        "cost_energy", "cpu_energy", "gpu_energy", "network_energy",
        "location_energy", "thermal_energy", "overhead_energy", "c_temper"
    }

    TIME_FIELDS = {
        "time", "pcTime", "deviceTime", "timestamp", "timeStamp",
        "device_time", "pc_time", "startTime", "endTime"
    }

    # 过滤字段（需要完全过滤掉的字段）
    FILTERED_FIELDS = {
        "frameTime", "label"
    }

    COMMON_FIELDS = {
        # 基础性能指标
        "fps", "device_cpu", "cpu", "all_cpus", "gpu", "mem",
        # 帧率质量指标（从IOS_EXCLUSIVE_FIELDS移动过来）
        "pf_jank", "pf_big_jank", "pf_stutter",
        # 内存相关指标（重要补充）
        "pssmem", "ussmem", "swapmem", "vmem", "heap", "native_heap",
        "code", "stack", "graphics", "private_other", "system", "native_pss",
        "egl", "gfx", "gl", "unknown", "dalvik_heap",
        # 网络指标（从IOS_EXCLUSIVE_FIELDS移动过来）
        "flow_up", "flow_down",
        # 系统指标（从IOS_EXCLUSIVE_FIELDS移动过来）
        "thread_count",
        # 进程信息
        "process_id"
    }

    def detect_platform_type(self, data: Dict[str, Any]) -> Optional[PlatformType]:
        """基于独有指标字段检测平台类型"""
        if "device_info" in data and "type" in data["device_info"]:
            device_type = data["device_info"]["type"]
            if device_type == "Android":
                logger.info("[性能数据处理] 通过device_info检测到Android平台")
                return PlatformType.ANDROID
            elif device_type == "iOS":
                logger.info("[性能数据处理] 通过device_info检测到iOS平台")
                return PlatformType.IOS

        return self._detect_by_exclusive_fields(data)

    def _detect_by_exclusive_fields(self, data: Dict[str, Any]) -> Optional[PlatformType]:
        """通过平台独有字段检测平台类型"""
        if "data" not in data or not data["data"]:
            logger.warning("[性能数据处理] GamePerf数据为空，无法检测平台类型")
            return None

        android_exclusive_count = 0
        ios_exclusive_count = 0

        for item in data["data"]:
            if isinstance(item, dict):
                item_keys = set(item.keys())
                android_exclusive_count += len(self.ANDROID_EXCLUSIVE_FIELDS & item_keys)
                ios_exclusive_count += len(self.IOS_EXCLUSIVE_FIELDS & item_keys)

        if android_exclusive_count > ios_exclusive_count:
            logger.info(f"[性能数据处理] 检测到Android平台 (独有字段数: {android_exclusive_count} vs {ios_exclusive_count})")
            return PlatformType.ANDROID
        elif ios_exclusive_count > android_exclusive_count:
            logger.info(f"[性能数据处理] 检测到iOS平台 (独有字段数: {ios_exclusive_count} vs {android_exclusive_count})")
            return PlatformType.IOS
        else:
            logger.warning(f"[性能数据处理] 无法确定平台类型 (Android独有字段: {android_exclusive_count}, iOS独有字段: {ios_exclusive_count})")
            return None

    def calculate_gameperf_data_avg(self, data: Dict[str, Any]) -> Mapping[str, Optional[float]]:
        """计算GamePerf数据的平均值"""
        try:
            logger.info("[性能数据处理] 开始计算GamePerf数据平均值")
            platform_type = self.detect_platform_type(data)
            if not platform_type:
                raise ValueError("无法确定平台类型")

            data_list = data.get("data", [])
            if not data_list:
                logger.warning("[性能数据处理] GamePerf数据为空")
                return {}

            numeric_metrics, list_metrics = self._get_all_metrics(data_list, platform_type)
            metrics = GamePerfMetrics(
                platform_type=platform_type.get_name(platform_type.value),
                metrics_values={field_name: [] for field_name in numeric_metrics},
                list_metrics_values={field_name: [] for field_name in list_metrics}
            )
            self._collect_metrics_data(data_list, metrics, numeric_metrics, list_metrics)
            result = metrics.calculate_averages()
            logger.info(f"[性能数据处理] GamePerf平均值计算完成 结果:{result}")
            return result
        except Exception as e:
            logger.error(f"[性能数据处理] 计算GamePerf平均值失败: {str(e)}")
            return {}

    def _get_all_metrics(self, data_list: List[dict], platform_type: PlatformType) -> Tuple[set, set]:
        """获取所有相关指标字段（排除时间字段和过滤字段）

        Returns:
            Tuple[set, set]: (数值字段集合, 列表字段集合)
        """
        numeric_metrics = set()
        list_metrics = set()

        if platform_type == PlatformType.ANDROID:
            platform_fields = self.ANDROID_EXCLUSIVE_FIELDS | self.COMMON_FIELDS
        else:
            platform_fields = self.IOS_EXCLUSIVE_FIELDS | self.COMMON_FIELDS

        for item in data_list:
            if isinstance(item, dict):
                for field in item.keys():
                    if field in platform_fields and field not in self.TIME_FIELDS and field not in self.FILTERED_FIELDS:
                        # 检查是否为数值字段
                        if isinstance(item[field], (int, float)):
                            numeric_metrics.add(field)
                        # 检查是否为列表字段（字符串格式）
                        elif field in LIST_FIELDS and isinstance(item[field], str):
                            list_metrics.add(field)

        logger.debug(f"[性能数据处理] 收集到的GamePerf数值字段（已排除时间字段和过滤字段）: {sorted(numeric_metrics)}")
        logger.debug(f"[性能数据处理] 收集到的GamePerf列表字段: {sorted(list_metrics)}")
        return numeric_metrics, list_metrics

    def _collect_metrics_data(self, data_list: List[dict], metrics: GamePerfMetrics, numeric_metrics: set, list_metrics: set):
        """收集指标数据"""
        for item in data_list:
            # 收集数值字段
            for field_name in numeric_metrics:
                if field_name in item:
                    value = item[field_name]
                    if isinstance(value, (int, float)):
                        metrics.add_metric_value(field_name, value)

            # 收集列表字段
            for field_name in list_metrics:
                if field_name in item:
                    value = item[field_name]
                    if isinstance(value, str):
                        parsed_values = self._parse_string_list(value)
                        if parsed_values:
                            metrics.add_list_metric_value(field_name, parsed_values)

    def _parse_string_list(self, value_str: str) -> Optional[List[float]]:
        """解析字符串格式的列表数据

        Args:
            value_str: 字符串格式的列表，如 "33.36 33.32 33.33"

        Returns:
            解析后的浮点数列表，解析失败返回None
        """
        try:
            if not value_str or value_str.strip() == "":
                return None

            # 按空格分割并转换为浮点数
            values = []
            for part in value_str.strip().split():
                try:
                    values.append(float(part))
                except ValueError:
                    continue

            return values if values else None
        except Exception as e:
            logger.debug(f"[性能数据处理] 解析字符串列表失败: {value_str}, 错误: {str(e)}")
            return None

class GamePerfDataProcessor:
    """GamePerf性能数据处理器主类

    专门处理GamePerf工具采集的性能数据，支持Android/iOS平台，
    提供数据加载、处理、保存等完整功能。
    """

    # 文件名配置
    FILE_NAMES = {
        "gameperf_raw": "gameperf_raw_perf_data.json",
        "gameperf_processed": "gameperf_processed_perf_data.json",
        "gameperf_avg": "gameperf_avg_perf_data.json"
    }

    def __init__(self):
        """初始化GamePerf数据处理器"""
        self.calculator = GamePerfMetricsCalculator()
        logger.info("[性能数据处理] GamePerf数据处理器初始化完成")

    def _fix_perf_data(self, data: List[dict]) -> List[dict]:
        """修复性能数据，统一时间字段格式"""
        if not data:
            return data

        logger.info("[性能数据处理] 开始修复GamePerf性能数据，使用全局时间对齐")

        # 统一转换时间戳为毫秒级并修复间隔（保持原有逻辑）
        base_pc_time_ms = convert_timestamp_to_digits(data[0].get("pc_time", 0), 13)
        base_device_time_ms = convert_timestamp_to_digits(data[0].get("device_time", 0), 13)

        # TODO：GamePerf数据Bug，等待GamePerf修复后移除
        for i, item in enumerate(data):
            if "pc_time" in item:
                item["pc_time"] = base_pc_time_ms + i * 1000  # 每秒递增1000毫秒
            if "device_time" in item:
                item["device_time"] = base_device_time_ms + i * 1000  # 每秒递增1000毫秒

        logger.info("[性能数据处理] GamePerf性能数据修复完成，时间字段格式已统一")
        return data





    def _save_gameperf_processed_data(self, data: List[dict], file_path: str) -> bool:
        """保存GamePerf处理后数据"""
        try:
            logger.info(f"[性能数据处理] 保存GamePerf处理后数据: {file_path}")

            # 基本数据检查
            if not isinstance(data, list) or not data:
                logger.error(f"[性能数据处理] GamePerf处理后数据格式无效")
                return False

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"[性能数据处理] GamePerf处理后数据保存成功，数据条数: {len(data)}")
            return True

        except Exception as e:
            logger.error(f"[性能数据处理] 保存GamePerf处理后数据失败: {str(e)}")
            return False

    def _save_gameperf_avg_data(self, avg_data: Mapping[str, Optional[float]], file_path: str) -> bool:
        """保存GamePerf平均值数据"""
        try:
            logger.info(f"[性能数据处理] 保存GamePerf平均值数据: {file_path}")

            # 基本数据检查
            if not avg_data:
                logger.error(f"[性能数据处理] GamePerf平均值数据为空")
                return False

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(dict(avg_data), f, ensure_ascii=False, indent=2)

            logger.info(f"[性能数据处理] GamePerf平均值数据保存成功，指标数量: {len(avg_data)}")
            return True

        except Exception as e:
            logger.error(f"[性能数据处理] 保存GamePerf平均值数据失败: {str(e)}")
            return False

    def _process_raw_data(self, raw_data: Dict[str, Any]) -> Optional[List[dict]]:
        """处理原始数据"""
        try:
            # 提取数据列表
            data_list = raw_data.get("data", [])
            if not data_list:
                logger.warning("[性能数据处理] GamePerf原始数据为空")
                return []

            # 修复时间字段
            processed_data = self._fix_perf_data(data_list.copy())

            logger.info(f"[性能数据处理] GamePerf数据处理完成，数据条数: {len(processed_data)}")
            return processed_data

        except Exception as e:
            logger.error(f"[性能数据处理] 处理GamePerf原始数据失败: {str(e)}")
            return None

    def handle_gameperf_perf_data_with_context(
        self,
        basic_perf_data: Dict[str, Any],
        cpu_profile_path: Optional[str],
        perf_account: dict,
        perf_device: Any,
        perf_app: Any,
        vqos_trace_configs: Optional[dict] = None,
        byteio_configs: Optional[list] = None
    ) -> Tuple[Optional[Dict[str, Any]], Optional[List[dict]], Optional[str], Optional[List[dict]]]:
        """处理GamePerf性能数据（带上下文信息）

        Returns:
            Tuple[基础性能数据, trace数据, CPU Profile路径, ByteIO数据]
        """
        trace_data = None
        byteio_data = None

        # 先处理原始数据以修复时间字段
        processed_data = self._process_raw_data(basic_perf_data)
        if not processed_data:
            logger.error("[性能数据处理] 处理GamePerf原始数据失败")
            return basic_perf_data, None, cpu_profile_path, None

        # 获取时间范围（使用修复后的数据）
        start_time, end_time = self.get_time_range(processed_data)

        # 构建保存路径
        if hasattr(perf_app, 'package_name'):  # Android应用
            package_name = perf_app.package_name
        else:  # iOS应用
            package_name = perf_app.bundle_id
        save_path = gameperf_manager.build_gameperf_save_path(perf_device.udid, package_name)

        # 处理trace数据并立即保存原始数据到与GamePerf相同的目录
        if vqos_trace_configs:
            trace_data = trace_data_manager.get_trace_metrics_data_with_raw(
                start_time=start_time,
                end_time=end_time,
                user_id=perf_account.get("account_uid"),
                device_id=perf_app.get_did(),
                vqos_trace_configs=vqos_trace_configs,
                save_raw_data_path=save_path  # 保存到与GamePerf相同的目录：data/{device_id}/{package_name}/
            )

        # 处理ByteIO数据并立即保存原始数据到与GamePerf相同的目录
        if byteio_configs:
            byteio_data = byteio_data_manager.get_byteio_metrics_data(
                device_id=perf_app.get_did(),
                start_time=start_time,
                end_time=end_time,
                byteio_configs=byteio_configs,
                save_raw_data_path=save_path  # 保存到与GamePerf相同的目录：data/{device_id}/{package_name}/
            )

        # 直接调用gameperf_data_processor处理数据，传递完整参数
        perf_data, perf_avg_data = self.handle_perf_data(
            basic_perf_data=basic_perf_data,
            trace_data=trace_data,
            cpu_profile_path=cpu_profile_path,
            byteio_data=byteio_data,
            save_local_path=save_path
        )

        # 返回4个值以保持接口一致性
        return perf_data, perf_avg_data, cpu_profile_path, byteio_data

    def handle_perf_data(
        self,
        basic_perf_data: Dict[str, Any],
        trace_data: Optional[List[dict]] = None,
        cpu_profile_path: Optional[str] = None,
        byteio_data: Optional[List[dict]] = None,
        save_local_path: Optional[str] = None
    ) -> tuple[List[dict], Mapping[str, Optional[float]]]:
        """处理GamePerf性能数据并计算平均值

        为了与DS数据处理器保持接口一致性而添加的方法。

        Args:
            basic_perf_data: GamePerf原始数据字典
            trace_data: Trace性能数据，可选
            cpu_profile_path: CPU Profile文件路径，可选
            byteio_data: ByteIO数据，可选
            save_local_path: 本地保存路径，可选

        Returns:
            tuple: (性能数据列表, 平均值数据)

        Note:
            当前实现中，trace_data、cpu_profile_path 和 byteio_data 参数
            暂时被忽略，因为 GamePerf 工具目前不处理这些类型的数据。
            这些参数的添加是为了与 DS 数据处理器保持接口一致性，
            便于在统一的调用接口中使用，并为将来的功能扩展预留空间。
        """
        try:
            logger.info("[性能数据处理] 开始处理GamePerf性能数据")

            # 处理原始数据
            processed_data = self._process_raw_data(basic_perf_data)
            if not processed_data:
                logger.error("[性能数据处理] 处理GamePerf原始数据失败")
                return [], {}

            # 根据要求，不进行数据合并，直接使用处理后的GamePerf数据
            enhanced_data = processed_data
            main_dir = os.getcwd()  # 获取测试用例主目录
            # 处理CPU Profile文件 - 保存到主目录
            if cpu_profile_path:
                profile_collector.handle_cpu_profile(cpu_profile_path, main_dir)
            if trace_data:
                # trace 处理后数据和平均值保存到与GamePerf相同的目录：data/{device_id}/{package_name}/（原始数据已保存）
                trace_data_manager.save_trace_processed_and_avg_data(trace_data, save_local_path)
            if byteio_data:
                # byteio 处理后数据和平均值保存到与GamePerf相同的目录：data/{device_id}/{package_name}/（原始数据已保存）
                byteio_data_manager.save_byteio_processed_and_avg_data(byteio_data, save_local_path)

            # 计算平均值（基于增强后的数据）
            avg_data = self._calculate_enhanced_avg_data(enhanced_data)

            # 如果提供了保存路径，保存处理后的数据
            if save_local_path:
                try:
                    # 确保目录存在
                    os.makedirs(save_local_path, exist_ok=True)

                    # 保存处理后数据
                    processed_file = os.path.join(save_local_path, self.FILE_NAMES["gameperf_processed"])
                    self._save_gameperf_processed_data(enhanced_data, processed_file)

                    # 保存平均值数据
                    avg_file = os.path.join(save_local_path, self.FILE_NAMES["gameperf_avg"])
                    self._save_gameperf_avg_data(avg_data, avg_file)
                    logger.info(f"[性能数据处理] GamePerf数据已保存到: {save_local_path}")
                except Exception as e:
                    logger.warning(f"[性能数据处理] 保存GamePerf数据失败: {str(e)}")

            logger.info(f"[性能数据处理] GamePerf性能数据处理完成，数据条数: {len(enhanced_data)}, 平均值指标数量: {len(avg_data)}")
            return enhanced_data, avg_data

        except Exception as e:
            logger.error(f"[性能数据处理] 处理GamePerf性能数据失败: {str(e)}")
            return [], {}







    def _calculate_enhanced_avg_data(
        self,
        enhanced_data: List[dict]
    ) -> Mapping[str, Optional[float]]:
        """计算GamePerf性能数据的平均值

        Args:
            enhanced_data: GamePerf性能数据

        Returns:
            Mapping[str, Optional[float]]: 平均值数据
        """
        try:
            if not enhanced_data:
                logger.warning("[性能数据处理] 增强数据为空")
                return {}

            # 检测平台类型
            platform_type = self.calculator.detect_platform_type({"data": enhanced_data})
            if not platform_type:
                raise ValueError("无法确定平台类型")

            # 获取所有指标字段（包括新增的trace和byteio字段）
            numeric_metrics, list_metrics = self.calculator._get_all_metrics(enhanced_data, platform_type)

            # 创建指标收集器
            metrics = GamePerfMetrics(
                platform_type=platform_type.get_name(platform_type.value),
                metrics_values={field_name: [] for field_name in numeric_metrics},
                list_metrics_values={field_name: [] for field_name in list_metrics}
            )

            # 收集增强数据中的所有指标
            self.calculator._collect_metrics_data(enhanced_data, metrics, numeric_metrics, list_metrics)

            # 计算平均值
            result = metrics.calculate_averages()

            logger.info(f"[性能数据处理] 增强平均值计算完成，指标数量: {len(result)}")
            return result

        except Exception as e:
            logger.error(f"[性能数据处理] 计算增强平均值失败: {str(e)}")
            return {}

    def get_time_range(self, perf_data: Dict[str, Any]) -> Tuple[Optional[int], Optional[int]]:
        """从GamePerf性能数据中获取时间范围

        Args:
            basic_perf_data: GamePerf原始数据

        Returns:
            Tuple[Optional[int], Optional[int]]: (开始时间, 结束时间) 或 (None, None) 如果无法获取时间信息
        """
        if not perf_data:
            raise IndexError("性能数据为空")

        start_time = int(perf_data[0]["pc_time"])
        end_time = int(perf_data[-1]["pc_time"])

        logger.info(f"[性能数据处理] 获取时间范围 开始:{start_time} 结束:{end_time}")
        return start_time, end_time

    def load_data_from_files(self, perf_device, perf_app) -> Dict[str, Any]:
        """从GamePerf文件中加载性能数据

        Args:
            perf_device: 性能测试设备对象
            perf_app: 性能测试应用对象

        Returns:
            Dict[str, Any]: GamePerf原始性能数据，如果加载失败则返回空字典
        """
        try:
            # 构建GamePerf数据文件路径
            device_id = perf_device.udid

            # 获取正确的包名信息，支持Android和iOS平台
            if hasattr(perf_app, 'package_name'):  # Android应用
                package_name = perf_app.package_name
            else:  # iOS应用
                package_name = perf_app.bundle_id

            # 构建基础路径：data/{device_id}/{package_name}/
            base_path = os.path.join("data", device_id, package_name)

            # 查找GamePerf原始数据文件，支持简化和多进程目录结构
            raw_data_file = None

            # 优先检查简化路径格式：data/{device_id}/{package_name}/gameperf_raw_perf_data.json
            simplified_file = os.path.join(base_path, "gameperf_raw_perf_data.json")
            if os.path.exists(simplified_file):
                raw_data_file = simplified_file
                logger.info(f"[GamePerf性能] 从简化路径加载数据: {raw_data_file}")
            else:
                # 如果简化路径不存在，尝试在进程子目录中查找
                pattern = os.path.join(base_path, "*", "gameperf_raw_perf_data.json")
                matching_files = glob.glob(pattern)

                if matching_files:
                    # 如果找到多个文件，选择最新的
                    raw_data_file = max(matching_files, key=os.path.getmtime)
                    logger.info(f"[GamePerf性能] 从多进程目录加载数据: {raw_data_file}")
                else:
                    logger.warning(f"[GamePerf性能] 未找到数据文件，检查路径: {base_path}")
                    return {}

            with open(raw_data_file, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
                logger.info(f"[GamePerf性能] 成功加载数据，数据条数: {len(raw_data.get('data', []))}")
                return raw_data

        except Exception as e:
            logger.error(f"[GamePerf性能] 加载数据文件失败: {str(e)}")
            return {}


# 创建全局GamePerf数据处理器实例
gameperf_data_processor = GamePerfDataProcessor()
