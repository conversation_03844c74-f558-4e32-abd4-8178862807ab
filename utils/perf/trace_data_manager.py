import json
import os
import time
import statistics
from typing import Any, Dict, List, Optional, Union, Mapping
from dataclasses import dataclass, field

from defines import LIVE_TRACE_NAMESPACE, RTC_TRACE_NAMESPACE
from utils.apis.trace_api import vqos_trace
from utils.common.log_utils import logger
from utils.common.time_utils import convert_timestamp_to_digits


@dataclass
class TraceMetrics:
    """Trace性能指标数据类"""
    metrics_values: Dict[str, List[float]] = field(default_factory=dict)

    def add_metric_value(self, metric_name: str, value: float):
        """添加指标值（排除值为0的数据）"""
        if metric_name not in self.metrics_values:
            self.metrics_values[metric_name] = []
        # 排除值为0的数据，只添加有效值（非0且非负）
        if isinstance(value, (int, float)) and value > 0:
            self.metrics_values[metric_name].append(float(value))

    def calculate_averages(self) -> Dict[str, Optional[float]]:
        """计算平均值"""
        averages = {}

        # 处理数值字段
        for metric_name, values in self.metrics_values.items():
            if values:
                try:
                    avg_value = statistics.mean(values)
                    averages[metric_name] = round(avg_value, 2)
                except statistics.StatisticsError:
                    averages[metric_name] = None
            else:
                averages[metric_name] = None

        return averages


class TraceMetricsCalculator:
    """Trace性能指标计算工具类"""

    # 时间相关字段（不应计算平均值的字段）
    TIME_FIELDS = {
        "time", "timestamp", "timeStamp", "device_time", "pc_time",
        "startTime", "endTime", "pcTime", "deviceTime"
    }

    # 元数据字段（不应计算平均值的字段）
    METADATA_FIELDS = {
        "event_key", "user_id", "device_id", "room_id", "rtc_channel_id",
        "direction", "media_type", "namespace"
    }

    def calculate_trace_avg_data(self, trace_data: List[Dict[str, Any]]) -> Mapping[str, Optional[float]]:
        """计算Trace数据的平均值"""
        try:
            logger.info("[性能数据处理] 开始计算Trace数据平均值")

            if not trace_data:
                logger.warning("[性能数据处理] Trace数据为空")
                return {}

            # 获取所有数值指标字段
            all_metrics = self._get_all_metrics(trace_data)

            # 初始化指标收集器
            metrics = TraceMetrics(
                metrics_values={field_name: [] for field_name in all_metrics}
            )

            # 收集指标数据
            self._collect_metrics_data(trace_data, metrics, all_metrics)

            # 计算平均值
            result = metrics.calculate_averages()
            logger.info(f"[性能数据处理] Trace平均值计算完成，指标数量: {len(result)}")
            return result

        except Exception as e:
            logger.error(f"[性能数据处理] 计算Trace平均值失败: {str(e)}")
            return {}

    def _get_all_metrics(self, trace_data: List[Dict[str, Any]]) -> set:
        """获取所有数值指标字段"""
        all_fields = set()

        # 收集所有字段
        for item in trace_data:
            if isinstance(item, dict):
                all_fields.update(item.keys())

        # 过滤掉时间字段和元数据字段
        metrics_fields = all_fields - self.TIME_FIELDS - self.METADATA_FIELDS

        # 只保留数值字段
        numeric_fields = set()
        for field_name in metrics_fields:
            for item in trace_data:
                if isinstance(item, dict) and field_name in item:
                    value = item[field_name]
                    if self._is_valid_number(value):
                        numeric_fields.add(field_name)
                        break

        logger.info(f"[性能数据处理] 识别到的Trace数值指标字段: {sorted(numeric_fields)}")
        return numeric_fields

    def _collect_metrics_data(self, trace_data: List[Dict[str, Any]], metrics: TraceMetrics, all_metrics: set):
        """收集指标数据"""
        for item in trace_data:
            if not isinstance(item, dict):
                continue

            for field_name in all_metrics:
                if field_name in item:
                    value = item[field_name]
                    if self._is_valid_number(value):
                        metrics.add_metric_value(field_name, float(value))

    def _is_valid_number(self, value: Any) -> bool:
        """检查值是否为有效数字"""
        if value is None:
            return False
        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False


class TraceDataManager:
    """Trace数据管理类"""

    # 文件命名规范
    FILE_NAMES = {
        "trace_raw": "trace_raw_data.json",
        "trace_processed": "trace_processed_data.json",
        "trace_avg": "trace_avg_data.json"
    }

    def __init__(self):
        self.trace_cache = {}
        self.metrics_calculator = TraceMetricsCalculator()

    def get_trace_data(self, namespace: str, start: int, end: int, query_string: str) -> Dict[str, Any]:
        """获取trace数据，支持分页获取所有数据"""
        start_time = convert_timestamp_to_digits(start, 13)
        end_time = convert_timestamp_to_digits(end, 13)
        cache_key = f"{namespace}_{start_time}_{end_time}_{query_string}"
        if cache_key in self.trace_cache:
            return self.trace_cache[cache_key]

        try:
            logger.debug(f"[性能数据处理] 开始获取trace数据 命名空间:{namespace} 开始时间戳:{start_time} 结束时间戳:{end_time} 查询参数:{query_string}")
            all_results = self._fetch_all_trace_data(namespace, start_time, end_time, query_string)

            result = {
                "Result": {
                    "List": all_results,
                    "TotalCount": len(all_results)
                }
            }

            self.trace_cache[cache_key] = result
            logger.info(f"[性能数据处理] Trace数据获取成功，共{len(all_results)}条")
            return result

        except Exception as e:
            logger.error(f"[性能数据处理] Trace数据获取失败: {e}")
            return {}

    def _fetch_all_trace_data(self, namespace: str, start: int, end: int, query_string: str) -> List[Dict[str, Any]]:
        """分页获取所有trace数据"""
        all_results = []
        offset = 0
        limit = 200
        total_count = 0

        while True:
            trace_data = vqos_trace.get_trace(
                namespace=namespace,
                start=start,
                end=end,
                offset=offset,
                limit=limit,
                query_filter=[],
                query_string=query_string
            )

            total_count = trace_data.get("Result", {}).get("Total", 0)
 
            # 获取当前页数据
            current_results = trace_data.get("Result", {}).get("List", [])
            if not current_results:
                break

            all_results.extend(current_results)
            # 判断是否还有更多数据
            if len(all_results) >= total_count or len(current_results) < limit:
                break

            offset += limit

        return all_results

    def get_rtc_channel_id(self, start_time: int, end_time: int, user_id: str, device_id: str) -> Optional[str]:
        """获取rtc_channel_id"""
        base_query = f"user_id: {user_id} AND rtc_channel_id: ?*"
        query_string = self._build_query_string(base_query, user_id, device_id)

        live_trace_data = self.get_trace_data(LIVE_TRACE_NAMESPACE, start_time, end_time, query_string)
        live_result_list = live_trace_data.get("Result", {}).get("List", [])
        channel_id = live_result_list[0].get("rtc_channel_id") if live_result_list else None

        log_message = f"成功获取rtc_channel_id: {channel_id}" if channel_id else "未获取到rtc_channel_id"
        logger.info(f"[性能数据处理] {log_message}")
        return channel_id

    def get_trace_metrics_data(
        self,
        start_time: int,
        end_time: int,
        user_id: str,
        device_id: str,
        vqos_trace_configs: Union[Dict[str, Any], List[Dict[str, Any]]],
    ) -> Optional[List[Dict[str, Any]]]:
        """获取trace性能指标数据，自动优化重复请求"""
        logger.info(f"[性能数据处理] 开始获取性能指标数据 用户:{user_id}")

        configs = vqos_trace_configs if isinstance(vqos_trace_configs, list) else [vqos_trace_configs]
        all_results = []

        # 按核心筛选参数对配置进行分组，避免重复的API调用
        config_groups = {}

        for i, config in enumerate(configs):
            query_conditions = config.get("query_conditions", {})
            namespace = query_conditions.get("namespace")
            base_query = query_conditions.get("query_string", "")

            # 生成分组键（核心筛选参数）
            group_key = (
                namespace,
                start_time,
                end_time,
                base_query,
                user_id,
                device_id
            )

            if group_key not in config_groups:
                config_groups[group_key] = []

            config_groups[group_key].append({
                'index': i,
                'config': config,
                'namespace': namespace,
                'base_query': base_query,
                'metric_key': config.get('metric_key'),
                'field_name': query_conditions.get('field_name', None),
                'required_fields': query_conditions.get('required_fields', None)
            })

        # 对每个分组只调用一次API，然后处理组内所有配置
        for group_key, group_configs in config_groups.items():
            group_namespace, group_start_time, group_end_time, group_base_query, group_user_id, group_device_id = group_key

            logger.info(f"[性能数据处理] 处理配置组 namespace:{group_namespace} 包含{len(group_configs)}个配置")

            # 处理该组的配置
            group_results = self._process_config_groups(
                group_configs, group_start_time, group_end_time,
                group_user_id, group_device_id, group_namespace, group_base_query
            )
            all_results.extend(group_results)

        # 最终合并所有组的数据，按时间戳聚合
        if all_results:
            final_merged_data = self._merge_results_by_timestamp(all_results)
            logger.info(f"[性能数据处理] 最终合并完成，从{len(all_results)}条记录合并为{len(final_merged_data)}条记录")
            return final_merged_data

        return all_results

    def get_trace_metrics_data_with_raw(
        self,
        start_time: int,
        end_time: int,
        user_id: str,
        device_id: str,
        vqos_trace_configs: Union[Dict[str, Any], List[Dict[str, Any]]],
        save_raw_data_path: Optional[str] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """获取trace性能指标数据，同时保存原始数据"""
        logger.info(f"[性能数据处理] 开始获取性能指标数据（含原始数据保存） 用户:{user_id}")

        configs = vqos_trace_configs if isinstance(vqos_trace_configs, list) else [vqos_trace_configs]
        all_results = []
        raw_data_saved = False

        # 按核心筛选参数对配置进行分组，避免重复的API调用
        config_groups = {}

        for i, config in enumerate(configs):
            query_conditions = config.get("query_conditions", {})
            namespace = query_conditions.get("namespace")
            base_query = query_conditions.get("query_string", "")

            # 生成分组键（核心筛选参数）
            group_key = (
                namespace,
                start_time,
                end_time,
                base_query,
                user_id,
                device_id
            )

            if group_key not in config_groups:
                config_groups[group_key] = []

            config_groups[group_key].append({
                'index': i,
                'config': config,
                'namespace': namespace,
                'base_query': base_query,
                'metric_key': config.get('metric_key'),
                'field_name': query_conditions.get('field_name', None),
                'required_fields': query_conditions.get('required_fields', None)
            })

        # 对每个分组只调用一次API，然后处理组内所有配置
        for group_key, group_configs in config_groups.items():
            group_namespace, group_start_time, group_end_time, group_base_query, group_user_id, group_device_id = group_key

            logger.info(f"[性能数据处理] 处理配置组 namespace:{group_namespace} 包含{len(group_configs)}个配置")

            # 处理该组的配置，同时获取原始数据
            group_results, raw_trace_data = self._process_config_groups_with_raw(
                group_configs, group_start_time, group_end_time,
                group_user_id, group_device_id, group_namespace, group_base_query
            )
            all_results.extend(group_results)

            # 保存原始数据（只保存一次）
            if save_raw_data_path and raw_trace_data and not raw_data_saved:
                try:
                    self.save_trace_raw_data(raw_trace_data, save_raw_data_path)
                    raw_data_saved = True
                    logger.info(f"[性能数据处理] Trace原始数据已保存")
                except Exception as e:
                    logger.error(f"[性能数据处理] 保存Trace原始数据失败: {str(e)}")

        # 最终合并所有组的数据，按时间戳聚合
        if all_results:
            final_merged_data = self._merge_results_by_timestamp(all_results)
            logger.info(f"[性能数据处理] 最终合并完成，从{len(all_results)}条记录合并为{len(final_merged_data)}条记录")
            return final_merged_data

        return all_results

    def _merge_results_by_timestamp(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按时间戳合并结果数据"""
        time_based_data = {}  # {timestamp: {metric_key: value, ...}}

        for item in results:
            if not isinstance(item, dict) or "time" not in item:
                continue

            timestamp = item["time"]
            if timestamp not in time_based_data:
                time_based_data[timestamp] = {"time": timestamp}

            # 合并除时间外的所有字段
            for key, value in item.items():
                if key != "time":
                    time_based_data[timestamp][key] = value

        # 转换为列表并按时间戳排序
        merged_results = list(time_based_data.values())
        merged_results.sort(key=lambda x: x["time"])
        return merged_results

    def _process_config_groups_with_raw(self, group_configs: List[Dict[str, Any]], start_time: int, end_time: int,
                                        user_id: str, device_id: str, namespace: str, base_query: str) -> tuple[List[Dict[str, Any]], Optional[Dict[str, Any]]]:
        """处理指定配置组的trace数据，支持重试机制，同时返回原始数据"""
        results = []
        raw_trace_data = None

        for attempt in range(3):
            logger.info(f"[性能数据处理] 第{attempt + 1}次尝试获取{namespace}数据")
            if attempt > 0:
                time.sleep(60)
                # 重试时清除相关缓存
                cache_keys_to_remove = [key for key in self.trace_cache.keys() if namespace in key]
                for cache_key in cache_keys_to_remove:
                    del self.trace_cache[cache_key]
                logger.debug(f"[性能数据处理] 清除了{len(cache_keys_to_remove)}个缓存项")

            # RTC数据需要先获取channel_id
            rtc_channel_id = None
            if namespace == RTC_TRACE_NAMESPACE:
                rtc_channel_id = self.get_rtc_channel_id(start_time, end_time, user_id, device_id)
                if not rtc_channel_id:
                    logger.warning(f"[性能数据处理] 第{attempt + 1}次尝试获取rtc_channel_id失败")
                    continue

            # 构建查询字符串
            final_base_query = base_query
            if namespace == RTC_TRACE_NAMESPACE and rtc_channel_id:
                final_base_query = f"room_id: {rtc_channel_id} AND {base_query}"

            query_string = self._build_query_string(final_base_query, user_id, device_id)

            # 只调用一次API获取原始数据
            trace_data = self.get_trace_data(namespace, start_time, end_time, query_string)

            if not trace_data:
                logger.warning(f"[性能数据处理] {namespace}数据查询失败")
                continue

            # 保存原始数据引用
            raw_trace_data = trace_data

            # 获取原始数据列表
            raw_data_list = trace_data.get("Result", {}).get("List", [])

            # 按时间戳合并所有配置的数据
            time_based_data = {}  # {timestamp: {metric_key: value, ...}}
            group_has_results = False

            for config_info in group_configs:
                metric_key = config_info['metric_key']
                field_name = config_info['field_name']
                required_fields = config_info['required_fields']
                config_has_data = False

                if field_name is not None:
                    # 使用 field_name 参数处理数据
                    processed_data = self.get_field_values(
                        data_list=raw_data_list,
                        field_name=field_name,
                        required_fields=required_fields
                    )

                    for i, item in enumerate(processed_data):
                        if not isinstance(item, dict):
                            continue

                        # 获取时间戳
                        timestamp = None
                        if i < len(raw_data_list) and isinstance(raw_data_list[i], dict) and "time" in raw_data_list[i]:
                            timestamp = int(raw_data_list[i]["time"])

                        if timestamp is not None:
                            if timestamp not in time_based_data:
                                time_based_data[timestamp] = {"time": timestamp}

                            # 添加处理后的字段值
                            for key, value in item.items():
                                time_based_data[timestamp][key] = value
                            config_has_data = True
                else:
                    # 使用原有的 metric_key 处理方式
                    for trace in raw_data_list:
                        if metric_key not in trace or "time" not in trace:
                            continue

                        timestamp = int(trace["time"])
                        if timestamp not in time_based_data:
                            time_based_data[timestamp] = {"time": timestamp}

                        time_based_data[timestamp][metric_key] = float(trace[metric_key])
                        config_has_data = True

                if config_has_data:
                    group_has_results = True
                    field_info = f"field_name:{field_name}" if field_name else f"metric_key:{metric_key}"
                    logger.info(f"[性能数据处理] 配置{config_info['index']} {field_info} 处理成功")
                else:
                    field_info = f"field_name:{field_name}" if field_name else f"metric_key:{metric_key}"
                    logger.warning(f"[性能数据处理] 配置{config_info['index']} {field_info} 未获取到有效数据")

            # 将合并后的数据转换为列表，按时间戳排序
            if time_based_data:
                merged_results = list(time_based_data.values())
                merged_results.sort(key=lambda x: x["time"])
                results.extend(merged_results)
                logger.info(f"[性能数据处理] 合并后获取{len(merged_results)}条记录，包含{len(group_configs)}个指标")

            if group_has_results:
                logger.info(f"[性能数据处理] 第{attempt + 1}次尝试获取{namespace}数据成功")
                break
            else:
                logger.warning(f"[性能数据处理] 第{attempt + 1}次尝试获取{namespace}数据失败")

        if not results:
            logger.warning(f"[性能数据处理] 获取{namespace}数据失败")

        return results, raw_trace_data

    def _process_config_groups(self, group_configs: List[Dict[str, Any]], start_time: int, end_time: int,
                                        user_id: str, device_id: str, namespace: str, base_query: str) -> List[Dict[str, Any]]:
        """处理指定配置组的trace数据，支持重试机制"""
        results = []

        for attempt in range(3):
            logger.info(f"[性能数据处理] 第{attempt + 1}次尝试获取{namespace}数据")
            if attempt > 0:
                time.sleep(60)
                # 重试时清除相关缓存
                cache_keys_to_remove = [key for key in self.trace_cache.keys() if namespace in key]
                for cache_key in cache_keys_to_remove:
                    del self.trace_cache[cache_key]
                logger.debug(f"[性能数据处理] 清除了{len(cache_keys_to_remove)}个缓存项")

            # RTC数据需要先获取channel_id
            rtc_channel_id = None
            if namespace == RTC_TRACE_NAMESPACE:
                rtc_channel_id = self.get_rtc_channel_id(start_time, end_time, user_id, device_id)
                if not rtc_channel_id:
                    logger.warning(f"[性能数据处理] 第{attempt + 1}次尝试获取rtc_channel_id失败")
                    continue

            # 构建查询字符串
            final_base_query = base_query
            if namespace == RTC_TRACE_NAMESPACE and rtc_channel_id:
                final_base_query = f"room_id: {rtc_channel_id} AND {base_query}"

            query_string = self._build_query_string(final_base_query, user_id, device_id)

            # 只调用一次API获取原始数据
            trace_data = self.get_trace_data(namespace, start_time, end_time, query_string)

            if not trace_data:
                logger.warning(f"[性能数据处理] {namespace}数据查询失败")
                continue

            # 获取原始数据列表
            raw_data_list = trace_data.get("Result", {}).get("List", [])

            # 按时间戳合并所有配置的数据
            time_based_data = {}  # {timestamp: {metric_key: value, ...}}
            group_has_results = False

            for config_info in group_configs:
                metric_key = config_info['metric_key']
                field_name = config_info['field_name']
                required_fields = config_info['required_fields']
                config_has_data = False

                if field_name is not None:
                    # 使用 field_name 参数处理数据
                    processed_data = self.get_field_values(
                        data_list=raw_data_list,
                        field_name=field_name,
                        required_fields=required_fields
                    )

                    for i, item in enumerate(processed_data):
                        if not isinstance(item, dict):
                            continue

                        # 获取时间戳
                        timestamp = None
                        if i < len(raw_data_list) and isinstance(raw_data_list[i], dict) and "time" in raw_data_list[i]:
                            timestamp = int(raw_data_list[i]["time"])

                        if timestamp is not None:
                            if timestamp not in time_based_data:
                                time_based_data[timestamp] = {"time": timestamp}

                            # 添加处理后的字段值
                            for key, value in item.items():
                                time_based_data[timestamp][key] = value
                            config_has_data = True
                else:
                    # 使用原有的 metric_key 处理方式
                    for trace in raw_data_list:
                        if metric_key not in trace or "time" not in trace:
                            continue

                        timestamp = int(trace["time"])
                        if timestamp not in time_based_data:
                            time_based_data[timestamp] = {"time": timestamp}

                        time_based_data[timestamp][metric_key] = float(trace[metric_key])
                        config_has_data = True

                if config_has_data:
                    group_has_results = True
                    field_info = f"field_name:{field_name}" if field_name else f"metric_key:{metric_key}"
                    logger.info(f"[性能数据处理] 配置{config_info['index']} {field_info} 处理成功")
                else:
                    field_info = f"field_name:{field_name}" if field_name else f"metric_key:{metric_key}"
                    logger.warning(f"[性能数据处理] 配置{config_info['index']} {field_info} 未获取到有效数据")

            # 将合并后的数据转换为列表，按时间戳排序
            if time_based_data:
                merged_results = list(time_based_data.values())
                merged_results.sort(key=lambda x: x["time"])
                results.extend(merged_results)
                logger.info(f"[性能数据处理] 合并后获取{len(merged_results)}条记录，包含{len(group_configs)}个指标")

            if group_has_results:
                logger.info(f"[性能数据处理] 第{attempt + 1}次尝试获取{namespace}数据成功")
                break
            else:
                logger.warning(f"[性能数据处理] 第{attempt + 1}次尝试获取{namespace}数据失败")

        if not results:
            logger.warning(f"[性能数据处理] 获取{namespace}数据失败")

        return results

    def _build_query_string(self, base_query: str, user_id: str, device_id: str) -> str:
        """构建trace查询字符串，添加user_id和device_id条件"""
        query_parts = []

        # 添加基础查询条件
        if base_query and base_query.strip():
            query_parts.append(base_query.strip())

        # 添加用户ID条件
        if user_id:
            query_parts.append(f"user_id: {user_id}")

        # 添加设备ID条件
        if device_id:
            query_parts.append(f"device_id: {device_id}")

        query_string = " AND ".join(query_parts)
        logger.debug(f"[性能数据处理] 构建的查询字符串: {query_string}")
        return query_string

    def get_field_values(
        self,
        data_list: List[Any],
        field_name: Optional[str] = None,
        required_fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """获取指定字段的值列表，支持通过点号访问嵌套字段

        注意：不再使用required_fields进行严格检查，每个字段独立处理
        """
        def get_nested_value(data: Any, keys: List[str]) -> Any:
            if not keys:
                return data

            current_key = keys[0]

            if isinstance(data, str):
                try:
                    data = json.loads(data)
                except json.JSONDecodeError:
                    return None

            if isinstance(data, dict):
                if current_key in data:
                    return get_nested_value(data[current_key], keys[1:])

            return None

        result = []
        logger.info(f"[数据处理] 开始处理字段提取，输入数据条数: {len(data_list)}, 字段名: {field_name}")

        for i, data in enumerate(data_list):
            if not isinstance(data, dict):
                logger.debug(f"[数据处理] 跳过非字典数据，索引: {i}")
                continue

            if field_name is None:
                result.append(data)
                continue

            item = {}
            field_path = field_name.split('.')
            logger.debug(f"[数据处理] 处理数据索引: {i}, 字段路径: {field_path}")

            value = get_nested_value(data, field_path)
            logger.debug(f"[数据处理] 提取到的值: {value} (类型: {type(value)})")

            result_key = field_path[-1]
            item[result_key] = value

            # 只要能提取到字段（即使值为None或0），都添加到结果中
            result.append(item)

        logger.info(f"[数据处理] 获取字段值列表，共 {len(result)} 条记录")
        if result:
            non_none_count = sum(1 for item in result if any(v is not None for v in item.values()))
            logger.info(f"[数据处理] 其中非None值记录数: {non_none_count}")
        return result

    def _process_trace_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理Trace原始数据，确保格式一致，并合并相同时间戳的数据

        Args:
            raw_data: 原始Trace数据

        Returns:
            List[Dict[str, Any]]: 处理后的Trace数据（时间序列格式，相同时间戳已合并）
        """
        try:
            if not raw_data:
                logger.warning("[性能数据处理] Trace原始数据为空")
                return []

            # 第一步：确保每个数据项都有time字段
            timestamped_data = []

            for item in raw_data:
                if isinstance(item, dict):
                    new_item = item.copy()

                    # 检查是否已经有time字段
                    if "time" not in new_item:
                        # 尝试从其他时间字段获取
                        time_value = None
                        for time_field in ["timestamp", "datetime", "server_time", "local_time_ms"]:
                            if time_field in new_item:
                                time_value = new_item[time_field]
                                break

                        if time_value is not None:
                            new_item["time"] = time_value
                        else:
                            # 最后兜底，使用当前时间戳
                            import time
                            new_item["time"] = int(time.time())
                            logger.warning(f"[性能数据处理] Trace数据缺少时间戳字段，使用当前时间: {new_item['time']}")

                    timestamped_data.append(new_item)
                else:
                    timestamped_data.append(item)

            # 第二步：按时间戳合并数据
            merged_data = {}

            for item in timestamped_data:
                if isinstance(item, dict) and "time" in item:
                    time_key = item["time"]

                    if time_key not in merged_data:
                        merged_data[time_key] = {"time": time_key}

                    # 合并除time字段外的所有字段
                    for key, value in item.items():
                        if key != "time":
                            # 如果字段已存在且值不同，优先保留非None和非负值
                            if key in merged_data[time_key]:
                                existing_value = merged_data[time_key][key]
                                # 优先保留有效值（非None且非负）
                                if value is not None and (existing_value is None or (isinstance(value, (int, float)) and value >= 0 and (existing_value is None or existing_value < 0))):
                                    merged_data[time_key][key] = value
                            else:
                                merged_data[time_key][key] = value

            # 第三步：转换为列表并按时间排序
            processed_data = list(merged_data.values())
            processed_data.sort(key=lambda x: x.get("time", 0))

            logger.info(f"[性能数据处理] Trace数据处理完成，原始数据条数: {len(timestamped_data)}，合并后数据条数: {len(processed_data)}")
            return processed_data

        except Exception as e:
            logger.error(f"[性能数据处理] 处理Trace数据失败: {str(e)}")
            return raw_data

    def calculate_trace_avg_data(self, trace_data: List[Dict[str, Any]]) -> Mapping[str, Optional[float]]:
        """计算Trace数据的平均值"""
        return self.metrics_calculator.calculate_trace_avg_data(trace_data)

    def save_trace_avg_data(self, trace_data: List[Dict[str, Any]], save_local_path: str) -> bool:
        """计算并保存Trace平均值数据"""
        try:
            if not trace_data:
                logger.warning("[性能数据处理] Trace数据为空，跳过平均值计算")
                return False

            # 计算平均值
            avg_data = self.calculate_trace_avg_data(trace_data)
            if not avg_data:
                logger.warning("[性能数据处理] Trace平均值计算结果为空")
                return False

            # 确保目录存在
            safe_path = os.path.realpath(save_local_path)
            os.makedirs(safe_path, exist_ok=True)

            # 保存平均值数据
            avg_file_path = os.path.join(safe_path, self.FILE_NAMES["trace_avg"])
            with open(avg_file_path, "w", encoding="utf-8") as f:
                json.dump(dict(avg_data), f, ensure_ascii=False, indent=2)

            logger.info(f"[性能数据处理] Trace平均值数据保存成功 路径:{avg_file_path} 指标数量:{len(avg_data)}")
            return True

        except Exception as e:
            logger.error(f"[性能数据处理] 保存Trace平均值数据失败: {str(e)}")
            return False

    def save_trace_raw_data(self, raw_trace_data: Dict[str, Any], save_local_path: str) -> Optional[str]:
        """保存Trace原始数据，保持完整的原始数据结构"""
        if not raw_trace_data:
            logger.warning("[性能数据处理] 没有Trace数据需要保存")
            return None

        try:
            safe_path = os.path.realpath(save_local_path)
            os.makedirs(safe_path, exist_ok=True)

            # 获取原始数据列表，保持完整结构
            raw_data_list = raw_trace_data.get("Result", {}).get("List", [])
            if not raw_data_list:
                logger.warning("[性能数据处理] Trace原始数据列表为空")
                return None

            # 保存原始trace数据 (raw) - 保持完整的原始数据结构
            trace_raw_path = os.path.join(safe_path, self.FILE_NAMES["trace_raw"])
            with open(trace_raw_path, "w", encoding="utf-8") as f:
                json.dump(raw_data_list, f, ensure_ascii=False, indent=2)
            logger.info(f"[性能数据处理] Trace原始数据保存成功 路径:{trace_raw_path} 数据条数:{len(raw_data_list)}")

            return trace_raw_path

        except Exception as e:
            logger.error(f"[性能数据处理] Trace原始数据保存失败: {str(e)}")
            return None

    def save_trace_processed_and_avg_data(self, data: List[Dict[str, Any]], save_local_path: str) -> bool:
        """保存Trace处理后数据和平均值数据"""
        try:
            if not data:
                logger.warning("[性能数据处理] 没有Trace数据需要处理")
                return False

            safe_path = os.path.realpath(save_local_path)
            os.makedirs(safe_path, exist_ok=True)

            # 处理数据并保存 (processed)
            processed_data = self._process_trace_data(data)
            trace_processed_path = os.path.join(safe_path, self.FILE_NAMES["trace_processed"])
            with open(trace_processed_path, "w", encoding="utf-8") as f:
                json.dump(processed_data, f, ensure_ascii=False, indent=2)
            logger.info(f"[性能数据处理] Trace处理后数据保存成功 路径:{trace_processed_path}")

            # 计算并保存平均值数据 (avg)
            self.save_trace_avg_data(processed_data, safe_path)

            return True

        except Exception as e:
            logger.error(f"[性能数据处理] 保存Trace处理后数据失败: {str(e)}")
            return False


# 创建全局Trace数据管理器实例
trace_data_manager = TraceDataManager()