"""CPU Profile 收集器 - 负责采集和文件处理"""
from bytedance.profile_toolkit.tool.profiler.cpu_profiler import CPUProfileFactory, Platform
import os
import shutil
import time
from utils.common.log_utils import logger


class ProfileCollector:
    """CPU Profile 收集器"""

    def __init__(self):
        """初始化收集器"""
        self.profiler = None

    def init_profiler(self, platform: Platform = Platform.IOS):
        """初始化 profiler"""
        logger.info(f"[Profile] 开始初始化profiler 平台:{platform.name}")
        self.profiler = CPUProfileFactory.create_profiler(platform=platform)
        logger.info("[Profile] profiler初始化完成")

    def use_device(self, device_id: str) -> None:
        """使用指定设备"""
        if not self.profiler:
            raise ValueError("请先调用 init_profiler 初始化")
        if not device_id:
            raise ValueError("device_id 不能为空")
        self.profiler.use_device(device_id=device_id)

    def sync_record(self, duration_sec: int, target: str) -> str:
        """同步记录"""
        if not self.profiler:
            raise ValueError("请先调用 init_profiler 初始化")

        logger.info("[Profile] 开始同步记录")
        trace_path = self.profiler.sync_record(duration_sec=duration_sec, target=target)

        if not trace_path or not os.path.exists(trace_path):
            raise FileNotFoundError("trace 文件不存在")

        logger.info(f"[Profile] 同步记录完成，trace路径: {trace_path}")
        return trace_path

    def async_record(self, duration_sec: int, target: str) -> str:
        """异步记录"""
        if not self.profiler:
            raise ValueError("请先调用 init_profiler 初始化")

        logger.info(f"[Profile] 开始异步记录 目标进程:{target} 时长:{duration_sec}s")
        async_pid = self.profiler.async_record(duration_sec=duration_sec, target=target)

        for i in range(duration_sec // 3):
            logger.info(f"[Profile] 异步记录进度: {i * 3}s/{duration_sec}s")
            time.sleep(3)

        trace_path = self.profiler.async_stop(async_pid)
        if not trace_path or not os.path.exists(trace_path):
            logger.error("[Profile] trace文件不存在")
            raise FileNotFoundError("trace 文件不存在")

        logger.info(f"[Profile] 异步记录完成 trace路径:{trace_path}")
        return trace_path

    def handle_cpu_profile(self, cpu_profile_path: str, save_local_path: str):
        """处理CPU Profile文件

        将CPU Profile文件复制到指定目录并重命名为标准格式

        Args:
            cpu_profile_path: CPU Profile文件路径
            save_local_path: 本地保存路径
        """
        try:
            if not cpu_profile_path or not os.path.exists(cpu_profile_path):
                logger.warning(f"[性能数据处理] CPU Profile文件不存在: {cpu_profile_path}")
                return

            # 确保保存目录存在
            os.makedirs(save_local_path, exist_ok=True)

            # 复制CPU Profile文件到保存目录并重命名为标准格式
            dest_path = os.path.join(save_local_path, "cpu_profile.data")
            shutil.copy2(cpu_profile_path, dest_path)

            logger.info(f"[性能数据处理] CPU Profile文件已保存: {dest_path}")

        except Exception as e:
            logger.error(f"[性能数据处理] 处理CPU Profile文件失败: {str(e)}")


profile_collector = ProfileCollector()


if __name__ == "__main__":
    collector = ProfileCollector()
    device_id = '00008030-001558E91A88802E'
    duration_sec = 10
    target = 'TikTok'
    collector.init_profiler(Platform.IOS)
    collector.use_device(device_id=device_id)
    collector.async_record(duration_sec=duration_sec, target=target)