from .feed_panel_ios import *
import time

class IosAction:
    def __init__(self, app):
        self.app = app
        self.feed_panel = FeedPanel(root=self.app)
    
    def set_feed(self, vids):
        if self.feed_panel["feed_panel_dropdown"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self.feed_panel["feed_panel_dropdown"].click()
            time.sleep(1)
        if self.feed_panel["manage_feed"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self.feed_panel["manage_feed"].click()
            time.sleep(1)
        if self.feed_panel["debug_menu"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self.feed_panel["debug_menu"].click()
            time.sleep(1)
        
        if self.feed_panel["import"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self.feed_panel["import"].click()
            time.sleep(1)
        
        if self.feed_panel["debug_video_input"].wait_for_existing(timeout=3, raise_error=False):
            self.feed_panel["debug_video_input"].click()
        time.sleep(3)
        self.feed_panel["input_vid"].click()
        self.feed_panel["input_vid"].input(vids)
        time.sleep(3)
        self.feed_panel["import_vid"].click()
        time.sleep(3)
        self.app.restart()
        self.feed_panel.swipe_up()
    
    def click_captions_button_in_long_press_panel(self):
        if self.feed_panel["captions_icon"].wait_for_existing(timeout=5, raise_error=False):
            self.feed_panel["captions_icon"].click()
        else:
            self.feed_panel.drag_combine_band()
            self.feed_panel["captions_icon"].click()
    
    def click_translation_button_in_long_press_panel(self):
        if self.feed_panel["Translation"].wait_for_existing(timeout=5, raise_error=False):
            self.feed_panel["Translation"].click()
        else:
            self.feed_panel.drag_combine_band()
            self.feed_panel["Translation"].click()
    
    def get_translate_into_lang(self,awemetype = "video"):
        self.feed_panel.open_panel_long_click()
        time.sleep(0.5)
        if awemetype!= "video":
            self.click_translation_button_in_long_press_panel()
            time.sleep(0.5)
            if self.feed_panel["translate_into_content_photo"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                res = self.feed_panel["translate_into_content_photo"].text
            else:
                res = "None"
            self.feed_panel.close_caption_panel()
            return res
        else:
            self.click_captions_button_in_long_press_panel()
            time.sleep(0.5)
            if self.feed_panel["translate_into_content_video"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                res = self.feed_panel["translate_into_content_video"].text
            else:
                res = "None"
            self.feed_panel.close_caption_panel()
            return res

    def set_translate_into_lang(self,string,awemetype = "video",swipe_method = "slowly"):
        self.feed_panel.open_panel_long_click()
        time.sleep(0.5)
        if awemetype != "video":
            self.click_translation_button_in_long_press_panel()
        else:
            self.click_captions_button_in_long_press_panel()
        time.sleep(0.5)
        if self.feed_panel["translate_into"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self.feed_panel["translate_into"].click()
        time.sleep(1)
        for _ in range(5):
            if self.feed_panel[string].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                self.feed_panel[string].click()
                break
            else:
                if swipe_method == "slowly":
                    self.feed_panel.slowly_swipe()
                elif swipe_method == "quickly":
                    self.feed_panel.quickly_swipe()
        time.sleep(1)
        if self.feed_panel["translate_into_done"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self.feed_panel["translate_into_done"].click()
        time.sleep(1)
        self.feed_panel.close_caption_panel()    
        