from common.tiktok.android.panels.vod import AndroidVodPanel
import time

from shoots_android.control import *
from uibase.upath import id_, UPath, text_, visible_, type_, href_, class_, fragment_

from utils.common.log_utils import logger

class FeedPanel(AndroidVodPanel):
    """
    继承自 AndroidVodPanel 的 Feed 面板类
    """

    def get_locators(self):
        # 获取父类的定位器
        locators = super().get_locators()
        # 添加 FeedPanel 特有的定位器
        locators.update({
            #combine panel
            "show_captions": {"type": Control,
                                    'path': UPath(id_ == "power_list") / 0 / UPath(id_ == "title_tv", depth=5)},
            "caption_panel_close": {"path": UPath(id_ == "nav_end") / UPath(type_ == "TuxIconView", depth=5)},
            "always_translate_posts": {"path": UPath(id_ == "power_list") / 2 / UPath(id_ == "title_tv", depth=5)},
            "do_not_translate": {"path": UPath(~text_ == "Do not translate|不翻译")},
            "dnt_done": {"path": UPath(~text_ == "Done|完成")},
            "dnt_english": {"path": UPath(text_ == "English")},
            "dnt_content": {"path": UPath(id_ == "power_list") / 3 / UPath(id_ == "label_tv", depth=5)},
            "always_translate_posts_photo": {"path": UPath(type_ == "TuxSwitch")},
            "Translation": {"path": UPath(~text_ == "Translation|翻译")},
            "see_translation_detail": {"path": UPath(~text_ == "See translation|查看翻译")},
            "biz_top_container": {"path": UPath(id_ == "biz_top_container", visible_ == True)},
            "interact_right_area": {"path": UPath(id_ == "interact_right_area", visible_ == True)},
            "translate_into_content_video": {"path": UPath(id_ == "power_list") / 4 / UPath(id_ == "label_tv", depth=5)},
            "translate_into_content_photo": {"path": UPath(id_ == "power_list") / 2 / UPath(id_ == "label_tv", depth=5)},
            "detailExit": {"path": UPath(id_ == "back_iv")},
            "profile_3lines": {"path": UPath(id_ == "nav_end") / 1},
            "photomode_tag": {"path": UPath(id_ == "photo_mode_tag")},
            "phototitle":{"path": UPath(id_ == "title", type_ == "TuxTextView", visible_ == True)},
            "translate_more": {"path": UPath(desc_ == "Translate more") / UPath(id_ == "icon_iv", visible_ == True, depth=5)},
            "text_on_posts": {"path": UPath(type_ == "TuxSwitch")},
            "exit_translate_more": {"path": UPath(type_ == "TuxIconView", visible_ == True)},
            "translate_into": {"path": UPath(~text_ == "Translate into|翻译为")},
            "caption_panel_root_view": {"path": UPath(id_ == "caption_panel_root_view")},
            "translation_feedback_panel": {"path": UPath(id_ == "translation_feedback_container")},
            "translation_feedback_thumb_up":{"path": UPath(id_ == "btn_thumb_up_container")},
            "translation_feedback_thumb_down": {"path": UPath(id_ == "btn_thumb_down_container")},
            "panel_captions_button": {"path": UPath(text_ == "Captions")},
            "Home": {"path": UPath(id_ == "main_bottom_button_home") / UPath(type_ == "ImageView", depth=5)}
        })
        return locators
    
    def open_panel_long_click(self):
        if self["interact_right_area"].wait_for_existing(timeout=5, raise_error=False):
            self["interact_right_area"].long_click(duration=2)
    
    def close_caption_panel(self):
        if self["caption_panel_close"].wait_for_existing(timeout=5, raise_error=False):
            self["caption_panel_close"].click()
    
    def drag_combine_band(self):
        width = self.device.screen_rect.width
        height = self.device.screen_rect.height
        self.drag(0.2 * width, 0.55 * height, 0.3 * width,0.05 * height,duration = 2, press_duration=1)
    
    def slowly_swipe(self):
        width = self.device.screen_rect.width
        height = self.device.screen_rect.height
        self.drag(0.5 * width, 0.35 * height, 0, 0.15 * height,duration = 0.8, press_duration=0.3)
    
    def quickly_swipe(self):
        width = self.device.screen_rect.width
        height = self.device.screen_rect.height
        self.drag(0.5 * width, 0.35 * height, 0, 0.15 * height,duration = 0.2, press_duration=0)

class LanguagePanel(Window):
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"
    }

    def get_locators(self):
        return {
            'content': {'path': UPath(id_ == "content")},
            'cancel': {'path': UPath(text_ == "Cancel")},
            "dnt_done": {"path": UPath(~text_ == "Done|完成")},
            'list_language': {'path':UPath(id_ == "list_language")},
            "Language_page":{"path": UPath(fragment_ == "TranslationLanguageSettingPage")},
            "language_swipe":{"path":UPath(type_ == "AndroidComposeView") / 1 / 2},

            #language
            "Bahasa Indonesia": {"path": UPath(text_ == "Bahasa Indonesia")},
            "Bahasa Melayu": {"path": UPath(text_ == "Bahasa Melayu")},
            "Català": {"path": UPath(text_ == "Català")},
            "Čeština": {"path": UPath(text_ == "Čeština")},
            "Deutsch": {"path": UPath(text_ == "Deutsch")},
            "Eesti": {"path": UPath(text_ == "Eesti")},
            "English": {"path": UPath(text_ == "English")},
            "Español": {"path": UPath(text_ == "Español")},
            "Français": {"path": UPath(text_ == "Français")},
            "Italiano": {"path": UPath(text_ == "Italiano")},
            "日本語": {"path": UPath(text_ == "日本語")},
            "한국어": {"path": UPath(text_ == "한국어")},
            "Nederlands": {"path": UPath(text_ == "Nederlands")},
            "Polski": {"path": UPath(text_ == "Polski")},
            "Português": {"path": UPath(text_ == "Português")},
            "Русский": {"path": UPath(text_ == "Русский")},
            "Svenska": {"path": UPath(text_ == "Svenska")},
            "Türkçe": {"path": UPath(text_ == "Türkçe")},
            "Tiếng Việt": {"path": UPath(text_ == "Tiếng Việt")},
            "中文（简体）": {"path": UPath(text_ == "中文（简体）")},
            "中文（繁體）": {"path": UPath(text_ == "中文（繁體）")},
            "Dansk": {"path": UPath(text_ == "Dansk")},
        }

    def close_page(self):
        self['cancel'].click()