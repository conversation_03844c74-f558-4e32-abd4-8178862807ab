from business.vod.common.ios.feed_panel import *
import time

class FeedPanel(FeedPanel):
    def get_locators(self):
        # 获取父类的定位器
        locators = super().get_locators()
        # 添加新的定位器
        locators.update({
            "debug_menu": {"path": UPath(type_ == "UIImageView", label_ == "菜单")},
            "import": {"path": UPath(id_ == "Import")},
            "text_edit": {"path": UPath(id_ == "fieldEditor")},
            "captions_icon": {"path": UPath(id_ == "null", ~label_ == "字幕和翻译|Captions") / UPath(type_ == "UIImageView", depth=5)},
            "AWEFeedRootViewControl": {"path": UPath(type_ == "TTKFeedPlayerContainerView", visible_ == True) / UPath(type_ == "ACCStickerSDKExcludeSelfView", depth=5)},
            "Translation": {"path": UPath(~text_ == "翻译|Translation", visible_ == True)},
            "caption_panel_close": {"path": UPath(id_ == "rightCloseButton") / UPath(type_ == "UIImageView", depth=5)},
            "english_in_dnt": {"path": UPath(text_ == "English")},
            "dnt_done": {"path": UPath(type_ == "UIButton", label_ == "Done")},
            "dnt_done_new": {"path": UPath(~text_ == "完成|Done")},
            "do_not_translate": {"path": UPath(~text_ == "不翻译|Do not translate")},
            "show_captions": {"path": UPath(type_ == "UITableView") / 1 / UPath(id_ == "actionBtn", depth=8)},
            "always_translate_posts": {"path": UPath(type_ == "UITableView") / 2 / UPath(id_ == "actionBtn", depth=8)},
            "always_translate_posts_zh": {"path": UPath(type_ == "UITableView") / 3 / UPath(id_ == "actionBtn", depth=8)},
            "show_captions_new": {"path": UPath(type_ == "UITableView") / 1 / UPath(id_ == "toggle", depth=7)},
            "always_translate_posts_new": {"path": UPath(type_ == "UITableView") / 2 / UPath(id_ == "toggle", depth=7)},
            "always_translate_posts_zh_new": {"path": UPath(type_ == "UITableView") / 3 / UPath(id_ == "toggle", depth=7)},
            "always_translate_posts_photomode": {"path": UPath(id_ == "toggle")},
            "combine_uiband": {"path": UPath(id_ == "tableView") / 0 / UPath(type_ == "UITableViewCellContentView", depth=5)},
            "captions_icon": {"path": UPath(id_ == "null", ~label_ == "字幕和翻译|Captions") / UPath(type_ == "UIImageView", depth=5)},
            "translate_into": {"path": UPath(~text_ == "翻译语言|翻译为|Translate into")},
            "translate_into_content_video": {"path": UPath(type_ == "UITableView") / 5 / UPath(id_ == "textLabel", depth=9)},
            "translate_into_content_photo": {"path": UPath(type_ == "UITableView") / 3 / UPath(id_ == "textLabel", depth=9)},
            "dnt_content": {"path": UPath(type_ == "UITableView") / 4 / UPath(id_ == "textLabel", depth=9)},
            "translate_into_done": {"path": UPath(~text_ == "完成|Done")},
            "translate_more": {"path": UPath(~text_ == "翻译更多内容|Translate more")},
            "text_on_posts": {"path": UPath(id_ == "toggle")},
            "exit_translate_more": {"path": UPath(id_ == "leftBackButton") / UPath(label_ == "(null)", depth=5)},
            "Translation": {"path": UPath(~text_ == "翻译|Translation", visible_ == True)},
            "detail_image": {"path": UPath(id_ == "stickerContainerView")},
            "phototitle": {"path": UPath(type_ == "YYLabel", visible_ == True)},
            "profile": {"path":UPath(id_ == "null", label_ == "主页") / UPath(id_ == "ic_tab_home_loading", depth=5)},
            "user_avatar": {"path": UPath(type_ == "AWEStoryAvatarButton", visible_ == True) / UPath(type_ == "UIImageView", depth=5)},
            "captions_creator": {"path": UPath(~text_ == "字幕|Captions")},
            #language
            "Bahasa_Indonesia": {"path": UPath(text_ == "Bahasa Indonesia")},
            "Bahasa Melayu": {"path": UPath(text_ == "Bahasa Melayu")},
            "Català": {"path": UPath(text_ == "Català")},
            "Čeština": {"path": UPath(text_ == "Čeština")},
            "Deutsch": {"path": UPath(text_ == "Deutsch")},
            "Eesti": {"path": UPath(text_ == "Eesti")},
            "English": {"path": UPath(text_ == "English")},
            "Español": {"path": UPath(text_ == "Español")},
            "Français": {"path": UPath(text_ == "Français")},
            "Italiano": {"path": UPath(text_ == "Italiano")},
            "日本語": {"path": UPath(text_ == "日本語")},
            "한국어": {"path": UPath(text_ == "한국어")},
            "Nederlands": {"path": UPath(text_ == "Nederlands")},
            "Polski": {"path": UPath(text_ == "Polski")},
            "Português": {"path": UPath(text_ == "Português")},
            "Русский": {"path": UPath(text_ == "Русский")},
            "Svenska": {"path": UPath(text_ == "Svenska")},
            "Türkçe": {"path": UPath(text_ == "Türkçe")},
            "Tiếng Việt": {"path": UPath(text_ == "Tiếng Việt")},
            "中文（简体）": {"path": UPath(text_ == "中文（简体）")},
            "中文（繁體）": {"path": UPath(text_ == "中文（繁體）")},
            "Dansk": {"path": UPath(text_ == "Dansk")},
        })
        return locators
    
    def drag_combine_band(self):
        width = self.device.screen_rect.width
        height = self.device.screen_rect.height
        self.drag(0.2 * width, 0.55 * height, 0.3 * width,0.05 * height,duration = 2, press_duration=1)
    
    def open_panel_long_click(self):
        if self["AWEFeedRootViewControl"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["AWEFeedRootViewControl"].long_click(duration=2)
        else:
            logger.info("not found AWEFeedRootViewControl")
            width = self.device.screen_rect.width
            height = self.device.screen_rect.height
            self.app.get_device().long_click(x=width*0.5,y=height*0.5)

    def close_caption_panel(self):
        if self["caption_panel_close"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["caption_panel_close"].click()

    def slowly_swipe(self):
        width = self.device.screen_rect.width
        height = self.device.screen_rect.height
        self.drag(0.5 * width, 0.25 * height, 0, 0.2 * height,duration = 0.8, press_duration=1)
    
    def quickly_swipe(self):
        width = self.device.screen_rect.width
        height = self.device.screen_rect.height
        self.drag(0.5 * width, 0.35 * height, 0, 0.15 * height,duration = 0.2, press_duration=1)        