from business.cla.common.feed_panel_Android import *
import time

class AndroidAction:
    def __init__(self, app):
        self.app = app
        self.feed_panel = FeedPanel(root=self.app)
        self.language_panel = LanguagePanel(root=self.app)
    
    def click_captions_button_in_long_press_panel(self):
        self.feed_panel.drag_combine_band()
        if self.feed_panel["panel_captions_button"].wait_for_visible(timeout=5, raise_error=False):
            self.feed_panel["panel_captions_button"].click()

    def click_translation_button_in_long_press_panel(self):
        if self.feed_panel["Translation"].wait_for_existing(timeout=5, raise_error=False):
            self.feed_panel["Translation"].click()
        else:
            self.feed_panel.drag_combine_band()
            self.feed_panel["Translation"].click()
    
    def get_translate_into_lang(self,awemetype = "video"):
        self.feed_panel.open_panel_long_click()
        time.sleep(0.5)
        if awemetype!= "video":
            self.click_translation_button_in_long_press_panel()
            time.sleep(0.5)
            if self.feed_panel["translate_into_content_photo"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                res = self.feed_panel["translate_into_content_photo"].text
            else:
                res = "None"
            self.feed_panel.close_caption_panel()
            return res
        else:
            self.click_captions_button_in_long_press_panel()
            time.sleep(0.5)
            if self.feed_panel["translate_into_content_video"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                res = self.feed_panel["translate_into_content_video"].text
            else:
                res = "None"
            self.feed_panel.close_caption_panel()
            return res
    
    def set_translate_into_lang(self,string,awemetype = "video",swipe_method = "slowly"):
        self.feed_panel.open_panel_long_click()
        time.sleep(0.5)
        if awemetype != "video":
            self.click_translation_button_in_long_press_panel()
        else:
            self.click_captions_button_in_long_press_panel()
        time.sleep(0.5)
        if self.feed_panel["translate_into"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self.feed_panel["translate_into"].click()
        time.sleep(1)
        for _ in range(5):
            if self.language_panel[string].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                self.language_panel[string].click()
                break
            else:
                if swipe_method == "slowly":
                    self.feed_panel.slowly_swipe()
                elif swipe_method == "quickly":
                    self.feed_panel.quickly_swipe()
        time.sleep(1)
        if self.feed_panel["translate_into_done"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self.feed_panel["translate_into_done"].click()
        time.sleep(1)
        self.feed_panel.close_caption_panel()

    def check_video_list(self,vid_list):
        vlist = vid_list.split(",")
        i = 0
        current_feed_id = self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", 
                                               method="copyVideoID")
        if current_feed_id == vlist[0]:
            return True
        else:
            self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi",
                                                method="insertSpecificFeedList",args=[vid_list])
            time.sleep(2.5)
            self.app.restart(clear_data = False)
            time.sleep(2.5)
            while i < 5:
                time.sleep(2.5)
                current_feed_id = self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi",
                                               method="copyVideoID")
                if current_feed_id != vlist[0]:
                    self.feed_panel.swipe_up_times(times = 1, duration = 2)
                else:
                    return True
                time.sleep(2)
                current_feed_id = self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi",
                                               method="copyVideoID")
                if current_feed_id == vlist[0]:
                    return True
                else:
                    i = i + 1
                    self.app.restart(clear_data = False)
                    time.sleep(2.5)
        return False
