"""
Author: huangyihong.0917
Date: 2025-6-20
Description: CLA  - 安卓加载离线字幕
"""
import time

from business.cla.common.feed_panel_Android import *
from business.cla.common.android_action import *
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *
from business.cla.utils.common_utils import VIDS

class OfflineCaption(TTCrossPlatformPerfAppTestBase):
    """
    CLA  - 安卓加载离线字幕
    """
    owner = "huangyihong.0917"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "cla - offline caption"
    description = "CLA  - 安卓加载离线字幕"
    tags = []

    def watch_video(self, times, duration):
        """
        CLA  - 安卓加载离线字幕
        """
        vod_panel = AndroidVodPanel(root=self.perf_app)
        vod_panel.open_home_tab()
        self.start_step("上划加载Feed流播放10min")
        vod_panel.swipe_up_times(times, duration)

    def run_test(self):
        act = AndroidAction(self.perf_app)
        self.start_step("登录账号")
        self.login_all_devices()
        feed = FeedPanel(root=self.perf_app)
        feed["Home"].click()
        self.start_step("插视频")
        vids = VIDS
        act.check_video_list(vids)
        self.start_step("开始采集性能数据")    
        self.collect_perf_data(
            app=self.perf_app,
            operations=self.watch_video,
            operations_args=(len(vids.split(',')) + 1, 10)
        )


if __name__ == '__main__':
    OfflineCaption().debug_run()
