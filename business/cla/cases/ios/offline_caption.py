"""
Author: huangyihong.0917
Date: 2025-6-12
Description: offline caption
"""

from defines import *
from business.vod.common.ios.vod_feed_panel import *
from business.vod.common.ios.debug_panel import *
from business.cla.common.ios_action import *
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from business.cla.utils.common_utils import *

class OfflineCaption(TTCrossPlatformPerfAppTestBase):
    """
    offline caption
    """
    owner = "huangyihong.0917"
    timeout = 3600
    platform = [PlatformType.IOS]
    device_count = 1
    title = "offline caption"
    description = "CLA测试 debug"
    tags = []
    
    def watch_video(self, times, duration):
        """
        Feed页 - 下滑播放完vid_list所有视频
        """
        vod_feed = VodPanelIOS(root=self.perf_app)
        vod_feed.into_home_page()
        self.start_step("上划加载Feed流")
        vod_feed.swipe_up_times(times, duration)

    def run_test(self):
        act = IosAction(self.perf_app)
        self.start_step("登录账号")
        self.login_all_devices()
        feed = VodPanelIOS(root=self.perf_app)
        feed["Home"].click()
        vids = VIDS
        self.start_step("插视频")
        act.set_feed(vids)
        self.start_step("开始采集性能数据")    
        self.collect_perf_data(
            app=self.perf_app,
            operations=self.watch_video,
            operations_args=(len(vids.split(',')) + 1, 10)
        )

if __name__ == '__main__':
    OfflineCaption().debug_run()