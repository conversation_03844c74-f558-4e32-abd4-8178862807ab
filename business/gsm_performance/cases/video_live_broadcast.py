from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from utils.common.log_utils import logger, log_exception
from defines import *

class VideoLiveBroadCast(TTCrossPlatformPerfAppTestBase):

    """视频直播场景测试用例"""
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 1
    title = "视频直播"
    description = "视频直播场景性能测试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("登录账号")
        self.login_all_devices()

        logger.info("主播开启直播")
        self.start_live_broadcast(self.perf_app)
        self.assert_anchor_live_broadcast(self.perf_app)

        logger.info("开始采集基础性能数据")
        self.collect_perf_data(app=self.perf_app)

class VideoLiveBroadCast_SG_Beauty(TTCrossPlatformPerfAppTestBase):
    """
    VideoLiveBroadCast_SG_Beauty
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 1
    title = "[指定SG区域][开启美颜]视频直播"
    description = "切换SG区域,直播开启美颜,视频直播场景性能测试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("登录账号")
        self.login_all_devices()

        logger.info("切换到SG区域")
        self.switch_region(self.perf_app, "SG")

        logger.info("主播开启直播")
        self.start_live_broadcast(self.perf_app, enable_beauty=True)
        self.assert_anchor_live_broadcast(self.perf_app)

        logger.info("开始采集基础性能数据")
        self.collect_perf_data(app=self.perf_app)

if __name__ == '__main__':
    VideoLiveBroadCast_SG_Beauty().debug_run()
