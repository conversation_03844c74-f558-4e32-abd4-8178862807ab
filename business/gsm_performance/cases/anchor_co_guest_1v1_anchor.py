'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 14:07:08
FilePath: /global_business_perf/business/global_rtc/cases/anchor_co_guest_1v1_anchor.py
Description: 
'''
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from utils.common.log_utils import logger, log_exception
from defines import *

class AnchorCoGuest_1V1_Anchor(TTCrossPlatformPerfAppTestBase):
    """
    AnchorCoGuest_1V1
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 2
    title = "主播与嘉宾1V1连麦"
    description = "主播与嘉宾1V1连麦场景性能测试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("获取辅助设备应用")
        auxil_app = self.auxil_app_list[0]

        logger.info("登录账号")
        self.login_all_devices()

        logger.info("主播开启直播")
        self.start_live_broadcast(self.perf_app)
        self.assert_anchor_live_broadcast(self.perf_app)
        
        logger.info("嘉宾通过UI进入直播间")
        self.search_and_enter_live_room(auxil_app, self.perf_account["account_username"])
        self.assert_guest_enter_anchor_room(auxil_app)
        
        logger.info("嘉宾申请连麦")
        self.guest_apply_connect(auxil_app)
        self.anchor_agree_guest_connect(self.perf_app)

        logger.info("嘉宾打开摄像头")
        self.guest_open_camera(auxil_app)
        self.assert_guest_connect(self.perf_app, assert_type="1v1")

        logger.info("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)

class AnchorCoGuest_1V1_Anchor_SG_Beauty(TTCrossPlatformPerfAppTestBase):
    """
    AnchorCoGuest_1V1_Anchor_SG_Beauty
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 2
    title = "[指定SG区域][开启美颜]主播与嘉宾1V1连麦"
    description = "切换SG区域,直播开启美颜,主播与嘉宾1V1连麦场景性能测试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("获取辅助设备应用")
        auxil_app = self.auxil_app_list[0]

        logger.info("登录账号")
        self.login_all_devices()

        logger.info("切换到SG区域")
        self.switch_region(self.perf_app, "SG")
        self.switch_region(auxil_app, "SG")

        logger.info("主播开启直播")
        self.start_live_broadcast(self.perf_app, enable_beauty=True)
        self.assert_anchor_live_broadcast(self.perf_app)

        logger.info("嘉宾通过UI进入直播间")
        self.search_and_enter_live_room(auxil_app, self.perf_account["account_username"])
        self.assert_guest_enter_anchor_room(auxil_app)

        logger.info("嘉宾申请连麦")
        self.guest_apply_connect(auxil_app)
        self.anchor_agree_guest_connect(self.perf_app)

        logger.info("嘉宾打开摄像头")
        self.guest_open_camera(auxil_app)
        self.assert_guest_connect(self.perf_app, assert_type="1v1")

        logger.info("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)

if __name__ == '__main__':
    AnchorCoGuest_1V1_Anchor_SG_Beauty().debug_run()
