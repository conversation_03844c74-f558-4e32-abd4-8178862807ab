from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from utils.common.log_utils import logger, log_exception
from defines import *

class RTCDebug(TTCrossPlatformPerfAppTestBase):

    """RTC调试用例"""
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 1
    title = "RTC调试用例"
    description = "RTC调试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("登录账号")
        # self.login_all_devices()

        logger.info("开始采集基础性能数据")
        self.collect_perf_data(app=self.perf_app)


if __name__ == '__main__':
    RTCDebug().debug_run()
