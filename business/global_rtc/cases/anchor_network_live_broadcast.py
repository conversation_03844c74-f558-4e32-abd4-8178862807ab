'''
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-18 14:09:22
FilePath: /global_business_perf/business/global_rtc/cases/anchor_network_live_broadcast.py
Description: 
'''
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from utils.common.log_utils import logger, log_exception
from defines import *

class AnchorNetworkLiveBroadCast(TTCrossPlatformPerfAppTestBase):

    """主播网直播场景测试用例"""
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 1
    title = "主播网直播"
    description = "主播网直播场景性能测试用例"
    tags = []

    # 定义固定账号信息
    fixed_account = {
        "account_iphone": "***********",  # 固定手机号
        "account_captcha": "008207",  # 固定验证码
        "account_username": "GlobalRTC9069",  # 固定昵称
        "account_uid": "7327132535639884818",  # 固定用户ID
        "account_country": "中国大陆",
        "account_phone_area_code": "86"
    }

    @log_exception
    def run_test(self):
        logger.info("登录固定账号")
        self.login_fixed_account(self.fixed_account)

        logger.info("主播开启直播")
        self.start_live_broadcast(self.perf_app)
        self.assert_anchor_live_broadcast(self.perf_app)

        logger.info("开始采集基础性能数据")
        self.collect_perf_data(app=self.perf_app)


if __name__ == '__main__':
    AnchorNetworkLiveBroadCast().debug_run()
