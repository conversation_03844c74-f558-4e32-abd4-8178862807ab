from common.tiktok.android.panels.vod import AndroidVodPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInFeed20Mins(TTCrossPlatformPerfAppTestBase):
    """
    [VodDebug]基础性能指标 - Feed页 - 上下滑动播放20 min
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "[VodDebug]VoD-PlayInFeed20Mins"
    description = "点播测试 Feed页 - 上下滑动播放20 min"
    tags = []
    
    def play_in_feed_20mins(self, times=1, duration=0):
        """
        Feed页 - 上下滑动播放20 min
        """
        vod_panel = AndroidVodPanel(root=self.perf_app)
        vod_panel.open_home_tab()

        self.start_step("快速上划20次")
        vod_panel.swipe_up_times(times, duration)

        self.start_step("feed页静止")
        vod_panel.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            operations=self.play_in_feed_20mins,
            operations_args=(20, 5)
        )

if __name__ == "__main__":
    PlayInFeed20Mins().debug_run()