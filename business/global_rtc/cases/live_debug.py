
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class GameGoLiveStage1080P60(TTCrossPlatformPerfAppTestBase):
    """
    [LiveDebug]非主播网手游直播-1080P60
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "[LiveDebug]非主播网手游直播-1080P60"
    description = ""
    tags = []
    trace = [LIVE_TRACE_PERF_INDEX]

    def run_test(self):
        self.start_step('登录测试账号')
        self.login_all_devices()

        self.start_step("主播开启直播")
        self.start_live_broadcast(self.perf_app, live_type="game", video_quality="1080p60")
        self.assert_anchor_live_broadcast(self.perf_app)

        self.start_step("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)

class GoLiveStage720(TTCrossPlatformPerfAppTestBase):
    """
    [LiveDebug]非主播网秀场直播-720P
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "[LiveDebug]非主播网秀场直播-720P"
    description = ""
    tags = []
    trace = [LIVE_TRACE_PERF_INDEX]

    def run_test(self):
        self.start_step('登录测试账号')
        self.login_all_devices()

        self.start_step("主播开启直播")
        self.start_live_broadcast(self.perf_app, live_type="camera", video_quality="720p")
        self.assert_anchor_live_broadcast(self.perf_app)

        self.start_step("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)

class GoLiveStage1080(TTCrossPlatformPerfAppTestBase):
    """
    [LiveDebug]非主播网秀场直播-1080P
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "[LiveDebug]非主播网秀场直播-1080P"
    description = ""
    tags = []
    trace = [LIVE_TRACE_PERF_INDEX]

    def run_test(self):
        self.start_step('登录测试账号')
        self.login_all_devices()

        self.start_step("主播开启直播")
        self.start_live_broadcast(self.perf_app, live_type="camera", video_quality="1080p")
        self.assert_anchor_live_broadcast(self.perf_app)

        self.start_step("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)

if __name__ == '__main__':
    GoLiveStage1080().debug_run()
    GoLiveStage720().debug_run()
    GameGoLiveStage1080P60().debug_run()
