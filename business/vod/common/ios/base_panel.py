# -*- coding: utf8 -*-
import time
import os
import cv2
from PIL import Image
from shoots import logger
from shoots_cv.cv import CV
from uibase.controls import *
from uibase.upath import *
from byted_cv import ocr_helper
from byted_cv.core import cv
from uibase.controls import Control
from shoots.config import settings
from shoots_ios.bdc_helper import bdc_call, BDCError
from uibase.controls import Control as BaseControl
from uibase.web import WebElement, Webview, WebTextEdit

def func_time(func):
    def inner(*args, **kw):
        start_time = time.time()
        result = func(*args, **kw)
        end_time = time.time()
        logger.info(f'{func.__name__} cost time:{end_time - start_time}s')
        return result
    return inner


class ImageControlLocatorError(Exception):
    """image control locator error
    """
    pass


class LocatorsBundleIdMixin(object):
    """locators bundle_id mixin
    """

    def get_locators(self):
        locators = self.get_universal_locators()
        bundle_id = self.app.bundle_id
        if "musically" in bundle_id:
            locators.update(self.get_musically_locators())
        else:
            locators.update(self.get_tiktok_locators())
        return

    def get_universal_locators(self):
        """locators do not distinguish musically or tiktok
        """
        return {}

    def get_musically_locators(self):
        """locators only for musically
        """
        return {}

    def get_tiktok_locators(self):
        """locators only for tiktok
        """
        return {}


class BasePanel(Window):
    """界面相关基类
    """

    def get_locators(self):
        return {
            #   Panel_Controller
            # "panel_controller": {"type": Control, "path": },
            #   Example
            # "": {"type": Control, "path": )},
        }

    def ui_checking(self):
        """检查界面元素内容
        """
        pass

    def wait_for_panel_loading(self):
        """判断是否正确进入本界面，关键元素加载显示正常
        """
        pass

    def swipe(self, x_direction=0, y_direction=0, swipe_coefficient=5):
        '''滑动

        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        后续会支持斜着滑动

        :param coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        '''

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(
                x_direction, y_direction))

        # self._driver.drag(self.id, x1, y1, x2, y2)
        self.drag(x2, y2, x1 - rect.width/2, y1 - rect.height/2)
        time.sleep(1)

    def element_compare_by_image(self, device, control: Control, original_pic_name: str):
        """
        通过截图对应元素的当前图像并与和预存储的图像进行精细对比，实现界面元素校验，
        校验内容包括：按钮及图片大小、内容、样式，字体内容、大小、是否加粗，字体；
        使用范围：
            不包含内容变化的界面或者界面区域进行比对；
        参数说明：
            device - 测试机设备实例
            Control - 需要进行图像对比的元素对象，元素对象对应的区域即为截图区域
            original_pic_name - 预存在resourc目录下的用例同名图片文件名（无需带.png后缀）
        额外说明：
            此函数对比方式简单粗暴，但是敏感度高，准确度也高；
            因此需要预存储图片的截取机型和当前测试机型保持一致；
        """
        capture = cv.get_screenshot(device)
        rect = control.rect
        target = capture[0][rect.top * 3:(rect.top + rect.height) * 3, rect.left * 3:(rect.left + rect.width) * 3]
        basedir = os.path.abspath('.')
        pic_dir = os.path.join(basedir, device.serial)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        original_pic_dir = os.path.join(os.path.abspath(os.path.join(os.getcwd(), "../../../../..")), "resources")
        original_pic_name = os.path.join(original_pic_dir, original_pic_name+".png")
        # return ocr_helper.dhash_diff_pic(target_pic_name, original_pic_name)
        from shoots_cv.cv import CV
        cv_ = CV()
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']
        return compare_result >= 1

    def device_input(self, text_view, text):
        text_view.click()
        text_view.text = ""
        time.sleep(1)
        device = self.app.get_device()
        try:
            device.send_keys(text)
        except BDCError:
            text_view.send_keys(text)
        except:
            logger.info("direct input error")

    def device_send_enter(self):
        device = self.app.get_device()
        device.send_keys("\r")

    def inside_screen(self, control: Control):
        if (
            control.rect.left >= self.rect.left
            and control.rect.top >= self.rect.top
            and (control.rect.left + control.rect.width) <= (self.rect.left + self.rect.width)
            and (control.rect.top + control.rect.height) <= (self.rect.top + self.rect.height)
        ):
            return True
        else:
            return False

    def device_click_point(self, location="center", offset_x=0, offset_y=0):
        """
        click screen point, remove pop window
        :param location:top,bottom,left,right,center
        :type:str
        """
        time.sleep(1)
        device = self.app.get_device()
        rect = device.screen_rect
        if "top" in location:
            x = rect.center[0] + offset_x
            y = rect.top + offset_y
        elif "bottom" in location:
            x = rect.center[0] + offset_x
            y = rect.bottom - offset_y
        elif "left" in location:
            x = rect.left + offset_x
            y = rect.center[1] + offset_y
        elif "right" in location:
            x = rect.right - offset_x
            y = rect.center[1] + offset_y
        elif "center" in location:
            x = rect.center[0] + offset_x
            y = rect.center[1] + offset_y
        logger.info(f"click point:{(x,y)}")
        device.click(x, y)

    @func_time
    def compare_pic(self, target_pic_path, src_pic_path):
        """
        Compare the similarity of two pictures
        """
        logger.info(f"tar path:{target_pic_path}, src path:{src_pic_path}")
        cv_tool = CV(self.app.get_device())
        try:
            result = cv_tool.fg_sim(target_pic_path, src_pic_path)
            logger.info(f"compare result:{result}")
            if "Service error" in result["msg"]:
                result = cv_tool.fg_sim(target_pic_path, src_pic_path)
                logger.info(f"second compare result:{result}")
            return result
        except Exception as e:
            logger.info(f"compare fail reason:{e}")
            return False

    def click_text_point(self, text, tar_path, select_no=1):
        device = self.app.get_device()
        cv = CV(device)
        path = tar_path.split(".")[0] + "_contrl.jpg"
        res = cv.ocr_location(text, target_pic=tar_path, save_path=path)
        position_list = res['result']
        logger.info(f"{text} position info:{res}")
        if len(position_list) == 1:
            point = position_list[0]['center_pos']
        else:
            point = position_list[select_no]['center_pos']
        logger.info(f"{text} selected point:{point}")
        device.click(point[0], point[1])

    def switch_toggle_status(self, path, target_path, index=0, similarity=0.5):
        cv_tool = CV(self.app.get_device())
        try:
            res = cv_tool.universal_ui_detection(path)
            logger.info(f"compare result:{res}")
            position = res["result"]["Switch"][index]
            self.img_crop(path, position)
            result = self.compare_pic(path, target_path)
            if index == 0:
                toggle_name = "Mute"
            elif index == 1:
                toggle_name = "Pin"
            elif index == 2:
                if "private" in self.app.testcase.test_name:
                    toggle_name = "Block"
                else:
                    toggle_name = "Approval"
            if result["similarity"] > similarity:
                logger.info(f"{toggle_name} switch toggle state is open")
                return True
            else:
                logger.info(f"{toggle_name} switch toggle state is close")
                return False
        except Exception as e:
            logger.info(f"get switch toggle state fail, reason:{e}")
            return False

    def img_crop(self, path, box):
        try:
            img = Image.open(path)
        except:
            logger.info("open img path fail")
        try:
            image1 = img.crop(box)
            image1.save(path)
        except:
            logger.info("crop img fail")

    def handle_uiambiguous_error(self, key, error):
        """
        :param:key, the key that uniquely locates a control
        :type:str
        :param:error, Exception of try catch

        eg: handle_uiambiguous_error(key, UIAmbiguousError)
        """
        rect = None
        ui_control_list = error.split("\n")
        for ui_info in ui_control_list:
            ui_info = eval(ui_info)
            if key in ui_info.values():
                rect = ui_info["rect"]
                break
        logger.info(f"=> expect path rect:{rect}")
        if rect:
            self.app.get_device().click(rect['left']+rect['width']/2, rect['top']+rect['height']/2)


class ImageControl(Control):
    """control has image attached
    """
    def __init__(self, *args, **kwargs):
        self._img_path = kwargs.pop("img_path")
        if self._img_path is None:
            raise ImageControlLocatorError("no img_path find in ImageControl locator")
        super(ImageControl, self).__init__(*args, **kwargs)
        self._screenshot_driver = None

    @property
    def img_path(self):
        return os.path.join(settings.PROJECT_ROOT, self._img_path)

    @property
    def screenshot_driver(self):
        if self._screenshot_driver is None:
            device = self.app.get_device()
            self._screenshot_driver = HighQualityScreenshotDriver(device.serial)
        return self._screenshot_driver

    @property
    def cv_existing(self):
        # driver = self.app.get_device()
        if ocr_helper.find_pic(self.screenshot_driver, self.img_path) is not None:
            return True
        return False

    def cv_click(self):
        driver = self.app.get_device()
        return ocr_helper.click_pic(driver, self.img_path)


class HighQualityScreenshotDriver(object):
    """bdc screenshot <screenshot_path> --quality high -u <udid>
    """

    def __init__(self, udid):
        self._udid = udid

    @property
    def serial(self):
        return self._udid

    def screenshot(self, screenshot_path):
        cmdline = "screenshot %s --quality high -u %s" % (screenshot_path, self._udid)
        bdc_call(cmdline)
        return screenshot_path


class Control(BaseControl):

    def swipe(self, x_direction=0, y_direction=0, swipe_coefficient=3):
        '''滑动

        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        后续会支持斜着滑动

        :param coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        '''

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        self.wait_for_ui_stable()
        self.wait_for_visible(raise_error=False)

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(x_direction, y_direction))
        self.scroll(x1 - x2, y1 - y2)
        # self.app.testcase.log_info(("x1-x2:%s,y1-y2:%s" % (x1 - x2, y1 - y2)))
        time.sleep(1)
