# -*- coding: utf-8 -*-
import time

from shoots.retry import Retry
from shoots_cv.controls import CVElement
from shoots_cv.cv import CV
from shoots_cv.upath import ocr_
from uibase.base import on_expired

from business.vod.common.ios.base_panel import *
from uibase.base import *


class NgoCell(Control):
    def get_locators(self):
        return {
            "title_label": { "path":UPath(type_=='UITableViewCellContentView')/UPath(type_ == "UILabel",id_=='titleLabel')},
            "content_label": { "path":UPath(type_=='UITableViewCellContentView')/UPath(type_ == "UILabel",id_=='content_label')}
        }
    def get_title_label(self):
        return  self['title_label']

class NgoCells(Control):
    elem_class = NgoCell
    elem_path = UPath(type_ == 'TikTokDonationOrigationTableViewCell')

    @on_expired
    def items(self):
        time.sleep(1)
        items = super(Ngo<PERSON>ells, self).items()
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if len(items) != 0:
                break
        return items


class otherPagePannel(BasePanel):
    """other page  panel
       """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}
    def get_locators(self):
        return {
            "ngo_able_games_foundation_inc": {
            "path": UPath(id_ == 'titleLabel', label_ == 'Able Gamers Foundation Inc')},
            "ngo_act_to_change": {"path": UPath(id_ == 'titleLabel', label_ == 'Act to Change')},
            "select_organization": {"path": UPath(label_ == 'Select organization')},
            "select_organization1": {"path": UPath(id_ == "headerView") / UPath(id_ == "titleLabel", depth=5)},
            "change_btn": {"path": UPath(type_=='AWEUIButton',id_=='Change')},
            "ngo_cells": {"type": NgoCells,
                                "path": UPath(controller_ == 'TikTokDonationOrganizationListViewController')},

        }

    def get_ngo_1st_click(self):
        ngo_cell = self["ngo_cells"].items()[0]
        label = ngo_cell.get_title_label().text
        ngo_cell.get_title_label().click()
        time.sleep(2)
        return label

    def get_ngo_2rd_click(self):
        ngo_cell = self["ngo_cells"].items()[1]
        label = ngo_cell.get_title_label().text
        ngo_cell.get_title_label().click()
        time.sleep(2)
        return label
        # return self["ngo_able_games_foundation_inc"].click()
    def get_select_organization(self):
        if self["select_organization"].wait_for_visible(raise_error=False):
            return self["select_organization"]
        elif self["select_organization1"].wait_for_visible():
            return self["select_organization1"]

    def change_btn_click(self):
        if  self["change_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["change_btn"].click()
            return  True
        else:
            logger.info(f"change_btn:{False}")
            return False



class SetNicknamePopUp(BasePanel):
    """nickname popUp
        """
    window_spec = {"path": UPath(type_ == 'UIView', visible_ == True,id_=='(containerView)')}
    def get_locators(self):
        return {
            "title_lable":{"path":UPath(type_=='TUXDialogContentSection',id_=='(contentSection)')/UPath(type_=='UILabel',id_=='(titleLabel)',visible_==True)},
            "content_label":{"path":UPath(type_=='TUXDialogContentSection',id_=='(contentSection)')/UPath(type_=='UITextView',id_=='(messageTextView)',visible_==True)},
            "confirm_btn":{"path":UPath(type_=='TUXDialogActionSection',id_=='(actionView)')/UPath(type_=='TUXDialogHighlightBackgroundButton',label_=='Confirm',visible_==True)},
            "cancel_btn":{"path":UPath(type_=='TUXDialogActionSection',id_=='(actionView)')/UPath(type_=='TUXDialogHighlightBackgroundButton',label_=='Cancel',visible_==True)},
        }
    def get_confirm_btn(self):
        return self['confirm_btn']
    def check_popUp(self):
        flag=True
        if self['content_label'].wait_for_existing(timeout=3, raise_error=False):
            content= self['content_label'].text
            flag=flag&(content=='You can only change your nickname once every 7 days')
            if not (content=='You can only change your nickname once every 7 days'):
                logger.info(f"content:{content}")
        else:
            logger.info("content_label:not exit")
            flag=flag&False
        if self['title_lable'].wait_for_existing(timeout=3, raise_error=False):
            content= self['title_lable'].text
            flag=flag&(content=='Set nickname?')
            if not (content=='Set nickname?'):
                logger.info(f"content:{content}")
        else:
            logger.info("title_lable:not exit")
            flag=flag&False
        flag2=self['confirm_btn'].wait_for_existing(timeout=3, raise_error=False)
        flag3=self['cancel_btn'].wait_for_existing(timeout=3, raise_error=False)
        if not flag2:
            logger.info("confirm_btn:not exit")
        if not flag3:
            logger.info("cancel_btn:not exit")
        flag=flag&flag3&flag2
        return flag



class ProfileBioMention1(BasePanel):
    window_spec = {"path": UPath(type_ == 'UITextEffectsWindow')}
    def get_locators(self):
        return {
            "mention": {"path": UPath(id_ == "atButton")},
            "mention1": {"path": UPath(id_ == "Mention")},
            "mention_result": {"path": UPath(id_ == "collectionView") / 0 / UPath(id_ == "usernameLabel", depth=5)},
        }
    def ClickNOVisibleBtn(self,upath):
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)

    def ClickVisibleBtn(self,upath,upath1=None):
        if upath1 == None:
            upath1 = upath
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)
        elif self[upath1].wait_for_visible():
            self[upath1].click()
            time.sleep(3)

    def ReturnBtnText(self,upath):
        if self[upath].wait_for_visible():
            return self[upath].text

    def ReturnBtnVisible(self,upath,upath1=None):
        if upath1 is None:
            upath1 = upath
        ui_scene = UIScene(self.app).dump()
        self.app.testcase.log_record("ui_scene", attachments={ui_scene: ui_scene})
        if self[upath].wait_for_visible(raise_error=False):
            self.app.testcase.log_record("当前点击的是第一个")
            return self[upath].wait_for_visible(raise_error=False)
        elif self[upath1].wait_for_visible(raise_error=False):
            self.app.testcase.log_record("当前点击的是第二个")
            return self[upath1].wait_for_visible(raise_error=False)


class ProfileBioMention(BasePanel):
    window_spec = {"path": UPath(type_ == 'UITextEffectsWindow',index=1)}
    def get_locators(self):
        return {
            "mention": {"path": UPath(id_ == "atButton")},
            "mention1": {"path": UPath(id_ == "Mention")},
            "mention_result": {"path": UPath(id_ == "collectionView") / 0 / UPath(id_ == "usernameLabel", depth=5)},
        }

    def ClickVisibleBtn(self,upath,upath1=None):
        if upath1 == None:
            upath1 = upath
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)
        elif self[upath1].wait_for_visible():
            self[upath1].click()
            time.sleep(3)

    def ClickNOVisibleBtn(self,upath):
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)

    def ReturnBtnText(self,upath):
        if self[upath].wait_for_visible():
            return self[upath].text

    def ReturnBtnVisible(self,upath,upath1=None):
        if upath1 is None:
            upath1 = upath
        ui_scene = UIScene(self.app).dump()
        self.app.testcase.log_record("ui_scene", attachments={ui_scene: ui_scene})
        if self[upath].wait_for_visible(raise_error=False):
            self.app.testcase.log_record("当前点击的是第一个")
            return self[upath].wait_for_visible(raise_error=False)
        elif self[upath1].wait_for_visible(raise_error=False):
            self.app.testcase.log_record("当前点击的是第二个")
            return self[upath1].wait_for_visible(raise_error=False)

class WebSiteBlackPopup(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWEZoomWindow')}

    def get_locators(self):
        return {
            "black_popup_title": {"path": UPath(id_ == "titleLabel")},
            "black_popup_desc": {"path": UPath(id_ == "messageLabel")},
            "black_popup_Edit_link": {"path": UPath(id_ == "label")},
        }
    def ReturnBtnText(self,upath):
        if self[upath].wait_for_visible():
            return self[upath].text

    def ClickVisibleBtn(self,upath,upath1=None):
        if upath1 == None:
            upath1 = upath
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)
        elif self[upath1].wait_for_visible():
            self[upath1].click()
            time.sleep(3)

class ProfileEditWin(BasePanel):
    """edit profile window
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "filming_cancel": {"path": UPath(id_ == "Cancel", type_ == "UIButtonLabel")},
            "self_profile_email": {"path": UPath(id_ == "Email")},
            "avatar_btn": {"path": UPath(type_ == "UIButton", label_ == "avatar")},
            "name_cell_label": {"path": UPath(type_ == "UILabel", label_ == 'Name')},
            "nickname_text": {"path": UPath(type_ == "_UITextLayoutCanvasView")},
            "nickname_text_you": {"path": UPath(id_ == "canvasView")},
            "toast_nickname": {"path": UPath(id_ == "messageLabel")},
            "username_cell_label": {"path": UPath(type_ == "UILabel", label_ == 'Username')},
            "bio_cell_label": {"path": UPath(label_ == "Bio")},
            "category_cell_label": {"path": UPath(type_ == "UILabel", label_ == 'Category')},
            "website_cell_label": {"path": UPath(label_ == "Links", visible_ == True)},
            "website_cell_label1": {"path": UPath(label_ == "Website")},
            "website_content_save": {"path": UPath(id_ == "Save", type_ == "UIButtonLabel")},
            "website_content_box": {"path": UPath(id_ == "placeholderLabel")},
            "website_content_value": {"path": UPath(type_ == "UITextField")},
            "website_content_value_close": {"path": UPath(type_ == "UIImageView", visible_ == True)},
            "website_cell_value": {"path": UPath(text_ == "www.aaa.com")},
            "profile_website_value": {"path": UPath(type_ == "AWEAnimatedButton") / UPath(type_ == "UIButtonLabel", depth=5)},
            "profile_website_value1": {"path": UPath(id_ == "linkLabel")},
            "website_back": {"path": UPath(type_ == "TUXNavBarPositionButton")},
            "Instagram": {"path": UPath(text_ == "Instagram")},
            "Instagram_new": {"path": UPath(id_ == "linkLabel")},
            "email_change": {"path": UPath(type_ == "UITextField")},
            "ngo_cell_label": {"path": UPath(type_=='AWESettingsTableViewCell')/UPath(type_ == "UILabel", ~label_ == 'Nonprofit|Non-profit',visible_==True,index=0)},
            "ngo_cell_label1": {"path": UPath(id_ == "titleLabel", text_ == "Non-profit")},
            "ngo_detail_label": {"path": UPath(type_ == "UILabel", id_=='detailLabel')},
            "change_video_label": {"path": UPath(id_ == "Change photo")},
            "pronouns_cell_label": {"path": UPath(label_ == "Pronouns")},
            "set_nickname": {"path": UPath(text_ == "Confirm")},
            "back_btn": {"path": UPath(label_ == 'Back')},
            "take_photo": {"path": UPath(text_ == "Take photo")},
            "ngo_choose_default":{"path":UPath(~text_=='Add organization|Add nonprofit to your profile|Add non-profit to your profile', id_=='detailLabel')},
            "ngo_able_games_foundation_inc": {"path": UPath(~label_ == '^Able Gamers Foundation', id_ == 'detailLabel')},
            "ngo_act_to_change": {"path": UPath(~label_ == '^Act to Change', id_ == 'detailLabel')},
            "ngo_select": {"path": UPath(type_=='TikTokDonationOrigationTableViewCell',visible_==True,index=4)/UPath(type_=='UITableViewCellContentV')},
            "nickname_save": {"path": UPath(text_ == "Save", visible_ == True)},
            "nickname_save_confirm": {"path": UPath(text_ == "Confirm", visible_ == True)},
            "pronouns_input_box": {"path": UPath(type_ == "YYTextSelectionView")},
            "pronouns_input_value": {"path": UPath(id_ == "label")},
            "pronouns_input_close": {"path": UPath(type_ == "UIImageView", visible_ == True)},
            "pronouns_result": {"path": UPath(id_ == "searchTable") / 0 / UPath(id_ == "textLabel", depth=5)},
            "pronouns_save": {"path": UPath(id_ == "Save")},
            "pronouns_edit_profile_value": {"path": UPath(id_ == "tableView") / 6 / UPath(id_ == "detailLabel", depth=5)},
            "pronouns_edit_profile_value1": {"path": UPath(id_ == "tableView") / 5 / UPath(id_ == "detailLabel", depth=5)},
            "edit_profile_back": {"path": UPath(id_ == "IconChevronLeftOffsetLTR")},
            "pronouns_profile_value": {"path": UPath(id_ == "pronounLabel")},
            "pronouns_profile_value1": {"path": UPath(id_ == "titleLabel", type_ == "UILabel", visible_ == True)},
            "username_icon_username_checked": {"path": UPath(id_ == "icon_username_checked")},
            "bio_cell_value": {"path": UPath(id_ == "tableView") / 7 / UPath(id_ == "detailLabel", depth=5)},
            "bio_cell_value1": {"path": UPath(id_ == "tableView") / 6 / UPath(id_ == "detailLabel", depth=5)},
            "add_bio": {"path": UPath(id_ == "+ Add bio")},
            "add_bio1": {"path": UPath(id_ == "Add bio", type_ == "UIButton")},
            "cancel": {"path": UPath(text_ == "Cancel")},
            "bio_box": {"path": UPath(type_ == "_UITextLayoutFragmentView")},
            "bio_save": {"path": UPath(text_ == "Save")},
            "profile_bio_value": {"path": UPath(id_ == "bioLabel")},
            "profile_suggested_account_hide": {"path": UPath(id_ == "Hide")},
            "profile_mention": {"path": UPath(id_ == "bioLabel") / 1},
            "profile_mention1": {"path": UPath(id_ == "Mention")},
            "other_profile_nickname": {"path": UPath(id_ == "titleLabel", visible_ == True)},
            #username illega popup
            "userhead": {"path": UPath(id_ == "mainView") / 0},
            "username": {"path": UPath(id_ == "usernameLabel")},
            "username_!": {"path": UPath(id_ == "warning_username_alert")},
            "popup_desc": {"path": UPath(id_ == "descLabel")},
            "popup_box": {"path": UPath(id_ == "fieldEditor")},
            "popup_close": {"path": UPath(label_ == "Close")},
            "popup_not_now": {"path": UPath(id_ == "Not now")},
            "popup_update": {"path": UPath(id_ == "Update")},
            "other_profile_qianmbi": {"path": UPath(id_ == "icon_profile_name_edit_highlight")},
        }
    def ReturnBtnText(self,upath,upath1=None,raise_error=True):
        if upath1 is None:
            upath1 = upath
        ui_scene = UIScene(self.app).dump()
        self.app.testcase.log_record("ui_scene", attachments={ui_scene: ui_scene})
        if self[upath].wait_for_visible(raise_error=False):
            return self[upath].text
        elif self[upath1].wait_for_visible(raise_error=raise_error):
            return self[upath1].text

    def SendKey(self,upath,value):
        if self[upath].wait_for_visible():
            self.device.send_keys(value)
            time.sleep(3)

    def CheckVisibleBtn(self,upath):
        if self[upath].wait_for_visible(raise_error=False):
            return self[upath].wait_for_visible(raise_error=False)

    def ClickVisibleBtn(self,upath,upath1=None,raise_error=True,time1=3):
        if upath1 == None:
            upath1 = upath
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(time1)
        elif self[upath1].wait_for_visible(raise_error=raise_error):
            self[upath1].click()
            time.sleep(time1)

    def ReturnBtnVisible(self,upath,upath1=None):
        if upath1 is None:
            upath1 = upath
        if self[upath].wait_for_visible(raise_error=False):
            return self[upath].wait_for_visible(raise_error=False)
        elif self[upath1].wait_for_visible(raise_error=False):
            return self[upath1].wait_for_visible(raise_error=False)

    def ClickNoVisibleBtn(self,upath):
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)

    def get_name(self):
        self.confirm_set_nickname()
        self.name_cell_label.click()
        name_edit_view = NameEditTextView(root=self.app)
        name = name_edit_view.text_view.elem_info.get("text")
        time.sleep(1)
        name_edit_view.cancel.click()
        return name

    def get_username(self):
        self.username_cell_label.click()
        username_edit_view = ChangeUsernameView(root=self.app)
        username = username_edit_view.text_field.elem_info.get("text")
        username_edit_view.cancel.click()
        return username

    def get_bio(self):
        self.bio_cell_label.click()
        time.sleep(3)
        bio_edit_view = BioEditTextView(root=self.app)
        bio = bio_edit_view.text_view.elem_info.get("text")
        time.sleep(3)
        bio_edit_view.cancel.click()
        time.sleep(3)
        return bio

    def change_name(self, name):
        self.app.testcase.start_step("进入nickname编辑页面")
        self.name_cell_label.click()
        time.sleep(1)
        name_edit_view = NameEditTextView(root=self.app)
        self.app.testcase.take_screenshot(self.app.get_device(),'screenshot_enter_'+ str(int(time.time())) + ".jpg")
        # logger.info(f'cancel:{name_edit_view.get_ele_by_ocr("Cancel").visible}')
        # if name_edit_view.get_ele_by_ocr("Save").wait_for_existing(timeout=2,raise_error=False):
        #         logger.info(f'Save:{name_edit_view.get_ele_by_ocr("Save").visible}')
        if name_edit_view.can_edit_nickname.wait_for_existing(timeout=3, raise_error=False):
            name_edit_view.text_view.input(name)
            name_edit_view.save.click()
            self.app.testcase.take_screenshot(self.app.get_device(),'screenshot_input_'+ str(int(time.time())) + ".jpg")
            name_popup=SetNicknamePopUp(root=self.app)
            if name_popup.check_popUp():
                name_popup.get_confirm_btn().click()
                return True, True
            else:
                return  True,False
        if name_edit_view.can_not_edit_nicknamee.wait_for_existing(timeout=3, raise_error=False):
            flag_save_btn=True
            if name_edit_view.save_ui_button.wait_for_existing(timeout=2, raise_error=False):
                if name_edit_view.save_ui_button.visible:
                    flag_save_btn = False
            logger.info(f"save 按钮是否隐藏,flag_save_btn:{flag_save_btn}")
            name_edit_view.text_view.click()
            # self.app.testcase.take_screenshot(self.app.get_device(),'toast'+ str(int(time.time())) + ".jpg")
            base_pic =self.app.get_device().screenshot()
            cv_ = CV()
            res1 = cv_.ocr_location("change your nickname because", base_pic)
            res2 = cv_.ocr_location("changed it in the last 7 days", base_pic)
            flags =res2.get('msg') == 'Success' #这个判断不稳先拿掉了
            if not flags:
                logger.info(f"toast 弹出失败,res1:{res1},res2:{res2}")
            time.sleep(5)
            name_edit_view.cancel.click()
            return False,flag_save_btn

    def confirm_set_nickname(self):
        if self["set_nickname"].wait_for_existing(timeout=3, raise_error=False):
            self["set_nickname"].click()

    def change_bio(self, bio):
        self.bio_cell_label.click()
        time.sleep(3)
        bio_edit_view = BioEditTextView(root=self.app)
        # bio_edit_view.text_view.input(bio)
        self.device_input(bio_edit_view.text_view, bio)

    def change_website(self):
        if self.website_cell_label.wait_for_visible(timeout=5, interval=1, raise_error=True):
            self.website_cell_label.click()
        if self.website_cell_label1.wait_for_visible(timeout=5, interval=1, raise_error=True):
            self.website_cell_label1.click()

    def change_email(self):
        if self.email_change.wait_for_visible(timeout=5, interval=1, raise_error=True):
            self.email_change.click()

    def back(self):
        self.back_btn.click()

    def avatar_btn(self):
        self["avatar_btn"].click()
        time.sleep(2)

    def take_photo(self):
        self["take_photo"].click()
        time.sleep(2)

    def change_video_check(self):
        if self["change_video_label"].wait_for_visible(timeout=5, raise_error=True):
            self["change_video_label"].click()
        # time.sleep(1)
        # video_change = OtherPanle(root=self.app)
        # self.back()
        # video_change.close_video_album_click()

    def init_ngo_edit(self):
        flag = self.ngo_choose_default_existed()
        logger.info(f"is default:{flag}")
        if not flag:
            time.sleep(2)
            if self["ngo_cell_label"].wait_for_visible(timeout=5, raise_error=False):
                self["ngo_cell_label"].click()
            elif self["ngo_cell_label1"].wait_for_visible(timeout=5):
                self["ngo_cell_label1"].click()
            ngo_change_panel = NgoChangePanel(root=self.app)
            return ngo_change_panel.click_ngo_remove()
        else:
            logger.info(f"不需要初始化")
            return True

    def open_ngo_panel(self,isdelete=False):
        flag=self.ngo_choose_default_existed()
        logger.info(f"is default:{flag}, ngo_cell_label：{self['ngo_cell_label'].text}")

        time.sleep(2)
        self["ngo_cell_label"].click()
        ngo_change = NgoChangePanel(root=self.app)
        if isdelete:
            return  ngo_change.click_ngo_remove(),""
        if not flag:
            ngo_change.click_ngo_change_organization()
        time.sleep(2)
        self.app.testcase.take_screenshot(self.app.get_device(), 'screenshot_select_organization' + str(int(time.time())) + ".jpg")
        other_page = otherPagePannel(root=self.app)
        if other_page.get_select_organization().wait_for_existing(timeout=3, raise_error=False):
            page_flag=True
            if flag:
              ngo_pannel=  other_page.get_ngo_1st_click()
            else:
                ngo_pannel= other_page.get_ngo_2rd_click()
                self.app.testcase.take_screenshot(self.app.get_device(),
                                                  'screenshot_select_organization_update' + str(int(time.time())) + ".jpg")
                page_flag=other_page.change_btn_click()
                logger.info(f"page_flag:{page_flag}, ngo_pannel{ngo_pannel}")
            return page_flag,ngo_pannel
        else:
            return False,""

    def get_ngo_label(self):
        return self['ngo_detail_label'].elem_info.get("label")

    def ngo_choose_default_existed(self):
        return self['ngo_choose_default'].wait_for_existing(timeout=3, raise_error=False)

    def ngo_able_games_foundation_inc_existed(self):
        return self['ngo_able_games_foundation_inc'].wait_for_existing(timeout=3, raise_error=False)

    def ngo_act_to_change_existed(self):
        return self['ngo_act_to_change'].wait_for_existing(timeout=3, raise_error=False)

    def open_pronouns_panel(self):
        self["pronouns_cell_label"].click()

    def get_ngo_title_exist(self, ngo_label):
        publish_btn = Control(root=self, path=UPath(label_ == ngo_label, id_ == 'detailLabel'))
        return  publish_btn.wait_for_existing(timeout=5, raise_error=True)


class NgoChangePanel(BasePanel):

    window_spec = {"path": UPath(controller_ == "UIAlertController")}

    def get_locators(self):
        return {
            "ngo_remove": {"path": UPath(type_ == "UILabel", text_ == "Remove")},
            "change_organization": {"path": UPath(type_ == "UILabel", text_ == "Change organization")},
            "change_organization1": {"path": UPath(id_ == "contentView", visible_ == True) / 0 / UPath(id_ == "stackView", depth=5) / 0 / UPath(id_ == "label", depth=5)},
        }

    def click_ngo_remove(self):
        if self["ngo_remove"].wait_for_visible(timeout=5, raise_error=False):
            self["ngo_remove"].click()
            return True
        else:
            logger.info(f"remove btn 未出现")
            return False

    def click_ngo_change_organization(self):
        if self["change_organization"].wait_for_visible(timeout=5, raise_error=False):
            self["change_organization"].click()
        elif self["change_organization1"].wait_for_visible(timeout=5, raise_error=True):
            self["change_organization1"].click()



class BaseProfileEditView(Window):
    """base profile edit view
    """
    def get_locators(self):
        universal_locators = {
            "cancel": {"path": UPath(text_ == 'Cancel')},
            "save": {"path": UPath(text_ == 'Save')},
            "save_ui_button": {"path": UPath(id_ == '(Save)',type_=='UIButton')},

            "can_edit_nickname": {
                "path": UPath(type_ == 'UILabel', label_ == 'Your nickname can only be changed once every 7 days.')},
            "can_not_edit_nicknamee": {"path": UPath(type_ == 'UILabel',
                                                     ~label_ == 'Your nickname can only be changed every 7 days. You can change it again after')},
            "toast_nickname": {"path": UPath(type_ == 'TUXToast') / UPath(type_ == 'UILabel', id_ == '(messageLabel)',
                                                                          label_ == 'You can’t change your nickname because you just changed it in the last 7 days.')},
        }
        universal_locators.update(self.get_page_locators())
        return universal_locators

    def get_page_locators(self):
        return {}
    def get_ele_by_ocr(self, text):
        return CVElement(path=UPath(ocr_ == text), root=self.app)


class NameEditTextView(BaseProfileEditView):
    """name edit text view
    """
    window_spec = {"path": UPath(type_ == 'AWEProfileEditTextViewController')}

    def get_page_locators(self):
        return {
            "text_view": {"path": UPath(type_ == 'UITextView',text_!="")},
        }


class BioEditTextView(BaseProfileEditView):
    """bio edit text view
    """
    window_spec = {"path": UPath(type_ == 'AWEProfileEditTextViewController')}

    def get_page_locators(self):
        return {
            "text_view": {"path": self.get_text_view_upath()}
        }

    def get_text_view_upath(self):
        driver = self.driver
        upath = UPath(type_ == 'UITextView')
        ids = driver.get_element_ids(upath)
        if len(ids) > 1:
            upath = UPath(type_ == 'UITextView', index=1)
        return upath


class ChangeUsernameView(BaseProfileEditView):
    """TM change username view
    """
    window_spec = {"path": UPath(type_ == 'TMChangeUsernameViewController')}

    def get_page_locators(self):
        return {
            "text_field": {"path": UPath(type_ == 'UITextField')}
        }

class ChangeWebSiteView(BaseProfileEditView):
    """TM change website view
    """
    window_spec = {"path": UPath(controller_ == 'AWEProfileEditTextViewController')}

    def get_page_locators(self):
        return {
            "text_view": {"path": UPath(type_ == 'UITextView')}
        }

class PhotoEditView(BasePanel):
    """photo edit view
    """
    window_spec = {"path": UPath(type_ == 'UIWindow', visible_ == True)}

    def get_locators(self):
        return {
            "take_photo": {"path": UPath(text_ == "Take photo")}
        }

    def take_photo(self):
        if self.existing:
            return self["take_photo"].click()
        return False
