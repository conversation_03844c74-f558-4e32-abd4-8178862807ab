# -*- coding: utf8 -*-
import time

from business.vod.common.ios.base_panel import *
from uibase.upath import *

class ClearStorageAndRestartAlert(Window):
    window_spec = {"path": UPath(type_ == 'UILabel', label_ == 'Clear Storage and Restart')}

    def do_it(self):
        control = Control(root=self.app, path=UPath(text_ == 'Do it'))
        if control.wait_for_visible(timeout=5, raise_error=False):
            return control.click()


class DebugPanel(BasePanel):
    """ debug panel
    """
    window_spec = {"path": UPath(~type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            #   Panel_Controller
            "panel_controller": {"type": Control, "path": UPath(type_ == 'AWEDebugSettingViewController')},
            #   info
            "deviceID": {"type": Control, "path": UPath(type_ == 'AWEDebugTableViewCell', text_ == 'Device ID')/UPath(type_ == 'UILabel', index=0)},
            #   Search
            "search_input": {"type": Control, "path": UPath(type_ == 'UISearchBarTextField', visible_ == True)},
            "debug_search_input": {"type": Control, "path": UPath(type_ == 'UISearchBarTextField', visible_ == True)},
            "search_clear": {"type": Control, "path": UPath(type_ == '_UITextFieldClearButton', index=0)},
            "clear_storage": {
                "root": "panel_controller",
                "path": UPath(type_ == 'UITableViewLabel', ~label_ == 'Clear (s|S)torage.*', index=0)},
            "clear_webview_cookies": {
                "root": "panel_controller",
                "path": UPath(label_ == 'Clear webview cookies', index=0)},
            "clearing": {"type": Control, "path": UPath(id_ == 'hudView')},
            "back": {"type": Control, "path": UPath(id_ == '(backButton)')},
            #   reset to new user
            "reset_to_new_user_entrance": {"type": Control, "path": UPath(id_ == '(Reset to new user)', index=0)},
            # reset NUJ
            "reset_nuj_flow":{"root": "panel_controller",
                "path": UPath(label_ == 'New User Journey Flow', index=0)},
            "execute_button": {"type": Control, "path": UPath(text_ == "Execute")},
            "back_button": {"path": UPath(label_ == "Back")},

            "ab": {"path": UPath(id_ == "AB Test & Settings", type_ == "UIButtonLabel")},
            "debug": {"path": UPath(text_ == "Debug Tools",visible_ == True)},

            # poi config
            "poi_config": {"path": UPath(type_ == "AWEABTestMockTableViewCell", visible_ == True)/UPath(id_ == "titleLabel")},
            "edit_json": {"path": UPath(text_ == "Edit as JSON")},
            "clear_mock": {"path": UPath(id_ == "Clear Mocked Value", type_ == "UIButton")},
            "enable": {"path": UPath(id_ == "collectionView")/0/UPath(type_ == "UIImageView", visible_ == True)},
            "edit_content": {"path": UPath(id_ == "textView")},
            "input_frame": {"path": UPath(id_ == "Search by name or key")},

            "debug_tools": {"path": UPath(text_ == "Debug Tools")},
            "clear_mock_region": {"path": UPath(id_ == "Device ID", text_ == "Clear mocked regions")},
            "left_btn": {"path": UPath(controller_ == "AWEABTestMockFilteredViewController") / UPath(
                id_ == "leftBtnsContainerView") / UPath(id_ == "null")},
            "go_back": {"path": UPath(type_ == "_UIButtonBarButton")},
            "ok": {"path": UPath(id_ == "actionContentView")},
            "poi_config_back": {"path": UPath(id_ == "null", type_ == "_UIModernBarButton")},
            "enable_mock": {"path": UPath(id_ == "actionContentView", label_ == "OK")},
            "yes": {"path": UPath(text_ == "YES")},
            # AB Test & Settings
            "ABTest&Setting": {"type": Control,
                               "path": UPath(type_ == 'UIButtonLabel', label_ == 'AB Test & Settings')},
            "ABTest&Setting_search_bar": {"type": Control,
                                          "path": UPath(type_ == "UISearchBarTextField", visible_ == True)},

            "chunk_debug_tag": {"type": Control,
                                "path": UPath(type_ == 'AWEABTestMockTableViewCell', visible_ == True) / UPath(
                                    label_ == 'chunk_debug_tag')},
            "chunk_debug_tag_desc": {"type": Control,
                                     "path": UPath(type_ == 'AWEABTestMockTableViewCell', visible_ == True) / UPath(
                                         label_ == '✎ YES')},
            "chunk_debug_tag_yes": {"type": Control, "path": UPath(label_ == 'YES')},
            "chunk_debug_tag_confirm": {"type": Control, "path": UPath(text_ == 'OK')},
            "profile_platfrom_self": {"type": Control, "path": UPath(type_ == "AWEABTestMockTableViewCell", visible_ == True) / UPath(id_ == "titleLabel", depth=5)},
            "profile_platfrom_self1": {"type": Control, "path": UPath(id_ == "collectionView") / 2 / UPath(id_ == "titleLabel", depth=5)},
            "profile_platfrom_self_ok": {"type": Control, "path": UPath(text_ == "OK")},
            "user_recommend_popup_debug": {"type": Control, "path": UPath(type_ == "UITableView") / 5 / UPath(type_ == "UITableViewLabel", depth=5)},
            "user_recommend_popup_debug1": {"type": Control, "path": UPath(id_ == "User Recommend Popup Debug")},
            "clear_popup_storage_data": {"type": Control, "path": UPath(id_ == "Clear popup storage data")},
            "feed_user_recpmmend_dev_tool_swith": {"type": Control, "path": UPath(id_ == "switchWellImageViewContainer", visible_ == True)},
            "feed_user_recpmmend_dev_tool_btn": {"type": Control, "path": UPath(id_ == "Feed Big Card")},
            "Force_Show_User_Recommend_Big_Card": {"type": Control, "path": UPath(id_ == "forceShowBigCardSwitch") / UPath(id_ == "switchWellImageViewContainer", depth=5)},
            "User_Recommend_Auto_Reset_Frequency": {"type": Control, "path": UPath(id_ == "autoResetFrequencySwitch") / UPath(id_ == "switchWellImageViewContainer", depth=5)},
            "photo_debug_tab": {"type": Control, "path": UPath(id_ == "switchWellImageViewContainer", visible_ == True)},
            "debug_tools1": {"type": Control, "path": UPath(text_ == "Debug Tools")},
            "debug_tools1_box": {"type": Control, "path": UPath(id_ == "Search")},
            "edit_storage_switch": {"type": Control, "path": UPath(id_ == "switchWellImageViewContainer", visible_ == True)},
        }
    def clear_add_bio(self):
        # self["debug_tools1"].click()
        # time.sleep(3)
        self["debug_tools1_box"].click()
        time.sleep(3)
        self["debug_tools1_box"].input("edit storage")
        time.sleep(3)
        self["edit_storage_switch"].click()
        time.sleep(3)

    def download_search_debug_tool(self):
        self["ABTest&Setting"].click()
        time.sleep(3)
        self["ABTest&Setting_search_bar"].click()
        time.sleep(3)
        self["ABTest&Setting_search_bar"].input("chunk_debug_tag")
        time.sleep(3)
        # self.device_input(self.search_input, "chunk_debug_tag")
        time.sleep(3)
        if not self["chunk_debug_tag_desc"].wait_for_visible(timeout=3, raise_error=False):
            self["chunk_debug_tag"].click()
            time.sleep(3)
            self["chunk_debug_tag_yes"].click()
            time.sleep(3)
        if self["chunk_debug_tag_confirm"].wait_for_visible(timeout=3, raise_error=False):
            self["chunk_debug_tag_confirm"].click()

    def ui_checking(self):
        """检查界面元素内容
        """
        pass

    def wait_for_panel_loading(self):
        """判断是否正确进入本界面，关键元素加载显示正常
        """
        pass

    def reset_did(self):
        self.device_input(self.search_input, "Reset to new user")
        time.sleep(2)
        self["reset_to_new_user_entrance"].wait_for_existing(timeout=3)
        print(self["reset_to_new_user_entrance"].rect)
        self["reset_to_new_user_entrance"].click()
        reset_did_panel = ResetDidPanel(root=self.app)
        if not reset_did_panel.wait_for_existing(raise_error=False):
            self["reset_to_new_user_entrance"].click()
        reset_did_panel.reset_did()

    def revert_did(self):
        self.device_input(self.search_input, "Reset to new user")
        self["reset_to_new_user_entrance"].wait_for_existing(timeout=3)
        self["reset_to_new_user_entrance"].click()
        reset_did_panel = ResetDidPanel(root=self.app)
        reset_did_panel.revert_did()

    def get_did(self):
        return self["deviceID"].text

    def clear_data(self):
        self["debug_tools"].click()
        # self["search_input"].click()
        # self["search_input"].input("cookies")
        self.device_input(self.search_input, "clear webview ")
        time.sleep(2)
        self["clear_webview_cookies"].click()
        time.sleep(2)
        # self["search_clear"].click()
        # self["search_input"].input("storage")
        self.device_input(self.search_input, "storage")
        time.sleep(2)
        self.scroll(distance_y=200)
        self["clear_storage"].click()
        clear_storage_alert = ClearStorageAndRestartAlert(root=self.app)
        clear_storage_alert.do_it()
        time.sleep(8)

    def clear_nuj(self):

        self.device_input(self.search_input, "New User Journey")
        time.sleep(2)
        self["reset_nuj_flow"].click()
        self["execute_button"].click()
        self["execute_button"].click()
        self["execute_button"].click()
        # time.sleep(2)
        # self["back_button"].click()
        # self.device_input(self.search_input, "storage ")
        # time.sleep(2)
        # self["clear_storage"].click()
        # clear_storage_alert = ClearStorageAndRestartAlert(root=self.app)
        # clear_storage_alert.do_it()

    def reconnect_ET_platform(self):
        self.device_input(self.search_input, "Connect ET log platform")
        switch = Control(root=self.app, path=UPath(type_ == 'AWEDebugTableViewCell', visible_ == True)/UPath(type_ == 'UISwitch'))
        switch.click()
        time.sleep(2)
        switch.click()
        time.sleep(2)
        self["back"].click

    def set_poi_config(self, value):
        self["ab"].click()
        try:
            rect = self["search_input"].rect
            self.app.testcase.device.click(rect.center[0], rect.center[1])
            self["search_input"].wait_for_ui_stable()
            self["search_input"].send_keys("poi_config")
        except Exception as e:
            logger.info(f"input {value} error, reason:{e}")
        self["poi_config"].wait_for_existing(timeout=3)
        self["poi_config"].click()
        self["edit_json"].click()
        time.sleep(1)
        self.app.testcase.take_screenshot(self.app.testcase.device, "enable.jpg")
        if self["enable_mock"].wait_for_existing(timeout=3, raise_error=False):
            self["enable_mock"].click()
        self["enable"].wait_for_existing(timeout=3, raise_error=False)
        self["enable"].click()
        if self["ok"].wait_for_existing(timeout=3, raise_error=False):
            self["ok"].click()
        self["edit_content"].text = 1
        time.sleep(1)
        self.app.testcase.take_screenshot(self.app.testcase.device, "edit_after.jpg")
        # time.sleep(5)
        # if self["go_back"].wait_for_existing(timeout=1, raise_error=False):
        #     self["go_back"].click()
        # if self["poi_config_back"].wait_for_existing(timeout=1, raise_error=False):
        #     self["poi_config_back"].click()
        # if self["go_back"].wait_for_existing(timeout=1, raise_error=False):
        #     self["go_back"].click()
        # if self["left_btn"].wait_for_existing(timeout=1, raise_error=False):
        #     self["left_btn"].click()
        #     time.sleep(1)
        # self["back"].click()

    def open_lynx_profile_toggle(self):
        self["ab"].click()
        self.input_text("profile message cell use lynx")
        self["poi_config"].wait_for_existing(timeout=3)
        self["poi_config"].click()
        self["yes"].click()

    def input_text(self, text):
        self["ab"].click()
        try:
            rect = self["search_input"].rect
            self.app.testcase.device.click(rect.center[0], rect.center[1])
            self["search_input"].wait_for_ui_stable()
            self["search_input"].send_keys(text)
        except Exception as e:
            logger.info(f"input {text} error, reason:{e}")

    def input_json_ab(self, value):
        if self["edit_json"].wait_for_visible(timeout=3, raise_error=False):
            self["edit_json"].click()
        time.sleep(1)
        self.app.testcase.take_screenshot(self.app.testcase.device, "enable.jpg")
        if self["enable_mock"].wait_for_existing(timeout=3, raise_error=False):
            self["enable_mock"].click()
        if self["enable"].wait_for_existing(timeout=3, raise_error=False):
            self["enable"].click()
        if self["ok"].wait_for_existing(timeout=3, raise_error=False):
            self["ok"].click()
        self["edit_content"].text = value
        time.sleep(1)
        self.app.testcase.take_screenshot(self.app.testcase.device, "edit_after.jpg")
        back_btn = Control(root=self.app, path=UPath(~type_ == "_UIBackButtonContainerView|_UIButtonBarButton"))
        if back_btn.wait_for_existing(timeout=3):
            back_btn.click()
            time.sleep(0.5)
            self.app.testcase.take_screenshot(self.app.testcase.device, "back_result.jpg")

    def open_tako_ab(self, value=1):
        if self["ab"].wait_for_existing(timeout=8):
            self["ab"].click()
        self.input_text("search_chatgpt_bot_enable")
        self["poi_config"].wait_for_existing(timeout=3)
        self["poi_config"].click()
        self.input_json_ab(value)

    def EnterIntoABLibra(self,libra_key):
        if self["ab"].wait_for_visible():
            self["ab"].click()
            time.sleep(5)
            rect = self["search_input"].rect
            self.app.testcase.device.click(rect.center[0], rect.center[1])
            self["search_input"].wait_for_ui_stable()
            self["search_input"].send_keys(libra_key)
            time.sleep(5)
        if self["profile_platfrom_self"].wait_for_visible():
            self["profile_platfrom_self"].click()
            time.sleep(3)
        if self["profile_platfrom_self1"].wait_for_visible():
            self["profile_platfrom_self1"].click()
            time.sleep(3)
        if self["profile_platfrom_self_ok"].wait_for_visible(raise_error=False):
            self["profile_platfrom_self_ok"].click()
            time.sleep(3)

    def EnterIntoRecommendPopupDebeg(self,libra_key):
        if self["debug"].wait_for_visible():
            self["debug"].click()
            time.sleep(5)
            rect = self["debug_search_input"].rect
            self.app.testcase.device.click(rect.center[0], rect.center[1])
            self["debug_search_input"].wait_for_ui_stable()
            self["debug_search_input"].send_keys(libra_key)
            time.sleep(5)
        if self["user_recommend_popup_debug"].wait_for_visible(raise_error=False):
            self["user_recommend_popup_debug"].click()
            time.sleep(3)
        elif self["user_recommend_popup_debug1"].wait_for_visible():
            self["user_recommend_popup_debug1"].click()
            time.sleep(3)
        if self["clear_popup_storage_data"].wait_for_visible():
            self["clear_popup_storage_data"].click()
            time.sleep(3)

    def EnterIntoRecommendCardDebeg(self,libra_key):
        if self["debug"].wait_for_visible():
            self["debug"].click()
            time.sleep(5)
            rect = self["debug_search_input"].rect
            self.app.testcase.device.click(rect.center[0], rect.center[1])
            self["debug_search_input"].wait_for_ui_stable()
            self["debug_search_input"].send_keys(libra_key)
            time.sleep(5)
        if self["feed_user_recpmmend_dev_tool_swith"].wait_for_visible():
            self["feed_user_recpmmend_dev_tool_swith"].click()
            time.sleep(3)
        if self["feed_user_recpmmend_dev_tool_btn"].wait_for_visible():
            self["feed_user_recpmmend_dev_tool_btn"].click()
            time.sleep(3)
        if self["Force_Show_User_Recommend_Big_Card"].wait_for_visible():
            self["Force_Show_User_Recommend_Big_Card"].click()
            time.sleep(3)
        if self["User_Recommend_Auto_Reset_Frequency"].wait_for_visible():
            self["User_Recommend_Auto_Reset_Frequency"].click()
            time.sleep(3)

    def photo_click_debug(self, libra_key):
        if self["debug"].wait_for_visible():
            self["debug"].click()
            time.sleep(5)
            rect = self["debug_search_input"].rect
            self.app.testcase.device.click(rect.center[0], rect.center[1])
            self["debug_search_input"].wait_for_ui_stable()
            self["debug_search_input"].send_keys(libra_key)
            time.sleep(5)
        if self["photo_debug_tab"].wait_for_visible():
            self["photo_debug_tab"].click()
            time.sleep(3)

class ResetDidPanel(BasePanel):

    window_spec = {"path": UPath(type_ == 'UIWindow', visible_ == True)}

    def get_locators(self):
        return {
            "reset_did": {"type": Control, "path": UPath(text_ == 'av_beauty_progress_reset', index=0)},
            "reset_did_revert": {"type": Control, "path": UPath(text_ == 'Revert')},
        }

    def reset_did(self):
        self["reset_did"].wait_for_existing(timeout=3)
        self["reset_did"].click()

    def revert_did(self):
        self["reset_did_revert"].wait_for_existing(timeout=3)
        self["reset_did_revert"].click()


class AnyWhereDoorAuthorizePanel(BasePanel):
    """AnyWhereDoor Authorize Panel
        """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            'AnyWhereDoorAuthorizeWebview': {'type': AnyWhereDoorAuthorizeWebview, 'path': UPath(type_ == 'WKScrollView', visible_ == True)},
        }

    def authorize(self):
        return self["AnyWhereDoorAuthorizeWebview"].authorize()


class AnyWhereDoorAuthorizeWebview(Webview):
    view_spec = {"title": "任意门"}

    def get_locators(self):
        return {
            "authorize": {"type": WebElement, "path": UPath(~class_ == 'submitBtn')},
            "authorize_success": {"type": WebElement, "path": UPath(class_ == 'semi-toast-content')},
        }

    def authorize(self):
        time.sleep(2)
        self["authorize"].click()
        return self["authorize_success"].wait_for_existing(timeout=15, raise_error=False)

class edit_storage(BasePanel):
    """ debug panel
    """
    window_spec = {"path": UPath(type_ == "TTKEditStorageFloatView")}

    def get_locators(self):
        return {
            "edit_storage_popup": {"type": Control, "path": UPath(id_ == "button")},
        }

    def ClickEditStorage(self):
        self["edit_storage_popup"].click()

class edit_storage_page(BasePanel):
    """ debug panel
    """
    window_spec = {"path": UPath(type_ == "AWEZoomWindow", visible_ == True)}

    def get_locators(self):
        return {
            "edit_storage_btn": {"type": Control, "path": UPath(text_ == "edit storage")},
            "edit_storage_box": {"type": Control, "path": UPath(id_ == "search key")},
            "edit_storage_box_1": {"type": Control, "path": UPath(id_ == "label", visible_ == True)},
            "edit_storage_box_1_delete": {"type": Control, "path": UPath(id_ == "deleteRow", type_ == "UIButtonLabel")},
        }

    def ClickEditStorage(self):
        self["edit_storage_btn"].click()
        time.sleep(3)
        self["edit_storage_box"].click()
        time.sleep(3)
        self["edit_storage_box"].send_keys("com.aweme.profile.AWEEmptyBioHintDidClickKey")
        time.sleep(3)
        self["edit_storage_box_1"].click()
        time.sleep(3)
        self["edit_storage_box_1_delete"].click()
