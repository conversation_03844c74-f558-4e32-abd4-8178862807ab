# -*- coding: utf8 -*-
import time

from .base_panel import *
from .basic_views import KeyboardWindow

class LoginPanel(BasePanel):
    """ login panel
    """
    window_spec = {"path": UPath(type_ == 'AWELoginWindow')}

    def get_locators(self):
        return {
            #   Panel_Controller
            "panel_controller": {"type": Control, "path": UPath(~type_ == 'AWEI18NLoginViewController|AWEI18NSignUpViewController', visible_ == True)},
            #   Login_panel
            "login_title": {"type": Control, "path": UPath(id_ == "titleLabel", text_ == "Log in to TikTok", visible_ == True)},
            "use_phone": {"type": Control, "path": UPath(type_ == 'UIStackView', visible_ == True)/0},
            "login": {"type": Control, "path": UPath(type_ == "UIView") / UPath(type_ == "TTTAttributedLabel", visible_ == True)},
            "phone_email_icon": {"type": Control, "path": UPath(id_ == "iconPhoneEmail", visible_ == True)},
            "phone_email_button": {"type": Control, "path": UPath(id_ == "phoneEmailButton")},
            "phone_email_text": {"type": Control, "path": UPath(text_ == "Use phone / email / username")},
            "use_facebook_account": {"type": Control, "root": "panel_controller", "path": UPath(type_ == 'UIStackView', visible_ == True)/1},
            "use_apple_account": {"type": Control, "root": "panel_controller", "path": UPath(type_ == 'ASAuthorizationAppleIDButton', visible_ == True)},
            "use_google_account": {"type": Control, "root": "panel_controller", "path": UPath(type_ == 'UIStackView', visible_ == True)/3},
            "use_twitter_account": {"type": Control, "root": "panel_controller", "path": UPath(type_ == 'UIStackView', visible_ == True)/4},
            "use_ins_account": {"type": Control, "root": "panel_controller", "path": UPath(type_ == 'UIStackView', visible_ == True)/5},
            "back_1": {"type": Control, "root": "panel_controller", "path": UPath(id_ == 'icon_close_panel')},
            "faq_1": {"type": Control, "root": "panel_controller", "path": UPath(type_ == 'TTKOnboardingNavigationBar')/1},
            "switch_username": {"path": UPath(type_ == 'UIButtonLabel', ~label_ == 'Email / Username|电子邮件/用户名')},
            "username": {"path": UPath(type_ == "UIStackView", visible_ == True) / UPath(type_ == "TMLoginInputView", index=0) /
            UPath(type_ == "TikTokAccountInfoTextField")},
            "password": {"path": UPath(type_ == "UIStackView", visible_ == True) / UPath(type_ == "TMLoginInputView", index=1) /
            UPath(type_ == "TikTokAccountInfoTextField")},
            "login_btn": {"path": UPath(type_ == "TMLoginButton", visible_ == True)},
            "verify_failed_text": {"path": UPath(id_ == 'errorHintLabel')},
            "region_label": {"path": UPath(id_ == 'prefixRegionLabel')},
            "code": {"type": VerificationCodeInputView,
                                 "path": UPath(type_ == 'TMVerificationCodeInputView')/UPath(type_ == 'UIStackView', visible_ == True)},
            "code_input_page_title": {"path": UPath(type_ == 'TMVerificationCodeView')/UPath(id_ == 'titleLabel')},
            "force_login_screen_title_text": {"path": UPath(~controller_ == "AWEI18NSignUpViewController|AWEI18NLoginViewController") / UPath(type_ == "UIScrollView") / 0 / 0},
            "desc_text": {"path": UPath(id_ == '(descLabel)', type_ == 'UILabel')},
            "force_login_screen_terms_label": {"path": UPath(controller_ == "AWEI18NSignUpViewController")/ 2 },
            "us_force_login_screen_terms_label":{"path": UPath(controller_ == "AWEI18NLoginViewController") / UPath(type_ == "UIScrollView") / 0 / 0},
            "signup_screen_terms_label": {"path": UPath(type_ == "TMSignUpProtocolAttributedLabel")},
            "us_signup_screen_terms_label": {"path": UPath(controller_ == "AWEI18NSignUpViewController")/ 2},
            "feedback_icon": {"path": UPath(id_ == "IconQuestionMarkCircleLTR") / UPath(type_ == "UIImageView")},
            "expand_options": {"path": UPath(id_ == "list_more_onLight",visible_ == True)},
            "us_use_phone_email_login_button1": {"path" : UPath(controller_ == "AWEI18NLoginViewController") / UPath(type_ == "UIStackView") / 0 / UPath(id_ == "titleLabel")},
            "us_use_phone_email_login_button2": {
                "path": UPath(controller_ == "AWEI18NLoginViewController") / UPath(type_ == "UIStackView") / 5 / UPath(
                    id_ == "titleLabel")},
            "save_login_info_text" : {"path": UPath(id_ == "emailSaveLoginLabel")},
            #previous login tooltip on login panel
            "prev_login_tooltip" : {"path" : UPath(id_ == "prevLoginPlatformLabel")},

            #   Sign_up
            "signup": {"path": UPath(id_ == "signUpLabel")},
            "signup_screen_title_text": {"path": UPath(controller_ == "AWEI18NSignUpViewController") / UPath(id_ == "Sign up for TikTok")},
            "signup_with_phone_email_button": {"path": UPath(controller_ == "AWEI18NSignUpViewController") / UPath(type_ == "UIStackView") / 0 / UPath(id_ == "titleLabel")},
            "phone_sign_up_disclaimer": {"path": UPath(id_ == "phoneAgreementLabel")},
            "save_login_info": {
                "path": UPath(id_ == "Save login info on your iCloud devices to log in automatically next time.")},
            "email_sign_up_disclaimer": {"path": UPath(type_ == "TMAutoEndEditingView", visible_ == True) / UPath(type_ == "UIStackView") / 1},
            "gb_email_trending_text": {"path": UPath(type_ == "TMAutoEndEditingView", visible_ == True) / UPath(type_ == "UIStackView") / 1 / UPath(type_ == "UILabel")},
            "gb_email_sign_up_disclaimer": {
                "path": UPath(type_ == "TMAutoEndEditingView", visible_ == True) / UPath(type_ == "UIStackView") / 2 },
            "email_signup_tab": {"path": UPath(id_ == "Email", type_ == "AWESlidingTabButton")},
            "email_edit_box": {"path": UPath(type_ == "UIFieldEditor")},
            "email_input_confirm": {"path": UPath(type_ == "TMLoginButton", visible_ == True)},
            "password_edit_box": {"path": UPath(id_ == "Enter password")},
            "create_password_next_button": {"path": UPath(id_ == "createPasswordButton")},
            # nickmane screen
            "nick_name_title": {"path": UPath(controller_ == "TTKCreateNicknameViewController") / 0},
            "nick_name_subtitle": {"path": UPath(controller_ == "TTKCreateNicknameViewController") / 1},
            "skip": {"path": UPath(text_ == "Skip")},
            "signup": {"type": Control, "path": UPath(type_ == 'TTTAttributedLabel', label_ == 'Don’t have an account? Sign up')},
            "signup_screen_title_text": {"path": UPath(id_ == '(titleLabel)', label_ == 'Sign up for TikTok')},
            #"signup_with_phone_email_button": {"path": UPath(id_ == "(registerButton)") / UPath(id_ == "(titleLabel)", depth=16)},

            #   example
            # "": {"type": Control, "path": },
            "back_btn": {"path": UPath(label_ == "Go back")},
            "close_btn": {"path": UPath(label_ == 'icon close panel')},
            "close_popup": {"path": UPath(id_ == "IconXMarkSmall")},
            "forgot_password": {"path": UPath(text_ == "Forgot password?", visible_ == True)},
            "reset_password_with_phone_number": {"path": UPath(type_ == "UILabel", label_ == "Phone number")},
            "reset_password_with_email": {"path": UPath(type_ == "UILabel", label_ == "Email")},
            "reset_password_with_cancel": {"path": UPath(type_ == "UILabel", label_ == "Cancel")},
            "login_button": {"path": UPath(id_ == '(loginButton)')},
            "next_btn": {"path": UPath(type_ == 'TUXButton')},
            "text_field": {"path": UPath(type_ == 'UITextField')},
            "date_picker": {"path": UPath(type_ == 'UIDatePicker')},
            "year_picker": {
                "root": "date_picker",
                "path": UPath(type_ == "UIAccessibilityPickerComponent", index=0)
            },
            "year_picker_scroll":{"path": UPath(type_ == "_UIDatePickerView") / 0 / 2 / 0 /
            UPath(type_ == "UIPickerTableView") / 2 / UPath(type_ == "UIDatePickerContentView")},

            "skip_inhouse_only": {"path": UPath(type_ == 'UIButton', label_ == 'skip inhouse only')},
            "close": {"path": UPath(label_ == "Close")},
            "close_new": {"path": UPath(id_ == "icon_close_panel", type_ == "UIImageView")},
            "login_mode_view": {"path": UPath(id_ == "contentViewForScroll") / UPath(id_ == "stackView", visible_ == True)},
            "back_btn_login": {"path": UPath(~label_ == 'Go back|Back|上一步|返回')},
            "add_another_account": {"path": UPath(id_ == "Add another account")},
            "add_another_account_button": {"path": UPath(id_ == "addAccountButton")},
            "log_in": {"type": Control, "path": UPath(id_ == "loginLabel", ~text_ == ".*Log in", visible_ == True)},

        }




    def prev_login_tooltip(self):

        if self['prev_login_tooltip'].wait_for_existing(timeout=3, raise_error=False,):
            return True
        else:
            return False

    def save_login_info_text(self):
        return self['save_login_info_text'].text

    def login_by_email(self, email, password):
        self["switch_username"].click()
        time.sleep(1)
        self["username"].input(email)
        time.sleep(1)
        self["password"].input(password)
        self["login_btn"].click()
        time.sleep(5)

    def us_use_phone_email_login_button(self):
        if self.prev_login_tooltip():
            return self['us_use_phone_email_login_button2'].click()
        else:
            return self['us_use_phone_email_login_button1'].click()

    def expand_options(self):
        if self["expand_options"].wait_for_existing(timeout=3, raise_error=False):
            return self["expand_options"].click()

    #Added check for prev login tooltip
    def login_option(self, pos):
        if self.prev_login_tooltip() == False:
            if pos == 1:
                s = Control(root=self.app,path =UPath(type_ == "UIStackView") / 0 / UPath(id_ == "titleLabel"))
            if pos ==2:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 1 / UPath(id_ == "titleLabel"))
            if pos == 3:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 2 / UPath(id_ == "titleLabel"))
            if pos == 4:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 3 / UPath(id_ == "titleLabel"))
            if pos == 5:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 4 / UPath(id_ == "titleLabel"))
            if pos == 6:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 5 / UPath(id_ == "titleLabel"))
            return s.text
        else:
            if pos == 1:
                s = Control(root=self.app,path =UPath(type_ == "UIStackView") / 5 / UPath(id_ == "titleLabel"))
            if pos ==2:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 0 / UPath(id_ == "titleLabel"))
            if pos == 3:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 1 / UPath(id_ == "titleLabel"))
            if pos == 4:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 2 / UPath(id_ == "titleLabel"))
            if pos == 5:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 3 / UPath(id_ == "titleLabel"))
            if pos == 6:
                s = Control(root=self.app, path=UPath(type_ == "UIStackView") / 4 / UPath(id_ == "titleLabel"))
            return s.text

    def select_account(self):
        if self["add_another_account"].wait_for_existing(timeout=3, raise_error=False):
            self["add_another_account"].click()
        elif self["add_another_account_button"].wait_for_existing(timeout=3, raise_error=False):
            self["add_another_account_button"].click()

    def feedback_icon(self):
        return self["feedback_icon"].wait_for_existing(timeout=5, interval=0.5)

    def skip_button(self):
        self['skip'].click()

    def nick_name_title(self):
        self['nick_name_title'].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        return self['nick_name_title'].text

    def nick_name_subtitle(self):
        return self['nick_name_subtitle'].text

    def create_password_and_click_next_button(self, password):
        self['password_edit_box'].input(password)
        time.sleep(1)
        return self['create_password_next_button'].click()

    def phone_sign_up_disclaimer(self):
        return self['phone_sign_up_disclaimer'].text

    def gb_email_trending_text(self):
        return self['gb_email_trending_text'].text

    def save_login_info(self):
        return self['save_login_info'].visible

    def email_sign_up_disclaimer(self):
        return self['email_sign_up_disclaimer'].text

    def gb_email_sign_up_disclaimer(self):
        return self['gb_email_sign_up_disclaimer'].text

    def switch_email_signup_tab(self):
        return self['email_signup_tab'].click()

    def enter_email(self, email):
        self['email_edit_box'].input(email)
        time.sleep(1)
        return self['email_input_confirm'].click()

    def ui_checking(self):
        """检查界面元素内容
        """
        pass

    def wait_for_panel_loading(self):
        return self["panel_controller"].wait_for_existing(timeout=5, interval=0.5,raise_error=False)

    def login_with_phone(self, region, phone_num, phone_code, specify_region=False):
        self.use_phone_email_username_login()
        LoginWithPhonePanel(root=self.app).login_with_phone(region=region, phone_num=phone_num, phone_code=phone_code,
                                                            specify_region=specify_region)

    def login_with_phone_in_us(self, region, phone_num, phone_code, specify_region=False):
        self.use_phone_email_username_login()
        LoginWithPhonePanel(root=self.app).login_with_phone_in_us(region=region, phone_num=phone_num, phone_code=phone_code,
                                                            specify_region=specify_region)
    @property
    def force_login_screen_title_text(self):
        if self["force_login_screen_title_text"].wait_for_existing(raise_error=False):
            return self["force_login_screen_title_text"].text

    @property
    def desc_text(self):
        return self["desc_text"].text

    @property
    def force_login_screen_terms_label(self):
        if self["force_login_screen_terms_label"].wait_for_existing(raise_error=False):
            return self["force_login_screen_terms_label"].text

    @property
    def us_force_login_screen_terms_label(self):
        if self['us_force_login_screen_terms_label'].wait_for_existing(raise_error=False):
            return self['us_force_login_screen_terms_label'].text

    @property
    def signup_screen_title_text(self):
        if self['signup_screen_title_text'].wait_for_existing(raise_error=False):
            return self['signup_screen_title_text'].text

    @property
    def us_signup_screen_terms_label(self):
        if self['us_signup_screen_terms_label'].wait_for_existing(raise_error=False):
            return self['us_signup_screen_terms_label'].text

    def switch_to_signup(self):
        if self["signup"].text == "Don’t have an account? Sign up":
            self["signup"].click()

    def signup_with_phone_email_button(self):
        self["signup_with_phone_email_button"].click()

    @property
    def signup_screen_terms_label(self):
        return self["signup_screen_terms_label"].text

    def use_phone_email_username_login(self):
        self.click_login_button()
        if self["add_another_account"].wait_for_visible(timeout=2,raise_error=False):
            self["add_another_account"].click()
        if self["phone_email_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["phone_email_button"].click()
        elif self["phone_email_icon"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["phone_email_icon"].click()
        elif self["phone_email_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["phone_email_text"].click()
        elif self["login_mode_view"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self.app.testcase.device.click(self["login_mode_view"].rect.center[0], self["login_mode_view"].rect.top+20)
        elif self["use_phone"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["use_phone"].click()

    def into_signup_panel(self):
        return self["signup"].click()

    def login_by_username(self, username, password):
        self.use_phone_email_username_login()
        self["switch_username"].click()
        time.sleep(1)
        self["username"].input(username)
        time.sleep(1)
        self["password"].input(password)
        self["login_btn"].click()
        time.sleep(5)

    def click_login_button(self):
        if self["login_button"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["login_button"].click()
        time.sleep(2)

    def click_forgot_password(self):
        if self["switch_username"].wait_for_visible(timeout=5, interval=1, raise_error=True):
            self["switch_username"].click()
        self["forgot_password"].click()

    def click_reset_password_with_phone_number(self):
        if self["reset_password_with_phone_number"].wait_for_visible(timeout=5, interval=1, raise_error=True):
            self["reset_password_with_phone_number"].click()

    def click_reset_password_with_email(self):
        if self["reset_password_with_email"].wait_for_visible(timeout=5, interval=1, raise_error=True):
            self["reset_password_with_email"].click()

    def dismiss_reset_password_with_dialog(self):
        if self["reset_password_with_cancel"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["reset_password_with_cancel"].click()

    def reset_password(self, password):
        self["password"].text = ""
        time.sleep(2)
        self["password"].input(password)
        time.sleep(5)
        self["login_btn"].click()

    def login_by_phone_password(self, phonenumber = '', password= ''):
        time.sleep(4)
        self.click_login_button()
        self.app.testcase.take_screenshot(self.app.testcase.device, "click_login.jpg")
        time.sleep(4)
        self["use_phone"].click()
        self.app.testcase.take_screenshot(self.app.testcase.device, "click_use_phone.jpg")
        time.sleep(4)
        LoginWithPhonePanel(root=self.app).login_with_phone_pwd(phonenumber, password)

    def login_by_phone_password_region(self, region='', phonenumber='', password=''):
        time.sleep(4)
        self.use_phone_email_username_login()
        self.app.testcase.take_screenshot(self.app.testcase.device, "phone_login.jpg")
        time.sleep(4)
        LoginWithPhonePanel(root=self.app).login_with_phone_pwd(phonenumber, password, region)

    @property
    def verify_failed_msg(self):
        time.sleep(2)
        return self["verify_failed_text"].text

    def reset_phone_pwd(self, password):
        LoginWithPhonePanel(root=self.app).reset_password(password)

    def switch_region_to_us(self):
        LoginWithPhonePanel(root=self.app).change_region_to_us()

    def get_current_region(self):
        self["region_label"].wait_for_visible(timeout=5,  interval=1, raise_error=False)
        return self["region_label"].text

    def judge_current_region(self, code):
        time.sleep(5)
        return self.get_current_region() == code

    def switch_sim_region_to_86(self):
        LoginWithPhonePanel(root=self.app).change_region_to_china()

    def reset_code(self, code):
        self['back_btn'].click()
        self['back_btn'].wait_for_disappear(timeout=2, interval=0.5, raise_error=False)
        LoginWithPhonePanel(root=self.app).reset_code(code)

    def click_close_btn(self):
        if self["close_popup"].wait_for_existing(timeout=3, raise_error=False):
            self["close_popup"].click()
        if self["skip_inhouse_only"].wait_for_existing(timeout=3, raise_error=False):
            self["skip_inhouse_only"].click()
        elif self["back_btn_login"].wait_for_existing(timeout=3, raise_error=False):
            self["back_btn_login"].click()
        elif self['close_btn'].wait_for_existing(timeout=3, raise_error=False):
            self['close_btn'].click()
        elif self["close"].wait_for_existing(timeout=3, raise_error=False):
            self["close"].click()
        elif self["close_new"].wait_for_existing(timeout=3, raise_error=False):
            self["close_new"].click()

    def navigate_to_year(self, year):
        current_year = int(self.year_picker.elem_info.get("value", 0))
        while current_year > year:
            current_year = current_year - 1
            date_picker_view = Control(root =self["date_picker"], path=UPath(type_ == "_UIDatePickerView") / 0 / 2 /
                0 / UPath(text_ == str(current_year)))
            date_picker_view.wait_for_ui_stable()
            date_picker_view.click()
            time.sleep(1)
            self.wait_for_ui_stable()
            self.refresh()

        return self.next_btn.click()



class VerificationCodeInputView(Control):
    """verification code input view
    """
    elem_path = UPath(type_ == "UILabel")
    elem_class = Control


class LoginWithPhonePanel(BasePanel):
    """ Use Phone or email to login panel
    """
    window_spec = {"path": UPath(type_ == 'AWELoginWindow')}

    def get_locators(self):
        return {
            "phone_num_input_popup": {"type": TextEdit, "path": UPath(type_ == "TUXInputFieldCore")},
            "Continue_send_code": {"path": UPath(type_ == "UIButtonLabel", visible_ == True)},
            "Continue_send_code1": {"path": UPath(id_ == "Continue", visible_ == True)},
            #   Panel_Controller
            "panel_controller": {"type": Control, "path": UPath(~controller_ == 'TMPhoneEmailLoginViewController|AWEI18NLoginViewController', visible_ == True)},
            #   Phone Number Input Panel
            "phone_num_input": {"type": TextEdit, "root": "panel_controller", "path": UPath(type_ == 'SHSPhoneTextField')},
            "phone_num_input_new": {"type": TextEdit, "path": UPath(controller_ == "_UICursorAccessoryViewController")},
            "phone_num_input_new1": {"type": TextEdit, "path": UPath(type_ == "_UITextLayoutCanvasView", visible_ == True)},
            "clear_phone_num_history": {"type": Control, "path": UPath(id_ == "icoLoginClear", visible_ == True)},
            "send_code": {"type": Control, "path": UPath(type_ == 'TMLoginButton', ~label_ == 'Send code|Continue|发送验证码', visible_ == True)},
            "back": {"type": Control, "root": "panel_controller", "path": UPath(label_ == 'icon back')},
            "faq": {"type": Control, "root": "panel_controller", "path": UPath(type_ == 'UIButton', id_ == 'null')},
            "region": {"type": Control, "root": "panel_controller", "path": UPath(id_ == 'prefixRegionLabel')},
            "country_code": {
                "root": "panel_controller",
                "path": UPath(type_ == 'TMCountryCodeView') / UPath(type_ == "UILabel", index=0)},
            "region_table": {"type": Control, "path": UPath(type_ == 'UITableView')},
            "region_china": {"type": Control, "path": UPath(label_ == 'China mainland', visible_ == True)},
            "region_us": {"path": UPath(label_ == 'United States', visible_ == True)},
            #   Phone Code Input Panel
            "phone_code_input": {"type": VerificationCodeInputView,
                                 "path": UPath(type_ == 'TMVerificationCodeInputView')/UPath(type_ == 'UIStackView', visible_ == True)},
            "phone_code_input_new": {"type": VerificationCodeInputView,
                                 "path": UPath(type_ == "TUXPinFieldCore") / UPath(type_ == "UIView", visible_ == True, depth=5)},
            #   example
            # "": {"type": Control, "root": "panel_controller", "path": },
            "login_pwd": {"path": UPath(type_ == 'UIButtonLabel', label_ == 'Log in with password', visible_ == True)},
            "password_input": {"path": UPath(type_ == 'TMPasswordInputView', id_ == 'passwordInputView') / UPath(type_ == 'TikTokAccountInfoTextField')},
            "login_btn": {"path": UPath(type_ == 'TMLoginButton', id_ == 'loginButton')},
            "send_verification_code": {"path": UPath(type_ == 'TMLoginButton', ~id_ == 'loginButton|phoneLoginButton')},
        }

    def ui_checking(self):
        """检查界面元素内容
        """
        pass

    def wait_for_panel_loading(self):
        return self["panel_controller"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def login_with_phone(self, region, phone_num, phone_code, specify_region=False):
        """Operation: 通过手机号+验证码的方式进行登录
        """
        #登录面板新样式
        #在登录弹窗上直接输入手机号码 https://libra-sg.tiktok-row.net/libra/flight/********/edit V4实验组
        if self["phone_num_input_popup"].wait_for_visible(timeout=2,raise_error=False) != True:
            if self.get_current_region() != region:
                self.switch_region(region)
            if self["clear_phone_num_history"].wait_for_existing(timeout=2, raise_error=False):
                self["clear_phone_num_history"].click()
            if self["phone_num_input"].wait_for_visible(timeout=2, raise_error=False):
                self["phone_num_input"].click()
                self["phone_num_input"].input(phone_num)
            elif self["phone_num_input_new"].wait_for_visible(timeout=2,raise_error=False):
                self["phone_num_input_new"].click()
                self["phone_num_input_new"].input(phone_num)
            elif self["phone_num_input_new1"].wait_for_visible(timeout=2):
                self["phone_num_input_new1"].click()
                self["phone_num_input_new1"].input(phone_num)
        else:
            if self["phone_num_input_popup"].wait_for_visible(timeout=2):
                self["phone_num_input_popup"].click()
                self["phone_num_input_popup"].input(phone_num)
                self.switch_region(region)
        if self["send_code"].wait_for_visible(timeout=3, raise_error=False):
            self["send_code"].click()
            self["send_code"].wait_for_disappear(timeout=10, interval=0.5, raise_error=False)
        elif self["send_verification_code"].wait_for_visible(timeout=3, raise_error=False):
            self["send_verification_code"].click()
            self["send_verification_code"].wait_for_disappear(timeout=10, interval=0.5, raise_error=False)
        elif self["Continue_send_code1"].wait_for_visible(timeout=3, raise_error=False):
            self["Continue_send_code1"].click()
            self["Continue_send_code1"].wait_for_disappear(timeout=10, interval=0.5, raise_error=False)
        elif self["Continue_send_code"].wait_for_visible(timeout=2):
            self["Continue_send_code"].click()
            self["Continue_send_code"].wait_for_disappear(timeout=10, interval=0.5, raise_error=False)
        if self["phone_code_input"].wait_for_visible(timeout=3, raise_error=False):
            self["phone_code_input"].click()
            if len(phone_code) == 4:
                phone_code = "00" + phone_code
            if len(self.phone_code_input.items()) == 4:
                phone_code = phone_code[-4:]
            for i in range(len(phone_code)):
                self["phone_code_input"].children[i].input(phone_code[i])
            time.sleep(2)
        elif self["phone_code_input_new"].wait_for_visible(timeout=3):
            self["phone_code_input_new"].click()
            if len(phone_code) == 4:
                phone_code = "00" + phone_code
            if len(self.phone_code_input_new.items()) == 4:
                phone_code = phone_code[-4:]
            for i in range(len(phone_code)):
                self["phone_code_input_new"].children[i].input(phone_code[i])
            time.sleep(2)
    def login_with_phone_in_us(self, region, phone_num, phone_code, specify_region=False):
        """Operation: 通过手机号+验证码的方式进行登录
                """
        if self.get_current_region() != region:
            self.switch_region_to_us(region)
        if self["clear_phone_num_history"].wait_for_existing(timeout=2, raise_error=False):
            self["clear_phone_num_history"].click()
        self["phone_num_input"].click()
        self["phone_num_input"].input(phone_num)
        if self["send_code"].wait_for_visible(timeout=3, raise_error=False):
            self["send_code"].click()
        elif self["send_verification_code"].wait_for_visible(timeout=3, raise_error=False):
            self["send_verification_code"].click()
        self["send_code"].wait_for_disappear(timeout=10, interval=0.5, raise_error=False)
        self["phone_code_input"].click()
        if len(phone_code) == 4:
            phone_code = "00" + phone_code
        if len(self.phone_code_input.items()) == 4:
            phone_code = phone_code[-4:]
        for i in range(len(phone_code)):
            self["phone_code_input"].children[i].input(phone_code[i])
        time.sleep(2)

    def reset_code(self, code):
        self["send_code"].wait_for_visible(timeout=3, raise_error=False)
        self.send_code()
        self["send_code"].wait_for_disappear(timeout=2, interval=0.5, raise_error=False)
        self.input_phone_code(code)

    def get_current_region(self):
        return self["region"].text

    def change_region_to_china(self):
        # self["region"].click(0)
        if self.country_code.elem_info.get("label") == "CN":
            return
        self.country_code.click()
        while not self["region_china"].wait_for_existing(timeout=1,  interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        return self["region_china"].click()

    def input_phone_num(self, phone_num):
        self["phone_num_input"].click()
        return self["phone_num_input"].input(phone_num)

    def send_code(self):
        return self["send_code"].click()

    def input_phone_code(self, phone_code):
        self["phone_code_input"].click()
        if len(self.phone_code_input.items()) == 4:
            phone_code = phone_code[-4:]
        return self["phone_code_input"].input(phone_code)

    def login_with_phone_pwd(self, phone_number='', password='', region=''):
        if self.get_current_region() != region:
            self.change_region_to_china()
        self["phone_num_input"].click()
        self["phone_num_input"].input(phone_number)
        time.sleep(2)
        self["send_code"].click()
        time.sleep(2)
        self["login_pwd"].click()
        self["password_input"].wait_for_visible(timeout=5, interval=0.5)
        self["password_input"].input(password)
        time.sleep(2)
        self["login_btn"].click()

    def reset_password(self, password=''):
        self["password_input"].text = ""
        time.sleep(2)
        self["password_input"].input(password)
        time.sleep(2)
        self["login_btn"].click()

    def change_region_to_us(self):
        if self.country_code.elem_info.get("label") == "US":
            return
        self.country_code.click()
        while not self["region_us"].wait_for_existing(timeout=1,  interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        return self["region_us"].click()

    def switch_region(self, region):
        self.country_code.click()
        if region == "CN":
            region_ele = Control(UPath(text_ == "+86", visible_ == True), root=self.app)
        else:
            region_ele = Control(UPath(text_ == region, visible_ == True), root=self.app)
        from shoots.retry import Retry
        for _ in Retry(timeout=120, interval=5, raise_error=True):
            if region_ele.wait_for_existing(timeout=1, interval=0.5, raise_error=False) and region_ele.rect.top + region_ele.rect.height < self.rect.height:
                return region_ele.click()
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()

    def switch_region_to_us(self, region):
        self.country_code.click()
        if region == "US":
            region_ele = Control(UPath(label_ == 'United States', visible_ == True), root=self.app)
        else:
            region_ele = Control(UPath(text_ == region, visible_ == True), root=self.app)
        while not region_ele.wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1,  swipe_coefficient=7)
            self.wait_for_ui_stable()
            self.refresh()
        return region_ele.click()


class AgeGateViewPanel(BasePanel):
    window_spec = {"path": UPath(~type_ == "AWELoginWindow")}

    def get_locators(self):
        return {
            "next_btn": {"path": UPath(type_ == 'TUXButton')},
            "text_field": {"path": UPath(type_ == 'UITextField')},
            "date_picker": {"path": UPath(type_ == 'UIDatePicker')},
            "year_picker": {
                "root": "date_picker",
                "path": UPath(type_ == "UIAccessibilityPickerComponent", index=0)
            },
            "signup_page_dialog": {"path": UPath(type_ == "TUXDialogActionSection") /
            UPath(type_ == "TUXButton", depth=10)},
            "kids_popup_dialog": {"path": UPath(id_ == "messageAttributedLabel") /
            UPath(type_ == "TTTAccessibilityElement")},
            "kids_popup_lets_go": {"path": UPath(id_ == "Let's go", type_ == "TUXButton")},
            "kids_mode_username_title": {"path": UPath(text_ == "Choose username")},
            "kids_mode_username_desc": {"path": UPath(text_ == "Choose a username for your account. For your safety, don't use your real name or other personal information as your username.")},
            "kids_mode_username_input": {"path": UPath(type_ == "TTKKidsSignupTextField")},
            "dob_review_jp": {"path": UPath(id_ == "alertView") / UPath(id_ == "titleLabel")},
            "close_review_popup": {"path": UPath(id_ == "backButton")},

            "age_gate_title": {"path": UPath(label_ == "Sign up")},
            "birthday_title": {"path": UPath(~controller_ == "TTKRegistrationAgeGateViewController|TTKAgeGateViewController") / 0},
            "birthday_subtitle": {"path": UPath(~controller_ == "TTKRegistrationAgeGateViewController|TTKAgeGateViewController") / 1},

            "birthday_alert": {"path": UPath(id_ == "alertView")},
            "birthday_alert_subtitle":{"path": UPath(id_ == "subtitleLabel")},
            "birthday_alert_ok_button": {"path": UPath(id_ == "backButton")},
            'confirm': {'path': UPath(type_ == "UIButtonLabel", id_ == "Confirm")}
        }

    def click_birthday_alert_ok_button(self):
        return self['birthday_alert_ok_button'].click

    def birthday_alert_subtitle(self):
        return self['birthday_alert_subtitle'].visible

    def birthday_alert(self):
        return self['birthday_alert'].visible

    def birthday_subtitle(self):
        return self['birthday_subtitle'].text

    def birthday_title(self):
        return self['birthday_title'].text

    def age_gate_title(self):
        return self['age_gate_title'].text

    def navigate_to_year(self, year):
        current_year = int(self.year_picker.elem_info.get("value", 0))
        while current_year > year:
            rect = self.year_picker.rect
            y1 = rect.top + rect.height // 2
            self.year_picker.drag(to_x=0, to_y = y1+30)
            self.wait_for_ui_stable()
            self.refresh()
            current_year = int(self.year_picker.elem_info.get("value", 0))
        return self.next_btn.click()

    def set_birthday_on_age_gate_screen(self, year):
        """
        Added hack for selecting year, right now selected year is always less than value of year passed into this method.
        """
        current_year = int(self.year_picker.elem_info.get("value", 0))
        while current_year > year:
            rect = self.year_picker.rect
            y1 = rect.top + rect.height // 2
            self.year_picker.drag(to_x=0, to_y=y1 + 400)
            self.wait_for_ui_stable()
            self.refresh()
            current_year = int(self.year_picker.elem_info.get("value", 0))
        return self.next_btn.click()

    def close_signup_dialog(self):
        if self["signup_page_dialog"].wait_for_visible(timeout=5,  interval=1, raise_error=False):
            self["signup_page_dialog"].click()
        time.sleep(2)

    def check_kids_login_popup(self):
        return self["kids_popup_dialog"].wait_for_visible(timeout=5,  interval=1, raise_error=True)


    def click_kids_pop_up(self):
        self["kids_popup_lets_go"].wait_for_visible(timeout=5,  interval=1, raise_error=True)
        self["kids_popup_lets_go"].click()

    def check_username_page(self):
        self["kids_mode_username_title"].wait_for_visible(timeout=5,  interval=1, raise_error=True)
        return self["kids_mode_username_desc"].wait_for_visible(timeout=5,  interval=1, raise_error=True)

    def input_kids_username(self, username):
        self["kids_mode_username_input"].wait_for_visible(timeout=5,  interval=1, raise_error=True)
        self["kids_mode_username_input"].input(username)

    def check_review_dob_popup(self):
        self["dob_review_jp"].wait_for_visible(timeout=5,  interval=1, raise_error=True)
        return self["dob_review_jp"].text

    def close_review_dob_popup(self):
        self["close_review_popup"].wait_for_visible(timeout=5,  interval=1, raise_error=True)
        self["close_review_popup"].click()

    def click_next_button(self):
        self["next_btn"].wait_for_visible(timeout=5,  interval=1, raise_error=True)
        self["next_btn"].click()

    def click_confirm(self):
        return self['confirm'].click()

class AccountSelectionLoginPanel(BasePanel):
    window_spec = {"path": UPath(type_ == "AWELoginWindow")}

    def get_locators(self):
        return {
            "panel_controller": {"type": Control,
                                 "path": UPath(type_ == 'TTKSelectAccountLoginViewController',
                                               visible_ == True)},

            # account selection login panel
            "close_login_panel_as": {"path": UPath(id_ == "icon_close_panel", type_ == "UIImageView")},
            "login_text_as": {"path": UPath(id_ == "horizontalStackView") / UPath(id_ == "titleLabel")},
            "login_subtitle_text_as": {"path": UPath(id_ == "contentViewForScroll") / UPath(id_ == "descLabel")},
            "feedback_button_as": {"path": UPath(controller_ == "TTKSelectAccountLoginViewController") / UPath(
                id_ == "IconQuestionMarkCircleLTR") / UPath(type_ == "UIImageView")},
            "profile_image_as": {"path": UPath(type_ == "TTKLoginRecordCell") / 0 / 0},
            "or_lable_as": {"path": UPath(id_ == "orLabel")},
            "delete_button_as": {"path": UPath(id_ == "deleteButton") / UPath(type_ == "UIImageView")},
            "add_another_account_button_as": {"path": UPath(id_ == "Add another account", type_ == "UIButtonLabel")},
            "signup_label_as": {
                "path": UPath(controller_ == "TTKSelectAccountLoginViewController") / UPath(id_ == "signUpLabel")},
        }
    def close_login_panel_as(self):
        return self['close_login_panel_as'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def click_close_login_panel_as(self):
        return self['close_login_panel_as'].click()

    def login_text_as(self):
        return self['login_text_as'].text

    def login_subtitle_text_as(self):
        return self['login_subtitle_text_as'].text

    def feedback_button_as(self):
        return self['feedback_button_as'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def profile_image_as(self):
        return self['profile_image_as'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def or_lable_as(self):
        return self['or_lable_as'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def delete_button_as(self):
        return self['delete_button_as'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def add_another_account_button_as(self):
        return self['add_another_account_button_as'].visible

    def signup_label_as(self):
        return self['signup_label_as'].text

    def wait_for_panel_loading(self):
        return self["panel_controller"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
