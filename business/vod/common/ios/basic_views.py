# -*- coding: utf-8 -*-

from shoots.retry import Retry

from .base_panel import *
from uibase.upath import *

class UIAlert(Window):
    """ui alert window
    """
    window_spec = {"path": UPath(type_ == "UIAlertController")}

    def get_locators(self):
        return {
            "delete": {"path": UPath(text_ == "Delete")}
        }


class AWENewUserJourneyContainerView(Window):
    """AWENewUserJourneyContainerView
    """
    window_spec = {"path": UPath(type_ == 'AWENewUserJourneyContainerViewController')}

    def get_locators(self):
        return {
            "new_user_journey_tiktok_logo": {"path": UPath(id_ == 'NewUserJourneyTiktokLogo')}
        }

    def wait_new_user_journey_view_invisible(self):
        time.sleep(3)
        if self.new_user_journey_tiktok_logo.existing:
            self.new_user_journey_tiktok_logo.wait_for_invisible(timeout=5, raise_error=False)

class AWECommentPanelCell(Control):
    """comment cell
    """
    def get_locators(self):
        return {
            "user_name": {"path": UPath(type_ == "AWEUserNameLabel")},
            "comment": {"path": UPath(type_ == "YYLabel", visible_ == True)},
            "like_btn": {"path": UPath(type_ == "UIButton", label_ == "Like")},
            "reposted_icon": {
                "path": UPath(type_ == "UITableView") / UPath(type_ == "AWECommentPanelCell", index=4) / UPath(type_ == "UITableViewCellContentView") / UPath(type_ == "UIImageView", index=1)
                }
        }

    @property
    def comment_content(self):
        return self.comment.elem_info.get("label")

    def like(self):
        self.like_btn.wait_for_visible(timeout=5)
        time.sleep(2)
        return self.like_btn.click()

    def has_reposted_message(self) -> None:
        self["reposted_icon"].wait_for_existing(timeout=5)


class CommentsTableView(Control):
    """comments table view
    """
    elem_path = UPath(type_ == "AWECommentPanelCell")
    elem_class = AWECommentPanelCell



class AWECommentListView(BasePanel):
    """aweme comment list view
    """
    window_spec = {"path": UPath(type_ == 'AWECommentListViewController')}

    def get_locators(self):
        return {
            "comments_table_view": {"type": CommentsTableView, "path": UPath(type_ == 'UITableView', visible_ == True)},
            "comment_text_view": {"path": UPath(type_ == 'AWEGrowingTextView')},
            "comments_turned_off": {"path": UPath(label_ == 'Comments are turned off')},
            "no_comment": {"path": UPath(label_ == '0 comments')},
        }

    def swipe_up_comments_table(self):
        for _ in range(3):
            self.comments_table_view.scroll(distance_y=300)
            time.sleep(2)

    def watch_comments(self):
        if self["no_comment"].wait_for_existing(timeout=3, raise_error=False):
            return True
        comment_list = self.comments_table_view.items()
        for _ in Retry(limit=10, raise_error=False):
            if comment_list:
                break
            else:
                comment_list = self.comments_table_view.items()
                time.sleep(1)

        self.swipe_up_comments_table()

        count = 0
        for comment_cell in self.comments_table_view.items():
            if comment_cell.visible:
                logger.info("[READ COMMENT]: %s" % comment_cell.comment_content)
                count += 1

        return count > 0

    def wait_for_comment_loaded(self, comment):
        logger.debug("wait for (%s) loaded" % comment)
        for _ in Retry(timeout=10, message="comment(%s) load failed in 10s" % comment):
            for comment_cell in self.comments_table_view.items():
                content = comment_cell.comment_content
                logger.debug("comment is : %s" % content)
                if comment in content:
                    return True

    def comment(self, comment):
        self.comment_text_view.wait_for_ui_stable()
        self.comment_text_view.input(comment + "\n")
        self.wait_for_comment_loaded(comment)

    def like_comment(self, comment):
        # assume target comment is on the display
        for comment_cell in self.comments_table_view.items():
            if comment in comment_cell.comment_content:
                return comment_cell.like()
        return False

    def delete_comment(self, comment):
        # assume target comment is on the display
        for comment_cell in self.comments_table_view.items():
            if comment in comment_cell.comment_content:
                comment_cell.long_click()
                ui_alert = UIAlert(root=self.app)
                return ui_alert.delete.click()
        return False


class FeedVideoButtonList(Control):
    elem_path = UPath(type_ == "AWEFeedVideoButton")
    elem_class = Control


class AWEPlayInteractionViewController(Control):
    """video detail view
    """

    def refresh(self):
        self.wait_for_invisible(timeout=5, raise_error=False)
        self._cache = {}
        return super(AWEPlayInteractionViewController, self).refresh()

    def get_locators(self):
        return {
            "avatar_btn": {"path": UPath(type_ == 'TTKStoryAvatarView', visible_ == True)},
            "follow_btn": {"path": UPath(id_ == 'follow_plus_small', visible_ == True)},
            "follow_btn1": {"path": UPath(id_ == "followPromptView", visible_ == True) / UPath(type_ == "UIImageView", visible_ == True, depth=5)},
            "feedfollow_btn": {"path": UPath(id_ == "followPromptView", visible_ == True) / UPath(type_ == "UIImageView", visible_ == True, depth=5)},
            "live_label": {"path": UPath(id_ == 'LIVE')},
            "comment_btn": {"path": UPath(id_ == "feedCommentButton", visible_ == True)},
            "comment_label": {"path": UPath(label_ == "Comments", visible_ == True)},
            "share_btn": {"path": UPath(type_ == "AWEFeedVideoButton", label_ == "Share")},
            "music_cover_btn": {"path": UPath(type_ == "AWEMusicCoverButton")},
            "see_more": {"path": UPath(type_ == 'AWEPlayInteractionDescriptionTailLabel')},
            "description": {"path": UPath(~type_ == 'YYLabel|AWEPlayInteractionDescriptionLabel', visible_ == True, index=0)},
            "name_label": {"path": UPath(id_ == 'nameLabel',index=0)},
            "feed_video_btn_list": {
                "type": FeedVideoButtonList,
                "path": UPath(label_ == 'right')
            },
            "repost_btn": {
                "path": UPath(id_ == "(Repost)")
                },
            "reposted_message": {
                "path": UPath(type_ == "TTKFeedPassthroughView") / UPath(type_ == "TTKUpvoteBubbleControllerView") / UPath(type_ == "TTKUpvoteBubbleScrollerView") / UPath(type_ == "TTKUpvoteBubbleElement") / UPath(type_ == "UILabel")
                },
            "add_a_comment_message": {
                "path": UPath(type_ == "TTKFeedPassthroughView") / UPath(type_ == "TTKUpvoteBubbleControllerView") / UPath(type_ == "TTKUpvoteBubbleScrollerView") / UPath(type_ == "TTKUpvoteBubbleElement") / UPath(type_ == "UIButtonLabel")
                }

        }

    @property
    def comment_button(self):
        if self.comment_btn.existing:
            return self.comment_btn
        elif self.comment_label.existing:
            return self.comment_label
        for _ in Retry(limit=5, message="got empty button list in 5 times"):
            buttons = self.feed_video_btn_list.items()
            if buttons:
                break
        for button in buttons:
            label = button.elem_info.get("label")
            if not label:
                return button

    def get_comment_count(self):
        count_label = Control(root=self.comment_button, path=UPath(type_ == "UILabel"))
        return count_label.text

    def get_description(self):
        if self.see_more.existing:
            self.see_more.click()
        if self.description.existing:
            self.description.refresh()
            return self.description.text
        return ""

    def get_creator_name(self):
        return self.name_label.text

    def creator_name_click(self):
        self.name_label.click()

    def is_living(self):
        return self.live_label.existing

    def enter_profile(self):
        if self.avatar_btn.wait_for_visible():
            self.avatar_btn.click()

    def show_comments(self):
        # self.comment_btn.click()
        self.comment_button.click()

    def watch_comments(self):
        self.show_comments()
        comment_list_view = AWECommentListView(root=self.app)
        comment_list_view.wait_for_visible(timeout=3)
        return comment_list_view.watch_comments()

    def find_follow_btn(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self.follow_btn.wait_for_existing(timeout=3, raise_error=False) or self.feedfollow_btn.wait_for_visible(timeout=3, raise_error=False):
                break
            self.swipe_up()

    def follow_creator(self):
        return self.follow_btn.click()

    def swipe_up(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=rect.height*0.75)

    def is_followed(self):
        return not self.follow_btn.existing

    def is_followed_visible(self):
        if self["follow_btn"].wait_for_visible(raise_error=False):
            return self.follow_btn.wait_for_invisible(timeout=3, raise_error=False)
        elif self["follow_btn1"].wait_for_visible(raise_error=False):
            return self.follow_btn1.wait_for_invisible(timeout=3, raise_error=False)

    def enter_music_profile(self):
        self.music_cover_btn.click()

    def share_to(self):
        self.share_btn.click()

    def callout_long_press_panel(self):
        self.long_click()

    def description_has_hashtag(self):
        return "#" in self.get_description()

    def click_first_hashtag_in_description(self):
        start = self.description.text.index("#")
        return self.description.click_text_span(start, start+2)

    def can_repost(self) -> None:
        """
        is repost bubble showing?
        """
        return self["repost_btn"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def click_repost(self) -> None:
        """
        clicks repost bubble button
        """
        self["repost_btn"].click()

    def can_show_reposted(self) -> None:
        """
        is the bubble confirming that you reposted showing?
        """
        return self["reposted_message"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def can_add_comment(self) -> None:
        """
        is the bubble showing you can add a comment showing?
        """
        return self["can_add_comment"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)


class AWESharePanel(Window):
    """AWESharePanel
    """
    window_spec = {"path": UPath(~controller_ == 'AWEIMDirectTranspondViewController|AWESharePanelController|TTKSharePanelViewController')}

    def get_locators(self):
        return {
            "save_video": {"path": UPath(label_ == 'Save video')},
            "cancel": {"path": UPath(type_ == 'AWEShareDismissButton')},
            "close_btn": {"path": UPath(type_ == "UIButton", label_ == "iconShare close small")}
        }

    def cancel_share(self):
        if self["cancel"].wait_for_existing(timeout=5, raise_error=False):
            self.cancel.click()
        else:
            self.close_btn.click()


class TIKTOKAwemeLongPressListView(Window):
    """TIKTOKAwemeLongPressListView
    """
    window_spec = {"path": UPath(type_ == 'TIKTOKAwemeLongPressListViewController')}

    def get_locators(self):
        return {
            "save_video": {"path": UPath(label_ == 'Save video')}
        }


class SearchVideoCollection(Control):
    """search video collection
    """
    elem_class = Control
    elem_path = UPath(type_ == "AWEFeedSearchVideoCollectionViewCell")


class AWEDiscoverView(BasePanel):
    """AWEDiscoverView
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "discover_view_controller": {"path": UPath(type_ == 'AWEDiscoverViewController')},
            "search_bar": {"path": UPath(type_ == 'UISearchBarTextField')},
        }

    def search(self, content):
        # self.search_bar.input(content + "\r")
        self.device_input(self.search_bar, content)
        self.device_send_enter()


class SearchResultUsersList(Control):
    elem_class = Control
    elem_path = UPath(type_ == 'AWEDoubleColumnGeneralSearchCommonCell', visible_ == True)


class VideoShareView(Control):
    """share collection
        """
    elem_class = Control
    elem_path = UPath(type_ == "AWEShareRowView")


class AWESearchView(Window):
    """AWESearchView
    """
    window_spec = {"path": UPath(type_ == 'AWESearchViewController')}

    def get_locators(self):
        return {
            "search_video_collection": {"type": SearchVideoCollection, "path": UPath(type_ == 'IGListCollectionView')},
            "users_list": {"type": SearchResultUsersList, "path": UPath(type_ == 'IGListCollectionView')}
        }

    def enter_first_video_in_search_result(self):
        for _ in Retry(timeout=5, message="got empty search video list in 5s"):
            if self.search_video_collection.items():
                break

        search_video_list = self.search_video_collection.items()
        sorted(search_video_list, key=lambda cell: cell.rect.left)
        sorted(search_video_list, key=lambda cell: cell.rect.top)

        first_search_video = search_video_list[0]
        first_search_video.click()

    def enter_first_user_and_watch_first_work(self, content):
        # for _ in Retry(limit=5, message="got empty search user list in 5 times"):
        #     users = self.users_list.items()
        #     if users:
        #         break
        # user = users[0]
        # user.click()
        user_result = Control(root=self, path=UPath(label_ == content))
        if user_result.wait_for_existing(raise_error=True):
            user_result.click()
            user_detail_view = UserDetailView(root=self.app)
            user_detail_view.watch_first_work()

class AWEChallengeDetailMultiColumnFeedView(Control):
    """challenge detail multi column feed view
    """
    elem_class = Control
    elem_path = UPath(type_ == "AWEDetailAwemeCollectionViewCell")


class AWEChallengeDetailContainerView(Window):
    """challenge detail container view
    """
    window_spec = {"path": UPath(type_ == 'AWEChallengeDetailContainerViewController')}

    def get_locators(self):
        return {
            "challenge_name": {"path": UPath(type_ == 'AWEChallengeDetailHeaderBasicInfoContainerView')/UPath(type_ == 'YYLabel', index=0)},
            "works_collection": {"type": AWEChallengeDetailMultiColumnFeedView,
                                 "path": UPath(type_ == 'AWEChallengeDetailMultiColumnFeedViewController')},
            "back_btn": {"path": UPath(id_ == "leftBtnsContainerView", visible_ == True)},
        }

    def swipe_up(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=rect.height * 0.75)
        time.sleep(2)

    def click_back(self):
        if self["back_btn"].wait_for_existing(timeout=2, raise_error=False):
            self["back_btn"].click()
        else:
            self.app.get_device().press_back()  #
        time.sleep(1)

    def get_challenge_name(self):
        return self.challenge_name.text

    def load_more(self):
        previous_works_num = 0
        current_works_num = -1
        limit = 0
        while not current_works_num == previous_works_num and limit < 10:
            logger.debug("load more works(previous:%s, current:%s)" % (previous_works_num, current_works_num))
            self.scroll(distance_y=500)
            time.sleep(2)
            previous_works_num = current_works_num
            current_works_num = len(self.works_collection.items())
            limit += 1
        return limit > 0


class ContactTable(Control):
    elem_path = UPath(type_ == "AWEContactListTableViewCell")
    elem_class = Control


class UserPickerContactView(Window):
    window_spec = {"path": UPath(type_ == 'AWEUserPickerContactViewController')}

    def get_locators(self):
        return {
            "table_view": {"type": ContactTable, "path": UPath(type_ == 'UITableView')},
        }

    def select_first_friend(self):
        for _ in Retry(limit=5, interval=1, message="got empty contact list in 5 times"):
            contact_list = self["table_view"].items()
            if contact_list:
                break
        contact_list[-1].click()


class UserWorkCollection(Control):
    # elem_path = UPath(type_ == 'AWEUserWorkCollectionViewCell')
    elem_class = Control


class UserDetailView(Window):
    window_spec = {"path": UPath(controller_ == 'TIKTOKUserDetailViewController')}

    def get_locators(self):
        return {
            "user_work_list": {"type": UserWorkCollection, "path": UPath(type_ == "AWEUserWorkCollectionViewCell")}
        }

    def watch_first_work(self):
        for _ in Retry(limit=5, message="got empty work list in 5 times"):
            works = self.user_work_list.items()
            if works:
                break
        work = works[0]
        work.click()


class KeyboardWindow(Window):
    window_spec = {"path": UPath(type_ == "UIRemoteKeyboardWindow")}

    def get_locators(self):
        return {
            "Search": {"path": UPath(type_ == 'UIAccessibilityElementKBKey', ~label_ == '搜索|search|Search|검색|rechercher')},
            "Confirm": {"path": UPath(type_ == 'UIAccessibilityElementKBKey', label_ == '确认')},
            "keyboard_area": {"path": UPath(type_ == 'UIKeyboardLayoutStar')},
            "Delete": {"path": UPath(type_ == 'UIAccessibilityElementKBKey', label_ == 'Delete')},
        }

    def click_search(self):
        if self["Confirm"].wait_for_existing(timeout=2, raise_error=False):
            self["Confirm"].click()
            time.sleep(2)
        else:
            self["Search"].click()
        time.sleep(2)

    def click_delete(self):
        if self["Delete"].wait_for_existing(timeout=2, raise_error=False):
            self["Delete"].click()
