# -*- coding: utf-8 -*-
import time

from shoots.retry import Retry

from .base_panel import *
from uibase.controls import PopupBase
from uibase.base import UIScene
from .cvbase import CVBase

class AWEUserActionSheetView(Window):
    """ensure logout sheet view
    """
    window_spec = {"path": UPath(type_ == 'AWEUserActionSheetView')}

    def get_locators(self):
        return {
            "switch_account": {"path": UPath(type_ == 'UILabel', label_ == 'Switch account')},
            "logout": {"path": UPath(~text_ == "^(退出登录|Log out)")},
            "cancel": {"path": UPath(~text_ == 'Cancel|取消')},
            "log_out": {"path": UPath(id_ == "Log out")}

        }

    def click_settings_logout(self):
        if self.logout.wait_for_existing(timeout=3, raise_error=False):
            self.logout.click()
        elif self.log_out.existing:
            self.log_out.click()


class LogoutConfirmAlert(Window):
    """confirm alert
    """
    window_spec = {"path": UPath(type_ == 'AWEUIAlertView')}

    def get_locators(self):
        return {
            "cancel": {"path": UPath(type_ == 'AWEUIButton', ~label_ == 'Cancel|取消')},
            "confirm": {"path": UPath(type_ == "UIButtonLabel", ~label_ == "Log out|退出登录")},
            "cancel": {"path": UPath(type_ == 'AWEUIButton', label_ == 'Cancel')},
            "confirm": {"path": UPath(type_ == "UIButtonLabel", label_ == "Log out")},
            "logout_btn": {"path": UPath(id_ == "Log out", type_ == "AWEUIButton")}

        }

    def click_confirm_btn(self):
        if self.logout_btn.wait_for_visible(timeout=3, raise_error=False):
            self.logout_btn.click()
        elif self.confirm.wait_for_visible(timeout=3, raise_error=False):
            self.confirm.click()


class User(Control):
    def get_locators(self):
        return {
            "user": {"path": UPath(type_ == 'UITableViewCellContentView')}
        }

    def to_user_profile(self):
        self["user"].click()
class UserNew(Control):
    def get_locators(self):
        return {
            "user": {"path": UPath(type_ == 'UITableViewCellContentView')},
            "nick_name": {"path": UPath(type_ == 'UITableViewCellContentView') / UPath(id_=='nickNameLable')},
            "user_name": {"path": UPath(type_ == 'UITableViewCellContentView') / UPath(id_=='bioContentLable')},
            "ublock_btn": {"path": UPath(type_ == 'UITableViewCellContentView') / UPath(label_=='Unblock',type_=='UIButtonLabel')},
            "block_btn": {"path": UPath(id_ == "doAndUndoButton", label_ == "Block")},
        }

    def to_user_profile(self):
        self["user"].click()

    def get_user_nickname(self):
        if self['nick_name'].wait_for_existing(timeout=5, raise_error=True):
            return self['nick_name'].text

    def get_user_username(self):
        if self['user_name'].wait_for_existing(timeout=5, raise_error=True):
            return self['user_name'].text

    def unblock_user(self):
        if self['ublock_btn'].wait_for_existing(timeout=5, raise_error=True):
            return self['ublock_btn'].click()

    def get_ation_btn(self,action):
        return  self[f'{action}_btn'].wait_for_existing(timeout=5, raise_error=True)


class UserListNew(Control):
    elem_class = UserNew
    elem_path = UPath(~type_ =="TTKIMBlockCell|TTKBlockAccountsTableViewCell")


class UserList(Control):
    elem_class = User
    elem_path = UPath(type_ == 	"AWEUIListTableViewCell")

class ImageViewList(Control):
    elem_class = Control
    elem_path = UPath(type_ == "TTKAlbumAssetListCollectionViewCell")

class SettingsWin(BasePanel, CVBase):
    """Settings and privacy window
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "back_btn": {"path": UPath(~label_ == 'Go back|Back|上一步|返回',index=0)},
            "log_out": {"path": UPath(text_ == 'Log out')},
            "security_login": {"type": Control, "path": UPath(label_ == 'Security and login', visible_ == True)},
            "security": {"type": Control, "path": UPath(label_ == "Security") / 0 / 0 / 0 / 1 / 0},
            "privacy": {"path": UPath(text_ == 'Privacy')},
            "privacy_setting_back": {"path": UPath(label_ == 'Back')},
            # change language
            "app_language": {"path": UPath(~type_ == "UILabel|TUXLabel", ~label_ == 'App language|应用语言|Language|语言')},
            "language_setting": {"type": Control, "path": UPath(id_ == '(titleLabel)', ~label_ == 'App language|应用语言', visible_ == True)},
            "creator_tools": {"path": UPath(type_ == 'UILabel', label_ == 'Creator tools')},
            "manage_account": {"type": Control, "path": UPath(id_ == "accessibilityProxyView", label_ == "Account .")},
            "delete_account_confirm": {"type": DeleteAccountConfirmWebview, "path":
                UPath(type_ == 'IESWKWebView')},
            "delete_account_confirm_check": {"type": DeleteAccountConfirmCheckWebview, "path":
                UPath(type_ == 'IESWKWebView')},
            "delete_account_code": {"type": DeleteAccountCodeWebview, "path":
                UPath(controller_ == 'AWETabBarController')},
            "delete_account_confirm_3": {"type": DeleteAccountConfirm3Webview, "path":
                UPath(controller_ == 'AWETabBarController')},
            "switch_account": {"path": UPath(label_ == 'Switch account', visible_ == True)},
            "pop_up_switch_account": {"path": UPath(type_ == 'UITableView') / 3 / UPath(id_ == '(userNameLabel)')},
            "pop_up_switch_sg_idc": {"path": UPath(id_ == "null", label_ == "sg_idc") / UPath(type_ == "UIImageView", visible_ == True)},
            "add_account_btn": {"path": UPath(label_ == 'Add account')},
            "delete_account_continue": {"type": DeleteAccountContinueWebview,
                                        "path": UPath(controller_ == 'AWETabBarController')},

            "title": {"path": UPath(id_ == 'titleLabel', label_ == 'Settings and privacy')},

            "switch_account": {"path": UPath(type_ == 'UILabel', label_ == 'Switch account')},
            "switch_account_in_setting": {"path": UPath(id_ == "accessibilityProxyView", label_ == "Switch account.")},

            "pop_up_switch_account": {"path": UPath(type_ == 'AWEAddAccountEffectView') / UPath(
                type_ == 'UITableView') / 3 / UPath(id_ == '(userNameLabel)')
                                      },
            "balance": {"path": UPath(text_ == 'Balance')},
            "open_settings": {"path": UPath(id_ == "(label)", label_ == 'Open settings')},
            "block_list": {"path": UPath(label_ == 'Blocked accounts',index=0)},
            "first_block_user": {"path": UPath(type_ == 'UITableViewCellContentView')},
            "user_list": {"type": UserList, "root": 'block_panel', "path": UPath(type_ == 'UITableView')},
            "user_list_new": {"type": UserListNew,  "path": UPath(controller_=='AWEIMBlockListViewController')},

            "block_panel": {"path": UPath(controller_ == 'AWEIMBlockListViewController')},
            "unblock_first": {"path": UPath(type_ == 'UITableView') / 0 / UPath(type_ == 'AWEUIListCellActionButton',
                                                                                label_ == 'Unblock')},
            "block_first": {"path": UPath(type_ == 'UITableView') / 0 / UPath(type_ == 'AWEUIListCellActionButton',
                                                                              label_ == 'Block')},
            "setting_panel": {"path": UPath(type_=='TTKSettingHeaderTitleCollectionViewCell')/UPath(label_ == 'Settings and privacy',type_=='UILabel')},
            "QR_code_btn": {"path": UPath(type_ == "TUXNavBar") / UPath(id_ == "endContainerView")},
            "tiktok_code": {"path": UPath(type_ == 'UIView', id_ == '(QRCodeView)')},
            "scan_QR_code_button": {"path": UPath(id_ == "(Scan QR code)")},
            "qr_photos_button": {"path": UPath(type_ == "UIButtonLabel", label_ == "Photos", visible_ == True)},
            "album_list": {"type": ImageViewList, "path": UPath(type_ == "TTKAlbumListCollectionView")},
            "translation_language": {"path": UPath(type_ == "UILabel", label_ == "Translation language")},
            "translation_language_panel": {"path": UPath(type_ == "AWENavigationBar") / UPath(id_ == "titleLabel", label_ == "Translation language")},
            "translation_language_done": {"path": UPath(text_ == "Done")},
            "report_a_problem": {"path": UPath(type_ == 'UILabel', label_ == 'Report a problem')},
            "kids_screen_time_text": {
                "path": UPath(text_ == "Enter Screen Time Management passcode", visible_ == True)},
            "kids_screen_time_input": {"path": UPath(type_ == "LynxScrollView") / 0 / 1 / 0 / 0 / 0},
            "kids_next": {"path": UPath(text_ == "Next")},
            "play_back": {"path": UPath(text_ == "Playback")},
            "silent_btn": {"path": UPath(type_ == "UITableView") / 1 / UPath(id_ == "accessoryViewContainerView", depth=6)},
            "settings_and_parivacy_popup": {"path": UPath(id_ == "Settings and privacy")},
            "settings_and_parivacy_popup1": {"path": UPath(id_ == "tableView") / 3 / UPath(id_ == "titleLabel", depth=5)}
        }
    def CheckSettingsAndPrivacy(self):
        if self["settings_and_parivacy_popup"].wait_for_visible(raise_error=False):
            return self["settings_and_parivacy_popup"].wait_for_visible()
        elif self["settings_and_parivacy_popup1"].wait_for_visible():
            return self["settings_and_parivacy_popup1"].wait_for_visible()

    def click_open_settings(self):
        if self.open_settings.wait_for_existing(raise_error=False, timeout=5):
            self.open_settings.click()
            # 打开手机定位服务
            setting_win = SettingLocationWin(root=self.app)
            setting_win.open_location_service()
            time.sleep(5)
            self.app.foreground()

    def add_account(self):
        return self["add_account_btn"].click()

    def report_a_problem(self):
        for _ in Retry(limit=3, raise_error=False):
            if self["report_a_problem"].wait_for_existing(raise_error=False, timeout=5):
                break
            self.swipe(y_direction=8)
        return self["report_a_problem"].click()

    def go_back(self):
        self["back_btn"].click()

    def enter_privacy_panel(self):
        self["privacy"].wait_for_visible(timeout=3, raise_error=False)
        self["privacy"].click()

    def back_profile_page(self):
        time.sleep(2)
        self["privacy_settings_back"].click()

    def navigate_security_and_login(self):
        logger.debug("click security and login")
        self.refresh()
        self["security"].wait_for_ui_stable()
        time.sleep(2)
        self["security"].wait_for_visible(timeout=10, interval=1, raise_error=True)
        self["security"].click()

    def navigate_manage_account(self):
        # self.refresh()
        # self["manage_account"].wait_for_ui_stable()
        time.sleep(5)
        self["manage_account"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["manage_account"].click()

    def logout(self):
        logger.debug("ensure log out cell visible")
        while not self.log_out.existing or not self.log_out.visible:
            self.swipe(y_direction=1)
            time.sleep(2)
        logger.debug("click Log out Button")
        self.log_out.click()
        if self["kids_screen_time_text"].wait_for_existing(timeout=2, raise_error=False):
            for i in range(0, 4):
                self["kids_screen_time_input"].children[i].click()
            self["kids_next"].click()
        user_action_sheet_view = AWEUserActionSheetView(root=self.root)
        if user_action_sheet_view.wait_for_existing(timeout=5, raise_error=False):
            logger.debug("confirm log out")
            user_action_sheet_view.click_settings_logout()
        logout_confirm_alert = LogoutConfirmAlert(root=self.root)
        logout_confirm_alert.click_confirm_btn()
        # if self["back_btn"].wait_for_existing(timeout=3, raise_error=False):
        #     try:
        #         self["back_btn"].click()
        #     except:
        #         pass

    def silent_start_app(self):
        logger.debug("ensure play back btn visible")
        while not self.play_back.existing or not self.play_back.visible:
            self.swipe(y_direction=1)
            time.sleep(2)
        logger.debug("click play back btn")
        self.play_back.click()
        time.sleep(2)
        self.silent_btn.click()
        time.sleep(2)
        self.app.restart()

    def into_language_select_panel(self):
        time.sleep(2)
        for i in range(5):
            if not self.cv_wait_for_visible("语言"):
                self.swipe(y_direction=1)
        self["app_language"].click()
        if self["language_setting"].wait_for_existing(timeout=5, raise_error=False):
            self["language_setting"].click()
        time.sleep(1)

    def into_translation_language_select_panel(self):
        time.sleep(2)
        self["app_language"].click()
        if self["translation_language"].wait_for_existing(timeout=5, raise_error=False):
            self["translation_language"].click()
        time.sleep(1)

    def set_translation_language(self, content_language):
        content_language_ele = Control(UPath(text_ == content_language, visible_ == True), root=self.app)
        while not content_language_ele.wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        content_language_ele.click()
        self["translation_language_done"].click()

    def find_creator_tools_and_click(self):
        time.sleep(5)
        self["creator_tools"].click()

    def delete_account_continue_click(self):
        self.refresh()
        self.delete_account_continue.continue_click()

    def next_page_click_continue(self):
        self.refresh()
        self.delete_account_confirm_check.next_page_continue_click()

    def delete_account_skip_click(self):
        self.refresh()
        self.delete_account_confirm.skip_click()

    def delete_account_code_right(self, code, delete=False):
        self.delete_account_code.input_right_code(code)
        if delete:
            return self.delete_account_confirm_3.delete_account_confirm()
        else:
            return self.delete_account_confirm_3.delete_account_cancel()

    def find_and_switch_account(self):
        count = 0
        while not self.switch_account.existing or not self.switch_account.visible or count < 10:
            count = count + 1
            self.swipe(y_direction=1)
            time.sleep(2)
        logger.debug("click Switch account Button")
        user_action_sheet_view = AWEUserActionSheetView(root=self.root)
        if user_action_sheet_view.wait_for_existing(timeout=5, raise_error=False):
            user_action_sheet_view.switch_account.click()
        self["switch_account"].click()

    def find_and_switch_account_in_settings(self):
        time.sleep(5)
        self["manage_account"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self.swipe(y_direction=1)
        time.sleep(2)
        self.swipe(y_direction=1)
        time.sleep(2)
        logger.debug("click Switch account Button")
        user_action_sheet_view = AWEUserActionSheetView(root=self.root)
        if user_action_sheet_view.wait_for_existing(timeout=5, raise_error=False):
            user_action_sheet_view.switch_account.click()
        self["switch_account_in_setting"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["switch_account_in_setting"].click()

    def click_switch_account(self):
        time.sleep(4)
        UIScene(self.app).dump()
        self["pop_up_switch_account"].click()

    def click_switch_account_sg_idc(self):
        time.sleep(4)
        UIScene(self.app).dump()
        self["pop_up_switch_sg_idc"].click()

    def judge_into_settings_page(self):
        return self["title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def enter_balance_page(self):
        self["balance"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["balance"].click()
        time.sleep(100)

    def block_list_click(self):
        self["block_list"].click()
        time.sleep(5)
    def block_account_btn_exist(self):
        return self["block_list"].wait_for_existing(timeout=5, raise_error=False)

    def to_block_profile(self, index=0):
        ui_scene = UIScene(self.app).dump()
        self.app.testcase.log_record("ui_scene", attachments={ui_scene: ui_scene})
        users = self["user_list_new"].items()
        logger.info(f"users count: {len(users)}")
        if len(users)==0:
            users = self["user_list"].items()
        first_user = users[index]
        return first_user.get_user_username() #289版本,blocklist页面头像nickname不可点击进入他人页

    def unblock_first_user(self):
        self["unblock_first"].click()
        return self["unblock_first"].wait_for_disappear(timeout=3, raise_error=False)

    def unblock_first_user_new(self, index=0):
        users = self["user_list_new"].items()
        logger.info(f"users count: {len(users)}")
        if len(users) == 0:
            users = self["user_list"].items()
        first_user = users[index]
        first_user.unblock_user()

    def get_first_user_status(self):
        users = self["user_list_new"].items()
        logger.info(f"len(users)：{len(users)}")
        if len(users) == 0:
            users = self["user_list"].items()
        first_user = users[0]
        logger.info(f"get_user_username:{first_user.get_user_username()}")
        return first_user.get_ation_btn("block")


    def wait_for_setting_panel_loading(self):
        return self["setting_panel"].wait_for_visible(timeout=5, raise_error=False)

    def QR_code_click(self):
        self["QR_code_btn"].click()

    def wait_for_tiktok_code_visible(self):
        return self["tiktok_code"].wait_for_visible(timeout=5, raise_error=False)


    def click_scan_QR_code_button(self):
        return self['scan_QR_code_button'].click()

    def click_QR_photos(self):
        return self["qr_photos_button"].click()

    def click_last_image(self):
        image_list = self['album_list'].items()
        logger.info("length of image_list: {}".format(len(image_list)))
        for i, image in enumerate(image_list):
            if image.wait_for_visible() and len(image_list) > 0:
                self['album_list'].items()[-1].click()
                return True
            raise 'Not Found Image'

    def get_album_list_scene(self):
        return UIScene(self['album_list']).dump()

    def swipe_up(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y= - rect.height*0.75)

    def swipe_down(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=rect.height*0.75)

class FeedbackPanel(BasePanel):
    """
    feedback panel
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "feedback_webview": {"type": FeedbackWebview, "path": UPath(type_ == "WKContentView")},
            "feedback_webview_v1": {"type": FeedbackWebviewV1, "path": UPath(type_ == "WKScrollView")},
            "feedback_webview_v2": {"type": FeedbackWebviewV2, "path": UPath(type_ == "WKScrollView")},
            "image": {"type": Control, "path": UPath(type_ == 'UICollectionView')/10/UPath(id_ == 'ic_selectpic_before')},
            "confirm_btn": {"type": Control, "path": UPath(type_ == 'AWEButton', visible_ == True)}
        }

    def select_image(self):
        self["image"].click()
        time.sleep(2)
        self["confirm_btn"].click()
        time.sleep(5)


class FeedbackWebview(Webview):

    view_spec = {"title": "TikTok",
                 "use_inject": True}

    def get_locators(self):
        return {
            "account_and_profile": {"type": WebElement, "path": UPath(text_ == "Account and profile", visible_ == True)},
            "submit_btn": {"type": WebElement, "path": UPath(type_ == "BUTTON")},
            "search_icon": {"type": WebElement, "path": UPath(type_ == "INPUT")},
            "reply_btn": {"type": WebElement, "path": UPath(text_ == "Reply", visible_ == True)}
        }

    def click_account_and_profile(self):
        return self["account_and_profile"].click()

    def click_submit_btn(self):
        self.scroll(distance_y=800)
        self["submit_btn"].click()

    def click_reply_btn(self):
        if self["reply_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["submit_btn"].click()
        time.sleep(3)

    def click_search_icon(self):
        self["search_icon"].click()
        time.sleep(3)


class FeedbackWebviewV1(Webview):

    view_spec = {"title": "TikTok",
                 "use_inject": True}

    def get_locators(self):
        return {
            "hw_create_account": {"type": WebElement, "path": UPath(text_ == "How to create an account", visible_ == True)},
            "feedback_info": {"type": WebElement, "path": UPath(type_ == "TEXTAREA", visible_ == True)},
            "submit_btn": {"type": WebElement, "path": UPath(text_ == "Submit")},
            "upload_image": {"type": WebElement, "path": UPath(id_ == "upload-image-pla-union")},
            "input_issue": {"type": WebElement, "path": UPath(type_ == "INPUT")},
            "issue_1": {"type": WebElement, "path": UPath(class_ == "search-result-container-AjVVH") / 0 / 0}
        }

    def click_hw_create_account(self):
        return self["hw_create_account"].click()

    def click_upload_image(self):
        return self["upload_image"].click()

    def input_issue(self, content):
        self.device_input(self["input_issue"], content)
        time.sleep(2)
        self["issue_1"].click()
        time.sleep(3)

    def input_feedback_info(self, content):
        self.device_input(self["feedback_info"], content)
        time.sleep(2)
        for _ in Retry(limit=2, raise_error=False):
            if self["submit_btn"].wait_for_existing(timeout=3, raise_error=False):
                self["submit_btn"].click()

    def device_input(self, text_view, text):
        text_view.click()
        text_view.text = ""
        time.sleep(1)
        device = self.app.get_device()
        try:
            device.send_keys(text)
        except BDCError:
            text_view.send_keys(text)
        except:
            logger.info("direct input error")


class FeedbackWebviewV2(Webview):

    view_spec = {"title": "TikTok",
                 "use_inject": True}

    def get_locators(self):
        return {
            "no_btn": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "No")},
            "more_help_btn": {"type": WebElement, "path": UPath(class_ == "csp-cell__title")},
            "feedback_title": {"type": WebElement, "path": UPath(class_ == "csp-header__title__text")},
        }


    def click_no_btn(self):
        if self["no_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["no_btn"].click()

    def click_more_help_btn(self):
        time.sleep(3)
        return self["more_help_btn"].click()

    def check_feedback_title(self):
        return self["feedback_title"].wait_for_existing(timeout=5, raise_error=False)


class ChooseLanguageViewController(Window):
    """choose the language for app
    """
    window_spec = {"path": UPath(controller_ == 'AWEChooseLanguageViewController')}

    def get_locators(self):
        return {
            "Eng": {"path": UPath(text_ == 'English')},
            "done": {"path": UPath(type_ == "UIButtonLabel", ~text_ == '完成|Done')},
            "cancel": {"path": UPath(text_ == 'Cancel')},
        }

    def select_language(self, language):
        if str(language).lower() == "en":
            self["Eng"].click()
            time.sleep(1)
        self["done"].click()
        time.sleep(1)
        # 有一种异常case：点击语言，Done按钮还是灰色的情况，但实际已经生效，兼容之
        if self["cancel"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            self["cancel"].click()



class PrivacyWin(BasePanel):
    """
    privacy window
    """
    window_spec = {"path": UPath(controller_ == 'AWEIMPrivacySettingViewController')}

    def get_locators(self):
        return {
            "privacy_title": {"path": UPath(label_ == 'Privacy')},
            "private_account_off": {"path": UPath(value_ == '1') / UPath(id_ == '(coverView)')},
            "private_account_on": {"path": UPath(type_ == 'AWESettingSwitch', label_ == 'Private account') / UPath(
                id_ == '(switchWellImageViewContainer)')},
            "suggest_setting_entry": {"path": UPath(label_ == 'Suggest your account to others', visible_ == True)},
            "downloads_setting_entry": {"path": UPath(label_ == 'Downloads', visible_ == True)},
            "downloads_on": {"path": UPath(label_ == 'On', visible_ == True)},
            "downloads_off": {"path": UPath(label_ == 'Off', visible_ == True)},
            "coments_setting_entry": {"path": UPath(label_ == 'Mentions', visible_ == True)},
            "mentions_setting_enter": {"path": UPath(label_ == 'Mentions', visible_ == True)},
            "following_setting_enter": {"path": UPath(label_ == 'Following list', visible_ == True)},
            "duet_setting_entry": {"path": UPath(label_ == 'Duet', visible_ == True)},
            "stitch_setting_entry": {"path": UPath(label_ == 'Stitch', visible_ == True)},
            "liked_setting_entry": {"path": UPath(label_ == 'Liked videos', visible_ == True)},
            "message_setting_entry": {"path": UPath(label_ == 'Direct messages')},
            "privacy_back": {"path": UPath(type_ == 'UIButton')},
            "ad_setting_on": {
                "path": UPath(type_ == 'AWESettingSwitch', label_ == 'Ad settings') / UPath(id_ == '(coverView)')},
            "ad_setting_off": {"path": UPath(value_ == '1') / UPath(id_ == '(coverView)')},
            "block_list": {"path": UPath(label_ == 'Blocked accounts',index=0)},
            "duet_status_everyone": {"path": UPath(type_ == 'UITableView') / 6 / UPath(label_ == 'Everyone')},
            "duet_status_friends": {"path": UPath(label_ == 'Friends')},
            "duet_status_only_me": {"path": UPath(type_ == 'UITableView') / 5 / UPath(label_ == 'Only me')},

        }

    def find_privacy_page(self):
        return self["privacy_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def find_private_account_on_and_click(self):
        self["private_account_on"].click()

    def private_account_off(self):
        if self["private_account_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return False
        else:
            return True

    def find_private_account_off_and_click(self):
        self["private_account_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["private_account_off"].click()

    def download_status_on_or_off(self):
        if self["downloads_on"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return True
        if self["downloads_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return False

    def download_status_off(self):
        if self["downloads_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return True

    def find_download_entry_and_click(self):
        self["downloads_setting_entry"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["downloads_setting_entry"].click()
        time.sleep(2)

    def download_entry_and_exist(self):
        if self["downloads_setting_entry"]:
            return True
        else:
            return False

    def privacy_back_and_click(self):
        self["private_account"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["privacy_back"].click()

    def ad_setting_on_and_click(self):
        self["ad_setting_on"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["ad_setting_on"].click()

    def ad_setting_off_and_click(self):
        self["ad_setting_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["ad_setting_off"].click()

    def swipe_to_bottom(self):
        i = 0
        for i in range(10):
            self.swipe(y_direction=1)
            time.sleep(1)

    def swipe_up(self):
        self.scroll(distance_y=1, coefficient_y=0.55)

    def comment_setting_entry_and_click(self):
        self["coments_setting_entry"].click()

    def mentions_setting_and_click(self):
        self["mentions_setting_enter"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["mentions_setting_enter"].click()

    def following_setting_and_click(self):
        self["following_setting_enter"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["following_setting_enter"].click()

    def duet_setting_entry_and_click(self):
        self["duet_setting_entry"].click()

    def stitch_setting_entry_and_click(self):
        self["stitch_setting_entry"].click()

    def liked_setting_entry_and_click(self):
        self["liked_setting_entry"].click()

    def message_setting_entry_and_click(self):
        self["message_setting_entry"].click()

    def duet_setting_status(self):
        status = 0
        if self["duet_status_everyone"].existing:
            status = 1
        elif self["duet_status_friends"].existing:
            status = 2
        elif self["duet_status_only_me"].existing:
            status = 3
        else:
            status = 0

        return status



class SecurityAndLoginPanel(BasePanel):
    """Security and login window
        """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "manage_app_permissions": {"path": UPath(label_ == 'Manage app permissions', visible_ == True)}
        }

    def navigate_manage_app_permission(self):
        logger.debug("click manage app permission")
        self["manage_app_permissions"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["manage_app_permissions"].click()


class ManageAppPermissionPanel(BasePanel):
    """Manage app permission window
            """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "no_app_section": {"path": UPath(type_ == "AWEI18NNoAuthorizedAppsView")},
            "no_app_icon": {
                "path": UPath(type_ == "AWEI18NNoAuthorizedAppsView") / UPath(type_ == "UIImageView", index=0)},
            "no_app_authorized_desc": {
                "path": UPath(type_ == "AWEI18NNoAuthorizedAppsView") / UPath(type_ == "UILabel", index=1)},
            "no_app_authorized_title": {
                "path": UPath(type_ == "AWEI18NNoAuthorizedAppsView") / UPath(type_ == "UILabel", index=0)},
            "authorized_app_list": {"type": Control, "path": UPath(type_ == "TTKAuthorizedAppsViewController") / UPath(type_ == "UITableView")}
        }

    def check_app_icon(self):
        time.sleep(1)
        return self["no_app_icon"].visible

    def no_app_authorized_title(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            return self["no_app_authorized_title"].text == "No apps authorized yet"

    def no_app_authorized_desc(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            return self[
                   "no_app_authorized_desc"].text == "Apps with permission to access your TikTok data will appear here."

    def check_app_permission_panel(self):
        time.sleep(2)
        if self['no_app_section'].visible:
            logger.debug("There are no apps authorized")
            return True
        elif self['no_app_section'].visible == False:
            logger.debug("There are apps authorized")
            return False
        return ""

class ChangeToPublicAccount(PopupBase):
    def handle(self):
        pass

    window_spec = {"path": UPath(type_ == '_UIAlertControllerInterfaceActionGroupView') / 1}

    def get_locators(self):
        return {
            "public_account_yes": {"path": UPath(text_ == 'Confirm')},
            "public_account_no": {"path": UPath(text_ == 'Cancel')}
        }

    def public_account_confrim(self):
        self["public_account_yes"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["public_account_yes"].click()
        time.sleep(5)

    def public_account_cancel(self):
        self["public_account_no"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["public_account_no"].click()
        time.sleep(5)


class TurnOffAds(PopupBase):
    def handle(self):
        pass

    window_spec = {"path": UPath(id_ == '(animationView)')}

    def get_locators(self):
        return {
            "ads_turn_off": {"path": UPath(type_ == 'UIButtonLabel', label_ == 'Turn off and continue')},
            "ads_cancel": {"path": UPath(text_ == 'Cancel')}
        }

    def ads_turn_off(self):
        self["ads_turn_off"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["ads_turn_off"].click()
        time.sleep(5)


class PrivacyUserSettingPanel(BasePanel):
    """
    user setting window
    """
    window_spec = {"path": UPath(controller_ == 'AWEPrivacySettingUserControlViewController')}

    def get_locators(self):
        return {
            "set_everyone": {"path": UPath(text_ == 'Everyone', visible_ == True)},
            "set_people_you_know": {
                "path": UPath(type_ == 'UILabel', label_ == 'People you follow', visible_ == True)},
            "set_friends": {"path": UPath(text_ == 'Friends', visible_ == True)},
            "set_only_me": {"path": UPath(text_ == 'Only me', visible_ == True)},
            "set_no_one": {"path": UPath(text_ == 'No one', visible_ == True)},
            "chat_friends": {"type": Control, "path": UPath(type_ == 'UITableView') / 3 / UPath(label_ == 'Friends')},
            "chat_no_one": {"type": Control, "path": UPath(type_ == 'UITableView') / 2 / UPath(id_ == '(titleLabel)')},
            "group_friends": {"type": Control,
                              "path": UPath(type_ == 'UITableView') / 1 / UPath(id_ == '(titleLabel)')},
            "group_no_one": {"type": Control, "path": UPath(type_ == 'UITableView') / 0 / UPath(label_ == 'No one')},
            "downloads_setting_click": {
                "path": UPath(type_ == 'UITableViewCellContentView') / UPath(id_ == '(titleLabel)')},
            "downloads_back": {"path": UPath(label_ == 'Back', visible_ == True)},
        }

    def find_download_setting_and_click(self):
        self["downloads_setting_click"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["downloads_setting_click"].click()
        time.sleep(3)

    def back_and_click(self):
        self["downloads_back"].click()

    def set_everyone_and_click(self):
        time.sleep(4)
        self["set_everyone"].click()
        time.sleep(1)

    def set_people_you_know_and_click(self):
        time.sleep(4)
        self["set_people_you_know"].click()
        time.sleep(1)

    def set_friends_and_click(self):
        time.sleep(4)
        self["set_friends"].click()
        time.sleep(1)

    def set_only_me_and_click(self):
        time.sleep(4)
        self["set_only_me"].click()
        time.sleep(1)

    def set_no_one_and_click(self):
        time.sleep(5)
        self["set_no_one"].click()

    def chat_friends_and_click(self):
        self["chat_friends"].click()

    def chat_no_one_and_click(self):
        self["chat_no_one"].click()

    def group_friends_and_click(self):
        self["group_friends"].click()

    def group_no_one_and_click(self):
        self["group_no_one"].click()


class CreatorToolsPanel(Window):
    window_spec = {"path": UPath(controller_ == 'TTKCreatorToolsPageViewController')}

    def get_locators(self):
        return {
            "play_list": {"path": UPath(text_ == 'Playlists')},
            "title": {"path": UPath(label_ == 'Creator tools')},
            "analytics": {"path": UPath(text_ == 'Analytics')},

        }

    def play_list_option_visible(self):
        return self["play_list"].visible

    def click_play_list_option(self):
        self["play_list"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["play_list"].click()

    def get_playlists(self):
        print(self["playlists"].text)
        return self["playlists"]


class CreatePlaylistPanel(Window):
    window_spec = {
        "path": UPath(type_ == 'UINavigationTransitionView') / UPath(type_ == 'UIViewControllerWrapperView')}

    def get_locators(self):
        return {
            "name_Playlist_title": {"path": UPath(label_ == 'Name playlist')},
            "enter_playlist_name_texbox": {"path": UPath(id_ == 'bodyStackView')},
            "next_button": {"path": UPath(type_ == "TUXLegendButton", label_ == 'Next')},
            "video_play_list": {"path": UPath(type_ == 'UICollectionView', id_ == 'collectionView')},
            "first_video_unavailable": {
                "path": UPath(type_ == 'UICollectionView') / 1 / UPath(id_ == 'unselectableMaskView')},
            "first_video": {"path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'TUXCheckbox')},
            "second_video_unavailable": {
                "path": UPath(type_ == 'UICollectionView') / 2 / UPath(id_ == 'unselectableMaskView')},
            "second_video": {"path": UPath(type_ == 'UICollectionView') / 2 / UPath(type_ == 'TUXCheckbox')},
            "next": {"path": UPath(type_ == 'TUXLegendButton', id_ == 'nextButton')},
            "create_playlist": {"path": UPath(id_ == 'Create playlist', type_ == 'UIButtonLabel')},
            "playlist_title": {"path": UPath(id_ == 'tv_mix_detail_title', visible_ == True)},
            "add_playlist_title": {"path": UPath(label_ == 'Add to playlist')}
        }

    def check_playlist_title_exists(self):
        return self["name_Playlist_title"].visible

    def check_add_playlist_title(self):
        return self["add_playlist_title"].visible

    def enter_playlist_name(self, desc):
        self["enter_playlist_name_texbox"].input(desc)

    def click_next_button(self):
        time.sleep(3)
        self["next_button"].click()

    def select_video(self):
        time.sleep(5)
        for item in self["video_play_list"].children[1:]:
            item = item.children[0]
            item_mask = Control(root=item, path=UPath(id_ == 'unselectableMaskView'))
            if not item_mask.visible:
                item_checkbox = Control(root=item, path=UPath(type_ == 'TUXCheckbox'))
                item_checkbox.click()
                break
        time.sleep(4)
        self["next"].click()

    def create_playlist(self):
        time.sleep(5)
        self["create_playlist"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["create_playlist"].click()


class PlaylistPanel(Window):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "playlist_title": {"path": UPath(controller_ == 'TikTokPlayListDetailPanelViewController') / 1 / 0 / UPath(
                type_ == 'UILabel')},
            "playlist_settings": {"path": UPath(controller_ == 'TikTokPlayListDetailPanelViewController') / 1 / 0 / 2},
            "delete_playlist": {"path": UPath(id_ == 'stackView') / 4},
            "delete_button": {
                "path": UPath(type_ == 'UIButtonLabel', text_ == 'Delete', id_ == 'Delete', visible_ == True)}
        }

    def getPlaylistTitle(self):
        time.sleep(3)
        return self["playlist_title"].text

    def delete_playlist(self):
        time.sleep(1)
        self["playlist_settings"].click()
        time.sleep(2)
        self["delete_playlist"].click()
        time.sleep(2)
        self["delete_button"].click()


class SecurityAndLoginPanel(Window):
    """Security and account device management interface
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "security_alerts": {"path": UPath(id_ == "accessibilityProxyView", label_ == "Security alerts.")},
            "manage_device": {"path": UPath(id_ == "accessibilityProxyView", label_ == "Manage devices.")},
            "manage_app_permissions": {"path": UPath(label_ == 'Manage app permissions')},
            "no_app_icon": {"type": Control, "path": UPath(id_ == 'iv_empty')},
        }

    def manage_device(self):
        if self["manage_device"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["manage_device"].click()
            return True
        else:
            return False

    def security_check(self):
        if self["security_alerts"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["security_alerts"].click()
            return True
        else:
            return False

    def manage_app_permissions(self):
        return self['manage_app_permissions'].click()

class SecurityCheckWebview(Webview):
    view_spec = {
        "title": None
    }

    def get_locators(self):
        return {
            "title": {"type": WebElement, "path": UPath(label_ == 'WebView')},
        }

    def page_loading(self):
        return self["title"].wait_for_existing()


class SecurityCheckPanel(Window):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {"security_check_webview": {"path": UPath(label_ == 'WebView')},
                }

    def security_check(self):
        time.sleep(2)
        return self["security_check_webview"].visible


class ManageAccountPanel(Window):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "password": {"type": Control, "path": UPath(id_ == "accessibilityProxyView", label_ == "Password.")},
            "user_information": {"type": Control, "path": UPath(type_ == "UITableView") / 1 / UPath(id_ == "accessibilityProxyView")},
            "email": {"type": Control, "path": UPath(label_ == 'Email', visible_ == True)},
            "delete_account": {"type": Control, "path": UPath(label_ == 'Delete account', visible_ == True)},
            "change_phone_num": {"path": UPath(type_ == 'UITableView')/4/UPath(id_ == 'focusView')},
            "change_phone_click": {"path": UPath(type_ == 'AWEUIButton', label_ == 'Change phone') / UPath(
                type_ == 'UIImageView', visible_ == True)},
            "use_phone_number": {"path": UPath(type_ == "TUXTextActionSheetItemView", label_ == "Use phone number")},
            "change_phone_pop_up": {"path": UPath(id_ == 'animationView')},
            "phone_num": {"path": UPath(type_ == 'UITableView') / 4 / UPath(type_ == 'AWESettingsCellContentView') /
                                  UPath(id_ == 'detailLabel')
                          }
        }

    def click_change_password(self):
        time.sleep(5)
        self["password"].wait_for_visible(timeout=5)
        self["password"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        time.sleep(5)
        self["password"].click()

    def click_change_email(self):
        time.sleep(5)
        self["user_information"].wait_for_visible(timeout=5)
        self["user_information"].click()
        time.sleep(5)
        self["email"].wait_for_visible(timeout=5)
        self["email"].click()

    def click_delete_account(self):
        self["delete_account"].wait_for_visible(timeout=5)
        self["delete_account"].click()

    def change_phone_num(self):
        self["change_phone_num"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["change_phone_num"].click()
        self["change_phone_pop_up"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        self["change_phone_click"].click()
        self["use_phone_number"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        self["use_phone_number"].click()

    def get_phone_num(self):
        time.sleep(5)
        self.refresh()
        num_str = self["phone_num"].text
        return num_str.split('*')[-1][0:2]


class PasswordCodePanel(Window):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "phone_code_input": {
                "path": UPath(type_ == 'TMVerificationCodeInputView') / UPath(id_ == 'containerView')},
            "wrong_notice": {"path": UPath(type_ == 'TMErrorHintView')},
            "error_msg": {"path": UPath(label_ == 'Your new password can’t be the same as your old password')},
            "input_password": {"path": UPath(id_ == 'passwordInputView') /
                                       UPath(type_ == 'TikTokAccountInfoTextField')},
            "password_input_confirm": {"path": UPath(type_ == 'TMLoginButton')},
            "password_input_clear": {"path": UPath(id_ == 'icoLoginClear')},
            "back_btn": {"path": UPath(id_ == 'icon_back', type_ == 'UIButton')}
        }

    def input_wrong_code(self, phone_code):
        time.sleep(2)
        self.input_code(phone_code)
        self["wrong_notice"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def input_code(self, phone_code):
        self["phone_code_input"].click()
        for i in range(len(phone_code)):
            self["phone_code_input"].children[i].input(phone_code[i])

    def input_right_code(self, phone_code):
        time.sleep(2)
        self["back_btn"].click()
        ManageAccountPanel(root=self.app).click_change_password()
        time.sleep(2)
        self.input_code(phone_code)

    def input_password_wrong(self, password):
        if self["password_input_clear"].visible:
            self["password_input_clear"].click()
        time.sleep(2)
        self["input_password"].input(password)
        return not self["password_input_confirm"].enabled

    def input_password_original(self, password):
        if self["password_input_clear"].visible:
            self["password_input_clear"].click()
        self["input_password"].input(password)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            self.refresh()
            time.sleep(2)
            print(self["error_msg"])
            return self["error_msg"].existing
        else:
            return False

    def input_password_right(self, password):
        if self["password_input_clear"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            self["password_input_clear"].click()
        self["input_password"].input(password)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            return True
        else:
            return False


class ManageDeviceWebview(Webview):
    view_spec = {
        # "url": "passport\/safe\/login_device\/index"
        "title": "tiktok device management"
    }

    def get_locators(self):
        return {
            "current_device": {"type": WebElement, "path": UPath(class_ == 'component-device-item isOwn')},
            "device_list": {"type": WebElement, "path": UPath(class_ == 'device-list')},
            "first_device_login_time": {"type": WebElement, "path": UPath(class_ == 'device-list') / 1 / UPath(
                class_ == 'content-login-time P2-Regular')},
            "delete_first_device": {"type": WebElement,
                                    "path": UPath(class_ == 'device-list') / 2 / UPath(type_ == 'I')},
            "delete_device_confirm": {"type": WebElement, "path": UPath(text_ == 'Remove')},
            "deleting": {"type": WebElement, "path": UPath(class_ == 'component-common-loading')},
            "delete_success_title": {"type": WebElement, "path": UPath(class_ == 'toast-content', visible_ == True,
                                                                       text_ == "Removed device")},

        }

    def delete_device(self):

        UIScene(self.app).dump()
        UIScene(self["current_device"]).dump()
        page_loading = self["current_device"].wait_for_visible()
        history_device = self["device_list"].wait_for_visible()
        if page_loading and history_device:
            first_login_info = self["first_device_login_time"].text
            self["delete_first_device"].click()
            self.refresh()
            self["delete_device_confirm"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
            self["delete_device_confirm"].click()
            self.refresh()
            self["deleting"].wait_for_disappear()
            self.refresh()
            self["first_device_login_time"].refresh()
            new_first_login_info = self["first_device_login_time"].text
            return new_first_login_info != first_login_info or self["delete_success_title"].wait_for_existing(
                timeout=2, raise_error=False)
        else:
            return False


class DeleteAccountConfirmWebview(Webview):
    view_spec = {
        "url": "https://www.tiktok.com/web-inapp/account/delete/confirm?.*"
    }

    def get_locators(self):
        return {
            "continue": {"type": WebElement, "path": UPath(type_ == 'SPAN')},
            "continue_checkbox": {"type": WebElement, "path": UPath(class_ == 'TUXCheckbox__icon') / UPath(
                class_ == 'text-color-LineSecondary'
            )},
            "continue_btn": {"type": WebElement, "path": UPath(text_ == 'Continue')},
            "skip_btn": {"type": WebElement, "path": UPath(type_ == 'BUTTON', text_ == 'Skip')}
        }

    def skip_click(self):
        self["skip_btn"].click()

    def next_page_continue_click(self):
        time.sleep(5)
        self["continue_btn"].click()


class DeleteAccountContinueWebview(Webview):
    view_spec = {
        "url": "https://www.tiktok.com/web-inapp/account/delete/status.*"
    }

    def get_locators(self):
        return {
            "continue": {"type": WebElement, "path": UPath(type_ == 'SPAN')},
            "continue_checkbox": {"type": WebElement, "path": UPath(class_ == 'TUXCheckbox__icon')},
            "continue_btn": {"type": WebElement, "path":
                UPath(text_ == 'Continue')},
        }

    def continue_click(self):
        self["continue_checkbox"].click()
        self["continue_btn"].click()

    def next_page_continue_click(self):
        self["continue_btn"].click()

    def download_continue_click(self):
        self["continue_checkbox"].click()
        self["continue_btn"].click()

class DeleteAccountConfirmCheckWebview(Webview):
    view_spec = {
        "url": "https://www.tiktok.com/web-inapp/account/delete/confirm/check?.*"
    }

    def get_locators(self):
        return {
            "continue": {"type": WebElement, "path": UPath(type_ == 'SPAN')},
            "continue_checkbox": {"type": WebElement, "path": UPath(class_ == 'TUXCheckbox__icon')},
            "continue_btn": {"type": WebElement, "path":
                UPath(text_ == 'Continue')},
        }

    def next_page_continue_click(self):
        time.sleep(5)
        self["continue_btn"].click()

class ManageDevicePanel(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "manage_device_panel": {"type": ManageDeviceWebview, "path": UPath(controller_ == 'AWEWebViewController')},
        }

    def manage_device_check(self):
        time.sleep(2)
        return self["manage_device_panel"].visible

    def delete_device(self):
        time.sleep(7)
        UIScene(self.app).dump()
        UIScene(self["manage_device_panel"]).dump()
        return self["manage_device_panel"].delete_device()


class BindorModifyPasswordPanel(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "phone_num_input": {"path": UPath(type_ == '_UITextFieldCanvasView')},
            "region_china": {"type": Control, "path": UPath(label_ == 'China mainland', visible_ == True)},
            "country_code": {
                "path": UPath(type_ == 'TMCountryCodeView') / UPath(type_ == "UILabel", index=0)},
            "send_code": {"type": Control,
                          "path": UPath(type_ == 'TMLoginButton', label_ == 'Send code', visible_ == True)},
            "phone_code_input": {"path": UPath(type_ == 'TMVerificationCodeInputView') / UPath(type_ == 'UIStackView',
                                                                                               visible_ == True)},
            "wrong_notice": {"path": UPath(label_ == 'Verification failed. Please click Resend and try again.',
                                           id_ == 'errorHintLabel')},
            "change_phone_num": {"path": UPath(type_ == 'SHSPhoneTextField')}
        }

    def input_phone_num(self, phone_num):
        time.sleep(2)
        self.change_region_to_china()
        self["phone_num_input"].click()
        self["phone_num_input"].input(phone_num)
        time.sleep(2)
        return self["send_code"].click()

    def input_change_phone_num(self, phone_num):
        time.sleep(2)
        self.change_region_to_china()
        self["change_phone_num"].click()
        self["change_phone_num"].input(phone_num)
        time.sleep(2)
        return self["send_code"].click()

    def change_region_to_china(self):
        self["country_code"].click()
        while not self["region_china"].wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        return self["region_china"].click()

    def input_code_wrong(self, code):
        self["phone_code_input"].click()
        self["phone_code_input"].input(code)
        return self["wrong_notice"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def input_code_right(self, code):
        self["phone_code_input"].wait_for_visible(timeout=5)
        self["phone_code_input"].click()
        self["phone_code_input"].input(code)
        return True


class ChangeEmailPanel(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "input_email": {"path": UPath(type_ == 'TMLoginInputView', id_ == 'emailInputView')},
            "email_input_clear": {"path": UPath(type_ == 'AWEAccountDeleteButton', id_ == 'deleteButton')},
            "send_code_btn": {"path": UPath(id_ == "primaryActionButton")},
            "wrong_notice": {"path": UPath(type_ == 'UILabel', id_ == 'errorHintLabel')},
            "input_code": {"path": UPath(type_ == 'TMVerificationCodeInputView', id_ == 'verificationCodeInputView')}
        }

    def input_email_wrong(self, email):
        while self["email_input_clear"].visible:
            self["email_input_clear"].click()
        self["input_email"].input(email)
        time.sleep(2)
        self["send_code_btn"].click()
        time.sleep(2)
        self['wrong_notice'].wait_for_visible(timeout=5)
        return self['wrong_notice'].text == 'Email is linked to another account. Unlink or try another email.' in self['wrong_notice'].text

    def input_email_right(self, email):
        while self["email_input_clear"].visible:
            self["email_input_clear"].click()
        self["input_email"].input(email)
        time.sleep(2)
        self["send_code_btn"].click()
        time.sleep(2)
        self.refresh()
        return self["input_code"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)


class BindorModifyPhonePanel(BasePanel):
    """绑定或者修改手机号界面
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "code_input": {"type": TextEdit, "path": UPath(id_ == 'containerView')},
            "new_phone_region": {"type": Control, "path": UPath(label_ == 'Enter new phone number')},
            "phone_num_input": {"type": TextEdit, "path": UPath(type_ == '_UITextFieldCanvasView')},
            "confirm": {"type": Control, "path": UPath(type_ == 'TMLoginButton')},
            "wrong_notice": {"path": UPath(id_ == 'errorHintLabel', label_ ==
                                           'Verification failed. Please click Resend and try again.')},
            "code-page-four-six": {"path": UPath(type_ == 'TMVerificationCodeView') / 0},
            "region_china": {"type": Control, "path": UPath(label_ == 'China mainland', visible_ == True)},
            "country_code": {
                "path": UPath(type_ == 'TMCountryCodeView') / UPath(type_ == "UILabel", index=0)},
        }

    def input_code_right(self, code):
        self["code_input"].click()
        time.sleep(1)
        if "4-digit" in self["code-page-four-six"].text:
            self["code_input"].input(code[2:])
        else:
            self["code_input"].input(code)
        return self["new_phone_region"].wait_for_visible(raise_error=False)

    def input_code_wrong(self, code):
        self["code_input"].input(code)
        return self["wrong_notice"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def input_new_phone_num(self, phone_num):
        self.change_region_to_china()
        self["phone_num_input"].click()
        self["phone_num_input"].input(phone_num)
        if self["confirm"].enabled:
            self["confirm"].click()
            return True
        else:
            return False

    def change_region_to_china(self):
        # self["region"].click(0)
        if self.country_code.elem_info.get("label") == "CN":
            return
        self.country_code.click()
        while not self["region_china"].wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        return self["region_china"].click()


class DeleteAccountCodeWebview(Webview):
    view_spec = {
        "url": "account\/delete\/mobile"
    }

    def get_locators(self):
        return {
            "code_input_title": {"type": WebElement, "path": UPath(class_ == 'mb-8 H2-Bold text-color-TextPrimary')},
            "code_input": {"type": WebTextEdit, "path": UPath(type_ == 'INPUT')},
            "code_input_clear": {"type": WebElement, "path": UPath(class_ == 'text-color-TextQuaternary mx-8') /
                                                             UPath(type_ == 'path')},
            "delete_button": {"type": WebElement, "path": UPath(class_ == 'truncate', visible_ == True,
                                                                text_ == 'Delete account')},
            "wrong_code_notice": {"type": WebElement, "path": UPath(class_ == 'mx-8')},
            "delete_confirm": {"type": WebElement, "path":
                UPath(
                    class_ ==
                    'tux-dialog-text-action h-48 w-full block text-center px-8 text-color-Negative H4-SemiBold truncate'
                )},
        }

    def input_wrong_code(self, code):
        self.refresh()
        self["code_input"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["code_input"].click()
        time.sleep(2)
        self.refresh()
        self["code_input"].wait_for_ui_stable()
        self["code_input"].input(code)
        self.refresh()
        self["delete_button"].click()
        self.refresh()
        return self["wrong_code_notice"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def input_right_code(self, code):
        self.refresh()
        self["code_input"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["code_input"].click()
        # if self["password_input_clear"].existing:
        #     self["password_input_clear"].click()
        self["code_input"].wait_for_ui_stable()
        if self["code_input_title"].text == "Enter 6-digit code":
            self["code_input"].input("00" + code)
        else:
            self["code_input"].input(code)
        self["delete_button"].click()


class DeleteAccountConfirm3Webview(Webview):
    view_spec = {
        "url": "account\/delete\/mobile"
    }

    def get_locators(self):
        return {
            "delete_confirm": {"type": WebElement, "path": UPath(text_ == 'Delete', type_ == "BUTTON")},
            "delete_cancel": {"type": WebElement,
                              "path": UPath(text_ == 'Cancel', type_ == "BUTTON", visible_ == True)},
        }

    def delete_account_confirm(self):
        if self["delete_confirm"].wait_for_visible():
            time.sleep(2)
            self["delete_confirm"].click()
            return True
        else:
            return False

    def delete_account_cancel(self):
        return self["delete_cancel"].wait_for_existing()

class SettingLocationWin(Window):

    """设置定位服务界面
    """
    window_spec = {"path": UPath(type_ == 'XCUIElementTypeApplication')}

    def get_locators(self):
        return {
            "location_btn": {"path": UPath(type_ == 'XCUIElementTypeSwitch', label_ == "定位服务")},
            "location_service": {"path": UPath(type_ == 'XCUIElementTypeCell', label_ == '定位服务')},
            "setting_btn": {"path": UPath(type_ == 'XCUIElementTypeButton', label_ == '设置')},
            "search_btn": {"path": UPath(type_ == 'XCUIElementTypeSearchField')},
            "content": {"path": UPath(type_ == 'XCUIElementTypeTable')},
            "share_location": {"path": UPath(label_ == '共享我的位置', type_ == "XCUIElementTypeStaticText")}
        }

    def find_and_click_search_btn(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self.search_btn.wait_for_existing(timeout=2, raise_error=False):
                self.search_btn.click()
                break
            self.swipe_down()
        self.search_btn.input("定位")
        if self.share_location.wait_for_existing(raise_error=False, timeout=3):
            self.share_location.click()
            time.sleep(5)
            self.location_btn.click()

    def open_location_service(self):
        if self["share_location"].wait_for_existing(raise_error=False, timeout=3):
            self.share_location.click()
            time.sleep(5)
            self.location_btn.click()
            return
        if self["location_service"].wait_for_existing(raise_error=False, timeout=3):
            self.location_btn.click()
            return
        self.setting_btn.click()
        self.find_and_click_search_btn()

    def swipe_down(self, ratio=4):
        """下滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] + offset_y
        self["content"].drag(to_x - 15, to_y, 15, -offset_y)
        time.sleep(1)

    def sure_click(self, element):
        element = self[element]
        for _ in Retry(timeout=10, raise_error=False):
            if not element.wait_for_existing(raise_error=False, timeout=3):
                break
            element.click()
            time.sleep(2)

class BalanceWebview(Webview):
    view_spec = {
        'title': 'Balance',
        'url': 'https://www.tiktok.com/falcon/main/wallet/home?hide_status_bar=0&hide_nav_bar=1&flag=%2Fwallet%2Fhome&entry=settings&no_hw=1',
       }

    def get_locators(self):
        return {
            "recharge": {"type": WebElement, "path": UPath(text_ == 'Recharge', type_ == "BUTTON")},
        }

    def enter_recharge(self):
        '''Enter recharge page from balance page
        '''
        self["recharge"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["recharge"].click()
        time.sleep(100)

class BalancePanel(BasePanel):

    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "webview": {"type": BalanceWebview, "path": UPath(type_ == 'WKContentView')/0/0/0/0/0},
            "recharge": {"type": Control, "path": UPath(text_ == 'Recharge')},
            "coins_balance": {"path": UPath(text_ == 'Coins Balance')},
        }

class RechargeWebview(Webview):
    view_spec = {
        'title': 'Balance',
        'url': 'https://www.tiktok.com/falcon/main/wallet/recharge?hide_status_bar=0&hide_nav_bar=1&no_hw=1&awe_falcon=sh'
    }

    def get_locators(self):
        return {
            "coins_balance": {"type": WebElement, "path": UPath(text_ == 'Coins Balance')},
        }

class RechargePanel(BasePanel):

    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "webview": {"type": RechargeWebview, "path": UPath(type_ == 'WKContentView')/0/0/0/0/0},
        }

