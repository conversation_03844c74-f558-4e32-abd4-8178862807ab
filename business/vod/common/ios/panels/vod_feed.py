"""
Author: leizihui
Date: 2025-3-5
FilePath: business/vod/common/ios/panels/vod_feed.py
Description: 点播iOS feed面板
"""

import time

from business.vod.common.ios.feed_panel import *
from uibase.upath import *

from utils.common.log_utils import logger


class VodPanelIOS(FeedPanel):
    """
    点播 iOS feed 面板
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "首页": {"path": UPath(id_ == "main_bottom_button_home")},
            'play_icon': {'type': Control, 'path': UPath(id_ == 'play_iv', visible_ == True)},
        }

    def swipe_up_times(self, times=1, duration=0):
        """
        首页上滑指定次数
        """
        logger.info("[点播-ios] 开始上划 %d 次" % times)
        logger.info("[点播-ios] 每次上划后等待 %d 秒" % duration)
        count = 1
        while count <= times:
            self.swipe(y_direction=1)
            if duration > 0:
                time.sleep(duration)
            count += 1
            logger.info("[点播-ios] 上划第 %d 次" % (count - 1))

    def swipe_down_times(self, times=1, duration=0):
        """
        首页下滑指定次数
        """
        logger.info("[点播-ios] 开始下划 %d 次" % times)
        logger.info("[点播-ios] 每次下划后等待 %d 秒" % duration)
        count = 1
        while count <= times:
            self.swipe(y_direction=-1)
            if duration > 0:
                time.sleep(duration)
            count += 1
            logger.info("[点播-ios] 下划第 %d 次" % (count - 1))

