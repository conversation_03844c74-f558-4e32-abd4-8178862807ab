# -*- coding: utf-8 -*-
import time

from .basic_views import *
from .base_panel import *
from common.tiktok.ios.panels.live import AWEMaskWindow



class Action_list(Control):
    elem_class = Control
    elem_path = UPath(type_ == 'AWEShareRowCell')


class GiftButton(Control):
    elem_path = UPath(type_ == "TikTokCommentInputViewController")
    elem_class = Control


class VideoDetailWin(BasePanel):
    """video detail win
    """
    window_spec = {"path": UPath(~type_ == 'AWEAwemeDetailTableViewController|AWEMaskWindow')}

    def get_locators(self):
        return {
            "panel_controller": {"path": UPath(controller_ == "AWENewAwemeDetailTableViewController")},
            "feed_intercept_interaction_view": {
                "type": AWEPlayInteractionViewController,
                "path": UPath(type_ == 'TTKFeedInteractionMainView')},

            #   Use Sound (only appear when get into from music detail page)
            "swipe_guide": {"path": UPath(id_ == "Swipe up for more")},
            "use_sound": {"path": UPath(label_ == 'Use sound')},
            "feed_description": {"type": Control, "path": UPath(type_ == 'UIScrollView')},
            "share": {"path": UPath(label_ == 'Share', visible_ == True) / UPath(type_ == 'UIView')},
            "privacy_setting": {"path": UPath(type_ == 'UILabel', label_ == 'Privacy settings')},
            "back": {"path": UPath(label_ == 'returnButton')},
            # "anchor_feed": {"path": UPath(type_ == 'TTKCommentBubbleMultiAnchorView')},
            "anchor_feed_panel_list": {"path": UPath(id_ == 'anchorPanelView')},
            "anchor_feed": {"path": UPath(~type_ == 'TikTokFeedAnchorView|TTKFeedNewStyleAnchorView|TTKCommentBubbleMultiAnchorView')},
            "delete": {"path": UPath(type_ == 'AWEShareRowCell', label_ == 'Delete')},
            "action_list": {"type": Action_list, "path": UPath(id_ == '(secondRowView)')},
            "action_list_area": {"path": UPath(id_ == '(secondRowView)')},
            "video_detail_page_right_icon": {"type": Control, "path": UPath(label_ == 'right')},
            "comment_icon": {"path": UPath(id_ == "feedCommentButton", visible_ == True)},
            "comment_edit": {"path": UPath(id_ == 'commentInputView') / UPath(type_ == '_UITextContainerView')},
            "comment_publish": {"path": UPath(type_ == 'UIView') / UPath(type_ == 'AWECommentListInputView') /
                                        UPath(id_ == 'sendCommentButton')},
            "comment_list": {"path": UPath(type_ == 'UITableView') / UPath(type_ == 'AWECommentPanelCell', index=0)},
            "comment_area": {"type": Control, "path": UPath(type_ == "UITableView", visible_ == True)},
            "comment_close": {"type": Control, "path": UPath(id_ == 'closeBtn', visible_ == True)},
            "reply_with_video": {"path": UPath(label_ == 'comment video reply button')},
            "comment_reply_sticker": {"path": UPath(type_ == 'ACCEmbedStickerDisplayView')},
            "comment_see": {"path": UPath(id_ == '(bodyImageView)') / UPath(id_ == '(contentView)') / 0},
            "share_button": {"path": UPath(id_ == 'AWEPlayInteractionShareElement')},
            "delete_popup": {"path": UPath(type_ == 'AWEUIButton', id_ == 'Delete')},
            "sticker_reply_video": {"path": UPath(label_ == 'Reply with video')},

            # CLA Content Mobility specific locators
            "long_press_panel_captions_button": {"path": UPath(text_ == "Captions", id_ == "titleLabel", type_ == "UILabel", visible_ == True)},
            "caption_toggle": {"path": UPath(id_ == "tuxContentView", label_ == "Captions.") / UPath(id_ == "customAccessoryView", type_ == "TTKCustomSwitch")},
            # "caption_toggle": {"type": "TTKCustomSwitch", "path": UPath(id_ == "tuxContentView", label_ == "Captions.") / UPath(id_ == "customAccessoryView")},
            "caption_toggle_off": {"path": UPath(id_ == "tuxContentView", label_ == "Captions.") / UPath(id_ == "actionBtn")},
            "translation_toggle": {"path": UPath(type_ == "UITableView") / 3 / UPath(id_ == "actionBtn", depth=8)},
            "caption_entrance_off": {"path": UPath(id_ == "rightCloseButton") / UPath(type_ == "UIImageView", visible_ == True)},
            # "collapsed_caption_icon": {"path": UPath(id_ == "(foldedIconImageView)")},
            "collapsed_caption_icon": {"path": UPath(type_ == "ACCClaCaptionStickerView")},
            # "collapsed_caption_icon": {"path": UPath(UPath(type_ == "ACCClaCaptionStickerView", visible_ == True) / UPath(id_ == "(backgroundView)", depth=27))},
            "captions": {"path": UPath(type_ == "ACCCLACaptionLabel", id_ == "contentLabel")},
            "captions_visible": {"path": UPath(id_ == "(AWEPlayInteractionAuthorElement)", visible_ == True)},
            # "caption_text": {"path": UPath(id_ == "(contentLabel)", ~type_ == "UILabel|ACCCLACaptionLabel")},
            "close_share_panel": {"path": UPath(id_ == "iconShare_close_small", type_ == "UIImageView", visible_ == True)},
            "video_pause": {"path": UPath(type_ == "TTKFeedPassthroughStackView",
                                          id_ == "(TTKFeedInteractionSingleViewContainerElement)",
                                          label_ == "full_size")},
            "new_video_pause": {"path": UPath(type_ == "AWEAwemePlayVideoPauseIcon", ~id_ == "icon_play_pause|icon_play_pause_new", visible_ == True)},
            "hide_captions": {"path": UPath(type_ == "UIImageView",id_ == "tooltip_body")},
            "caption_icon": {"path": UPath(type_ == "UIImageView", id_ == "ic_auto_captions_fold_icon", visible_ == True)},
            "see_translation": {
                "path": UPath(type_ == "UIButtonLabel", id_ == "(See translation)", label_ == "See translation", visible_ == True)},
            "see_original": {
                "path": UPath(type_ == "UIButtonLabel", id_ == "(See translation)", label_ == "See original")},
            "description_text": {"path": UPath(type_ == "AWEPlayInteractionDescriptionLabel", id_ == "(label)", visible_ == True)},
            "username": {"path": UPath(id_ == 'nameLabel')},
            "gift_popup": {"path": UPath(type_ == 'TUXDialogContentSection', id_ == '(contentSection)')},
            "gift_popup_cancel_btn": {"path": UPath(id_ == '(cancelButton)')},
            "gift_btn_root": {"type": Control,
                              "path": UPath(type_ == 'AWECommentListInputView', id_ == "(commentInputView)")},
            "video_gift_btn": {"type": Control, "root": "gift_btn_root",
                               "path": UPath(type_ == "UIButton", id_ == '(giftButton)')},
            "video_gift_mad_my_day": {"type": Control, "root": "gift_btn_root",
                                      "path": UPath(id_ == '(titleLabel)', label_ == 'Made My Day')},
            "video_gift_selected_preview": {"path": UPath(id_ == '(giftView)')},
            "video_gift_selected_preview_cancel_btn": {"path": UPath(id_ == '(cancelButton)')},
            "video_gift_available_coins": {"type": Control, "root": "gift_btn_root",
                                           "path": UPath(id_ == '(valueLabel)')},
            "video_gift_send_button": {"path": UPath(type_ == 'TTKVideoGiftActionBar', id_ == '(footer)') / 1 / 0},
            "user_profile_icon2": {"path": UPath(id_ == "headImageView")},
            "user_profile_icon": {"path": UPath(type_ == "IESLiveMTUserProfileView") / UPath(id_ == "avatarView")},
            "captions_text_visible": {"type": Control, "path": UPath(id_ == "contentLabel", visible_ == True)},
            "captions_show_toast": {"type": Control, "path": UPath(type_ == "TUXToast")},
            "turn_on_captions": {"type": Control, "path": UPath(text_ == "Turn on captions")},
            "turn_off_captions": {"type": Control, "path": UPath(text_ == "Turn off captions")},
            "edit_caption_tooltip": {"type": Control, "path": UPath(text_ == "Edit captions")},
            "action_list_v2": {"type": Control, "path": UPath(type_ == "UITableView") / 0 / UPath(id_ == "bizView")},
            "manage_captions": {"type": Control, "path": UPath(text_ == "Manage captions")},
            "music_cover": {"type": Control, "path": UPath(type_ == "AWEMusicCoverButton", id_ == "musicButton", visible_ == True)},
            "origin_music_cover": {"type": Control, "path": UPath(id_ == 'origin_music_cover', visible_ == True)},
            "exit_button": {"path": UPath(type_ == "AWEButton", label_ == "returnButton", visible_ == True)}
        }

    def swipe_up(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=rect.height * 0.75)
        time.sleep(2)

    def click_exit_button(self):
        if self["exit_button"].wait_for_existing(timeout=2, raise_error=False):
            self["exit_button"].click()
        else:
            self.app.get_device().press_back()  #
        time.sleep(2)

    def comment_swipe(self, y_direction=1):
        self["comment_area"].swipe(y_direction=y_direction, swipe_coefficient=8)
        time.sleep(1)

    def comment_close(self):
        self["comment_close"].click()
        time.sleep(1)

    def click_music_cover_btn(self):
        if self["music_cover"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["music_cover"].click()
        elif self["origin_music_cover"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["origin_music_cover"].click()
        time.sleep(2)

    def wait_for_panel_loading(self):
        return self["panel_controller"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def check_panel(self):
        return self["panel_controller"].wait_for_existing(timeout=3, raise_error=True)

    def collapsed_caption_icon(self):
        self["collapsed_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["collapsed_caption_icon"].click()

    def collapsed_caption_icon_visiblity(self):
        return self["collapsed_caption_icon"].visible

    def check_caption_visibility(self):
        for _ in Retry(limit=30, interval=1):
            # if self['captions'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            #     self["captions"].wait_for_ui_stable()
            #     return self["captions"].visible
            if self['captions'].wait_for_existing(timeout=15, interval=0.2, raise_error=False):
                if self['captions'].wait_for_visible(timeout=15, interval=1, raise_error=False):
                    return True
                else:
                    return False
            else:
                return False

    def close_caption_entrance(self):
        self["caption_entrance_off"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["caption_entrance_off"].click()

    def long_press_panel_captions_button(self):
        self["long_press_panel_captions_button"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["long_press_panel_captions_button"].click()

    def caption_toggle(self):
        self["caption_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["caption_toggle"].click()

    def caption_toggle_visibility(self):
        if self["caption_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=True):
            return True

    def translation_toggle(self):
        self["translation_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["translation_toggle"].click()

    def translation_toggle_visibility(self):
        if self["translation_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=True):
            return True

    def caption_toggle_off(self):
        self["caption_toggle_off"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["caption_toggle_off"].click()

    def dismiss_swipe_guide(self):
        if self["swipe_guide"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self.swipe(y_direction=1, swipe_coefficient=8)

    def click_asr_caption(self):
        if self["collapsed_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            if not self["captions_text_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["collapsed_caption_icon"].click()
        # Trying to click on the caption for 90 seconds, with each time waiting for the caption to be visible with timeout=5 interval=1
        for _ in Retry(limit=30, interval=1):
            if self["captions_text_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["captions_text_visible"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["captions_text_visible"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    self["captions_text_visible"].wait_for_ui_stable()
                    self["captions_text_visible"].click()
                    break
                self.fyp_video_toggle()

    def fyp_video_toggle(self):
        self["new_video_pause"].click()

    def edit_caption_tooltip_visible(self):
        return self["edit_caption_tooltip"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_edit_caption_tooltip(self):
        if self["edit_caption_tooltip"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["edit_caption_tooltip"].click()
            time.sleep(3)

    def captions_visible(self):
        if self["captions_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            return True

    def captions_text_visible(self):
        self["captions_text_visible"].refresh()
        return self["captions_text_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_captions_show_toast(self):
        return self["captions_show_toast"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_turn_off_captions_visible(self):
        return self["turn_off_captions"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_turn_off_captions_icon(self):
        self["turn_off_captions"].click()
        time.sleep(3)

    def click_turn_on_captions_icon(self):
        self["turn_on_captions"].click()
        time.sleep(3)

    def turn_on_caption_visible(self):
        if self["turn_on_captions"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            return True

    def video_pause(self):
        self["video_pause"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["video_pause"].click()

    def new_video_pause(self):
        self["new_video_pause"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["new_video_pause"].click()

    def pause_video_when_caption_is_displayed(self):
        if self['captions'].wait_for_visible(timeout=15, interval=1, raise_error=False):
            self.fyp_video_toggle()
        # for _ in Retry(limit=30, interval=1):
        #     if self['captions'].wait_for_existing(timeout=15, interval=0.2, raise_error=False):
        #         if self['captions'].wait_for_visible(timeout=15, interval=1, raise_error=False):
        #             self.fyp_video_toggle()
        #         else:
        #             # self.log_record("While pausing, captions are existing on video But NOT Visible ")
        #             break
        #     else:
        #         # self.log_record("While pausing, captions are NOT existing on video, pause FAILED")
        #         break

    def hide_captions(self):
        self["hide_captions"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["hide_captions"].click()

    def caption_icon(self):
        self["caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["caption_icon"].click()

    def see_translation(self):
        self["see_translation"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["see_translation"].click()

    def see_translation_visibility(self):
        # if self["see_translation"].wait_for_visible(timeout=5, interval=1, raise_error=False):
        #     return True
        return self["see_translation"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def see_original(self):
        self["see_original"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["see_original"].click()

    def caption_text(self):
        self["caption_text"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        text = self["caption_text"].text
        return text

    def click_captions(self):
        self["captions"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["new_video_pause"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["new_video_pause"].click()
        time.sleep(5)
        self["captions"].click()

    def click_on_captions(self):
        self["captions"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["captions"].click()

    def click_share_button(self):
        self["share_button"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["share_button"].click()

    def close_share_panel(self):
        self["close_share_panel"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["close_share_panel"].click()

    def click_subtitle(self):
        self["captions"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        time.sleep(2)
        self["captions"].click()

    def description_text(self):
        desc_text = self["description_text"].text
        return desc_text

    def is_use_sound_existing(self):
        return self["use_sound"].wait_for_existing(timeout=20, raise_error=False)

    def check_video_detail_page_right_icon(self):
        return self["video_detail_page_right_icon"].wait_for_existing(timeout=5, raise_error=False)

    def get_back(self):
        time.sleep(2)
        self["back"].click()
        time.sleep(1)

    def get_share_and_click(self):
        self["share"].click()
        time.sleep(2)

    def click_manage_caption(self):
        for _ in Retry(limit=10, interval=1):
            try:
                self["manage_captions"].click()
                time.sleep(3)
                return True
            except:
                self["action_list_v2"].swipe(x_direction=1)
                time.sleep(3)

    def click_turn_on_caption(self):
        for _ in Retry(limit=10, interval=1):
            try:
                self["turn_on_captions"].click()
                time.sleep(3)
                return True
            except:
                self["action_list_v2"].swipe(x_direction=1)
                time.sleep(3)

    def is_anchor_feed_exists(self):
        return self["anchor_feed"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_anchor_feed(self, index=0):
        # If there are multiple anchors, then the function will click on the anchor link based on the index provided
        self.is_anchor_feed_exists()
        self["anchor_feed"].click()
        if self["anchor_feed_panel_list"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            anchor_link = Control(root=self["anchor_feed_panel_list"],
                                  path=UPath(type_ == "TikTokFeedAnchorGeneralPanelElementView", index=index))
            anchor_link.click()

    def click_comment_detail_page(self):
        self["comment_icon"].wait_for_existing(timeout=3, interval=1, raise_error=False)
        self["comment_icon"].click()
        time.sleep(2)

    def get_comment_list(self):
        time.sleep(2)
        self["comment_list"].click()

    def add_comment(self, content):
        time.sleep(1)
        self["comment_edit"].refresh()
        self["comment_edit"].click()
        time.sleep(1)
        self["comment_edit"].input(content)
        time.sleep(2)
        if self["comment_publish"].wait_for_existing(timeout=3, raise_error=False):
            time.sleep(2)
            self["comment_publish"].click()

    def video_reply_to_comment(self):
        time.sleep(1)
        self["reply_with_video"].click()

    def click_comment_sticker(self):
        if self["comment_reply_sticker"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["comment_reply_sticker"].click()
            time.sleep(2)
            self["comment_reply_sticker"].click()
            return True
        return False

    def click_comment_sticker_reply(self):
        self["comment_reply_sticker"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["comment_reply_sticker"].click()
        time.sleep(3)
        self["sticker_reply_video"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["sticker_reply_video"].click()

    def delete_video(self):
        self["share_button"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["share_button"].click()
        share_panel = SharePanelWin(root=self.app)
        share_panel.find_delete_and_click()
        delete_panel = DeletePanel(root=self.app)
        delete_panel.delete_video()

    def close_gift_popup(self):
        time.sleep(2)
        if self["gift_popup_cancel_btn"].wait_for_visible(timeout=10, interval=1, raise_error=False):
            self["gift_popup_cancel_btn"].click()

    def is_video_gift_btn_exist(self):
        return self["video_gift_btn"].wait_for_existing(timeout=20, raise_error=False)

    def open_video_gift_panel(self):
        self["video_gift_btn"].wait_for_visible(timeout=20, raise_error=True)
        self["video_gift_btn"].click()

    def is_video_gift_made_my_day_exist(self):
        return self["video_gift_mad_my_day"].wait_for_existing(timeout=20, raise_error=True)

    def select_gift_icon_made_my_day(self):
        self["video_gift_mad_my_day"].click()

    def is_selected_gift_icon_preview_exist(self):
        return self["video_gift_selected_preview"].wait_for_existing(timeout=20, raise_error=False) and self[
            "video_gift_selected_preview_cancel_btn"].wait_for_existing(timeout=20, raise_error=False)

    def get_video_gift_coins_available_value(self):
        self["video_gift_available_coins"].wait_for_existing(timeout=20, raise_error=False)
        coins = self["video_gift_available_coins"].text
        return coins

    def get_video_gift_send_button_text(self):
        self["video_gift_send_button"].wait_for_existing(timeout=20, raise_error=False)
        btn_text = self["video_gift_send_button"].text
        return btn_text

    def can_repost_from_bubble(self) -> None:
        """
        is repost bubble showing?
        """
        return self["feed_intercept_interaction_view"].can_repost()

    def click_repost_from_bubble(self) -> None:
        """
        clicks repost bubble button
        """
        return self["feed_intercept_interaction_view"].click_repost()

    def can_show_reposted(self) -> None:
        """
        is the bubble confirming that you reposted showing?
        """
        return self["feed_intercept_interaction_view"].can_show_reposted()

    def can_add_comment(self) -> None:
        """
        is the bubble showing you can add a comment showing?
        """
        return self["feed_intercept_interaction_view"].can_add_comment()

    #def see_translation_btn(self):
    #    self["see_translation_btn"].wait_for_visible(timeout=5, interval=1, raise_error=True)
     #   self["see_translation_btn"].click()


class ShareFriend(Control):
    def get_locators(self):
        return {
            "user_name": {"type": Control, "path": UPath(id_ == "attributedNameLabel") / 0},
            "user_avatar": {"type": Control, "path": UPath(id_ == 'avatarView', visible_ == True)}
        }


class ShareFriendList(Control):
    elem_class = ShareFriend
    elem_path = UPath(type_ == "AWEIMTranspondListCollectionViewCell", visible_ == True, enabled_ == True)


class SharePanelWin(BasePanel):
    """
    share detail win
    """
    window_spec = {"path": UPath(~controller_ == "AWESharePanelController|TTKSharePanelViewController|TTKNSharePanelViewController")
                   }

    def get_locators(self):
        return {
            "swipe_row": {"type": Control, "path": UPath(id_ == "secondRowView")},
            'Privacy_settings': {'path': UPath(type_ == 'UILabel', label_ == 'Privacy settings')},
            "delete": {"path": UPath(type_ == 'AWEShareRowCell', label_ == 'Delete')},
            "admin": {"path": UPath(type_ == 'AWEShareRowCell', label_ == 'Admin')},
            "friends_row": {"type": Control, "path": UPath(id_ == "collectionContainer")},
            "friends_list": {"type": ShareFriendList, "path": UPath(id_ == 'collectionView')},
            "more_btn": {"type": Control, "path": UPath(type_ == 'AWEIMTranspondListMoreCollectionViewCell')},
            "repost_btn": {"type": Control, "path": UPath(type_ == "TTKUpvoteCollectionCellView")},
            "send": {"type": Control, "path": UPath(id_ == "shareButton", type_ == "UIButton")},
            "repost_item": {"type": Control, "path": UPath(type_ == "AWEIMTranspondUpvoteCollectionViewCell")},
            "channel_list": {"type": Control, "path": UPath(id_ == "tableView") / 1 / UPath(id_ == "bizView")},
            "action_list": {"type": Control, "path": UPath(id_ == "tableView") / 0 / UPath(id_ == "bizView")}

        }

    def find_privacy_setting_and_click(self):
        privacy_action = self.swipe_from_right_to_left("Privacy_settings")
        self[privacy_action].click()

    def find_delete_and_click(self):
        delete_action = self.swipe_from_right_to_left("admin")
        self["delete"].click()

    def swipe_from_right_to_left(self, action):
        time.sleep(2)
        while not self[action].existing:
            self["swipe_row"].swipe(x_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
            time.sleep(2)
        return action

    def can_repost(self) -> bool:
        return self["repost_btn"].existing

    def click_repost(self) -> None:
        if self.can_repost():
            self["repost_btn"].click()
        time.sleep(1)
        popup_panel=AWEMaskWindow(root=self.app)
        if popup_panel["repost_confirm"].wait_for_visible(timeout=5, raise_error=False):
            popup_panel["repost_confirm"].click()
        time.sleep(1)



    def find_friend_and_click(self, user):
        time.sleep(3)
        self.wait_for_ui_stable()
        friend_found = False
        while friend_found is False:
            friends_list = self["friends_list"].items()
            for item in friends_list:
                item["user_name"].wait_for_visible(timeout=5)
                if item["user_name"].text == user:
                    item["user_avatar"].wait_for_visible(timeout=5)
                    item["user_avatar"].click()
                    time.sleep(2)
                    friend_found = True
                    break
            if friend_found is False:
                self["friends_row"].swipe(x_direction=1, swipe_coefficient=5)
                self.wait_for_ui_stable()
                self.refresh()
                time.sleep(1)
            else:
                return
            if self["more_btn"].existing:
                self["more_btn"].click()
                friend_found = True

    def scroll_to_more_btn(self):
        for _ in Retry(limit=40, interval=1, raise_error=False):
            if self["more_btn"].wait_for_visible(timeout=3, interval=1, raise_error=False):
                # self["friends_row"].swipe(x_direction=1, swipe_coefficient=5)
                self["more_btn"].click()
                return
            else:
                self["friends_row"].swipe(x_direction=1, swipe_coefficient=8)
                time.sleep(1)

    def send_first_friend(self):
        time.sleep(3)
        if self["friends_list"].wait_for_existing(timeout=5, raise_error=False):
            self["friends_list"].items()[0].click()

    def send_multiple_friend(self, multiple_friend, create_group=False, more_flag=True):
        """
        Send to multiple friends in share panel
        :param create_group:Create a group chat. Default is False
        :type: bool
        :param multiple_friend:select a few friends. Default is two,eg["a", "b"]
        :type: list
        :param more_flag:
        :type:bool
        """
        time.sleep(3)
        self["friends_row"].wait_for_visible(timeout=3)
        width = self.app.testcase.device.screen_rect.width
        friend_found = False
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if not more_flag:
                for item in self["friends_list"].items():
                    if item.type == "AWEIMTranspondListCollectionViewCell":
                        if item["user_name"].text == multiple_friend[0]:
                            friend_found = True
                            item.click()
                            break
            else:
                if self["more_btn"].wait_for_visible(timeout=3, interval=1, raise_error=False) and \
                    self["more_btn"].rect.right < width:
                    self["more_btn"].click()
                    break
            if friend_found:
                return
            self["friends_row"].swipe(x_direction=1, swipe_coefficient=6 if more_flag else 4)
            time.sleep(1)
        send_to_panel = SendToPanel(root=self.app)
        send_to_panel.select_user_and_send(multiple_friend, create_group)

    def get_share_channel(self, channel_name):
        """
        Returns the channel control and location
        """
        self["channel_list"].refresh()
        for _ in Retry(limit=3, interval=1, raise_error=False):
            channel_list = self["channel_list"].items()
            for index, channel in enumerate(channel_list):
                try:
                    if channel_name in channel.children[0].children[2].children[1].text:
                        return channel, index
                except:
                    logger.info("current video no repost")
            self["channel_list"].swipe(x_direction=1, swipe_coefficient=5)
        return None

    def get_share_action(self, action_name):
        """返回一个指定action name的控件
        """
        self["action_list"].refresh()
        for _ in Retry(limit=3, interval=1, raise_error=False):
            action_list = self["action_list"].items()
            for index, action in enumerate(action_list):
                if action_name in (action.children[0].children[2].children[1].text if action.children[0].children[1].text is None else action.children[0].children[1].text):
                    return action, index
            self["action_list"].swipe(x_direction=1, swipe_coefficient=5)


class SendToUser(Control):
    def get_locators(self):
        return {
            "chat_room_name": {"path": UPath(id_ == "nameLabel", ~type_ == "UILabel|TUXLabel", visible_==True)},
            "relationship": {"path": UPath(id_ == "relationshipLabel")},
        }


class SendToUserList(Control):
    elem_class = SendToUser
    elem_path = UPath(type_ == "AWEIMDirectTranspondUserCell", visible_ == True)


class SendToPanel(BasePanel):
    """
    Send to panel
    """
    window_spec = {"path": UPath(controller_ == 'AWEIMDirectTranspondViewController')}

    def get_locators(self):
        return {
            "search_bar": {"type": Control, "path": UPath(id_ == 'textField', type_ == 'UITextField')},
            "search_list": {"type": SendToUserList, "path": UPath(type_ == 'UICollectionView')},
            "share_btn": {"type": Control, "path": UPath(type_ == 'UIButton', label_ == 'Send')},
            "create_group": {"type": Control, "path": UPath(text_ == "Create group")},
            "send": {"type": Control, "path": UPath(id_ == "shareButton", type_ == "UIButton")},
            "create_group_old": {"type": Control, "path": UPath(id_ == "createGroupButton")},
        }

    def send_to_user(self, user):
        self.device_input(self['search_bar'], user)
        time.sleep(2)
        search_list = self.search_list.items()
        sorted_list = sorted(search_list, key=lambda result: result.rect.top)
        sorted_list[0].click()
        self["share_btn"].click()
        time.sleep(2)

    def select_user_and_send(self, friend_list, create_group):
        clear = Control(root=self.app, path=UPath(id_ == "clearButton"))
        for friend in friend_list:
            if clear.wait_for_visible(timeout=3, raise_error=False):
                clear.click()
            self.device_input(self['search_bar'], friend)
            time.sleep(2)
            for item in self.search_list.items():
                if item["chat_room_name"].text == friend:
                    self.app.testcase.device.click(self['search_bar'].rect.center[0], self['search_bar'].rect.top-20)
                    item.click()
                    break
            else:
                logger.info(f"no found {friend}")

        if create_group:
            if self["create_group"].wait_for_existing(timeout=3, raise_error=False):
                self["create_group"].click()
            elif self["create_group_old"].wait_for_existing(timeout=1, raise_error=False):
                self["create_group_old"].click()
        self["send"].wait_for_visible(timeout=3)
        self["send"].click()


class ShareInputPanel(BasePanel):
    """
    share input panel
    """
    window_spec = {"path": UPath(controller_ == "AWEIMShareInputViewController")}

    def get_locators(self):
        return {
            "send_btn": {"type": Control, "path": UPath(type_ == 'UIButton', label_ == 'Send')},
            "share_btn": {"type": Control, "path": UPath(id_ == "shareButton", type_ == "UIButton")},
        }

    def click_send(self):
        if self["send_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["send_btn"].click()
        elif self["share_btn"].existing:
            self["share_btn"].click()


class AwemePrivacyPanel(BasePanel):
    """
    aweme privacy detail win
    """
    window_spec = {"path": UPath(type_ == "AWEAwemePrivacyControlViewController")}

    def get_locators(self):
        return {
            'private_video_entry': {'path': UPath(label_ == 'Who can watch this video')},
            'comment_set': {'path': UPath(type_ == 'AWESettingSwitch', label_ == 'Allow comments')
                                    / UPath(id_ == '(switchWellImageViewContainer)')},
            'duet_set': {'path': UPath(type_ == 'AWESettingSwitch', label_ == 'Allow Duet')
                                 / UPath(id_ == '(switchWellImageViewContainer)')},
            'stitch_set': {'path': UPath(type_ == 'AWESettingSwitch', label_ == 'Allow Stitch')
                                   / UPath(id_ == '(switchWellImageViewContainer)')},

            'set_everyone': {'path': UPath(text_ == 'Everyone')},
            'set_friends': {'path': UPath(text_ == 'Friends')},
            'set_only_me': {'path': UPath(text_ == 'Only me')},
        }

    def private_video_entry_and_click(self):
        time.sleep(2)
        self["private_video_entry"].click()
        time.sleep(2)

    def set_everyone_and_click(self):
        self["set_everyone"].click()
        time.sleep(2)

    def set_friends_and_click(self):
        self["set_friends"].click()
        time.sleep(2)

    def set_only_me_and_click(self):
        self["set_only_me"].click()
        time.sleep(2)

    def comment_set_and_click(self):
        self["comment_set"].click()
        time.sleep(2)

    def duet_set_and_click(self):
        self["duet_set"].click()
        time.sleep(2)

    def stitch_set_and_click(self):
        self["stitch_set"].click()
        time.sleep(2)


class PrivateDetailControl(BasePanel):
    """
    aweme privacy detail win
    """
    window_spec = {"path": UPath(type_ == "UINavigationController")}

    def get_locators(self):
        return {
            'set_everyone': {'path': UPath(text_ == 'Everyone')},
            'set_friends': {'path': UPath(text_ == 'Friends')},
            'set_only_me': {'path': UPath(text_ == 'Only me')},
        }

    def set_everyone_and_click(self):
        self["set_everyone"].click()
        time.sleep(2)

    def set_friends_and_click(self):
        self["set_friends"].click()
        time.sleep(2)

    def set_only_me_and_click(self):
        self["set_only_me"].click()
        time.sleep(2)


class DeletePanel(BasePanel):
    """
        aweme delete video win
        """
    window_spec = {"path": UPath(type_ == "AWEUIAlertView")}

    def get_locators(self):
        return {
            'delete': {'path': UPath(type_ == 'AWEUIButton', label_ == 'Delete')},
        }

    def delete_video(self):
        self["delete"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["delete"].click()


class CloseTiktokStoriesPopup(BasePanel):
    window_spec = {
        "path": UPath(~controller_ == "AWEStoryShootingGuideViewController|TTKStoryShootingGuideViewController")}

    def get_locators(self):
        return {
            "close": {"path": UPath(type_ == "TUXNavBar") /
                              UPath(type_ == "UIImageView")},
            "ok_btn": {"path": UPath(type_ == "TUXButton", id_ == 'OK')}
        }

    def close_pop_up(self):
        if self["close"].wait_for_existing(timeout=5, raise_error=False):
            return self["close"].click()
        return False

    def click_ok(self):
        self["ok_btn"].click()
