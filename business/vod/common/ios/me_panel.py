# -*- coding: utf8 -*-
import time
from shoots.retry import Retry
from shoots_cv.controls import *
from shoots_cv.cv import CV
from shoots_cv.upath import *

from .base_panel import *
from uibase.base import *
from uibase.upath import *
from uibase.web import WebElement,Webview
from shoots.test_result import LogRecord


class TIKTOKProfileSocialStatisticView(Control):
    def get_locators(self):
        return {
            "following": {"path": UPath(type_ == "UIStackView", index=0) / UPath(type_ == "UILabel", index=0)},
            "followers": {"path": UPath(type_ == "UIStackView", index=1) / UPath(type_ == "UILabel", index=0)},
            "likes": {"path": UPath(type_ == "UIStackView", index=2) / UPath(type_ == "UILabel", index=0)}
        }


class VideoOfPlayList(Control):
    # window_spec = {"path": UPath(type_ == 'TikTokPlayListVideoCollectionViewCell')}

    def get_locators(self):
        return {
            "check_box": {"type": Control, "path": UPath(type_ == "TUXCheckbox")}
        }

    def check_enable(self):
        return self["check_box"].enabled
        # if self["check_box"].wait_for_existing(timeout=2, raise_error=False):
        #     return self["check_box"].enabled
        # else:
        #     return False

    def click_checkbox(self):
        self["check_box"].click()


class VideoListofPlayList(Control):
    elem_class = VideoOfPlayList
    elem_path = UPath(type_ == 'TikTokPlayListVideoCollectionViewCell')

    @on_expired
    def items(self):
        time.sleep(1)
        items = super(VideoListofPlayList, self).items()
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if len(items) != 0:
                break
        return items


class TIKTOKProfileHeaderView(Control):

    def get_locators(self):
        return {
            "AWEUserNameLabel": {"path": UPath(type_ == 'AWEUserNameLabel') / UPath(~id_ == 'nameLabel')},
            "TIKTOKProfileSocialStatisticView": {
                "type": TIKTOKProfileSocialStatisticView,
                "path": UPath(type_ == 'TIKTOKProfileSocialStatisticView')
            },
            "edit_profile_btn": {"path": UPath(~type_ == "AWEUIButton|UIButtonLabel", ~label_ == "(Edit|Set up) profile",index=0)},
            "edit_profile_btn1": {"path": UPath(id_ == "Edit", type_ == "UIButton")},
            "add_to_favorites": {"path": UPath(type_ == "AWEStandardButton", label_ == "Add to Favorites")}
        }

    @property
    def username(self):
        return self["AWEUserNameLabel"].text


class AWENavigationBar(Control):

    def get_locators(self):
        return {
            "find_friends": {"path": UPath(id_ == '(addFriendButton)')},
            "find_friends2": {"path": UPath(label_ == 'ic profile addFriends')},
            "more_icon_logged": {"path": UPath(id_ == '(settingButton)')},
            "more_icon_logged_1": {"path": UPath(type_ == 'UIButton', id_ == "Icon3LinesHorizontal2", visible_ == True, index=-1)},
            "more_icon_logged_2": {"path": UPath(~id_ == "IconEllipsisHorizontal2|TTKProfileNavBarSettingComponent|IconEllipsisHorizontal", visible_ == True)},
            "more_icon_not_logged": {"path": UPath(type_ == "AWENaviBarButtonContainerView")},
            "find_friends3": {"path": UPath(type_ == 'AWENaviBarButtonContainerView', id_ == 'leftBtnsContainerView', visible_ == True)},
        }

    def enter_setting_logged(self):
        if self['more_icon_logged'].wait_for_existing(timeout=3, raise_error=False):
            self['more_icon_logged'].click()
        elif self['more_icon_logged_1'].wait_for_existing(timeout=3, raise_error=False):
            self['more_icon_logged_1'].click()
        elif self['more_icon_logged_2'].wait_for_existing(timeout=3, raise_error=False):
            self['more_icon_logged_2'].click()

    def enter_setting_not_logged(self):
        if self['more_icon_not_logged'].wait_for_existing(timeout=3, raise_error=True):
            self['more_icon_not_logged'].click()

    def find_friends_click(self):
        if self['find_friends3'].wait_for_existing(timeout=3, raise_error=True):
            self['find_friends3'].click()


class VideoOfPlayList(Control):
    # window_spec = {"path": UPath(type_ == 'TikTokPlayListVideoCollectionViewCell')}

    def get_locators(self):
        return {
            "check_box": {"type": Control, "path": UPath(type_ == "TUXCheckbox")}
        }

    def check_enable(self):
        return self["check_box"].enabled
        # if self["check_box"].wait_for_existing(timeout=2, raise_error=False):
        #     return self["check_box"].enabled
        # else:
        #     return False

    def click_checkbox(self):
        self["check_box"].click()


class VideoListofPlayList(Control):
    elem_class = VideoOfPlayList
    elem_path = UPath(type_ == 'TikTokPlayListVideoCollectionViewCell')

    @on_expired
    def items(self):
        time.sleep(1)
        items = super(VideoListofPlayList, self).items()
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if len(items) != 0:
                break
        return items


class UserWorkCollection(Control):
    """user work collection
    """
    elem_path = UPath(type_ == "AWEUserWorkCollectionViewCell", visible_ == True, depth=1)
    elem_class = Control

class FollowingUsersListUpath(BasePanel):
    def get_locators(self):
        return {
            "following_nickname": {"path": UPath(id_ == "nameLabel", visible_ == True)},
            "following_username": {"path": UPath(type_ == "UITableViewCellContentView") / 4},
            "relation_button": {"path": UPath(type_ == "TTKRelationButton")},
        }


class FollowingUsersList(Control):
    elem_class = FollowingUsersListUpath
    elem_path = UPath(type_ == "TTKFanStarUserCollectionCell")

class FollowingUsersListUsernameUpath(Control):
    def get_locators(self):
        return {
            "following_nickname": {"path": UPath(type_ == "AWEAliasEditLabel", visible_ == True) / UPath(id_ == "nameLabel", visible_ == True)},
            "following_username": {"path": UPath(type_ == "UITableViewCellContentView") / 4},
        }


class FollowingUsersListUsernameUpathList(Control):
    elem_class = FollowingUsersListUsernameUpath
    elem_path = UPath(type_ == "TTKFanStarUserCollectionCell")

class MePanel(BasePanel):
    """ Me panel
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            #   Panel_Controller
            "panel_controller": {
                "path": UPath(
                    ~type_ == 'AWEPreLoginProfileViewController|TIKTOKUserHomeViewController|TTKPreLoginProfileViewController')
            },
            #   未登录态
            "signup": {"type": Control,
                       "path": UPath(type_ == 'UIButton', ~label_ == 'Sign up|Log in or sign up|登录或注册|注册')},
            "signup_new": {"type": Control, "path": UPath(id_ == "signinButton")},
            "signup_new1": {"type": Control, "path": UPath(id_ == "signUpLabel")},
            #   登录态
            "TIKTOKProfileHeaderView": {"type": TIKTOKProfileHeaderView,
                                        "path": UPath(~type_ == 'TIKTOKProfileHeaderView|TTKProfileHeaderView')},
            "AWENavigationBar": {"type": AWENavigationBar, "path": UPath(type_ == 'AWENavigationBar')},
            "liked_controller": {"type": Control, "path": UPath(type_ == 'TIKTOKLikeWorkViewController')},
            "liked_video_entrance": {"type": Control,
                                     "path": UPath(type_ == 'UIImageView', id_ == 'tab_likes_default')},
            "like_video_list": {"type": Control, "root": "liked_controller",
                                "path": UPath(type_ == 'UICollectionView', visible_ == True)},
            "active_video_post_entrance": {"type": Control,
                                           "path": UPath(type_ == 'UIImageView', id_ == 'tab_videos_active')},
            "private_video_entrance": {"type": Control,
                                       "path": UPath(type_ == 'UIImageView', id_ == 'tab_private_default')},
            "private_video": {"type": Control, "path": UPath(id_ == 'tabView') / UPath(id_ == 'scrollView') / 1},
            "private_account_text": {"path": UPath(label_ == 'Private account')},
            "profile_login_title": {"path": UPath(id_ == "indicatorLabel")},
            "profile_login_title2": {"path": UPath(type_ == "UILabel", text_ == "Log into existing account")},
            "profile_login_button": {"path": UPath(id_ == "signinButton")},

            # In Suggested account area
            "suggested_account_follow_btn": {"type": Control,
                                             "path": UPath(id_ == "relationButton",label_ =="Follow")},
            "suggested_account_following_btn": {"type": Control,
                                                "path": UPath(id_ == "relationButton",label_ == "Following")},
            "mutual_friends_btn": {"type": Control, "path": UPath(type_ == 'TTKRelationButton', label_ == 'Friends')},
            "follow_list_back_btn": {"type": Control,
                                     "path": UPath(type_ == 'AWENavigationBar', visible_ == True) / UPath(
                                         label_ == 'Back')},

            "add_playlist_btn": {"path": UPath(type_ == 'TikTokUserProfileEntrancesCollectionView') / 0},
            "draft_video_cell": {"path": UPath(type_ == 'TIKTOKUserDraftCollectionViewCell')},
            "video_post_first": {"path": UPath(~type_ == 'UICollectionView|TTKUserProfileWorkCollectionView') / 1},
            "video_post_second": {"path": UPath(~type_ == 'UICollectionView|TTKUserProfileWorkCollectionView') / 2},
            "private_video_collection_view": {"path": UPath(type_ == 'UICollectionView', visible_ == True)},
            "follow_your_friends_page": {"path": UPath(id_ == 'title_recommend_user')},
            "right_top_cancel": {"type": Control, "path": UPath(id_ == 'close')},
            'add_account_entrance': {'type': Control,
                                     "path": UPath(type_ == 'AWESwitchAccountNavigationBarTitleView')},
            "switch_account_2": {
                "path": UPath(type_ == 'UITableView') / 1 / UPath(type_ == 'UITableViewCellContentView')},
            "switch_account_uiautomation18": {"path": UPath(text_ == "uiautomation18")},
            "settings_enter": {"path": UPath(label_ == 'Settings and privacy')},
            "settings_enter_text": {"path": UPath(id_ == "Settings and privacy")},
            "avatar_pop_up_close": {
                "path": UPath(type_ == "TTKNaviIntroSheetHeaderView") / UPath(type_ == "UIImageView")},
            "creator_tools_enter": {"path": UPath(label_ == 'Creator tools')},
            "following_btn": {"path": UPath(id_ == '(followingLabelsStackView)')},
            "following_btn1": {"path": UPath(id_ == "followingLabel",visible_==True)},
            "following_btn2": {"path": UPath(id_ == "Following", visible_==True)},
            "suggested_title": {"path": UPath(id_ == "titleLabel", text_ == "Suggested accounts ", visible_ == True)},
            "following_count": {"path": UPath(~id_ == "TTKProfileUserRelationInfoFollowingComponent|followingLabelsStackView") / 0},
            "following_count_new": {"path": UPath(type_ == "TTKProfileHeaderView") / 1 / 1 / 0 / 0 / UPath(type_ == "TUXLabel", depth=5)},
            "cdu_001": {"path": UPath(id_ == "(nameLabel)", label_ == 'cduiauto001')},
            # "settings_and_privacy_btn": {"path": UPath(~label_ == 'Settings and privacy|设置与隐私', visible_ == True)},
            "settings_and_privacy_btn": {"path": UPath(type_ == "TUXActionSheetCell", ~text_ == "Settings and privacy|设置与隐私")},
            "switch_account_2": {"path": UPath(type_ == 'UITableView') / 1 / UPath(type_ == 'UITableViewCellContentView')},
            "settings_and_privacy_btn": {"path": UPath(~label_ == 'Settings and privacy|设置与隐私')},
            "settings_and_privacy_btn": {
                "path": UPath(~label_ == 'Settings and privacy|设置与隐私', type_ == "UITableViewLabel")},
            # Playlist
            "friends_panel": {"type": Control, "path": UPath(type_ == 'TTKUserSuggestionPopupView')},
            "close_friends_panel": {"type": Control,
                                    "path": UPath(type_ == "TTKUserSuggestionPopupView") / UPath(type_ == "AWEButton",
                                                                                                 index=1)},
            "mine_playlist": {"type": Control,
                              "path": UPath(type_ == 'TikTokPostWorkVCPlaylistGuideCell') / UPath(type_ == 'UIView')},
            "mine_created_playlist": {"type": Control,
                                      "path": UPath(type_ == 'TikTokUserProfileEntrancesCollectionView')},
            "sort_videos_into_playlist": {"type": Control,
                                          "path": UPath(type_ == 'TikTokPostWorkVCPlaylistGuideCell') / UPath(
                                              type_ == 'UILabel')},
            "start_creating": {"type": Control, "path": UPath(type_ == 'TUXButton')},
            "input_playlist_name": {"type": Control, "path": UPath(type_ == 'UITextFieldLabel')},
            "next_button": {"type": Control, "path": UPath(type_ == "UIButtonLabel", visible_ == True)},
            "video_list": {"type": VideoListofPlayList, "path": UPath(type_ == 'UICollectionView')},
            "first_video_select": {"type": Control,
                                   "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'UIView',
                                                                                          visible_ == True) / 5},
            "first_select_video": {"type": Control, "path": UPath(id_ == '(playListIconView)', visible_ == True)},
            "enable_click": {"type": Control,
                             "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'TUXCheckbox')},
            "select_video": {"type": Control,
                             "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'TUXCheckbox') / UPath(
                                 type_ == 'UIImageView')},
            "first_playlist": {"type": Control,
                               "path": UPath(type_ == 'TikTokUserProfileEntrancesCollectionView') / 1},
            "first_playlist_name": {"type": Control,
                                    "path": UPath(type_ == 'TikTokUserProfileEntrancesCollectionView') / 1 / UPath(
                                        type_ == 'UILabel')},
            "manage_icon": {"type": Control, "path": UPath(
                controller_ == 'TikTokPlayListDetailPanelViewController') / 1 / 0 / 2 / UPath(type_ == 'UIImageView')},
            "manage_panel": {"type": Control,
                             "path": UPath(controller_ == 'TikTokPlayListEditSheetViewController') / UPath(
                                 id_ == '(containerView)')},
            "delete_playlist": {"type": Control, "path": UPath(label_ == 'Delete playlist')},
            "delete_playlist_no_video": {"type": Control, "path": UPath(type_ == 'TUXDialogHighlightBackgroundButton',
                                                                        label_ == 'Delete playlist')},
            "delete_confirm": {"type": Control, "path": UPath(text_ == 'Delete')},
            "playlist_next": {"type": Control, "path": UPath(type_ == "UIButtonLabel", visible_ == True)},
            "create_confirm": {"type": Control, "path": UPath(id_ == '(Create playlist)')},
            "select_video": {"path": UPath(type_ == 'UICollectionView') / 5 / UPath(type_ == 'BDImageView')},
            "video_more_button": {"path": UPath(label_ == 'Share') / UPath(id_ == '(aweImageView)')},
            "tab_list": {"type": Control, "path": UPath(type_ == 'AWESlidingTabbarView', id_ == 'null') / UPath(
                type_ == 'UIScrollView')},
            "collection_tab": {"type": Control, "path": UPath(type_ == "UIScrollView", visible_ == True) / 3},
            "collections_tab": {"type": Control, "path": UPath(id_ == "contentScrollView") / UPath(id_ == "collectionView") / 1},
            "videos_tab": {"type": Control,
                           "path": UPath(controller_ == 'TIKTOKProfileFavoriteViewController') / UPath(
                               type_ == 'UIScrollView') / 0},
            "tab_video": {"type": Control,
                          "path": UPath(type_ == 'TTKUserProfileWorkCollectionView', visible_ == True) / 0},
            "video_verify": {"type": Control, "path": UPath(label_ == 'right')},
            "collect_list": {"type": Control, "path": UPath(type_ == 'UICollectionView', visible_ == True)},
            "video_collection_list": {"type": Control,
                                      "path": UPath(type_ == 'TTKUserProfileWorkCollectionView', visible_ == True)},
            "profile_collection_list": {"type": Control,
                                        "path": UPath(controller_ == 'TTKFavoriteAwemeCollectionsViewController')},
            "no_video_in_collection": {"type": Control, "path": UPath(label_ == 'No videos in this collection')},
            "add_videos_btn": {"type": Control, "path": UPath(id_ == 'manageBtn')},
            "select_video_icon": {"type": Control, "path": UPath(type_ == 'UICollectionView') / 0 / UPath(
                id_ == 'CheckboxUncheckCircleConst')},
            "added_btn": {"type": Control, "path": UPath(type_ == 'TUXButton')},
            "collections_group_video": {"type": Control, "path": UPath(type_ == 'BDImageView', index=0)},
            "collection_group_v1": {"type": Control,
                                    "path": UPath(type_ == "TTKFavoriteAwemeCollectionCell", index=0)},
            "collection_group_v0": {"type": Control,
                                    "path": UPath(controller_ == 'TTKFavoriteAwemeCollectionsViewController') / UPath(
                                        type_ == 'UICollectionView') / 0},
            "group_manage_icon": {"type": Control,
                                  "path": UPath(~label_ == 'IconEllipsisHorizontal|Icon3ptArrowTurnUpRight',
                                                visible_ == True)},
            "group_share_icon": {"type": Control,
                                 "path": UPath(controller_ == 'TTKFavoriteAwemeViewController') / 3 / 0 / 2 / UPath(
                                     type_ == 'TUXNavBarPositionButton')},
            "shared_friends": {"type": Control,
                               "path": UPath(controller_ == 'AWEIMTranspondListViewController') / UPath(
                                   type_ == 'UICollectionView') / 0 / UPath(type_ == 'AWEIMCarouselShareChatView')},
            "send_btn": {"type": Control, "path": UPath(type_ == 'UIButton', label_ == 'Send')},
            "change_group_name": {"type": Control, "path": UPath(label_ == 'Change collection name')},
            "delete_group": {"type": Control, "path": UPath(label_ == 'Delete collection')},
            "group_title": {"type": Control, "path": UPath(id_ == 'nameLabel', visible_ == True)},
            "group_panel": {"type": Control, "path": UPath(type_ == 'TUXStatusView') / UPath(id_ == 'containerView')},
            "group_image": {"type": Control, "path": UPath(type_ == 'BDImageView', visible_ == True)},
            "change_private_group": {"type": Control,
                                     "path": UPath(type_ == 'TUXDialogHighlightBackgroundButton', label_ == 'OK')},
            "videos_text": {"type": Control, "path": UPath(type_ == 'UIButtonLabel', label_ == 'Videos 29')},
            "privacy_settings": {"type": Control,
                                 "path": UPath(type_ == 'AWEShareRowView') / UPath(text_ == "Privacy settings",
                                                                                   visible_ == True)},
            "permission_change": {
                "path": UPath(type_ == 'UITableView') / UPath(text_ == "Who can watch this video", visible_ == True)},
            "only_me": {"path": UPath(type_ == 'UITableView') / 0 / UPath(type_ == 'UITableViewCellContentView')},
            "permission_back_btn": {
                "path": UPath(type_ == 'UIImageView', id_ == '(00C03447-B0DA-42FA-9AFE-E35354CF5C18)')},
            "change_permission_panel": {"type": Control, "path": UPath(
                controller_ == "AWEAwemePrivacyControlVisibleDetailViewController")},
            "private_add_to_playlist": {"type": Control,
                                        "path": UPath(type_ == 'AWEShareRowCell', label_ == 'Add to playlist') / UPath(
                                            type_ == 'UIImageView')},
            "playlist_feed_icon": {"type": Control, "path": UPath(type_ == 'AWEAntiAddictedNoticeBarView')},
            "back_profile_page": {"type": Control, "path": UPath(label_ == 'returnButton')},
            "private_video_icon": {"type": Control,
                                   "path": UPath(type_ == 'UIImageView', id_ == '(tab_private_default)')},
            "first_private_video": {"type": Control,
                                    "path": UPath(type_ == 'UICollectionView', visible_ == True) / 0 / UPath(
                                        type_ == 'BDImageView')},
            "playlist_button_list": {"type": Control, "path": UPath(type_ == 'AWEShareRowView')},
            "cancel_button": {"type": Control, "path": UPath(type_ == 'AWEShareDismissButton')},
            "no_video_playlist": {"type": Control, "path": UPath(type_ == 'UIView', id_ == '(containerView)')},
            "button_list": {"type": Control, "path": UPath(id_ == '(secondRowView)')},
            "permission_change_panel": {"type": Control,
                                        "path": UPath(controller_ == "TUXDialog") / UPath(type_ == "UIView", index=0)},
            "confirm_change": {"type": Control,
                               "path": UPath(type_ == "TUXDialogHighlightBackgroundButton", label_ == "Confirm")},

            "playlist_video_detail_panel": {"type": Control, "path": UPath(
                controller_ == 'TikTokPlayListDetailPanelViewController') / 1},

            # CLA Content Mobility
            "choose_account_follower": {"type": Control,
                                        "path": UPath(type_ == 'UILabel', id_ == '(cla_automation_creator01)',
                                                      text_ == 'cla_automation_creator01')},
            "following_list_contacts_close_btn": {
                "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'UIButton')},
            "followingList_first_user_1": {
                "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'AWEUserNameLabel') / UPath(
                    type_ == 'UILabel', visible_ == True)},
            "followingList_first_user_name_1": {
                "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'UITableViewCellContentView') / UPath(
                    type_ == 'UILabel', visible_ == True, depth=1, index=1)},

            "following_list_first_user_2": {
                "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'AWEAliasEditLabel') / UPath(
                    type_ == 'UILabel')},
            "following_list_first_user_follow_btn": {
                "path": UPath(type_ == 'UICollectionView') / 1 / UPath(type_ == 'TTKRelationButton')},
            "following_list_first_user_follow_txt": {
                "path": UPath(type_ == 'UICollectionView') / 1 / UPath(text_ == 'Following')},
            "following_list_user_photo": {
                "path": UPath(id_ == "collectionView") / 5 / UPath(id_ == "innerBorder", depth=5)},
            "following_list_user_name": {
                "path": UPath(id_ == "collectionView") / 5 / UPath(id_ == "nameLabel", depth=5)},
            "following_list_nicky_name": {
                "path": UPath(type_ == "UICollectionView") / 5 / UPath(type_ == "UITableViewCellContentView") / 4},
            "following_list_following_btn": {
                "path": UPath(id_ == "collectionView") / 6 / UPath(type_ == "UIButtonLabel", visible_ == True, depth=5)},
            "following_list_live_notification": {
                "path": UPath(id_ == "collectionView") / 5 / UPath(id_ == "IconBell") / UPath(type_ == "UIImageView", depth=5)},
            "following_list_live_more": {
                "path": UPath(id_ == "collectionView") / 4 / UPath(id_ == "relationCellMore", depth=5)},
            "following_list_live_more1": {
                "path": UPath(id_ == "collectionView") / 6 / UPath(id_ == "relationCellMore", depth=5)},
            "following_list_search": {"path": UPath(id_ == "searchFieldBackgroundView", visible_ == True)},
            "follower_list_user_photo": {
                "path": UPath(type_ == "UICollectionView") / 4 / UPath(type_ == "AWEGradientBorderView", visible_ == True)},
            "follower_list_user_photo1": {"path": UPath(id_ == "collectionView") / 5 / UPath(id_ == "innerBorder", depth=5)},
            "follower_list_user_name": {
                "path": UPath(type_ == "UICollectionView") / 4 / UPath(type_ == "AWEAliasEditLabel") / UPath(type_ == "UILabel")},
            "follower_list_user_name1": {"path": UPath(id_ == "collectionView") / 5 / UPath(id_ == "nameLabel", depth=5)},
            "follower_list_nicky_name": {
                "path": UPath(type_ == "UICollectionView") / 4 / UPath(type_ == "UITableViewCellContentView") / 4},
            "follower_list_nicky_name1": {"path": UPath(type_ == "UICollectionView") / 5 / UPath(type_ == "UITableViewCellContentView", depth=5) / 4},
            "follower_list_follow_btn": {
                "path": UPath(type_ == "UICollectionView") / 4 / UPath(type_ == "TTKRelationButton")},
            "follower_list_follow_btn1": {"path": UPath(type_ == "UICollectionView") / 5 / UPath(type_ == "TUXButton", depth=5)},
            "follower_list_more_icon": {
                "path": UPath(type_ == "UICollectionView") / 4 / UPath(type_ == "UITableViewCellContentView") / 11},
            "follower_list_more_icon1": {"path": UPath(type_ == "UICollectionView") / 5 / UPath(type_ == "UITableViewCellContentView", depth=5) / 11},
            "find_friends_new": {"path": UPath(type_ == 'AWEUIButton', label_ == 'Add friends', visible_ == True)},
            "add_friends_new": {"path": UPath(id_ == "TTKProfileCTAFindFriendComponent", visible_ == True)},
            "add_friends_new_2": {"path": UPath(controller_ == "TIKTOKProfileHeaderExtraViewController") / 2},
            "add_friends_new_3": {"path": UPath(label_ == "Find friends") / UPath(type_ == "UIImageView", depth=5)},
            "search_friends": {"path": UPath(id_ == "Search by name or username")},
            "search_users": {"path": UPath(id_ == "Search users",visible_ == True)},
            # following list设置为private的账户
            "xytest000003": {"path": UPath(label_ == 'xytest000003')},
            "strive09170": {"path": UPath(label_ == 'strive09170', id_ == '(nameLabel)')},
            "strive1018_": {"path": UPath(~label_ == '^[^ ]strive1018_$', visible_ == True)},
            "justin_username": {"path": UPath(type_ == "UILabel", text_ == "justinbieber", visible_ == True, index=0)},
            "justin_username_2": {"path": UPath(type_ == "UILabel", text_ == "Justin Bieber", visible_ == True, index=0)},
            "justinbieber": {
                "path": UPath(~type_ == 'AWEUserNameLabel|AWEInviteSearchTableViewCell') / UPath(label_ == 'justinbieber', visible_ == True)},
            "justinbieber_icon": {
                "path": UPath(~type_ == 'AWEUserNameLabel|AWEInviteSearchTableViewCell',visible_==True) / UPath(
                    label_ == 'Verified', visible_ == True,type_=='UIImageView',index=0)},
            "user7235758151115": {"path": UPath(type_=='TTKFanStarSearchUserCollectionCell')/UPath(text_ == 'user7235758151115',index=0)},
            "following_tab": {"path": UPath(type_ == 'UIScrollView') / 0},
            "private_following_tip_title": {
                "path": UPath(type_ == "TikTokEmptyPageContainerView") / UPath(type_ == "UILabel", depth=5)},
            "private_following_tip_desc": {"path": UPath(type_ == "YYLabel", visible_ == True)},
            "following_list_find_friends": {"path": UPath(label_ == 'Find friends')},
            "find_your_contacts": {"path": UPath(label_ == 'Find your contacts')},
            "following_user_list": {"type": FollowingUsersList, "path": UPath(type_ == "UICollectionView")},
            "following_user_list_username": {"type": FollowingUsersListUsernameUpathList, "path": UPath(type_ == "UICollectionView")},
            "find_friends_on_facebook": {"path": UPath(label_ == 'Find friends on Facebook')},
            "UI_Collection_view": {"path": UPath(type_ == 'UICollectionView')},
            "following_list_back_btn": {
                "path": UPath(type_ == 'AWENavigationBar', visible_ == True) / UPath(label_ == 'Back')},
            "following_list_back_tux_btn": {
                "path": UPath(type_ == 'TUXNavBar', visible_ == True) / UPath(id_ == '(startContainerView)',
                                                                              type_ == 'UIView')},
            "nicky_name_label": {"path": UPath(text_ == 'Strived')},
            "switch_account_panel": {"path": UPath(type_ == 'AWEAddAccountEffectView')},
            "profile_user_photo": {"path": UPath(type_ == "BDImageView", visible_ == True)},
            "profile_user_photo_2": {"path": UPath(id_ == "storyAvatar") / UPath(type_ == "BDImageView", depth=5)},
            "profile_user_photo_1": {"path": UPath(id_ == "TTKProfileAvatarNormalComponent") / UPath(type_ == "BDImageView", visible_ == True, depth=5)},
            "share_profile_panel": {"path": UPath(id_ == "changePhotoButton",visible_ == True)},
            "share_profile_panel_2": {"path": UPath(type_ == "_UIVisualEffectContentView")},
            "search_icon": {"path": UPath(type_ == 'UISearchBarTextField') / UPath(type_ == 'UIImageView')},
            "search_icon1": {"path": UPath(id_ == "searchFieldStackView") / UPath(type_ == "UIImageView", depth=5)},
            "live_event_btn": {"path": UPath(id_ == "TTKProfileNavBarLiveComponent") / UPath(type_ == "UIImageView", depth=5)},
            "live_event_btn1": {"path": UPath(id_ == "endContainerView") / 0 / UPath(type_ == "UIImageView", depth=5)},
            "website_link": {"path": UPath(~type_ == "TTKProfileBIOEmailLinkView|TIKTOKProfileBioView", visible_ == True)},
            "website_link_new": {"path": UPath(~type_ == "TTKProfileBIOEmailLinkView|TIKTOKProfileBioView", visible_ == True)},
            "website_input": {"path": UPath(text_ == 'Add', id_ == 'placeholderLabel')},
            "website_input_edit": {"path": UPath(id_ == "textCanvasView", type_ == "_UITextLayoutCanvasView")},
            "website_save": {"path": UPath(text_ == 'Save')},
            "back_btn": {"path": UPath(label_ == 'Back', visible_ == True)},
            "links_back_btn": {"path": UPath(type_ == "TUXNavBarPositionButton")},
            "website_clear": {"path": UPath(label_ == 'ic search bar clear')},
            "website_go_back_btn": {"path": UPath(type_ == 'AWENavigationBar') / 0 / 2 / UPath(type_ == 'UIButton')},
            "website_go_back_spark_btn": {"path": UPath(id_ == "IconChevronLeftOffsetLTR")},
            "close_recents": {"path": UPath(id_ == "closeButton", visible_ == True) / UPath(type_ == "UIImageView", depth=5)},
            "recents_text": {"path": UPath(id_ == "leftLabel", visible_ == True)},
            "nicky_name_text_view": {"path": UPath(type_ == 'UITextView')},
            "select_organization": {"path": UPath(label_ == 'Select organization')},
            "change_pronouns_panel": {"path": UPath(controller_ == 'TTKChangePronounsViewController')},
            "check_captions_account_follower": {"type": Control, "path": UPath(type_ == 'UILabel',
                                                                               id_ == '(cla_automation_creator01)',
                                                                               text_ == 'cla_automation_creator01')},
            "check_see_translation_account_follower": {"type": Control, "path": UPath(type_ == 'UILabel',
                                                                                      id_ == '(cla_automation_creator02)',
                                                                                      text_ == 'cla_automation_creator02')},
            "eyes_view": {"path": UPath(type_ == 'AWENavigationBar') / 0 / 1 / 0},
            "eyes_view_new": {"path": UPath(id_ == "TTKProfileNavBarViewerComponent") / UPath(type_ == "UIImageView")},
            "eyes_view_new1": {"path": UPath(type_ == "TTKProfileViewsEntryView") / UPath(type_ == "UIImageView", depth=5)},
            "user_rec_btn": {"path": UPath(type_ == 'AWEButton', ~id_ == 'userRecommendBtn|whitefansrectangle', visible_ == True)},
            "user_rec_panel": {"path": UPath(controller_ == "TTKRelationUserCardViewController") / UPath(type_ == "UICollectionView")},
            "suggested_tittle": {"path": UPath(id_ == "titleLabel", text_ == "Suggested accounts ", visible_ == True)},
            "suggested_tittle1": {"path": UPath(controller_ == "TTKProfileUserRecommendViewController") / 0 / 0},
            "user_rec_panel_controller": {"path": UPath( ~controller_ == 'TTKRelationUserCardViewController|TIKTOKUserRecommendViewController|TTKOthersProfileUserRecommendViewController')},
            "following_search_bar": {"path": UPath(type_ == 'UITextField')},
            "following_search_user": {"path": UPath(id_ == 'nameLabel', ~text_ == "yangkui20.*", visible_ == True)},
            "yangkuiui20222fyp": {
                "path": UPath(type_ == 'TTKFanStarSearchUserCollectionCell', visible_ == True) / UPath(
                    label_ == 'yangkuiui20222fyp', index=0)},
            "magnifier_icon": {"path": UPath(type_ == 'AWECommonSearchBar') / UPath(type_ == 'UIView') / 0},
            "magnifier_icon1": {"path": UPath(id_ == "searchFieldStackView") / UPath(type_ == "UIImageView", depth=5)},
            "other_profile_view_controller": {"path": UPath(controller_ == 'TTKFanStarViewController')},
            "plus_btn": {"type": Control, "path": UPath(id_ == 'plusView')},
            "expired_story_banner": {"type": Control, "path": UPath(id_ == 'guideLabel')},
            "upload_story_msg": {"type": Control,
                                 "path": UPath(type_ == 'TUXToast') / UPath(id_ == 'messageLabel', visible_ == True)},
            "story_archive": {"type": Control,
                              "path": UPath(type_ == 'TTKStoryArchiveCollectionViewCell', visible_ == True)},
            "story_ring": {"type": Control, "path": UPath(id_ == 'pureColorBorder', visible_ == True)},
            "story_gradient_ring": {"type": Control, "path": UPath(id_ == 'gradientColorBorder', visible_ == True)},
            "story_label": {"type": Control, "path": UPath(id_ == 'storyLabel', visible_ == True)},
            "profile_avatar": {"type": Control, "path": UPath(id_ == 'storyAvatar', visible_ == True)},
            "story_collection": {"type": Control,
                                 "path": UPath(type_ == 'TTKStoryEntranceCollectionViewCell', visible_ == True)},
            "video_collection": {"type": Control,
                                 "path": UPath(type_ == 'AWEUserWorkCollectionViewCell', visible_ == True)},
            "story_count": {"type": Control, "root": "story_collection",
                            "path": UPath(id_ == 'storyLabel', visible_ == True)},
            # profile email
            "email_input": {"type": TextEdit, "path": UPath(type_ == "UITextField")},
            "email_save": {"path": UPath(text_ == 'Save')},
            "email_clear": {"path": UPath(id_ == "ic_search_bar_clear", type_ == "UIButton")},
            "email_go_back_btn": {"path": UPath(type_ == 'AWENavigationBar') / 0 / 2 / UPath(type_ == 'UIButton')},
            "follow_your_friends_popup": {"path": UPath(id_ == '(titleLabel)')},
            "follow_your_friends_popup_close_btn": {"path": UPath(id_ == '(closeButton)')},
            "close_button": {
                "path": UPath(id_ == "(closeButton)") / UPath(id_ == "(imageContentView)", visible_ == True)},
            "titleView": {"path": UPath(id_ == "titleView", type_ == "UIView") / UPath(type_ == "UIImageView")},
            "add_account": {"path": UPath(text_ == "Add account")},

            #   Video List
            "user_work_collection": {
                "type": UserWorkCollection,
                "path": UPath(
                    ~controller_ == 'TIKTOKPostWorkViewController|TIKTOKLikeWorkViewController|AWEPrivatePostWorkViewController',
                    visible_ == True) / UPath(type_ == 'UICollectionView')
            },
            "favorites_button": {
                "path": UPath(type_ == "UIScrollView") / UPath(type_ == "AWESlidingTabButton", index=3) / UPath(
                    type_ == "UIImageView")},
            "favorites_collection": {"path": UPath(type_ == "AWESlidingTabButton", ~id_ == "Collections")},
            "analytics_collection": {
                "path": UPath(type_ == "TTKFavoriteAwemeCollectionCell") / UPath(type_ == "UILabel",
                                                                                 text_ == "Shoots automation collection")},
            "video_favorites_collection": {"path": UPath(label_ == 'Favorites') / UPath(type_ == 'UIImageView')},
            "post_tab": {"path": UPath(type_ == 'TIKTOKProfileHeaderView') / UPath(type_ == 'UIScrollView') / 0},
            "private_tab": {"path": UPath(label_ == 'Private', visible_ == True)},
            "create_post_guide": {"path": UPath(id_ == '(createPostWorkGuideView)', visible_ == True)},
            "create_private_guide": {"path": UPath(label_ == 'Your private videos', visible_ == True)},
            "no_favorites_videos": {"path": UPath(label_ == 'Favorite videos', visible_ == True)},
            # "collection_video_v0": {"path": UPath(type_ == 'TTKUserProfileWorkCollectionView', visible_ == True)/0},
            "collection_video_v1": {"path": UPath(type_ == 'UICollectionView', visible_ == True) / 1},
            "video_nickname": {"path": UPath(id_ == "nameLabel", visible_ == True)},
            "favorites_video": {"path": UPath(label_ == 'Favorites')},
            "no_collection_video": {"type": Control,
                                    "path": UPath(label_ == 'No videos in this collection', visible_ == True)},
            "post_video_v1": {"type": Control,
                              "path": UPath(type_ == 'TTKUserProfileWorkCollectionView', visible_ == True) / 1},
            "profile_view_List_title": {"path": UPath(label_ == 'Profile views', visible_ == True)},
            "profile_view_List_title1": {"path": UPath(type_ == "TTKProfileViewsEntryView") / UPath(type_ == "UIImageView", depth=5)},
            "recommend_view": {"type": Control, "path": UPath(id_ == "recommendView", visible_ == True)},
            "hide_btn": {"type": Control, "path": UPath(text_ == "Hide", visible_ == True)},
            "kids_private_account": {"path": UPath(text_ == "Private account", visible_ == True)},
            "collection_share_panel": {"path": UPath(type_ == 'UITableView', visible_ == True)},
            "close_share_panel": {"path": UPath(id_ == 'iconShare_close_small', visible_ == True)},
            "hamburger_menu": {"type": Control, "path": UPath(id_ == "settingButton")},
            "creator_tools": {"type": Control, "path": UPath(id_ == "Creator tools")},
            "creator_analytics": {"type": Control, "path": UPath(text_ == "Analytics")},
            "click_settings": {"path": UPath(id_ == "Icon3LinesHorizontal2") / UPath(type_ == "UIImageView")},

            # @caiyu
            "Home": {"path": UPath(type_ == "UILabel", label_ == "Home", visible_ == True)},

            #Creator Analytics
            "hamburger_menu":{"type": Control, "path": UPath(id_ == "settingButton")},
            "creator_tools":{"type": Control, "path": UPath(id_ == "Creator tools")},
            "creator_analytics":{"type": Control, "path":UPath(text_ == "Analytics")},

            "click_settings" :{"path": UPath(id_ == "Icon3LinesHorizontal2") / UPath(type_ == "UIImageView")},

            "action_buttons":{"type": Control, "path":UPath(text_ == "Action buttons")},
            "actionbuttons_webview":{"type": ActionButtonsWebView, "path": UPath(type_ == "WKContentView")},
            "profile_following":{"path":UPath(text_ == "Following", visible_ == True)},
            "following_list_userhead_0":{"path":UPath(id_ == "storyAvatarView") / UPath(type_ == "UIImageView", visible_ == True)},
            "other_profile_follow":{"path":UPath(id_ == "TTKProfileCTARelationComponent", visible_ == True) / UPath(id_ == "Follow")},
            "other_profile_message":{"path":UPath(id_ == "messageButtonWithAirplaneIcon", visible_ == True)},
            "following_list_close":{"path":UPath(id_ == "collectionView") / 5 / UPath(id_ == "dislikeButton")},
            "otherprofile_unfollow":{"path":UPath(id_ == "collectionView") / 5 / UPath(id_ == "dislikeButton")},
            "followers_list": {"path": UPath(id_ == "TTKProfileUserRelationInfoFollowerComponent")},
            "followers_list1": {"path": UPath(id_ == "Following")},
            "suggest_view_all": {"path": UPath(id_ == "findMoreButton")},
            "following_list_search1": {"path": UPath(id_ == "textCanvasView", visible_ == True)},
            "following_list_search2": {"path": UPath(id_ == "searchTextField", visible_ == True)},
            "following_list_search_result_nickname": {"path": UPath(id_ == "nameLabel", visible_ == True)},
            #otherprofile following list only_me
            "following_list_only_me_icon": {"path": UPath(type_ == "TikTokEmptyPageContainerView") / UPath(type_ == "UIImageView", depth=5)},
            "following_list_only_me_xiao_title": {"path": UPath(id_ == "This user’s following list is private")},
            "following_list_only_me_da_title": {"path": UPath(controller_ == "TTKFanStarViewController", visible_ == True) / 1 / 0 / UPath(id_ == "null", depth=5)},
            "following_list_only_me_da_title1": {"path": UPath(controller_ == "TTKFanStarViewController", visible_ == True) / 2 / 0 / UPath(id_ == "null", depth=5)},

            #profile_platfrom enevt
            #navbar
            "profile_platfrom_ug":{"path": UPath(id_ == "null", type_ == "UIButton") / UPath(type_ == "UIImageView")},
            "profile_platfrom_visitor":{"path": UPath(id_ == "TTKProfileNavBarViewerComponent") / UPath(type_ == "UIImageView")},
            "profile_platfrom_settings":{"path": UPath(id_ == "TTKProfileNavBarSettingComponent") / UPath(type_ == "UIImageView")},
            "profile_platfrom_nickname":{"path": UPath(id_ == "titleLabel", type_ == "TUXLabel")},
            "profile_platfrom_live":{"path": UPath(id_ == "TTKProfileNavBarLiveComponent") / UPath(type_ == "UIImageView")},
            #avatar
            "profile_platfrom_avatar":{"path": UPath(type_ == "TIKTOKProfileAvatarEditView") / UPath(type_ == "UIImageView")},
            "profile_platfrom_capture":{"path": UPath(type_ == "TTKAvatarCommonBadgeView", visible_ == True)},
            "profile_platfrom_capture1":{"path": UPath(id_ == "backgroundView", visible_ == True)},
            "profile_platfrom_capture2":{"path": UPath(type_ == "TTKAvatarCommonBadgeView") / UPath(id_ == "backgroundView", depth=5)},
            #info
            "profile_platfrom_username":{"path": UPath(id_ == "nameLabel")},
            "profile_platfrom_username1":{"path": UPath(text_ == "@testqxbvrctban")},
            "profile_platfrom_following":{"path": UPath(id_ == "Following")},
            "profile_platfrom_follower":{"path": UPath(id_ == "Follower", label_ == "Follower",visible_ == True)},
            "profile_platfrom_follower1":{"path": UPath(id_ == "TTKProfileCTAComponent",visible_ == True)},
            "profile_platfrom_follower2":{"path": UPath(id_ == "Followers", label_ == "Followers", visible_ == True)},
            "profile_platfrom_likes_popup":{"path": UPath(id_ == "Likes")},
            #cta
            "profile_platfrom_suggested_account_hide":{"path": UPath(id_ == "Hide")},
            "profile_platfrom_edit_profile":{"path": UPath(id_ == "Edit profile")},
            "profile_platfrom_set_up_profile":{"path": UPath(id_ == "Set up profile")},
            "profile_platfrom_share_profile":{"path": UPath(id_ == "Share profile")},
            "profile_platfrom_add_friends":{"path": UPath(id_ == "IconPersonPlus") / UPath(type_ == "UIImageView")},
            "profile_platfrom_add_friends1":{"path": UPath(id_ == "TTKProfileCTAFindFriendComponent")},
            #bio
            "profile_platfrom_add_bio":{"path": UPath(id_ == "+ Add bio")},
            "profile_platfrom_tap_to_add_bio":{"path": UPath(id_ == "signatureLabel")},
            "profile_platfrom_address":{"path": UPath(id_ == "Address", type_ == "UIButtonLabel")},
            "profile_platfrom_phone":{"path": UPath(~id_ == "Call|Phone", type_ == "UIButtonLabel")},
            "profile_platfrom_email":{"path": UPath(id_ == "Email", type_ == "UIButtonLabel")},
            "profile_platfrom_bio_signature":{"path": UPath(type_ == "TTTAccessibilityElement")},
            "profile_platfrom_bio_link":{"path": UPath(type_ == "TTKProfileBIOEmailLinkView")},
            "profile_platfrom_coupons":{"path": UPath(id_ == "Coupons")},
            #advance
            "profile_platfrom_add_yours":{"path": UPath(text_ == "Add Yours")},
            "profile_platfrom_shop_btn":{"path": UPath(id_ == "titleLabel", label_ == "Shop")},
            "profile_platfrom_your_orders":{"path": UPath(label_ == "Your orders")},
            "profile_platfrom_Youtube":{"path": UPath(label_ == "Youtube")},
            "profile_platfrom_Instagram":{"path": UPath(text_ == "Instagram")},
            "profile_platfrom_Series":{"path": UPath(text_ == "Series")},
            "profile_platfrom_youtube":{"path": UPath(label_ == "Youtube")},
            "profile_platfrom_tips":{"path": UPath(text_ == "Tips")},
            "profile_platfrom_live_activity":{"path": UPath(type_ == "TTKProfileAdvancedSectionView") / 3 / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_live_activity1":{"path": UPath(type_ == "TTKProfileAdvancedSectionView") / 2 / UPath(id_ == "titleLabel", depth=5)},
            "profile_platfrom_showcase":{"path": UPath(id_ == "titleLabel", label_ == "Showcase")},
            "profile_platfrom_social_btn":{"path": UPath(id_ == "titleLabel", visible_ == True)},
            "profile_platfrom_ngo":{"path": UPath(type_ == "TTKProfileAdvancedSectionView") / 3 / UPath(id_ == "titleLabel", depth=5)},
            #video tab
            "profile_platfrom_post_video_tab":{"path": UPath(type_ == "TTKProfileTabVideoButton") / UPath(id_ == "null", visible_ == True) / 0},
            "profile_platfrom_privacy_video_tab":{"path": UPath(id_ == "TTKProfileTabBaseButton_10") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_repost_video_tab":{"path": UPath(id_ == "TTKProfileTabBaseButton_15") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_collect_video_tab":{"path": UPath(id_ == "TTKProfileTabFavouriteButton_6") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_like_video_tab":{"path": UPath(id_ == "TTKProfileTabLikeButton_2") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_like_video_tab1":{"path": UPath(id_ == "Icon3ptHeart") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_ba_video_tab":{"path": UPath(id_ == "TTKProfileTabBaseButton_17") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_SpecialEffects_video_tab":{"path": UPath(id_ == "TTKProfileTabBaseButton_4") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_shop_video_tab":{"path": UPath(id_ == "TTKProfileTabBaseButton_12") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_music_video_tab":{"path": UPath(id_ == "TTKProfileTabBaseButton_3") / UPath(type_ == "UIImageView", depth=5)},
            "profile_platfrom_Stickers_video_tab":{"path": UPath(id_ == "TTKProfileTabBaseButton_16") / UPath(type_ == "UIImageView", depth=5)},

            "gear_icon": {"path": UPath(id_ == "IconGear") / UPath(type_ == "UIImageView", depth=5)},
            "inspiration_setting_text": {"path": UPath(text_ == "Inspiration settings")},
            "video_toggle_status": {"path": UPath(type_ == "UITableView") / 2 / UPath(id_ == "customAccessoryView", depth=7)},
            "recents_photo_1": {"path": UPath(id_ == "selectedCellMaskView")},
            "recents_photo_2": {"path": UPath(id_ == "(collectionView)") / 1 / UPath(id_ == "selectedCellMaskView", depth=5)},
            "Post_this_photo_to_Story": {"path": UPath(id_ == "checkBoxLabel")},
            "save": {"path": UPath(id_ == "Save")},
            "save1": {"path": UPath(id_ == "Save & post")},
            "and_1_more": {"path": UPath(id_ == "moreLabel")},
            "profile_website_link": {"path": UPath(id_ == "flow.page")},
            "profile_website_link1": {"path": UPath(type_ == "UITableView") / 0 / UPath(type_ == "UITableViewLabel", depth=5)},
            "profile_website_link_back": {"path": UPath(id_ == "IconChevronLeftOffsetLTR", type_ == "TUXNavBarPositionButton")}
        }
    def LongClickBtn(self,upath):
        if self[upath].wait_for_visible():
            self[upath].long_click()
            time.sleep(3)

    def ReturnBtnText(self,upath,raise_error=True):
        if self[upath].wait_for_visible(raise_error=raise_error):
            return self[upath].text

    def ClickVisibleBtn(self,upath,upath1=None):
        if upath1 == None:
            upath1 = upath
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)
        elif self[upath1].wait_for_visible():
            self[upath1].click()
            time.sleep(3)

    def CheckVisibleBtn(self,upath,raise_error=True):
        if self[upath].wait_for_visible(raise_error=raise_error):
            return self[upath].wait_for_visible(raise_error=False)

    def FollowingListSearch(self,username=None):
        if self['following_list_search1'].wait_for_visible(raise_error=False):
            self['following_list_search1'].click()
            time.sleep(3)
            self["following_list_search1"].input(username)
        else:
            self['following_list_search2'].wait_for_visible()
            self['following_list_search2'].click()
            time.sleep(3)
            self["following_list_search2"].input(username)


    def click_home(self):
        if self['Home'].wait_for_existing(timeout=2, raise_error=True):
            self['Home'].click()
            time.sleep(2)

    def ClickAddYourEmail(self):
        actionbuttons = self.actionbuttons_webview
        return actionbuttons.click_add_your_email()

    def ClickEmailBack(self):
        actionbuttons = self.actionbuttons_webview
        return actionbuttons.click_email_back()

    def email_clear_not_visible(self):
        pass

    @property
    def email_ocr(self):
        if not hasattr(self, '_cv_window'):
            self._cv_window = ActionButtonsCVWindow(root=self.app)
        return self._cv_window

    def wait_for_action_buttons_visible_by_ocr(self):
        return self.email_ocr['Action_buttons'].wait_for_visible(timeout=10, raise_error=False)

    def click_email_by_ocr(self):
        if self.email_ocr['Email'].wait_for_visible(timeout=5, raise_error=True):
            self.email_ocr['Email'].click()

    def ClickProfileSelfPlatfromSuggestedHide(self):
        if self['profile_platfrom_suggested_account_hide'].wait_for_visible(raise_error=False):
            self['profile_platfrom_suggested_account_hide'].click()
            time.sleep(3)

    def ReturnVisibleBtn(self,upath,upath2=None,upath3=None):
        if upath2 is None:
            upath2 = upath
        if upath3 is None:
            upath3 = upath
        if self[upath].wait_for_visible(raise_error=False):
            return self[upath].wait_for_visible(raise_error=False)
        elif self[upath2].wait_for_visible(raise_error=False):
            return self[upath2].wait_for_visible(raise_error=False)
        elif self[upath3].wait_for_visible():
            return self[upath3].wait_for_visible(raise_error=False)

    def ReturnVisibleBtnText(self,upath,upath2=None,upath3=None):
        if upath2 is None:
            upath2 = upath
        if upath3 is None:
            upath3 = upath
        if self[upath].wait_for_visible(raise_error=False):
            return self[upath].text
        elif self[upath2].wait_for_visible(raise_error=False):
            return self[upath2].text
        elif self[upath3].wait_for_visible():
            return self[upath3].text

    def ClickProfileSelfPlatfromBtn(self,upath,upath2=None,upath3=None):
        if upath2 is None:
            upath2 = upath
        if upath3 is None:
            upath3 = upath
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)
        elif self[upath2].wait_for_visible(raise_error=False):
            self[upath2].click()
            time.sleep(3)
        elif self[upath3].wait_for_visible():
            self[upath3].click()
            time.sleep(3)

    def Clickotherprofile_unfollow(self):
        if self['otherprofile_unfollow'].wait_for_existing():
            self['otherprofile_unfollow'].click()
            time.sleep(3)

    def ClickFollowingListRecommendClose(self):
        if self['following_list_close'].wait_for_existing():
            self['following_list_close'].click()
            time.sleep(3)

    def ClickOtherProfileMessage(self):
        if self['other_profile_message'].wait_for_existing():
            self['other_profile_message'].click()
            time.sleep(3)

    def ClickOtherProfileFollow(self):
        if self['other_profile_follow'].wait_for_existing():
            self['other_profile_follow'].click()
            time.sleep(3)

    def ClickFollowingListRecommendUserHeadOne(self):
        if self['following_list_userhead_0'].wait_for_existing():
            self['following_list_userhead_0'].click()
            time.sleep(3)

    def click_settings(self):
        if self['hamburger_menu'].wait_for_existing(timeout=2, raise_error=True):
            self['hamburger_menu'].click()
            if self['settings_enter'].wait_for_existing(timeout=2, raise_error=True):
                return self['settings_enter'].click()

    #Yi Wang 0103
    def click_geariocon(self):
        self['gear_icon'].click()

    def is_inspiration_setting_text_present(self):
        if self["inspiration_setting_text"].wait_for_existing(timeout=3, raise_error=False):
            return True
        return False

    def is_inspiration_setting_toggle_active(self):
        if self["video_toggle_status"].wait_for_elem_info('is_on', "true"):
            return True
        return False

    def close_follow_your_friends(self):
        if self["close_button"].wait_for_visible(timeout=5, raise_error=False):
            self["close_button"].click()

    def check_collection_share_panel(self):
        return self["collection_share_panel"].wait_for_existing(timeout=3, raise_error=False)

    def close_share_panel(self):
        self["close_share_panel"].click()
        time.sleep(2)

    def choose_account_follower(self, test_case_name):
        if test_case_name == "check_captions":
            self['check_captions_account_follower'].wait_for_visible(timeout=5, interval=1, raise_error=False)
            self["check_captions_account_follower"].click()
        elif test_case_name == "check_see_translation":
            self['check_see_translation_account_follower'].wait_for_visible(timeout=5, interval=1, raise_error=False)
            self["check_see_translation_account_follower"].click()
        else:
            self.log_record("Account Follower Element NOT found!")

    def check_create_post_guide(self):
        if self["create_post_guide"].wait_for_existing(timeout=3, raise_error=False):
            logger.info("视频投稿列表拉空！")
            return False
        return True

    def check_create_private_guide(self):
        if self["create_private_guide"].wait_for_existing(timeout=3, raise_error=False):
            logger.info("私密视频列表拉空！")
            return False
        return True

    def click_group_manage_icon(self):
        if self["group_manage_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["group_manage_icon"].click()
        time.sleep(2)

    def share_group(self):
        if self["shared_friends"].wait_for_existing(timeout=5, raise_error=False):
            self["shared_friends"].click()
            time.sleep(2)
            self["send_btn"].click()

    def share_private_group(self):
        if self["shared_friends"].wait_for_existing(timeout=5, raise_error=False):
            self["shared_friends"].click()
            if self["change_private_group"].wait_for_existing(timeout=5, raise_error=False):
                self["change_private_group"].click()
                time.sleep(3)
                self["shared_friends"].click()
            time.sleep(2)
            self["send_btn"].click()

    def check_video_tab(self):
        if self["videos_text"].existing:
            return False
        return True

    def click_group_share_icon(self):
        if self["group_share_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["group_share_icon"].click()
        time.sleep(2)

    def verify_manage_text(self):
        if self["change_group_name"].text == "Change collection name" and self["delete_group"].text == "Delete collection":
            return True
        return False

    def swipe_group_panel(self):
        if self["group_panel"].wait_for_existing(timeout=5, raise_error=False):
            self.swipe_up()
        time.sleep(2)

    def check_group_panel(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["no_collection_video"].wait_for_existing(timeout=5, raise_error=False):
                logger.info("收藏夹下没有收藏视频！")
                return True
            return self["group_image"].wait_for_existing(timeout=5, raise_error=False)

    def swipe_up(self, ratio=4):
        """上滑
        """
        rect = self.app.get_device().screen_rect
        print(rect.height)
        self.scroll(distance_y=rect.height * 0.8)
        time.sleep(2)

    def check_tab_arrangement_sequence(self):
        tab_list = self["tab_list"].items()
        tab_name_list_v2 = ['Videos', 'Collec', 'Sounds', 'Effect']
        for item in tab_list:
            for children_list in item.children:
                tab_name_list_v1 = children_list.children[0].text[0:6]
                if tab_name_list_v1 in tab_name_list_v2:
                    return True
                return False

    def swipe_group_list(self):
        return self.swipe_up()

    def click_collections_tab(self):
        if self["collections_tab"].wait_for_existing(timeout=5, raise_error=False):
            self["collections_tab"].click()
        time.sleep(5)

    def click_collections_video(self):
        if self["collection_group_v1"].wait_for_existing(timeout=5, raise_error=False):
            self["collection_group_v1"].click()
            if self["no_video_in_collection"].wait_for_existing(timeout=5, raise_error=False):
                self.add_collection_video()
            self["collections_group_video"].click()

    def add_collection_video(self):
        if self["add_videos_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["add_videos_btn"].click()
            if self["select_video_icon"].wait_for_existing(timeout=5, raise_error=False):
                self["select_video_icon"].click()
                time.sleep(2)
                self["added_btn"].click()
                time.sleep(3)
            else:
                self.app.testcase.log_record("个人页没有收藏的视频")
                from shoots.exceptions import StopRunningCase
                raise StopRunningCase

    def click_videos_tab(self):
        if self["videos_tab"].wait_for_existing(timeout=5, raise_error=False):
            self["videos_tab"].click()

    def hide_suggested_account(self):
        if self["recommend_view"].wait_for_existing(timeout=5, raise_error=False):
            self["hide_btn"].click()
        time.sleep(3)

    def click_tab_video(self):
        self["video_collection_list"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(2)
        if self["tab_video"].wait_for_existing(timeout=5, raise_error=False):
            self["tab_video"].click()

    def check_video_verify(self):
        return self["video_verify"].wait_for_existing(timeout=5, raise_error=False)

    def click_tab_name(self):
        if self["tab_list"].wait_for_existing(timeout=3, raise_error=False):
            self["videos_tab"].click()
        time.sleep(3)

    def check_video_collection_list(self):
        return self["video_collection_list"].wait_for_existing(timeout=10, raise_error=False)

    def swipe_tab_name(self):
        if self["tab_list"].wait_for_existing(timeout=3, raise_error=False):
            self["video_collection_list"].scroll(distance_x=1, coefficient_x=0.8)
        time.sleep(3)

    def check_collection_lists(self):
        return self["profile_collection_list"].wait_for_existing(timeout=10, raise_error=False)

    def check_collect_list(self):
        return self["collect_list"].wait_for_existing(timeout=3, raise_error=False)

    def click_video_favorites_collection(self):
        if self["video_favorites_collection"].wait_for_existing(timeout=5, raise_error=False):
            self["video_favorites_collection"].click()

    def check_no_favorites_videos(self):
        if self["no_favorites_videos"].wait_for_existing(timeout=5, raise_error=False):
            logger.info("视频收藏列表拉空!")
            return False
        return True

    def click_collection_video_v0(self):
        if self["tab_video"].wait_for_existing(timeout=3, raise_error=False):
            self["tab_video"].click()

    def click_collection_video_v1(self):
        if self["collection_video_v1"].wait_for_existing(timeout=3, raise_error=False):
            self["collection_video_v1"].click()

    def check_post_video(self):
        return self["post_video_v1"].wait_for_existing(timeout=3, raise_error=False)

    def check_private_video(self):
        return self["post_video_v1"].wait_for_existing(timeout=3, raise_error=False)

    def swipe_down(self):
        rect = self.app.get_device().screen_rect
        print(rect.height)
        self.scroll(distance_y=-1, coefficient_y=-0.58)
        time.sleep(2)

    def return_video_nickname(self):
        if self["video_nickname"].wait_for_existing(timeout=3, raise_error=False):
            return self["video_nickname"].text

    def check_nickname_v1(self, content):
        collection_nickname_v1 = self.return_video_nickname()
        if content == collection_nickname_v1:
            return True
        return False

    def check_nickname_v2(self, content):
        collection_nickname_v2 = self.return_video_nickname()
        if content == collection_nickname_v2:
            return False
        return True

    def remove_favorites_video(self):
        if self["favorites_video"].wait_for_existing(timeout=3, raise_error=False):
            self["favorites_video"].click()

    def click_settings_and_privacy_btn(self):
        if self['settings_and_privacy_btn'].wait_for_visible(timeout=3, raise_error=False):
            time.sleep(5)
            self['settings_and_privacy_btn'].click()

    def following(self):
        return self["following_btn"].click()

    def search_suggested_title(self):
        for i in range(1, 5):
            if self["suggested_title"].wait_for_visible(timeout=3, raise_error=False):
                break
            else:
                self.swipe(y_direction=1, swipe_coefficient=3)

    def ClickProfileFollowingBtn(self):
        if self['following_btn1'].wait_for_visible(timeout=5, raise_error=False):
            self["following_btn1"].click()
        elif self['following_btn2'].wait_for_visible(timeout=5, raise_error=True):
            self["following_btn2"].click()
        time.sleep(3)

    def ui_checking(self):
        """检查界面元素内容
        """
        pass

    def click_first_video(self) -> None:
        self.app.get_device().drag(from_y=200, from_x=0, to_x=0, to_y=100)
        video_list = self["user_work_collection"].items()
        if len(video_list) > 0:
            video_list[0].click()


    def click_first_playlist(self):
        if self["first_playlist"].existing:
            self["first_playlist"].click()
            time.sleep(3)

    def click_manage_icon(self):
        if self["manage_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["manage_icon"].click()
            time.sleep(1)
        else:
            return False

    def check_friends_panel(self):
        if self["friends_panel"].wait_for_existing(timeout=5, raise_error=False):
            self["close_friends_panel"].click()
            time.sleep(2)

    def click_select_video(self):
        self["select_video"].click()

    def click_video_more_button(self):
        self["video_more_button"].click()
        time.sleep(2)

    def change_video_permission(self):
        self["button_list"].swipe(x_direction=1, swipe_coefficient=8)
        self["privacy_settings"].click()
        self["permission_change"].click()
        self["only_me"].click()

    def check_permission_change_panel(self):
        if self["permission_change_panel"].wait_for_existing(timeout=5, raise_error=False):
            self["confirm_change"].click()
            time.sleep(2)

    def back_to_profile_page(self):
        self["change_permission_panel"].swipe(y_direction=-1, swipe_coefficient=8)
        time.sleep(2)
        self["back_profile_page"].click()
        time.sleep(2)

    def click_private_video(self):
        self["private_video_icon"].click()
        time.sleep(3)
        self["first_private_video"].click()
        self["video_more_button"].click()
        time.sleep(3)

    def click_private_add_to_playlist(self):
        self["private_add_to_playlist"].click()
        time.sleep(3)

    def check_private_add_to_playlist(self):
        if self["playlist_feed_icon"].wait_for_existing(timeout=5, raise_error=False):
            return False
        else:
            return True

    def check_playlist_video_detail_panel(self):
        return self["playlist_video_detail_panel"].wait_for_existing(timeout=5, raise_error=False)

    def element_compare_by_image(self, device, original_pic_name):
        capture = cv.get_screenshot(device)
        self["playlist_button_list"].ensure_visible()
        rect1 = self["playlist_button_list"].rect
        # self["cancel_button"].ensure_visible()
        # rect2 = self["cancel_button"].rect
        target = capture[0][rect1.top * 3: (rect1.top + rect1.height) * 3,
                 rect1.left * 3: (rect1.left + rect1.width) * 3]
        import cv2
        basedir = os.path.abspath('.')
        pic_dir = os.path.join(basedir, device.serial)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        from shoots_cv.cv import CV
        cv_ = CV()
        original_pic_dir = os.path.join(os.path.abspath(os.path.realpath(__file__) + os.path.sep + "../../.."),
                                        "resources")
        print("path", original_pic_dir)
        original_pic_name = os.path.join(original_pic_dir, original_pic_name + ".png")
        # return original_pic_dir, target_pic_name
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']
        print(compare_result)
        return compare_result > 0.8

    def check_manage_panel(self):
        return self["manage_panel"].wait_for_existing(timeout=5, raise_error=False)

    def delete_playlist(self):
        self["delete_playlist"].click()
        time.sleep(2)
        self["delete_confirm"].click()

    def delete_playlist_no_video(self):
        self["delete_playlist_no_video"].click()
        time.sleep(2)

    def return_playlist_name(self):
        return self["first_playlist_name"].text

    def check_delete_playlist_name(self):
        if self["first_playlist"].existing:
            if self["first_playlist_name"].text == self.return_playlist_name():
                return True
            else:
                return False

    def create_playlist(self, content):
        if self["mine_playlist"].existing:
            if self["sort_videos_into_playlist"].text == "Sort videos into playlists":
                self["sort_videos_into_playlist"].click()
                self["start_creating"].click()
                self.device_input(self["input_playlist_name"], content)
                time.sleep(3)
                self["next_button"].click()
        elif self["mine_created_playlist"].existing:
            self["add_playlist_btn"].click()
            self.device_input(self["input_playlist_name"], content)
            time.sleep(3)
            self["next_button"].click()

    def get_playlist_video(self):
        video_list = self["video_list"].items()
        for item in video_list:
            if item.check_enable():
                return item.click_checkbox()
        return False

        # swipe_cnt = 0
        # chosen_cnt = 0
        # video_list = self["video_list"].items()
        # while chosen_cnt < 1 and swipe_cnt < 13:
        #     for item in video_list:
        #         if item.check_enable():
        #            item.click_checkbox()
        #            chosen_cnt += 1
        #         self.swipe(y_direction=1, swipe_coefficient=3)
        #         video_list.refresh()
        #         time.sleep(3)
        #         swipe_cnt += 1
        #     if chosen_cnt > 1:
        #         break

    def confirm_create(self):
        self["playlist_next"].click()
        # self["create_confirm"].click()

    def wait_for_panel_loading(self):
        return self["panel_controller"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def wait_for_unlogin_panel_loading(self):
        return self.signup.wait_for_existing(timeout=5, raise_error=False)

    def wait_for_profile_login(self):
        login_title = self["profile_login_title"].wait_for_visible(timeout=3, raise_error=False)
        login_title2 = self["profile_login_title2"].wait_for_visible(timeout=3, raise_error=False)
        logger.info(f"Profile login button status:{login_title}, {login_title2}")
        if login_title or login_title2:
            if self["profile_login_button"].wait_for_existing(timeout=3, raise_error=False):
                self["profile_login_button"].click()

    def sign_up(self):
        if self["titleView"].wait_for_existing(timeout=5, raise_error=False):
            self["titleView"].click()
            self["add_account"].wait_for_existing(timeout=5, raise_error=False)
            self["add_account"].click()
        try:
            if self["signup_new"].wait_for_existing(timeout=5, raise_error=False):
                self["signup_new"].click()
            elif self["signup_new1"].wait_for_existing(timeout=3, raise_error=False):
                self["signup_new1"].click()
            elif self["signup"].wait_for_existing(timeout=3, raise_error=False):
                return self["signup"].click()
        except:
            import traceback
            logger.info("[Error msg]: %s" % traceback.format_exc())
            logger.info("Already on the login mode page")

    def is_logged(self):
        return self["TIKTOKProfileHeaderView"].wait_for_visible(timeout=3, raise_error=False) or self["kids_private_account"].wait_for_visible(timeout=3, raise_error=False)

    def enter_settings(self):
        if self["avatar_pop_up_close"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["avatar_pop_up_close"].click()
        if self.is_logged():
            self['AWENavigationBar'].enter_setting_logged()
            if self["settings_enter"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self["settings_enter"].click()
            elif self["settings_enter_text"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self["settings_enter_text"].click()
            elif self.app.cv_wait_for_visible("设置与隐私"):
                self.app.click_by_ocr_text("设置与隐私")
        else:
            self['AWENavigationBar'].enter_setting_not_logged()

    def enter_settings_for(self):
        time.sleep(5)
        if self["avatar_pop_up_close"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["avatar_pop_up_close"].click()
        time.sleep(3)
        if self.is_logged():
            self['AWENavigationBar'].enter_setting_logged()
            if self["settings_enter"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self["settings_enter"].click()
        else:
            self['AWENavigationBar'].enter_setting_not_logged()

    def enter_creator_tools(self):
        time.sleep(5)
        if self["avatar_pop_up_close"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["avatar_pop_up_close"].click()
        time.sleep(3)
        if self.is_logged():
            self['AWENavigationBar'].enter_setting_logged()
            if self["creator_tools_enter"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                self["creator_tools_enter"].click()
        else:
            self['AWENavigationBar'].enter_setting_not_logged()

    def get_username(self):
        time.sleep(7)
        return self.TIKTOKProfileHeaderView.username

    def enter_profile_edit_view(self, need_scroll=False):
        if self.TIKTOKProfileHeaderView.edit_profile_btn.wait_for_visible(timeout=3, interval=1, raise_error=False):
            self.TIKTOKProfileHeaderView.edit_profile_btn.click()
        else:
            self.TIKTOKProfileHeaderView.edit_profile_btn1.click()
        time.sleep(3)
        if need_scroll:
            self.scroll(distance_y=300)

    def click_action_buttons(self):
        while not self["action_buttons"].wait_for_existing(timeout=5, raise_error=False):
            self.swipe(y_direction=1)
            time.sleep(2)
        self["action_buttons"].click()
        time.sleep(5)


    def into_private_video_list(self):
        self["private_video_entrance"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["private_video_entrance"].click()

    def enter_private_video(self):
        if self["private_video"].wait_for_visible(timeout=5, raise_error=False):
            self["private_video"].click()

    def into_liked_video_list(self):
        self["liked_video_entrance"].click()
        return self["like_video_list"].wait_for_existing(timeout=5)

    def into_active_video_list(self):
        self["active_video_post_entrance"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["active_video_post_entrance"].click()

    def click_video_in_liked_list(self, index=0):
        if self["like_video_list"].children[index].type == "AWEUserWorkCollectionViewCell":
            return self["like_video_list"].children[index].click()
        else:
            return False

    def click_draft_video_from_active_list(self):
        self["draft_video_cell"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["draft_video_cell"].click()

    def private_account_text_exist(self):
        time.sleep(2)
        print(self["private_account_text"].text)
        if self["private_account_text"].exist:
            return True
        else:
            return False

    def playlist_add_button_is_visible(self):
        return self["add_playlist_btn"].visible

    def click_add_playlist_button(self):
        if self["avatar_pop_up_close"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["avatar_pop_up_close"].click()
        time.sleep(2)
        self["add_playlist_btn"].click()

    def into_following_list(self):
        try:
            self.TIKTOKProfileHeaderView.TIKTOKProfileSocialStatisticView.following.click()
            time.sleep(2)
        except UINotFoundError:
            self.click_profile_following()

    def click_profile_following(self):
        if self["profile_following"].wait_for_visible(timeout=5, raise_error=True):
            self["profile_following"].click()

    def into_follower_list(self):
        try:
            self.TIKTOKProfileHeaderView.TIKTOKProfileSocialStatisticView.followers.click()
        except UINotFoundError:
            if self["followers_list"].wait_for_visible(timeout=5, raise_error=False):
                self["followers_list"].click()
            elif self["followers_list1"].wait_for_visible(timeout=5):
                self["followers_list1"].click()

    def into_suggest_list(self):
        time.sleep(3)
        if self["suggest_view_all"].wait_for_visible(timeout=5, raise_error=True):
                self["suggest_view_all"].click()

    def click_follow_in_suggest_account(self):
        if self["suggested_account_follow_btn"].wait_for_visible():
            self["suggested_account_follow_btn"].click()
            time.sleep(3)

    def click_following_in_suggest_account(self):
        if self["suggested_account_following_btn"].wait_for_visible():
            self["suggested_account_following_btn"].click()
            time.sleep(3)

    def click_video_cover(self, address):
        time.sleep(2)

        if self["follow_your_friends_popup"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            if self["follow_your_friends_popup_close_btn"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
                self["follow_your_friends_popup_close_btn"].click()
        if address == 1:
            self["video_post_first"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)
            self["video_post_first"].click()
        elif address == 2:
            self["video_post_second"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)
            self["video_post_second"].click()
        time.sleep(2)

    def click_private_video_cover(self, index=0):
        # If there are multiple videos in the private section, then the function will click
        # on the video cover link based on the index provided
        self["private_video_collection_view"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        video_cover = Control(root=self["private_video_collection_view"],
                              path=UPath(type_ == "AWEUserWorkCollectionViewCell", index=index))
        video_cover.click()

    def switch_account(self):
        if self["follow_your_friends_page"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["right_top_cancel"].click()
        if self["avatar_pop_up_close"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["avatar_pop_up_close"].click()
        self["add_account_entrance"].click()
        self.refresh()
        self['switch_account_2'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self['switch_account_2'].click()

    def switch_account_uiautomation18(self):
        if self["follow_your_friends_page"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["right_top_cancel"].click()
        if self["avatar_pop_up_close"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["avatar_pop_up_close"].click()
        self["add_account_entrance"].click()
        self.refresh()
        self['switch_account_uiautomation18'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self['switch_account_uiautomation18'].click()


    def following_list_close_find_friends_win(self):
        if (self["find_your_contacts"]).wait_for_visible(timeout=3, raise_error=False):
            self["following_list_contacts_close_btn"].click()
            self["UI_Collection_view"].refresh()
        if (self["find_friends_on_facebook"]).wait_for_visible(timeout=3, raise_error=False):
            self["following_list_contacts_close_btn"].click()
            self["UI_Collection_view"].refresh()
        if self['following_list_back_tux_btn'].wait_for_existing(timeout=5, raise_error=False):
            self['following_list_back_tux_btn'].click()
        else:
            self["following_list_back_btn"].click()
        self.into_following_list()

    def GetFollowingUsersListNickname(self, index=0):
        return self["following_user_list"].items()[index]["following_nickname"].text

    def GetFollowingUsersListUsername(self, index=0):
        return self["following_user_list"].items()[index]["following_username"].text

    def GetFollowingListUername(self):
        list = []
        time.sleep(3)
        for item in self["following_user_list_username"].items():
            list.append(item["following_username"].text)
        self.app.testcase.log_info(f"当前页面的username：{list}")
        return list

    def GetFollowingListNickname(self):
        list = []
        time.sleep(3)
        for item in self["following_user_list_username"].items():
            list.append(item["following_username"].text)
        self.app.testcase.log_info(f"当前页面的nickname：{list}")
        return list

    def get_following_list_first_userName(self, index=0):
        # self.following_list_close_find_friends_win()
        time.sleep(5)
        nickname = self["following_user_list"].items()[index]["following_nickname"].text
        username = self["following_user_list"].items()[index]["following_username"].text
        return nickname,username

    def get_following_count(self):
        # self["following_count"].refresh()
        time.sleep(5)
        if self["following_count"].wait_for_visible(timeout=3, raise_error=False):
            count = self["following_count"].text
            logger.info(f"following_count: {count}")
        else:
            self["following_count_new"].wait_for_visible()
            count = self["following_count_new"].text
            logger.info(f"following_count_new: {count}")
        return int(count)

    def cancel_follow(self, index=0):
        return self["following_user_list"].items()[index]["relation_button"].click()

    def is_followed(self, index=0):
        self["following_user_list"].refresh()
        time.sleep(3)
        return self["following_user_list"].items()[index]["relation_button"].elem_info.get("label")

    def wait_following_list_user_photo_existing(self):
        return self["following_list_user_photo"].wait_for_existing(timeout=3, raise_error=False)

    def wait_following_list_user_name_existing(self):
        return self["following_list_user_name"].wait_for_existing(timeout=3, raise_error=False)

    def wait_following_list_nicky_name_existing(self):
        return self["following_list_nicky_name"].wait_for_existing(timeout=3, raise_error=False)

    def wait_following_list_following_btn_existing(self):
        return self["following_list_following_btn"].wait_for_existing(timeout=3, raise_error=False)

    def wait_following_list_live_notification_existing(self):
        if self["following_list_live_notification"].wait_for_existing(timeout=3, raise_error=False):
            return self["following_list_live_notification"].wait_for_existing(timeout=3, raise_error=False)
        elif self["following_list_live_more1"].wait_for_existing(timeout=3, raise_error=False):
            return self["following_list_live_more1"].wait_for_existing(timeout=3, raise_error=False)
        elif self["following_list_live_more"].wait_for_existing(timeout=3, raise_error=True):
            return self["following_list_live_more"].wait_for_existing(timeout=3, raise_error=False)

    def wait_following_list_search_existing(self):
        if self["following_list_search"].wait_for_existing(timeout=3, raise_error=False):
            return self["following_list_search"].wait_for_existing(timeout=3, raise_error=True)
        elif self["following_list_search1"].wait_for_existing(timeout=3, raise_error=False):
            return self["following_list_search1"].wait_for_existing(timeout=3, raise_error=True)
        elif self["following_list_search2"].wait_for_existing(timeout=3):
            return self["following_list_search2"].wait_for_existing(timeout=3, raise_error=True)

    def wait_follower_list_user_photo_existing(self):
        return self["follower_list_user_photo"].wait_for_existing(timeout=3, raise_error=False)

    def wait_follower_list_user_name_existing(self):
        return self["follower_list_user_name"].wait_for_existing(timeout=3, raise_error=False)

    def wait_follower_list_nicky_name_existing(self):
        return self["follower_list_nicky_name"].wait_for_existing(timeout=3, raise_error=False)

    def wait_follower_list_follower_btn_existing(self):
        return self["follower_list_follow_btn"].wait_for_existing(timeout=3, raise_error=False)

    def wait_follower_list_more_icon_existing(self):
        return self["follower_list_more_icon"].wait_for_existing(timeout=3, raise_error=False)

    def find_friends_icon_click(self):
        if self["find_friends"].wait_for_existing(timeout=3, raise_error=False):
            return self["find_friends"].click()
        if self["find_friends_new"].wait_for_existing(timeout=3, raise_error=False):
            return self["find_friends_new"].click()
        if self["add_friends_new"].wait_for_existing(timeout=3, raise_error=False):
            return self["add_friends_new"].click()
        if self["AWENavigationBar"].wait_for_existing(timeout=3, raise_error=False):
            return self["AWENavigationBar"].find_friends_click()
        self["AWENavigationBar"].wait_for_existing(timeout=3, raise_error=True)

    def enter_findfriends_page(self):
        if self["find_friends_new"].wait_for_existing(timeout=5, raise_error=False):
            return self["find_friends_new"].click()
        elif self["add_friends_new"].wait_for_existing(timeout=5, raise_error=False):
            return self["add_friends_new"].click()
        elif self["add_friends_new_2"].wait_for_existing(timeout=5, raise_error=False):
            return self["add_friends_new_2"].click()
        elif self["add_friends_new_3"].wait_for_existing(timeout=5, raise_error=True):
            return self["add_friends_new_3"].click()

    def search_friends(self, username="xytest000003"):
        if self["search_friends"].wait_for_existing(timeout=5, raise_error=False):
            self["search_friends"].click()
            self["search_friends"].input(username)
        else:
            self["search_users"].wait_for_visible(timeout=5, raise_error=True)
            self["search_users"].click()
            self["search_users"].input(username)
        time.sleep(3)
        self.app.testcase.take_screenshot(self.app.get_device(), 'screenshot_search_friends_' + str(int(time.time())) + ".jpg")
        # if username=='justinbieber':
        #     username="justinbieber_icon"
        if self["justin_username"].wait_for_existing(timeout=5, raise_error=False):
            self["justin_username"].click()
            return True
        elif self["justin_username_2"].wait_for_existing(timeout=5, raise_error=False):
            self["justin_username_2"].click()
            return True
        else:
            return False

    def search_ffp_friends(self, username="xytest1"):
        if self["search_friends"].wait_for_existing(timeout=5, raise_error=True):
            self["search_friends"].click()
            self["search_friends"].input(username)
        time.sleep(3)
        name_click = Control(root=self.app, path=UPath(~id_ == "titleLabel|descLabel", text_ == username, visible_ == True, index=0))
        if name_click.wait_for_visible(timeout=5, raise_error=True):
            name_click.click()

    def click_username(self):
        pass

    def following_tab_click(self):
        if self["following_tab"].wait_for_visible(timeout=5, raise_error=True):
            self["following_tab"].click()

    def check_private_following(self):
        if self["private_following_tip_title"].wait_for_visible(timeout=5, raise_error=True):
            return self["private_following_tip_title"].text

    def check_private_following_desc(self):
        if self["private_following_tip_desc"].wait_for_visible(timeout=5, raise_error=True):
            return self["private_following_tip_desc"].text

    def wait_for_AWENavigationBar_visible(self):
        return self["AWENavigationBar"].wait_for_visible(timeout=3, raise_error=False)

    def nicky_name_click(self):
        self["add_account_entrance"].click()

    def profile_user_photo_click(self):
        if self["profile_user_photo_1"].wait_for_visible(timeout=5, raise_error=False):
            self["profile_user_photo_1"].long_click()
        elif self["profile_user_photo"].wait_for_visible(timeout=5, raise_error=False):
            self["profile_user_photo"].long_click()
        elif self["profile_user_photo_2"].wait_for_visible():
            self["profile_user_photo_2"].long_click()

    def wait_for_switch_account_visible(self):
        return self["switch_account_panel"].wait_for_visible(timeout=5, raise_error=False)

    def wait_for_search_friends_visible(self):
        if self["search_friends"].wait_for_visible(timeout=5, raise_error=False):
            return self["search_friends"].wait_for_visible(timeout=5)
        else:
            return self["search_users"].wait_for_visible(timeout=5)


    def wait_for_share_profile_visible(self):
        if self["share_profile_panel"].wait_for_visible(timeout=5):
            return True

    def wait_for_search_icon_visible(self):
        if self["search_icon"].wait_for_visible(timeout=5,raise_error=False):
            return self["search_icon"].wait_for_visible(timeout=5)
        else:
            return self["search_icon1"].wait_for_visible(timeout=5)

    def live_event_check(self):
        if self["live_event_btn"].wait_for_visible(timeout=5, raise_error=False):
            self["live_event_btn"].click()
        elif self["live_event_btn1"].wait_for_visible(timeout=5):
            self["live_event_btn1"].click()
        time.sleep(10)
        device = self.app.get_device()
        image_before_path = device.screenshot()
        from shoots_cv.cv import CV
        cv_ = CV()
        text = cv_.ocr_get_text(image_before_path)
        for dic in text:
            if (dic['rec'] == 'LIVE Events Calendar') or (dic['rec'] == 'LIVE Events'):
                return True
        return False

    def live_event_btn_isExited(self):
        if self["live_event_btn"].wait_for_visible(timeout=5, raise_error=False):
            return self["live_event_btn"].wait_for_visible(timeout=5, raise_error=False)
        elif self["live_event_btn1"].wait_for_visible(timeout=5):
            return self["live_event_btn1"].wait_for_visible(timeout=5, raise_error=False)

    def open_website(self):
        if self["website_link"].wait_for_visible(timeout=5, raise_error=False):
            self["website_link"].click()
        elif self["website_link_new"].wait_for_visible(timeout=5, raise_error=True):
            self["website_link_new"].click()
        time.sleep(10)
        return self.is_website_link_exits()

    def is_website_link_exits(self):
        return self["website_link"].wait_for_existing(timeout=5, raise_error=False)

    def change_website(self, website):
        self.click_website()
        if self["website_clear"].wait_for_visible(timeout=5, raise_error=False):
            self["website_clear"].click()
        self.edit_website(website)

    def edit_website(self, website):
        if self["website_input"].wait_for_visible(timeout=5, raise_error=True):
            self["website_input"].click()
            time.sleep(2)
            self["website_input"].input(website)
            if self["website_save"].wait_for_visible(timeout=5, raise_error=True):
                self["website_save"].click()

    def click_website(self):
        for _ in Retry(timeout=10, raise_error=True):
            if self["website_input"].existing and self["website_input"].visible:
                self["website_input"].click()
                return
            if self["website_input_edit"].existing and self["website_input_edit"].visible:
                self["website_input_edit"].click()
                return

    def is_website_opened(self):
        device = self.app.get_device()
        image_before_path = device.screenshot()
        from shoots_cv.cv import CV
        cv_ = CV()
        text = cv_.ocr_get_text(image_before_path)
        for dic in text:
            if (dic['rec'] == 'Open anyway'):
                return True
        return False

    def back_btn_click(self):
        self["back_btn"].click()

    def website_clear(self):
        if self["email_input"].wait_for_visible(timeout=5, raise_error=False):
            self["email_input"].click()
        self["website_clear"].click()
        self["website_save"].click()
        return self["email_input"].wait_for_disappear(timeout=5, raise_error=False)

    def ocr_click(self, device, text):
        time.sleep(2)
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)

    def website_go_back_click(self):
        if self['website_go_back_spark_btn'].wait_for_existing(timeout=3, raise_error=False):
            self['website_go_back_spark_btn'].click()
        else:
            self["website_go_back_btn"].click()

    def wait_for_all_videos_visible(self):
        return self["close_recents"].text

    def get_edit_nicky_name(self):
        return self["nicky_name_text_view"].text

    def wait_for_select_organization_visible(self):
        return self["select_organization"].wait_for_visible(timeout=5, raise_error=False)

    def wait_for_change_pronouns_panel_visible(self):
        return self["change_pronouns_panel"].wait_for_visible(timeout=5, raise_error=False)

    def wait_for_eyes_visible(self):
        if self["eyes_view_new"].wait_for_visible(timeout=5,raise_error=False):
            return self["eyes_view_new"].wait_for_visible(timeout=5)
        elif self["eyes_view_new1"].wait_for_visible(timeout=5):
            return self["eyes_view_new1"].wait_for_visible(timeout=5)

    def get_eyes_view(self):
        if self["eyes_view_new"].wait_for_visible(timeout=5,raise_error=False):
            return self['eyes_view_new']
        elif self["eyes_view_new1"].wait_for_visible(timeout=5):
            return self['eyes_view_new1']


    def get_profile_view_List_title_exist(self):
        if self["profile_view_List_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["profile_view_List_title"].wait_for_existing(timeout=5, raise_error=True)
        elif self["profile_view_List_title1"].wait_for_existing(timeout=5):
            return self["profile_view_List_title1"].wait_for_existing(timeout=5, raise_error=True)

    def click_rec_card(self):
        if self['user_rec_btn'].wait_for_visible(timeout=5, raise_error=True):
            self['user_rec_btn'].click()
        time.sleep(5)

    def check_rec_card_visible(self):
        if self["suggested_tittle"].wait_for_visible(timeout=5, raise_error=False):
            return self["suggested_tittle"].wait_for_visible(timeout=5, raise_error=True)
        elif self["suggested_tittle1"].wait_for_visible():
            return self["suggested_tittle1"].wait_for_visible(timeout=5, raise_error=True)

    def check_rec_card_disappear(self):
        return self["suggested_tittle"].wait_for_disappear(timeout=5, raise_error=True)

    def following_search(self, user):
        if self["following_search_bar"].wait_for_existing(timeout=5, raise_error=True):
            self["following_search_bar"].click()
            self["following_search_bar"].input(user)
        time.sleep(3)

    def click_following_search_user(self):
        if self["following_search_user"].wait_for_existing(timeout=5, raise_error=True):
            self["following_search_user"].click()

    def wait_for_following_magnifier_icon_existing(self):
        if self["magnifier_icon"].wait_for_existing(timeout=5,raise_error=False):
            return self["magnifier_icon"].wait_for_existing(timeout=5)
        elif self["magnifier_icon1"].wait_for_existing(timeout=5):
            return self["magnifier_icon1"].wait_for_existing(timeout=5)

    def following_search_other(self, user):
        if self["following_search_bar"].wait_for_existing(timeout=2):
            self["following_search_bar"].click()
            self["following_search_bar"].input(user)
            return self[user].wait_for_existing(timeout=2, raise_error=False)
        else:
            if self['following_list_back_tux_btn'].wait_for_existing(timeout=5, raise_error=False):
                self['following_list_back_tux_btn'].click()
            else:
                self["following_list_back_btn"].click()
            return False

    def go_into_search_user(self, user):
        if self[user].wait_for_existing(timeout=2, raise_error=True):
            self[user].click()

    def wait_for_other_profile_following_list_visible(self):
        return self["other_profile_view_controller"].wait_for_visible(timeout=5, raise_error=False)

    def click_plus(self):
        self["plus_btn"].click()

    def enter_story_archive(self):
        self["story_archive"].click()
        time.sleep(3)

    def click_avatar(self):
        self["profile_avatar"].click()

    def enter_story_collection(self):
        if self["story_collection"].wait_for_existing(timeout=4, raise_error=False):
            self["story_collection"].click()
            time.sleep(1)

    def enter_video(self):
        if self["video_collection"].wait_for_existing(timeout=4, raise_error=False):
            self["video_collection"].click()
            time.sleep(1)

    def play_story(self, index):
        if self["story_list"].wait_for_existing(timeout=5, raise_error=False):
            self["story_list"].items()[index].click()

    def get_story_count(self):
        if self["story_count"].wait_for_existing(timeout=4, raise_error=False):
            string = self["story_count"].text
            start = string.find('(') + 1
            end = string.find(')')
            count = int(string[start:end])
            return count

    def change_email(self, email):
        if self["email_input"].wait_for_visible(timeout=5, raise_error=True):
            self["email_input"].click()
        if self["email_clear"].wait_for_visible(timeout=3, raise_error=False):
            self["email_clear"].click()
        if (len(email) > 1):
            self["email_input"].input(email)
            if self["email_save"].wait_for_visible(timeout=5, raise_error=True):
                self["email_save"].click()
        time.sleep(2)

    def email_clear(self):
        if self["email_input"].wait_for_visible(timeout=5, raise_error=True):
            self["email_input"].click()
        if self["email_clear"].wait_for_visible(timeout=5, raise_error=False):
            self["email_clear"].click()
            time.sleep(3)
            self["email_save"].click()

    def email_input_disappear(self):
        return self["email_input"].wait_for_disappear(timeout=5, raise_error=False)

    def enter_edit_email(self):
        if self["email_input"].wait_for_visible(timeout=5, raise_error=True):
            self["email_input"].click()

    def click_favorites_button(self):
        if self["favorites_button"].wait_for_existing(timeout=5, raise_error=False):
            self["favorites_button"].click()
        time.sleep(2)

    def click_favorites_collection(self):
        if self["favorites_collection"].wait_for_existing(timeout=5, raise_error=False):
            self["favorites_collection"].click()
        time.sleep(2)

    def enter_into_analytics_collection_panel(self):
        self.swipe(y_direction=1, swipe_coefficient=5)
        self["analytics_collection"].wait_for_existing(timeout=5, raise_error=True)
        self["analytics_collection"].click()
        time.sleep(2)

    def click_hamburger_menu(self):
        if self["hamburger_menu"].wait_for_existing(timeout=5, raise_error=False):
            self["hamburger_menu"].click()
        time.sleep(2)

    def click_creator_tools(self):
        if self["creator_tools"].wait_for_existing(timeout=5, raise_error=False):
            self["creator_tools"].click()
        time.sleep(1)

    def click_creator_analytics(self):
        if self["creator_analytics"].wait_for_existing(timeout=5, raise_error=False):
            self["creator_analytics"].click()
        time.sleep(1)


class LoginPopup(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWELoginWindow')}

    def get_locators(self):
        return {
            "icon_close_panel": {"path": UPath(id_ == "icon_close_panel", type_ == "UIImageView")},
        }

    def close_login_popup(self):
        if self['icon_close_panel'].wait_for_existing(timeout=2, raise_error=False):
            self['icon_close_panel'].click()


class DonationSummaryWindow(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "donate_button": {"type": Control, "path": UPath(type_ == "UIButtonLabel", text_ == "Donate")},
            "donation_count": {"type": Control,
                               "path": UPath(type_ == "AWEDonationListHeaderView") / UPath(type_ == "UILabel",
                                                                                           index=1)},
            "donation_title": {"type": Control,
                               "path": UPath(controller_ == "AWEDonationViewController") / UPath(type_ == "UIView",
                                                                                                 index=1) / UPath(
                                   type_ == "UIView", index=0) / UPath(type_ == "UILabel")},
            "close_button": {"type": Control, "path": UPath(controller_ == "AWEDonationViewController") / UPath(
                type_ == "UIButton") / UPath(type_ == "UIImageView")}
        }

    def check_donation_count(self):
        return self["donation_count"].wait_for_visible(timeout=5, interval=1, raise_error=True)

    def check_donation_summary_title(self):
        return self["donation_title"].wait_for_visible(timeout=5, interval=1, raise_error=True)

    def donate_button_click(self):
        self["donate_button"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["donate_button"].click()
        time.sleep(5)

    def close_button_click(self):
        self["close_button"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["close_button"].click()
        time.sleep(5)


class TiltifyPageValidation(Window):
    window_spec = {"path": UPath(type_ == "XCUIElementTypeApplication")}

    def get_locators(self):
        return {
            "done_button": {"path": UPath(type_ == "XCUIElementTypeButton", ~label_ == "Done|完成")},
            "tiltify_page_title": {"path": UPath(type_ == "XCUIElementTypeOther", ~label_ == "Address|地址")}
        }

    def check_tiltify_page_title(self):
        self["tiltify_page_title"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        return True

    def done_button_click(self):
        self["done_button"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["done_button"].click()
        time.sleep(5)


class VideoPage(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "donation_anchor": {"type": Control, "path": UPath(type_ == "BDImageView") / UPath(type_ == "UILabel",
                                                                                               text_ == "ADG Family Foundation")},
            "donation_sticker": {"type": Control, "path": UPath(type_ == "AWEAwemeDetailTableViewCell", visible_ == True) / UPath(type_ == "ACCEmbedStickerDisplayView", depth=8)}
        }

    def donation_anchor_click(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['donation_anchor'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return self['donation_anchor'].click()

    def donation_sticker_click(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['donation_sticker'].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return self['donation_sticker'].click()

    def check_donation_sticker_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["donation_sticker"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return True
        return False

    def check_donation_anchor_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['donation_anchor'].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return True
        return False


class DonationSummaryWindow(BasePanel):
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "donate_button": {"type": Control, "path": UPath(id_ == "Donate", type_ == "UIButtonLabel")},
            "donation_count": {"type": Control,
                               "path": UPath(type_ == "AWEDonationListHeaderView") / UPath(type_ == "UILabel",
                                                                                           index=1)},
            "donation_title": {"type": Control,
                               "path": UPath(controller_ == "AWEDonationViewController") / UPath(type_ == "UIView",
                                                                                                 index=1) / UPath(
                                   type_ == "UIView", index=0) / UPath(type_ == "UILabel")},
            "close_button": {"type": Control, "path": UPath(controller_ == "AWEDonationViewController") / UPath(
                type_ == "UIButton") / UPath(type_ == "UIImageView")}
        }

    def check_donation_count_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['donation_count'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return True
        return False

    def check_donation_summary_title(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['donation_title'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return True
        return False

    def donate_button_click(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["donate_button"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return self['donate_button'].click()

    def close_button_click(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["close_button"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return self['close_button'].click()


class TiltifyDonationForm(Window):
    window_spec = {"path": UPath(type_ == "XCUIElementTypeApplication")}

    def get_locators(self):
        return {
            "done_button": {"path": UPath(type_ == "XCUIElementTypeButton", ~label_ == "Done|完成")},
            "tiltify_page_title": {"path": UPath(id_ == "URL", type_ == "XCUIElementTypeOther")},
            "learn_more_button": {'path': UPath(label_ == "Learn more")}
        }

    def check_tiltify_page_title(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["tiltify_page_title"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return True
        return False

    def done_button_click(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["done_button"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return self['done_button'].click()

    def click_learn_more(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['learn_more_button'].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return self['learn_more_button'].click()

    def get_Tiltify_donation_ui_scene(self):
        return UIScene(self['tiltify_page_title']).dump()


class ActionButtonsCVWindow(CVWindow):

    def get_locators(self):
        return {
            'Action_buttons': {'path': UPath(ocr_ == 'Action buttons')},
            'Email': {'path': UPath(ocr_ == 'Email')},
        }


class ActionButtonsWebView(Webview):
     view_spec = {
         "title": None
     }

     def get_locators(self):
         return {
             "add_your_email": {"type": WebElement, "path": UPath(class_ == "pt-8") / 0 / UPath(class_ == "truncate")},
             "add_your_app_link": {"type": WebElement, "path": UPath(class_ == "pt-8") / 1 / UPath(class_ == "truncate")},
             "back_button": {"type": WebElement, "path": UPath(type_ == "BUTTON", visible_ == True) / UPath(type_ == "path", depth=5)},
         }

     def click_add_your_email(self):
         if self["add_your_email"].wait_for_visible(timeout=30, raise_error=True):
            self["add_your_email"].click()
         time.sleep(2)

     def click_add_your_app_link(self):
         if self["add_your_app_link"].wait_for_visible(timeout=5, raise_error=True):
            self["add_your_app_link"].click()
         time.sleep(2)

     def click_email_back(self):
         if self["back_button"].wait_for_visible(timeout=5, raise_error=True):
            self["back_button"].click()
         time.sleep(2)

