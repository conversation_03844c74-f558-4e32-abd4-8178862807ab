# -*- coding:utf-8 _*-
import os
import time
import traceback
from shoots.config import settings
from shoots.util import ThreadGroupLocal
from shoots.logger import EnumLogLevel, LogRecord
from shoots_cv.cv import CV
from collections import defaultdict
import json
from shoots import logger
from shoots_ios.bdc_helper import bdc_call

_cv = CV()
pre_image_path = None
pre_ocr_result = None
global_ocr_results = None
global_pic_ocr_results = None


#
def click_close_attention_popup(self):
    """
    当直播间弹出关注主播,即可在主播开启直播时收到通知时，自动点击关闭
    """
    self.click_by_similar_coordinate(172, 262, text="点击关闭推荐关注主播弹窗")


def click_login_register(self):
    """
    当首页出现登录以关注账号,并对该视频点赞或发表评论弹窗时，自动点击关闭
    """
    self.click_by_similar_coordinate(185, 203, text="关闭登录或注册弹窗")


def click_follow_friends(self):
    """
    当出现关注你的好友弹窗时，自动点击关闭X
    """
    self.click_by_similar_coordinate(323, 112, text="click X")


def close_create_avatar_popup_window(self):
    """
    当出现创建头像弹窗时，自动点击关闭
    """
    self.click_by_similar_coordinate(279, 127, text="关闭创建头像弹窗")

def click_agree_and_continue_cn(app):
    try:
        app.click_by_ocr_text("同意并继续|同意並繼續", index=-1, auto_dismiss=False, fuzzy_match=False, refresh=True)
        time.sleep(1.5)
    except Exception as e:
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("can not find 同意并继续 %s " % str(e))

def swipe_up(instance):
    instance.swipe_up_cvbase()

unexpect_texts = [
    {"rec_text": "turn on push notifications", "click_text": "Later"},
    {"rec_text": "Gift your favorite videos to", "click_text": "OK"},
    {"rec_text": "Privacy Policy Update", "click_text": "ok"},
    {"rec_text": "your account will be closed", "click_text": "OK"},
    {"rec_text": "Save login", "click_text": "Cancel|Not Now"},
    # {"rec_text": "Learn more", "click_text": "OK"},
    {"rec_text": "Post Now", "click_text": "Post Now"},
    {"rec_text": "Refuser", "click_text": "Refuser"},
    {"rec_text": "View your", "click_text": "OK"},
    {"rec_text": "Don't Allow", "click_text": "Don't Allow"},
    {"rec_text": "Mulai tugas", "click_text": "Mulai tugas"},
    {"rec_text": "English auto-generated", "click_text": "OK"},
    {"rec_text": "Tiktok is better with friends", "click_text": "skip"},
    {"rec_text": "Sync your contacts", "click_text": "Not now"},
    {"rec_text": "Allow error", "click_text": "Don't Allow"},
    {"rec_text": "TikTok Stories", "click_text": "OK"},
    {"rec_text": "Would Like", "click_text": "Don't"},
    {"rec_text": "Ask App", "click_text": "Ask App"},
    {"rec_text": "Memulai", "click_text": "Memulai"},
    {"rec_text": "App Update", "click_text": "Cancel"},
    {"rec_text": "可以录制视频", "click_text": "好"},
    {"rec_text": "要傳送通知", "click_text": "不允许"},
    {"rec_text": "开启精准位置", "click_text": "不允许"},
    {"rec_text": "改善你的广告体验", "click_text": "下一步"},
    {"rec_text": "想访问您的通讯录", "click_text": "不允许"},
    {"rec_text": "想访问你的通讯录", "click_text": "不允许"},
    {"rec_text": "查看通讯录好友", "click_text": "不允许"},
    {"rec_text": "想访问您的照片", "click_text": "允许访问"},
    {"rec_text": "无法上传视频", "click_text": "点击以重试"},
    {"rec_text": "想访问您的照片", "click_text": "好"},
    {"rec_text": "账号状态", "click_text": "确定"},
    {"rec_text": "TikTok 限时动态", "click_text": "确定"},
    {"rec_text": "TikTok 限时动态", "click_text": "好的"},
    {"rec_text": "此设备上的直播已结束", "click_text": "确定"},
    {"rec_text": "允许TikTok访问你的", "click_text": "不允许"},
    {"rec_text": "使用无线数据", "click_text": "无线局域网与蜂窝网络"},
    {"rec_text": "想查找并连接", "click_text": "不允许"},
    {"rec_text": "想访问你的照片", "click_text": "允许访问"},
    {"rec_text": "允许访问所有照片", "click_text": "允许访问所有照片"},
    {"rec_text": "To upload from or download", "click_text": "允许访问"},
    {"rec_text": "已开启个人主页查看记录", "click_text": "好的"},
    {"rec_text": "已开启个人主页查看记录", "click_text": "保存"},
    {"rec_text": "想给您发送", "click_text": "不允许"},
    {"rec_text": "想给你发送", "click_text": "不允许"},
    {"rec_text": "同步你的通讯录", "click_text": "不允许"},
    {"rec_text": "同步你的通讯录", "click_text": "不要"},
    {"rec_text": "与通讯录好友互动", "click_text": "不允许"},
    {"rec_text": "查看好友的作品", "click_text": "好的"},
    {"rec_text": "我们更新了", "click_text": "知道了"},
    {"rec_text": "验证Apple", "click_text": "以后"},
    {"rec_text": "进行拼接", "click_text": "试试看"},
    {"rec_text": "寻找通讯录好友", "click_text": "不允许"},
    {"rec_text": "上滑查看更多视频|查看更多视频", "click_func": swipe_up},
    {"rec_text": "Swipe up for more", "swipe_up": ""},
    {"rec_text": "使用 App时允许", "click_text": "使用 App时允许"},
    {"rec_text": "使用App时允许", "click_text": "使用App时允许"},
    {"rec_text": "当前网页证书不可信", "click_text": "继续访问"},
    {"rec_text": "检测并屏蔽裸露内容", "click_text": "取消"},
    {"rec_text": "你使用的内存过多", "click_text": "关闭"},
    {"rec_text": "跟好友一起使用", "click_text": "跳过"},
    {"rec_text": "音乐已添加", "click_text": "知道了"},
    {"rec_text": "Find contacts", "click_text": "Don"},
    {"rec_text": "Warning", "click_text": "Cancel"},
    {"rec_text": "and connect", "click_text": "Don't"},
    {"rec_text": "facebook.com", "click_text": "取消"},
    {"rec_text": "同步Facebook好友", "click_text": "不允许"},
    {"rec_text": "Find Facebook friends", "click_text": "Don't"},
    {"rec_text": "可以看到你的发布", "click_text": "确定"},
    {"rec_text": "尚未发布", "click_text": "取消"},
    {"rec_text": "unposted video", "click_text": "Cancel"},
    {"rec_text": "你的意见反馈有回复了", "click_text": "取消"},
    {"rec_text": "确认使用的手机号码", "click_text": "暂时不要"},
    {"rec_text": "要求App不跟踪", "click_text": "要求App不跟踪"},
    {"rec_text": "隐私政策更新", "click_text": "确认"},
    {"rec_text": "隐私政策更新", "click_text": "确定"},
    {"rec_text": "Start watch", "click_text": "Start watch"},
    {"rec_text": "同意并继续", "click_func": click_agree_and_continue_cn},
    {"rec_text": "同意並繼續", "click_func": click_agree_and_continue_cn},
    {"rec_text": "送出礼物", "click_text": "好的"},
    {"rec_text": "我同意", "click_text": "我同意"},
    {"rec_text": "在这段视频中标记人", "click_text": "确认"},
    {"rec_text": "观看10分钟", "click_text": "确认"},
    {"rec_text": "中打开", "click_text": "打开"},
    {"rec_text": "开始创建", "click_text": "开始创建"},
    {"rec_text": "开启通知", "click_text": "暂时不要"},
    {"rec_text": "休息提醒", "click_text": "以后再说"},
    {"rec_text": "账号警告", "click_text": "知道了"},
    {"rec_text": "与更多人聊天", "click_text": "暂时不要|确定|ok"},
    {"rec_text": "登录以关注账号", "click_func": click_login_register},
    {"rec_text": "关注你的好友", "click_func": click_follow_friends},
    {"rec_text": "Follow your friends", "click_func": click_follow_friends},
    {"rec_text": "关注主播", "click_func": click_close_attention_popup},
    {"rec_text": "借助AI技术", "click_func": close_create_avatar_popup_window},
    {"rec_text": "查找通讯录好友", "click_text": "不允许"},
    {"rec_text": "允许推送热门内容", "click_text": "允许"},
    {"rec_text": "希望接收TikTok的最新", "click_text": "暂时不要"},
    {"rec_text": "不喜欢某段视频", "click_text": "知道了"},
    {"rec_text": "想添加VPN配置", "click_text": "允许"},
    {"rec_text": "你的账号即将关闭", "click_text": "确认"},
    {"rec_text": "你的账号即将被关闭", "click_text": "确认"},
    {"rec_text": "功能现已上线", "click_text": "确认"},
    {"rec_text": "功能现已上线", "click_text": "确定"},
    {"rec_text": "你的视频将会显示", "click_text": "确定"},
    {"rec_text": "你的视频将会显示", "click_text": "好的"},
    {"rec_text": "让你关注的人看到", "click_text": "暂时不要"},
    {"rec_text": "开启过滤的请求", "click_text": "暂时不要"},
    {"rec_text": "保存TikTok 登录账号?", "click_text": "取消"},
    {"rec_text": "保存TikTok登录账号?", "click_text": "取消"},
    {"rec_text": "显示视频", "click_text": "显示视频"},
    {"rec_text": "阅读状态", "click_text": "完成"},
    {"rec_text": "推荐此视频", "click_text": "推荐"},
    {"rec_text": "在你离开之前", "click_text": "暂时不要"},
    {"rec_text": "你的账号已退出登录", "click_text": "确定"},
    {"rec_text": "公开视频发布", "click_text": "立刻发布"},
    {"rec_text": "最多可查看这台设备上", "click_text": "好的"},
    {"rec_text": "确认使用的电子邮件地址", "click_text": "暂时不要"},
    {"rec_text": "保留登录信息", "click_text": "暂时不要"},
    {"rec_text": "已开启作品浏览记录", "click_text": "保存"},
    {"rec_text": "你熟悉哪些语言", "click_text": "确认"},
    {"rec_text": "开启在线状态显示", "click_text": "保存"},
    {"rec_text": "添加电子邮件地址", "click_text": "跳过"},
    {"rec_text": "输入手机号码", "click_text": "跳过"},
    {"rec_text": "跳过", "click_text": "跳过"},
    {"rec_text": "你如何评价刚观看过的视频", "click_text": "Cancel"},
    {"rec_text": "当你将创作者的", "click_text": "好的"},
    {"rec_text": "送礼物给自己喜爱视频", "click_text": "确定"},
    {"rec_text": "今寸<参加", "click_text": "今寸<参加"},
    {"rec_text": "后下", "click_text": "后下"},
    {"rec_text": "今寸<千工久", "click_text": "今寸<千工久"},
    {"rec_text": "今寸<手工夕", "click_text": "今寸<手工夕"},
    {"rec_text": "今寸<千工夕", "click_text": "今寸<千工夕"},
    {"rec_text": "今寸 参加", "click_text": "今寸 参加"},
    {"rec_text": "暂时不要", "click_text": "暂时不要"},
    {"rec_text": "What languages", "click_text": "Confirm"}

]

cur_dir = os.path.split(os.path.realpath(__file__))[0]
local_account_file = os.path.join(cur_dir, "english2Chinese_OCR_text.json")
with open(local_account_file, 'r') as f:
    simplified_chineses_dict = json.load(f)

simplified_chineses = defaultdict(str, simplified_chineses_dict)

from functools import wraps


def popup_handler(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        # 最多处理5个弹窗
        for times in range(5):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                logger.warn("action error")
                self.app.on_app_popup()

    return wrapper


class CVBase(object):
    """control with ocr
    """

    render_rate = 3

    def __init__(self):
        self.set_render_rate()

    def set_render_rate(self):
        cur_dir = os.path.split(os.path.realpath(__file__))[0]
        local_account_file = os.path.join(cur_dir, "ios_version_ocr_render.json")
        with open(local_account_file, 'r') as f:
            ios_version_ocr_render = json.load(f)
        ios_version_ocr_render_dict = defaultdict(str, ios_version_ocr_render)
        try:
            device = self.app.get_device()
        except:
            device = self.get_device()
        ios_os_version = str((device.detail_info)['model'])
        self.render_rate = ios_version_ocr_render_dict[ios_os_version]
        if self.render_rate == "":
            self.render_rate = 3
        logger.info("os version render_rate: {}".format(self.render_rate))

    def cv_existing(self, text, auto_dismiss=True, fuzzy_match=True, refresh=True):
        """is UIElement existing
        """
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("check text existing: {}".format(text))
        if "|" in text:
            text = text.split("|")
        rect = self.get_target_pos(text, auto_dismiss=auto_dismiss, fuzzy_match=fuzzy_match, refresh=refresh)
        return True if rect is not None else False

    def cv_existing_numbers(self, text, fuzzy_match=True):
        rect = self.get_target_pos(text, fuzzy_match=fuzzy_match)
        return len(rect)

    def cv_wait_for_visible(self, text, retry=2, auto_dismiss=True, fuzzy_match=True, refresh=True):
        for _ in range(retry):
            if self.cv_existing(text, fuzzy_match, auto_dismiss, refresh=refresh) is True:
                return True
            else:
                time.sleep(_ + 0.5)
        return False

    def get_ocr_results(self, refresh):
        global global_ocr_results

        if refresh or global_ocr_results is None:
            try:
                device = self.get_device()
            except:
                device = self.app.get_device()
            screenshot_path = os.path.join(settings.PROJECT_ROOT, "resources", 'tmp', device.serial)
            if not os.path.isdir(screenshot_path):
                os.makedirs(screenshot_path)
            screenshot_file = os.path.join(screenshot_path, "screenshot_{}.png".format(int(time.time())))
            device.screenshot(path=screenshot_file)
            try:
                ocr_results = self.get_words_in_pic(screenshot_file)
                print(screenshot_file)
                testcase = getattr(ThreadGroupLocal(), 'testcase', None)
                testcase.log_info("ocr_results: {}".format(ocr_results))
                global_ocr_results = ocr_results
            except Exception as e:
                testcase = getattr(ThreadGroupLocal(), 'testcase', None)
                testcase.log_info("ocr return error, the screenshot file is: {}".format(screenshot_file))
                traceback.print_exc()
                return None
        else:
            ocr_results = global_ocr_results
        return ocr_results

    def get_target_pos(self, text, index=-1, filter=True, auto_dismiss=True, fuzzy_match=True, refresh=True):
        """
            fuzzy_match: True:模糊匹配， False:精确匹配
        """
        if isinstance(text, str):
            text = [text]

        self.set_render_rate()
        match_ocr_rect = []
        ocr_results = self.get_ocr_results(refresh)
        if auto_dismiss:
            self.click_unexpect_text(ocr_results, self.render_rate)
        for ocr_result in ocr_results:
            for _text in text:
                if fuzzy_match and _text.lower() in ocr_result['rec'].lower():
                    match_ocr_rect.append({"rec": ocr_result['rec'], "pos": ocr_result['pos']})
                elif _text.lower() == ocr_result['rec'].lower():
                    match_ocr_rect.append({"rec": ocr_result['rec'], "pos": ocr_result['pos']})
                else:
                    for chinese_text in simplified_chineses[_text]:
                        if chinese_text != '' and chinese_text in ocr_result['rec']:
                            match_ocr_rect.append({"rec": ocr_result['rec'], "pos": ocr_result['pos']})
        if len(match_ocr_rect) > 0:
            if filter is True:
                return match_ocr_rect[index]
            else:
                return match_ocr_rect
        else:
            return None

    def get_ocr_pos(self, icon, loop=3, refresh=True, auto_dismiss=True):
        ocr_results = self.get_ocr_results(refresh)
        if auto_dismiss:
            self.click_unexpect_text(ocr_results, self.render_rate)
        try:
            device = self.get_device()
        except:
            device = self.app.get_device()
        screenshot_path = os.path.join(settings.PROJECT_ROOT, "resources", 'tmp', device.serial)
        if not os.path.isdir(screenshot_path):
            os.makedirs(screenshot_path)
        screenshot_file = os.path.join(screenshot_path, "screenshot_{}.png".format(int(time.time())))
        rect = None
        for i in range(loop):
            device.screenshot(path=screenshot_file)
            try:
                images = _cv.universal_ui_detection(screenshot_file, screenshot_file)
                if icon in images['result']:
                    rect = images['result'][icon]
                    break
                else:
                    time.sleep(1)
            except Exception:
                pass
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        attachments = {"%s" % device.udid: screenshot_file}
        stack = None
        msg = "图像识别记录"
        level = EnumLogLevel.INFO
        testcase.log_record(msg, level=level, attachments=attachments, stack=stack)
        return rect

    def icon_existing(self, icon, refresh=True, auto_dismiss=True):
        """
        根据元素识别判断图标元素是否存在
        图标：https://bytedance.feishu.cn/sheets/shtcn0zf24O37GP6TmtPf7Tduvc
        """
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("check text existing: {}".format(icon))
        rect = self.get_ocr_pos(icon, loop=1, refresh=refresh, auto_dismiss=auto_dismiss)
        return True if rect is not None else False

    def icon_wait_for_visible(self, icon, retry=3, refresh=True, auto_dismiss=True):
        """
        根据元素识别等待图标出现
        图标：https://bytedance.feishu.cn/sheets/shtcn0zf24O37GP6TmtPf7Tduvc
        """
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("wait text existing: {}".format(icon))
        for _ in range(retry):
            if self.icon_existing(icon, refresh, auto_dismiss) is True:
                return True
            else:
                time.sleep(3)
        return False

    def count_icon_number(self, icon, refresh=True, auto_dismiss=True):
        """
        根据元素识别判断图标元素是否存在
        图标：https://bytedance.feishu.cn/sheets/shtcn0zf24O37GP6TmtPf7Tduvc
        """
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("check text existing: {}".format(icon))
        rect = self.get_ocr_pos(icon, loop=1, refresh=refresh, auto_dismiss=auto_dismiss)
        return len(rect)

    def click_by_ocr_icon(self, icon, index=0, render_rate=3, long_click=False, loop=3, refresh=True,
                          auto_dismiss=True):
        """
        根据元素识别点击图标
        图标：https://bytedance.feishu.cn/sheets/shtcn0zf24O37GP6TmtPf7Tduvc
        """
        self.set_render_rate()
        render_rate = float(self.render_rate)
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("click: {}".format(icon))
        rect = self.get_ocr_pos(icon, loop=loop, refresh=refresh, auto_dismiss=auto_dismiss)
        try:
            device = self.get_device()
        except:
            device = self.app.get_device()
        if rect is None:
            raise Exception("can not find icon:{} in screenshot".format(icon))
        else:
            if index < len(rect):
                pos = rect[index]
            else:
                pos = rect[-1]
            x = (pos[0] + pos[2]) / 2
            y = (pos[1] + pos[3]) / 2
            testcase.log_info("click: x:{}, y:{} {}".format(x / render_rate, y / render_rate, rect))
            if long_click is True:
                device.long_click(x / render_rate, y / render_rate)
            else:
                device.click(x / render_rate, y / render_rate)

    def click_by_ocr_text(self, text, index=-1, render_rate=2, auto_dismiss=True, long_click=False, refresh=True, fuzzy_match=True,
                          wait=False):
        self.set_render_rate()
        render_rate = float(self.render_rate)
        if "|" in text:
            text = text.split("|")
        if wait:
            self.cv_wait_for_visible(text, auto_dismiss=auto_dismiss, refresh=refresh)
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        try:
            device = self.get_device()
        except:
            device = self.app.get_device()
        rect = self.get_target_pos(text, index, filter=True, auto_dismiss=auto_dismiss, fuzzy_match=fuzzy_match, refresh=refresh)
        if rect is None:
            raise Exception("can not find text:{} in screenshot".format(text))
        else:
            pos = rect['pos']
            x = (pos[0] + pos[2]) / 2
            y = (pos[1] + pos[3]) / 2
            testcase.log_info("try ocr click {}".format(text))
            testcase.log_info("click: x:{}, y:{} ({})".format(x / render_rate, y / render_rate, str(rect)))
            if long_click is True:
                device.long_click(x / render_rate, y / render_rate)
            else:
                device.click(x / render_rate, y / render_rate)
            self._take_screenshoot(text)
            time.sleep(1)
        return True if rect is not None else False

    def click_all_by_ocr_text(self, text, long_click=False, refresh=True, fuzzy_match=True,
                              wait=False):
        self.set_render_rate()
        render_rate = float(self.render_rate)
        if wait:
            self.cv_wait_for_visible(text, refresh=refresh)
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        try:
            device = self.get_device()
        except:
            device = self.app.get_device()
        rects = self.get_target_pos(text, index=0, filter=False, fuzzy_match=fuzzy_match, refresh=refresh)
        if rects is None:
            raise Exception("can not find text:{} in screenshot".format(text))
        else:
            for rect in rects:
                pos = rect['pos']
                x = (pos[0] + pos[2]) / 2
                y = (pos[1] + pos[3]) / 2
                testcase.log_info("try ocr click {}".format(text))
                testcase.log_info("click: x:{}, y:{}".format(x / render_rate, y / render_rate))
                if long_click is True:
                    device.long_click(x / render_rate, y / render_rate)
                else:
                    device.click(x / render_rate, y / render_rate)
                time.sleep(1)

    def cv_wait_for_pic_visible(self, template_pic_path, retry=3, refresh=True):
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("wait pic existing: {}".format(template_pic_path))
        for _ in range(retry):
            if self.get_target_pic_pos(template_pic_path, refresh=refresh) is not None:
                return True
            else:
                time.sleep(_ + 0.5)
        return False

    def get_target_pic_pos(self, template_pic_path, index=0, refresh=True):
        template_pic_path = os.path.join(settings.PROJECT_ROOT, "resources", "images", template_pic_path)
        global global_pic_ocr_results
        if refresh or global_pic_ocr_results is None:
            try:
                device = self.get_device()
            except:
                device = self.app.get_device()
            screenshot_path = os.path.join(settings.PROJECT_ROOT, "resources", 'tmp', device.serial)
            if not os.path.isdir(screenshot_path):
                os.makedirs(screenshot_path)
            screenshot_file = os.path.join(screenshot_path, "screenshot_{}.png".format(int(time.time())))
            device.screenshot(path=screenshot_file)
            try:
                cv_ = CV()
                pic_ocr_results = cv_.tpl(template_pic_path, screenshot_file,
                                          template_pic_device_resolution=[1080, 1920])
                print(screenshot_file)
                testcase = getattr(ThreadGroupLocal(), 'testcase', None)
                # testcase.log_info("ocr_results: {}".format(pic_ocr_results))
                global_pic_ocr_results = pic_ocr_results
            except Exception as e:
                testcase = getattr(ThreadGroupLocal(), 'testcase', None)
                testcase.log_info("ocr return error, the screenshot file is: {}".format(screenshot_file))
                traceback.print_exc()
                return None
        else:
            pic_ocr_results = global_pic_ocr_results
        if len(pic_ocr_results['result']) >= index:
            return pic_ocr_results['result'][index]['center_pos']
        else:
            return None

    def _click_by_ocr_pic(self, template_pic_path, index=0, render_rate=1, long_click=False, refresh=True, wait=False):
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.log_info("click: {}".format(template_pic_path))
        if wait:
            self.cv_wait_for_pic_visible(template_pic_path, refresh=refresh)
        try:
            device = self.get_device()
        except:
            device = self.app.get_device()
        rect = self.get_target_pic_pos(template_pic_path, index=index, refresh=refresh)
        if rect is None:
            raise Exception("can not find pic:{} in screenshot".format(template_pic_path))
        else:
            x = rect[0]
            y = rect[1]
            testcase.log_info("click: x:{}, y:{}".format(x / render_rate, y / render_rate))
            if long_click is True:
                device.long_click(x / render_rate, y / render_rate)
            else:
                device.click(x / render_rate, y / render_rate)
            self._take_screenshoot(template_pic_path)

    @popup_handler
    def click_by_ocr_pic(self, template_pic_path, index=0, render_rate=1, long_click=False, refresh=True, wait=False):
        self._click_by_ocr_pic(template_pic_path, index, render_rate, long_click, refresh, wait)

    @staticmethod
    def _take_screenshoot(name):
        """
        自动在进行图片识别点击的时候进行截图
        """
        name = ''.join(filter(str.isalnum, name))
        time_stamp = int(time.time())
        pic_name = "%s_%s" % (time_stamp, name)
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        testcase.take_screenshot(testcase.device, file_path="%s.png" % pic_name, msg=pic_name)

    def click_by_similar_coordinate(self, x_rate, y_rate, long_click=False, text=""):
        x_rate = x_rate / 375
        y_rate = y_rate / 812
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        try:
            device = self.app.get_device()
        except:
            device = self.get_device()
        height = device.screen_rect.height
        width = device.screen_rect.width
        # print("分辨率",height, width )
        x = int(width * x_rate)
        y = int(y_rate * height)

        testcase.log_info("click position of {}".format(text))
        testcase.log_info("click: x:{}, y:{}".format(x, y))
        if long_click is True:
            device.long_click(x, y)
        else:
            device.click(x, y)
        self._take_screenshoot(text)
        time.sleep(1)

    def click_unexpect_text(self, ocr_results, render_rate):
        unexpect_texts_found = []
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        for unexpect_text in unexpect_texts:
            rec_text = unexpect_text.get("rec_text")
            for ocr_result in ocr_results:
                ocr_text = ocr_result['rec'].lower()
                if rec_text.lower() in ocr_text:
                    from shoots.context import current_testcase
                    try:
                        self.app = current_testcase().app
                    except:
                        pass
                    if 'click_text' in unexpect_text:
                        testcase.log_info("find unexpect text:{}".format(unexpect_text))
                        click_texts = unexpect_text.get("click_text")
                        click_texts = click_texts.split('|')
                        for click_text in click_texts:
                            unexpect_texts_found.append(click_text)
                    # elif 'click_upath' in unexpect_text:
                    #     self[unexpect_text['click_upath']].click()
                    elif 'click_x_y' in unexpect_text:
                        x, y = unexpect_text['click_x_y']
                        self.click_by_similar_coordinate(x, y)
                    elif 'click_func' in unexpect_text:
                        unexpect_text['click_func'](self)

        for ocr_result in ocr_results:
            ocr_text = ocr_result['rec'].lower()
            if not unexpect_texts_found:
                break
            else:
                for unexpect_text_val in unexpect_texts_found:
                    if unexpect_text_val.lower() in ocr_text:
                        try:
                            pos = ocr_result['pos']
                            x = (pos[0] + pos[2]) / 2
                            y = (pos[1] + pos[3]) / 2
                            testcase.log_info(
                                "find unexcept text: {}, and try click {}".format(unexpect_text_val, ocr_text))
                            testcase.log_info("click: x:{}, y:{}".format(x / render_rate, y / render_rate))
                            try:
                                self.app.get_device().click(x / render_rate, y / render_rate)
                            except:
                                self.app.get_device().click(x / render_rate, y / render_rate)
                        except Exception as e:
                            testcase.log_info(str(e))
                            testcase.log_info("can not find {}".format(unexpect_text_val))

    def get_words_in_pic(self, screenshot_file):
        global pre_image_path
        global pre_ocr_result
        # if pre_image_path is not None:
        #     image_diff = ocr_helper.dhash_diff_pic(screenshot_file, pre_image_path)
        #     if image_diff is True and pre_ocr_result:
        #         return pre_ocr_result
        cv_ = CV()
        ocr_results = cv_.ocr_get_text(screenshot_file)
        if ocr_results is not None:
            pre_image_path = screenshot_file
            pre_ocr_result = ocr_results
        return ocr_results

    def swipe_up_x_cvbase(self, x_rate, y_rate, height_rate, ratio=4):  # 从上到下滑动
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        try:
            device = self.app.get_device()
        except:
            device = self.get_device()
        height = device.screen_rect.height
        width = device.screen_rect.width
        x_rate = x_rate / 414.0
        y_rate = y_rate / 896.0
        height_rate = height_rate / 896.0
        x1 = int(width * x_rate)
        y1 = int(height * y_rate)
        y2 = (height * height_rate) / ratio + y1
        # x2 = x1
        device.drag(x1, y1, x1, y2)

    def swipe_up_y_cvbase(self, x_rate, y_rate, height_rate, ratio=4):  # 从左到右滑动
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        try:
            device = self.app.get_device()
        except:
            device = self.get_device()
        height = device.screen_rect.height
        width = device.screen_rect.width
        x_rate = x_rate / 414.0
        y_rate = y_rate / 896.0
        height_rate = height_rate / 414.0
        x1 = int(width * x_rate)
        y1 = int(height * y_rate)
        x2 = (width * height_rate) / ratio + x1
        # y2 = y1
        device.drag(x1, y1, x2, y1)

    def swipe_up_cvbase(self, x_rate=209, y_rate=736, height_rate=100, ratio=4):  # 从下到上滑动
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        try:
            device = self.app.get_device()
        except:
            device = self.get_device()
        height = device.screen_rect.height
        width = device.screen_rect.width
        x_rate = x_rate / 414.0
        y_rate = y_rate / 896.0
        height_rate = height_rate / 896.0
        offset_y = (height * height_rate) / ratio
        to_x = int(width * x_rate)
        to_y = int(height * y_rate) - offset_y
        device.drag(to_x + 15, to_y, to_x, -offset_y)

    def swipe_up_birthday(self, x_rate, y_rate, height_rate, ratio=4):
        testcase = getattr(ThreadGroupLocal(), 'testcase', None)
        try:
            device = self.app.get_device()
        except:
            device = self.get_device()
        height = device.screen_rect.height
        width = device.screen_rect.width
        x_rate = x_rate / 414.0
        y_rate = y_rate / 896.0
        height_rate = height_rate / 896.0
        x1 = int(width * x_rate)
        y1 = int(height * y_rate)
        y2 = (height * height_rate) / ratio + y1
        x2 = x1
        device.drag(x1, y1, x2, y2)
