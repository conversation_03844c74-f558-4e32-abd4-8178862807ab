"""
Author: leizihui
Date: 2025-3-5
FilePath: business/vod/common/ios/panels/vod_feed.py
Description: 点播iOS feed面板
"""

from uibase.upath import *
from uibase.base import *
from utils.common.log_utils import logger
from uibase.controls import *


class VodPanelIOS(Window):
    """
    点播 iOS feed 面板
    """
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            "首页": {"path": UPath(id_ == "main_bottom_button_home")},
            "主页": {"path":UPath(id_ == "主页")},
            "privacy_policy": {"type": Control, "path": UPath(text_ == "Got it", visible_ == True)},
            "Home": {"type": Control, "path": UPath(
                ~type_ == "AWETabbarGeneralButton|TTKTabBarFeedButton|TTKTabBarButton|TikTokKidsTabBarButtonInnerView",
                ~label_ == "Home|首页|For You|推荐|Refresh") / UPath(~text_ == "Home|首页|For You|推荐|Refresh")},
            "kids_home": {"path": UPath(type_ == 'TikTokKidsTabBarButton') / 2},
            "收藏": {"path": UPath(type_ == "UIImageView", label_ == "收藏")},
            "收藏-作品": {"path": UPath(~text_ == "作品.*")},
            "favorite_first_video": {
                "path": UPath(id_ == "collectionView", visible_ == True) / 0 / UPath(id_ == "profile_video", depth=5)},
            "favorite_2nd_video": {
                "path": UPath(id_ == "collectionView", visible_ == True) / 1 / UPath(id_ == "profile_video", depth=5)},
            "exit_video": {"path": UPath(label_ == "返回")},
            "profile_first_video": {
                "path": UPath(id_ == "collectionView") / 4 / UPath(type_ == "BDImageView", depth=5)},

        }

    def swipe_up_times(self, times=1, duration=0):
        """
        首页上滑指定次数
        """
        # 手动处理一波弹窗
        self.app.on_app_popup()

        logger.info("[点播-ios] 开始上划 %d 次" % times)
        logger.info("[点播-ios] 每次上划后等待 %d 秒" % duration)
        count = 1
        while count <= times:
            self.scroll(coefficient_y=0.6)
            if duration > 0:
                time.sleep(duration)
            count += 1
            logger.info("[点播-ios] 上划第 %d 次" % (count - 1))

    def swipe_down_times(self, times=1, duration=0):
        """
        首页下滑指定次数
        """
        # 手动处理一波弹窗
        self.app.on_app_popup()
        logger.info("[点播-ios] 开始下划 %d 次" % times)
        logger.info("[点播-ios] 每次下划后等待 %d 秒" % duration)
        count = 1
        while count <= times:
            self.scroll(coefficient_y=-0.6)
            if duration > 0:
                time.sleep(duration)
            count += 1
            logger.info("[点播-ios] 下划第 %d 次" % (count - 1))

    def into_home_page(self):
        if self["privacy_policy"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["privacy_policy"].click()
        try:
            if self["Home"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["Home"].click()
        except:
            if self["Home"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["Home"].click()
        if self["kids_home"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["kids_home"].click()
        time.sleep(1)

    def vod_pause(self):
        self.click()

    def open_profile_tab(self):
        """
        进入主页并处理弹窗
        """

        self.scroll(coefficient_y=0.5)
        if self["主页"].wait_for_visible(timeout=5, raise_error=False):
            self["主页"].click()
            logger.info("[点播] 点击主页按钮")

    def favorite_tab(self):
        """
        个人页点击收藏
        """

        if self["收藏"].wait_for_visible(timeout=5, raise_error=False):
            self["收藏"].click()
            logger.info("[点播] 点击收藏按钮")
        else:
            logger.debug("[点播] 找不到收藏按钮")

        if self["收藏-作品"].wait_for_visible(timeout=5, raise_error=False):
            self["收藏-作品"].click()
            logger.info("[点播] 点击收藏-作品")
        else:
            logger.debug("[点播] 找不到收藏-作品按钮")

    def favorite_first_video(self):
        """
        个人页点击收藏的第一个视频
        """

        if self["favorite_first_video"].wait_for_visible(timeout=5, raise_error=False):
            self["favorite_first_video"].click()
        else:
            logger.debug("[点播] 找不到favorite_first_video")

    def favorite_2nd_video(self):
        """
        个人页点击收藏的第2个视频
        """

        if self["favorite_2nd_video"].wait_for_visible(timeout=5, raise_error=False):
            self["favorite_2nd_video"].click()
        else:
            logger.debug("[点播] 找不到favorite_2nd_video")

    def exit_profile_video(self):
        """
        退出个人页视频播放
        """

        if self["exit_video"].wait_for_visible(timeout=5, raise_error=False):
            self["exit_video"].click()
        else:
            logger.debug("[点播] 找不到exit_video")

        if not self["收藏"].wait_for_visible(timeout=3, raise_error=False):
            self.open_profile_tab()
            self.favorite_tab()
            logger.info("[点播] 重新进入收藏页")


    def profile_first_video(self):
        """
        个人页点击的第一个视频
        """

        if self["profile_first_video"].wait_for_visible(timeout=5, raise_error=False):
            self["profile_first_video"].click()
        else:
            logger.debug("[点播] 找不到profile_first_video")





