# -*- coding: utf8 -*-
from socket import timeout
import time

from .base_panel import *
from shoots.retry import Retry
from shoots_cv.controls import C<PERSON><PERSON>indow, CVElement
from shoots_cv.upath import tpl_
from shoots_cv.cv import CV
from shoots_cv.upath import ocr_
from uibase.base import *

from business.vod.common.ios.edit_profile_win import ProfileEditWin
from .login_panel import LoginPanel
from .video_detail_win import SendToPanel
from .me_panel import MePanel
from .debug_panel import DebugPanel
from .settings_win import SettingsWin
from .basic_views import AWEPlayInteractionViewController, KeyboardWindow
from uibase.exceptions import AppPopupNotHandled, UIAmbiguousError, UIStatusError


class FeedPanel(BasePanel):
    """Feed Panel
    """
    # window_spec = {"path": UPath(type_ == 'AWEFeedRootViewController')}
    window_spec = {"path": UPath(type_ == 'AWEMaskWindow')}

    def get_locators(self):
        return {
            #   Panel_Controller
            "panel_controller": {"type": Control, "path": UPath(type_ == 'AWEFeedRootViewController')},
            #   Title
            "foryou": {"type": Control, "path": UPath(type_ == 'TikTokFeedTabItemControl')},
            "foryou_new": {"type": Control, "path": UPath(id_ == "titleLabel", label_ == "For You")},
            "following": {"type": Control, "path": UPath(type_ == 'TikTokFeedTabItemFollowControl')},
            "nearby": {"type": Control, "path": UPath(~id_ == "Nearby|Singapore", visible_ == True)},
            "explore": {"type": Control, "path": UPath(text_ == "Explore", visible_ == True)},
            "explore_userhead": {"type": Control, "path": UPath(type_ == "UICollectionView") / 6 / UPath(type_ == "ExploreFeedCellInteractionAuthorView") / UPath(type_ == "UIImageView", visible_ == True, depth=5)},
            #   Region_Panel
            "region": {"type": Control, "path": UPath(type_ == 'TIKTOKSwitchRegionButton', visible_ == True)},
            "region_close": {"type": Control, "path": UPath(type_ == '_UIModernBarButton', visible_ == True)},
            #   Feed_Operation
            "feed": {"type": Control, "path": UPath(type_ == 'AWEFeedTableView', visible_ == True)},
            "feed1": {"type": Control, "path": UPath(~type_ == 'TTKFeedPlayerContainerView|TTkFeedInteractionMainView',
                                                     visible_ == True) / UPath(
                type_ == 'ACCStickerSDKExcludeSelfView')},
            "like": {
                "type": Control,
                "path": UPath(type_ == "UIView") /
                        UPath(type_ == "UITableViewCellContentView") /
                        UPath(type_ == "TTKFeedInteractionRootView") /
                        UPath(type_ == "TTKFeedInteractionMainView") /
                        UPath(type_ == "TTKFeedPassthroughStackView", label_ == "right") /
                        UPath(type_ == "TTKFeedPassthroughView", index=2) /
                        UPath(label_ == "Like") /
                        UPath(type_ == "UIImageView", visible_ == True),
            },
            "like_count": {
                "type": Control,
                "path": UPath(label_ == 'Like') / UPath(type_ == 'UILabel', visible_ == True)
            },
            "favorite_area": {"type": Control, "path": UPath(type_ == "AWEFeedViewCell", visible_ == False) / UPath(
                id_ == "TTKPlayInteractionFavoriteElement")},
            "comment": {"type": Control, "path": UPath(id_ == 'AWEPlayInteractionCommentElement', visible_ == True)},
            "comment1": {"type": Control,
                         "path": UPath(label_ == "Comments") / UPath(type_ == "UIImageView", visible_ == True)},
            "comment_following": {"type": Control,
                                  "path": UPath(id_ == '(AWEPlayInteractionCommentElement)', visible_ == True,
                                                index=0) / UPath(type_ == 'UIView')},
            "comment_close": {"type": Control, "path": UPath(id_ == 'closeBtn', visible_ == True)},
            "comment_count": {"type": Control,
                              "path": UPath(label_ == 'Comment') / UPath(type_ == 'UILabel', visible_ == True)},
            "share": {"type": Control,
                      "path": UPath(label_ == 'Share') / UPath(id_ == 'aweImageView', visible_ == True, index=0)},
            "share_label": {"type": Control,
                            "path": UPath(id_ == "iconHomeShareRight", type_ == "UIImageView", visible_ == True,
                                          index=0)},
            "share_button": {"type": Control,
                             "path": UPath(id_ == "dmIcon") / UPath(type_ == "UIImageView", visible_ == True)},
            "share_alternate": {
                "path": UPath(type_ == "UITransitionView") /
                        UPath(type_ == "UIDropShadowView") /
                        UPath(type_ == "UILayoutContainerView") /
                        UPath(type_ == "UITransitionView") /
                        UPath(type_ == "UIViewControllerWrapperView") /
                        UPath(type_ == "UILayoutContainerView") /
                        UPath(type_ == "UINavigationTransitionView") /
                        UPath(type_ == "UIViewControllerWrapperView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "AWESlidingScrollView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "AWENewFeedTableView") /
                        UPath(type_ == "AWEFeedViewCell") /
                        UPath(type_ == "UITableViewCellContentView") /
                        UPath(type_ == "TTKFeedInterfactionRootView") /
                        UPath(type_ == "TTKFeedInteractionMainView") /
                        UPath(type_ == "TTKFeedPassthroughStackView") /
                        UPath(type_ == "TTKFeedPassthroughView") /
                        UPath(type_ == "AWEFeedVideoButton", label_ == "Share", visible_ == True) /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UIImageView", index=0)
            },
            "share_close": {"type": Control, "path": UPath(type_ == 'AWEShareDismissButton', visible_ == True)},
            "share_count": {"type": Control, "path": UPath(label_ == 'Share') /
                                                     UPath(type_ == 'UILabel', visible_ == True)},
            "share_parent": {"type": Control, "path": UPath(id_ == "null", label_ == "Share", visible_ == True)},
            "feed_guide": {"type": Control, "path": UPath(type_ == 'LOTAnimationView', id_ == '(imageView)')},
            "more": {"path": UPath(label_ == 'More') / UPath(id_ == '(aweImageView)')},
            "select_area": {"path": UPath(id_ == '(secondRowView)', visible_ == True)},
            "feed_intercept_interaction_view": {
                "type": AWEPlayInteractionViewController,
                "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) /
                        UPath(type_ == "UITableViewCellContentView", visible_ == True) /
                        UPath(type_ == "TTKFeedInteractionMainView", visible_ == True)},
            "privacy_policy": {"type": Control, "path": UPath(text_ == "Got it", visible_ == True)},
            "return_btn": {'path': UPath(label_ == "returnButton")},

            #   Feed_Information
            "feed_current": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True)},
            "feed_creator_name": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) /
                                                           UPath(id_ == 'nameLabel')},
            "feed_user_name": {"root": "feed_current",
                               "path": UPath(type_ == 'AWEUserNameLabel') / UPath(id_ == 'nameLabel')},
            # "feed_description": {"type": Control, "path": UPath(~type_ == 'YYLabel|AWEPlayInteractionDescriptionLabel', visible_ == True)},
            "feed_description": {"type": Control, "path": UPath(type_ == 'AWEPlayInteractionDescriptionLabel',
                                                                visible_ == True, index=0)},
            "description_translation": {"type": Control, "path": UPath(id_ == 'See translation', visible_ == True)},
            "feed_report": {"type": Control, "path": UPath(id_ == '(feed_home_report)')},
            "photomode_dot": {"type": Control, "path": UPath(type_ == "AWEDotStylePageControl")},

            "feed_search": {"type": Control, "path": UPath(type_ == 'TTKSearchEntranceButton')},
            "feed_search_edit": {"type": Control, "path": UPath(type_ == "UISearchBarTextFieldLabel")},
            "search_icon": {"type": Control, "path": UPath(id_ == "searchEntranceView")},
            "feed_comment_suggested": {"type": Control, "path": UPath(id_ == '(suggestedText)', visible_ == True)},
            "feed_comment_input": {"type": Control, "path": UPath(type_ == '_UITextViewCanvasView')},
            "feed_comment": {"type": Control, "path": UPath(~id_ == 'icon_home_comment|AWEPlayInteractionCommentElement', visible_ == True, index=0)},
            "comment_at_friends": {"type": Control, "path": UPath(label_ == 'Mention friend in comment')},
            "comment_at_friends_list": {"type": CommentAtFriends_list, "path": UPath(controller_ == 'AWECommentSearchViewController')/UPath(type_ == 'UITableView')},
            "at_friends_list": {"type": Control, "path": UPath(id_ == '(searchContainerView)')},
            "feed_follow": {"type": Control, "path": UPath(type_ == 'LOTAnimationView', visible_ == True)},
            "share_button_v1": {"type": Control, "root": "feed_current",
                                "path": UPath(type_ == 'AWEFeedVideoButton', label_ == 'Share', visible_ == True)},
            "share_button_v5": {"type": Control, "root": "feed_current",
                                "path": UPath(label_ == 'Share', visible_ == True) / UPath(id_ == '(aweImageView)')},
            # "close_share_panel": {"type": Control, "path": UPath(label_ == 'iconShare close small') / UPath(type_ == "UIImageView")},
            "search_at_friends_list": {"type": Control,
                                "path": UPath(controller_ == 'AWECommentSearchViewController') / UPath(
                                    type_ == 'UITableView')},
            "more_button_v5": {"type": Control,
                               "path": UPath(label_ == 'More', visible_ == True) / UPath(id_ == '(aweImageView)')},
            "close_share_panel": {"type": Control,
                                  "path": UPath(type_ == 'AWEShareDismissButton') / UPath(type_ == 'UIButtonLabel')},
            "close_share_panel_v2": {"type": Control,
                                     "path": UPath(type_ == 'UIImageView', id_ == 'iconShare_close_small')},
            "long_press": {"type": Control, "path": UPath(type_ == 'UITableView')},
            "long_press_friends_list": {"type": LongPressChatList, "path": UPath(type_ == 'UITableView')},
            "sent_btn": {"type": Control, "path": UPath(text_ == 'Sent')},
            "not_interested_btn": {"type": Control, "path": UPath(label_ == "Not interested", type_ == "UILabel",
                                                                  id_ == "(titleLabel)")},
            "share_element": {"type": Control,
                              "path": UPath(id_ == 'AWEPlayInteractionShareElement', visible_ == True)},

            # Feed_Debug
            "feed_debug": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) / UPath(
                id_ == '(TTKFeedDebugElement)', visible_ == True)},
            "feed_debug_panel": {"type": Control, "path": UPath(type_ == 'TTKFeedDebugManageVideosView')},
            "manage_feed": {"type": Control, "path": UPath(id_ == '(gearButton)')},
            "search_vid": {"type": Control, "path": UPath(type_ == 'UIButtonLabel', label_ == 'Search videos')},
            "search_result": {"type": Control, "path": UPath(id_ == '(searchButton)')},
            "input_vid": {"type": Control, "path": UPath(type_ == 'UITextField', visible_ == True)},
            "debug_video_input": {"type": Control,
                                  "path": UPath(type_ == 'TUXTextActionSheetItemView', id_ == 'Import')},
            "copy_vid": {"type": Control, "path": UPath(id_ == "Copy video ID")},
            "feed_panel_dropdown": {"type": Control, "path": UPath(id_ == "IconListArrowDown") / UPath(type_ == "UIImageView")},
            # Exploring
            "favorites_icon": {"type": Control, "path": UPath(type_ == 'TTKFeedInteractionMainView', index=0) / UPath(
                ~id_ == 'ic_original_bookmark_before|ic_bookmark_1', visible_ == True)},
            "feed_trending_bar": {"type": Control,
                                  "path": UPath(type_ == 'TTKFeedBaseBottomBarView', visible_ == True)},
            "trends_detail": {"type": Control, "path": UPath(
                ~label_ == 'TTKFeedDetailSearchBillboardRankTitleViewComponent|TTKSwiftFeedDetailSearchBillboardRankTitleViewComponent') / 0},
            "trends_title": {"type": Control, "path": UPath(label_ == 'Trending · ', visible_ == True)},

            # Location-Monitor
            "search_click": {"path": UPath(type_ == 'UISearchBarTextField')},
            "privacy_test_page": {"path": UPath(type_ == 'AWEDebugTableViewCell', visible_ == True)},
            "Mock_click": {"path": UPath(text_ == "Mock")},
            "Guard_SDK": {
                "path": UPath(controller_ == "PBDebugViewController") / UPath(type_ == "UITableView") / 1 / UPath(
                    id_ == "textLabel")},
            "CLLManager_Test": {"path": UPath(label_ == "CLLManager Test")},
            "Request_WhenInUse_Authorization": {
                "path": UPath(controller_ == "TSPKToolMockDetailViewController") / UPath(
                    type_ == "UITableView") / UPath(type_ == "UITableViewCell", index=10) / UPath(
                    type_ == "UITableViewLabel")},
            "Request_Location": {
                "path": UPath(controller_ == "TSPKToolMockDetailViewController") / UPath(
                    type_ == "UITableView") / UPath(type_ == "UITableViewCell", index=9) / UPath(
                    type_ == "UITableViewLabel")},
            "Start_Updating_Location": {
                "path": UPath(controller_ == "TSPKToolMockDetailViewController") / UPath(
                    type_ == "UITableView") / UPath(type_ == "UITableViewCell", index=8) / UPath(
                    type_ == "UITableViewLabel")},
            "Start_Monitor_Significant_Location_Changes": {
                "path": UPath(controller_ == "TSPKToolMockDetailViewController") / UPath(
                    type_ == "UITableView") / UPath(type_ == "UITableViewCell", index=7) / UPath(
                    type_ == "UITableViewLabel")},
            "Start_Monitor_For_Region": {
                "path": UPath(controller_ == "TSPKToolMockDetailViewController") / UPath(
                    type_ == "UITableView") / UPath(type_ == "UITableViewCell", index=6) / UPath(
                    type_ == "UITableViewLabel")},
            "Change_Accuracy_To_Coarse": {
                "path": UPath(controller_ == "TSPKToolMockDetailViewController") / UPath(
                    type_ == "UITableView") / UPath(type_ == "UITableViewCell", index=5) / UPath(
                    type_ == "UITableViewLabel")},
            "Change_Accuracy_To_Precise": {
                "path": UPath(controller_ == "TSPKToolMockDetailViewController") / UPath(
                    type_ == "UITableView") / UPath(type_ == "UITableViewCell", index=4) / UPath(
                    type_ == "UITableViewLabel")},
            "Call_In_Background": {
                "path": UPath(id_ == '(textField)')},

            "feed_desc_hashtag": {"type": Control, "path": UPath(label_ == "#qwerty")},
            "feed_nickname": {"type": Control, "path": UPath(id_ == '(nameLabel)')},
            "favorites_nickname": {"type": Control,
                                   "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) / UPath(
                                       id_ == '(nameLabel)', depth=9)},
            "feed_name": {"type": Control, "path": UPath(id_ == '(nameLabel)', index=0)},
            "back_to_feed": {"type": Control,
                             "path": UPath(~label_ == 'Back|Back to previous screen') / UPath(type_ == 'UIImageView')},
            "desc_comment_icon": {"type": Control, "path": UPath(type_ == 'AWEDuetPlayTagView', visible_ == True)},
            "start_watching": {"type": Control, "path": UPath(type_ == 'TUXButton', label_ == 'Start watching')},
            "add_vid": {"type": Control, "path": UPath(type_ == 'TUXNavBar') / 0 / 1 / 0 / UPath(type_ == 'UIImageView')},
            "import_btn": {"type": Control, "path": UPath(type_ == "TUXTextActionSheetItemView", label_ == "Import")},
            "debug_setting": {"type": Control, "path": UPath(id_ == '(endContainerView)', visible_ == True)},
            "import_vid": {"type": Control, "path": UPath(id_ == '(importButton)', visible_ == True)},
            "vid_list": {"type": Control, "path": UPath(type_ == 'TTKFeedDebugTableViewCell')},
            "remove_vid": {"type": Control,
                           "path": UPath(type_ == 'TUXNavBar') / 0 / 1 / 2 / UPath(type_ == 'UIImageView')},
            "nickname": {"type": Control, "path": UPath(type_ == 'AWEUserNameLabel') /
                                                  UPath(type_ == 'UILabel', visible_ == True)},
            "advanced": {"type": Control, "path": UPath(type_ == 'UIButtonLabel', ~label_ == 'Advanced|高级')},
            "settings_enter": {"path": UPath(label_ == 'Settings and privacy')},
            "settings_icon": {"type": Control,
                              "path": UPath(type_ == 'AWENaviBarButtonContainerView', visible_ == True)},
            "settings_icon_from_login_status": {"type": Control, "path": UPath(id_ == '(settingButton)')},
            "create_avatar": {"type": Control, "path": UPath(label_ == 'Welcome to TikTok avatars!')},
            "exit_create_avatar": {"type": Control,
                                   "path": UPath(id_ == '(closeButton)') / UPath(type_ == 'UIImageView')},
            "feed_hashtag": {"type": Control,
                             "path": UPath(type_ == 'AWEPlayInteractionDescriptionLabel', visible_ == True)},
            "feed_playlist_icon": {"type": Control,
                                   "path": UPath(type_ == 'AWEShareRowCell', label_ == 'Add to playlist') / UPath(
                                       type_ == 'UIImageView')},
            "playlist_feed_icon": {"type": Control, "path": UPath(type_ == 'AWEAntiAddictedNoticeBarView')},
            "bottom_progress_bar": {"type": Control, "path": UPath(type_ == 'TTKNewUserProgressBarView')},
            "close_bottom_progress_bar": {"type": Control,
                                          "path": UPath(id_ == '(hideButton)') / UPath(type_ == 'UIImageView')},
            "video_add_to_playlist": {"type": Control, "path": UPath(label_ == 'Add to playlist')},
            "create_playlist": {"type": Control,
                                "path": UPath(type_ == 'TikTokPlayListCreateCell') / UPath(type_ == 'UILabel')},
            "input_playlist_name": {"type": Control, "path": UPath(type_ == 'UITextFieldLabel')},
            "confirm_create": {"type": Control, "path": UPath(id_ == '(Create playlist)')},
            "confirm_add": {"type": Control, "path": UPath(id_ == '(confirmBtn)')},
            "person_page_added_playlist": {"type": Control, "path": UPath(
                type_ == 'TikTokUserProfileEntrancesCollectionView') / 1 / UPath(type_ == 'UILabel')},
            "remove_from_playlist": {"type": Control,
                                     "path": UPath(type_ == 'UITableView') / UPath(text_ == "Remove from playlist")},
            "confirm_remove": {"type": Control, "path": UPath(text_ == 'Remove')},
            "feed_more_icon": {"type": Control,
                               "path": UPath(label_ == 'Share', visible_ == True) / UPath(id_ == '(aweImageView)')},
            "feed_share_icon": {"type": Control, "path": UPath(type_ == 'AWEFeedVideoButton', label_ == 'Share',
                                                               visible_ == True) / UPath(id_ == '(aweImageView)')},
            "add_to_playlist_icon": {"type": Control,
                                     "path": UPath(type_ == 'AWEShareRowCell', label_ == 'Add to playlist') / UPath(
                                         type_ == 'UIImageView')},
            "create_icon": {"type": Control, "path": UPath(label_ == 'Create playlist')},
            "create_panel": {"type": Control, "path": UPath(label_ == 'Name playlist')},
            "feed_video_add": {"type": Control,
                               "path": UPath(controller_ == 'TikTokPlayListAddPanelViewController') / 1},
            "playlist_caption_panel": {"type": Control,
                                       "path": UPath(controller_ == "TikTokPlayListDetailPanelViewController") / 1},
            "feed_playlist_video": {"type": Control, "path": UPath(type_ == 'TTKPlayInteractionBackgroundView')},
            "playlist_caption_title": {"type": Control, "path": UPath(id_ == '(tipsLabel)', visible_ == True)},
            "check_company_information": {"type": Control, "path": UPath(type_ == '_AXUITextViewParagraphElement')},
            "know_and_agree": {"type": Control,
                               "path": UPath(type_ == 'UIButtonLabel', label_ == 'I Know & Agree', visible_ == True)},
            "feed_comment_panel": {"type": Control,
                                   "path": UPath(type_ == 'AWECommentListHeaderView', visible_ == True)},
            "feed_bottom_bar": {"type": Control, "path": UPath(type_ == 'TTKNewUserProgressBarView')},
            "close_bottom_bar": {"type": Control,
                                 "path": UPath(id_ == '(hideButton)') / UPath(type_ == 'UIImageView')},
            "turn_off_bar": {"type": Control,
                             "path": UPath(type_ == 'TUXDialogHighlightBackgroundButton', label_ == 'Turn off')},
            "playlist_add_action": {"type": Control, "path": UPath(label_ == 'Add to playlist')},
            "collection_video": {"type": Control, "path": UPath(type_ == 'AWEPlayInteractionDescriptionLabel', visible_ == True)},
            "login_title": {"type": Control, "path": UPath(type_ == 'UILabel', label_ == 'Log in')},
            "login_back": {"type": Control, "path": UPath(type_ == 'UIImageView', id_ == '(icon_back)')},

            #   Music
            "music_album": {"type": Control, "path": UPath(type_ == 'AWEMusicCoverButton', visible_ == True)},
            "music_info": {"type": Control, "path": UPath(id_ == 'loopContainerView', visible_ == True) / 0 / 0},
            #   Comment_Panel
            "comment_panel_count": {"type": Control, "path": UPath(id_ == 'countLabel', visible_ == True)},
            "comment_add": {"type": Control,
                            "path": UPath(id_ == '(commentInputView)') / UPath(type_ == 'AWETextViewInternal')},
            "comment_add_message": {"type": Control, "path": UPath(id_ == '(commentInputView)') / UPath(
                type_ == '_UITextViewCanvasView', depth=6)},
            "comment_add_text": {"path": UPath(type_ == 'AWEGrowingTextView', visible_ == True, index=-1)},
            "comment_area": {"path": UPath(type_ == "UIVisualEffectView", visible_ == True)},
            "send_comment_btn": {"path": UPath(id_ == "(sendCommentButton)", visible_ == True)},
            "close_comment_section": {"type": Control,
                                      "path": UPath(label_ == 'Close comment section', visible_ == True)},
            "comment_header": {"type": Control, "path": UPath(type_ == "AWECommentListHeaderView", visible_ == True)},
            #   Tab
            "Home": {"type": Control, "path": UPath(~type_ == "AWETabbarGeneralButton|TTKTabBarFeedButton|TTKTabBarButton|TikTokKidsTabBarButtonInnerView",
                ~label_ == "Home|首页|For You|推荐|Refresh") / UPath(~text_ == "Home|首页|For You|推荐|Refresh")},
            "Home_for_language": {"type": Control,"path": UPath(~type_ == "AWETabbarGeneralButton|TTKTabBarButton", visible_ == True, index=-2)},
            "Discover": {"type": Control, "path": UPath(~type_ == "AWETabbarGeneralButton|TTKTabBarAnimationContentView", ~label_ == "Discover|Shop", visible_ == True)},
            "Shoot_video": {"path": UPath(type_ == 'AWETabBarPlusButton')},
            "Inbox": {"type": Control, "path": UPath(~type_ == "AWETabbarGeneralButton|TTKTabBarButton", ~label_ == 'Inbox', visible_ == True)},
            "Inbox_for_language": {"type": Control, "path": UPath(~type_ == "AWETabbarGeneralButton|TTKTabBarButton", visible_ == True, index=-2)},
            "Me": {"type": Control, "path": UPath(~type_ == "AWETabbarGeneralButton|TTKTabBarButton", ~label_ == 'Me|Profile|我|个人资料|个人主页|主页')},
            "Me_for_language": {"type": Control, "path": UPath(~type_ == "UILabel", ~label_ == "Profile",visible_ == True)},
            "Me_Online": {"type": Control, "path": UPath(~type_ == "UILabel|AWETabbarGeneralButton|TTKTabBarButton|TTKTabBarAnimationContentView", ~label_ == 'Me|Profile|我|个人资料|个人主页|主页')},
            "Friends": {"type": Control, "path": UPath( ~type_ == "AWETabbarGeneralButton|TTKTabBarFriendsButton|TTKTabBarAnimationContentView", ~label_ == 'Friends|好友|Now')},
            "Add_Friends": {"type": Control, "path": UPath(type_ == "TTKFriendsNavView") / 0 / UPath(type_ == "UIImageView", visible_ == True)},
            "Add_Friends_2": {"type": Control, "path": UPath(id_ == "friendsNavView") / 0 / UPath(type_ == "UIImageView", visible_ == True)},
            "now_Friends": {"type": Control, "path": UPath(type_ == "TikTokFeedTabItemFriendsControl")},
            "bottom_Friends": {"type": Control, "path": UPath(~type_ == "TTKTabBarFriendsButton|AWETabbarGeneralButton", ~label_ == "Now|Shop")},
            #   change_region_button
            "region_change": {"path": UPath(type_ == 'TIKTOKSwitchRegionButton') / UPath(type_ == 'UIButtonLabel')},
            #   search
            "search_entrance": {"type": Control, "path": UPath(~type_ == "TTKSearchEntranceButton|TTKSearchEntranceBar|AWESearchBar")},
            "search_entrance_friends": {"type": Control, "path": UPath(id_ == "secondRightItemButton")},
            "search_entrance_friends_spare": {"type": Control, "path": UPath(type_ == "TTKFriendsNavView") / 1},
            #   live audience
            "live_audience_entrance": {"type": Control, "path": UPath(type_ == 'AWELiveFeedEntranceView')},
            "share_live": {"type": Control, "path": UPath(id_ == "icon_ttlive_share_new")},
            "friends_row": {"type": Control, "path": UPath(controller_ == "AWEIMTranspondListViewController") / UPath(
                type_ == "UICollectionView")},
            "more_btn": {"type": Control, "path": UPath(type_ == "AWEIMTranspondListMoreCollectionViewCell") / UPath(
                type_ == "UIImageView")},
            "friend_search_bar": {"type": Control, "path": UPath(id_ == "textField")},
            "exit_live_room_btn": {"type": Control,
                                   "path": UPath(type_ == "HTSLive4LayerContainerView") / UPath(type_ == "UIButton",
                                                                                                visible_ == True) / UPath(
                                       type_ == "UIImageView")},
            "select_friend_btn": {"type": Control,
                                  "path": UPath(type_ == "AWEIMDirectTranspondUserCell", visible_ == True) / 0 / 1},
            "send_to_friend_btn": {"type": Control, "path": UPath(id_ == "shareButton")},
            "live_user_name": {
                "path": UPath(type_ == "IESLiveMTUserProfileView") / UPath(id_ == "nameLabel", type_ == "UILabel")},

            "switch_account": {"path": UPath(type_ == 'AWESwitchAccountNavigationBarTitleView')},
            "switch_account_1": {"path": UPath(label_ == "Switch accounts")},
            "add_account": {"path": UPath(type_ == 'AWENewAccountPopoverTableViewCell') /
                                    UPath(type_ == 'UITableViewCellContentView')},
            "add_account_1": {
                "path": UPath(type_ == "XCUIElementTypeTable") / 3 / UPath(type_ == "XCUIElementTypeImage")},
            "compare_test": {"path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) /
                                     UPath(type_ == 'ACCStickerContainerView') / 0},
            "live_panel": {"type": Control, "path": UPath(type_ == "IESLiveMTRoomSlideCell")},
            "long_press_panel": {"type": Control, "path": UPath(controller_ == "AWEIMLongPressViewController")},


            # 儿童模式：登录儿童账号后，控件名不同
            "kids_shoot_video": {"path": UPath(type_ == 'TikTokKidsTabBarButton') / 2},
            "kids_me": {"path": UPath(type_ == 'TikTokKidsTabBarButton') / 4},
            "kids_home": {"path": UPath(type_ == 'TikTokKidsTabBarButton') / 2},
            "kids_draft": {"path": UPath(id_ == "workCollectionView") / 0},

            "comment_list_panel": {"path": UPath(id_ == '(baseView)')},

            # find friends
            "friend_list": {"path": UPath(id_ == "tableView", visible_ == True)},

            # poi
            "poi_anchor_icon": {"path": UPath(id_ == '(panelScrollContentView)') / 0 / UPath(type_ == 'UIImageView',
                                                                                             visible_ == True)},
            "poi_anchor_title": {"path": UPath(id_ == '(mainTitleLabel)', visible_ == True)},
            "like_poi_title": {"path": UPath(id_ == "(mainTitleLabel)")},
            "poi_anchor_view": {"path": UPath(id_ == "(anchorView)", visible_ == True)},
            "poi_comments": {"path": UPath(label_ == "Comments", visible_ == True)},
            "comments_poi_icon": {"path": UPath(type_ == "TTKCommentCaptionAnchorSubView")},
            "close_poi_panel": {"path": UPath(id_ == "(closeBtn)")},
            "poi_anchor_midtitle": {"path": UPath(id_ == '(midTitleLabel)', visible_ == True)},
            "profile_poi_anchor_midtitle": {
                "path": UPath(type_ == 'TTKStoryInteractionBubbleCellContainerView', visible_ == True)},
            "poi_anchor_subtitle": {
                "path": UPath(type_ == 'TikTokFeedAnchorView') / UPath(id_ == '(subTitleLabel)', visible_ == True)},
            "following_poi_anchor": {"path": UPath(type_ == 'BDImageView', visible_ == True)},
            "add_to_favorites": {"path": UPath(type_ == 'UIButtonLabel', label_ == 'Add to Favorites')},
            "remove_to_favorites": {"path": UPath(text_ == 'Added to Favorites')},
            "poi_detail_page_map_icon": {"path": UPath(id_ == '(poiDetailNameLabel)')},
            "map_navigation": {"path": UPath(id_ == '(naviButtonBackgroundView)')},
            "feed_poi_comment": {"path": UPath(id_ == '(AWEPlayInteractionCommentElement)', visible_ == True) / UPath(
                type_ == 'AWEFeedVideoButton')},
            "comment_poi_icon": {"type": Control,
                                 "path": UPath(type_ == 'TTKCommentAnchorView') / UPath(type_ == 'BDImageView')},
            "detail_poi_name": {"path": UPath(id_ == '(poiNameLabel)')},
            "activity_entrance": {"type": Control, "path": UPath(id_ == "(All activity)", visible_ == True)},
            "choose_activity": {"type": Control, "path": UPath(id_ == "(titleView)", text_ == "All activity")},
            "activities": {"type": Control, "path": UPath(type_ == "TTKInboxNewActivityHeaderView", visible_ == True)},
            "poi_panel_anchor_icon": {"type": Control,
                                      "path": UPath(type_ == 'TikTokFeedMultiAnchorPanelPOIElementView') / UPath(
                                          type_ == 'UIImageView')},
            "poi_panel_anchor_text": {"type": Control, "path": UPath(id_ == 'mainContainerView', visible_ == True)},
            "poi_panel_city_name": {"type": Control, "path": UPath(id_ == 'subTagContainerView', visible_ == True)},


            # poi more
            "poi_icon_more": {"path": UPath(label_ == '(2)', visible_ == True)},
            "profile_multiple_anchor": {"path": UPath(type_ == 'TTKCommentBubbleCaptionCell') / UPath(
                type_ == 'TTKStoryInteractionBubbleCellContainerView')},
            "poi_select_more": {"path": UPath(type_ == 'TikTokFeedAnchorPanelView')},
            "poi_anchor_icon_more": {
                "path": UPath(id_ == '(panelScrollContentView)') / 0 / UPath(type_ == 'UIImageView',
                                                                             visible_ == True)},
            "poi_anchor_title_more": {
                "path": UPath(id_ == '(panelScrollContentView)') / 0 / UPath(id_ == '(titleLabel)', visible_ == True)},
            "poi_anchor_subtitle_more": {
                "path": UPath(id_ == '(panelScrollContentView)') / 0 / UPath(id_ == '(subtitleLabel)',
                                                                             visible_ == True)},
            "poi_anchor_cancel": {"path": UPath(type_ == 'TikTokAnchorPanelCloseButton', visible_ == True)},

            # poi comment
            "poi_anchor_comment": {
                "path": UPath(~type_ == 'TTKCommentCaptionAnchorSubView|AWECommentListHeaderView', visible_ == True,
                              index=0)},
            "poi_anchor_icon_comment": {
                "path": UPath(~type_ == 'TTKCommentAnchorView|TTKCommentCaptionAnchorSubView') / UPath(
                    type_ == 'BDImageView') / 0},
            "poi_name_comment": {"path": UPath(id_ == 'poi_name', visible_ == True)},
            "poi_anchor_title_comment": {
                "path": UPath(type_ == 'TTKCommentAnchorView') / UPath(id_ == '(mainTitleLabel)', visible_ == True)},
            "poi_anchor_subtitle_comment": {
                "path": UPath(type_ == 'TTKCommentAnchorView') / UPath(id_ == '(subTitleLabel)', visible_ == True)},
            "poi_comment_panel": {"type": Control, "path": UPath(type_ == '_UITextViewCanvasView')},
            "poi_anchor_icon_comment_v2": {
                "path": UPath(type_ == "TTKCommentCaptionAnchorSubView") / UPath(id_ == "(imageView)")},
            "poi_anchor_title_comment_v2": {
                "path": UPath(type_ == "TTKCommentCaptionAnchorSubView") / UPath(id_ == "(mainTitleLabel)")},
            "poi_anchor_subtitle_comment_v2": {
                "path": UPath(type_ == "TTKCommentCaptionAnchorSubView") / UPath(id_ == "(subTitleLabel)")},
            "search_bar": {"path": UPath(type_ == 'AWESearchBar')},
            "change_default_head": {"type": Control,
                                    "path": UPath(id_ == 'challengeDetailDefaultHead', visible_ == True)},
            "search_content_list": {"type": Control, "path": UPath(id_ == "contentScrollView", visible_ == True)},

            # exploring
            "feed_favourite_icon": {"type": Control,
                                    "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) / UPath(
                                        label_ == 'Favorites', depth=6)},
            "feed_like_icon": {"type": Control, "path": UPath(type_ == 'TTKFeedInteractionMainView', index=0) / UPath(
                id_ == 'icon_home_like_before', visible_ == True)},
            "feed_comment_icon": {"type": Control,
                                  "path": UPath(type_ == 'TTKFeedInteractionMainView', index=0) / UPath(
                                      id_ == 'icon_home_comment', visible_ == True)},
            "feed_share_icon_v1": {"type": Control,
                                   "path": UPath(type_ == 'TTKFeedInteractionMainView', index=0) / UPath(
                                       id_ == 'iconHomeShareRight', visible_ == True)},
            "feed_share_icon_v2": {"type": Control, "path": UPath(type_ == 'AWEFeedVideoButton', label_ == 'Share')},
            "public_settings": {"type": Control, "path": UPath(id_ == 'switchWellImageViewContainer')},
            "panel_add_to_favourites": {"type": Control,
                                        "path": UPath(type_ == 'UILabel', label_ == 'Add to Favorites')},
            "share_panel_collection_remove": {"type": Control,
                                              "path": UPath(type_ == 'UILabel', label_ == 'Remove from Favorites')},
            "share_panel_collection_add": {"type": Control,
                                           "path": UPath(type_ == 'UILabel', label_ == 'Add to Favorites')},
            # "feed_user_icon": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True)/UPath(type_ == 'TTKStoryAvatarView', depth=8)},
            "follow_text": {"type": Control, "path": UPath(id_ == 'sendMsgTitleBtn', visible_ == True)},
            "username": {"path": UPath(id_ == '(nameLabel)', visible_ == True, label_ == '@12341833173qqq')},
            # poi detail
            "poi_detail_header": {"path": UPath(
                ~type_ == 'TTKPOIDetailHeaderView|TTKPOINewDetailHeaderView|TTKPOIDetailHeaderPOINameComponentView',
                visible_ == True)},
            "poi_detail_title": {"path": UPath(~id_ == '(poiDetailNameLabel)|(poiNameLabel)', visible_ == True)},
            "poi_map_title": {
                "path": UPath(type_ == "UITableViewCellContentView") / UPath(id_ == "(label)", visible_ == True)},
            "poi_map_title_v2": {
                "path": UPath(type_ == "UITableView") / 0 / UPath(type_ == "UITableViewCellContentView", depth=18)},
            "poi_map_text": {"path": UPath(id_ == "(label)", visible_ == True)},
            "poi_map_text_v2": {"path": UPath(id_ == "(contentTable)") / 0 / UPath(id_ == "(label)")},
            "poi_red_point": {"path": UPath(controller_ == 'TTKPOIDetailMapViewController')},
            "poi_map_view": {"path": UPath(controller_ == "TTKPOIDetailMapViewController") / 0},
            "poi_sign_icon": {"path": UPath(id_ == 'naviButtonBackgroundView', visible_ == True)},
            "get_directions": {"path": UPath(controller_ == 'TUXContainerViewController') / 1},
            "copy_address": {"path": UPath(type_ == 'UIButtonLabel', label_ == 'Copy address')},
            "comment_close": {"type": Control, "path": UPath(label_ == 'Close comment section')},
            "maps": {"path": UPath(id_ == "(Apple Maps)", type_ == "TUXTextActionSheetItemView")},
            "delete": {"path": UPath(type_ == 'AWEShareRowCell', label_ == 'Delete', visible_ == True)},
            "privacy": {"path": UPath(type_ == 'AWEShareRowCell', label_ == 'Privacy settings', visible_ == True)},
            "delete_btn": {"path": UPath(~text_ == 'Delete|删除')},
            "action_list": {"type": Action_list, "path": UPath(id_ == '(secondRowView)')},
            "action_list_area": {"type": Control, "path": UPath(id_ == '(secondRowView)')},

            "debug_tool": {"path": UPath(id_ == '(TTKFeedDebugElement)', visible_ == True)},
            "add_to_feed_btn": {"path": UPath(type_ == 'UIButtonLabel', label_ == 'Add to feed')},
            "cdu_002": {"path": UPath(type_ == 'AWEIMTranspondListCollectionViewCell', label_ == 'cduiauto002') / 0},
            "cdu_001": {"path": UPath(type_ == 'AWEIMTranspondListCollectionViewCell', label_ == 'cduiauto001') / 0},
            "send_btn": {"path": UPath(text_ == 'Send')},
            "send_success": {"path": UPath(type_ == 'AWEAntiAddictedNoticeBarView')},
            "close_language_understand": {"path": UPath(type_ == 'UIButton', id_ == '(closeButton)')},
            "follow_your_friends_close_btn": {
                "path": UPath(controller_ == "TTKUserSuggestionPopupContainer") / UPath(type_ == "UIView",
                                                                                        index=0) / UPath(
                    type_ == "AWEButton", index=1)},
            "recommend_card_title": {"path": UPath(label_ == 'Trending creators')},
            'recommend_card_desc': {'path': UPath(label_ == 'Follow an account to see their latest videos here.')},
            'recommend_card_photo': {'path': UPath(type_ == 'YYAnimatedImageView', visible_ == True, index=0)},
            'recommend_card_nicky_name': {'path': UPath(id_ == "nameLabel", visible_ == True, index=0)},
            'recommend_card_real_name': {'path': UPath(id_ == "usernameLabel", visible_ == True, index=0)},
            'recommend_card_follow_btn': {'path': UPath(id_ == "relationButton", visible_ == True, index=0)},
            'recommend_card_close_btn': {'path': UPath(id_ == "super_accounts_close", visible_ == True, index=0)},
            "follow_btn_1": {"path": UPath(type_ == 'LOTAnimationView', visible_ == True)},
            "living_list": {"path": UPath(label_ == 'TTKFeedLiveSkylightComponent')},
            "profile_content": {"path": UPath(type_ == 'AWEPlayInteractionDescriptionLabel', visible_ == True)},
            "feed_creator_name_1": {
                "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) / UPath(type_ == 'AWEUserNameLabel',
                                                                                    depth=8) / UPath(
                    type_ == 'UILabel', visible_ == True)},
            "comment_username_label": {
                "path": UPath(type_ == 'UITableView') / 1 / UPath(type_ == 'AWEUserNameLabel', visible_ == True)},
            "comment_user_photo": {
                "path": UPath(type_ == 'UITableView') / 1 / UPath(type_ == 'UITableViewCellContentView') / 2},
            "profile_comment_poi_icon": {
                "path": UPath(type_ == 'TTKCommentCaptionAnchorSubView') / UPath(type_ == 'UIView')},
            "back_like_tab": {"path": UPath(label_ == 'returnButton', visible_ == True)},
            "back_inbox_tab": {"path": UPath(label_ == 'Back', visible_ == True)},
            # realtion_feed
            "relation_label": {"type": RelationLabel,
                               "path": UPath(type_ == 'AWEFeedHybridTagView', visible_ == True, index=0)},
            "relation_bth": {"type": RelationButton, "path": UPath(type_ == 'TTKFeedPassthroughStackView',
                                                                   id_ == '(TTKFeedInteractionLeftContainerElement)',
                                                                   visible_ == True, index=0)},
            "relation_feed_avatar_name": {
                "path": UPath(type_ == 'AWEPlayInteractionUserAvatarView', visible_ == True) / UPath(
                    id_ == '(userAvatarView)', type_ == 'AWEAdAvatarView', visible_ == True
                    , index=0)},
            "relation_feed_avatar_name_new": {
                "path": UPath(type_ == 'AWEPlayInteractionUserAvatarView', visible_ == True) / UPath(
                    id_ == 'contentView', type_ == 'AWEStoryAvatarButton', visible_ == True
                    , index=0)},

            "feed_pass_view": {"type": FeedPassStackView, "path": UPath(type_ == 'TTKFeedPassthroughStackView',
                                                                        id_ == '(TTKFeedInteractionLeftContainerElement)',
                                                                        visible_ == True, index=0)},
            "music_cover": {"type": Control, "path": UPath(id_ == 'music_cover', visible_ == True)},
            "music_post_title": {"type": Control, "path": UPath(label_ == 'Use this sound', visible_ == True)},
            "play_on_cover": {"path": UPath(id_ == '(playImageView)', visible_ == True)},
            "save_video": {"path": UPath(label_ == 'Save video', id_ == 'titleLabel')},
            "debug_hashtag_text": {"path": UPath(label_ == '#qwerty', visible_ == True)},
            "feed_debug_desc": {"path": UPath(id_ == '(AWEPlayInteractionDescriptionElement)', visible_ == True)},


            # pop up
            "pop_up": {"path": UPath(id_ == "messageLabel", visible_==True, type_=="TUXLabel", index=0)},
            "story_ring": {"type": Control, "path": UPath(id_ == '(pureColorBorder)', visible_ == True)},
            # following tab tips view
            "following_tips_view": {
                "path": UPath(type_ == 'TikTokFeedFollowTipsView', id_ == '(tipsView)', visible_ == True) / UPath(
                    type_ == 'UILab', visible_ == True)},
            "friends_feed_tab": {"type": Control, "path": UPath(type_ == 'TikTokFeedTabItemFriendsControl')},
            "friends_tab_btn": {"type": Control, "path": UPath(type_ == 'TTKTabBarFriendsButton', visible_ == True)},
            "friends_tab_red_dot": {"root": "friends_tab_btn", "path": UPath(type_ == 'TTKUIViewUnreadDotView')},
            "comment_count1": {"type": Control,
                               "path": UPath(id_ == 'AWEPlayInteractionCommentElement', visible_ == True) / UPath(
                                   type_ == 'UILabel')},
            "comment_mention_panel": {"type": Control, "path": UPath(controller_ == 'AWECommentSearchViewController',
                                                                     visible_ == True)},
            "add_comment_input": {"type": TextEdit,
                                  "path": UPath(id_ == '(commentInputView)') / UPath(type_ == 'AWETextViewInternal')},

            "repost_button": {"type": Control, "path": UPath(id_ == "upvoteButton")},
            "repost_section": {"type": Control, "path": UPath(type_ == "TTKUpvoteBubbleElement")},

            # repost panel
            "repost_panel_btn": {
                "path": UPath(controller_ == "TTKUpvoteV2GuideViewController") /
                        UPath(type_ == "UIStackView") /
                        UPath(type_ == "TUXButton") /
                        UPath(type_ == "UIButtonLabel"),
            },

            # repost bubble
            "repost_bubble": {
                "path": UPath(controller_ == "AWENewFeedTableViewController") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "AWENewFeedTableView") /
                        UPath(type_ == "AWEFeedViewCell") /
                        UPath(type_ == "UITableViewCellContentView") /
                        UPath(type_ == "TTKFeedInteractionRootView") /
                        UPath(type_ == "TTKFeedInteractionMainView") /
                        UPath(type_ == "TTKFeedPassthroughStackView") /
                        UPath(type_ == "TTKFeedPassthroughView") /
                        UPath(type_ == "TTKUpvoteBubbleControllerView") /
                        UPath(type_ == "TTKUpvoteBubbleScrollerView") /
                        UPath(type_ == "TTKUpvoteBubbleElement") /
                        UPath(type_ == "UIButton", label_ == "Repost"),
            },
            "you_reposted_bubble": {
                "path": UPath(controller_ == "AWENewFeedTableViewController") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "AWENewFeedTableView") /
                        UPath(type_ == "AWEFeedViewCell") /
                        UPath(type_ == "UITableViewCellContentView") /
                        UPath(type_ == "TTKFeedInteractionMainView") /
                        UPath(type_ == "TTKFeedPassthroughStackView") /
                        UPath(type_ == "TTKFeedPassthroughView") /
                        UPath(type_ == "TTKUpvoteBubbleControllerView") /
                        UPath(type_ == "TTKUpvoteBubbleElement") /
                        UPath(type_ == "TTKUpvoteBubbleElementWhoRepostedView") /
                        UPath(type_ == "UILabel", text_ == "You reposted"),
            },
            "add_comment_bubble": {
                "path": UPath(controller_ == "AWENewFeedTableViewController") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "AWENewFeedTableView") /
                        UPath(type_ == "AWEFeedViewCell") /
                        UPath(type_ == "UITableViewCellContentView") /
                        UPath(type_ == "TTKFeedInteractionRootView") /
                        UPath(type_ == "TTKFeedInteractionMainView") /
                        UPath(type_ == "TTKFeedPassthroughStackView") /
                        UPath(type_ == "TTKFeedPassthroughView") /
                        UPath(type_ == "TTKUpvoteBubbleElement") /
                        UPath(type_ == "UIButtonLabel", text_ == "Add comment"),
            },
            "repost_comment_separator": {
                "path": UPath(controller_ == "AWECommentListViewController") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UIScrollView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UITableView") /
                        UPath(type_ == "TTKCommentPanelUpvoteLoadMoreCell") /
                        UPath(type_ == "UITableViewCellContentView"),
            },
            "remove_repost_btn": {
                "path": UPath(type_ == "AWEUIAlertView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UIView") /
                        UPath(type_ == "UIButtonLabel", text_ == "Remove"),
            },
            "live_now_tag": {"path": UPath(text_ == "LIVE now")},
            "story_tag": {
                "path": UPath(controller_ == "TTKStoryContainerViewController") /
                        UPath(text_ == "Story")
            },
            "sponsored_tag": {"path": UPath(text_ == "Sponsored", type_ == "UILabel", visible_ == True)},
            # comment
            'at_btn': {'path': UPath(~id_ == "(atBtn|IconAt)", visible_ == True, index=1)},
            'emotion_btn': {'path': UPath(id_ == "(emoticonButton)", visible_ == True)},
            'add_comment': {'path': UPath(type_ == "AWEGrowingTextView", label_ == "Add comment...")},
            'see_more': {'path': UPath(type_ == 'AWEPlayInteractionDescriptionTailLabel', ~label_ == "... See more|... more", index=0)},
            'comment_area_mode': {'path': UPath(type_ == 'AWEElementStackView')},
            'comment_profile_icon': {
                'path': UPath(type_ == "AWECommentListInputView") / UPath(type_ == "UIStackView", index=0) / UPath(
                    type_ == "UIView", index=0) / UPath(type_ == "UIImageView")},
            'comment_emotion': {'path': UPath(id_ == "(emoticonButton)", visible_ == True)},
            'comment_views': {'path': UPath(type_ == "TTKBottomVideoViewHistoryView")},
            'comment_views_btn': {'path': UPath(id_ == "(selectViewReference)")},

            'comment_oo2': {'path': UPath(text_ == "cduiauto002")},
            'repost_confirm': {'path': UPath(type_ == "TUXButton") / UPath(id_ == "(Repost)")},
            "close_anchor_panel": {'path': UPath(type_ == 'TikTokAnchorPanelCloseButton')},
            "comment_num": {"type": Control,
                            "path": UPath(label_ == 'Comments', visible_ == True) / UPath(type_ == 'UILabel')},
            "comment_edit_input": {"type": Control, "path": UPath(type_ == 'AWEGrowingTextView', visible_ == True)},
            "comment_send_btn": {"type": Control, "path": UPath(id_ == 'sendCommentButton', visible_ == True)},
            "comment_back_btn": {"type": Control,
                                 "path": UPath(label_ == 'Close comment section') / UPath(type_ == 'UIImageView')},
            "comment_detail_num": {"type": Control, "path": UPath(type_ == 'TTKCommentHeaderTabView', index=0) / UPath(
                id_ == 'tabLabel', visible_ == True)},
            "first_comment": {"type": Control, "path": UPath(type_ == 'UITableView') / 3},
            "feed_follow_btn": {"type": Control, "path": UPath(type_ == 'LOTAnimationView', visible_ == True)},
            "small_follow_btn": {"type": Control, "path": UPath(id_ == 'follow_plus_small', visible_ == True)},
            "report_btn": {"type": Control, "path": UPath(text_ == 'Report', visible_ == True)},
            "report_page": {"type": Control,
                            "path": UPath(controller_ == 'TTKReportSparkViewController', visible_ == True)},
            "see_more_btn": {"type": Control,
                             "path": UPath(type_ == 'AWEPlayInteractionDescriptionTailLabel', visible_ == True)},
            "hide_btn": {"type": Control, "path": UPath(~label_ == 'Hide|less', visible_ == True)},
            "panel_page": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True)},
            "pause_icon": {"type": Control, "path": UPath(type_ == 'AWEAwemePlayVideoPauseIcon', visible_ == True)},
            "icon_music_play": {"type": Control,
                                "path": UPath(type_ == 'TTKMusicDetailBasicInfoView', visible_ == True)},
            "feed_music_cover": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) / UPath(
                id_ == 'AWEPlayInteractionMusicInfoElement')},
            "not_interest": {"type": Control, "path": UPath(type_ == 'UILabel', label_ == 'Not interested')},
            "not_interest_toast": {"type": Control, "path": UPath(
                ~label_ == 'You can press and hold the video to dislike it|We\'ll show fewer videos like this.')},
            "feed_right_icon": {"type": Control, "path": UPath(label_ == 'right', visible_ == True)},
            "clear_mode": {"type": Control, "path": UPath(~text_ == 'Clear mode|Clear display', visible_ == True)},
            "search_top": {"type": Control, "path": UPath(label_ == 'Top', visible_ == True)},
            "search_back_btn": {"type": Control, "path": UPath(controller_ == 'AWESearchViewController')/1/UPath(type_ == 'UIImageView')},
            "feed_text": {"type": Control, "path": UPath(text_ == 'For You', visible_ == True)},
            "hashtag_icon": {"type": Control, "path": UPath(id_ == 'challengeDetailDefaultHead')},
            "search_feed_bar": {"type": Control, "path": UPath(type_ == 'TTKFeedBaseBottomBarView')},
            # "one_second_ago": {"path": UPath(label_ == '· 1s ago')},
            "ppe_lane_input": {"type": Control, "path": UPath(type_ == '_UIAlertControllerTextField')},
            "confirm": {"type": Control, "path": UPath(text_ == 'Confirm')},
            "one_second_ago": {"path": UPath(label_ == '· 1s ago')},
            "follow_back": {"path": UPath(type_ == 'TTKRelationButton', label_ == 'Follow')},
            "nation_word": {"path": UPath(type_ == "UITableView") / UPath(type_ == "AWECommentPanelCell", index=1) / UPath(type_ == "YYLabel", visible_ == True)},
            "more_friends": {'path': UPath(text_ == "More friends")},
            "undo": {'path': UPath(id_ == "rightCustomView", text_ == "Undo")},
            "effect_detail_title": {"type": Control,
                                    "path": UPath(type_ == 'AWEStickerDetailView') / UPath(id_ == 'titleLabel')},
            "movie_anchor_icon": {"type": Control,
                                  "path": UPath(type_ == 'TTKTopicSingleAnchorCustomView', visible_ == True)},
            "movie_photo": {"type": Control,
                            "path": UPath(id_ == 'movieCoverConatinerView') / UPath(type_ == 'UIImageView')},
            "movie_header": {"type": Control, "path": UPath(type_ == 'TTKMovieTokDetailHeaderView', visible_ == True)},
            "book_anchor": {"type": Control,
                            "path": UPath(type_ == 'TTKTopicSingleAnchorCustomView', visible_ == True)},
            "book_anchor_icon": {"type": Control, "path": UPath(type_ == 'TTKTopicSingleAnchorCustomView') / UPath(
                type_ == 'UIImageView', visible_ == True)},
            "book_photo": {"type": Control, "path": UPath(label_ == 'Tap to rate this book', visible_ == True)},
            "playlist_bar": {"type": Control, "path": UPath(type_ == 'TTKFeedPlaylistBarView', visible_ == True)},
            "playlist_bar_view": {"type": Control, "path": UPath(type_ == 'TTKFeedPlaylistBarView') / 0},
            "playlist_detail_cell": {"type": Control,
                                     "path": UPath(type_ == 'TikTokPlayListDetailCell', visible_ == True)},
            "play_next_btn": {"type": Control, "path": UPath(id_ == 'Play next', visible_ == True)},
            "feed_playlist_name": {"type": Control, "path": UPath(id_ == 'tipsLabel', visible_ == True)},
            "playlist_return_btn": {"type": Control,
                                    "path": UPath(label_ == 'returnButton') / UPath(type_ == 'UIImageView',
                                                                                    visible_ == True)},
            "trending_banner": {"type": Control, "path": UPath(
                ~type_ == 'TikTokTrendsSwiftImpl.TTKSwiftTrendingBarView|TTKFeedTrendingBarView|TTKSwiftTrendingBarView',
                visible_ == True)},
            "flame_icon": {"type": Control, "path": UPath(
                ~type_ == 'TikTokTrendsSwiftImpl.TTKSwiftTrendingBarView|TTKFeedTrendingBarView|TTKSwiftTrendingBarView') / 0},
            "fixed_allow": {"type": Control, "path": UPath(
                ~type_ == 'TikTokTrendsSwiftImpl.TTKSwiftTrendingBarView|TTKFeedTrendingBarView|TTKSwiftTrendingBarView') / 1},
            "rank_info": {"type": Control,
                          "path": UPath(type_ == 'TikTokTrendsSwiftImpl.TTKSwiftTrendingBarView') / 5},
            "trending_text": {"type": Control, "path": UPath(
                ~type_ == 'TikTokTrendsSwiftImpl.TTKSwiftTrendingBarView|TTKFeedTrendingBarView|TTKSwiftTrendingBarView') / 4},
            "feed_interaction": {"type": Control, "path": UPath(type_ == "TTKFeedInteractionRootView", index=0)},
            "feed_comment_num": {"type": Control, "root": "feed_current",
                                 "path": UPath(id_ == 'AWEPlayInteractionCommentElement') / UPath(type_ == 'UILabel')},

            # qna
            "qna_desc": {"type": Control, "path": UPath(type_ == "AWEDuetPlayTagView")},
            "qna_banner": {"type": Control, "path": UPath(type_ == "AWEAntiAddictedNoticeBarView")},
            "feed_username": {"type": Control, "path": UPath(type_ == "AWEUserNameLabel", visible_ == True) / UPath(
                id_ == "nameLabel")},
            # photomode
            "photomode_title": {"type": Control, "path": UPath(id_ == "AWEPlayInteractionTitleElement") / 0},
            # like
            "like_icon_white": {"type": Control, "path": UPath(type_ == "AWEFeedViewCell", visible_ == True) / UPath(
                id_ == "icon_home_like_before", depth=8)},
            "like_icon_red": {"type": Control, "path": UPath(type_ == "AWEFeedViewCell", visible_ == True) / UPath(
                id_ == "icon_home_like_after", depth=8)},
            "ad_ui": {"type": Control, "path": UPath(type_ == 'AWEAdLearnMoreView', visible_ == True)},
            "ad_nickname_jumping": {"type": Control,
                                    "path": UPath(type_ == 'AWENavigationBar') / UPath(id_ == 'contentView')},
            "ad_nickname": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) / UPath(
                type_ == 'AWEUserNameLabel', depth=8)},
            "advertisement_nickname": {"type": Control, "path": UPath(id_ == "nameLabel", ~type_ == "TUXLabel|UILabel",
                                                                      visible_ == True)},
            "ad_card": {"type": Control, "path": UPath(id_ == "lynxView") / 1},
            "ad_card_panel": {"type": Control,
                              "path": UPath(type_ == 'AWENavigationBar') / UPath(id_ == 'contentView')},
            "feed_debug_cell": {"type": Control,
                                "path": UPath(type_ == 'TTKFeedDebugTableViewCell', visible_ == True)},
            "debug_remove_video": {"type": Control, "path": UPath(type_ == "UITableViewCellEditControl", visible_ == True)},
            "shop_now": {"type": Control, "path": UPath(type_ == 'LynxTextView', label_ == 'Shop now', visible_ == True)},
            "ad_card_view": {"type": Control, "path": UPath(type_ == 'AWEAdLynxCardView', visible_ == True)},
            "ad_guide": {"type": Control, "path": UPath(type_ == 'AWEAdOperationGuideView', visible_ == True)},
            "learn_more_btn": {"type": Control, "path": UPath(id_ == 'learnMoreBtn', visible_ == True)},
            "ad_user_icon": {"type": Control, "path": UPath(type_ == 'AWEFeedViewCell', visible_ == True) / UPath(
                type_ == 'TTKStoryAvatarView', depth=8)},
            "user_profile_collection": {"type": Control,
                                        "path": UPath(type_ == 'TTKUserProfileWorkCollectionView', visible_ == True)},
            "ad_open_guide": {"type": Control, "path": UPath(type_ == 'AWENewAdOpenGuideView', visible_ == True)},
            "ad_web_view": {"type": Control, "path": UPath(type_ == 'TTKAdsWebViewRuntime', visible_ == True)},
            "tt_feed_anchor": {"type": Control,
                               "path": UPath(label_ == 'A A A A A A a a a a a a a a a a a a a a Aaba Persianas ',
                                             visible_ == True)},
            # "multi_anchor_sub": {"type": Control, "path": UPath(id_ == 'multiAnchorSubview', visible_ == True)},

            # Event
            "send": {"type": Control, "path": UPath(text_ == "Send")},
            "feed_poi_contrl": {"type": Control, "path": UPath(type_ == "TTKPOIFeedAnchorView")},
            "desc": {"type": Control, "path": UPath(type_ == "AWEFeedViewCell", visible_ == True) / UPath(
                type_ == "AWEPlayInteractionDescriptionLabel", depth=6)},
            "share_button_list_v2": {"type": Control, "path": UPath(controller_ == 'AWESharePanelController') / 1},

            "ice_pop_up": {
                "path": UPath(id_ == "messageLabel", text_ == "Sent to mitten_test_5422", visible_ == True)},
            "live_long_pop": {"path": UPath(type_ == "IESLiveMTLongPressMenuView") / UPath(id_ == "contentView")},
            "live_bottom_tab": {"path": UPath(type_ == "GBLToolbarView")},
            "send_to_panel": {"type": Control,
                              "path": UPath(id_ == "collectionContainer") / UPath(id_ == "collectionView")},
            "more_cell": {"type": Control, "path": UPath(type_ == "AWEIMTranspondListMoreCollectionViewCell")},
            "share_btn": {"type": Control, "path": UPath(id_ == "feedShareButton", visible_ == True)},

            # Save photo btn text on share panel - forPhotoMode
            "save_txt": {"type": Control, "path": UPath(text_ == 'Save photo')},

            # must show components
            "tns_warning_banner": {"type": Control, "path": UPath(id_ == "warningLabel")},
            "tns_covid_banner": {"type": Control, "path": UPath(text_ == "Learn more about COVID-19 vaccines")},
            "covid_banner_detail_page": {"type": Control,
                                         "path": UPath(text_ == "MOH COVID-19 Vaccination Registration")},
            "ecommerce_anchor": {"type": Control, "path": UPath(type_ == "TTKECFeedCustomAnchorView")},
            "ecommerce_anchor_detail_page": {"type": Control, "path": UPath(type_ == "TTKECPdpContainerView")},
            "paid_collection_anchor": {"type": Control, "path": UPath(type_ == "TTKPaidContentFeedAnchorView")},
            "paid_collection_anchor_detail_page": {"type": Control, "path": UPath(
                controller_ == "TTKPaidContentCollectionDetailContainerViewController")},
            "tcm_comment_anchor": {"type": Control, "path": UPath(text_ == "test_VIPSHOP | Shop like a VIP")},
            "anchor_detail_page": {"type": Control, "path": UPath(type_ == "HybridLynxView")},
            "comment_emoji": {"type": Control, "path": UPath(id_ == "😁", type_ == "UIButtonLabel")},
            "send_comment": {"type": Control,
                             "path": UPath(id_ == "sendCommentButton") / UPath(type_ == "UIImageView")},
            "tcm_vertical_solution_game_anchor": {"type": Control, "path": UPath(text_ == "Honkai: Star Rail")},
            "ug_pick_resso_anchor": {"type": Control, "path": UPath(text_ == "Resso ")},
            "resso_anchor_detail_page": {"type": Control, "path": UPath(text_ == "Go to Resso")},
            "ttm_playlist_anchor": {"type": Control, "path": UPath(text_ == "Playlist ")},
            "ttm_playlist_anchor_detail_page": {"type": Control,
                                                "path": UPath(text_ == "Play full playlist with Resso")},
            "ba_leadgen_anchor": {"type": Control, "path": UPath(text_ == "Get quote")},
            "capcut_anchor": {"type": Control, "path": UPath(text_ == "CapCut ")},
            "effect_anchor": {"type": Control, "path": UPath(text_ == "Super Cute Snail")},
            "effect_anchor_detail_page": {"type": Control,
                                          "path": UPath(controller_ == "AWEStickerdDetailContainerViewController")},
            "poi_anchor": {"type": Control, "path": UPath(id_ == "poiNameLabel")},
            "poi_anchor_detail_page": {"type": Control,
                                       "path": UPath(type_ == "TTKPOIDetailHeaderSectionPOINameCell", visible_ == True)},
            "live_anchor": {"type": Control, "path": UPath(text_ == "LIVE Event: Apr 7 11:10 PM")},
            "live_anchor_detail_page": {"type": Control,
                                        "path": UPath(type_ == "GBLHybridSparkCardView")},
            "minigame_anchor": {"type": Control, "path": UPath(text_ == "Play Love Tester on TikTok")},
            "minigame_anchor_detail_page": {"type": Control,
                                            "path": UPath(type_ == "HGHTMLGameWebView")},
            "be_anchor": {"type": Control, "path": UPath(text_ == "The Babylon Effect")},
            "movietok_booktok_anchor": {"type": Control, "path": UPath(type_ == "TTKTopicSingleAnchorCustomView")},
            "movietok_anchor_detail_page": {"type": Control,
                                            "path": UPath(controller_ == "TTKMovieTokDetailViewController")},

            "quick_comment": {"type": Control, "path": UPath(id_ == "TTKCommentQuickInputElement")},
            "booktok_anchor_detail_page": {"type": Control, "path": UPath(type_ == "TTKDetailCollectionMultiTabCell")},
            "game_anchor": {"type": Control, "path": UPath(text_ == "Click to get Elite skin")},
            "duet_this": {"type": Control, "path": UPath(text_ == "Duet this")},
            "multi_anchor": {"type": Control, "path": UPath(id_ == "multiAnchorSubview", visible_ == True)},
            "quick_comment_input": {"type": Control,
                                    "path": UPath(controller_ == 'TTKCommentQuickInputViewController') / UPath(
                                        type_ == 'UIImageView')},
            "quick_comment_input_panel": {"type": Control, "path": UPath(type_ == "AWECommentListInputView")},
            "donation_anchor": {"type": Control, "path": UPath(text_ == "Act to Change")},
            "qna_button": {"type": Control, "path": UPath(text_ == "Invite to answer")},
            "close_playlist_detail_panel": {"type": Control,
                                            "path": UPath(id_ == "IconXMarkSmall") / UPath(type_ == "UIImageView")},
            "ba_anchor_detail_return": {"type": Control,
                                        "path": UPath(id_ == "kitView") / 1 / UPath(type_ == "BDXLynxViewSvg")},
            "multi_anchor_panel": {"type": Control,
                                   "path": UPath(type_ == "TikTokFeedAnchorPanelView", visible_ == True)},
            "module_search_icon": {"type": Control, "path": UPath(type_ == 'UIButton', label_ == 'Search')},
            "creator_popup_title": {"type": Control,
                                    "path": UPath(id_ == "favoriteNotificationHeaderViewLight", visible_ == True)},
            "ok_btn": {"type": Control, "path": UPath(text_ == "OK", visible_ == True)},
            "liveroom_more": {"type": Control, "path": UPath(text_ == "More", visible_ == True)},
            "comment_nums": {"type": Control,
                             "path": UPath(type_ == "TTKFeedPassthroughView", index=3) / UPath(id_ == "label",
                                                                                               visible_ == True)},
            "poi_desc": {"type": Control, "path": UPath(label_ == "poi cmd video", visible_ == True)},
            "repost_btn": {"type": Control, "path": UPath(~id_ == "Repost|OK", type_ == "TUXButton")},
            "tv_repost_status": {"type": Control, "path": UPath(text_ == "You reposted")},
            "tako_icon": {"type": Control, "path": UPath(type_ == "AWEFeedViewCell", visible_ == True) / UPath(id_ == "tikBotButton", depth=6)},
            "tako_disclaimer_confirm": {"type": Control, "path": UPath(id_ == "confirmBtn")},
            "tako_disclaimer_quite": {"type": Control, "path": UPath(id_ == "quitLabel")},
            "tako_icon_two": {"type": Control, "path": UPath(id_ == "tikBotButton", visible_ == True)},
            "tako_disclaimer_close": {"type": Control, "path": UPath(id_ == "closeButton")},
            "tako": {"path": UPath(id_ == "tikBotButton", index=0)},
            "add_friends_btn": {"path": UPath(id_ == "friendsNavView") / 0},
            "Profile": {"path": UPath(type_ == "TTKTabBarAnimationContentView", ~label_ == "个人主页|Profile") / UPath(id_ == "ic_tab_home_loading")},
            "comment_count_num": {"path": UPath(id_ == "feedCommentButton", visible_ == True) / UPath(id_ == "label")},
            "feed_userhead": {"path": UPath(type_ == "AWEStoryAvatarButton", visible_ == True) / UPath(type_ == "UIImageView", depth=5)},
            "following_username": {"path": UPath(id_ == "nameLabel", visible_ == True)},
            "inbox_suggested_username": {"path": UPath(label_ == "hahaha075", visible_ == True)},
            "inbox_suggested_username1": {"path": UPath(type_ == "UICollectionView", visible_ == True) / 15 / UPath(id_ == "labelsStackView", depth=5)},
            "inbox_suggested_username2": {"path": UPath(id_ == "nameLabel", visible_ == True)},
            "friends_tab_follow_views_popup": {"path": UPath(id_ == "IconXMarkSmall") / UPath(type_ == "UIImageView", depth=5)},
            "shop_tab_back": {"path": UPath(id_ == "viewContainer") / UPath(id_ == "kitView") / 0 / 1 / 1 / 0 / 0 / 0 / 0 / 0 / UPath(type_ == "BDXLynxViewSvg", depth=5)},
            "popup_not_now": {"path": UPath(id_ == "notNowButton")},
            "feed_+_follow_btn": {"path": UPath(id_ == "followPromptView", visible_ == True) / UPath(type_ == "UIImageView", visible_ == True, depth=5)},
            "Not_now": {"path": UPath(id_ == "Not now")}
        }
    def ClickBtn(self,upath,upath2=None,upath3=None):
        if upath2 is None:
            upath2 = upath
        if upath3 is None:
            upath3 = upath
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)
        elif self[upath2].wait_for_visible(raise_error=False):
            self[upath2].click()
            time.sleep(3)
        elif self[upath3].wait_for_visible():
            self[upath3].click()
            time.sleep(3)

    def ClickVisibleBtn(self,upath):
        if self[upath].wait_for_visible(raise_error=False):
            self[upath].click()
            time.sleep(3)

    def ReturnBtnVisible(self,upath):
        if self[upath].wait_for_visible(raise_error=False):
            return self[upath].wait_for_visible(raise_error=False)

    def ClickFollowingFeedUsername(self):
        if self["inbox_suggested_username"].wait_for_visible(timeout=5,raise_error=False):
            return self["inbox_suggested_username"].text
        elif self["inbox_suggested_username1"].wait_for_visible(timeout=5,raise_error=False):
            return self["inbox_suggested_username1"].text
        elif self["inbox_suggested_username2"].wait_for_visible(timeout=5):
            return self["inbox_suggested_username2"].text

    def ClickInboxSuggestedUserhead(self):
        if self["inbox_suggested_username"].wait_for_visible(timeout=5):
            self["inbox_suggested_username"].click()
            time.sleep(3)

    def ClickFeedUserHead(self):
        if self["feed_userhead"].wait_for_visible(timeout=5):
            self["feed_userhead"].click()
            time.sleep(3)

    def click_profile(self):
        if self["Profile"].wait_for_visible(timeout=2,raise_error=False):
            self["Profile"].click()
        else:
            return False

    def exit_ba_anchor_detail_page(self):
        if self["ba_anchor_detail_return"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["ba_anchor_detail_return"].click()
            time.sleep(3)

    def copy_vid(self):
        self["feed_panel_dropdown"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["feed_panel_dropdown"].click()
        self["copy_vid"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["copy_vid"].click()
        return self["copy_vid"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def multi_anchor_panel_exist(self):
        return self["multi_anchor_panel"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def qna_button_exist(self):
        return self["qna_button"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_donation_anchor_exists(self):
        return self["donation_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_quick_comment_input_panel_exists(self):
        return self["quick_comment_input_panel"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_quick_comment_input(self):
        if self["quick_comment_input"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["quick_comment_input"].click()
            time.sleep(3)

    def check_multi_anchor_exists(self):
        return self["multi_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_multi_anchor_icon(self):
        if self["multi_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["multi_anchor"].click()
            time.sleep(3)

    def check_duet_this_exists(self):
        return self["duet_this"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_movietok_anchor_exists(self):
        return self["movietok_booktok_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_quick_comment_exists(self):
        return self["quick_comment"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_movietok_anchor(self):
        if self["movietok_booktok_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["movietok_booktok_anchor"].click()
            time.sleep(3)

    def check_movietok_anchor_detail_page_exists(self):
        return self["movietok_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_booktok_anchor_exists(self):
        return self["movietok_booktok_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_booktok_anchor(self):
        if self["movietok_booktok_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["movietok_booktok_anchor"].click()
            time.sleep(3)

    def check_booktok_anchor_detail_page_exists(self):
        return self["booktok_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_game_anchor_exists(self):
        return self["game_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_game_anchor(self):
        if self["game_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["game_anchor"].click()
            time.sleep(3)

    def check_be_anchor_exists(self):
        return self["be_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_be_anchor(self):
        if self["be_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["be_anchor"].click()
            time.sleep(3)

    def check_poi_anchor_exists(self):
        return self["poi_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_poi_anchor(self):
        if self["poi_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["poi_anchor"].click()
            time.sleep(3)

    def check_poi_anchor_detail_page_exists(self):
        return self["poi_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_live_anchor_exists(self):
        return self["live_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_live_anchor(self):
        if self["live_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["live_anchor"].click()
            time.sleep(5)

    def check_live_anchor_detail_page_exists(self):
        return self["live_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_minigame_anchor_exists(self):
        return self["minigame_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_minigame_anchor(self):
        if self["minigame_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["minigame_anchor"].click()
            time.sleep(6)

    def check_minigame_anchor_detail_page_exists(self):
        return self["minigame_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_effect_anchor_exists(self):
        return self["effect_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_effect_anchor(self):
        if self["effect_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["effect_anchor"].click()
            time.sleep(3)

    def check_effect_anchor_detail_page_exists(self):
        return self["effect_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_capcut_anchor_exists(self):
        return self["capcut_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_capcut_anchor(self):
        if self["capcut_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["capcut_anchor"].click()
            time.sleep(3)

    def check_ba_leadgen_anchor_exists(self):
        return self["ba_leadgen_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_ba_leadgen_anchor(self):
        if self["ba_leadgen_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["ba_leadgen_anchor"].click()
            time.sleep(5)

    def check_ttm_playlist_anchor_exists(self):
        return self["ttm_playlist_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_ttm_playlist_anchor(self):
        if self["ttm_playlist_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["ttm_playlist_anchor"].click()
            time.sleep(3)

    def check_ttm_playlist_anchor_detail_page_exists(self):
        return self["ttm_playlist_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_ug_pick_resso_anchor_exists(self):
        return self["ug_pick_resso_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_ug_pick_resso_anchor(self):
        if self["ug_pick_resso_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["ug_pick_resso_anchor"].click()
            time.sleep(3)

    def check_resso_anchor_detail_page_exists(self):
        return self["resso_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_tcm_vertical_solution_game_anchor_exists(self):
        return self["tcm_vertical_solution_game_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_tcm_vertical_solution_game_anchor(self):
        if self["tcm_vertical_solution_game_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["tcm_vertical_solution_game_anchor"].click()
            time.sleep(3)

    def check_anchor_detail_page_exists(self):
        return self["anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_tcm_comment_anchor_exists(self):
        return self["tcm_comment_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def dismiss_input(self):
        if self["comment_emoji"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["comment_emoji"].click()
            time.sleep(2)
        if self["send_comment"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["send_comment"].click()
            time.sleep(3)

    def click_tcm_comment_anchor(self):
        if self["tcm_comment_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["tcm_comment_anchor"].click()
            time.sleep(3)

    def check_paid_collection_anchor_exists(self):
        return self["paid_collection_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_paid_collection_anchor(self):
        if self["paid_collection_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["paid_collection_anchor"].click()
            time.sleep(3)

    def check_paid_collection_anchor_detail_page_exists(self):
        return self["paid_collection_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_ecommerce_anchor_exists(self):
        return self["ecommerce_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_ecommerce_anchor(self):
        if self["ecommerce_anchor"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["ecommerce_anchor"].click()
            time.sleep(3)

    def check_ecommerce_anchor_detail_page_exists(self):
        return self["ecommerce_anchor_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_warning_banner_exists(self):
        return self["tns_warning_banner"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_warning_banner(self):
        if self["tns_warning_banner"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["tns_warning_banner"].click()
            time.sleep(3)

    def check_covid_banner_exists(self):
        return self["tns_covid_banner"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_covid_banner(self):
        if self["tns_covid_banner"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["tns_covid_banner"].click()
            time.sleep(3)

    def check_covid_banner_detail_page_exists(self):
        return self["covid_banner_detail_page"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_video_hashtag(self):
        rect = self["feed_description"].rect
        x = rect.left + rect.width
        y = rect.left + rect.width
        print("话题坐标", x, y)
        self.app.get_device().click(x=x, y=y)

    def restart_to_find_friends_tab(self):
        for _ in Retry(timeout=200):
            if self.Friends.wait_for_visible(raise_error=False, timeout=3):
                return
            self.app.restart()
            self.into_home_page()
            time.sleep(5)

    def get_comment_at_friends_list(self):
        time.sleep(5)
        self.comment_at_friends_list.elem_info
        self.comment_at_friends_list.ui_tree
        return self.comment_at_friends_list.items()

    def get_comment_at_friends_num(self):
        return len(self.get_comment_at_friends_list())

    def click_first_comment_at_friends(self):
        return self.get_comment_at_friends_list()[-1].click()

    def click_at_btn(self):
        return self.at_btn.click()

    def get_friend_list(self):
        return self["friend_list"].items()

    def click_qna_desc(self):
        if self["qna_desc"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["qna_desc"].click()
            time.sleep(3)

    def click_repost_icon(self):
        if self["repost_button"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["repost_button"].click()
            time.sleep(3)

    def check_video_reposted(self):
        return self["repost_section"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def like_feed_video(self):
        self["like_icon_white"].click()
        self.wait_for_ui_stable()
        self.refresh()
        self.swipe_up()
        self.swipe_down()

    def dislike_feed_video(self):
        self["like_icon_red"].click()
        self.wait_for_ui_stable()
        self.refresh()
        self.swipe_up()
        self.swipe_down()

    def click_photomode_title(self):
        if self["photomode_title"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["photomode_title"].click()
            time.sleep(3)

    def click_feed_user_icon(self):
        self["ad_user_icon"].click()
        time.sleep(3)

    def return_follow_status(self):
        return self["follow_text"].wait_for_existing(timeout=5, interval=1, raise_error=False)

    def photomode_dot_indicator_exist(self):
        return self["photomode_dot"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_qna_banner(self):
        if self["qna_banner"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["qna_banner"].click()

    def click_feed_description(self):
        if self["feed_description"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["feed_description"].click()

    def check_correct_video(self, target_username):
        # Retry for only 15 times as it is the amount of feed that gets pulled down from recommendation per refresh
        for _ in Retry(limit=15, interval=1):
            try:
                self["feed_username"].refresh()
                current_username = self["feed_username"].text
                self.app.testcase.log_info(current_username)
                if current_username.casefold() == target_username.casefold():
                    return True
            except:
                self.app.testcase.log_info("Error when trying to get username")
            self.swipe_up()
            time.sleep(5)
        return False

    def qna_banner_exist(self):
        return self["qna_banner"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def open_comment(self):
        if not self.poi_anchor_title_comment.wait_for_existing(raise_error=False, timeout=2):
            self.comment_following()

    # 临时处理无关闭按钮sign up弹窗
    def close_no_close_btn_panel(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=rect.height)

    def check_movie_photo(self):
        return self["movie_header"].wait_for_existing(timeout=5, raise_error=False)

    def check_ad_web_view(self):
        return self["ad_web_view"].wait_for_existing(timeout=5, raise_error=False)

    def click_ad_open_guide(self):
        if self["ad_open_guide"].wait_for_existing(timeout=3, raise_error=False):
            self["ad_open_guide"].click()

    def check_user_profile_collection(self):
        return self["user_profile_collection"].wait_for_existing(timeout=5, raise_error=False)

    def find_tt_feed_anchor(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["poi_desc"].visible:
                break
            self.swipe_up()

    def find_multi_anchor_sub(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["multi_anchor"].wait_for_existing(timeout=2, raise_error=False):
                break
            self.swipe_up()

    def check_ad_card_panel(self):
        return self["ad_card_panel"].wait_for_existing(timeout=5, raise_error=False)

    def check_nickname(self):
        return self["advertisement_nickname"].wait_for_existing(timeout=5, raise_error=False)

    def check_ad_nickname_jumping(self):
        return self["ad_nickname_jumping"].wait_for_existing(timeout=5, raise_error=False)

    def click_ad_user_icon(self):
        return self["ad_user_icon"].click()

    def click_learn_more_btn(self):
        return self["learn_more_btn"].click()

    def check_ad_guide(self):
        return self["ad_guide"].wait_for_existing(timeout=5, raise_error=False)

    def check_ad_card_view(self):
        return self["ad_card_view"].wait_for_existing(timeout=5, raise_error=False)

    def click_nickname(self):
        return self["ad_nickname"].click()

    def click_shop_now(self):
        if not self["shop_now"].wait_for_existing(raise_error=False, timeout=2):
            self["shop_now"].click()

    def click_ad_card(self):
        if self["ad_card"].wait_for_existing(timeout=20, raise_error=False):
            self["ad_card"].click()
        time.sleep(2)

    def check_ad_info(self):
        if self["advertisement_nickname"].wait_for_existing(timeout=3, raise_error=False):
            if self["feed_debug_desc"].wait_for_existing(timeout=3, raise_error=False):
                if self["ad_ui"].wait_for_existing(timeout=3, raise_error=False):
                    return True
        return False

    def check_fixed_allow(self):
        return self["fixed_allow"].wait_for_existing(timeout=5, raise_error=False)

    def click_fixed_allow(self):
        return self["fixed_allow"].click()

    def check_trending_text(self):
        if self["trending_text"].wait_for_existing(timeout=5, raise_error=False):
            if self["trending_text"].text[0:10] == "Trending ·":
                return True
            return False

    def check_rank_info(self):
        if self["rank_info"].wait_for_existing(timeout=5, raise_error=False):
            if self["rank_info"].text[0:3] == "No.":
                return True
            return False

    def check_play_next_btn(self):
        return self["play_next_btn"].wait_for_existing(timeout=5, raise_error=False)

    def check_flame_icon(self):
        return self["flame_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_play_next_btn(self):
        self["play_next_btn"].click()
        time.sleep(3)

    def click_playlist_return_btn(self):
        self["playlist_return_btn"].click()
        time.sleep(3)

    def return_playlist_name(self):
        return self["feed_playlist_name"].text

    def find_movie_anchor_icon(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["movie_anchor_icon"].wait_for_existing(timeout=5, raise_error=False):
                break
            else:
                self.swipe_up()

    def find_trending_banner(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["trending_banner"].wait_for_existing(timeout=5, raise_error=False):
                break
            elif self["playlist_bar"].existing or self["search_feed_bar"].existing:
                self.app.testcase.log_record("热点视频优先展示了playlist bar！")
                from shoots.exceptions import StopRunningCase
                raise StopRunningCase
            else:
                self.swipe_up()

    def find_see_more_btn(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["see_more_btn"].wait_for_existing(timeout=5, raise_error=False):
                break
            else:
                self.swipe_up()

    def find_comments_video(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["feed_hashtag"].existing and self["feed_hashtag"].text == "#Collage ":
                break
            else:
                self.swipe_up()

    def return_feed_comment_num(self):
        self["feed_comment_num"].refresh()
        logger.info("feed_comment_num: %s" % int(self["feed_comment_num"].text))
        return int(self["feed_comment_num"].text)

    def check_trending_banner(self):
        return self["trending_banner"].wait_for_existing(timeout=5, raise_error=False)

    def close_playlist_detail_page(self):
        self["close_playlist_detail_panel"].click()
        time.sleep(3)

    def click_trending_banner(self):
        self["trending_banner"].click()
        time.sleep(3)

    def check_playlist_detail_cell(self):
        return self["playlist_detail_cell"].wait_for_existing(timeout=5, raise_error=False)

    def check_playlist_bar_view(self):
        return self["playlist_bar_view"].wait_for_existing(timeout=5, raise_error=False)

    def find_playlist_bar(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["playlist_bar"].wait_for_existing(timeout=5, raise_error=False):
                break
            else:
                self.swipe_up()

    def click_playlist_bar(self):
        self["playlist_bar"].click()
        time.sleep(3)

    def check_book_photo(self):
        return self["book_photo"].wait_for_existing(timeout=5, raise_error=False)

    def check_book_anchor_icon(self):
        return self["book_anchor_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_book_anchor(self):
        self["book_anchor"].click()
        time.sleep(3)

    def find_book_anchor_icon(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["book_anchor"].wait_for_existing(timeout=5, raise_error=False):
                break
            else:
                self.swipe_up()

    def click_movie_anchor_icon(self):
        self["movie_anchor_icon"].click()
        time.sleep(3)

    def find_feed_effect_icon(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["following_poi_anchor"].wait_for_existing(timeout=5, raise_error=False):
                break
            else:
                self.swipe_up()

    def click_feed_effect_icon(self):
        self["following_poi_anchor"].click()
        time.sleep(3)

    def return_effect_title(self):
        return self["poi_anchor_title"].text

    def return_effect_detail_title(self):
        return self["effect_detail_title"].text

    def check_search_feed_bar(self):
        return self["search_feed_bar"].wait_for_existing(timeout=5, raise_error=False)

    def click_search_feed_bar(self):
        self["search_feed_bar"].click()
        time.sleep(3)

    def check_hashtag_icon(self):
        if self["change_default_head"].wait_for_existing(timeout=5, raise_error=False):
            return True
        elif self["search_content_list"].wait_for_existing(timeout=5, raise_error=False):
            return True
        return False

    def check_feed_text(self):
        return self["feed_text"].wait_for_existing(timeout=5, raise_error=False)

    def back_feed_page(self):
        return self["search_back_btn"].click()

    def check_search_top(self):
        return self["search_top"].wait_for_existing(timeout=5, raise_error=False)

    def click_feed_hashtag(self):
        if self["feed_description"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_description"].click()
            time.sleep(5)
            rect = self["feed_description"].rect
            x = rect.width - rect.left - 160
            y = rect.top + rect.height - 10
            print("点击feed话题坐标", x, y)
            self.app.get_device().click(x=x, y=y)

    def check_feed_right_icon(self):
        if self["feed_right_icon"].existing:
            return True
        return False

    def check_clear_mode_btn(self):
        return self["clear_mode"].wait_for_existing(timeout=3, raise_error=False)

    def click_clear_mode(self):
        if self["clear_mode"].wait_for_existing(timeout=3, raise_error=False):
            self["clear_mode"].click()
        time.sleep(3)

    def check_not_interest_toast(self):
        if self["not_interest_toast"].existing:
            return True
        return False

    def click_not_interest(self):
        if self["not_interest"].wait_for_existing(timeout=3, raise_error=False):
            self["not_interest"].click()
        time.sleep(3)

    def check_icon_music_play(self):
        return self["icon_music_play"].wait_for_existing(timeout=5, raise_error=False)

    def click_music_cover(self):
        if self["feed_music_cover"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_music_cover"].click()
        time.sleep(3)

    def click_panel_page(self):
        return self["panel_page"].click()

    def check_pause_icon(self):
        return self["pause_icon"].wait_for_existing(timeout=5, raise_error=False)

    def vod_pause(self):
        """
        暂停播放
        """
        self.click()
        logger.info("[点播] 暂停播放")
        # if not self.check_pause_icon():
        #     self.click()

    def check_hide_btn(self):
        return self["hide_btn"].wait_for_existing(timeout=5, raise_error=False)

    def click_hide_btn(self):
        if self["hide_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["hide_btn"].click()
        time.sleep(3)

    def click_see_more_btn(self):
        if self["see_more_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["see_more_btn"].click()
        time.sleep(3)

    def click_comment_btn(self):
        self['feed_comment'].click()
        time.sleep(2)

    def add_comment_num(self, text):
        self["comment_edit_input"].wait_for_visible()
        self["comment_edit_input"].input(text)
        time.sleep(2)
        self["comment_send_btn"].click()
        time.sleep(2)

    def return_comment_num(self):
        logger.info("comment_detail_num: %s" % int(self["comment_detail_num"].text[-3:]))
        return int(self["comment_detail_num"].text[-3:])

    def delete_first_comment(self):
        self["first_comment"].long_click()
        time.sleep(3)
        # self["delete"].click()

    def click_report_btn(self):
        if self["report_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["report_btn"].click()
        time.sleep(3)

    def check_report_page(self):
        if self["report_page"].wait_for_existing(timeout=20, raise_error=False):
            return True
        return False
        time.sleep(3)

    def swipe_report_page(self):
        self["report_page"].swipe(y_direction=-1, swipe_coefficient=8)
        time.sleep(2)


    def click_poi_title_comment(self):
        for _ in Retry(timeout=20, raise_error=False):
            self.comment_close.click()
            self["poi_anchor_title_comment"].click()
            if self["poi_map_title"].wait_for_existing(timeout=3, raise_error=False):
                return

    def close_anchor_panel(self):
        if self["close_anchor_panel"].wait_for_existing(raise_error=False, timeout=3):
            self["close_anchor_panel"].click()
            if self["return_btn"].wait_for_existing(raise_error=False, timeout=3):
                self["return_btn"].click()
            time.sleep(2)
            if self["back_inbox_tab"].wait_for_existing(raise_error=False, timeout=3):
                self["back_inbox_tab"].click()

    def close_anchor_panel_v2(self):
        if self["close_anchor_panel"].wait_for_existing(raise_error=False, timeout=3):
            self["close_anchor_panel"].click()

    def click_profile_comment_poi_icon(self):
        if self["profile_comment_poi_icon"].wait_for_existing(raise_error=False, timeout=3):
            self["profile_comment_poi_icon"].click()

    def click_public_settings_twice(self):
        for _ in Retry(limit=2, raise_error=False):
            self["public_settings"].click()

    def is_repost_panel_first_time_prompt_existing(self) -> bool:
        return self["repost_panel_btn"].wait_for_existing(timeout=10, raise_error=False)

    def close_repost_if_existing(self) -> None:
        if self["repost_panel_btn"].wait_for_existing(timeout=20, raise_error=False):
            self["repost_panel_btn"].click()

    def confirm_remove_repost_if_existing(self) -> None:
        if self["remove_repost_btn"].wait_for_existing(timeout=20, raise_error=False):
            self["remove_repost_btn"].click()

    def has_you_reposted_bubble(self) -> bool:
        """
        verify that the bubble "you reposted" appears
        """
        return self["you_reposted_bubble"].wait_for_existing(timeout=20, raise_error=False)

    def has_add_comment_bubble(self) -> bool:
        """
        verify that the bubble "add comment" appears
        """
        return self["add_comment_bubble"].wait_for_existing(timeout=20, raise_error=False)

    def has_repost_message_in_comment_panel(self) -> bool:
        return self["repost_comment_separator"].wait_for_existing(timeout=10, raise_error=False)

    def repost_from_bubble(self) -> None:
        if self["repost_bubble"].existing:
            self["repost_bubble"].click()
        self.close_repost_if_existing()

    def add_to_feed(self):
        self["debug_tool"].click()
        self["add_to_feed_btn"].click()
        time.sleep(10)
        self.app.restart(clear_data=False)
        time.sleep(10)

    def check_activity_entrance(self):
        if self["activity_entrance"].wait_for_existing(timeout=5, raise_error=False):
            self["activity_entrance"].click()
            time.sleep(2)
            self["choose_activity"].click()
        elif self["activities"].wait_for_existing(timeout=5, raise_error=False):
            self["activities"].click()

    def click_poi_map_title(self):
        if self["poi_map_title_v2"].wait_for_existing(timeout=5, raise_error=False):
            self["poi_map_title_v2"].click()
        elif self["poi_map_title"].wait_for_existing(timeout=5, raise_error=False):
            self["poi_map_title"].click()

    def return_poi_map_title(self):
        if self["poi_map_text_v2"].wait_for_existing(timeout=5, raise_error=False):
            return self["poi_map_text_v2"].text[0:10]
        elif self["poi_map_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["poi_map_title"].text[0:10]

    def check_music_post_title(self):
        return self["music_post_title"].wait_for_existing(raise_error=False, timeout=5)

    def check_play_icon(self):
        return self["play_on_cover"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def ocr_click(self, device, text):
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)

    def get_ele_by_ocr(self, text):
        return CVElement(path=UPath(ocr_ == text), root=self.app)

    def delete_video(self):
        # for _ in Retry(timeout=100):
        #     if not self["more"].wait_for_existing(timeout=3, raise_error=False):
        #         break
        #     self["more"].click()
        #     for _ in Retry(timeout=20):
        #         if self["delete"].wait_for_existing(timeout=1.5, raise_error=False):
        #             self["delete"].click()
        #             break
        #         self["select_area"].scroll(distance_x=100, distance_y=0)
        #         time.sleep(2)
        #     self["delete_btn"].click()
        #     time.sleep(3)
        if self["more"].wait_for_existing(timeout=3, raise_error=False):
            self["more"].click()
            for _ in Retry(timeout=20):
                if self["delete"].wait_for_existing(timeout=1.5, raise_error=False):
                    self["delete"].click()
                    break
                self["select_area"].scroll(distance_x=100, distance_y=0)
                time.sleep(2)
            self["delete_btn"].click()
            time.sleep(3)
        else:
            return

    def paste_poi_address(self):
        if self["search_bar"].wait_for_existing(timeout=5, raise_error=False):
            self["search_bar"].click()
            self["search_bar"].long_click()
            time.sleep(2)
        other_panel = OtherPanel(root=self.app)
        if other_panel.paste.wait_for_existing(timeout=5, raise_error=False):
            other_panel.paste.click()
        time.sleep(2)

    def check_favorites_icon(self):
        for _ in Retry(limit=5, raise_error=False):
            if self["favorites_icon"].wait_for_existing(timeout=3, raise_error=False):
                break
            else:
                self.swipe_up()

    def check_ad_ui(self):
        for _ in Retry(timeout=150, raise_error=False):
            if self["ad_ui"].wait_for_existing(timeout=3, raise_error=False):
                break
            else:
                self.swipe_up()

    def click_ad_ui(self):
        return self["ad_ui"].click()

    def check_feed_name(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self.return_feed_name() == "@user830gb":
                break
            self.swipe_up()
            time.sleep(2)

    def check_no_favorites_icon(self):
        if self["favorites_icon"].existing:
            return False
        return True

    def check_panel_add_to_favourites(self):
        return self["panel_add_to_favourites"].wait_for_existing(timeout=3, raise_error=False)

    def click_panel_add_to_favourites(self):
        if self["panel_add_to_favourites"].wait_for_existing(timeout=3, raise_error=False):
            self["panel_add_to_favourites"].click()
        time.sleep(2)

    def click_collections_share_btn(self):
        if self["feed_share_icon_v1"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_share_icon_v1"].click()
        else:
            self["feed_share_icon_v2"].click()
        time.sleep(2)

    def remove_from_favourites(self):
        self.click_collections_share_btn()
        time.sleep(2)
        self["share_button_list_v2"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(2)
        if self["share_panel_collection_remove"].wait_for_existing(timeout=3, raise_error=False):
            self["share_panel_collection_remove"].click()
        time.sleep(2)

    def check_share_panel_collection(self):
        self.click_collections_share_btn()
        time.sleep(2)
        self["share_button_list_v2"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(2)
        if self["share_panel_collection_remove"].wait_for_existing(timeout=3, raise_error=False):
            self["share_panel_collection_remove"].click()
            time.sleep(2)
        self["share_panel_collection_add"].click()

    def video_add_to_favorites(self):
        if self["favorites_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["favorites_icon"].click()
            if self["creator_popup_title"].existing:
                self["ok_btn"].click()

    def return_favorites_nickname(self):
        if self["favorites_nickname"].wait_for_existing(timeout=3, raise_error=False):
            return self["favorites_nickname"].text

    def find_trending_bar(self):
        for _ in Retry(limit=4, raise_error=False):
            if self["trends_title"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def check_feed_trending_bar(self):
        return self["trends_title"].wait_for_existing(timeout=5, raise_error=False)

    def click_feed_trending_bar(self):
        self["trends_title"].click()
        time.sleep(3)

    def check_trends_detail(self):
        return self["trends_detail"].wait_for_existing(timeout=5, raise_error=False)

    def private_video(self):
        self["more"].click()
        for _ in Retry(timeout=100):
            if self["privacy"].wait_for_existing(timeout=1.5, raise_error=False):
                self["privacy"].click()
                break
            self["select_area"].scroll(distance_x=100, distance_y=0)
            time.sleep(2)

    def check_feed_guide(self):
        return self["feed_guide"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def wait_for_feed(self):
        return self["feed"].existing

    def check_feed_report(self):
        return self["feed_report"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def check_feed_search(self):
        return self["feed_search"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_feed_search(self):
        if self["feed_search"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_search"].click()

    def input_info_and_search(self, content):
        self.device_input(self["feed_search_edit"], content)
        time.sleep(3)
        self["module_search_icon"].click()
        time.sleep(2)

    def return_feed_hashtag(self):
        return self["feed_hashtag"].text

    def check_feed_playlist_icon(self):
        return self["feed_playlist_icon"].wait_for_existing(timeout=3, raise_error=False)

    def check_company_information(self):
        if self["check_company_information"].wait_for_existing(timeout=5, raise_error=False):
            self["know_and_agree"].click()

    def remove_feed_playlist(self):
        if self["playlist_feed_icon"].wait_for_existing(timeout=3, raise_error=False):
            self.long_click()
            time.sleep(5)
            self["remove_from_playlist"].click()
            self["confirm_remove"].click()
            time.sleep(3)

    def check_feed_bottom_bar(self):
        if self["feed_bottom_bar"].wait_for_existing(timeout=5, raise_error=False):
            self["close_bottom_bar"].click()
            time.sleep(2)
            self["turn_off_bar"].click()

    def click_video_add_to_playlist(self):
        self["video_add_to_playlist"].click()

    def remove_from_playlist_action(self):
        if self["playlist_feed_icon"].wait_for_existing(timeout=5, raise_error=False):
            self.long_click()
            time.sleep(5)
            self["remove_from_playlist"].click()
            self["confirm_remove"].click()

    def add_to_playlist_action(self, content):
        if self["bottom_progress_bar"].wait_for_existing(timeout=5, raise_error=False):
            self["close_bottom_progress_bar"].click()
            time.sleep(2)
        self.remove_from_playlist_action()
        time.sleep(3)
        self.add_playlist_action(content)

    def add_playlist_action(self, content):
        if self["playlist_feed_icon"].wait_for_existing(timeout=5, raise_error=False):
            time.sleep(3)
        else:
            self.long_click()
            self["playlist_add_action"].click()
            self["create_playlist"].click()
            self["input_playlist_name"].input(content)
            time.sleep(3)
            self["confirm_create"].click()
            self["confirm_add"].click()

    def check_playlist_feed_icon(self):
        if self["playlist_feed_icon"].wait_for_existing(timeout=5, raise_error=False):
            return False
        else:
            return True

    def click_playlist_caption(self, content):
        self.add_playlist_action(content)
        time.sleep(3)
        self["playlist_feed_icon"].click()

    def check_feed_comment_panel(self):
        return self["feed_comment_panel"].wait_for_existing(timeout=5, raise_error=False)

    def feed_comment_input(self, content):
        # self.device_input(self["feed_comment_input"], content)
        self.input(content)
        time.sleep(2)

    def check_feed_comment_input(self):
        return self["feed_comment_input"].wait_for_existing(timeout=5, raise_error=False)

    def return_playlist_caption_title(self):
        return self["playlist_caption_title"].text

    def compare_playlist_caption_title(self, content):
        if self.return_playlist_caption_title() == content:
            return True

    def check_caption(self):
        return self["playlist_feed_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_add_button(self):
        self["confirm_add"].click()

    def input_playlist_name(self, content):
        self["input_playlist_name"].input(content)
        time.sleep(3)
        self["confirm_create"].click()

    def compare_playlist_name(self, content):
        pl_name = self["person_page_added_playlist"].text
        if content == pl_name:
            return True

    def check_video_add(self):
        return self["feed_video_add"].wait_for_existing(timeout=5, raise_error=False)

    def click_more_icon(self):
        if self["feed_share_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_share_icon"].click()
        else:
            self["feed_more_icon"].click()

    def click_create_icon(self):
        self["create_icon"].click()

    def check_create_panel(self):
        return self["create_panel"].wait_for_existing(timeout=5, raise_error=False)


    def check_add_to_playlist_icon(self):
        return self["add_to_playlist_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_add_to_playlist_icon(self):
        self["add_to_playlist_icon"].click()

    def check_playlist_caption_panel(self):
        return self["playlist_caption_panel"].wait_for_existing(timeout=5, raise_error=False)

    def swipe_playlist_panel(self):
        self["playlist_caption_panel"].swipe(y_direction=-1, swipe_coefficient=8)
        time.sleep(2)

    # def swipe_left(self):
    # self.scroll(distance_x=1, coefficient_x=0.65)

    def check_feed_playlist_video(self):
        if self["playlist_caption_panel"].existing:
            return self["playlist_caption_panel"].wait_for_existing(timeout=5, raise_error=False)
        else:
            return self["feed_playlist_video"].wait_for_existing(timeout=5, raise_error=False)

    def check_feed_comment(self):
        return self["feed_comment"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_feed_comment(self):
        self["feed_comment"].click()
        time.sleep(3)

    def click_feed_comment_suggested(self):
        self["feed_comment_suggested"].click()

    def check_feed_at_friends(self):
        return self["comment_at_friends"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_comment_at_friends(self):
        self["comment_at_friends"].click()
        time.sleep(3)

    def check_at_friends_list(self):
        return self["at_friends_list"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def check_feed_follow(self):
        return self["feed_follow_btn"].wait_for_existing(timeout=10, interval=1, raise_error=False)

    def click_feed_follow(self):
        if self["feed_follow_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_follow_btn"].click()
            time.sleep(3)

    def check_small_follow_btn(self):
        return self["small_follow_btn"].wait_for_existing(timeout=10, interval=1, raise_error=False)

    def check_no_feed_follow(self):
        return not self["feed_follow_btn"].wait_for_existing(timeout=10, interval=1, raise_error=False)

    def check_more_v5(self):
        return self["more_button_v5"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_more_v5(self):
        self["more_button_v5"].click()
        time.sleep(3)

    def check_share_v5(self):
        return self["share_element"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_share_v5(self):
        self["share_element"].click()
        time.sleep(3)

    def close_share_panel(self):
        if self["close_share_panel"].wait_for_existing(timeout=5, raise_error=False):
            self["close_share_panel"].click()
        else:
            self["close_share_panel_v2"].click()
        time.sleep(3)

    def check_share_v1(self):
        return self["share_element"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_share_v1(self):
        self["share_element"].click()
        time.sleep(3)

    # Share btn click on feeb panel-forPhotoMode
    def click_share_btn(self):
        self["share_btn"].click()
        time.sleep(3)

    # Save Photo txt on feed panel-forPhotoMode
    def check_save_phototxt_exists(self):
        return self["save_txt"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def feed_debug_function(self):
        self["feed_debug"].click()
        time.sleep(3)
        self["search_vid"].click()
        time.sleep(3)

    def check_feed_debug_panel(self):
        if self["feed_debug_panel"].wait_for_existing(timeout=5, raise_error=False):
            return True
        else:
            return False

    def manage_debug(self):
        if self.app.app_spec["bundle_id"] not in ['com.ss.iphone.ugc.TrillInhouse1', 'com.zhiliaoapp.musically.ep']:
            self.app.testcase.log_record("线上包无法使用debug工具")
            from shoots.exceptions import StopRunningCase
            raise StopRunningCase
        # self.app.restart()
        time.sleep(5)
        if self["feed_debug"].wait_for_existing(timeout=5, raise_error=False):
            try:
                self["feed_debug"].click()
            except RuntimeError:
                self["feed_debug"].refresh()
                self["feed_debug"].click()
            time.sleep(3)
            self["manage_feed"].click()
            if self["feed_debug_cell"].wait_for_existing(timeout=3, raise_error=False):
                self["debug_remove_video"].click()
                time.sleep(1)
                self["delete_btn"].click()
            time.sleep(3)
        elif self["debug_tool"].wait_for_existing(timeout=5, raise_error=False):
            try:
                self["debug_tool"].click()
            except RuntimeError:
                self["debug_tool"].refresh()
                self["debug_tool"].click()
            time.sleep(3)
            self["manage_feed"].click()
            if self["feed_debug_cell"].wait_for_existing(timeout=3, raise_error=False):
                self["debug_remove_video"].click()
                time.sleep(1)
                self["delete_btn"].click()
            time.sleep(3)

    def check_feed_debug_exist(self):
        while not self.check_feed_debug_panel():
            self.swipe(y_direction=1, swipe_coefficient=5)
            self.manage_debug()
            if self.check_feed_debug_panel():
                break

    def check_create_avatar(self):
        if self["create_avatar"].existing:
            self["exit_create_avatar"].click()
            time.sleep(3)

    def get_current_vid(self):
        self["feed_debug"].click()
        self["copy_vid"].click()
        return self.app.testcase.device.get_clipboard()

    def add_vid(self, content):
        self["add_vid"].click()
        time.sleep(3)
        if self["debug_video_input"].wait_for_existing(timeout=3, raise_error=False):
            self["debug_video_input"].click()
        time.sleep(3)
        self["input_vid"].click()
        self["input_vid"].input(content)
        time.sleep(3)
        self["import_vid"].click()
        time.sleep(3)
        self.app.restart(clear_data=False)
        # 找到对应Feed
        self.swipe_up()



    def check_long_press(self):
        return self["long_press"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def send_first_friend(self):
        rect = self.rect
        self["not_interested_btn"].drag(to_x=rect.width, to_y=(rect.height - rect.top) / 2)
        first_friend = self["long_press_friends_list"].items()[-1]
        first_friend.click_send_btn()

    def copy_image(self, device, original_pic_name):
        capture = cv.get_screenshot(device)
        self["feed_search"].ensure_visible()
        rect1 = self["feed_search"].rect
        self["nickname"].ensure_visible()
        rect2 = self["nickname"].rect
        target = capture[0][rect1.top * 3: (rect2.top + rect2.height) * 3, :]
        import cv2
        basedir = os.path.abspath('')
        pic_dir = os.path.join(basedir, device.serial)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        from shoots_cv.cv import CV
        cv_ = CV()
        original_pic_dir = os.path.join(os.path.abspath(os.path.join(os.getcwd(), "../../../../..")), "resources")
        original_pic_name = os.path.join(original_pic_dir, original_pic_name + ".png")
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']
        print(compare_result)
        return compare_result > 0.9999

    def search_image(self, device, original_pic_name):
        capture = cv.get_screenshot(device)
        self["feed_search"].ensure_visible()
        rect = self["feed_search"].rect
        target = capture[0][rect.top * 3:(rect.top + rect.height) * 3, rect.left * 3:(rect.left + rect.width) * 3]
        basedir = os.path.abspath('')
        pic_dir = os.path.join(basedir, device.serial)
        # os.mkdir(pic_dir)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        original_pic_dir = os.path.join(os.path.abspath(os.path.realpath(__file__) + os.path.sep + "../../.."),
                                        "resources")
        print("路径：", original_pic_dir)
        original_pic_name = os.path.join(original_pic_dir, original_pic_name + ".png")
        # return ocr_helper.dhash_diff_pic(target_pic_name, original_pic_name)
        from shoots_cv.cv import CV
        cv_ = CV()
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']
        print(compare_result)
        return compare_result > 0.99999

    def add_feed_vid(self, content):
        self["input_vid"].click()
        self["input_vid"].input(content)
        time.sleep(3)
        self["search_result"].click()
        time.sleep(3)

    def check_feed_desc(self):
        return self["feed_desc_hashtag"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def feed_desc(self):
        self["feed_desc_hashtag"].ensure_visible()
        rect = self["feed_desc_hashtag"].rect
        x = rect.left + rect.width * 1 / 6
        y = rect.top + rect.height * 1 / 2
        return x, y

    def feed_search_hashtag(self):
        self["feed_hashtag"].ensure_visible()
        rect = self["feed_hashtag"].rect
        x = rect.left + rect.width * 1 / 6
        y = rect.top + rect.height * 1 / 2
        return x, y

    def check_feed_nickname(self):
        return self["feed_nickname"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_feed_nickname(self):
        self["feed_nickname"].click()
        time.sleep(3)

    def return_feed_nickname(self):
        return self["feed_nickname"].text

    def return_feed_name(self):
        self["feed_name"].refresh()
        return self["feed_name"].text

    def back_to_feed(self):
        self["back_to_feed"].click()
        time.sleep(3)

    def check_desc_comment_icon(self):
        return self["desc_comment_icon"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def click_desc_comment_icon(self):
        self["desc_comment_icon"].click()
        time.sleep(3)

    def click_start_watching(self):
        self["start_watching"].click()
        time.sleep(3)

    def enter_comment_input(self, comment: str) -> None:
        self["add_comment_input"].input(comment)

    def comment_(self, num):
        for i in range(num):
            self.comment_add.input("A")

    def clear_cookies_and_storage(self):
        self.into_mine_panel()
        me_panel = MePanel(root=self.app)
        if me_panel.is_logged():
            me_panel.enter_settings()
            me_panel.click_settings_and_privacy_btn()
            settings_panel = SettingsWin(root=self.app)
            settings_panel.logout()
        else:
            pass
        self.into_debug_panel()
        debug_panel = DebugPanel(root=self.app)
        debug_panel.clear_data()

    def find_delete_and_click(self):
        for _ in Retry(timeout=20):
            if len(self["action_list"].items()) > 0:
                for item in self["action_list"].items():
                    print(item.elem_info.get("label"))
                    if item.elem_info.get("label") == "Delete":
                        item.click()
                        return
                self["action_list"].refresh()
                self["action_list_area"].swipe(x_direction=1, swipe_coefficient=5)

    def test_click(self):
        device = self.app.get_device()
        screen_rect = device.screen_rect
        device.click(x=screen_rect.width / 2, y=screen_rect.height / 3)

    def click_comment_close(self):
        return self["comment_close"].click()

    def back_to_cancel(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self.poi_anchor_cancel.wait_for_existing(timeout=1.5, raise_error=False):
                return
            self.app.get_device().press_back()

    def back_to_comment(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self.poi_anchor_cancel.wait_for_existing(timeout=1.5, raise_error=False):
                self.poi_anchor_cancel.click()
                if self.feed_comment.wait_for_existing(timeout=1.5, raise_error=False):
                    self.feed_comment.click()
            elif self.comment_add.wait_for_existing(timeout=1.5, raise_error=False):
                return
            self.app.get_device().press_back()

    def back_to_profile(self):
        for _ in Retry(timeout=30):
            if self.cdu_002.wait_for_existing(timeout=1.5, raise_error=False):
                return
            self.app.get_device().press_back()

    def back_to_links(self):
        for _ in Retry(timeout=30):
            if self.poi_anchor_title_more.wait_for_existing(timeout=1.5, raise_error=False):
                break
            self.app.get_device().press_back()

    def find_poi_long(self):
        for _ in Retry(timeout=50):
            time.sleep(3)
            if not self.poi_anchor_title.wait_for_existing(timeout=1.5, raise_error=False):
                self.swipe_up()
            else:
                base_pic = self.poi_anchor_title.capture()
                cv_ = CV()
                res1 = cv_.ocr_location("…", base_pic)
                res2 = cv_.ocr_location("..", base_pic)
                if res1.get("msg") == "Success" or res2.get("msg") == "Success":
                    break
                else:
                    self.swipe_up()

    def find_collection_video(self):
        for _ in Retry(timeout=40):
            time.sleep(5)
            if self.collection_video.existing and self.collection_video.text == "Collection test":
                break
            else:
                self.swipe_up()
            time.sleep(3)

    def find_like_poi_anchor(self):
        for _ in Retry(timeout=50):
            time.sleep(3)
            if not self.poi_anchor_view.wait_for_existing(timeout=1.5, raise_error=False):
                self.swipe_up()
            else:
                return False

    def click_like_poi_comment(self):
        if self["poi_anchor_view"].wait_for_existing(timeout=1.5, raise_error=False):
            if self["poi_comments"].wait_for_existing(timeout=1.5, raise_error=False):
                self["poi_comments"].click()
                time.sleep(3)
                if self["comments_poi_icon"].wait_for_existing(timeout=1.5, raise_error=False):
                    self["comments_poi_icon"].click()
                    time.sleep(3)

    def close_poi_panel(self):
        if self["close_poi_panel"].wait_for_existing(timeout=1.5, raise_error=False):
            self["close_poi_panel"].click()
        time.sleep(3)
        self["back_like_tab"].click()
        time.sleep(2)

    def find_poi_more(self):
        for _ in Retry(timeout=80):
            if self.poi_icon_more.wait_for_existing(timeout=3, raise_error=False):
                return
            self.swipe_up()
            time.sleep(3)

    def click_poi_more(self):
        for _ in Retry(timeout=80):
            if self.profile_multiple_anchor.wait_for_existing(timeout=1.5, raise_error=False):
                self.profile_multiple_anchor.click()
            time.sleep(3)

    def find_share(self):
        for _ in Retry(timeout=50):
            if self["share_label"].wait_for_existing(timeout=1.5, raise_error=False):
                return
            elif self["share_btn"].wait_for_existing(timeout=1.5, raise_error=False):
                return
            self.swipe_up()

    def swipe_up(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=rect.height * 0.75)

    def swipe_down(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=- rect.height * 0.8)

    def swipe_up_till_normal_video(self, timeout=60) -> None:
        """
        scrolls feed panel till a normal video is found
        """
        limit = time.time() + timeout
        while True:
            if time.time() <= limit:
                time.sleep(5)
                if self["follow_back"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                    self.swipe_up()
                    continue
                elif self["live_now_tag"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                    self.swipe_up()
                    continue
                elif self["story_tag"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                    self.swipe_up()
                    continue
                elif self["sponsored_tag"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                    self.swipe_up()
                    continue
                elif self["photomode_dot"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                    self.swipe_up()
                    continue
                else:
                    # Return if none of above elements found
                    return
            break

    def swipe_up_till_matching_description(self, desc: str, timeout=60) -> None:
        """
        scrolls feed panel till a matching description is found
        """
        limit = time.time() + timeout
        while True:
            if time.time() <= limit:
                time.sleep(5)
                if self.get_feed_description() != desc:
                    self.swipe_up()
                    continue
            break

    def swipe_up_self(self, num):
        self.scroll(distance_y=num)

    # 按控件位置截图
    def take_capture(self, area):
        time.sleep(2)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    def ui_checking(self):
        pass

    def wait_for_panel_loading(self):
        if self["Home"].wait_for_existing(timeout=5, raise_error=True):
            self["Home"].click()
        time.sleep(3)
        return self["panel_controller"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def add_account(self):
        self["switch_account"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        self["switch_account"].click()
        self["add_account"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        self["add_account"].click()

    def add_account_1(self):
        self["switch_account_1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        self["switch_account_1"].click()
        self["add_account"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        self["add_account"].click()

    def switch_to_following(self):
        if self["following"].wait_for_existing(timeout=5, raise_error=True):
            self["following"].click()
        time.sleep(3)

    def switch_to_nearby(self):
        if self["nearby"].wait_for_visible():
            self["nearby"].click()
            time.sleep(5)

    def switch_to_explore(self):
        if self["explore"].wait_for_visible():
            self["explore"].click()
            time.sleep(5)

    def ClickExploreUserhead(self):
        if self["explore_userhead"].wait_for_visible():
            self["explore_userhead"].click()
            time.sleep(5)

    def switch_to_friends_tab(self):
        if self["friends_feed_tab"].wait_for_existing(timeout=3, raise_error=False):
            self["friends_feed_tab"].click()
        time.sleep(2)

    def switch_to_foryou(self):
        return self["foryou"].click()

    def feed_pause(self):
        if self["feed1"].wait_for_existing(timeout=5, raise_error=False):
            result = self["feed1"].click()
        else:
            result = self["feed"].click()
        time.sleep(2)  # wait for click was actually responded
        return result

    def is_paused(self):
        device = self.app.get_device()
        image_before_path = device.screenshot()
        time.sleep(2)
        image_after_path = device.screenshot()
        from byted_cv.ocr_helper import diff_pic
        try:
            return diff_pic(image_before_path, image_after_path)
        finally:
            import os
            os.remove(image_before_path)
            os.remove(image_after_path)

    def feed_swipe_up(self):
        feed_intercept_interaction_view = self.feed_intercept_interaction_view
        original_author_name = feed_intercept_interaction_view.get_creator_name()
        if original_author_name == "":
            self.scroll(distance_y=500)
            feed_intercept_interaction_view.refresh()
            original_author_name = feed_intercept_interaction_view.get_creator_name()
        self.scroll(distance_y=500)
        feed_intercept_interaction_view.refresh()
        new_author_name = feed_intercept_interaction_view.get_creator_name()
        if new_author_name == "":
            self.scroll(distance_y=500)
            feed_intercept_interaction_view.refresh()
            new_author_name = feed_intercept_interaction_view.get_creator_name()
        logger.debug("original author: %s, new author: %s" % (original_author_name, new_author_name))
        if original_author_name == "" or new_author_name == "":
            return False
        else:
            return original_author_name != new_author_name

    def find_feed_not_live(self):
        for _ in Retry(limit=5, message="find not live feed failed in 5 times"):
            if self.feed_intercept_interaction_view.wait_for_existing(timeout=5, raise_error=False):
                if self.feed_intercept_interaction_view.is_living():
                    logger.debug("creator of this feed is living ...")
                    self.scroll(distance_y=500)
                    self.feed_intercept_interaction_view.refresh()
                else:
                    break
            else:
                logger.debug("this is a living feed.")
                self.scroll(distance_y=500)
                self.refresh()

    def CheckFeedFollowBtnVisible(self):
        for _ in Retry(limit=20, message="寻找 feed 页面是否存在+关注按钮"):
            if self["feed_+_follow_btn"].wait_for_visible(timeout=5, raise_error=False) == True:
                break
            else:
                self.scroll(distance_y=500)

    def return_feed_hashtag_text(self):
        if self["debug_hashtag_text"].wait_for_existing(timeout=5, raise_error=False):
            return self["debug_hashtag_text"].text


    def find_feed_have_hashtag(self):
        for _ in Retry(limit=5, message="find feed have hashtag icon in 5 times"):
            if self["feed_debug_desc"].wait_for_existing(timeout=5, raise_error=False):
                if self["debug_hashtag_text"].wait_for_existing(timeout=5, raise_error=False):
                    break
                else:
                    self.swipe_up()
                    time.sleep(2)

    def get_comment_count_number(self):
        try:
            if self["comment_count_num"].wait_for_visible(timeout=5, raise_error=True):
                count = self["comment_count_num"].text
                return int(count)
        except UIStatusError:
            return None

    def find_feed_has_comment(self):
        for _ in Retry(limit=20, message="find feed has comment failed in 5 times"):
            comment_count = self.get_comment_count_number()
            logger.debug("feed comment count is %s" % comment_count)
            if comment_count == 0 or comment_count == None:
                self.scroll(distance_y=500)
                self.refresh()
            else:
                break

    def find_video_has_hashtag_and_enter(self):

        for _ in Retry(limit=5, message="found no clickable hashtag in 5 times"):
            while not self.feed_intercept_interaction_view.description_has_hashtag():
                logger.debug("[video description] {}".format(self.feed_intercept_interaction_view.get_description()))
                self.scroll(distance_y=500)
                if self.feed_intercept_interaction_view.existing:
                    self.feed_intercept_interaction_view.refresh()
                else:
                    self.scroll(distance_y=500)
                time.sleep(2)
            self.feed_intercept_interaction_view.click_first_hashtag_in_description()
            from basic_views import AWEChallengeDetailContainerView
            challenge_detail_view = AWEChallengeDetailContainerView(root=self.app)
            if challenge_detail_view.existing:
                break
            else:
                logger.info("try to click text span failed, retry ...")
                self.scroll(distance_y=500)
                self.feed_intercept_interaction_view.refresh()
                time.sleep(2)

    def region_click(self):
        return self["region"].click()

    def region_close(self):
        return self["region_close"].click()

    def into_creator_profile_panel(self):
        self.feed_intercept_interaction_view.enter_profile()
        time.sleep(1)

    def into_add_friends_btn(self):
        rect = self["add_friends_btn"].rect
        x = rect.left + rect.width / 2
        y = rect.top + rect.height / 2
        print("加好友按钮坐标", x, y)
        self.app.get_device().click(x=x, y=y)
        # return self["add_friends_btn"].click()

    def ClickFeedFollowBtn(self):
        if self["feed_+_follow_btn"].wait_for_visible(timeout=3):
            self["feed_+_follow_btn"].click()

    def follow_creator(self):
        self.feed_intercept_interaction_view.find_follow_btn()
        self.feed_intercept_interaction_view.follow_creator()
        self.feed_intercept_interaction_view.follow_btn.wait_for_disappear(timeout=5)
        return self.feed_intercept_interaction_view.is_followed()

    def click_follow_icon(self):
        self.feed_intercept_interaction_view.find_follow_btn()
        self.feed_intercept_interaction_view.follow_creator()

    def is_followed(self):
        return self.feed_intercept_interaction_view.is_followed()

    def like(self):
        return self["like"].click()

    def get_like_count(self):
        if self["like_count"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            return self["like_count"].text
        else:
            return ""

    def comment(self):
        if self["comment"].wait_for_visible(raise_error=False, timeout=3):
            return self["comment"].click()
        else:
            return self["comment1"].click()

    def click_feed_comment_icon(self):
        return self["feed_comment_icon"].click()

    def click_send_comment_btn(self) -> None:
        self["send_comment_btn"].click()

    def comment_following(self):
        return self["comment_following"].click()

    def comment_to(self, count):
        self["comment_add"].input("a")
        self["send_comment_btn"].click()
        for i in range(count):
            self.comment_following()
            self["comment_add"].input("a")
            self["send_comment_btn"].click()

    def comment_close(self):
        return self["comment_close"].click()

    def get_comment_count(self):
        if self["comment_count"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            return self["comment_count"].text
        else:
            return ""

    def share(self):
        if self["share_label"].wait_for_existing(timeout=3, raise_error=False):
            self["share_label"].click()
        elif self["share_alternate"].wait_for_existing(timeout=3, raise_error=False):
            self["share_alternate"].click()
        elif self["feed_user_name"].wait_for_existing(timeout=3, raise_error=False):
            device = self.app.get_device()
            right_menu = Control(root=self.app, path=UPath(id_ == "TTKFeedInteractionRightContainerElement", visible_== True))
            total_control = len(right_menu.children)
            x, y = right_menu.rect.center[0], (right_menu.rect.height/total_control)*(total_control-1)+right_menu.rect.top-30
            logger.info(f"share icon:{[x,y]}")
            device.click(x, y)
        elif self["share_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["share_btn"].click()

    def share_close(self):
        return self["share_close"].click()

    def get_share_count(self):
        if self["share_count"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            return self["share_count"].text
        else:
            return ""

    def get_creator_name(self):
        if self["feed_creator_name"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            return self["feed_creator_name"].text
        else:
            return ""

    def get_creator_name_New(self):
        # feed_intercept_interaction_view = self.feed_intercept_interaction_view
        # creator_name = feed_intercept_interaction_view.get_creator_name()
        creator_name = self.get_feed_avatarname()
        logger.info(f"creator_name:{creator_name}")

        if "@" == creator_name[0]:
            creator_name = creator_name[1:]
        return creator_name

    def get_feed_description(self):
        if self["feed_description"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            return self["feed_description"].text
        else:
            return ""

    def description_translate(self):
        return self["description_translation"].click()

    def into_music_detail_panel(self):
        self["music_album"].click()
        time.sleep(1)

    def get_music_info(self):
        if self["music_info"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            return self["music_info"].text
        else:
            return ""

    def into_home_page(self):
        if self["privacy_policy"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            self["privacy_policy"].click()
        try:
            if self["Home"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["Home"].click()
        except:
            if self["Home"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["Home"].click()
        if self["kids_home"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["kids_home"].click()
        time.sleep(1)

    def into_kids_home_page(self):
        self["kids_home"].click()
        time.sleep(1)

    def get_home_button_text(self):
        self["Home"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        return self["Home"].text

    def into_discover_panel(self):
        self["Discover"].click()
        time.sleep(1)

    def into_friends_tab(self):
        if self["Friends"].wait_for_visible(timeout=5, raise_error=False):
            self["Friends"].click()

    def into_friends_panel_new(self):
        if self["Friends"].wait_for_visible(timeout=5, raise_error=False):
            self["Friends"].click()
        else:
            self["bottom_Friends"].click()
            time.sleep(5)
            self["Friends"].click()
        time.sleep(5)

    def into_message_panel(self):
        time.sleep(1)
        self["Inbox"].click()
        time.sleep(1)

    def into_message_panel_for_language(self):
        time.sleep(1)
        self["Inbox_for_language"].click()
        time.sleep(1)

    def into_mine_panel(self):
        login_panel = LoginPanel(root=self.app)
        login_panel.click_close_btn()
        if self["Me"].wait_for_existing(timeout=3, interval=1, raise_error=False):
            return self["Me"].click()
        if self["Me_Online"].wait_for_existing(timeout=3, interval=1, raise_error=False):
            self["Me_Online"].click()
        if self["kids_me"].wait_for_existing(timeout=3, interval=1, raise_error=False):
            self["kids_me"].click()
        self.ProfileEditWin = ProfileEditWin(root=self.app)
        if self.ProfileEditWin.CheckVisibleBtn("profile_suggested_account_hide") == True:
            self.ProfileEditWin.ClickNoVisibleBtn("profile_suggested_account_hide")

    def into_friends_panel(self):
        time.sleep(2)
        self["Friends"].click()
        time.sleep(1)
        self.switch_to_friends_tab()

    def click_add_contacts(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["Add_Friends"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self["Add_Friends"].click()
            elif self["Add_Friends_2"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self["Add_Friends_2"].click()
        return False

    def into_debug_panel(self):
        try:
            self["Me"].long_click()
            # If the operation is too fast, the app may can not get the foreground data
            time.sleep(3)
        except Exception as msg:
            logger.info(f"{msg}")

    def debug_panel(self):
        if self.app.app_spec["bundle_id"] not in ['com.ss.iphone.ugc.TrillInhouse1', 'com.zhiliaoapp.musically.ep']:
            self.app.testcase.log_record("线上包无法使用debug工具")
            from shoots.exceptions import StopRunningCase
            raise StopRunningCase
        self["settings_icon"].click()
        time.sleep(3)
        self["settings_enter"].click()
        self["advanced"].click()
        time.sleep(3)

    def debug_panel_in_login_status(self):
        self["settings_icon_from_login_status"].click()
        time.sleep(2)
        self["settings_enter"].click()
        time.sleep(2)
        self["advanced"].click()
        time.sleep(2)

    def into_shoot_panel(self):
        try:
            if self["Shoot_video"].wait_for_existing(timeout=5, raise_error=False):
                self["Shoot_video"].click()
            if self["kids_shoot_video"].wait_for_existing(timeout=5, raise_error=False):
                self["kids_shoot_video"].click()
        except:
            if self["Shoot_video"].wait_for_existing(timeout=5, raise_error=False):
                self["Shoot_video"].click()
            if self["kids_shoot_video"].wait_for_existing(timeout=5, raise_error=False):
                self["kids_shoot_video"].click()
        return True

    def into_region_change_panel(self):
        self["region_change"].click()
        time.sleep(1)

    def into_live_audience_panel(self):
        self["live_audience_entrance"].click()
        time.sleep(10)

    def long_press_live_audience_panel(self):
        self["live_panel"].long_click(duration=5)
        time.sleep(5)
        if self.live_long_pop.wait_for_visible(timeout=5, raise_error=False):
            rect = self.live_long_pop.rect
            self.app.testcase.device.click(rect.center[0], rect.top - 20)
            time.sleep(1)
            live_bottom_tab_rect = self.live_bottom_tab.rect
            self.app.testcase.device.click(live_bottom_tab_rect.right - 20, live_bottom_tab_rect.center[1])
            return
        if not self["long_press_panel"].wait_for_visible(timeout=5, raise_error=False):
            self["live_panel"].long_click(duration=5)
            time.sleep(5)

    def click_live_room_share_icon(self):
        time.sleep(5)
        if self.liveroom_more.wait_for_visible(timeout=3, interval=1, raise_error=False):
            self.liveroom_more.click()
        elif self["share_live"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            self["share_live"].click()
        self["friends_row"].wait_for_visible(timeout=5, interval=1)

    def check_share_panel_exist(self):
        return self["friends_row"].wait_for_visible(timeout=5, raise_error=False)

    def check_long_press_panel_exist(self):
        return True if self["long_press_panel"].wait_for_visible(timeout=3,
                                                                 raise_error=False) else self.send_to_panel.wait_for_visible(
            timeout=3, raise_error=False)

    def scroll_to_more_btn(self):
        self["friends_row"].swipe(x_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["friends_row"].swipe(x_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["friends_row"].swipe(x_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["more_btn"].click()

    def scroll_to_more_in_long_press_panel(self):
        if self.send_to_panel.wait_for_visible(timeout=5, raise_error=False):
            for _ in Retry(limit=8, interval=1, raise_error=False):
                self.send_to_panel.swipe(x_direction=1, swipe_coefficient=8)
                if self.more_cell.wait_for_visible(timeout=3, raise_error=False):
                    self.more_cell.click()
                    break
            return
        for _ in Retry(limit=15, interval=1):
            if self["more_friends"].wait_for_visible(timeout=3, raise_error=False):
                self["more_friends"].click()
                break
            else:
                self["long_press_panel"].swipe(y_direction=1, swipe_coefficient=8)
                time.sleep(1)

    def send_to_user(self, user):
        self.device_input(self['friend_search_bar'], user)
        time.sleep(2)
        self["select_friend_btn"].click()
        self["send_to_friend_btn"].click()
        time.sleep(2)

    def exit_live_room(self):
        self["exit_live_room_btn"].click()
        time.sleep(3)

    def get_region_info(self):
        self["region_change"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        try:
            return self["region_change"].text.split("in ")[1][0:2]
        except:
            return self["region_change"].text.split("(")[0][-2:]

    def return_home(self):
        self.Home.click()

    def click_home_icon(self):
        try:
            self["Home"].click
        except AppPopupNotHandled:
            pass

    def into_search_entrance(self):
        if self["search_entrance"].wait_for_visible(raise_error=False, timeout=3):
            self["search_entrance"].click()
        elif self["search_entrance_friends"].wait_for_visible(raise_error=False, timeout=3):
            self["search_entrance_friends"].click()
        elif self["search_bar"].wait_for_visible(raise_error=False, timeout=3):
            self["search_bar"].click()
        else:
            self["search_entrance_friends_spare"].click()
        time.sleep(2)

    '''
        Location-Monitor
        (点击changeAccuracyToPrecise 或 changeAccuracyToCoarse后其他按钮位置会变)
    '''

    def feed_search_click(self):
        self["search_click"].click()

    def privacy_test_page(self):
        self["privacy_test_page"].click()
        time.sleep(1)

    def Mock_click(self):
        self["Mock_click"].click()
        time.sleep(1)

    def Guard_SDK(self):
        self["Guard_SDK"].click()
        time.sleep(1)

    def CLLManager_Test(self):
        self["CLLManager_Test"].click()
        time.sleep(1)

    def Change_Accuracy_To_Coarse(self):
        self["Change_Accuracy_To_Coarse"].click()
        time.sleep(1)

    def Change_Accuracy_To_Precise(self):
        self["Change_Accuracy_To_Precise"].click()
        time.sleep(1)

    def Request_WhenInUse_Authorization(self):
        self["Request_WhenInUse_Authorization"].click()
        time.sleep(1)

    def Start_Updating_Location(self):
        self["Start_Updating_Location"].click()
        time.sleep(1)

    def Request_Location(self):
        self["Request_Location"].click()
        time.sleep(1)

    def Start_Monitor_Significant_Location_Changes(self):
        self["Start_Monitor_Significant_Location_Changes"].click()
        time.sleep(1)

    def Start_Monitor_For_Region(self):
        self["Start_Monitor_For_Region"].click()
        time.sleep(1)

    def Me_LongClick(self):
        self["Me"].long_click(duration=3)
        time.sleep(1)

    def Call_In_Background(self):
        self["Call_In_Background"].click()
        time.sleep(1)

    def Call_In_Background_Edit(self, content):
        self.device_input(self["Call_In_Background"], content)
        time.sleep(2)


    def click_follow_back(self):
        self["follow_back"].click()
        time.sleep(3)

    def click_username(self):
        self["username"].click()
        time.sleep(3)

    def click_follow_your_friends_close_btn(self):
        value = self["follow_your_friends_close_btn"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        if value == True:
            print("Found Follow your friends tab")
            self["follow_your_friends_close_btn"].click()
            time.sleep(3)
        else:
            print("Didn't find Follow your friends tab")
            time.sleep(3)

    def wait_for_following_visible(self):
        return self["following"].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_for_you_visible(self):
        if self["foryou"].wait_for_visible(timeout=3, raise_error=False):
            return self["foryou"].wait_for_visible(timeout=3, raise_error=False)
        elif self["foryou_new"].wait_for_visible(timeout=3):
            return self["foryou_new"].wait_for_visible(timeout=3, raise_error=False)

    def check_rec_card_title(self):
        if (self['recommend_card_title'].wait_for_visible(timeout=3, raise_error=False)):
            return self['recommend_card_title'].text == 'Trending creators'
        return False

    def is_rec_card_exist(self):
        return self['recommend_card_title'].wait_for_visible(timeout=3, raise_error=False)

    def check_rec_card_desc(self):
        if (self['recommend_card_desc'].wait_for_visible(timeout=3, raise_error=False)):
            return self['recommend_card_desc'].text == 'Follow an account to see their latest videos here.'
        return False

    def wait_for_rec_card_photo_visible(self):
        return self['recommend_card_photo'].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_rec_card_nicky_name_visible(self):
        return self['recommend_card_nicky_name'].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_rec_card_real_name_visible(self):
        return self['recommend_card_real_name'].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_rec_card_follow_btn_visible(self):
        return self['recommend_card_follow_btn'].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_rec_card_close_btn(self):
        return self['recommend_card_close_btn'].wait_for_visible(timeout=3, raise_error=False)

    def find_video_out_follow(self):
        while self.feed_intercept_interaction_view.is_followed_visible():
            ui_scene = UIScene(self.app).dump()
            self.app.testcase.log_record("ui_scene", attachments={ui_scene: ui_scene})
            self.swipe_up()

    def check_right_swipe_success(self):
        return self.feed_intercept_interaction_view.is_followed_visible()

    def wait_for_follow_btn_visible(self):
        return self["follow_btn_1"].wait_for_visible(timeout=3, raise_error=False)

    def feed_follow_wait_for_disappear(self):
        print(self["feed_follow"].wait_for_disappear(timeout=5))

    def creator_name_click(self):
        self.feed_intercept_interaction_view.creator_name_click()

    def comment_username_click(self):
        self["comment_username_label"].click()

    def comment_user_photo_click(self):
        self["comment_user_photo"].click()

    def get_feed_view(self):
        attr = {}
        feed_view = self['feed_pass_view']
        username = self.get_feed_avatarname()
        attr[username] = {}
        relation_btn = feed_view.get_relation_bth()
        relation_label = feed_view.get_relation_label()
        if relation_btn.get_not_interested_btn().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["tuxbtn"] = relation_btn.get_not_interested_btn().text
        if relation_btn.get_tt_relation_btn().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["relationbtn"] = relation_btn.get_tt_relation_btn().text
        if relation_label.get_hollowable_lable().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["hollowable"] = relation_label.get_hollowable_lable().text
        if relation_label.get_yy_label().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["yy"] = relation_label.get_yy_label().text
        if relation_label.get_normal_reason_label().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["normal"] = relation_label.get_normal_reason_label().text
        if relation_label.get_avatar_view().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["avatar"] = relation_label.get_avatar_view().visible
        return attr

    def get_relation_label(self):
        return self["relation_label"]

    def get_relation_bth(self):
        return self['relation_bth']

    def get_feed_nickname(self):
        # print('visible',self['relation_feed_name'].visible)
        return self['relation_feed_name'].text

    def get_feed_avatarname(self):
        if self['relation_feed_avatar_name_new'].wait_for_visible():
            user_name = self['relation_feed_avatar_name_new'].elem_info.get("label")
            if user_name != "":
                return user_name
        # logger.info("relation_feed_avatar_name 未找到")
        # if self['relation_feed_avatar_name_new'].wait_for_existing(timeout=2, raise_error=False):
        #     return self['relation_feed_avatar_name_new'].elem_info.get("label")
        # else:
        #     logger.info("relation_feed_avatar_name_new 未找到")
        #     self['relation_feed_avatar_name'].elem_info.get("label")
        #     return ""

    def get_feed_relation_attr(self):
        attr = {}
        feed_view = self['feed_pass_view']
        relation_label = feed_view.get_relation_label()
        relation_btn = self.get_relation_bth()
        username = self.get_feed_avatarname()
        attr[username] = {}
        if relation_btn.get_not_interested_btn().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["tuxbtn"] = relation_btn.get_not_interested_btn().text
        if relation_btn.get_tt_relation_btn().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["relationbtn"] = relation_btn.get_tt_relation_btn().text
        if relation_label.get_hollowable_lable().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["hollowable"] = relation_label.get_hollowable_lable().text
        if relation_label.get_yy_label().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["yy"] = relation_label.get_yy_label().text
        if relation_label.get_normal_reason_label().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["normal"] = relation_label.get_normal_reason_label().text
        if relation_label.get_avatar_view().wait_for_existing(timeout=3, raise_error=False):
            attr[username]["avatar"] = relation_label.get_avatar_view().visible
        return attr

    def get_live_video_view(self):
        return self['feed_intercept_interaction_view'].visible

    def enter_comment(self, content):
        device = self.app.get_device()
        device.send_keys(content)
        time.sleep(3)

    def send_friend_by_long_press_panel(self, friend, create_group=False, long_press_panel_friend=False,
                                        undo_flag=False):
        """
        :param friend: friend,
        :type:str
        :param create_group: create group flag
        :type:bool
        :param long_press_panel_friend: true-click friend sent,false-click more friends enter search list
        :type:bool
        :param undo_flag
        :type:bool
        """
        flag = False
        for _ in Retry(limit=15, interval=1):
            if long_press_panel_friend:
                for item in self["long_press_friends_list"].items():
                    if item.type == "AWEIMLongPressChatCell":
                        if item["friend_name"].text == friend[0]:
                            if not item.visible:
                                self["long_press_friends_list"].swipe(y_direction=1, swipe_coefficient=2)
                                time.sleep(3)
                            x, y = item.rect.center[0], item.rect.center[1]
                            logger.info(f"current item point:{(x, y)}")
                            self.app.testcase.device.click(x, y)
                            time.sleep(1)
                            if undo_flag:
                                self.app.testcase.device.click(x, y)
                            send_result = item["sent_btn"].wait_for_visible(timeout=8, raise_error=False)
                            flag = True
                            break
            else:
                if self["more_friends"].wait_for_visible(timeout=1, raise_error=False):
                    break
            if flag:
                break
            self["long_press_friends_list"].swipe(y_direction=1, swipe_coefficient=3 if long_press_panel_friend else 8)
            time.sleep(3)
        else:
            logger.info(f'{self["more_friends"]} not found')
        if long_press_panel_friend:
            self.device_click_point(location="top", offset_y=100)  # 点击屏幕上半部分，移除长按面板
            return False if send_result is None else send_result
        self["long_press_friends_list"].swipe(y_direction=1, swipe_coefficient=3)
        self["more_friends"].wait_for_visible(timeout=5)
        self["more_friends"].click()
        send_to_panel = SendToPanel(root=self.app)
        send_to_panel.select_user_and_send(friend, create_group)

    def get_nick_name(self):
        nick_name = ""
        if self["panel_controller"].existing:
            self["panel_controller"].refresh()
            self["panel_controller"].wait_for_ui_stable()
        if self["feed_user_name"].wait_for_existing(timeout=3, raise_error=False):
            nick_name = self["feed_user_name"].text
        else:
            nick_control = Control(root=self.app, path=UPath(type_ == "AWEUserNameLabel") / 0)
            if nick_control.existing:
                nick_name = nick_control.text
        logger.info(f"nick name:{nick_name}")
        return nick_name

    def click_send(self):
        self.send.wait_for_visible(timeout=5)
        self.send.click()

    def find_target_msg(self, msg_type):
        find_flag = False
        if "live_event" in msg_type:
            self.following.click()
            self.swipe_up()
        for _ in Retry(limit=5, interval=1, raise_error=False):
            for _ in Retry(limit=15, interval=1, raise_error=False):
                if "poi" in msg_type:
                    if self.feed_poi_contrl.wait_for_visible(timeout=3, raise_error=False):
                        self.feed_poi_contrl.click()
                        find_flag = True
                        break
                elif "hashtag" in msg_type:
                    if self.feed_description.wait_for_visible(timeout=3, raise_error=False) \
                        and not self.see_more.existing:
                        text = self.feed_description.text
                        logger.info(f"des:{text}")
                        if "#" in text:
                            start = text.find("#")
                            logger.info(f"current desc:{text}, hashtag:{text[start:start + 3]}")
                            self.feed_description.click_text_span(start, start + 3)
                            time.sleep(3)
                            if self.feed_description.wait_for_visible(timeout=2, raise_error=False):
                                self.click_text_rect()
                            find_flag = True
                            break
                elif "live_event" in msg_type:
                    if self.poi_anchor_title.wait_for_visible(timeout=3, raise_error=False):
                        if "LIVE Event" in self.poi_anchor_title.text:
                            self.poi_anchor_title.click()
                            time.sleep(5)
                            webview = Control(root=self.app, path=UPath(type_ == "WKContentView"))
                            rect = webview.rect
                            self.app.testcase.device.click(rect.right - 20, rect.top + 20)
                            find_flag = True
                            break
                self.swipe_up()

            if find_flag:
                logger.info(f"find expect {msg_type}")
                break
            self.into_home_page()

    def click_text_rect(self):
        for i in range(4):
            rect = self.feed_description.rect
            if i == 0:
                self.app.testcase.device.click(rect.left + 15, rect.top + 10)
            elif i == 1:
                self.app.testcase.device.click(rect.right - 15, rect.top + 10)
            elif i == 2:
                self.app.testcase.device.click(rect.left + 15, rect.bottom - 10)
            elif i == 3:
                self.app.testcase.device.click(rect.right - 15, rect.bottom - 10)
            if not self.feed_description.wait_for_visible(timeout=3, raise_error=False):
                break

    def click_toast(self, toast_text):
        """
        click toast
        :param toast_text:[Sent to, 发送] or Sent to
        :type:list or str
        """
        if self.ice_pop_up.wait_for_visible(timeout=10, raise_error=False):
            if isinstance(toast_text, list) and len(toast_text) > 1:
                toast_text_zh = toast_text[0]
                toast_text_us = toast_text[1]
                if toast_text_zh in self.ice_pop_up.text or toast_text_us in self.ice_pop_up.text:
                    self.ice_pop_up.click()
            else:
                if toast_text in self.ice_pop_up.text:
                    self.ice_pop_up.click()
            time.sleep(3)
        else:
            return False

    def enter_homepage(self):
        nick_control = Control(root=self.app, path=UPath(type_ == "AWEUserNameLabel") / 0)
        if self["feed_user_name"].wait_for_existing(timeout=3, raise_error=False):
            self.feed_user_name.click()
        elif nick_control.existing:
            nick_control.click()

    def check_repost_locate(self, ab_value, share_panel):
        if ab_value == 0:
            for _ in Retry(limit=8, interval=1, raise_error=True):
                if share_panel["repost_item"].wait_for_visible(timeout=5, raise_error=False):
                    repost_result = share_panel["repost_item"].children[0].children[0].children[1]
                    logger.info(f"item text:{repost_result.text}")
                    share_panel["repost_item"].click()
                    self.confirm_repost_guidance()
                    return True
                else:
                    self.check_repost_video()
        elif ab_value == 1 or ab_value == 2:
            for _ in Retry(limit=8, interval=1, raise_error=True):
                if share_panel.get_share_channel("Repost") is not None:
                    repost_result = share_panel.get_share_channel("Repost")
                    logger.info(f"Repost locate:{repost_result[1]}")
                    repost_result[0].click()
                    self.confirm_repost_guidance()
                    return True if ab_value == 2 else repost_result[1] == 0
                else:
                    self.check_repost_video()
        elif ab_value == 3 or ab_value == 4:
            for _ in Retry(limit=8, interval=1, raise_error=False):
                if share_panel.get_share_action("Save video") is not None and share_panel.get_share_action(
                    "Repost") is not None:
                    save_result = share_panel.get_share_action("Save video")
                    repost_result = share_panel.get_share_action("Repost")
                    logger.info(f"Repost locate:{repost_result[1]}, Save video locate:{save_result[1]}")
                    repost_result[0].click()
                    self.confirm_repost_guidance()
                    if ab_value == 3:
                        return repost_result[1] == 0
                    return repost_result[1] == save_result[1] - 1
                else:
                    self.check_repost_video()
        return False

    def check_repost_video(self):
        logger.info("current video no repost")
        device = self.app.testcase.device
        screen_size = device.screen_rect
        device.click(screen_size.center[0], screen_size.center[1])
        time.sleep(1)
        self.swipe_up()
        self.swipe_up_till_normal_video()
        self.find_share()
        self.share()
        time.sleep(1)

    def confirm_repost_guidance(self):
        if self["repost_btn"].wait_for_visible(timeout=5, raise_error=False):
            self["repost_btn"].click()

    def check_repost_result(self):
        return self["tv_repost_status"].wait_for_visible(timeout=15)

    def click_tako_icon(self):
        for _ in Retry(limit=6, interval=1, raise_error=False):
            self.swipe_up_till_normal_video()
            self.app.testcase.take_screenshot(self.app.testcase.device, "feed_tikbot_icon.jpg")
            if self.tako_icon.wait_for_visible(timeout=3, raise_error=False):
                rect = self.tako_icon.rect
                logger.info(f"click point:{(rect.center[0], rect.top + 5)}")
                self.app.testcase.device.click(rect.center[0], rect.top + 5)
                break
            elif self.tako_icon_two.wait_for_visible(timeout=3, raise_error=False):
                self.tako_icon_two.click()
                break
            self.app.restart()

    def click_toko_disclaimer_operation(self, action=1):
        """
        action:1-click confirm, 2-click quit, 3-slide right, 4-click close
        """
        if self.toko_disclaimer_confirm.wait_for_visible(timeout=5, raise_error=False):
            if action == 1:
                self.tako_disclaimer_confirm.click()
            elif action == 2:
                self.tako_disclaimer_quite.click()
            elif action == 4:
                self.tako_disclaimer_close.click()
            else:
                device = self.app.testcase.device
                device.drag(0, device.screen_rect.height / 2, device.screen_rect.width / 1.5,
                            device.screen_rect.height / 2, duration=3)

    def click_kids_draft(self):
        time.sleep(4)
        return self["kids_draft"].click()

    def click_share_video_toast(self, toast_txt):
        try:
            if self["pop_up"].wait_for_visible(timeout=10, raise_error=False):
                if toast_txt in self["pop_up"].text:
                    self["pop_up"].click()
                    time.sleep(3)
        except UIAmbiguousError as e:
            logger.info(e)
            self.handle_uiambiguous_error(toast_txt, e)


class CommentAtFriends_list(Control):
    elem_class = Control
    elem_path = UPath(type_ == 'AWECommentSearchCell')


class LongPressChat(Control):
    def get_locators(self):
        return {
            "send_btn": {"type": Control, "path": UPath(type_ == 'UIButton', label_ == "Send", id_ == "(actionButton)",
                                                        visible_ == True)},
            "friend_name": {"type": Control, "path": UPath(id_ == "titleLabel")},
            "sent_btn": {"type": Control, "path": UPath(id_ == "actionButton", label_ == "Message")},
        }


class LongPressChatList(Control):
    elem_class = LongPressChat
    elem_path = UPath(type_ == 'AWEIMLongPressChatCell')


class Action_list(Control):
    elem_class = Control
    elem_path = UPath(type_ == 'UILabel', visible_ == True)


class RelationLabel(Control):
    """Other Panel
    """
    window_spec = {"path": UPath(type_ == 'AWEFeedHybridTagViewCell')}


class FeedPassStackView(Control):
    window_spec = {"path": UPath(id_ == '(TTKFeedInteractionLeftContainerElement)')}

class RelationButton(Control):
    """Other Panel
    """
    window_spec = {"path": UPath(id_ == '(TTKFeedDualButtonsUserSuggestionElement)')}


class OtherPanel(BasePanel):
    """Other Panel
    """
    window_spec = {"path": UPath(type_ == 'UITextEffectsWindow')}

    def get_locators(self):
        return {
            "paste": {"path": UPath(type_ == 'UICalloutBarButton', label_ == '粘贴')},
            "repost": {
                "path": UPath(type_ == "UIInputSetContainerView") / UPath(type_ == "UIInputSetHostView") / UPath(
                    type_ == "TTKCommentListInputAccessoryView") / UPath(type_ == "UILabel")
            }
        }


