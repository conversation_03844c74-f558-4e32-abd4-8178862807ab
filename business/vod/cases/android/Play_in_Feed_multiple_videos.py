"""
Author: leizihui
Date: 2025-2-13
Description: 点播测试 Feed页 - 滑动播放数百个视频
"""
import time

from common.tiktok.android.panels.vod import AndroidVodPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInFeedMultipleVideos(TTCrossPlatformPerfAppTestBase):
    """
    基础性能指标 - Feed页 - 滑动播放数百个视频
    """
    owner = "leizihui"
    timeout = 10800
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "VoD-PlayInFeedMultipleVideos"
    description = "点播测试 Feed页 - 滑动播放数百个视频"
    tags = []

    # 定义固定账号信息
    fixed_account = {
        "account_iphone": "***********",  # 固定手机号
        "account_captcha": "009404",  # 固定验证码
        "account_username": "testeyzxoiomfl",  # 固定昵称
        "account_uid": "7348372772635165698"  # 固定用户ID
    }

    def play_in_feed_20mins(self, times, duration):
        """
        Feed页 - 上下滑动播放20 min
        """
        self.perf_app.restart()
        vod_panel = AndroidVodPanel(root=self.perf_app)
        vod_panel.open_home_tab()

        self.start_step("上划加载Feed流播放10min")
        vod_panel.swipe_up_times(times, duration)

        self.start_step("feed页静止")
        vod_panel.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_fixed_account(self.fixed_account)

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            operations=self.play_in_feed_20mins,
            operations_args=(500, 5)
        )


if __name__ == '__main__':
    PlayInFeedMultipleVideos().debug_run()
