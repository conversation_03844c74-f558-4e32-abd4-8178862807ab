"""
Author: leizihui
Date: 2025-7-1
Description: 点播测试 个人页收藏视频 - 同一个视频播放退出100次
"""
import time

from common.tiktok.android.panels.vod import AndroidVodPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInFavoriteBackForth100(TTCrossPlatformPerfAppTestBase):
    """
    基础性能指标 - 个人页收藏视频 - 同一个视频播放退出100次
    """
    owner = "leizihui"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "VoD-PlayInFavoriteBackForth100"
    description = "点播测试 Feed页 - 个人页收藏视频 - 同一个视频播放退出100次"
    tags = []

    # 定义固定账号信息
    fixed_account = {
        "account_iphone": "***********",  # 固定手机号
        "account_captcha": "006762",  # 固定验证码
        "account_username": "testZVYLXtvzkZ",  # 固定昵称
        "account_uid": "7348372465293493249"  # 固定用户ID
    }

    def play_in_favorite_back_and_forth_100(self, times, duration):
        """
        个人页收藏视频 - 同一个视频播放退出100次
        """
        self.perf_app.restart()
        vod_panel = AndroidVodPanel(root=self.perf_app)
        vod_panel.open_profile_tab()
        vod_panel.favorite_tab()

        for i in range(times):
            vod_panel.favorite_2nd_video()
            time.sleep(duration)
            vod_panel.exit_profile_video()

        self.start_step("feed页静止")
        vod_panel.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_fixed_account(self.fixed_account)

        # # self.switch_ppe_environment("ppe_wuzhihao_feedmock")
        # self.enable_link_to_ET()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            device_ip=self.perf_device_ip,
            udid=self.perf_udid,
            operations=self.play_in_favorite_back_and_forth_100,
            operations_args=(100, 10)
        )


if __name__ == '__main__':
    PlayInFavoriteBackForth100().debug_run()
