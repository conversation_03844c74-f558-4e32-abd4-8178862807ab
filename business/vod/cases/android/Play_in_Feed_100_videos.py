"""
Author: leizihui
Date: 2025-7-1
Description: 点播测试 Feed页 - 播放100个随机视频
"""
import time

from common.tiktok.android.panels.vod import AndroidVodPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInFeed100Videos(TTCrossPlatformPerfAppTestBase):
    """
    基础性能指标 - Feed页 - 播放100个随机视频
    """
    owner = "leizihui"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "VoD-PlayInFeed100Videos"
    description = "点播测试 Feed页 - 播放100个随机视频"
    tags = []

    # 定义固定账号信息
    fixed_account = {
        "account_iphone": "***********",  # 固定手机号
        "account_captcha": "009404",  # 固定验证码
        "account_username": "testeyzxoiomfl",  # 固定昵称
        "account_uid": "7348372772635165698"  # 固定用户ID
    }

    def Play_In_Feed_100_Videos(self, times, duration):
        """
        Feed页 - 播放100个视频
        """
        self.perf_app.restart()
        vod_panel = AndroidVodPanel(root=self.perf_app)
        vod_panel.open_home_tab()

        self.start_step("上划加载Feed流播放100个视频")
        vod_panel.swipe_up_times(times, duration)

        self.start_step("feed页静止")
        vod_panel.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        # self.switch_ppe_environment("ppe_wuzhihao_feedmock")
        # self.enable_link_to_ET()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            device_ip=self.perf_device_ip,
            udid=self.perf_udid,
            operations=self.Play_In_Feed_100_Videos,
            operations_args=(100, 10)
        )


if __name__ == '__main__':
    PlayInFeed100Videos().debug_run()
