"""
Author: leizihui
Date: 2025-7-1
Description: 点播测试 个人页收藏视频 - 同一个视频播放退出100次
"""
import time

from business.vod.common.ios.vod_feed_panel import *
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInFavoritebackforth100ios(TTCrossPlatformPerfAppTestBase):
    """
    基础性能指标 - 个人页收藏视频 - 同一个视频播放退出100次
    """
    owner = "leizihui"
    timeout = 3600
    platform = [PlatformType.IOS]
    device_count = 1
    title = "VoD-PlayInFavoriteBackForth100ios"
    description = "点播测试 Feed页 - 个人页收藏视频 - 同一个视频播放退出100次"
    tags = []

    def play_in_favorite_back_forth_100times(self, times, duration):
        """
        个人页收藏视频 - 同一个视频播放退出100次
        """
        self.perf_app.restart()
        vod_feed = VodPanelIOS(root=self.perf_app)
        vod_feed.open_profile_tab()
        vod_feed.favorite_tab()
        for i in range(times):
            vod_feed.favorite_2nd_video()
            time.sleep(duration)
            vod_feed.exit_profile_video()

        self.start_step("feed页静止")
        vod_feed.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            device_ip=self.perf_device_ip,
            udid=self.perf_udid,
            operations=self.play_in_favorite_back_forth_100times,
            operations_args=(100, 10)
        )


if __name__ == '__main__':
    PlayInFavoritebackforth100ios().debug_run()
