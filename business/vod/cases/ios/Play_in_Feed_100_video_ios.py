"""
Author: leizihui
Date: 2025-7-2
Description: 点播测试 Feed页 - 播放100个随机视频
"""

from business.vod.common.ios.vod_feed_panel import *
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInFeed100VideosIOS(TTCrossPlatformPerfAppTestBase):
    """
    基础性能指标 - Feed页 - 播放100个随机视频 - iOS
    """
    owner = "leizihui"
    timeout = 3600
    platform = [PlatformType.IOS]
    device_count = 1
    title = "VoD-PlayInFeed100Videos-ios"
    description = "点播iOS测试 Feed页 - 播放100个随机视频"
    tags = []

    def Play_In_Feed_100_Videos_iOS(self, times, duration):
        """
        Feed页 - 播放100个视频
        """
        self.perf_app.restart()
        vod_feed = VodPanelIOS(root=self.perf_app)
        vod_feed.into_home_page()

        self.start_step("播放100个视频")
        vod_feed.swipe_up_times(times, duration)

        self.start_step("feed页静止")
        vod_feed.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            device_ip=self.perf_device_ip,
            udid=self.perf_udid,
            operations=self.Play_In_Feed_100_Videos_iOS,
            operations_args=(100, 10)
        )


if __name__ == '__main__':
    PlayInFeed100VideosIOS().debug_run()
