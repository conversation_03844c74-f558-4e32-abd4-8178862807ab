"""
Author: leizihui
Date: 2025-2-25
Description: 点播测试 Feed页 - 上下滑动播放20 min
"""

from business.vod.common.ios.vod_feed_panel import *
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInFeed20MinsIOS(TTCrossPlatformPerfAppTestBase):
    """
    基础性能指标 - Feed页 - 上下滑动播放20 min - iOS
    """
    owner = "leizihui"
    timeout = 3600
    platform = [PlatformType.IOS]
    device_count = 1
    title = "VoD-PlayInFeed20Mins-ios"
    description = "点播iOS测试 Feed页 - 上下滑动播放20 min"
    tags = []

    def play_in_feed_20mins(self, times, duration):
        """
        Feed页 - 上下滑动播放20 min
        """
        self.perf_app.restart()
        vod_feed = VodPanelIOS(root=self.perf_app)
        vod_feed.into_home_page()

        self.start_step("上划加载Feed流播放10min")
        vod_feed.swipe_up_times(times, duration)

        # self.start_step("快速下划5次")
        # vod_feed.swipe_down_times(5,2)
        #
        # self.start_step("下划加载Feed流播放10min")
        # vod_feed.swipe_down_times(times, duration)
        #
        # self.start_step("快速上划20次")
        # vod_feed.swipe_up_times(20,2)

        self.start_step("feed页静止")
        vod_feed.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        self.switch_ppe_environment("ppe_wuzhihao_feedmock")
        self.enable_link_to_ET()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            operations=self.play_in_feed_20mins,
            operations_args=(40, 20)
        )


if __name__ == '__main__':
    PlayInFeed20MinsIOS().debug_run()
