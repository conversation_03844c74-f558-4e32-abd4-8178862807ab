"""
Author: leizihui
Date: 2025-7-1
Description: 点播测试 个人页视频 - 滑动播放一百个
"""
import time

from business.vod.common.ios.vod_feed_panel import *
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class PlayInProfileScroll100ios(TTCrossPlatformPerfAppTestBase):
    """
    基础性能指标 - 个人页收藏视频 - 滑动播放一百个
    """
    owner = "leizihui"
    timeout = 3600
    platform = [PlatformType.IOS]
    device_count = 1
    title = "VoD-PlayInProfileScroll100ios"
    description = "点播测试 Feed页 - 个人页视频 - 滑动播放一百个"
    tags = []

    def Play_in_Profile_scroll_100_ios(self, times, duration):
        """
        个人页视频 - 滑动播放一百个
        """
        self.perf_app.restart()
        vod_feed = VodPanelIOS(root=self.perf_app)
        vod_feed.open_profile_tab()
        vod_feed.profile_first_video()
        vod_feed.swipe_up_times(times, duration)

        self.start_step("feed页静止")
        vod_feed.vod_pause()

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        # # self.switch_ppe_environment("ppe_wuzhihao_feedmock")
        # self.enable_link_to_ET()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            device_ip=self.perf_device_ip,
            udid=self.perf_udid,
            operations=self.Play_in_Profile_scroll_100_ios,
            operations_args=(100, 10)
        )


if __name__ == '__main__':
    PlayInProfileScroll100ios().debug_run()
