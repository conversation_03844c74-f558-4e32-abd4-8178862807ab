'''
Author: w<PERSON><PERSON><PERSON>.wzh
Date: 2025-2-06
Description: 第一次的测试，我们主要是为了测试一下，看看点播安卓的case是否可行
'''
from common.tiktok.android.panels.vod import AndroidVodPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *

class VodAndroidSlideTest(TTCrossPlatformPerfAppTestBase):
    """
    GameLiveBroadCast
    """
    owner = "wuzhihao.wzh"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "点播测试用例安卓滑动"
    description = "点播测试用例，测试安卓滑动是否可以"
    tags = []
    
    def slide_home_times(self, times=100):
        """
        首页上滑指定次数
        """
        vod_panel = AndroidVodPanel(root=self.perf_app)
        vod_panel.slide_home_times(times)

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        self.start_step("开始采集性能数据")
        self.collect_perf_data(
            app=self.perf_app,
            operations=self.slide_home_times,
            operations_args=(100,)
        )


if __name__ == '__main__':
    VodAndroidSlideTest().debug_run()
