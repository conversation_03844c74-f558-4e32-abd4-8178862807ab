'''
Author: liulei.32
Date: 2025-03-26 14:09:22
FilePath: /global_business_perf/business/ttlh/cases/android/anchor_game_golive.py
Description:
'''
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class GameLiveBroadCast(TTCrossPlatformPerfAppTestBase):
    """
    GameLiveBroadCast
    """
    owner = LIULEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 1
    title = "开播-游戏-720p"
    description = "开播-游戏-720p"
    tags = []

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        self.start_step("主播开启游戏直播")
        self.start_live_broadcast(self.perf_app, live_type="game")
        self.assert_anchor_live_broadcast(self.perf_app)

        self.start_step("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)


if __name__ == '__main__':
    GameLiveBroadCast().debug_run()
