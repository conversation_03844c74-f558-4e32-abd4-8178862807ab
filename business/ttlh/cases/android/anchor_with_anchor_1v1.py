"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 11:36:48
FilePath: /global_business_perf/business/global_rtc/cases/anchor_co_anchor_1v1.py
Description: 主播多档位选档开播测试
"""
import time

from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *
from business.ttlh.utils.android.M2LAndroidLiveUI import HostLivePanel
from business.ttlh.utils.android.Login import LoginPanel
import asyncio

class AnchorVsAnchor1v1(TTCrossPlatformPerfAppTestBase):
    """
    AnchorCoAnchor_1V1
    """
    owner = HANXIN
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 2
    title = "[已废弃]主播连麦1v1"
    description = ""
    tags = []

    def run_test(self):
        # 获取第一个辅助设备的数据
        auxil_device = self.auxil_device_list[0]
        auxil_app = self.auxil_app_list[0]

        loginpage = LoginPanel(root=self.perf_app)
        loginpage.login(account="***********", code="006815")
        loginpage2 = LoginPanel(root=auxil_app)
        loginpage2.login(account="***********", code="006815")


        self.start_step("主播开启直播")
        hostpage=HostLivePanel(root=self.perf_app)
        hostpage.start_live(wait_time=10)

        guestpage=HostLivePanel(root=auxil_app)
        guestpage.start_live(wait_time=10)


        self.start_step("主播A与主播B连麦")
        time.sleep(20)
        hostpage.co_host_invite(be_invited_user_name="Landen-15")

        guestpage.host_accept_co_host()
        self.start_step("性能采集场景截图")
        self.screenshot_all_devices()

        self.start_step("性能采集设备开始无线连接")
        perf_wireless_udid = self.wireless_connect(self.perf_udid, self.perf_device_ip)
        self.assert_(f"{self.perf_udid} 性能设备 无线连接 断言失败", perf_wireless_udid)

        # self.start_step("停止充电")
        # self.serial_control.relay_switch(self.perf_device_serial_port, False)

        self.start_step("开始记录性能数据")
        perf_data = self.start_perf_collect(self.perf_app,
                                            device_ip=self.perf_device_ip,
                                            udid=self.perf_udid)
        self.assert_("性能采集失败", perf_data)

        # self.start_step("开始充电")
        # self.serial_control.relay_switch(self.perf_device_serial_port, True)

        self.start_step("性能采集设备断开无线连接")
        is_wireless_disconnect = self.wireless_disconnect(self.perf_udid)
        self.assert_(f"{self.perf_udid} 性能设备 断开无线连接 断言失败", is_wireless_disconnect)

        self.start_step("关闭直播")
        hostpage.end_live()
        guestpage.end_live()
        # self.close_live_broadcast(self.perf_app)
        # self.close_live_broadcast(auxil_app)

        self.start_step("处理性能数据")
        self.handle_perf_data(perf_data)


if __name__ == '__main__':
    AnchorVsAnchor1v1().debug_run()