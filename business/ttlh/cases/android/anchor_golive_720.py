'''
Author: liulei.32
Date: 2025-03-26 14:09:22
FilePath: /global_business_perf/business/ttlh/cases/android/anchor_golive_720.py
Description:
'''
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *
from business.ttlh.utils.android.M2LAndroidLiveUI import *
from business.ttlh.utils.android.Login import LoginPanel

class AnchorNetworkLiveBroadCast(TTCrossPlatformPerfAppTestBase):
    """直播场景测试用例"""
    owner = LIULEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 1
    title = "开播-秀场-720p"
    description = "开播-秀场-720p"
    tags = []

    def run_test(self):
        self.start_step("登录账号")
        self.login_all_devices()

        self.start_step("主播开启直播")
        #page = HostLivePanel(root=self.perf_app)
        #page.start_live(choose_quality=True, quality='720p', wait_time=5)
        self.start_live_broadcast(self.perf_app, live_type="camera", video_quality="720p")
        self.assert_anchor_live_broadcast(self.perf_app)

        self.start_step("开始采集基础性能数据")
        self.collect_perf_data(app=self.perf_app)
if __name__ == '__main__':
    AnchorNetworkLiveBroadCast().debug_run()
