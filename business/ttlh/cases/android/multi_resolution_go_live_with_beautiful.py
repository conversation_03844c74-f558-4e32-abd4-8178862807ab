"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 11:36:48
FilePath: /global_business_perf/business/global_rtc/cases/anchor_co_anchor_1v1.py
Description: 主播多档位选档开播测试
"""
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *
from business.ttlh.utils.android.M2LAndroidLiveUI import *
from business.ttlh.utils.android.Login import LoginPanel

class GoliveWithbeautiful(TTCrossPlatformPerfAppTestBase):
    """
    Golive
    """
    owner = HANXIN
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "[已废弃]主播选档720p默认美颜开播"
    description = ""
    tags = []

    def run_test(self):
        self.start_step('登录测试账号')
        loginpage=LoginPanel(root=self.perf_app)
        loginpage.login(account="***********",code="006815")

        self.start_step("主播开启直播")
        page = HostLivePanel(root=self.perf_app)
        page.start_live(choose_quality=False,wait_time=5)

        self.start_step("开启美颜")
        page.switch_beautiful()


        self.start_step("性能采集场景截图")
        self.screenshot_all_devices()

        self.start_step("性能采集设备开始无线连接")
        perf_wireless_udid = self.wireless_connect(self.perf_udid, self.perf_device_ip)
        self.assert_(f"{self.perf_udid} 性能设备 无线连接 断言失败", perf_wireless_udid)

        self.start_step("停止充电")
        self.serial_control.relay_switch(self.perf_device_serial_port, False)

        self.start_step("开始记录性能数据")
        perf_data = self.start_perf_collect(self.perf_app,
                                            device_ip=self.perf_device_ip,
                                            udid=self.perf_udid)
        self.assert_("性能采集失败", perf_data)

        self.start_step("开始充电")
        self.serial_control.relay_switch(self.perf_device_serial_port, True)

        self.start_step("性能采集设备断开无线连接")
        is_wireless_disconnect = self.wireless_disconnect(self.perf_udid)
        self.assert_(f"{self.perf_udid} 性能设备 断开无线连接 断言失败", is_wireless_disconnect)

        self.start_step("关闭美颜并且关闭直播")
        page.switch_beautiful()
        self.start_step("关闭直播")
        page.close_live()

        self.start_step("处理性能数据")
        self.handle_perf_data(perf_data)


if __name__ == '__main__':
    GoliveWithbeautiful().debug_run()