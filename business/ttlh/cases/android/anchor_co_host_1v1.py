'''
Author: liulei.32
Date: 2025-03-26 14:09:22
FilePath: /global_business_perf/business/ttlh/cases/android/anchor_co_host_1v1.py
Description:
'''
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *


class AnchorCoAnchor_1V1(TTCrossPlatformPerfAppTestBase):
    """
    AnchorCoAnchor_1V1
    """
    owner = LIULEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 2
    title = "主播连麦-1V1"
    description = "主播连麦-1V1"
    tags = []

    def run_test(self):
        self.start_step("获取辅助设备应用")
        auxil_app = self.auxil_app_list[0]

        self.start_step("登录账号")
        self.login_all_devices()

        self.start_step("主播开启直播")
        self.start_live_broadcast(self.perf_app)
        self.assert_anchor_live_broadcast(self.perf_app)
        self.start_live_broadcast(auxil_app)
        self.assert_anchor_live_broadcast(auxil_app)

        self.start_step("主播A与主播B连麦")
        self.invite_anchor_connect(auxil_app, self.perf_account["account_username"])
        self.accept_anchor_connect(self.perf_app)
        self.assert_anchor_connect(self.perf_app, assert_type="1v1")

        self.start_step("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)


if __name__ == '__main__':
    AnchorCoAnchor_1V1().debug_run()
