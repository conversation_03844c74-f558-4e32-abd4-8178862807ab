from shoots.data_driven import DataDriven
from bytedance.ds import PerfDeviceType
from common.tiktok.android.app import AndroidTik<PERSON><PERSON><PERSON>pp
from common.tiktok.android.base import TTAndroidPerfAppTestBase
from common.tiktok.ios.app import iOSTikTokApp
from common.tiktok.ios.base import TTiOSPerfAppTestBase
from common.tiktok.android.actions.anchor_connect_anchor_action import *
from common.tiktok.android.actions.anchor_connect_guest_action import *
from common.tiktok.android.actions.game_live_broadcast_action import *
from common.tiktok.android.actions.live_broadcast_action import *
from common.tiktok.android.actions.user_login_action import *
from common.tiktok.android.asserts.anchor_connect_anchor_assert import *
from common.tiktok.android.asserts.anchor_connect_guest_assert import *
from common.tiktok.android.asserts.anchor_game_live_broadcast_assert import *
from common.tiktok.android.asserts.anchor_live_broadcast_assert import *
from common.tiktok.android.asserts.user_login_assert import *
from common.tiktok.ios.actions.anchor_connect_anchor_action import *
from common.tiktok.ios.actions.anchor_connect_guest_action import *
from common.tiktok.ios.actions.game_live_broadcast_action import *
from common.tiktok.ios.actions.live_broadcast_action import *
from common.tiktok.ios.actions.user_login_action import *
# iOS assert functions are now in panels modules
from common.tiktok.ios.panels.anchor_host import iOSAnchorHostPanel
from common.tiktok.ios.panels.guest_host import iOSGuestHostPanel
from common.tiktok.ios.panels.live import iOSLivePanel
from common.tiktok.ios.panels.login import iOSLoginPanel
from defines import PlatformType
from utils.common.file_utils import clean_file, get_work_dir, get_task_json


@DataDriven([get_task_json()])
class TTLHPerfTestBase(TTAndroidPerfAppTestBase, TTiOSPerfAppTestBase):
    """跨平台测试基类"""

    def __init__(self, *args, **kwargs):
        super(TTLHPerfTestBase, self).__init__(*args, **kwargs)

        # 初始化性能设备相关属性
        self.perf_device = None
        self.perf_app = None
        self.perf_platform = None  # PlatformType.ANDROID or PlatformType.IOS

        # 初始化辅助设备相关属性
        self.auxil_device_list = []
        self.auxil_app_list = []
        self.auxil_platforms = []  # [PlatformType.ANDROID, PlatformType.IOS, ...]

    def screenshot_all_devices(self, save_dir=None):
        """对所有设备进行截图
        Args:
            save_dir: 截图保存目录
        """
        if save_dir is None:
            save_dir = get_work_dir()
        # 性能测试设备截图
        self.perf_device.screenshot(save_dir)

        # 所有辅助设备截图
        for device in self.auxil_device_list:
            device.screenshot(save_dir)

    def _check_platform_support(self, platform_type):
        """检查平台是否支持
        Args:
            platform_type: PlatformType 枚举值
        Raises:
            ValueError: 如果平台不支持则抛出异常
        """
        if platform_type not in [PlatformType.ANDROID, PlatformType.IOS]:
            platform_name = PlatformType.get_name(platform_type)
            raise ValueError(f"暂不支持 {platform_name} 平台")

    def pre_test(self):
        """测试前置准备"""
        self.start_step("获取数据驱动数据")
        self.setup_driven_data()

        # 初始化性能测试设备
        self.start_step("初始化性能测试设备")
        perf_device_type = self.device_group["perf_device"].get("sys_type", PlatformType.ANDROID)
        self.perf_platform = perf_device_type

        if perf_device_type == PlatformType.ANDROID:
            self.perf_device = self.android_init_device({"udid": self.perf_udid})
            self.perf_device_ip = self.perf_device.ip
            self.perf_app = self.android_init_app(self.perf_device)
        elif perf_device_type == PlatformType.IOS:
            self.perf_device = self.ios_init_device({"udid": self.perf_udid})
            self.perf_app = self.ios_init_app(self.perf_device)
        else:
            platform_name = PlatformType.get_name(perf_device_type)
            raise ValueError(f"暂不支持 {platform_name} 平台")

        # 初始化所有辅助设备
        # self.start_step("初始化辅助设备")
        # for idx, auxil_device_data in enumerate(self.auxil_devices):
        #     device_type = auxil_device_data.get("sys_type", PlatformType.ANDROID)
        #     if device_type == PlatformType.ANDROID:
        #         auxil_device = self.android_init_device({"udid": auxil_device_data["device_udid"]})
        #         auxil_app = self.android_init_app(auxil_device)
        #         self.android_control.close_auto_rotate_screen(auxil_device_data["device_udid"])
        #     elif device_type == PlatformType.IOS:
        #         auxil_device = self.ios_init_device({"udid": auxil_device_data["device_udid"]})
        #         auxil_app = self.ios_init_app(auxil_device)
        #     else:
        #         platform_name = PlatformType.get_name(device_type)
        #         raise ValueError(f"暂不支持 {platform_name} 平台的辅助设备")
        #
        #     self.auxil_device_list.append(auxil_device)
        #     self.auxil_app_list.append(auxil_app)
        #     self.auxil_platforms.append(device_type)

        # 关闭性能设备自动旋转屏幕(仅Android)
        if self.perf_platform == PlatformType.ANDROID:
            self.android_control.close_auto_rotate_screen(self.perf_udid)

        # 重启所有应用
        self.start_step("重启应用")
        if self.perf_platform == PlatformType.ANDROID:
            self.perf_app.restart()
        for auxil_app in self.auxil_app_list:
            auxil_app.restart()

        # 登录所有账号
        self.start_step("登录账号")
        self._login_perf_account()
        # self._login_auxil_accounts()

    def _login_perf_account(self):
        """登录性能测试设备账号"""
        if self.perf_platform == PlatformType.IOS:
            ios_user_captcha_login(self.perf_app, self.perf_device,
                                   self.perf_account_iphone,
                                   self.perf_account_captcha,
                                   self.perf_account_username)
            self.assert_(f"{self.perf_udid} 性能设备 用户登录场景 断言失败",
                         ios_login_success_assert(self.perf_app))
        else:
            android_user_captcha_login(self.perf_app,
                                       self.perf_account_iphone,
                                       self.perf_account_captcha,
                                       self.perf_account_username)
            self.assert_(f"{self.perf_udid} 性能设备 用户登录场景 断言失败",
                         android_login_success_assert(self.perf_app))

    def _login_auxil_accounts(self):
        """登录辅助设备账号"""
        for idx, (auxil_app, platform) in enumerate(zip(self.auxil_app_list, self.auxil_platforms)):
            auxil_account = self.auxil_accounts[idx]
            if platform == PlatformType.IOS:
                ios_user_captcha_login(auxil_app, self.auxil_device_list[idx],
                                       auxil_account["account_iphone"],
                                       auxil_account["account_captcha"],
                                       auxil_account["account_username"])
                self.assert_(f"{self.auxil_devices[idx]['device_udid']} 辅助设备 用户登录场景 断言失败",
                             ios_login_success_assert(auxil_app))
            else:
                android_user_captcha_login(auxil_app,
                                           auxil_account["account_iphone"],
                                           auxil_account["account_captcha"],
                                           auxil_account["account_username"])
                self.assert_(f"{self.auxil_devices[idx]['device_udid']} 辅助设备 用户登录场景 断言失败",
                             android_login_success_assert(auxil_app))

    def _check_app_platform(self, app, method_name):
        """检查应用平台类型是否支持
        Args:
            app: 应用实例
            method_name: 方法名称，用于错误提示
        Raises:
            ValueError: 如果平台不支持则抛出异常
        """
        if not isinstance(app, (AndroidTikTokApp, iOSTikTokApp)):
            raise ValueError(f"{method_name} 方法暂不支持该平台的应用")
            
           
        
    def start_live_broadcast(self, app, is_game=False):
        """开启直播"""
        self._check_app_platform(app, "start_live_broadcast")
        if isinstance(app, AndroidTikTokApp):
            if is_game:
                android_home_page_start_game_live_broadcast(app, self.perf_app)
            else:
                android_home_page_live_broadcast(app)
        elif isinstance(app, iOSTikTokApp):
            if is_game:
                ios_home_page_start_game_live_broadcast(app, self.perf_app)
            else:
                ios_home_page_live_broadcast(app)
        else:
            raise ValueError(f"start_live_broadcast 方法暂不支持该平台的应用类型: {type(app)}")

    def assert_live_broadcast(self, app, udid, is_game=False):
        """断言直播是否成功"""
        self._check_app_platform(app, "assert_live_broadcast")
        if isinstance(app, AndroidTikTokApp):
            if is_game:
                self.assert_(f"{udid} 性能设备 开启游戏直播场景 断言失败",
                             android_game_live_broadcast_success_assert(app))
            else:
                self.assert_(f"{udid} 性能设备 开启直播场景 断言失败",
                             android_live_broadcast_success_assert(app))
        elif isinstance(app, iOSTikTokApp):
            ios_live_panel = iOSLivePanel(root=app)
            if is_game:
                self.assert_(f"{udid} 性能设备 开启游戏直播场景 断言失败",
                             ios_live_panel.assert_game_live_broadcast_success())
            else:
                self.assert_(f"{udid} 性能设备 开启直播场景 断言失败",
                             ios_live_panel.assert_live_broadcast_success())
        else:
            raise ValueError(f"assert_live_broadcast 方法暂不支持该平台的应用类型: {type(app)}")

    def close_live_broadcast(self, app, is_game=False):
        """关闭直播"""
        self._check_app_platform(app, "close_live_broadcast")
        if isinstance(app, AndroidTikTokApp):
            if is_game:
                android_live_room_page_anchor_close_game_live_stream(app)
            else:
                android_live_room_page_close_live_stream(app)
        elif isinstance(app, iOSTikTokApp):
            ios_live_room_page_close_live_stream(app)
        else:
            raise ValueError(f"close_live_broadcast 方法暂不支持该平台的应用类型: {type(app)}")

    def invite_anchor_connect(self, app, anchor_name):
        """邀请主播连麦"""
        self._check_app_platform(app, "invite_anchor_connect")
        if isinstance(app, AndroidTikTokApp):
            android_live_room_page_invite_anchor_connect(app, anchor_name)
        elif isinstance(app, iOSTikTokApp):
            ios_live_room_page_invite_anchor_connect(app, anchor_name)
        else:
            raise ValueError(f"invite_anchor_connect 方法暂不支持该平台的应用类型: {type(app)}")

    def accept_anchor_connect(self, app):
        """接受主播连麦"""
        self._check_app_platform(app, "accept_anchor_connect")
        if isinstance(app, AndroidTikTokApp):
            android_live_room_page_anchor_accept_anchor_connect(app)
        elif isinstance(app, iOSTikTokApp):
            ios_live_room_page_anchor_accept_anchor_connect(app)
        else:
            raise ValueError(f"accept_anchor_connect 方法暂不支持该平台的应用类型: {type(app)}")

    def assert_anchor_connect_1v1(self, app, udid):
        """断言主播1V1连麦是否成功"""
        self._check_app_platform(app, "assert_anchor_connect_1v1")
        if isinstance(app, AndroidTikTokApp):
            self.assert_(f"{udid} 性能设备 主播与主播1V1连麦场景 断言失败",
                         android_anchor_co_anchor_1v1_assert(app))
        elif isinstance(app, iOSTikTokApp):
            ios_anchor_host_panel = iOSAnchorHostPanel(root=app)
            self.assert_(f"{udid} 性能设备 主播与主播1V1连麦场景 断言失败",
                         ios_anchor_host_panel.assert_anchor_host_connect_1v1())
        else:
            raise ValueError(f"assert_anchor_connect_1v1 方法暂不支持该平台的应用类型: {type(app)}")

    def search_and_enter_live_room(self, app, anchor_name):
        """搜索并进入直播间"""
        self._check_app_platform(app, "search_and_enter_live_room")
        if isinstance(app, AndroidTikTokApp):
            android_home_page_search_account_enter_live_broadcast(app, anchor_name)
        elif isinstance(app, iOSTikTokApp):
            ios_home_page_search_account_enter_live_broadcast(app, anchor_name)
        else:
            raise ValueError(f"search_and_enter_live_room 方法暂不支持该平台应用类型: {type(app)}")

    def guest_apply_connect(self, app):
        """嘉宾申请连麦"""
        self._check_app_platform(app, "guest_apply_connect")
        if isinstance(app, AndroidTikTokApp):
            android_live_room_page_guest_apply_connect(app)
        elif isinstance(app, iOSTikTokApp):
            ios_live_room_page_guest_apply_connect(app)
        else:
            raise ValueError(f"guest_apply_connect 方法暂不支持该平台的应用类型: {type(app)}")

    def anchor_agree_guest_connect(self, app):
        """主播同意嘉宾连麦"""
        self._check_app_platform(app, "anchor_agree_guest_connect")
        if isinstance(app, AndroidTikTokApp):
            android_live_room_page_anchor_agree_guest_connect(app)
        elif isinstance(app, iOSTikTokApp):
            ios_live_room_page_anchor_agree_guest_connect(app)
        else:
            raise ValueError(f"anchor_agree_guest_connect 方法暂不支持该平台的应用类型: {type(app)}")

    def guest_confirm_connect(self, app):
        """嘉宾确认连麦"""
        self._check_app_platform(app, "guest_confirm_connect")
        if isinstance(app, AndroidTikTokApp):
            android_live_room_page_guest_confirm_connect(app)
        elif isinstance(app, iOSTikTokApp):
            ios_live_room_page_guest_confirm_connect(app)
        else:
            raise ValueError(f"guest_confirm_connect 方法暂不支持该平台的应用类型: {type(app)}")

    def assert_guest_connect_1v1(self, app, udid):
        """断言嘉宾1V1连麦是否成功"""
        self._check_app_platform(app, "assert_guest_connect_1v1")
        if isinstance(app, AndroidTikTokApp):
            self.assert_(f"{udid} 性能设备 主播与嘉宾1V1连麦场景 断言失败",
                         android_anchor_co_guest_1v1_assert(app))
        elif isinstance(app, iOSTikTokApp):
            ios_guest_host_panel = iOSGuestHostPanel(root=app)
            self.assert_(f"{udid} 性能设备 主播与嘉宾1V1连麦场景 断言失败",
                         ios_guest_host_panel.assert_guest_connect_1v1())
        else:
            raise ValueError(f"assert_guest_connect_1v1 方法暂不支持该平台的应用类型: {type(app)}")

    def assert_guest_connect_1v3(self, app, udid):
        """断言嘉宾1V3连麦是否成功"""
        self._check_app_platform(app, "assert_guest_connect_1v3")
        if isinstance(app, AndroidTikTokApp):
            self.assert_(f"{udid} 性能设备 主播与嘉宾1V3连麦场景 断言失败",
                         android_anchor_co_guest_1v3_assert(app))
        elif isinstance(app, iOSTikTokApp):
            ios_guest_host_panel = iOSGuestHostPanel(root=app)
            self.assert_(f"{udid} 性能设备 主播与嘉宾1V3连麦场景 断言失败",
                         ios_guest_host_panel.assert_guest_connect_1v3())
        else:
            raise ValueError(f"assert_guest_connect_1v3 方���暂不支持该平台的应用类型: {type(app)}")

    def start_perf_collect(self, app, device_ip=None, udid=None):
        """开始性能数据采集"""
        self._check_app_platform(app, "start_perf_collect")
        if isinstance(app, AndroidTikTokApp):
            return self.ds_perf_monitor.start_perf_collect(
                perf_device_type=PerfDeviceType.Android,
                device_ip=device_ip,
                package_name=app.package_name,
                collect_time=self.collect_time,
                collect_metrics=self.collect_metrics,
                collect_interval=self.collect_interval
            )
        elif isinstance(app, iOSTikTokApp):
            return self.ds_perf_monitor.start_perf_collect(
                perf_device_type=PerfDeviceType.iOS,
                bundle_id=app.bundle_id,
                udid=udid,
                collect_time=self.collect_time,
                collect_metrics=self.collect_metrics,
                collect_interval=self.collect_interval
            )
        else:
            raise ValueError(f"start_perf_collect 方法暂不支持该平台的应用类型: {type(app)}")

    def wireless_connect(self, udid, device_ip=None):
        """无线连接"""
        if self.perf_platform == PlatformType.ANDROID:
            return self.android_control.wireless_connect(udid, device_ip)
        elif self.perf_platform == PlatformType.IOS:
            return self.ios_control.wireless_connect(udid)
        else:
            platform_name = PlatformType.get_name(self.perf_platform)
            raise ValueError(f"暂不支持 {platform_name} 平台的无线连接")

    def wireless_disconnect(self, udid):
        """断开无线连接"""
        if self.perf_platform == PlatformType.IOS:
            return self.ios_control.wireless_disconnect(udid)
        elif self.perf_platform == PlatformType.ANDROID:
            return self.android_control.wireless_disconnect(udid)
        else:
            platform_name = PlatformType.get_name(self.perf_platform)
            raise ValueError(f"暂不支持 {platform_name} 平台的无线连接断开")

    def handle_perf_data(self, perf_data, **kwargs):
        """处理性能数据
        Args:
            perf_data: 性能数据
            **kwargs: 额外的性能数据，如 frame_rate_sent_data 等
        """
        # 处理额外的性能数据
        if kwargs.get("frame_rate_sent_data"):
            frame_rate_sent_data = self.perf_parse.get_frame_rate_sent(perf_data, self.perf_account_uid)
            kwargs["frame_rate_sent_data"] = frame_rate_sent_data

        # 处理主性能数据
        self.perf_parse.perf_data_handler(perf_data, **kwargs)

    def post_test(self):
        """测试结束处理"""
        # 清理无用数据
        clean_file(get_work_dir(), patterns=[
            "shoots-ios_*.log",
            "shoots_android_*.log",
            "shoots_cv_*.log",
            "*_Device_*.log",
            "*_sys.log",
        ])