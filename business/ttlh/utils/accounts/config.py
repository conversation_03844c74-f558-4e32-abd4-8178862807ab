# -*- coding: utf8 -*-

"""
1. website: https://ttat-us.byteintl.net/api/tools/account_management/test_account_pool/*
2. 说明文档 https://bytedance.feishu.cn/wiki/wikcnCrAokfqTHQ4EP97M0xE1Db#b9Quck
"""

nebula_user_token = "c8429fe9694a7d432bb25856675293ae"
# TTLSSDKHostAccounts
nebula_host_collection_id = "7320179475860165650"
# TTLSSDKGuestAccounts
nebula_guest_collection_id = "7320166793211810824"
# TTLSSDKViewerAccounts
nebula_viewer_collection_id = "7320167067221496850"

nebula_collection_ids = {
    "stability": {
        "host": "7320179475860165650",    # TTLSSDKHostAccounts
        "guest": "7320166793211810824",   # TTLSSDKGuestAccounts
        "viewer": "7320167067221496850"   # TTLSSDKViewerAccounts
    },
    "experiment": {
        "default": "7425474091479471111"   # TTLSSDKFunctionalUIAccounts
    }
}


sg_host = "https://ttugqa-sg.tiktok-row.org/"

