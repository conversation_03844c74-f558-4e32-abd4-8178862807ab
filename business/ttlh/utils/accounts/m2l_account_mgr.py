# -*- coding: utf8 -*-

from business.ttlh.utils.accounts.config import *
from business.ttlh.utils.accounts.nebula_sdk import TiktokUGQAOpenApi
from shoots from utils.common.log_utils from utils.common.log_utils import logger
from enum import Enum


class AccountType(Enum):
    VIEWER = "Viewer"
    HOST = "Host"
    GUEST = "Guest"


class Account:
    """
    账号类
    """

    def __init__(self, account_dict):
        self.account_inited = False
        if account_dict:
            self.account_id = account_dict["account_id"]
            self.app_id = account_dict["appid"]
            self.collect_id = account_dict["collect_id"]
            self.create_time = account_dict["create_time"]
            self.did = account_dict["did"]
            self.mobile = account_dict["mobile"]
            self.pwd = account_dict["password"]
            self.session_id = account_dict["sessionid"]
            self.sm_code = account_dict["sm_code"]
            self.tags = account_dict["tags"][0]
            self.test_data = account_dict["test_data"]
            self.uid = account_dict["uid"]
            self.update_time = account_dict["update_time"]
            self.use_user = account_dict["use_user"]

            self.account_inited = True

    def __str__(self):
        return str(self.account_id) if self.account_id else ""


class M2LAccountManager:
    """账号管理类，管理平台上的账号
        tag: 机房位置 [sg,ttp,...]
    """

    def __init__(self, account_type, task_type="stability"):
        self.account_type = account_type
        self.task_type = task_type
        if self.task_type == "stability":   # 稳定性自动化测试
            collection_ids = nebula_collection_ids["stability"]
            if account_type == AccountType.VIEWER:
                self.collection_id = collection_ids["viewer"]
                self.client = TiktokUGQAOpenApi(user_token=nebula_user_token)
            elif account_type == AccountType.HOST:
                self.collection_id = collection_ids["host"]
                self.client = TiktokUGQAOpenApi(user_token=nebula_user_token)
            elif account_type == AccountType.GUEST:
                self.collection_id = collection_ids["guest"]
                self.client = TiktokUGQAOpenApi(user_token=nebula_user_token)
            else:
                self.collection_id = 0
                self.client = TiktokUGQAOpenApi(user_token="")
        elif self.task_type == "experiment":  # 功能UI自动化, 暂不区分角色
            collection_ids = nebula_collection_ids["experiment"]
            self.collection_id = collection_ids["default"]
            self.client = TiktokUGQAOpenApi(user_token=nebula_user_token)
        else:
            self.collection_id = 0
            self.client = TiktokUGQAOpenApi(user_token="")
        self.pool = []

    def batch_occupy_accounts(self, count, location=""):
        """
        批量获取账号
        :param location:
        :param count:
        :return:
        """
        accounts = []
        accounts = self.client.get_occupy_account(collect_id=self.collection_id, num=count).get("data")
        logger.info("get accounts :%s" % accounts)
        return accounts

    def occupy_account(self, location="sg"):
        """
        获取并占用一个account
        :param location:
        :return: account
        """
        account = None
        collection_id = self.collection_id
        resp = self.client.get_occupy_account(tag=location, collect_id=collection_id)
        if not resp["data"]:
            return None
        if len(resp["data"]) > 0:
            account = resp["data"][0]
            self.pool.append(account["account_id"])
        return account

    def release_all_account(self):
        """
        释放collections下的所有账号
        :return:
        """
        collection_id = self.collection_id
        resp = self.client.release_account(collect_id=collection_id)
        self.pool = []
        return resp

    def release_account_by_id(self, account_id):
        """
        根据id释放账号
        :param account_id:
        :return:
        """
        resp = self.client.release_account(account_id=account_id,collect_id=self.collection_id)
        self.pool.remove(account_id)
        return resp

    def get_available_account_by_location(self, location):
        """
        获取
        :param location:
        :return:
        """
        collection_id = self.collection_id
        resp = self.client.get_account(tag=location, collect_id=collection_id)
        if len(resp["data"]) > 0:
            return [Account(i) for i in resp["data"]]
        return []

    def clean_pool(self):
        """
        清空用到的所有账号
        :return:
        """
        for account_id in self.pool:
            self.release_account_by_id(account_id)


def get_account_mgr(account_type, task_type="stability"):
    """返回账号管理对象
    :param task_type: 自动化任务类型, 避免不同任务类型账号混用
    :param account_type:
    :return:
    """
    return M2LAccountManager(account_type, task_type)


if __name__ == "__main__":
    # 遍历清除
    for at in AccountType:
        print("AccountType: %s" % at.name)
        # 获取账号类型
        account_mgr = get_account_mgr(at)

        # Clean accupy
        account_mgr.release_all_account()
