import json
import logging
import traceback
from servicediscovery import requests
from testlib.account.config import sg_host

logger = logging.getLogger("__name__")


class TiktokUGQAOpenApi:
    def __init__(self, user_token):
        self.host = sg_host

        self.user_token = user_token

        self.params = {"user_token": self.user_token}

    def add_account_collect(self, collect_name: str, collect_group: str, collect_desc: str = None):
        method_name = self.add_account_collect.__name__
        try:
            if collect_desc is None:
                collect_desc = collect_name
            params = {"collect_name": collect_name, "collect_desc": collect_desc, "collect_group": collect_group}
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/collect/add".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.post(url, json=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 获取集合
    def get_account_collect(self, collect_name: str = None, collect_id: int = None):
        method_name = self.get_account_collect.__name__
        try:
            params = {"collect_name": collect_name, "collect_id": collect_id}
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/collect/get".format(host=self.host)

            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.get(url, params=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 更新集合
    def update_account_collect(self, collect_id: int, collect_name: str = None, collect_group: str = None,
                               collect_desc: str = None):
        method_name = self.update_account_collect.__name__
        try:
            params = {"collect_id": collect_id, "collect_name": collect_name, "collect_desc": collect_desc,
                      "collect_group": collect_group}
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/collect/update".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.post(url, json=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 删除集合，集合下账号逻辑删除，标签删除
    def delete_account_collect(self, collect_id: int):
        method_name = self.delete_account_collect.__name__
        try:
            params = {"collect_id": collect_id}
            url = "{host}/ttqa/account/collect/delete".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.post(url, json=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 添加账号到集合中
    def add_account(self, collect_id: int = None, collect_name: str = None, appid: str = None,
                    mobile: str = None, tags: list = None, uid: str = None,
                    did: str = None, password: str = None, sessionid: str = None,
                    sm_code: str = None):
        method_name = self.add_account.__name__
        try:
            if not isinstance(tags, list):
                raise Exception("tags shoud be a list")
            params = {"collect_id": collect_id, "collect_name": collect_name,
                      "mobile": mobile, "appid": appid, "tags": tags, "uid": uid,
                      "did": did, "password": password, "sessionid": sessionid,
                      "sm_code": sm_code}
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/add".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.post(url, json=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 更新账号
    def update_account(self, account_id: int, collect_name: str = None, appid: str = None,
                       mobile: str = None, tags: list = None, uid: str = None,
                       did: str = None, password: str = None, sessionid: str = None,
                       sm_code: str = None):
        method_name = self.update_account.__name__
        try:
            if not isinstance(tags, list):
                raise Exception("tags shoud be a list")
            params = {"account_id": account_id, "collect_name": collect_name,
                      "mobile": mobile, "appid": appid, "tags": tags, "uid": uid,
                      "did": did, "password": password, "sessionid": sessionid,
                      "sm_code": sm_code}
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/update".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.post(url, json=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 逻辑删除账号
    def delete_account(self, account_id: int):
        method_name = self.delete_account.__name__
        try:
            params = {"account_id": account_id}
            url = "{host}/ttqa/account/delete".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.post(url, json=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 获取账号
    def get_account(self, account_id: int = None, collect_id: int = None, collect_name: str = None, mobile: str = None,
                    appid: str = None, tag: str = None, uid: str = None, did: str = None, status: str = None):
        method_name = self.get_account.__name__
        try:
            params = {
                "account_id": account_id, "collect_id": collect_id, "collect_name": collect_name,
                "mobile": mobile, "appid": appid, "tag": tag,
                "uid": uid, "did": did, "status": status
            }
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/get".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.get(url, params=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 获取并占有账号
    def get_occupy_account(self, account_id: int = None, collect_id: int = None, collect_name: str = None,
                           mobile: str = None, appid: str = None, tag: str = None,
                           uid: str = None, did: str = None, num: int = 1):
        method_name = self.get_occupy_account.__name__
        try:
            params = {
                "account_id": account_id, "collect_id": collect_id, "collect_name": collect_name,
                "mobile": mobile, "appid": appid, "tag": tag,
                "uid": uid, "did": did, "num": num
            }
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/get_occupy".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.get(url, params=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise

    # 释放账号
    def release_account(self, account_id: int = None, collect_id: int = None, collect_name: str = None,
                        mobile: str = None, appid: str = None, tag: str = None,
                        uid: str = None, did: str = None):
        method_name = self.release_account.__name__
        try:
            params = {
                "account_id": account_id,
                "collect_id": collect_id,
                "collect_name": collect_name,
                "mobile": mobile,
                "appid": appid,
                "tag": tag,
                "uid": uid,
                "did": did
            }
            params = {k: v for k, v in params.items() if v is not None}
            url = "{host}/ttqa/account/release".format(host=self.host)
            params = {
                **params,
                **self.params
            }
            logger.info(f"{method_name} request_url=%s, params={params}")
            resp = requests.post(url, json=params)
            logger.info(" resp_text=%s", resp.text)
            logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            resp_json = json.loads(resp.text)
            return resp_json
        except Exception:
            msg = f"{method_name} has exception, " + traceback.format_exc()
            logger.error(msg)
            raise


if __name__ == "__main__":
    # pass
    client = TiktokUGQAOpenApi()
    res = client.get_account_collect()
    print(res)
