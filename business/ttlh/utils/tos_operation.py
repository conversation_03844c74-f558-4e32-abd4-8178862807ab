# -*- coding: utf8 -*-

import bytedtos

from business.ttlh.utils import config as con


class TosClient(object):
    """ToS SDK
    """

    def __init__(self, access_key, **kwargs):
        """初始化
        """
        self.access_key = access_key
        self.kwargs = kwargs

        self.client = TosClient.get_client(self.access_key, **self.kwargs)

    @staticmethod
    def get_client(access_key, **kwargs):
        """
        :param access_key:
        :param kwargs:
        :return:
        """
        access_key = access_key

        bucket_name = kwargs.get("bucket_name", con.TOS_BUCKET)
        cluster = kwargs.get("cluster", con.DEFAULT_CLUSTER)
        endpoint = kwargs.get("endpoint", con.TOS_ENDPOINT)
        service_name = kwargs.get("service", con.TOS_API_SERVICE_NAME)
        idc = kwargs.get("idc", con.TOS_IDC)

        return bytedtos.Client(bucket_name, access_key,
                               service=service_name,
                               cluster=cluster,
                               idc=idc,
                               endpoint=endpoint)

    def download(self, file_name):
        """下载
        :param file_name:
        :return:
        """
        resp = self.client.get_object(file_name)
        data = resp.data

        return data


if __name__ == "__main__":
    """
    """
    client = TosClient("B1VI7Y7P6ODAX5AHFBG2")
    res = client.download("ttmock.json")
    print(res)
