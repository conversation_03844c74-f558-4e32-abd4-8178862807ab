# -*- coding: utf8 -*-

import subprocess


class FFmpegOperation:
    """Bits oplatform的容器默认集成ffmpeg/ffplay等工具
    """

    @staticmethod
    def video_transcoding(src_file, dest_file):
        """无损视频转码
        :param src_file:
        :param dest_file:
        :return:
        """
        # -crf参数: 表示视频比特率，数值越小视频的清晰度越高
        # 注: iOS不加-crf参数时, 转码后会出现模糊问题(转码前无明显异常)
        subprocess.run(
            f"ffmpeg -i {src_file} -c:v libx264 -crf 23 -preset slow -c:a copy {dest_file} && rm {src_file}",
            shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    @staticmethod
    def extract_frames(video_path, output_path, fps=1, output_pattern=None):
        """视频分帧方式
        :param output_path: 视频分帧后图片存在的地址
        :param video_path: 录制视频文件
        :param fps: 设置帧率，可以提取每秒的特定帧数
        :param output_pattern: 输出的图片名称
        :return:
        """
        if output_pattern is None:
            output_pattern = "%04d.jpg"

        subprocess.run(
            f"cd {output_path} && ffmpeg -i {video_path} -vf fps={fps} {output_pattern}",
            shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)



