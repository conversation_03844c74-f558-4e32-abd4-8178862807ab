# -*- coding: utf8 -*-

import threading
from datetime import timedelta


class MultiThread(threading.Thread):
    """多线程通用类
    """

    def __init__(self, func, args):
        """
        :param func:
        :param args:
        """
        # threading.Thread.__init__(self)
        super(MultiThread, self).__init__()
        self.result = None
        self.func = func
        self.args = args

    def run(self) -> None:
        """
        :return:
        """
        self.result = self.func(*self.args)

    def get_result(self):
        """重写线程，获取线程return返回值
        :return:
        """
        # 等待线程执行完毕
        threading.Thread.join(self)
        try:
            return self.result
        except Exception:
            return None


def start_task(task_infos, is_get_result=True):
    """
    :param is_get_result: 是否等待各子线程返回结果
    :param task_infos: eg (func, (arg1, arg2, ...))
    :return:
    """
    # 多线程任务
    tasks = []
    for func, args in task_infos:
        task = MultiThread(func, args)
        # 启动线程运行
        task.start()

        tasks.append(task)

    # 等待所有子线程执行完毕并输出结果
    for task in tasks:
        task.join()

    # 获取所有子线程的返回结果
    res = [task.get_result() for task in tasks] if is_get_result else []

    return res


def seconds_to_time_string(seconds):
    """
    :param seconds:
    :return:
    """
    # 创建一个 timedelta 对象
    td = timedelta(seconds=int(seconds))

    # 获取小时、分钟和秒
    total_seconds = int(td.total_seconds())
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    # 格式化为 HH:MM:SS
    return f"{hours:02}:{minutes:02}:{seconds:02}"
