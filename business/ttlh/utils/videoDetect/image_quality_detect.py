import cv2, os
import numpy as np
from concurrent.futures import ThreadPoolExecutor


class ImageQualityDetect(object):
    """基于cv2的图片质量检测方法
    """

    def __init__(self):
        """初始化
        """
        pass

    @staticmethod
    def check_green_image(image, threshold=0.3):
        """绿屏检测
        将图像从 RGB 转换为 HSV 颜色空间, 检查绿色通道的值
        :param threshold: 阈值
        :param image: 图片数据矩阵
        :return:
        """
        # 转换为 HSV 颜色空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        # 定义绿色的阈值范围
        lower_green = np.array([40, 40, 40])
        upper_green = np.array([80, 255, 255])
        # 掩膜
        mask = cv2.inRange(hsv, lower_green, upper_green)
        # 计算绿色区域的比例
        green_area = cv2.countNonZero(mask)
        total_area = image.shape[0] * image.shape[1]

        score = float(green_area) / total_area

        return {
            "score": score,
            "is_abnormal": True if score > threshold else False
        }

    @staticmethod
    def check_black_image(image, threshold=30):
        """黑屏检测
        通过计算图像的平均亮度值来检测, 平均亮度值越低, 表示画面趋于黑屏
        :param threshold: 黑屏阈值
        :param image: 图片数据矩阵
        :return:
        """
        # 计算图像的平均亮度值
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        score = np.mean(gray)

        return {
            "score": score,
            "is_abnormal": True if score < threshold else False
        }

    @staticmethod
    def detect_abnormal(image_path, detect_type=None):
        """
        :param image_path:
        :return:
        """
        if detect_type is None:
            detect_type = ["check_green", "check_black"]
        elif isinstance(detect_type, str):
            detect_type = [detect_type]

        if not os.path.exists(image_path):
            return {}

        res = {}
        image = cv2.imread(image_path)
        for d_type in detect_type:
            if d_type == "check_green":
                res[d_type] = ImageQualityDetect.check_green_image(image)
            elif d_type == "check_black":
                res[d_type] = ImageQualityDetect.check_black_image(image)

        return {
            "image": image_path,
            "result": res
        }

    @staticmethod
    def process_images(image_folder, detect_type=None):
        """图片
        :param image_folder:
        :return:
        """
        image_files = [f for f in os.listdir(image_folder) if f.endswith(('.jpg', '.jpeg', '.png'))]

        results = []
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []

            for image_file in image_files:
                image_path = os.path.join(image_folder, image_file)
                futures.append(
                    executor.submit(ImageQualityDetect.detect_abnormal, image_path, detect_type)
                )

            # 多进程
            for future in futures:
                results.append(future.result())

        return results


if __name__ == "__main__":
    # 测试
    directory = "/Users/<USER>/Documents/workspace/code/test-livelinkmicsdk/ttlivetest/m2l/utils/videoDetect/imgs"
    res = ImageQualityDetect.process_images(directory, detect_type=None)

    print(res)

