
# -*- coding: utf8 -*-
import json
import time, random, os

from business.ttlh.utils import config as con
from business.ttlh.utils import common as com
from business.ttlh.utils.ffmpeg_operate import FFmpegOperation
from business.ttlh.utils.accounts.m2l_account_mgr import *

from business.ttlh.utils.android.M2LAndroidBase import M2LAndroidBase
from business.ttlh.utils.ios.M2LiOSBase import M2LiOSBase

from business.ttlh.utils.ios.M2LiOSTTApp import M2LiOSTTLiveApp
from business.ttlh.utils.android.M2LAndroidTTApp import M2LAndroidTTApp
from business.ttlh.utils.videoDetect.image_quality_detect import ImageQualityDetect


def account_login(characters, sleep_time=5):
    """测试账号登陆
    M2L自动化测试账号管理池: https://ttat-us.byteintl.net/api/tools/account_management/test_account_pool/*
    :return:
    """
    if not isinstance(characters, list):
        characters = [characters]

    logger.info("Login for all characters")
    task_infos = []
    for character in characters:
        if character is None:
            logger.info("The character object is None...")
            continue

        logger.info("Test account login for device:%s, character:%s" % (character["device"].type, character["role"]))
        # 账号占用
        resp = character["account_mgr"].occupy_account(location='')
        if resp is None:  # 若异常, 直接释放可占用账号(存在风险: 已占用账号会重复被占用)
            logger.info(
                "All %s accounts have been accupyed, release_all_account before occupy_account" % character["role"])
            character["account_mgr"].release_all_account()
            resp = character["account_mgr"].occupy_account(location='')

        # 校验是否已登录
        if not character["live_obj"].check_login():
            logger.info("Test account login, account:%s" % resp["mobile"])
            # character["live_obj"].login(phone=resp["mobile"], sm_code=resp["sm_code"])
            task = (character["live_obj"].login, (resp["mobile"], resp["sm_code"]))
            task_infos.append(task)
        else:
            # 获取当前已登录账号uid
            uid = character["live_obj"].get_uid()
            logger.info("[%s]: logined, uid:%s" % (character["device"].name, uid))

            # 若已登录账号与占用账号不一致时, 释放账号占用, 避免资源浪费
            if uid != resp["uid"]:
                logger.info("[%s]: release account occupy, mobile:%s" % (character["device"].name, resp["mobile"]))
                character["account_mgr"].clean_pool()

    # 多线程登录
    com.start_task(task_infos, is_get_result=False)
    time.sleep(sleep_time)


def init_obj(device, account_type, task_type="stability"):
    """Init Test Case Obj
    :param task_type: 自动化任务类型, 避免不同任务类型账号混用
    :param device:
    :param account_type: AccountType Enum类型
    :return:
    """
    logger.info("%s: Init LinkMic & UIOperation Obj..." % device.type)
    if device.type == "Android":
        live_obj = M2LAndroidBase(M2LAndroidTTApp(device))
        device_id = live_obj.get_did()
        return {
            "device": device,
            "live_obj": live_obj,
            "account_mgr": get_account_mgr(account_type, task_type),
            "device_id": device_id,
            "role": account_type.name
        }
    elif device.type == "iOS":
        live_obj = M2LiOSBase(M2LiOSTTLiveApp(device))
        # live_obj = M2LiOSBase(iOSTTLiveApp(device))
        device_id = live_obj.get_did()
        return {
            "device": device,
            "live_obj": live_obj,
            "account_mgr": get_account_mgr(account_type, task_type),
            "device_id": device_id,
            "role": account_type.name
        }
    else:
        raise KeyError("device type error! @%s" % device.name)


def start_recording(characters, case_name="default"):
    """设备录制
    :param case_name:
    :param characters:
    :return:
    """
    task_infos = []
    for character in characters:
        logger.info("[%s]: start recording..." % character["device"].serial)
        video_file = "%s_%s_%s_%s.mp4" % (
            character["device"].type, character["role"], character["device"].serial, case_name)
        task = (character["device"].start_recording, (video_file,))

        task_infos.append(task)

    com.start_task(task_infos, is_get_result=False)


def stop_recording(characters, case_name="default", is_transcode=False):
    """停止录制
    :param is_transcode:
    :param case_name:
    :param characters:
    :return:
    """
    # 暂停录制并转码
    for character in characters:
        logger.info("[%s]: stop recording..." % character["device"].serial)
        character["device"].stop_recording()

        # 无须转码, 转码耗时较长, 可本地ffmpeg下载查看
        if not is_transcode:
            continue

        file_suffix = "%s_%s_%s_%s" % (
            character["device"].type, character["role"], character["device"].serial, case_name)
        src_video_file = "%s.mp4" % file_suffix
        dst_video_file = "%s_tran.mp4" % file_suffix
        logger.info(
            "[%s]: video_transcoding from %s to %s..." % (character["device"].serial, src_video_file, dst_video_file))

        FFmpegOperation.video_transcoding(src_video_file, dst_video_file)


class M2LLiveBase(object):
    """M2L Android&iOS 通用方法
    """

    # 分辨率档位
    quality_size = {
        "1080": [1080, 1920],
        "720": [720, 1280],
        "540": [576, 1024],
        "480": [480, 864]
    }

    def __init__(self):
        """Init
        :return:
        """
        # 角色列表
        self.characters = {}
        # case执行信息词典
        self.detect_info = {}

    # ------------------------------------ Basic Method ------------------------------------

    def init_character(self, devices, host=1, guest=0, viewer=0, task_type="stability"):
        """Init LIVE Character Obj
        :param task_type: 自动化任务类型
        :param devices:
        :param host: 主播数
        :param guest: 角色数
        :param viewer: 观众数
        :return:
        """
        logger.info("Init LIVE Character Obj...")
        if not isinstance(devices, list):
            devices = [devices]

        assert len(devices) >= (host + guest + viewer)

        k = 0
        for account_type in AccountType:
            num = host if account_type == AccountType.HOST else (guest if account_type == AccountType.GUEST else viewer)

            self.characters[account_type] = []
            if num > 0:
                self.characters[account_type] = [init_obj(devices[i + k], account_type, task_type) for i in range(num)]
                k += num

        logger.info("LIVE Character Obj Number: %d..." % len(self.characters))

        return self.characters

    def clean_up(self, is_logout=False, wait_time=10):
        """测试账号释放
        :return:
        """
        for _, characters in self.characters.items():
            for character in characters:
                logger.info("clean account pool + logout")
                character["account_mgr"].clean_pool()

                if is_logout:
                    character["live_obj"].logout()
        # wait
        logger.info("Testcase cleanup wait for stable...")
        time.sleep(wait_time)

    def print_detect_info(self):
        """输出各角色的相关信息
        :return:
        """
        # 初始化信息
        for account_type, items in self.characters.items():
            for character in items:
                serial, os_version = character["device"].serial, character["device"].os_version
                model = character["device"].model if character["device"].type == "Android" else \
                    character["device"].detail_info["model"]
                # 获取登录后的did
                # did = character["device_id"]
                did = character["live_obj"].get_did()
                uid = character["live_obj"].get_uid()

                msg = f"{account_type.name}: {model}({os_version}), serial={serial}, did={did}, uid={uid}"
                logger.info(msg)

    # ------------------------------------ Video/Image Quality Detect ------------------------------

    @staticmethod
    def imgae_quality_detect(image_path, detect_type=None):
        """
        :param detect_type:
        :param image_path:
        :return:
        """
        # 发起检测
        resp = ImageQualityDetect.detect_abnormal(image_path, detect_type=detect_type)

        ret = {}
        for d_type, value in resp["result"].items():
            ret[d_type] = value.get("is_abnormal", False)

        logger.info(f'imgae_quality_detect: ImageQualityDetect.detect_abnormal: '
                    f'detect_type={detect_type}, '
                    f'detect_resp={json.dumps(resp)}, '
                    f'check_resp={json.dumps(ret)}'
                    )

        return ret

    @staticmethod
    def video_quality_detect(video_path, fps=1, output_pattern=None, detect_type=None):
        """
        :param detect_type:
        :param fps:
        :param video_path:
        :return:
        """
        # 同视频路径下, 创建视频分帧的目录文件地址
        output_path = str(video_path).split(".")[0]
        if not os.path.exists(output_path):
            os.mkdir(output_path)

        # 视频分帧
        FFmpegOperation.extract_frames(video_path, output_path=output_path, fps=fps, output_pattern=output_pattern)
        # 发起检测
        resp = ImageQualityDetect.process_images(video_path, detect_type=detect_type)

        # 遍历分析输出检测的结果
        ret = {
            "video": video_path,
            "is_abnormal": False,
            "time_area": [],
            "result": []
        }
        for img_dict in resp:
            if "result" not in img_dict:
                continue

            img_res = {}
            is_black, is_green = False, False
            if "check_black" in img_dict["result"]:
                is_black = img_dict["result"]["check_black"]["is_abnormal"]

                if is_black:
                    # score, is_abnormal
                    img_res["check_black"] = img_dict["result"]["check_black"]

            if "check_green" in img_dict["result"]:
                is_green = img_dict["result"]["check_green"]["is_abnormal"]

                if is_green:
                    # score, is_abnormal
                    img_res["check_green"] = img_dict["result"]["check_green"]

            # 校验最终检测结果
            if is_black or is_green:
                # 检测图片地址
                img_res["image"] = img_dict["image"]
                # 估计视频中位置
                imgae_name = img_res["image"].split("/")[-1]
                # 转为视频中的时间点
                img_res["times"] = com.seconds_to_time_string(seconds=imgae_name.split(".")[0])

                # 视频整体检测结果
                ret["is_abnormal"] = True
                # 视频内异常时间点
                ret["time_area"].append(img_res["times"])

            ret["result"].append(img_res)

        logger.info(f'video_quality_detect: ImageQualityDetect.process_images: '
                    f'detect_type={detect_type}, '
                    f'detect_resp={json.dumps(resp)}, '
                    f'check_resp={json.dumps(ret)}'
                    )

        return ret

    # ------------------------------------ Live Static Method ------------------------------

    @staticmethod
    def get_user_info_dict(character, room_id, type_list=None):
        """主播或嘉宾: 获取直播间内用户信息
        详情: https://bytedance.larkoffice.com/wiki/wikcnoh89BWd3NaEMc0M6C5ehhf
        :param character: 
        :param room_id: 
        :param type_list: 
        :return: 
        """
        # 类型
        if type_list is None:
            type_list = [1, 2, 3, 4, 5]

        # 查询麦位列表（麦上信息，申请列表，邀请列表）
        ret = character["live_obj"].get_list_by_type(room_id=room_id, type_list=type_list)
        resp = ret if character["device"].type == "Android" else ret["data"]["getListByTypeResult"]

        # user list of who has already joined linkmic
        linked_users = resp["data"].get("linked_users", [])
        # live room viewer list without applied and linked user
        candidate_and_invited_users = resp["data"].get("candidate_and_invited_users", [])
        # connecting user list
        connecting_users = resp["data"].get("connecting_users", [])

        u_infos = {}
        for item in linked_users + candidate_and_invited_users + connecting_users:
            uid = item["user"]["id_str"]
            if uid in u_infos:
                continue

            # nickname
            u_infos[uid] = item["user"]["nickname"]

        return u_infos

    @staticmethod
    def live_operation(character, scene, is_recover=False, **kwargs):
        """主播: 中断测试
        :param is_recover:
        :param character:
        :param scene:
        :param kwargs: 可变参数
        :return:
        """
        if scene in ["Background", "Foreground"]:  # 前后台切换
            if is_recover or scene == "Foreground":
                character["live_obj"].app.foreground()
            else:
                character["live_obj"].app.background()
        elif scene in ["Lock", "Unlock"]:  # 注意设备不能添加锁屏密码, 否则解锁
            if is_recover or scene == "Unlock":
                for _ in range(3):
                    character["device"].unlock()
                    time.sleep(2)
                    # 判断是否仍在锁屏状态
                    if not character["device"].is_locked():
                        break
            else:
                character["device"].lock()
        elif scene in ["Pause", "Continue"]:  # 秀场: 暂停/恢复直播
            character["live_obj"].charactor.host.pause_live(is_recover=is_recover)
        elif scene in ["Restart", "Resume"]:  # 杀死APP / 续播
            if is_recover or scene == "Resume":
                M2LLiveBase.go_live(character, video_mock=kwargs.get("video_mock", False),
                                    is_ui=kwargs.get("is_ui", True), is_resume=kwargs.get("is_ui", True))
            else:
                character["live_obj"].restart()
        elif scene in ["Connect", "Disconnect"]:  # 上/下麦
            live_type = kwargs.get("live_type", "")
            characters = kwargs.get("characters", [])

            if live_type == "multi-guest":  # 嘉宾连麦
                if is_recover or scene == "Connect":  # 重新连麦
                    room_id = kwargs.get("room_id", character["live_obj"].get_room_info()["data"]["room_id"])
                    M2LLiveBase.multi_guest_connect(room_id, character, characters)
                else:
                    M2LLiveBase.leave_channel_by_host(character, characters)
            elif live_type == "co-host":  # 主播连麦
                if is_recover or scene == "Connect":  # 重新连麦
                    M2LLiveBase.co_host_connect(character, characters)
                else:
                    character["live_obj"].disconnect_co_host()
        elif scene in ["Effect", "UnEffect"]: # 特效操作
            character["live_obj"].charactor.host.effect_dressup(is_clean=is_recover)
        elif scene in ["Flip", "UnFlip"]:  # 相机翻转
            character["live_obj"].charactor.host.camera_flip()
        elif scene in ["SwitchLayout"]: # 秀场/嘉宾连麦: 切换布局
            layout = random.choice(con.layout_scenes)
            layout_info = random.choice(con.layout_infos.get(layout, []))

            logger.info(
                    "Change Layout[%s]: %s[id=%s] ..." % (layout, layout_info[1], layout_info[0]))
            M2LLiveBase.change_guest_layout_for_multi(character, scene=layout, layout_info=layout_info)
        elif scene in ["CameraAndMicroPhone"]:  # 嘉宾连麦: 相机/麦克风 开关
            characters = kwargs.get("characters", [])
            mute_audio_v = kwargs.get("mute_audio", random.randint(0, 1))
            mute_video_v = kwargs.get("mute_video", random.randint(0, 1))
            # 随机mute操作
            mute_audio = True if int(mute_audio_v) == 1 else False
            mute_video = True if int(mute_video_v) == 1 else False

            logger.info("Multi-guest: Set mute_audio=%s,  mute_video=%s..." % (mute_audio, mute_video))
            M2LLiveBase.mute_local_audio_video_for_multi(characters, mute_audio=mute_audio, mute_video=mute_video)
        elif scene in ["Match", "UnMatch"]:   # 主播连麦: Match(PK)
            characters = kwargs.get("characters", [])

            scene = "invite" if scene == "Match" else "leave"
            M2LLiveBase.match_for_co_host(character, invite_hosts=characters, scene=scene)

    @staticmethod
    def go_live(host, video_mock=True, is_ui=True, is_resume=False, quality=""):
        """Go LIVE
        :param quality:
        :param is_resume:
        :param video_mock:
        :param host:
        :param is_ui:
        :return:
        """
        # 兜底: 校验账号账号是否登录
        if not host["live_obj"].check_login():
            logger.info("Check %s whether is login before go LIVE..." % host["device"].serial)
            try:
                # 先释放之前占用, 后申请新账号
                host["account_mgr"].clean_pool()

                # APP冷启动
                host["live_obj"].restart()
                time.sleep(7)
            finally:
                account_login(host, sleep_time=5)

        if host["live_obj"].charactor.host.is_living():
            logger.info("HOST has been in LIVE...")
            return host["live_obj"].get_room_info()

        logger.info("[%s]: set video mock for host before Go LIVE..." % host["device"].serial)
        host["live_obj"].mock_video_for_live(
            is_enable=video_mock,
            size=M2LLiveBase.quality_size.get(quality, [720, 1280])
        )
        time.sleep(2)

        # 续播走UI方式续播, API无法直接续播
        if is_resume or is_ui:
            try:
                logger.info("Go LIVE with UI Click...")
                host["live_obj"].charactor.host.go_live(is_resume=is_resume, quality=quality)
            except:
                logger.info("Fallback: Go LIVE with API...")
                host["live_obj"].start_live()
            finally:
                logger.info("HOST: get_room_info...")
                return host["live_obj"].get_room_info()

        # “18岁检测”弹窗会阻塞
        return host["live_obj"].start_live()

    @staticmethod
    def enter_room(characters, room_id):
        """
        :param characters:
        :param room_id:
        :return:
        """
        if room_id == "":
            raise Exception("room_id is NULL...")

        res = []
        if characters is not None and len(characters) > 0:
            task_infos = []
            for charactor in characters:
                task = (charactor["live_obj"].enter_room, (room_id,))
                task_infos.append(task)

            # 多线程进房
            res = com.start_task(task_infos)

        return res

    @staticmethod
    def close_live(host, is_ui=True, sleep_time=5):
        """End LIVE
        :return:
        """
        # 判断是否仍处于直播间状态
        if host["live_obj"].charactor.host.is_living():
            try:
                logger.info("[%s]: clean video mock for host before End LIVE..." % host["device"].serial)
                host["live_obj"].mock_video_for_live(is_enable=False)

                # 双端实现存在差异
                host["live_obj"].close_live(is_ui=is_ui, sleep_time=sleep_time)
            except Exception as e:
                logger.exception("Close live occur Exception: %s" % str(e))
                host["live_obj"].close_live(is_ui=(not is_ui), sleep_time=sleep_time)
        else:
            logger.info("[%s]: the host bas been End LIVE..." % host["device"].serial)

    @staticmethod
    def disconnect_co_host(host, is_ui=True):
        """Host auto disconnect co-host Live
        :param host:
        :param is_ui:
        :return:
        """
        if M2LLiveBase.check_live_status(host, scene="co-host"):
            # 双端实现存在差异
            host["live_obj"].disconnect_co_host(is_ui=is_ui)

    @staticmethod
    def leave_channel_by_host(host, guests=None, is_ui=True):
        """Host leave multi-guest channel
        :param host:
        :param guests:
        :param is_ui:
        :return:
        """
        # 主播主动断开连麦
        if is_ui:
            host["live_obj"].charactor.host.host_leave_channel()
        else:
            pass

        # 嘉宾侧关闭下麦弹窗, 避免影响后续case执行
        if guests is not None:
            for _, guest in enumerate(guests):
                guest["live_obj"].charactor.viewer.guest_confirm_disconnect()

    @staticmethod
    def check_live_status(character, scene):
        """简单校验直播间状态
        :param character:
        :param scene: 秀场go-live, 嘉宾连麦multi-guest, 主播连麦co-host
        :return:
        """
        logger.info("Check LIVE status for '%s'..." % scene)
        if scene == "co-host":  # 主播侧
            return character["live_obj"].charactor.host.is_cohost_status()
        elif scene == "multi-guest":  # 嘉宾侧
            return character["live_obj"].charactor.viewer.is_multi_guest_status()

        # 主播侧
        return character["live_obj"].charactor.host.is_living()

    @staticmethod
    def co_host_connect(host, invite_hosts, is_ui=True, wait_time=2):
        """Co-Host Connect
        :param wait_time:
        :param host:
        :param invite_hosts:
        :param is_ui:
        :return:
        """
        logger.info("Co-Host in order...")
        resp = host["live_obj"].get_usr_info()
        if "nickName" not in resp["data"]:
            user_info_dict = M2LLiveBase.get_user_info_dict(host, room_id=host["live_obj"].inner_room_id)
            host_nick_name = user_info_dict.get(resp["data"]["uid"], "")
        else:
            host_nick_name = resp["data"]["nickName"]

        for invite_host in invite_hosts:
            if M2LLiveBase.check_live_status(invite_host, scene="co-host"):
                logger.info("invite_host has been connected with Host...")
                continue

            try:
                resp = invite_host["live_obj"].get_usr_info()
                if "nickName" not in resp["data"]:
                    user_info_dict = M2LLiveBase.get_user_info_dict(invite_host, room_id=invite_host["live_obj"].inner_room_id)
                    be_invited_user_name = user_info_dict.get(resp["data"]["uid"], "")
                else:
                    be_invited_user_name = resp["data"]["nickName"]

                if is_ui:
                    logger.info(
                        "Host['%s'] send request invite Host['%s'] to Co-Host" % (host_nick_name, be_invited_user_name))
                    host["live_obj"].charactor.host.host_invite_co_host(be_invited_user_name)

                    logger.info("Host['%s'] accept to Connect..." % be_invited_user_name)
                    invite_host["live_obj"].charactor.host.host_accept_co_host()
            except Exception as e:
                logger.error("co_host_connect: Host[%s]->Host[%s] fail. Detail: %s" % (host["device_id"],
                                                                                       invite_host["device_id"], str(e)))

    @staticmethod
    def multi_guest_connect(room_id, host, guests, is_ui_apply=True, is_ui_permit=True, wait_time=5):
        """Multi-guest Connect
        :param wait_time: 申请 和 接受间隔时间, 等待网络响应
        :param is_ui_permit: 主播接受连麦的方式, UI 或 API
        :param is_ui_apply: 嘉宾申请上麦的方式, UI 或 API
        :param room_id: 主播直播间 roomid
        :param host:
        :param guests:
        :return:
        """
        # 依次申请连麦
        user_info_dict = None
        for i, guest in enumerate(guests, start=1):
            # 判断当前是否已处于连麦状态
            if M2LLiveBase.check_live_status(guest, scene="multi-guest"):
                logger.info("Guest has been connected with Host...")
                continue

            try:
                # 校验是否有主播断开连麦弹窗, 若有则取消, 避免阻断发起连麦
                guest["live_obj"].charactor.viewer.guest_confirm_disconnect()

                # 校验是否进直播间
                guest["live_obj"].enter_room(room_id=room_id)

                # 获取嘉宾信息, Android: uid+昵称, iOS: uid
                guest_info = guest["live_obj"].get_usr_info()

                logger.info("The Guest[%s] apply for multi-guest..." % guest_info["data"]["uid"])
                try:
                    guest["live_obj"].apply_for_multi("-1", is_ui=is_ui_apply)
                except Exception as e:
                    logger.debug("apply_for_multi occured Exception, detail: %s" % str(e))

                    logger.info("Fallback: is_ui = %s..." % (not is_ui_apply))
                    guest["live_obj"].apply_for_multi("-1", is_ui=(not is_ui_apply))
                finally:
                    time.sleep(wait_time)

                # iOS: 若通过get_usr_info无法获取嘉宾nickName, 则通过getListByType查询麦位列表，后根据uid获取到对应nickName
                if "nickName" not in guest_info["data"] or guest_info["data"]["nickName"] == "":
                    try:
                        # 主播通过getListByType查询麦位列表, 获取当前直播间列表内用户信息
                        if user_info_dict is None:
                            # 主播通过getListByType查询麦位列表, 获取当前直播间列表内用户信息
                            user_info_dict = M2LLiveBase.get_user_info_dict(host, room_id=room_id)

                        guest_info["data"]["nickName"] = user_info_dict.get(guest_info["data"]["uid"], "")
                    except Exception as e:
                        logger.debug("get_user_info_dict occured Exception, detail: %s" % str(e))
                        guest_info["data"]["nickName"] = ""

                logger.info(
                    "Host permit Guest[%s] apply, and wait for the popup dismiss..." % guest_info["data"]["nickName"])
                try:
                    logger.info("permit_apply_for_multi, is_ui_permit = %s ..." % is_ui_permit)
                    host["live_obj"].permit_apply_for_multi("1", room_id, guest_info, is_ui=is_ui_permit)
                except Exception as e:
                    logger.debug("permit_apply_for_multi occured Exception, detail: %s" % str(e))

                    logger.info("Fallback: permit_apply_for_multi, is_ui_permit = %s..." % (not is_ui_permit))
                    host["live_obj"].permit_apply_for_multi("1", room_id, guest_info, is_ui=(not is_ui_permit))
            except Exception as e:
                logger.error("multi_guest_connect: Host[%s]->guest[%s] fail. Detail: %s" % (host["device_id"],
                                                                                            guest["device_id"], str(e)))

    @staticmethod
    def mute_local_audio_video_for_multi(guests, mute_audio=True, mute_video=False, is_ui=True):
        """嘉宾连麦: 嘉宾侧音频 mute/unmute
        :param guests:
        :param mute_audio:
        :param mute_video:
        :param is_ui:
        :return:
        """
        for guest in guests:
            # 获取嘉宾信息
            guest_info = guest["live_obj"].get_usr_info()

                # 麦克风
            try:
                logger.info("Set Guest Audio mute status = %d..." % int(mute_audio))
                guest["live_obj"].mute_local_audio_for_multi(mute_audio, guest_info=guest_info, is_ui=is_ui)
            except Exception as e:
                logger.info("Occured Exception, Detail: %s" % str(e))

                try:
                    logger.info("Fallback with other method[is_ui=%s], Audio mute status = %d..." % (not is_ui, int(mute_audio)))
                    guest["live_obj"].mute_local_audio_for_multi(mute_audio, guest_info=guest_info, is_ui=(not is_ui))
                except Exception as e:  # 避免影响后续操作
                    logger.error("Try to set Audio mute status Fail!!! Detail: %s" % str(e))

            # 摄像头
            try:
                logger.info("Set Guest Camera mute status = %d..." % int(mute_video))
                guest["live_obj"].mute_local_video_for_multi(mute=mute_video, guest_info=guest_info, is_ui=is_ui)
            except Exception as e:
                logger.info("Occured Exception, Detail: %s" % str(e))

                try:
                    logger.info("Fallback with other method[is_ui=%s], Camera mute status = %d..." % (not is_ui, int(mute_video)))
                    guest["live_obj"].mute_local_video_for_multi(mute=mute_video, guest_info=guest_info, is_ui=(not is_ui))

                except Exception as e:  # 避免影响后续操作
                    logger.error("Try to set Camera mute status Fail!!! Detail: %s" % str(e))

    @staticmethod
    def change_guest_layout_for_multi(host, scene, is_ui=True, layout_info=None, nick_name="", **kwargs):
        """主播: 放大或缩小嘉宾画面, 且缩小需在放大前操作
        :param is_ui: True UI操作, False API 切换
        :param layout_info: 布局信息, eg: (layout_id, layout_name, max_pos)
        :param nick_name:
        :param host:
        :param scene: "Fixed Panel", "Fixed Grid", "Panel", "Fixed", "Expand" / "Shrink"
        :return:
        """
        if scene in ["Expand", "Shrink"]:  # 放大/缩小嘉宾画面
            return host["live_obj"].charactor.host.change_guest_layout_for_multi(scene, nick_name=nick_name)

        # 摄像头
        try:
            logger.info("change_guest_layout_for_multi: is_ui = %d..." % int(is_ui))
            host["live_obj"].change_guest_layout_for_multi(scene=scene, is_ui=is_ui, layout_info=layout_info)
        except Exception as e:
            logger.info("Occured Exception, Detail: %s" % str(e))

            try:
                logger.info("Fallback with other method[is_ui=%s] ..." % (not is_ui))
                host["live_obj"].change_guest_layout_for_multi(scene=scene, is_ui=(not is_ui), layout_info=layout_info)

            except Exception as e:  # 避免影响后续操作
                logger.error("Try to set change_guest_layout_for_multi Fail!!! Detail: %s" % str(e))

        return ""

    @staticmethod
    def match_for_co_host(host, invite_hosts, scene):
        """主播连麦(2/4主播): Match(PK)操作
        scene: invite / accept / leave
        :return:
        """
        if len(invite_hosts) < 1 or len(invite_hosts) == 2:
            logger.info("Match: must be 2/4 Host co-host connect...")
            return

        if scene == "leave":   # 本端主播: 离开PK
            host["live_obj"].charactor.host.match_for_co_host(scene="leave")
        elif scene in ["invite", "accept"]:   # 本端主播: 发起PK申请 / 对端主播: 接受PK邀请
            host["live_obj"].charactor.host.match_for_co_host(scene="invite")

            # 对端主播接受连麦
            task_infos = []
            for invite_host in invite_hosts:
                task = (invite_host["live_obj"].charactor.host.match_for_co_host, ("accept",))
                task_infos.append(task)

            com.start_task(task_infos, is_get_result=False)

    @staticmethod
    def go_game_live(host, tagName="Clash Royale"):
        """游戏录屏开播
        :param host:
        :param tagName:
        :return:
        """
        logger.info("Go LIVE with UI Click...")
        host["live_obj"].charactor.host.go_game_live(tagName=tagName)

        return host["live_obj"].get_room_info()

    @staticmethod
    def apply_ab_clone(characters, android_abid="", ios_abid="", wait_time=10):
        """游戏录屏开播
        :param wait_time:
        :param characters:
        :param android_abid:
        :param ios_abid:
        :return:
        """
        tasks = []
        for character in characters:
            target_abid = android_abid if character["device"].type == "Android" else ios_abid

            task = (character["live_obj"].apply_ab_clone, (target_abid, wait_time))
            tasks.append(task)

        com.start_task(tasks, is_get_result=False)

