# -*- coding: UTF-8 -*-

from uibase.controls import Window, Control
from uibase.upath import *
from uibase.controls import PopupBase


class CommunityGuidelinesUpdate(PopupBase):
    """《社区自律公约》更新
    """
    window_spec = {"path": UPath(~text_ == 'Got it|知道了')}

    def handle(self):
        """弹窗处理
        :return:
        """
        return self.click()


class BlockCommentSettingPage(PopupBase):
    """《社区自律公约》更新
    """
    window_spec = {"path": UPath(~text_ == '关闭')}

    def handle(self):
        """弹窗处理
        :return:
        """
        return self.click()


class CoHostModulePopup(PopupBase):
    """直播期间
    """
    window_spec = {"path": UPath(~text_ == '查看所有模式')}

    def handle(self):
        """弹窗处理
        :return:
        """
        return self.click()

