# -*- coding: utf8 -*-
import time

import wget
from uibase.app import SystemPopupHandler

from testlib.app.ios_lib.iOSCommonApp import *
from testlib.app.ios_lib.popups.popup_panel import *
from testlib.thread.multi_thread import MultiThread

from business.ttlh.utils.ios.popup import *
from business.ttlh.utils.ios import config as con


class M2LiOSTTLiveApp(iOSCommonApp):
    init_popup = False
    app_popup_classes = [SSOPopup, LanguagePopup, SwipeGuideFeedPopup, closeFollowPopup, FacebookGrantPopup, knowAgree,
                         LimitPopup, closeSpreadPopup, CommunityGuidelinesUpdate, BlockCommentSettingPage,
                         UpdateOfPrivacyPolicy, CoHostModulePopup]

    app_spec = {
        "bundle_id": "com.zhiliaoapp.musically.ep",  # 本地debug的
        "kill_process": True,
        "timeout": 20,  # default driver rpc timeout
        "env": {
            "SHOOTS_ENABLE_API_SOCKET": 1
        }
    }
    system_popup_rules = [
        {
            "match": {"label": ".*用于 iMessage 和 FaceTime"},
            "popup_params": {"button_texts": ["是", "否"]}
        },
        {
            "match": {"label": "提醒事项"},
            "popup_params": {"button_texts": ["忽略"]}
        },
        {
            "match": {"label": "私有地址"},
            "popup_params": {"button_texts": ["重新加入"]}
        },
        {
            "match": {"label": "允许“BDCBot”删除.*"},
            "popup_params": {"button_texts": ["删除"]}
        },
        {
            "match": {"label": "未受信任的企业级开发者"},
            "popup_params": {"button_texts": ["取消"]}
        },
        {
            "match": {"label": "“TikTok M”想访问您的相机"},
            "popup_params": {"button_texts": ["好"]}
        },
        {
            "match": {"label": "“TikTok M”想访问您的麦克风"},
            "popup_params": {"button_texts": ["好"]}
        },
        {
            "match": {"label": "“TikTok M”想访问您的通讯录"},
            "popup_params": {"button_texts": ["好"]}
        },
        {
            "match": {"label": "“TikTok M”想给您发送通知"},
            "popup_params": {"button_texts": ["允许"]}
        },
        {
            "match": {"label": "App Update"},
            "popup_params": {"button_texts": ["Cancel"]}
        },
        {
            "match": {"label": "查找通讯录好友"},
            "popup_params": {"button_texts": ["确定"]}
        },
        {
            "match": {"label": "查找 Facebook 好友"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "直播屏幕"},
            "popup_params": {"button_texts": ["好"]}
        },
        {
            "match": {"label": "允许“TikTok M”使用无线数据？"},
            "popup_params": {"button_texts": ["无线局域网与蜂窝网络"]}
        },
        {
            "match": {"label": "允许“TikTok M”跟踪你在其他公司的App和网站上的活动吗？"},
            "popup_params": {"button_texts": ["允许"]}
        },
        {
            "match": {"label": "允许“TikTok M”使用你的大致位置？"},
            "popup_params": {"button_texts": ["使用App时允许"]}
        },
        {
            "match": {"label": "同步 Facebook 好友和电子邮件地址"},
            "popup_params": {"button_texts": ["不允许"]}
        }
    ]

    def __init__(self, *args, **kwargs):
        """初始化
        :param args:
        :param kwargs:
        """
        self.device = args[0]
        # mock 设置
        self.set_mock()

        # 初始化APP
        super(M2LiOSTTLiveApp, self).__init__(*args, **kwargs)

    def request(self, method, params=None, timeout=None):
        """iOS request方法
        :param method:
        :param params:
        :param timeout:
        :return:
        """
        if params is None:
            params = {}

        # 多线程循环监听
        t = MultiThread(self.clean_popup)
        t.start()

        method = "GBLAutoTest_" + method
        resp = self.rpc.request(method, params, timeout)
        logger.info("[method: %s] with param:%s  get resp: %s" % (method, params,json.dumps(resp)))
        time.sleep(2)

        return resp

    def clean_popup(self):
        """清理popup
        :return:
        """
        if not self.init_popup:
            self.init_popup = True
            logger.info("do clean popup")
            handler = SystemPopupHandler(self)

            # 多线程循环监听
            while True:
                handled_popups = handler.handle()
                time.sleep(0.5)

    def get_did(self):
        """获取设备did
        :return:
        """
        # 多线程循环监听popup
        t = MultiThread(self.clean_popup)
        t.start()
        time.sleep(2)

        # iOS>=33.9.0版本新增
        resp = self.call_method(class_name="TTInstallIDManagerStub", method="deviceID")
        return resp["value"]

    def set_mock(self):
        """
        :return:
        """
        # 本地文件路径
        ttmock_file = os.path.join(con.CUR_PATH, "absettings.json")

        # ttmock资源文件地址
        ttmock_url = os.environ.get("ttmock_url", "")
        if ttmock_url != "":
            # 远程资源配置ttmock文件下载
            wget.download(ttmock_url, out=ttmock_file)
        else:
            # # 默认配置
            # with open(ttmock_file, "w+") as json_file:
            #     json_file.write(json.dumps(con.default_mock_config))
            #
            # # 导入终端设备目标目录
            # # device.push_file("absettings.json", "Documents/absettings.json", bundle_id=bundle_id)
            # bundle_id = self.app_spec["bundle_id"]
            # # self.get_device().push_file(ttmock_file, con.device_ttmock_path, bundle_id=bundle_id)
            # self.device.push_file(ttmock_file, con.device_ttmock_path, bundle_id=bundle_id)
            pass

        # # absettings配置设置
        # self.app_spec["env"]["kTTKABSettingsLaunchStubFileEnvKey"] = "absettings.json"
