# -*- coding: utf8 -*-

import time, random, copy
from utils.common.log_utils import logger
from shoots.retry import Retry
from uibase.controls import *
from uibase.upath import *
from uibase.upath import type_, label_
from business.ttlh.utils.ios.iOSBaseUI import BasePanel, parse_app_info_by_clipboard
from shoots_ios.app import AccessibilityApp

from business.ttlh.utils.ios import config as con


class iOSSystemDesktop(AccessibilityApp):
    """iOS系统桌面类
    """
    app_spec = {
    }


class AWEExtensionWindow(Window):
    """iOS 扩展类
    """
    window_spec = {"path": UPath(~type_ == "XCUIElementTypeApplication|SBTransientOverlayWindow")}

    def start_live(self):
        """游戏 录屏开播
        :return:
        """
        # self.scroll_and_enter("TikTok M");
        # self.start_live_btn.click()
        control = Control(root=self, path=UPath(label_ == "开始直播"))
        control.click()


class CoHostListItemCell(BasePanel):
    """Co-Host List Item Cell
    """

    def get_locators(self):
        return {
            "avatar_view": {"path": UPath(type_ == "GBLAnchorCoHostListCircleAvatarView")},
            "invite_button": {"path": UPath(type_ == "GBLAnchorCoHostListInviteButton")},
            "cohost_username": {
                "path": UPath(type_ == "GBLAnchorCoHostListInfoNameView", visible_ == True)
                        / UPath(type_ == "UIStackView", id_ == "stackView") / UPath(id_ == "nameLabel")
            }
        }


class CoHostListView(BasePanel):
    """CoHost List View
    """
    elem_path = UPath(type_ == "GBLAnchorCoHostListItemCell") / UPath(type_ == "UIView")
    elem_class = CoHostListItemCell


class CoHostResultListItemCell(BasePanel):
    """Co-Host List Item Cell
    """

    def get_locators(self):
        return {
            "invite_button": {"path": UPath(id_ == "inviteButton", visible_ == True)},
            "cohost_username": {
                "path": UPath(id_ == "nameView") / UPath(id_ == "stackView") / UPath(id_ == "nameLabel", visible_ == True)
            },
            "offline_tag": {"path": UPath(~text_ == "未开播|Offline", id_ == "textLabel", visible_ == True)},
        }


class CoHostSearchResultListView(BasePanel):
    """CoHost Search Result List View
    """
    elem_path = UPath(id_ == "collectionView") / UPath(type_ == "GBLCoHostSearchResultListCell")
    elem_class = CoHostResultListItemCell


class AnchorAvatarView(BasePanel):
    """multi co-host: multi host(>2) connect panel
    """

    def get_locators(self):
        return {
            "avatar_border": {"path": UPath(id_ == "avatarBorderView")},
            "anchor_name": {"path": UPath(id_ == "nameLineView", type_ == "UIStackView") / UPath(id_ == "nameLabel")}
        }


class CoHostAvatarContainerView(BasePanel):
    """CoHost Avatar Container View
    """
    elem_path = UPath(type_ == "GBLAnchorCoHostingPanelAvatarView")
    elem_class = AnchorAvatarView


class TableViewCellContentView(BasePanel):
    """嘉宾连麦: 嘉宾请求Cell
    """

    def get_locators(self):
        return {
            "nick_name": {"path": UPath(id_ == "displayIdLabel")},
            "accept_btn_follow": {"path": UPath(id_ == "acceptBtn", visible_ == True)}
        }


class MultiGuestReuqestTableViewCell(BasePanel):
    """嘉宾连麦: 主播侧 嘉宾申请连麦面板
    """
    elem_path = UPath(type_ == "GBLMultiGuestAnchorPanelReuqestTableViewCell")
    elem_class = TableViewCellContentView


class SingleMicView(BasePanel):
    """嘉宾侧各嘉宾画面内容
    """

    def get_locators(self):
        return {
            "no_guest_view": {"path": UPath(id_ == "noGuestView")},
            "nick_name": {"path": UPath(id_ == "guestInfoViewV2", type_ == "GBLMultiGuestMicGuestInfoView") / UPath(
                id_ == "nameLabel")},
            "mute_btn": {"path": UPath(id_ == "muteBtn", visible_ == True)},
            "host_tag": {"path": UPath(id_ == "hostTag", type_ == "IESMTAnchorTagView", visible_ == True)},
            "mute_video_bg": {"path": UPath(id_ == "blurView", type_ == "UIVisualEffectView", visible_ == True)},
        }


class MultiGuestSingleMicView(BasePanel):
    """嘉宾连麦: 嘉宾侧各嘉宾画面View
    """
    elem_path = UPath(type_ == "GBLLMSSingleMicView")
    elem_class = SingleMicView


class AppInfoCell(BasePanel):
    """APP info列表项
    """

    def get_locators(self):
        return {
            "text_label": {"path": UPath(type_ == "UITableViewLabel")}
        }


class DebugAPPInfoControllerView(BasePanel):
    """Debug设置页: APP info页面
    """
    elem_path = UPath(type_ == "IESLiveAppInfoCell") / UPath(type_ == "UITableViewCellContentView")
    elem_class = AppInfoCell


class EffectCellView(BasePanel):
    """Effect面板子cell
    """

    def get_locators(self):
        return {
            "effect_image": {"path": UPath(id_ == "effectImageContainer") / UPath(type_ == "UIImageView")}
        }


class EffectContainerItemView(BasePanel):
    """直播间内Effect面板View
    """
    elem_path = UPath(id_ == "collectionView", type_ == "UICollectionView") / UPath(
        type_ == "IESLiveMTCakeStickerItemCell")
    elem_class = EffectCellView


class SwitchRecordModeTab(BasePanel):
    """Tab 栏
    """
    def get_locators(self):
        return {
            "tab_title": {"path": UPath(id_ == "titleLabel", type_ == "UILabel", visible_ == True)}
        }


class SwitchRecordMode(BasePanel):
    """拍摄页底部tab栏
    """
    elem_path = UPath(type_ == "AWESwitchRecordModeCollectionViewCell")
    elem_class = SwitchRecordModeTab


class HostLivePanel(BasePanel):
    """Anchor live window
    """
    window_spec = {"path": UPath(type_ == "AWEMaskWindow")}

    def get_locators(self):
        return {
            # "+" 创作者中心
            "creator_+": {"type": Control, "path": UPath(type_ == "UIImageView", id_ == "create_inverse")},
            "creator_warning": {"type": Control, "path": UPath(type_ == "UIButtonLabel", label_ == "I Know & Agree")},
            "creator_tabs": {"type": SwitchRecordMode,
                             "path": UPath(type_ == "AWESwitchRecordModeCollectionView", id_ == "collectionView", visible_ == True)},
            "creator_live_tab": {"type": Control,
                                 "path": UPath(id_ == "titleLabel", type_ == "UILabel", label_ == "直播")},
            "popup_continue_button": {"type": Control, "path": UPath(id_ == "continueButton", type_ == "TUXButton")},
            "confidential_popup_confirm": {"type": Control, "path": UPath(type_ == "AWEUIButton", id_ == "OK")},

            # 直播tab 开播类型
            "video_button": {"type": Control, "path": UPath(id_ == "videoButton", visible_ == True)},
            "screen_shot_button": {"type": Control, "path": UPath(id_ == "screenShotButton", visible_ == True)},

            # Guide page
            "go_live": {"type": Control, "path": UPath(type_ == "UIButton", id_ == "startButton")},
            "setting_btn": {"type": Control, "path": UPath(id_ == "guide_icon_settings", visible_ == True)},
            "video_quality_btn": {"type": Control, "path": UPath(~text_ == "视频质量|Video quality", visible_ == True)},
            "reback_icon": {"type": Control,
                            "path": UPath(id_ == "IconChevronLeftOffsetLTR", type_ == "UIButton", visible_ == True)},
            "setting_list": {"type": BasePanel,
                             "path": UPath(type_ == "GBLSheetNativeContainer", visible_ == True)},

            # 组件加载失败Lynx弹窗
            "lynx_close_button": {"type": Control, "path": UPath(type_ == "UIButton", id_ == "closeButton")},

            # 主播
            "close_live": {"type": Control, "path": UPath(type_ == "UIImageView", id_ == "live_mt_tool_close_new_new")},
            "live_end_confirm": {"type": Control, "path": UPath(type_ == "AWEUIButton", id_ == "立即结束")},
            "resume_button": {"type": Control,
                              "path": UPath(type_ == "AWEUIButton", ~id_ == "恢复|Resume", visible_ == True)},
            "cancel_resume_button": {"type": Control, "path": UPath(~id_ == "取消|Cancel", visible_ == True)},
            "end_live_close_button": {"type": Control,
                                      "path": UPath(type_ == "UILynxView", label_ == ",button", visible_ == True)},
            "live_finish_lynx_view": {"type": Control,
                                      "path": UPath(controller_ == "IESLiveMTAnchorFinishViewController", visible_ == True)},
            # 设置页
            "more_setting_button": {"type": Control, "path": UPath(type_ == "UIView", ~label_ == "更多|More")},
            "more_setting_page": {"type": Control,
                                  "path": UPath(type_ == "HTSLivePopupContainer") / UPath(
                                      type_ == "GBLPopupContainer")},
            # Control类型不支持滑动, 自定义元素类型
            "more_setting_container": {"type": BasePanel,
                                       "path": UPath(id_ == "popupContainer") / UPath(
                                           type_ == "GBLPopupContainer") / UPath(id_ == "contentView")},
            "pause_live_button": {"type": Control, "path": UPath(type_ == "UILabel",
                                                                 ~label_ == "暂停直播|Pause LIVE", visible_ == True)},
            "pause_confirm": {"type": Control,
                              "path": UPath(type_ == "TUXDialogHighlightBackgroundButton", ~id_ == "暂停|Pause")},
            "resume_live_button": {"type": Control, "path": UPath(type_ == "UIButton", ~label_ == "恢复|Resume")},
            "flip_camera_button": {"type": Control,
                                   "path": UPath(type_ == "UILabel", ~label_ == "Flip camera|翻转摄像头")},
            "comment_button": {"type": Control, "path": UPath(type_ == "UILabel", ~label_ == "Comment|评论")},
            # Setting page
            "host_avatar_view": {"type": Control,
                                 "path": UPath(type_ == "GBLRoomProfileView")/UPath(type_ == "HTSLiveAvatarImageView", id_ == "avatarView")},
            "debug_button": {"type": Control, "path": UPath(id_ == "debugButton")},
            "app_info_button": {"type": Control, "path": UPath(id_ == "APP Info")},
            "copy_all_button": {"type": Control, "path": UPath(id_ == "Copy All")},
            "turn_back_button": {"type": Control, "path": UPath(label_ == "返回", type_ == "_UIButtonBarButton")},
            "back_button": {"type": Control, "path": UPath(id_ == "backButton", label_ == "IconChevronLeftOffsetLTR")},
            "camera_security_popup": {"type": Control, "path": UPath(type_ == "AWEUIButton", id_ == "OK")},
            "room_id_cell": {"type": Control, "path": UPath(id_ == "Room ID")},

            # 特效设置项
            "effect_popup": {"type": Control, "path": UPath(id_ == "popupContainer", label_ == "PopupContainerLayer")},
            "clear_effect": {"type": Control,
                             "path": UPath(id_ == "cleanButton", type_ == "UIButton", visible_ == True)},
            "mt_matting_view": {"type": Control, "path": UPath(type_ == "IESLiveMTMattingView")},
            "effect_open_button": {"type": Control, "path": UPath(id_ == "expandButton", visible_ == True)},
            "enhance_button": {"type": Control, "path": UPath(type_ == "UIView", ~label_ == "美颜|Enhance")},
            "effect_button": {"type": Control,
                              "path": UPath(id_ == "gbl_tool_decoration_beauty", type_ == "UIImageView")},
            "effect_content_container": {"type": EffectContainerItemView,
                                         "path": UPath(type_ == "GBLCakeStickerPanelSubPage", visible_ == True)},
            "effect_panel_popup": {"type": Control, "path": UPath(id_ == "okButton", type_ == "TUXButton")},
            "effect_close_button": {"type": Control, "path": UPath(id_ == "closeButton", visible_ == True)},

            # Multi-guest
            "multi_guest_button": {"type": Control,
                                   "path": UPath(type_ == "GBLInteractiveMultiLiveButton", id_ == "customView")},
            "multi_guest_setting": {"type": Control,
                                    "path": UPath(~label_ == "嘉宾连麦设置", visible_ == True)},
            "layout_switch_confirm": {"type": Control,
                                      "path": UPath(id_ == "切换", type_ == "AWEUIButton", visible_ == True)},
            "multi_guest_disconnect": {"type": Control,
                                       "path": UPath(~label_ == "全部断开", visible_ == True)},
            "disconnect_confirm": {"type": Control, "path": UPath(type_ == "UIButtonLabel", id_ == "断开连线")},

            "multi_guest_tableview": {"type": MultiGuestReuqestTableViewCell,
                                      "path": UPath(id_ == "tableview", type_ == "UITableView")},
            "live_show": {"type": Control, "path": UPath(type_ == "UIView", ~label_ == "Show|直播秀")},
            "guest_reback_icon": {"type": Control,
                                  "path": UPath(id_ == "IconChevronLeftOffsetLTR", visible_ == True)},
            "multi_guest_panel": {"type": Control,
                                  "path": UPath(controller_ == "IESLiveMTPopupNavigationController", visible_ == True)},

            # 嘉宾: 画面操作面板
            "multi_guest_mic_view": {"type": MultiGuestSingleMicView,
                                     "path": UPath(type_ == "HTSLive4LayerContainerView", id_ == "containerView")},
            "expand_button": {"type": Control, "path": UPath(label_ == "IconFullScreen", type_ == "UIButton")},
            "shrink_button": {"type": Control, "path": UPath(label_ == "IconArrowsToCenter", type_ == "UIButton")},

            # 主播连麦
            "co_host_button": {"type": Control,
                               "path": UPath(type_ == "GBLInteractiveCoHostButton", id_ == "customView")},
            "invite_user_list": {"type": CoHostListView,
                                 "path": UPath(controller_ == "GBLAnchorCoHostListViewController") / UPath(
                                     type_ == "UIView") / UPath(type_ == "UICollectionView", id_ == "collectionView",
                                                                visible_ == True)},
            "invite_list_expand_btn": {'type': Control,
                                       'path': UPath(type_ == "GBLAnchorCoHostListSectionFooterView",
                                                     visible_ == True)},
            "cohost_disconnect": {"type": Control,
                                  "path": UPath(type_ == "UIView", id_ == "bgView") / UPath(type_ == "UIImageView")},
            "cohost_disconnect_confirm": {"type": Control,
                                          "path": UPath(type_ == "AWEUIButton", ~id_ == "Disconnect|断开连线")},
            "cohost_accept": {"type": Control, "path": UPath(type_ == "UIButton", id_ == "acceptButton")},

            "multi_cohost_panel": {"type": Control,
                                   "path": UPath(controller_ == "GBLAnchorCoHostingPanelViewController")},
            "multi_cohost_avatar_container": {"type": CoHostAvatarContainerView,
                                              "path": UPath(id_ == "avatarContainerView")},
            "co_host_search_entrance": {"type": Control,
                                        "path": UPath(id_ == "IconMagnifyingGlassOffset", visible_ == True)},
            "co_host_search_text": {"type": Control, "path": UPath(id_ == "fieldEditor", visible_ == True)},
            "co_host_search_btn": {"type": Control, "path": UPath(id_ == "searchLabel", visible_ == True)},
            "co_host_search_result_list": {"type": CoHostSearchResultListView,
                                           "path": UPath(id_ == "searchResultView", visible_ == True)},
            "cohost_reback_icon": {"type": Control,
                                   "path": UPath(~label_ == "返回|Back", type_ == "UIImageView", visible_ == True)},

            # 连麦状态
            "in_cohost_status": {"type": Control, "path": UPath(type_ == "IESLiveMTAnimatedImageView", id_ == "null",
                                                                visible_ == True)},
            # Match
            "cohost_match_btn": {"type": Control,
                                 "path": UPath(type_ == "GBLInteractiveBattleButton")},
            "cohost_match_notice_popup": {"type": Control,
                                          "path": UPath(id_ == "battleButton", ~label_ == "试试看|Try it")},
            "cohost_match_module_popup": {"type": Control,
                                          "path": UPath(id_ == "battleButton", label_ == "查看所有模式")},
            "cohost_match_invite_btn": {"type": Control,
                                        "path": UPath(id_ == "inviteButton")},
            "cohost_match_accept_btn": {"type": Control,
                                        "path": UPath(id_ == "acceptButton", ~label_ == "同意|Accept")},
            "cohost_match_leave_confirm_btn": {"type": Control,
                                               "path": UPath(id_ == "confirmButton", ~label_ == "离开比赛|Leave match")},
            "cohost_match_status": {"type": Control,
                                    "path": UPath(type_ == "GBLMatchScoreBar", visible_ == True)},

            # Lynx弹窗View
            "spark_view_popup": {"type": Control,
                                 "path": UPath(type_ == "SparkView", id_ == "viewContainer")},
            "spark_popup_maskView": {"type": Control,
                                     "path": UPath(controller_ == "SparkPopupViewController") / UPath(
                                         id_ == "maskView")},

            # 游戏录屏开播
            "game_search_text": {"type": Control, "path": UPath(id_ == "searchTextField", visible_ == True)},
            "game_continue_btn": {"type": Control, "path": UPath(id_ == "btn", visible_ == True)},
            "game_allow_btn": {"type": Control, "path": UPath(text_ == "允许", visible_ == True)},
            "game_cancel_btn": {"type": Control,
                                "path": UPath(id_ == "取消", type_ == "UIButtonLabel", visible_ == True)},
        }

    def get_app_info_by_setting(self, times=3):
        """获取debug设置页信息
        :return:
        """
        room_id = ""
        # 直播间内点击主播头像进入debug设置页获取APP info
        if self["host_avatar_view"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click Host avatar icon...")
            self["host_avatar_view"].click()

            # 点击头像设置按钮
            logger.info("Click debug button...")
            self.click_by_name("debug_button", timeout=5, raise_error=False, sleep_time=2)
            # 进入debug设置页, 获取直播间信息
            logger.info("Click app_info button...")
            self.click_by_name("app_info_button", timeout=5, raise_error=False, sleep_time=3)

            # 双击 Copy ALL 复制到剪贴板, 获取room_id
            for _ in Retry(limit=times, raise_error=False):
                # 相机隐私弹窗处理
                if self["camera_security_popup"].existing:
                    self["camera_security_popup"].click()

                logger.info("Get room id...")
                if self["room_id_cell"].wait_for_visible(timeout=3, raise_error=False):
                    parent = self["room_id_cell"].parent
                    # 遍历获取子节点text数据
                    for child in parent.children:
                        ele_type = child.elem_info.get("type", "")
                        ele_text = child.elem_info.get("text", "")

                        logger.info("Traverse ele: type=%s, text=%s..." % (ele_type, ele_text))
                        if ele_type != "UITableViewLabel" or ele_text in ["Room ID", ""]:
                            continue

                        room_id = ele_text
                        break

                # 判断是否获取到room_id, 否则重试
                if room_id != "":
                    break

                time.sleep(2)

            # 返回上一层
            if self["turn_back_button"].wait_for_visible(timeout=5, raise_error=False):
                self["turn_back_button"].click()

                # 再返回上一层, 关闭debug页, 返回直播间
                if self["back_button"].wait_for_visible(timeout=5, raise_error=False):
                    self["back_button"].click()

        # 检查是否 "设置" 弹窗是否遮挡, 若有, 则隐藏
        if self["debug_button"].existing:
            # 点击用户头像隐藏弹窗
            self["debug_button"].click()

            if self["back_button"].wait_for_visible(timeout=5, raise_error=False):
                self["back_button"].click()

        return room_id

    def set_video_quality(self, quality, sleep_time=3):
        """
        :param sleep_time:
        :param quality:
        :return:
        """
        if self["setting_btn"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click setting_btn...")
            self["setting_btn"].click()
            time.sleep(2)

            if self["video_quality_btn"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click video_quality_btn...")
                self["video_quality_btn"].click()
                time.sleep(2)

                new_quality = f"{quality}p"
                expect_quality_locator = {
                    new_quality: {"type": Control,
                                  "path": UPath(text_ == new_quality, id_ == "titleLabel", visible_ == True)}}
                self.locators.update(expect_quality_locator)
                if self[new_quality].existing:
                    logger.info("Select video_quality = %s..." % new_quality)
                    self[new_quality].click()
                else:
                    # 开播测速建议分辨率
                    suggest_quality_locator = {
                        "suggest_quality": {"type": Control,
                                            "path": UPath(id_ == "suggestedLabel", ~text_ == "Suggested|推荐")}
                    }
                    self.locators.update(suggest_quality_locator)

                    if self["suggest_quality"].existing:
                        logger.info("Select suggest_quality...")
                        self["suggest_quality"].click()

                time.sleep(sleep_time)

            # 隐藏相关弹窗, 避免阻塞后续开播操作
            if self["reback_icon"].existing:
                logger.info("Click reback_icon to back past page...")
                self["reback_icon"].click()
                time.sleep(2)

            if self["video_quality_btn"].existing:
                # 下拉页面隐藏弹窗
                self["setting_list"].swipe(y_direction=-1, swipe_coefficient=5)
                time.sleep(2)

    def go_live_prepare(self, scene="live"):
        """进入直播预览页
        :param scene: 直播场景, live-设备摄像头, game-手机游戏
        :return:
        """
        # 点击 "+" 进入创作者中心
        self.click_by_name("creator_+", timeout=5)

        # 创作者中心 WARNING 弹窗
        self.click_by_name("creator_warning", timeout=3, raise_error=False, sleep_time=2)

        # 卸载安装首次启动: 允许相机&麦克风访问权限弹窗
        if self["popup_continue_button"].wait_for_visible(timeout=3, raise_error=False):
            self["popup_continue_button"].click()

        # 切换至 "直播"tab
        # self.click_by_name("creator_live_tab", timeout=10, sleep_time=5)
        if self["creator_tabs"].existing:
            i = 0
            for cell in self["creator_tabs"].items():
                tab_title = cell["tab_title"].elem_info.get("text", "")
                logger.info("Current Tab: %s" % tab_title)
                i += 1
                if i == 1:  # 跳过
                    continue

                # tab点击
                cell.click()
                if tab_title in ["直播", "LIVE"]:
                    time.sleep(3)
                    break
                # waiting
                time.sleep(2)
            else:
                # 兜底
                self.click_by_name("creator_live_tab", timeout=3, sleep_time=3)

        # 避免影响续播场景执行
        # if self["cancel_resume_button"].wait_for_visible(timeout=3, raise_error=False):
        #     self["cancel_resume_button"].click()
        #     time.sleep(2)

    def go_live(self, times: int = 10, quality="", sleep_time=10, is_resume=False):
        """Go LIVE through UI Click Operator
        :param quality:
        :param sleep_time: 开播或续播后等待页面加载时长
        :param is_resume: 是否续播
        :param times: 开播或续播 失败重试次数
        :return:
        """
        # 进入开播预览页
        self.go_live_prepare()

        if not is_resume:
            # Go LIVE
            for _ in Retry(limit=times, raise_error=False):
                # 开播: 公司confidential弹窗
                if self["confidential_popup_confirm"].existing:
                    self["confidential_popup_confirm"].click()

                # 先判断是否处于续播状态
                if self["resume_button"].wait_for_visible(timeout=10, raise_error=False):
                    logger.info("Click Resume button back to LIVE")
                    self["resume_button"].click()
                else:
                    # 账号冲突: 立即结束 "结束另一台设备上的直播？"
                    if self["live_end_confirm"].existing:
                        logger.info("Click live_end_confirm to end another device LIVE streaming...")
                        self["live_end_confirm"].click()
                        time.sleep(2)

                    logger.info("Click video_button to go LIVE with camera...")
                    self["video_button"].click()

                    if self["go_live"].wait_for_visible(timeout=10, raise_error=False):
                        # 判断是否需要设置开播分辨率
                        if quality != "":
                            logger.info("Set video_quality == %s..." % quality)
                            self.set_video_quality(quality=quality)

                        logger.info("Click go LIVE button...")
                        # 点击 "Go LIVE|开启直播"
                        self["go_live"].click()

                time.sleep(5)
                if self.is_living():
                    logger.info("Host go Live: SUCCESS...")
                    break
        else:
            # 预览页续播
            if self["resume_button"].wait_for_visible(timeout=20, raise_error=True):
                self["resume_button"].click()

                # 判断是否进入直播间, 若未进入, 再校验是否在预览页, 若在，则点击直播按钮开播
                if self["close_live"].wait_for_visible(timeout=15, raise_error=False):
                    logger.info("Host resume Live: SUCCESS...")

        # 开播或续播后等待页面加载时长
        time.sleep(sleep_time)

    def pause_live(self, is_recover=False, times=5):
        """
        :param times:
        :param is_recover:
        :return:
        """
        if is_recover:
            logger.info("Click Resume button back to LIVE")
            self.click_by_name("resume_live_button", raise_error=False)
        else:
            logger.info("Click More button")
            self.click_by_name("more_setting_button")
            for _ in Retry(limit=times, raise_error=False):
                if self["pause_live_button"].wait_for_visible(timeout=15, raise_error=False):
                    logger.info("Click Pause button")
                    self["pause_live_button"].click()
                    # 第一次点击"暂停"确认弹窗
                    if self["pause_confirm"].wait_for_visible(timeout=5, raise_error=False):
                        self["pause_confirm"].click()
                    break

                logger.info("Swipe up to look for the pause_live_button")
                self["more_setting_container"].swipe(y_direction=1, swipe_coefficient=5)
                time.sleep(2)

    def effect_dressup(self, is_clean=False, try_times=2, sleep_time=4):
        """随机选择特效并生效
        :return:
        """
        try:
            if self["effect_open_button"].existing:  # 底部特效栏展开按钮
                self["effect_open_button"].click()
                time.sleep(sleep_time)
            elif self["enhance_button"].wait_for_visible(timeout=5, raise_error=False):  # Enhance -> Effect
                logger.info("Host Click '+Enhance'...")
                self["enhance_button"].click()
                time.sleep(2)

                if self["effect_button"].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click Effects button enter list...")
                    self["effect_button"].click()
                    time.sleep(2)

                    # "长按预览特效" 弹窗
                    if self["effect_panel_popup"].wait_for_visible(timeout=3, raise_error=False):
                        self["effect_panel_popup"].click()
                        time.sleep(sleep_time)

            # 特效面板展开, 随机选择或反选任意特效装扮
            if self["clear_effect"].wait_for_visible(timeout=5, raise_error=False):
                if not is_clean:
                    # 随机选择任一特效生效
                    if self["effect_content_container"].wait_for_ui_stable():
                        effect_items = self["effect_content_container"].items()
                        if len(effect_items) > 0:
                            selected_icon = 0 if len(effect_items) < 8 else random.randint(0, len(effect_items) - 8)

                            try:
                                if effect_items[selected_icon]["effect_image"].wait_for_ui_stable():
                                    logger.info("Click the %d effect ..." % selected_icon)
                                    effect_items[selected_icon]["effect_image"].click()
                            except Exception as e:
                                logger.info("Click the %d effect occur Exception, Detail: %s   ..." % (selected_icon, str(e)))

                                logger.info("Fallback: Get the 1 effect ...")
                                effect_items[0]["effect_image"].click()

                        # 检验是否使用绿幕特效, iOS“绿幕面板-提示”会阻塞后续UI操作
                        if self["mt_matting_view"].wait_for_visible(timeout=3, raise_error=False):
                            logger.info("Matting effect, clear_effect...")
                            self["clear_effect"].click()
                else:
                    logger.info("Click clear_effect button to close Effect...")
                    self["clear_effect"].click()
                # loading
                time.sleep(sleep_time)
        except Exception as e:
            logger.error("Fail to set effect, detail: %s" % str(e))
        finally:
            # 隐藏特效面板, 避免影响后续操作
            if self["effect_popup"].wait_for_visible(timeout=5, raise_error=False):
                for _ in Retry(limit=try_times, raise_error=False):
                    # 左滑隐藏弹窗
                    logger.info("Hide Effect panel through device click...")
                    self.device.click(10, 100)
                    # self["effect_popup"].click()
                    time.sleep(2)

                    if self["clear_effect"].wait_for_invisible(timeout=2, raise_error=False):
                        logger.info("effect_content_container has been hided...")
                        break
                else:
                    if self["effect_popup"].existing:
                        logger.info("Hide Effect panel through click effect_popup view...")
                        self["effect_popup"].click()
                        time.sleep(2)

            # 关闭直播间内特效底部面板
            if self["effect_close_button"].existing:
                logger.info("Click effect_close_button to hide effect panel...")
                self["effect_close_button"].click()

    def camera_flip(self, sleep_time=3, times=3):
        """翻转相机
        :return:
        """
        if self["more_setting_button"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click More button...")
            self["more_setting_button"].click()
            time.sleep(3)

        if self["flip_camera_button"].wait_for_visible(timeout=10, raise_error=False):
            logger.info("Click flip_camera button...")
            self["flip_camera_button"].click()
            time.sleep(2)

            visible_rect = self["flip_camera_button"].ensure_visible()
            to_x, to_y = self.get_abs_coordinate(visible_rect)
            offset = 300
            for _ in Retry(limit=times, raise_error=False):
                # iOS: 滑动超过页面一半高度可触发隐藏
                logger.info("Hide More -> Tools page through swipe down, y_offset: %d..." % offset)
                self["comment_button"].drag(to_x=to_x, to_y=to_y + offset)
                time.sleep(sleep_time)

                if self["flip_camera_button"].wait_for_invisible(timeout=2, raise_error=False):
                    logger.info("Hide More -> Tools page success...")
                    break

                # 滑动偏移量加大
                offset += 250

    def end_live(self):
        """End LIVE through UI Click Operator
        :return:
        """
        if self["close_live"].wait_for_visible(timeout=5, raise_error=False):
            # End LIVE
            self["close_live"].click()
            time.sleep(3)

            # Confirm: 新版可能不存在确认弹窗
            self.click_by_name("live_end_confirm", raise_error=False)

    def close_live_end_page(self):
        """
        :return:
        """
        # Lynx直播结束页暂无法通过UI方式关闭
        # if self["end_live_close_button"].wait_for_visible(timeout=10, raise_error=False):
        #     self["end_live_close_button"].click()

        # Schema跳转FYP
        self.app.open_scheme(con.fyp)

    def host_leave_channel(self, sleep_time=3):
        """The Host leave multi-guest channel
        :param sleep_time:
        :return:
        """
        # 校验是否有组件加载失败Lynx弹窗
        if self["lynx_close_button"].existing:
            self["lynx_close_button"].click()
            time.sleep(2)

        logger.info("Host Click '+Hosts'")
        self.click_by_name("multi_guest_button")

        logger.info("Host Click disconnect button")
        self.click_by_name("multi_guest_disconnect")

        logger.info("Host Click confirm disconnect")
        self.click_by_name("disconnect_confirm")
        time.sleep(sleep_time)

        # 判断面板是否还存在, 若存在, 则需隐藏, 避免影响后续操作
        if self["multi_guest_setting"].existing:
            logger.info("click host_avatar_view for hideing the multi_guest_setting page...")
            self["host_avatar_view"].click()

    def leave_co_host(self, sleep_time=3):
        """Leave Co-Host
        :param sleep_time:
        :return:
        """
        # 校验是否有组件加载失败Lynx弹窗
        if self["lynx_close_button"].existing:
            self["lynx_close_button"].click()
            time.sleep(2)

        # Click Co-Host button
        self.click_by_name("co_host_button")

        logger.info("Host disconnect")
        self.click_by_name("cohost_disconnect")

        logger.info("Host disconnect confirm")
        self.click_by_name("cohost_disconnect_confirm")
        time.sleep(sleep_time)

    def open_co_host_list_page(self, sleep_time=5):
        """打开主播连麦列表页
        :param sleep_time:
        :return:
        """
        # 校验是否有组件加载失败Lynx弹窗
        if self["lynx_close_button"].existing:
            self["lynx_close_button"].click()
            time.sleep(2)

        logger.info("Host click co_host_button to invite other host...")
        self.click_by_name("co_host_button", timeout=15, sleep_time=7)

        # 多主播连麦(>2)邀请
        if self["multi_cohost_panel"].wait_for_visible(timeout=5, raise_error=False):
            # 复杂对象赋值, 避免UI变换影响加载
            copy_elem_dict_1 = self["multi_cohost_avatar_container"].items()
            for cell in copy_elem_dict_1:
                if not cell["avatar_border"].visible:
                    cell.click()
                    time.sleep(2)
                    break
            else:  # 无空余连麦麦位
                return

        # See more
        if self["invite_list_expand_btn"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
            logger.info("Click see more button for expand_collapse...")
            self["invite_list_expand_btn"].click()
            time.sleep(sleep_time)

    def host_invite_co_host(self, be_invited_user_name, times=10, sleep_time=5):
        """Host invite other host to co-host
        :param be_invited_user_name:
        :param times:
        :param sleep_time:
        :return:
        """
        # 打开主播连麦列表页
        self.open_co_host_list_page(sleep_time=sleep_time)

        logger.info("Wait for invited host[%s] online, and invite host to co-host..." % be_invited_user_name)
        if self["invite_user_list"].wait_for_ui_stable():
            is_invited = False
            try:
                logger.info("Try to click co_host_search_entrance for inviting Host...")
                # 列表页内通过搜索主播昵称邀请
                if self["co_host_search_entrance"].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click co_host_search_entrance...")
                    self["co_host_search_entrance"].click()
                else:
                    # Icon遍历: 尝试兜底
                    for i in range(3):
                        icon_image = "icon_image_%d" % i
                        # 动态设置UI
                        new_locator = {
                            icon_image: {"type": Control, "path": UPath(id_ == "bgView", visible_ == True)/i},
                        }
                        self.locators.update(new_locator)
                        # 遍历UIImageView类型UI
                        if self[icon_image].type != "UIImageView":
                            continue

                        logger.info("Click %s button..." % icon_image)
                        self[icon_image].click()
                        time.sleep(2)

                        # 成功进入搜素页
                        if self["co_host_search_text"].existing:
                            logger.info("Success into co_host_search page...")
                            break
                        else:
                            logger.info("Into co_host_setting page...")
                            # 进入设置页
                            if self["cohost_reback_icon"].existing:
                                self["cohost_reback_icon"].click()
                                time.sleep(2)
                # waiting
                time.sleep(2)

                logger.info("Set co_host_search_text =='%s'..." % be_invited_user_name)
                self["co_host_search_text"].text = be_invited_user_name
                time.sleep(2)

                for _ in Retry(limit=times, raise_error=False):
                    logger.info("Click co_host_search_btn to search...")
                    self["co_host_search_btn"].click()
                    time.sleep(2)

                    # 复杂对象赋值, 避免UI变换影响加载
                    copy_elem_dict = self["co_host_search_result_list"].items()
                    logger.info(f'Search result list length = {len(copy_elem_dict)}...')
                    for cell in copy_elem_dict:
                        current_name = cell["cohost_username"].elem_info.get("label", "")
                        is_offline = cell["offline_tag"].existing
                        logger.info("host[%s] in co-host search result list, is_offline: %s..." % (current_name, is_offline))

                        if be_invited_user_name == current_name and not is_offline:
                            logger.info("Invite %s co-host connect" % be_invited_user_name)
                            cell["invite_button"].click()
                            time.sleep(2)
                            is_invited = True
                            break

                    if is_invited:
                        logger.info("Host successfully click Invite button for %s..." % be_invited_user_name)
                        break

                    time.sleep(sleep_time)
                else:
                    # 搜索失败, 返回上一层
                    logger.info("Co-Host connect fail by Seatching, click cohost_reback_icon back to Previous page...")
                    if self["cohost_reback_icon"].existing:
                        self["cohost_reback_icon"].click()
                        time.sleep(3)

                    raise Exception("Don't find Host(%s) in co-host list page..." % be_invited_user_name)
            except:
                logger.debug("Fallback: click invite_user_list for inviting Host...")

                # 兜底: 判断搜索弹窗是否存在, 若存在点击隐藏, 避免影响列表页的操作
                if self["co_host_search_btn"].existing:
                    logger.debug("Hide co_host_search page...")
                    self["cohost_reback_icon"].click()
                    time.sleep(3)

                # 主播列表页 Cell 点击
                for _ in Retry(limit=times, raise_error=False):
                    # 复杂对象赋值, 避免UI变换影响加载
                    copy_elem_dict = self["invite_user_list"].items()
                    for cell in copy_elem_dict:
                        current_name = cell["cohost_username"].elem_info.get("label", "")
                        logger.info("host[%s] in co-host list..." % current_name)

                        if be_invited_user_name == current_name:
                            logger.info("Invite %s co-host connect" % be_invited_user_name)
                            cell["invite_button"].click()
                            time.sleep(2)
                            is_invited = True
                            break

                    # 校验是否邀请成功: 邀请列表页面隐藏
                    if is_invited:
                        logger.info("Host successfully click Invite button for %s..." % be_invited_user_name)
                        break

                    logger.info("Swipe down, refresh invite_user_list page for %s..." % be_invited_user_name)
                    self["invite_user_list"].swipe(y_direction=-1, swipe_coefficient=8)
                    time.sleep(sleep_time)
                else:
                    raise Exception("Don't find Host(%s) in co-host list page..." % be_invited_user_name)

        # 兜底: 避免主播连麦列表半屏页面阻断后续操作
        if self["co_host_search_entrance"].wait_for_visible(timeout=2, raise_error=False):
            for _ in Retry(limit=times, raise_error=False):
                # 左滑隐藏弹窗
                logger.info("Click device to hide co_host list...")
                self.device.click(10, 100)
                # self["effect_popup"].click()

                if self["co_host_search_entrance"].wait_for_invisible(timeout=2, raise_error=False):
                    logger.info("effect_content_container has been hided...")
                    break
            else:
                if self["co_host_search_entrance"].existing:
                    logger.info("Click host_avatar_view to hide co_host list...")
                    # 点击直播间内 "主播头像icon", 隐藏主播连麦半屏页
                    self["host_avatar_view"].click()
                    time.sleep(2)

    def host_accept_co_host(self, timeout=60):
        """Host accept co-host connect
        :return:
        """
        # 主播侧等待接受弹窗
        self["cohost_accept"].wait_for_visible(timeout=timeout, interval=1, raise_error=True)

        # 主播接受邀请上麦
        self["cohost_accept"].click()

        # 等待接受连麦,RTC合流
        logger.info("Wait for accept linkmic, RTC Stream Merge...")
        self["in_cohost_status"].wait_for_visible(timeout=60, interval=1, raise_error=True)

    def enter_multi_guest_page(self, wait_time=3):
        """点击 "嘉宾连麦" 按钮打开连麦设置页
        :return:
        """
        # 校验是否有组件加载失败Lynx弹窗
        if self["lynx_close_button"].existing:
            self["lynx_close_button"].click()
            time.sleep(2)

        logger.info("Click multi_guest_button...")
        # 初步验证Android 连续点击2次嘉宾连麦按钮, 方可触发成功
        for _ in Retry(limit=3):
            self["multi_guest_button"].click()
            time.sleep(wait_time)

            # 判断是否成功点击嘉宾连麦按钮
            if self["multi_guest_setting"].wait_for_visible(timeout=5, raise_error=False):
                break

    def permit_apply_for_multi(self, nick_name, wait_time=3):
        """嘉宾连麦: 主播接受观众上麦申请
        :return:
        """
        try:
            # 进入嘉宾连麦设置页
            self.enter_multi_guest_page(wait_time=wait_time)

            # 选择嘉宾request按钮, 并点击 Accept 确认连麦
            if self["multi_guest_setting"].wait_for_visible(timeout=10, raise_error=False):
                # 复杂对象赋值, 避免UI变换影响加载
                copy_elem_dict = self["multi_guest_tableview"].items()
                for cell in copy_elem_dict:
                    if nick_name == "" or nick_name == cell["nick_name"].text:
                        logger.info("Host[%s] accept guest request..." % nick_name)
                        cell["accept_btn_follow"].click()

                        # 若指定nickName, 则点击Accept按钮退出; 否则主播接受所有嘉宾的请求
                        if nick_name != "":
                            time.sleep(3)
                            break
        except Exception as e:
            logger.error("Fail to permit_apply_for_multi, detail: %s" % str(e))
        finally:
            # 判断面板是否还存在, 若存在, 则需隐藏, 避免影响后续操作
            if self["multi_guest_setting"].existing:
                logger.info("click Device's Absolute Coordinates for hiding the multi_guest_setting page...")
                self.device.click(10, 100)

    def change_guest_layout_for_multi(self, scene, nick_name=""):
        """嘉宾连麦: 主播放大嘉宾画面
        :param scene:
        :param nick_name:
        :return:
        """
        # 放大/缩小
        if scene in ["Expand", "Shrink"]:
            # 复杂对象赋值, 避免UI变换影响加载
            copy_elem_dict = self["multi_guest_mic_view"].items()

            # 选择嘉宾request按钮, 并点击 Accept 确认连麦
            for cell in copy_elem_dict:
                # 空麦位 或 主播麦位
                if cell["host_tag"].existing or not cell["nick_name"].visible:
                    continue

                # 任一 或 指定嘉宾
                cell_nick_name = cell["nick_name"].text
                logger.info("Expand: Expected=%s, Real=%s..." % (nick_name, cell_nick_name))
                if nick_name == "" or nick_name == cell_nick_name:
                    logger.info("Click Guest(%s) layout for %s..." % (cell_nick_name, scene))
                    cell.click()

                    if scene == "Expand":  # 放大嘉宾画面
                        self.click_by_name("expand_button", timeout=5)
                    elif scene == "Shrink" and nick_name == cell_nick_name:  # 缩小嘉宾画面
                        self.click_by_name("shrink_button", timeout=5)

                    return cell_nick_name
        else:
            # 其它布局切换
            # 进入嘉宾连麦设置页
            self.enter_multi_guest_page()

            logger.info("Click btn_layout_setting...")
            self["multi_guest_setting"].click()
            time.sleep(2)

            # 切换不同布局
            if scene == "Fixed Panel":    # 固定面板
                layout_scene_id = "panelFixButton"
            elif scene == "Fixed Grid":   # 固定网格
                layout_scene_id = "gridFixButton"
            elif scene == "Grid":         # 网格
                layout_scene_id = "gridFloatButton"
            else:                         # 面板
                layout_scene_id = "panelFloatButton"

            layout_scene_locator = {
                layout_scene_id: {"type": Control,
                                  "path": UPath(id_ == layout_scene_id, visible_ == True)}}
            self.locators.update(layout_scene_locator)
            try:
                if self[layout_scene_id].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
                    logger.info(f'Change to layout={scene} ...')
                    self[layout_scene_id].click()
                    time.sleep(2)

                    # 预期点击确认 布局切换 按钮后, 弹窗会自动收起
                    if self["layout_switch_confirm"].wait_for_visible(timeout=3, raise_error=False):
                        logger.info("layout_switch_confirm...")
                        self["layout_switch_confirm"].click()
                        time.sleep(3)
            except Exception as e:
                logger.info("Fail to change_guest_layout_for_multi: %s..." % str(e))
            finally:
                # 兜底: 返回直播间避免影响后续UI操作
                if self["guest_reback_icon"].existing:
                    # 侧滑隐藏返回上一页
                    self["guest_reback_icon"].click()
                    time.sleep(3)

        # 返回直播间避免影响后续UI操作
        if self["multi_guest_setting"].existing:
            logger.info("click Device's Absolute Coordinates for hiding the multi_guest_setting page...")
            self.device.click(10, 100)

        return nick_name

    def is_cohost_status(self):
        """嘉宾: 根据当前画面UI状态判断是否处于连麦状态
        :return:
        """
        is_existing = self["in_cohost_status"].existing
        logger.info("whether Co-Host status is connected? %s..." % is_existing)

        return is_existing

    def is_living(self):
        """判断是否直播状态
        :return:
        """
        return self["close_live"].existing

    def close_cohost_lynxview_popup(self):
        """Check whether the lynxview popup exists, cancel popup
        :return:
        """
        # 主播连麦: 首次结束后, 直播平均Lynx弹窗
        if self["spark_view_popup"].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Hide Host data preview popup...")
            # 点击页面内其它区域隐藏弹窗
            self["spark_popup_maskView"].click()

    def check_match_status(self):
        """校验Match状态是否成功
        :return:
        """
        return self["cohost_match_status"].existing

    def match_for_co_host(self, scene):
        """主播连麦: Match(PK) 操作
        :param scene: invite / accept / leave
        :return:
        """
        if scene in ["invite", "leave"]:
            # 主播连麦: Match按钮
            if self["cohost_match_btn"].wait_for_visible(timeout=15):
                logger.info("click cohost_match_btn start Match...")
                self["cohost_match_btn"].click()
                time.sleep(2)

            # 首次PK弹窗确认
            if self["cohost_match_notice_popup"].wait_for_visible(timeout=3, raise_error=False):
                logger.info("click cohost_match_notice_popup confirm 'Try it'...")
                self["cohost_match_notice_popup"].click()
                time.sleep(2)

            if scene == "invite":  # 开始PK
                logger.info("Click cohost_match_invite_btn...")
                # 点击开始PK
                self.click_by_name("cohost_match_invite_btn", timeout=5, sleep_time=3)

                if self["cohost_match_module_popup"].existing:
                    logger.info("Click cohost_match_module_popup...")
                    self["cohost_match_module_popup"].click()
                    time.sleep(2)
            elif scene == "leave":  # 提前离开PK
                logger.info("Click cohost_match_leave_confirm_btn...")
                # 点击确认离开
                self.click_by_name("cohost_match_leave_confirm_btn", timeout=5, sleep_time=3)
                # 校验是否成功退出PK状态
                if self.check_match_status():
                    logger.info("Leave Match: Success...")
        elif scene == "accept":
            if self["cohost_match_accept_btn"].wait_for_visible(timeout=10, raise_error=False):
                self["cohost_match_accept_btn"].click()
                time.sleep(2)

            # 校验是否成功退出PK状态
            if not self.check_match_status():
                logger.info("Accept Match: Success...")

    def go_game_live(self, tagName="Clash Royale", is_resume=False):
        """游戏录屏开播
        iOS18 开启录屏直播权限无法进行授权
        :param tagName:
        :param is_resume:
        :return:
        """
        # 进入开播预览页
        self.go_live_prepare(scene="game")

        # 判断是否 "恢复直播" 弹窗
        if not is_resume:
            if self["game_cancel_btn"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                self["game_cancel_btn"].click()
                time.sleep(3)

            logger.info("Click screen_shot_button to go LIVE with game...")
            self["screen_shot_button"].click()

            # 选择开播游戏
            logger.info("Go Live: First click...")
            self["go_live"].click()
            time.sleep(5)

            if self["game_search_text"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                # Search game name
                logger.info("Search game_search_box...")
                self["game_search_text"].text = tagName

                # Click selected_game
                selected_game = {
                    tagName: {"type": Control,
                              "path": UPath(text_ == tagName, id_ == "titleLabel", visible_ == True)}}
                self.locators.update(selected_game)
                if self[tagName].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                    self[tagName].click()
                    time.sleep(2)

                # 点击 “Go LIVE”
                logger.info("Go Live: Second click...")
                self["go_live"].click()

            # 点击iOS系统 "开启直播" 权限
            logger.info("Click iOSSystem 'start live' button...")
            desktopApp = iOSSystemDesktop(self.device)
            extensionWin = AWEExtensionWindow(root=desktopApp)
            extensionWin.start_live()
            time.sleep(8)

            if self["game_allow_btn"].wait_for_visible(timeout=3, raise_error=False):
                self["game_allow_btn"].click()
                time.sleep(3)


class ViewerLivePanel(BasePanel):
    """Viewer live window
    """
    window_spec = {"path": UPath(type_ == "AWEMaskWindow")}

    def get_locators(self):
        return {
            # 连麦结束弹窗确认
            "disconnect_confirm": {"path": UPath(type_ == "AWEUIButton", ~label_ == "Got it|知道了")},

            # 嘉宾连麦
            "multi_guest_button": {"type": Control,
                                   "path": UPath(~label_ == "嘉宾连麦|Multi-guest", type_ == "UIView", visible_ == True)},
            "multi_guest_request": {"type": Control,
                                    "path": UPath(id_ == "requestButton",
                                                  type_ == "GBLMultiGuestAudienceApplyLinkMicRequestButton")},
            "guest_follow_and_request": {"type": Control,
                                         "path": UPath(id_ == "requestAndFollowButton", visible_ == True)},

            "multi_guest_mic_view": {"type": MultiGuestSingleMicView,
                                     "path": UPath(type_ == "IESLiveMTFeedCollectionView", id_ == "collectionView") /
                                             UPath(type_ == "IESLiveMTRoomSlideCell")},

            # 麦克风状态
            "micro_phone_btn": {"type": Control,
                                "path": UPath(type_ == "UIView", ~label_ == "麦克风|Mic", visible_ == True)},
            "micro_phone_mute_btn": {"type": Control,
                                     "path": UPath(id_ == "muteBtn", visible_ == True)},

            "camera_btn": {"type": Control,
                           "path": UPath(type_ == "UIView", ~label_ == "相机|Camera", visible_ == True)},
            "camera_open_confirm": {"type": Control,
                                    "path": UPath(id_ == "validateButton", ~label_ == "保存|Save", visible_ == True)},

            # 嘉宾数据概览弹窗
            "spark_view_popup": {"type": Control,
                                 "path": UPath(type_ == "SparkView", id_ == "viewContainer")},
            # 直播画面头部区域
            "spark_popup_maskView": {"type": Control,
                                     "path": UPath(controller_ == "SparkPopupViewController")},
            "mt_audience_view": {"type": Control,
                                 "path": UPath(controller_ == "IESLiveMTAudienceViewController")},
            "live_dialog_popup": {"type": MultiGuestSingleMicView,
                                  "path": UPath(type_ == "AWEUIButton", ~id_ == "确认|OK")},

            # 观众或嘉宾退出直播间
            "exit_room": {"type": Control, "path": UPath(type_ == "UIButton", ~id_ == "IconXMark")},
            "exit_room_confirm": {"type": Control, "path": UPath(type_ == "AWEUIButton", ~id_ == "确认|Confirm")},
        }

    def is_enter_room(self):
        """校验观众是否进房
        :return:
        """
        try:
            return self["exit_room"].existing
        except Exception as e:
            logger.info("exit_room doesn't exit. Detail: %s" % str(e))
            return False

    def leave_room_by_ui(self, times=3, wait_time=3):
        """退出直播间
        :return:
        """
        if self["exit_room"].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Guest click exit button leave room...")
            self["exit_room"].click()
            time.sleep(wait_time)

            # 连麦期间, 直接退出直播间需要再次确认是否退出直播间
            if self["exit_room_confirm"].wait_for_visible(timeout=3, raise_error=False):
                for _ in Retry(limit=times, raise_error=False):
                    logger.info("Guest click '确认|Confirm' confirm exit LIVE room...")
                    self["exit_room_confirm"].click()
                    time.sleep(wait_time)

                    if not self["exit_room"].existing:
                        break

    def guest_confirm_disconnect(self):
        """The Guest confirm leave multi-guest channel
        :return:
        """
        # 校验是否有主播断开连麦弹窗, 若有则取消, 避免阻断发起连麦
        if self["disconnect_confirm"].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Guest confirm 'Got it' of disconnect")
            self["disconnect_confirm"].click()
            time.sleep(3)
        elif self["live_dialog_popup"].wait_for_visible(timeout=3, raise_error=False):
            # 连麦期间, 主播异常下播, 超过30s 续播返回直播间, 连麦状态自动断开场景
            logger.info("Guest confirm 'OK' of auto disconnect by Host")
            self["live_dialog_popup"].click()

        # 嘉宾数据概览弹窗 Lync页面弹窗
        if self["spark_view_popup"].existing:
            logger.info("Hide Guest data preview popup...")
            # 点击页面内其它区域隐藏弹窗
            # self["spark_popup_maskView"].click()
            self["mt_audience_view"].click()

    def is_multi_guest_status(self):
        """嘉宾: 根据当前画面UI状态判断是否处于连麦状态
        :return:
        """
        # 校验是否在直播间内
        if not self["exit_room"].existing:
            logger.info("Doesn't in LIVE room...")
            return False

        is_existing = self["camera_btn"].existing
        logger.info("whether Multi-guest status is connected? %s..." % is_existing)

        return is_existing

    def apply_for_multi(self, position="-1"):
        """申请嘉宾连麦
        :return:
        """
        if self.is_multi_guest_status():
            logger.info("Guest has been connected with Host...")
            return

        # 点击嘉宾连麦按钮
        if self["multi_guest_button"].wait_for_visible(timeout=10):
            logger.info("Guest click multi_guest_button...")
            self["multi_guest_button"].click()
            time.sleep(2)

            # 发起连麦申请
            self.click_by_name("multi_guest_request")
            if self["multi_guest_request"].wait_for_visible(timeout=3, raise_error=False):
                self["multi_guest_request"].click()
                time.sleep(2)

                # Follow and request
                if self["guest_follow_and_request"].wait_for_visible(timeout=2, raise_error=False):
                    logger.info("Click guest_follow_and_request button...")
                    self["guest_follow_and_request"].click()

    def check_audio_video_mute_status_by_name(self, nick_name, icon_type):
        """校验麦克风/摄像头状态
        :param icon_type: camera or micro
        :param nick_name:
        :return:
        """
        # 昵称为空, 则默认返回false, 暂不考虑其它
        if nick_name == "":
            return False

        # 复杂对象赋值, 避免UI变换影响加载
        copy_elem_dict = self["multi_guest_mic_view"].items()

        # 选择嘉宾request按钮, 并点击 Accept 确认连麦
        for cell in copy_elem_dict:
            # 空麦位
            if cell["no_guest_view"].visible:
                continue

            # 校验嘉宾名称是否对应
            if nick_name == cell["nick_name"].text:
                return cell["mute_btn"].existing if icon_type == "micro" else cell["mute_video_bg"].existing
        else:
            return False

    def mute_local_audio_for_multi(self, mute, nick_name, times=10):
        """嘉宾侧: 麦克风状态切换
        若nick_name为空, 则无需关注按钮状态, 直接点击即可
        :param times:
        :param nick_name:
        :param mute:
        :return:
        """
        # 校验嘉宾是否正常上麦(麦克风按钮)
        if self["micro_phone_btn"].wait_for_visible(timeout=20):
            # 根据控件状态和预期状态判断是否点击 “麦克风” 按钮, 改变状态
            mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="micro")
            for i in range(times):
                logger.info("The %d time check micro_phone_btn status..." % (i + 1))

                # 根据mute状态判断是否需要点击按钮
                if nick_name == "" or (mute and not mute_status) or (not mute and mute_status):
                    logger.info("Click micro_phone_btn...")
                    self["micro_phone_btn"].click()
                    time.sleep(2)

                # 再次检测mute状态判断是否需要重试
                mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="micro")
                if nick_name == "" or (mute and mute_status) or (not mute and not mute_status):
                    break

                time.sleep(2)

            logger.info("micro_phone_btn: expected == %s, mute_btn_exist == %s..." % (mute, mute_status))
        else:
            logger.error("micro_phone_btn doesn't exit...")

    def mute_local_video_for_multi(self, mute, nick_name, times=10):
        """嘉宾侧: 摄像头状态切换
        若nick_name为空, 则无需关注按钮状态, 直接点击即可
        :param times: 重试次数
        :param nick_name:
        :param mute: True 关闭摄像头, False 开启摄像头
        :return:
        """
        # 校验嘉宾是否正常上麦(摄像头按钮)
        self["camera_btn"].wait_for_visible(timeout=20)

        # 根据控件状态和预期状态判断是否点击 “摄像头” 按钮, 改变状态
        mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="camera")
        for i in range(times):
            logger.info("The %d time check camera_btn status..." % (i + 1))

            # 根据mute状态判断是否需要点击按钮
            if nick_name == "" or (mute and not mute_status) or (not mute and mute_status):
                logger.info("Click camera_btn...")
                self["camera_btn"].click()
                time.sleep(2)

                # 摄像头打开确认弹窗
                if self["camera_open_confirm"].wait_for_visible(timeout=5, raise_error=False):
                    self["camera_open_confirm"].click()
                    time.sleep(2)

            # 再次检测mute状态判断是否需要重试
            mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="camera")
            if nick_name == "" or (mute and mute_status) or (not mute and not mute_status):
                break

            time.sleep(2)

        logger.info("camera_icon: expected == %s, mute_btn_exist == %s..." % (mute, mute_status))


class LiveCharactor(object):
    """Live Charactor UI object
    """

    def __init__(self, app):
        """
        :param app:
        :return:
        """
        self._host = HostLivePanel(root=app)
        self._viewer = ViewerLivePanel(root=app)

    @property
    def host(self):
        return self._host

    @property
    def viewer(self):
        return self._viewer
