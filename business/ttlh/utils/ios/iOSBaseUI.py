# -*- coding: utf8 -*-

import time
from uibase.controls import *
from uibase.upath import *
from shoots.retry import Retry
from uibase.upath import type_, label_
from utils.common.log_utils import logger


def parse_app_info_by_clipboard(app):
    """解析剪贴板获取room_id
    :param app:
    :return:
    """
    # 获取剪贴板内容
    clipboard = app.get_clipboard()
    logger.info("APP info[clipboard]: %s" % clipboard)

    if clipboard is not None and clipboard != "":
        items = clipboard.split("\n")
        # 获取Room ID
        for item in items:
            if "Room ID" not in item:
                continue

            values = item.split(":")
            return values[1].replace(" ", "")

    return None


class BasePanel(Window):
    """界面相关基类
    """

    def click_by_name(self, ctrl_name, timeout=10, interval=0.5, raise_error=True, sleep_time=3):
        if self[ctrl_name].wait_for_visible(timeout=timeout, interval=interval, raise_error=raise_error):
            self[ctrl_name].click()
            time.sleep(sleep_time)

    def swipe(self, x_direction=0, y_direction=0, swipe_coefficient=5):
        '''滑动

        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        后续会支持斜着滑动

        :param coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        '''

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(
                x_direction, y_direction))

        # self._driver.drag(self.id, x1, y1, x2, y2)
        self.drag(x2, y2, x1 - rect.width / 2, y1 - rect.height / 2)
        time.sleep(1)

    def send_enter(self):
        device = self.app.get_device()
        device.send_keys("\r")


class FeedPanel(BasePanel):
    """Anchor live window
    """
    window_spec = {"path": UPath(controller_ == "TTKTabBarController")}

    def get_locators(self):
        return {
            "profile": {"type": Control, "path": UPath(id_ == "ic_tab_me_active", visible_ == True)},

        }

    def open_debug_panel(self, try_times=3):
        """
        循环长按profile控件，进入debug界面失败后，通过device长按
        """
        logger.info("wait for profile button visible...")
        self["profile"].wait_for_visible(timeout=30, raise_error=False)
        for _ in Retry(limit=try_times, raise_error=False):
            if self["profile"].wait_for_invisible(timeout=2, raise_error=False):
                break
            else:
                logger.info("long click profile button open debug_panel...")
                self["profile"].long_click(duration=5)
        else:
            if self["profile"].wait_for_visible(timeout=3, raise_error=False):
                me_tab_rect = self["profile"].rect.center
                logger.info("long click profile button open debug_panel...")
                self.app.get_device().long_click(me_tab_rect[0], me_tab_rect[1], duration=5)

    def set_ab_clone(self, abid):
        """设置AB Clone
        :param abid: AB Clone的ABID号
        :return:
        """
        logger.info("Open debug panel...")
        self.open_debug_panel()

        logger.info("Open the AB Clone panel...")
        debug = DebugPanel(root=self.app)
        debug.click_ab_clone()

        logger.info(f'Open open_apply_settings_page...')
        ab_clone_panel = ABClonePanel(root=self.app)
        ab_clone_panel.open_apply_settings_page()

        logger.info(f'Set AB Clone[ABID={abid}] for for device={self.app.get_device().model}...')
        ab_clone_input_panel = ABCloneInput(root=self.app)
        ab_clone_input_panel.input_abid(abid=abid)

        logger.info(f'Click apply all AB settings...')
        ab_clone_apply_panel = ABApplyPage(root=self.app)
        ab_clone_apply_panel.click_apply_all()

        logger.info(f'Confirm apply all AB settings...')
        ab_clone_apply_confirm = ABTUXDialog(root=self.app)
        ab_clone_apply_confirm.click_enable_btn()


class DebugPanel(Window):
    """
    """
    window_spec = {
        "path": UPath(controller_ == "AWEDebugSettingViewController")
    }

    def get_locators(self):
        return {
            'AB_test_tab': {"type": Control, 'path': UPath(id_ == "AB Test & Settings")},
            'ab_clone': {"type": Control, 'path': UPath(id_ == "AB/Settings Clone")},
        }

    def click_ab_clone(self):
        """进入AB clone配置页: Debug -> AB Test & Settings -> AB clone
        :return:
        """
        # 切换Config Tab
        if self["AB_test_tab"].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Click AB_test_tab...")
            self["AB_test_tab"].click()

            if self["ab_clone"].wait_for_existing(timeout=5, raise_error=False):
                logger.info("Click ab_clone, enter ab_clone setting page...")
                self["ab_clone"].click()
                time.sleep(2)


class ABClonePanel(Window):
    """
    """
    window_spec = {
        "path": UPath(controller_ == "TTKABCloneHomeViewController")
    }

    def get_locators(self):
        return {
            'apply_abid': {'path': UPath(label_ == "Apply Settings with ABID")},
        }

    def open_apply_settings_page(self, wait_time=2):
        """打开ABID设置页面
        :return:
        """
        if self['apply_abid'].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Click 'Apply Settings with ABID'...")
            self['apply_abid'].click()
            time.sleep(wait_time)


class ABCloneInput(Window):
    """
    """
    window_spec = {
        "path": UPath(controller_ == "TTKABCloneInputViewController")
    }

    def get_locators(self):
        return {
            'abid_input': {'path': UPath(id_ == "fieldEditor")},
            'Next': {'path': UPath(id_ == "Next")},
        }

    def input_abid(self, abid):
        """输入并下载ABID
        :param abid:
        :return:
        """
        if self['abid_input'].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Input ABID and apply...")
            self['abid_input'].click()
            self['abid_input'].input(text=abid)

            if self['Next'].wait_for_visible(timeout=15, raise_error=False):
                logger.info("Click Next button...")
                self['Next'].click()
                time.sleep(2)


class ABApplyPage(Window):
    """AB Setting Apply page
    """
    window_spec = {
        "path": UPath(controller_ == "TTKABCloneOverviewViewController")
    }

    def get_locators(self):
        return {
            'apply_all_btn': {'path': UPath(id_ == "applyButton")},
        }

    def click_apply_all(self):
        """点击应用所有设置
        :return:
        """
        if self['apply_all_btn'].wait_for_visible(timeout=3, raise_error=False):
            self['apply_all_btn'].click()
            time.sleep(2)


class ABTUXDialog(Window):
    """确认应用
    """

    window_spec = {
        "path": UPath(controller_ == "TUXDialog")
    }

    def get_locators(self):
        return {
            'enable_btn': {'path': UPath(id_ == "Enable", type_ == "UIButtonLabel")},
        }

    def click_enable_btn(self):
        """点击确认
        :return:
        """
        if self['enable_btn'].existing:
            self['enable_btn'].click()
            time.sleep(2)
