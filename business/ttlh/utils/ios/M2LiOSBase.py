# -*- coding: utf8 -*-
import json
import time

from shoots.retry import Retry
from business.ttlh.utils.accounts.m2l_account_mgr import *
from common.tiktok.rpc.link_mic_instance.link_mic_ios import LinkMiciOS
from business.ttlh.utils.ios.M2LiOSLiveUI import Live<PERSON><PERSON>ctor
from business.ttlh.utils.ios.iOSBaseUI import FeedPanel
from business.ttlh.utils.android import config as con


class M2LiOSBase(LinkMiciOS):
    """M2L iOS基础类
    """

    # system_popup_rules = con.system_popup_rules

    def __init__(self, app):
        """
        :param app:
        """
        super().__init__(app)

        self.app_obj = app
        # init app ui operation object
        self.inner_room_id = None
        self.charactor = LiveCharactor(app=self.app)

    def check_login(self):
        """是否登录
        :return:
        """
        resp = self.app.call_method(class_name="TTKUserLoginStub", method="isLogin")
        return resp.get("value", 0) == 1

    def get_uid(self):
        """
        :return:
        """
        resp = self.app.call_method(class_name="TTKUserLoginStub", method="userId")
        return resp.get("value", "")

    def get_did(self):
        """获取设备did
        :return:
        """
        try:
            return self.app_obj.get_did()
        except:
            return self.app.get_device_did()

    def login(self, phone, sm_code, region=86, times=3, sleep_time=15):
        """重载login, 添加登陆态校验, 避免账号多次登陆
        :param sleep_time:
        :param times:
        :param phone:
        :param sm_code:
        :param region:
        :return:
        """
        # 登录验证
        for _ in Retry(limit=times, raise_error=False):
            if self.check_login():
                break

            super().login(phone, sm_code, region)
            time.sleep(sleep_time)
        else:
            self.app.call_method(class_name="TTKUserLoginStub", method="loginWithPhoneNumber",
                                 phoneNumber=f"+{region}{phone}", SMSCode=sm_code)
            self.restart()
            time.sleep(sleep_time)

        return self.get_uid()

    def logout(self, times=3, sleep_time=10):
        """
        :return:
        """
        # 退出登录
        for _ in Retry(limit=times, raise_error=False):
            self.app.call_method(class_name="TTKUserLoginStub", method="logout")

            self.restart()
            time.sleep(sleep_time)

            if not self.check_login():
                break

    def start_live(self, retry=3):
        """
        :param retry:
        :return:
        """
        resp = super().start_live(retry=retry)

        # 设置room_id, 方便后续使用
        self.inner_room_id = resp["data"]["room_id"]

        return resp

    def get_room_info(self):
        """获取直播间信息
        :return:
        """
        res = {"data": {"room_id": ""}}
        if self.inner_room_id is None:  # 开播场景
            try:
                res["data"]["room_id"] = self.app.request("getRoomInfo")["data"]["roomId"]
            except Exception as e:
                logger.debug("Use getRoomInfo API get roomId, fail: %s" % str(e))

                logger.info("Fallback: get APP Info by UI...")
                res["data"]["room_id"] = self.charactor.host.get_app_info_by_setting()
        else:
            # 续播场景
            res["data"]["room_id"] = self.inner_room_id

        logger.info("[get_room_info_by_ui]: %s" % json.dumps(res))

        return res

    def close_live(self, is_ui=False, sleep_time=5):
        """End LIVE
        :param sleep_time:
        :param is_ui:
        :return:
        """
        if is_ui:
            logger.info("End LIVE with UI Click")
            self.charactor.host.end_live()
        else:
            self.leave_room()
        time.sleep(sleep_time)

        try:
            logger.info("Jump to FYP through schema...")
            self.charactor.host.close_live_end_page()
        except Exception as e:
            logger.debug("Jump to FYP fail: %s" % str(e))

            logger.info("Fallback: APP restart...")
            self.restart()
            time.sleep(sleep_time)

    def enter_room(self, room_id, times=5, sleep_time=3, try_times=3):
        """观众进直播间
        :param try_times:
        :param sleep_time:
        :param times:
        :param room_id:
        :return:
        """
        # 校验是否已经进房, 若已进房, 则无需处理
        if self.charactor.viewer.is_enter_room():
            return

        for _ in Retry(limit=try_times, raise_error=False):
            logger.info("Enter room through API...")
            super().enter_room(room_id=room_id, times=times)
            time.sleep(sleep_time)

            if self.charactor.viewer.is_enter_room():
                logger.info("Enter room success...")
                break

            # 直播间跳转失败, 可能是由于弹窗阻断, 暂通过重启的方式规避
            self.restart()
            time.sleep(5)
        else:
            logger.info("Fallback: Jump to LIVE room through schema...")
            for _ in Retry(limit=try_times, raise_error=False):
                self.app.open_scheme(f"snssdk1180://live?room_id={room_id}")
                time.sleep(sleep_time)

                if self.charactor.viewer.is_enter_room():
                    break
            else:
                raise Exception("Can't enter LIVE room...")

    def disconnect_co_host(self, is_ui=True):
        """Host disconnect Co-Host
        :param is_ui:
        :return:
        """
        logger.info("Host start disconnect Co-Host...")
        if is_ui:
            self.charactor.host.leave_co_host()
        else:  # Todo
            pass

        # close co-host end page, turn back
        logger.info("The lynxview page exist, turn back to close this page")
        self.charactor.host.close_cohost_lynxview_popup()

    def apply_for_multi(self, position="-1", is_ui=False):
        """
        :param is_ui:
        :param position:
        :return:
        """
        if not is_ui:
            super().apply_for_multi(position=position)
        else:
            self.charactor.viewer.apply_for_multi()

    def permit_apply_for_multi(self, status, room_id, guest_info, is_ui=True):
        """
        :param guest_info:
        :param status:
        :param room_id:
        :param is_ui:
        :return:
        """
        if guest_info is None:
            return None

        guest_uid, guest_nickname = guest_info["data"]["uid"], guest_info["data"]["nickName"]
        # UI点击, 暂无返回
        if is_ui:
            self.charactor.host.permit_apply_for_multi(guest_nickname)
            return {}

        return super().permit_apply_for_multi(status=status, room_id=room_id, guest_uid=guest_uid)

    def mute_local_audio_for_multi(self, mute, guest_info=None, is_ui=True):
        """
        :param guest_info:
        :param mute:
        :param is_ui:
        :return:
        """
        if is_ui and guest_info is not None:
            guest_nickname = guest_info["data"].get("nickName", "")
            self.charactor.viewer.mute_local_audio_for_multi(mute, nick_name=guest_nickname)
            return {}

        return super().mute_local_audio_for_multi(mute=mute)

    def mute_local_video_for_multi(self, mute, guest_info=None, is_ui=True, **kwargs):
        """嘉宾侧 摄像头状态切换
        :param guest_info: 用户昵称+uid
        :param is_ui:
        :param mute:
        :param kwargs: 自定义参数
        :return:
        """
        if is_ui and guest_info is not None:
            guest_nickname = guest_info["data"].get("nickName", "")
            self.charactor.viewer.mute_local_video_for_multi(mute=mute, nick_name=guest_nickname)
            return {}

        return super().mute_local_video_for_multi(mute=mute)

    def change_guest_layout_for_multi(self, scene, is_ui=True, layout_info=None):
        """切换连麦布局
        :param scene:
        :param is_ui:
        :param layout_info:
        :return:
        """
        if is_ui:
            return self.charactor.host.change_guest_layout_for_multi(scene)
        else:
            if layout_info is None:
                layout_info = ("1013", "宫格-非固定(最大9麦位)", "9")
            return self.change_layout_for_multi(layout_info[0], layout_info[2])

    def mock_video_for_live(self, is_enable=False, tos_url="", size=None):
        """mock直播视频流
        :param is_enable:
        :param size:
        :param tos_url:
        :return:
        """
        # 根据mock状态设置相关URL设置项
        tos_url = (con.MOCK_VIDEO_TOS_URL if tos_url == "" else tos_url) if is_enable else ""

        logger.info("Get LiveDebugManager instance...")
        res = self.app.call_method(class_name="IESLiveDebugManager", method="sharedInstance")

        logger.info("Save video tos url in LiveDebugManager: %s" % tos_url)
        self.app.call_method(class_name="IESLiveDebugManager", method="setDebugMockVideoCaptureURLString:",
                             object_id=res["value"], args=[tos_url])

        logger.info("Get LiveDebugMockVideoManager instance...")
        res_mock = self.app.call_method(class_name="IESLiveDebugMockVideoManager", method="sharedInstance")

        logger.info("Set video tos url in Mock debug tools: %s" % tos_url)
        self.app.call_method(class_name="IESLiveDebugMockVideoManager", method="setVideoURLString:",
                             object_id=res_mock["value"], args=[tos_url])

        if is_enable:
            logger.info("Set Mock tool enable...")
            self.app.call_method(class_name="IESLiveDebugMockVideoManager", method="setupHook",
                                 object_id=res_mock["value"])

    def set_effect_with_effect_id(self, effect_id, tab_key=None, wait_time=5):
        """主播: 直播间内根据特效effect_id使用特效效果
        :param wait_time:
        :param effect_id: 设置为空, 则取消特效
        :param tab_key: iOS默认None即可
        :return:
        """
        logger.info("Get GBLEffectPerformanceTest instance...")
        res = self.app.call_method(class_name="GBLEffectPerformanceTest", method="sharedInstance")

        logger.info("Start Download Effect With EffectId ...")
        self.app.call_method(class_name="GBLEffectPerformanceTest", method="downloadEffectWithEffectId:completion:",
                             object_id=res["value"], args=[effect_id, tab_key])
        time.sleep(wait_time)

        logger.info("Use effect with effect_id ...")
        self.app.call_method(class_name="GBLEffectPerformanceTest", method="useEffectWithEffectId:",
                             object_id=res["value"], args=[effect_id])

    def apply_ab_clone(self, abid="", wait_time=10):
        """
        :param wait_time:
        :param abid:
        :return:
        """
        if abid == "":
            return

        panel = FeedPanel(root=self.app)
        panel.set_ab_clone(abid=abid)

        logger.info("Restart APP for ABID take effect...")
        self.restart()

        time.sleep(wait_time)
