# -*- coding: utf8 -*-

import os

# ==================== Basic Config ====================
BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DATA_PATH = os.path.join(BASE_PATH, "data")

if not os.path.exists(DATA_PATH):
    os.mkdir(DATA_PATH)

MOCK_VIDEO_TOS_URL = "http://tosv.byted.org/obj/ttlive-performance-upload/effect_multi_action.mp4"

# ==================== Multi-Guest ====================
# 嘉宾连麦布局信息: https://bytedance.larkoffice.com/wiki/wikcnZx6egGl2U6LzdDx6acJRDh
# 暂不考虑 放大 "Expand" 操作, Android部分机型点击放大嘉宾画面会失败
layout_scenes = ["Fixed Panel", "Fixed Grid", "Panel", "Grid"]
layout_infos = {
    "Fixed Panel": [("1015","4宫格", "4"), ("1006", "6宫格", "6"), ("1014", "9宫格", "9")],
    "Fixed Grid": [("1002","侧边1v4", "5"), ("1012", "侧边1v6", "7")],
    "Panel": [("1011", "侧边-非固定(1v6)", "7")],
    "Grid": [("1013", "宫格-非固定(最大9麦位)", "9")]
}

# ==================== ToS Config ====================
# ToS默认配置
DEFAULT_CLUSTER = "default"
TOS_BUCKET = "ttlh-uiautomation-bucket"
TOS_API_SERVICE_NAME = "toutiao.tos.tosapi"
TOS_ENDPOINT = "tos-cn-north.byted.org"
TOS_IDC = "CN-3DC"
