# -*- coding: UTF-8 -*-
import time

from shoots_android.control import *
from utils.common.log_utils import logger
from uibase.upath import *
from uibase.controls import Control, Window, PopupBase

# __all__ = ["CommunityGuidelinesPopup", "MinePopup"]


class CommonPopup(PopupBase):
    """splash activity下的常见Popup
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.splash.SplashActivity.*'
                               '|com.ss.android.ugc.aweme.relation.ffp.ui.FindFriendsPageActivity'
                               '|com.ss.android.ugc.aweme.hybridkit.spark.TranslucentActivity'
                               '|com.ss.android.ugc.aweme.pns.universalpopup.core.ui.UniversalPopupActivity'
                               '|com.ss.android.ugc.aweme.pns.universalpopup.core.ui.UniversalPopupActivity2'}

    def get_locators(self):
        return {
            "positive_button": {"type": Control, "path": UPath(id_ == 'positive_button', visible_ == True)},
            "negative_button": {"type": Control, "path": UPath(id_ == 'negative_button', visible_ == True)},
            "not_now": {'type': Control, 'path': UPath(text_ == 'Not now', visible_ == True)},
            "got_it": {'type': Control, 'path': UPath(~text_ == 'Got it|知道了', visible_ == True)},
            "cancel": {"type": Button, "path": UPath(text_ == "Cancel", visible_ == True)},
            "sync": {"type": Button, "path": UPath(text_ == "Sync", visible_ == True)},
            "close": {"path": UPath(~id_ == "close|close_button", visible_ == True)},
            "agree_and_continue": {"path": UPath(id_ == 'btn_agree', visible_ == True)},
            "later": {"path": UPath(text_ == "Later", visible_ == True)},
            "skip_btn": {"path": UPath(text_ == "Skip", visible_ == True)},
            "not_allow": {"path": UPath(~text_ == "Don't allow|不允许", visible_ == True)},
            "privacy_policy": {"path": UPath(text_ == "Privacy Policy", visible_ == True)},
            "ok": {"path": UPath(text_ == "OK", visible_ == True)},
            # View Your friends' post
            "close_1": {"path": UPath(id_ == "friends_tab_intro_close_button", visible_ == True)},
            "close_btn": {"path": UPath(id_ == 'close_icon_view', visible_ == True)},
            "lynx_close": {"path": UPath(id_ == 'bullet_container', visible_ == True)/0/0/0/2},
            "disagree": {"path": UPath(text_ == "不允许")},
            "continue": {"path": UPath(text_ == "Continue")},
            "story_close": {"path": UPath(id_ == "auto_dark_detect_view")},
            "close_activity_start": {"path": UPath(id_ == "nav_end", visible_ == True)},
            "activity_status_save": {"path": UPath(id_ == "activity_status_save")},
            "close_login_or_sign": {"path": UPath(id_ == "top_image_area") / UPath(type_ == "ConstraintLayout") / 1},
            "contacts_ok": {"path": UPath(text_ == "OK")},
            # ad pop control
            "no_like_ad": {"path": UPath(desc_ == "I don't like it", visible_ == True)},
            "submit": {"path": UPath(desc_ == "Submit", visible_ == True)},

        }

    def handle(self):
        logger.info("popup %s handled" % self.__class__.__name__)
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].visible:
                return self[elem_name].click()
        return False

class AdvertisementPopup(PopupBase):
    """splash activity下的常见Popup
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.splash.SplashActivity#1'}

    def get_locators(self):
        return {
            "advertisement_close": {"type": Control, "path": UPath(id_ == "bullet_container") / 0 / 0 / 0},
        }

    def handle(self):
        logger.info("popup %s handled" % self.__class__.__name__)
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].visible:
                return self[elem_name].click()
        return False


#   ForYouFeedPage
class CommunityGuidelinesUpdate(PopupBase):
    """
        ForFouFeed 界面
        社区条款更新弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity#1"}

    def get_locators(self):
        return {
            "action_area": {"type": Control, "path": UPath(id_ == 'action_area', visible_ == True)},
            "ok": {"type": Control, "root": "action_area", "path": UPath(text_ == "OK", visible_ == True)},
        }

    def handle(self):
        if self['ok'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['ok'].click()
        else:
            return False


class SyncContactsPopup(PopupBase):
    """
        ForFouFeed 界面
        同步通讯录弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "action_area": {"type": Control, "path": UPath(id_ == 'action_area')},
            "close": {"type": Control, "root": "action_area", "path": UPath(~text_ == "Don't allow|不允许")},
            "close_btn": {"path": UPath(id_ == 'close_icon_view')},
            "cancel_btn": {"path": UPath(desc_ == 'Cancel', visible_ == True)},
        }

    def handle(self):
        if self['close'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['close'].click()
        elif self['close_btn'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['close_btn'].click()
        elif self['cancel_btn'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['cancel_btn'].click()
        else:
            return False


class TermsOfServiceAndVirtualItemsPolicyPopup(PopupBase):
    """
         更改服务条款(ToS)和虚拟物品政策弹框
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "agree": {"path": UPath(text_ == 'Agree')},
        }

    def handle(self):
        if self['agree'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['agree'].click()
        else:
            return False


class ContentBoxPopup(PopupBase):
    """
        Content box 弹窗
        在非强制登录国家会出现
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.setting.ui.I18nSettingNewVersionActivity|com.ss.android.ugc.aweme.setting.ui.SettingContainerActivity.*|com.ss.android.ugc.aweme.journey.NewUserJourneyActivity.*"}

    def get_locators(self):
        return {
            "agree_and_continue": {"path": UPath(id_ == 'btn_agree')},
        }

    def handle(self):
        if self['agree_and_continue'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['agree_and_continue'].click()
        else:
            return False


class MotivateLoginPopup(PopupBase):
    """

    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "close": {"path": UPath(id_ == 'motivate_login_popup_delete_btn')},
            "close2": {"type": Control,
                             "path": UPath(id_ == "visual_area", visible_ == True) / UPath(id_ == "top_image_area", visible_ == True) / UPath(
                                 type_ == "TuxIconView", index=1)},
        }

    def handle(self):
        if self['close'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['close'].click()
        elif self['close2'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['close2'].click()
        else:
            return False


#   Inbox
class InboxFollowFriendPopup(PopupBase):
    """
        Inbox界面
        Follow your friends 弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "close": {"type": Control, "path": UPath(id_ == 'close')},
            "confirm": {"type": Control, "path": UPath(id_ == "disclaimer_confirm_btn")}
        }

    def handle(self):
        logger.info("popup %s handled" % self.__class__.__name__)
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing:
                return self[elem_name].click()


class ProfileFollowFriendPopup(PopupBase):
    """
        Profile界面
        Follow your friends 弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "close": {"type": Control, "path": UPath(id_ == 'close_icon_view')},
        }

    def handle(self):
        if self['close'].existing:
            logger.info("popup %s handed" % self.__class__.__name__)
            self['close'].click()
        else:
            return False


class CommunityGuidelinesPopup(PopupBase):
    """社区指引页面
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.splash.SplashActivity#1',  # 所在的Activity,不能为空
                   "process_name": None}

    def get_locators(self):
        return {
            "action_area": {"type": Control, "path": UPath(id_ == 'action_area')},
            "OK": {"type": Control, "root": "action_area", "path": UPath(text_ == "OK")}
        }

    def handle(self):
        if self['OK'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['OK'].click()
        else:
            return False


class VideoRecordPopop(PopupBase):
    """
    feed 页视频拍摄权限弹窗
    """
    window_spec = {"activity":'com.ss.android.ugc.aweme.shortvideo.ui.VideoRecordPermissionActivity'}

    def get_locators(self):
        return {
            "cancel_btn":{"path":UPath(text_ == 'Cancel')},
            "open_settings":{"path":UPath(type_ == 'com.bytedance.tux.widget.b')/0}
        }

    def handle(self):
        if self["cancel_btn"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["cancel_btn"].click()
        return False


#  VideoRecordPage
class VideoRecordPopup(PopupBase):
    """视频拍摄界面同步Popup
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.shortvideo.ui.VideoRecordNewActivity.*|com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity.*|com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity#1|com.ss.android.ugc.aweme.adaptation.saa.SAAActivity.*'  # 所在的Activity,不能为空
                   }

    def get_locators(self):
        return {
            "close_btn": {"path": UPath(~id_ == 'nav_end|close_button|story_tutorial_close_button', visible_ == True)},
            "ok_btn": {"path": UPath(id_ == "create_story_button")},
            "End": {"path": UPath(text_ == "End now", visible_ == True)},
            "continue": {"path": UPath(text_ == "continue|Continue", visible_ == True)},
            "cancel":{"path": UPath(id_ == "negative_button", text_ == "Cancel", visible_ == True)},
            "ok": {"path": UPath(text_ == "OK", visible_ == True)},
        }

    def handle(self):
        logger.info("popup %s handled" % self.__class__.__name__)
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing:
                return self[elem_name].click()
        return False


class VideoPublishPopup(PopupBase):
    """视频发布界面同步Popup
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.shortvideo.ui.VideoPublishActivity.*'  # 所在的Activity,不能为空
                   }

    def get_locators(self):
        return {
            "not_now_btn": {"path": UPath(
                type_ == "com.bytedance.tux.input.TuxTextView", id_ == "btn_not_now", visible_ == True)},
            "ok": {"path": UPath(id_ == "video_tag_intro_cta")},
            "video_ok": {"path": UPath(text_ == "OK")},
            "ok_auto_captions": {"path": UPath(id_ == "btn_dismiss")},
            "close": {"path": UPath(id_ == "iv_close")}
        }

    def handle(self):
        logger.info("popup %s handled" % self.__class__.__name__)
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing:
                return self[elem_name].click()


class SearchPopup(PopupBase):
    """
    Search消息弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.search.pages.core.ui.activity.SearchResultActivity.*"}

    def get_locators(self):
        return {
            "agree_btn": {"type": Control, "path": UPath(id_ == "btn_agree", visible_ == True)},
            "close": {"path": UPath(id_ == "iv_close")}
        }

    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].enabled:
                return self[elem_name].click()
        return False


#  MinePage
class FindFriendsPopup(PopupBase):
    window_spec = {"activity": "com.ss.android.ugc.aweme.relation.SocialFriendsActivity"}

    def get_locators(self):
        return {
            "skip": {"path": UPath(text_ == "Skip", visible_ == True)},
        }

    def handle(self):
        if self["skip"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["skip"].click()
        return False



class CloseFeedRestrictedPopup(PopupBase):
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "got_it": {"path": UPath(text_ == "Got it", visible_ == True)}
        }

    def handle(self):
        if self["got_it"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["got_it"].click()
        else:
            return False


class NotificationLaterPopup(PopupBase):
    """
    Notifications keep you up to date!
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "later_button": {"path": UPath(text_ == "Later", visible_ == True)},
            "close_btn": {"path": UPath(id_ == 'close_icon_view')}
        }

    def handle(self):
        if self["later_button"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["later_button"].click()
        elif self['close_btn'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self['close_btn'].click()
        else:
            return False


class CloseRestrictedFeaturePopup(PopupBase):
    """
    restricted some TikTok features in your location
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.I18nSignUpActivityWithNoAnimation.*|com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivityWithNoAnimation.*|com.ss.android.ugc.aweme.pns.universalpopup.core.ui.UniversalPopupActivity"}

    def get_locators(self):
        return {
            "got_it": {"path": UPath(text_ == "Got it", visible_ == True)},
            "ok": {"path": UPath(text_ == "OK", visible_ == True)}
        }

    def handle(self):
        logger.info("popup %s handled" % self.__class__.__name__)
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].visible:
                return self[elem_name].click()
        return False

class ClosePnsUniversalPopup(PopupBase):
    """
    关闭隐私侧冷启动出现的弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.pns.universalpopup.core.ui.UniversalPopupActivity"}
    def get_locators(self):
        return {
            "ok": {"path": UPath(~text_ == "OK|確定", visible_ == True)}
        }

    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].visible:
                logger.info("popup %s handled" % self.__class__.__name__)
                return self[elem_name].click()
        return False

class CloseHostPaymentPopup(PopupBase):
    """
    招待报酬弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "close": {"path": UPath(id_ == "closeImg")},
            "close_btn": {"path": UPath(id_ == 'close_icon_view')}
        }

    def handle(self):
        if self["close"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close"].click()
        elif self['close_btn'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['close_btn'].click()
            return True
        else:
            return False


class CloseFollowFriendsPopup(PopupBase):
    """
    关注好友弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity#1"}

    def get_locators(self):
        return {
            "close": {"path": UPath(id_ == "close_icon_view", visible_ == True)},
            "not_allow": {"path": UPath(text_ == "Don't allow", visible_ == True)},
        }

    def handle(self):
        if self["not_allow"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["not_allow"].click()
        elif self["not_allow"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["not_allow"].click()
        else:
            return False


class CloseFriendsPostPopup(PopupBase):
    """
    关闭好友作品弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "close": {"path":  UPath(id_ == 'friends_tab_intro_close_button')}
        }

    def handle(self):
        if self["close"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close"].click()
        else:
            return False


class SkipAddEmailAddressPopup(PopupBase):
    """
    关闭登录时填写邮箱地址弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.v2.ui.SignUpOrLoginActivity"}

    def get_locators(self):
        return {
            "skip_btn": {"path":  UPath(text_ == "Skip", visible_ == True)}
        }

    def handle(self):
        if self["skip_btn"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["skip_btn"].click()
        else:
            return False


class CloseAdjustClipsFeaturePopup(PopupBase):
    """
    关闭调整剪辑功能弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity#1|com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity|com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity#2"}

    def get_locators(self):
        return {
            "close": {"path":  UPath(id_ == 'close_button')},
            "post_now": {"path": UPath(text_ == 'Post Now')}
        }

    def handle(self):
        if self["close"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close"].click()
        if self["post_now"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["post_now"].click()
        else:
            return False
class ConfirmPopup(PopupBase):
    """
    开启直播时候确认按钮"""
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.adaptation.saa.SAAActivity"
    }
    def get_locators(self):
        return {
            "confirm": {"path": UPath(text_ == "确认")},
            "confidential_popup": {"type": Control, "path": UPath(id_ == "content_constraint_layout")}
        }

    def handle(self):
        if self["confidential_popup"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["confirm"].click()
        else:
            return False

class CloseFeedLanguagesPopup(PopupBase):
    """
    Feed页面语言选择弹窗
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "close": {"path": UPath(id_ == "tux_nav_bar") / UPath(id_ == "nav_end")}
        }

    def handle(self):
        if self["close"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close"].click()
        else:
            return False


# 弹窗的样式一样但是activity不一样
class CloseFeedLanguagesPopupNew(PopupBase):
    """
    Feed页面语言选择弹窗
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity#1"}

    def get_locators(self):
        return {
            "close": {"path": UPath(id_ == "tux_nav_bar") / UPath(type_ == "TuxIconView")}
        }

    def handle(self):
        if self["close"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close"].click()
        else:
            return False


class AccountStatusPopup(PopupBase):
    """
    账号异常登出弹窗，登出后重新登录
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.login.ui.LogoutDialogActivity"}

    def get_locators(self):
        return {
            "OK": {"path": UPath(id_ == "button1", visible_ == True)}
        }

    def handle(self):
        if self["OK"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self["OK"].click()
            logger.info("登出后重新登录")
            self.app.testcase.force_login()
            return True
        else:
            return False


# class InboxChangeStstusPopup(PopupBase):
#     """
#     inbox page turn on activity status popup
#     """
#     window_spec = {
#         "activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}
#
#     def get_locators(self):
#         return {
#             "close": {"path": UPath(id_ == "nav_end", visible_ == True)}
#         }
#
#     def handle(self):
#         if self["close"].existing:
#             logger.info("popup %s handled" % self.__class__.__name__)
#             return self["close"].click()




class MinePopup(PopupBase):
    """TikTok popup handler in Mine Panel
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.splash.SplashActivity'}

    def get_locators(self):
        return {
            "find_your_friends": {'type': Control,
                                  'path': UPath(id_ == 'title_tv', text_ == 'Find your friends on TikTok',
                                                visible_ == True)},
            "not_now": {'type': Button,
                        'path': UPath(id_ == 'action_area') / UPath(text_ == 'Not now', visible_ == True)},
            "follow_your_friends_control": {"type": Control,
                                            "path": UPath(id_ == 'title_recommend_user',
                                                          text_ == 'Follow your friends')},
            "follow_your_friends_close": {"type": Control,
                                          "path": UPath(id_ == 'layout_content') / UPath(id_ == 'close')},
            "general_not_now": {'type': Control, 'path': UPath(text_ == 'Not now', visible_ == True)},
            "general_later": {'type': Control, 'path': UPath(text_ == 'Later', visible_ == True)},
        }

    def handle(self):
        time.sleep(1)
        return self.handler_find_your_friends() or self.handler_follow_your_friends() or self.handler_general_not_now()

    def handler_find_your_friends(self):
        if self['not_now'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['not_now'].click()
            time.sleep(1)
            return True
        return False

    def handler_follow_your_friends(self):
        if self['follow_your_friends_close'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['follow_your_friends_close'].click()
            time.sleep(1)
            return True
        return False

    def handler_general_not_now(self):
        if self['general_not_now'].existing:
            self['general_not_now'].click()
            logger.info("popup %s handled" % self.__class__.__name__)
            time.sleep(1)
            return True
        return False

    def handler_general_later(self):
        if self['general_later'].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            self['general_later'].click()
            time.sleep(1)
            return True
        return


class AnchorLivePopup(PopupBase):
    """
    主播直播间弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LiveBroadcastActivity.*"}

    def get_locators(self):
        return {
            'continue': {'type': Control, 'path': UPath(text_ == 'Continue')},
            'btn_try': {'type': Control, 'path': UPath(id_ == 'btn_try')}
        }

    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing:
                logger.info("popup %s handled: %s" % (self.__class__.__name__, elem_name))
                return self[elem_name].click()

class QuickGiftPopup(PopupBase):
    """
    直播间内快捷礼物弹窗
    """
    window_spec = {'activity': "com.ss.android.ugc.aweme.live.LivePlayActivity#1"}

    def get_locators(self):
        return {
            'send_btn': {'type': Control, 'path': UPath(id_ == "giftPriceLayout")}
        }

    def handle(self):
        if self["send_btn"].existing:
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["send_btn"].click()

class RepostPopup(PopupBase):
    """
    Repost this video
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "tb_recommend": {"path": UPath(id_ == "tb_recommend", visible_ == True)},
            "iv_close": {"path": UPath(id_ == "iv_close", visible_ == True)},
        }

    def handle(self):
        if self["tb_recommend"].wait_for_existing(timeout=10, raise_error=False):
            logger.info("popup %s handled" % self.__class__.__name__)
            self["tb_recommend"].click()
            if self["iv_close"].wait_for_existing(timeout=10, raise_error=False):
                return self["iv_close"].click()
        return False

class ViewPostPopup(PopupBase):
    window_spec = {"activity": "com.ss.android.ugc.aweme.detail.ui.DetailActivity.*"}

    def get_locators(self):
        return {
            "btn_not_now": {"type": Control, "path": UPath(id_ == "btn_not_now", visible_ == True)},
        }

    def handle(self):
        if self["btn_not_now"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["btn_not_now"].click()


class SearchPopup(PopupBase):
    """
    Search消息弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.search.pages.core.ui.activity.SearchResultActivity.*"}

    def get_locators(self):
        return {
            "agree_btn": {"type": Control, "path": UPath(id_ == "btn_agree", visible_ == True)}
        }

    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].enabled:
                return self[elem_name].click()
        return False


class ProfilePagePostViewHistory(PopupBase):
    """profile页面视频浏览记录浮层"""

    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity#1|com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "close_btn": {"type": Control, "path": UPath(id_ == "viewer_dialog_exit_icon")},
        }

    def handle(self):
        if self["close_btn"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close_btn"].click()


class InboxPageActivityStatus(PopupBase):
    """
    Inbox页面打开活动状态弹窗
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.splash.SplashActivity.*'}

    def get_locators(self):
        return {
            'close_btn': {"type": Control, "path": UPath(id_ == 'sheet_nav_bar_container')/UPath(id_ == 'nav_end', visible_ == True)},
        }

    def handle(self):
        if self["close_btn"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close_btn"].click()


class AddedToFavoritesPopup(PopupBase):
    """
        视频创造者收到该视频被收藏的弹窗提醒，did维度
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.splash.SplashActivity.*'}

    def get_locators(self):
        return {
            'ok_btn': {"type": Control, "path": UPath(id_ == 'btn_ok_favorite', visible_ == True)},
        }

    def handle(self):
        if self["ok_btn"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["ok_btn"].click()


class USServiceUpdatePopup(PopupBase):
    """"
    US 服务更新弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.pns.universalpopup.core.ui.UniversalPopupActivity"}

    def get_locators(self):
        return {
            "later": {"type": Control, "path": UPath(text_ == "Remind me later")}
        }

    def handle(self):
        if self["later"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["later"].click()


class AddkeywordsPopup(PopupBase):
    """"
    add keywords to filter pop
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "close": {"type": Control, "path": UPath(id_ == "iv_close")}
        }

    def handle(self):
        if self["close"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("popup %s handled" % self.__class__.__name__)
            return self["close"].click()


class ReadStatusPopup(PopupBase):
    """
    Read status弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity.*"}

    def get_locators(self):
        return {
            "close": {"type": Control, "path": UPath(id_ == "iv_close", visible_ == True)}
        }

    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].visible:
                logger.info("popup %s handled" % self.__class__.__name__)
                return self[elem_name].click()
        return False


class NudeContentPopup(PopupBase):
    """
    Safeguards for nude content
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity.*"}

    def get_locators(self):
        return {
            "cancel_btn": {"type": Control, "path": UPath(text_ == "Cancel")}
        }

    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].wait_for_visible(timeout=5, raise_error=False):
                logger.info("popup %s handled" % self.__class__.__name__)
                return self[elem_name].click()
        return False

