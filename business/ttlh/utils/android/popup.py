# -*- coding: UTF-8 -*-

from uibase.upath import *
from uibase.controls import PopupBase


class AnchorLivePopup(PopupBase):
    """主播直播间弹窗
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LiveBroadcastActivity.*"
                               "|com.ss.android.ugc.aweme.adaptation.saa.SAAActivity.*"
                               "|com.ss.android.ugc.aweme.live.LivePlayActivity",}

    def get_locators(self):
        return {
            "Okay": {"path": UPath(text_ == "Okay", visible_ == True)},
            "Dismiss": {"path": UPath(text_ == "Dismiss", visible_ == True)},
            "兑换":{"path": UPath(text_ == "兑换", visible_ == True)}
        }

    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].visible:
                return self[elem_name].click()
        return False



