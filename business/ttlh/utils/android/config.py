# -*- coding: utf8 -*-

import os
from business.ttlh.utils.config import *

# ==================== Basic Config ====================
CUR_PATH = os.path.dirname(os.path.abspath(__file__))

# LIVE preview page
live_preview = "snssdk1233://openRecord?tab=live&enter_from=direct_shoot&source_params=%7B%22child_tab%22%3A%22video%22%7D"
game_live_preview = "snssdk1233://openRecord?&tab=live&enter_from=direct_shoot&source_params=%7B%22child_tab%22%3A%22screenshot%22%7D"

# ttmock file path of Android
device_ttmock_path = "/data/local/tmp/ttmock.json"

# ==================== TTMock Config ====================
default_mock_config = {
  "region": {
    "carrier_region": "MY",
    "mcc_region": "",
    "sys_region": "MY",
    "op_region": "MY"
  },
  "sso": {
    "bypass": True
  },
  "language": "en",
  "channel": "ttls_stability_test",
  "ab_reverse": False,
  "ab_settings_live": {
    "live_test_skip_aab_check": True,
    "multi_linkmic_skip_allow": True,
    "ttlive_one_tap_golive_entrance": True,
    "live_stream_performance_auheartbeatmonitor_enable": True,
    "linkmic_rust_linker_enable": 0
  }
}
