# -*- coding: utf8 -*-
import time

from utils.common.log_utils import logger
from shoots_android.control import *
from business.ttlh.utils.android import config as con
from uibase.upath import tag_


class BasePanel(Window):
    """界面相关基类
    """

    def click_by_name(self, ctrl_name, timeout=10, interval=0.5, raise_error=True, sleep_time=3):
        if self[ctrl_name].wait_for_visible(timeout=timeout, interval=interval, raise_error=raise_error):
            self[ctrl_name].click()
            time.sleep(sleep_time)

    def swipe(self, x_direction=0, y_direction=0, swipe_coefficient=3):
        '''滑动
        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        后续会支持斜着滑动

        :param coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        '''

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(
                x_direction, y_direction))

        # self._driver.drag(self.id, x1, y1, x2, y2)
        self.drag(x2, y2, x1 - rect.width / 2, y1 - rect.height / 2)
        time.sleep(1)


class FeedPanel(BasePanel):
    """Anchor live window
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"
                    "|com.ss.android.ugc.aweme.hybridkit.spark.TranslucentActivity"
                    "|com.ss.android.ugc.aweme.pns.universalpopup.core.ui.UniversalPopupActivity"
    }

    def get_locators(self):
        return {
            # 底部tab
            "profile": {"type": Control, "path": UPath(id_ == "main_bottom_button_me", visible_ == True)},
            # Settings 入口（登陆）
            "settings_btn_logged": {"type": Control,
                                    "path": UPath(type_ == "com.bytedance.tux.icon.TuxIconView",
                                                  desc_ == "Profile menu", visible_ == True)},
            "settings_privacy": {"type": Control, "path": UPath(text_ == "Settings and privacy")},
            # Feed_report
            "feed_report": {"type": Control, "path": UPath(id_ == 'desc', text_ == 'Report')}
        }

    def enter_setting_page(self, wait_time=3):
        """进入设置页
        :return:
        """
        # 进入Profile
        # feed_panel = FeedPanel(root=self.app)
        self.click_by_name("profile")
        # 点击设置按钮弹窗
        self.click_by_name("settings_btn_logged")
        # 弹窗点击
        self.click_by_name("settings_privacy")
        time.sleep(wait_time)

    def open_debug_panel(self, try_times=3):
        """
        循环长按profile控件，进入debug界面失败后，通过device长按
        """
        logger.info("wait for profile button visible...")
        self["profile"].wait_for_visible(timeout=30, raise_error=False)
        for _ in Retry(limit=try_times, raise_error=False):
            if self["profile"].wait_for_invisible(timeout=2, raise_error=False):
                break
            else:
                if self["feed_report"].wait_for_visible(timeout=1, raise_error=False):
                    self.app.get_device().press_back()

                time.sleep(3)
                self["profile"].refresh()
                self["profile"].wait_for_ui_stable()

                logger.info("long click profile button open debug_panel...")
                self["profile"].long_click(duration=5)
        else:
            if self["profile"].wait_for_visible(timeout=3, raise_error=False):
                me_tab_rect = self["profile"].rect.center
                logger.info("long click profile button open debug_panel...")
                self.app.get_device().long_click(me_tab_rect[0], me_tab_rect[1], duration=5)

    def set_ab_clone(self, abid, wait_time=15):
        """设置AB Clone
        :param abid: AB Clone的ABID号
        :return:
        """
        logger.info("Open debug panel...")
        self.open_debug_panel()

        logger.info("Open the AB Clone panel...")
        debug = DebugPanel(root=self.app)
        debug.click_ab_clone()

        logger.info(f'Set AB Clone for for device={self.app.get_device().model}...')
        ab_clone_panel = ABClonePanel(root=self.app)
        ab_clone_panel.input_ab_clone(content=abid, wait_time=wait_time)


class DebugPanel(Window):
    """Debug页
    """

    window_spec = {"activity": "com.bytedance.ies.assistant.AssistantActivity"
                               "|com.ss.android.ugc.aweme.local_test.impl.search.SearchDebugActivity"}

    def get_locators(self):
        return {
            "config_tab": {"type": Control, "path": UPath(desc_ == 'Config')},
            "ab_clone": {"type": Control,
                         "path": UPath(text_ == 'AB clone', id_ == 'title_text')},
        }

    def click_ab_clone(self):
        """进入AB clone配置页: Debug -> Config -> AB clone
        :return:
        """
        # 切换Config Tab
        if self["config_tab"].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Click config_tab...")
            self["config_tab"].click()

            if self["ab_clone"].wait_for_existing(timeout=5, raise_error=False):
                logger.info("Click ab_clone, enter ab_clone setting page...")
                self["ab_clone"].click()
                time.sleep(2)


class ABClonePanel(Window):
    """ABClone面板
    """
    window_spec = {'activity': 'com.bytedance.ies.abmock.debugtool.submain.CloneActivity'}

    def get_locators(self):
        return {
            "config_id": {"type": Control, "path": UPath(id_ == 'idInput', visible_ == True)},
            "download_icon": {"type": Control, "path": UPath(id_ == 'downloadBtn', visible_ == True)},
        }

    def input_ab_clone(self, content, wait_time=15):
        """输入ABID并设置
        :param wait_time: 等待资源下载时间
        :param content: ABID号
        :return:
        """
        if self["config_id"].wait_for_visible(timeout=3, raise_error=False):
            self["config_id"].input(content)
            time.sleep(1)

            self["download_icon"].click()
            time.sleep(wait_time)

