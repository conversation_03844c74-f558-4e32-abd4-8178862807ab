# -*- coding: utf8 -*-
import random
from re import S
import datetime
import sys
from utils.common.log_utils import logger
from shoots_android.control import *
from business.ttlh.utils.android import config as con
from uibase.upath import *
from shoots import logger
from business.ttlh.utils.android.AndroidBaseUI import BasePanel


class ItemContainer(BasePanel):
    """Item Container
    """

    def get_locators(self):
        return {
            "nick_name": {"path": UPath(id_ == "tv_user_name")},
            "accept_btn_follow": {"path": UPath(id_ == "btn_accept", visible_ == True)},
            "kick_off_button": {"path": UPath(id_ == "iv_ic_accept_kick_off", visible_ == True)}
        }


class GuestItemContainer(BasePanel):
    """Guest Item Container
    """
    elem_path = UPath(id_ == "item_container")
    elem_class = ItemContainer


class SingleMicView(BasePanel):
    """嘉宾侧各嘉宾画面内容
    """

    def get_locators(self):
        return {
            "no_guest_view": {"path": UPath(id_ == "empty_container", visible_ == True)},
            "nick_name": {"path": UPath(id_ == "nick_name_follow_status_container") / UPath(id_ == "nick_name")},
            "mute_btn": {"path": UPath(id_ == "online_player_mute")},
            "host_tag": {"path": UPath(id_ == "online_host_tag_layout")},
            "mute_video_bg": {"path": UPath(id_ == "online_player_mute_video_bg")},
        }


class MultiGuestSingleMicView(BasePanel):
    """嘉宾连麦: 嘉宾侧嘉宾画面View
    """
    elem_path = UPath(type_ == "android.widget.FrameLayout") / UPath(type_ == "android.widget.FrameLayout")
    elem_class = SingleMicView


class EffectCellView(BasePanel):
    """Effect面板子cell
    """

    def get_locators(self):
        return {
            "effect_image": {
                "path": UPath(id_ == "image", type_ == "com.bytedance.android.live.core.widget.HSImageView",
                              visible_ == True)}
        }


class EffectContainerItemView(BasePanel):
    """直播间内Effect面板View
    """
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")
    elem_class = EffectCellView


class main_panel(BasePanel):
    """main_panel
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity"
                    "|com.ss.android.ugc.aweme.search.pages.core.ui.activity.SearchResultActivity"

                    }
    def get_locators(self):
        return {
            "search":{'path':UPath(id_ == "favorite_button", visible_ == True)},
            "search_input":{'path':UPath(id_ == "et_search_middle")},
            "target_user":{'path':UPath(id_ == "recycler_view") / 0 / UPath(id_ == "tvl_unified_sug", depth=5)},
            "enter_room":{'path':UPath(type_ == "SmartAvatarBorderView", visible_ == True)},
            "up_load":{"path":UPath(id_ == "tv_strengthen_swipe_up_guide", visible_ == True)}
        }

    def search_anchor(self,anchor_name):
        # self.main_panel = main_panel(root=self.app)
        # self.main_panel.scroll(distance_y=-200)
        if self["search"].wait_for_visible(timeout=5, raise_error=False):
            self["search"].click()
            time.sleep(1)
            self["search_input"].input(anchor_name)
            time.sleep(1)
            if self["target_user"].wait_for_visible(timeout=5, raise_error=False):
                self["target_user"].click()
                time.sleep(1)
                if self["enter_room"].wait_for_visible(timeout=5, raise_error=False):
                    self["enter_room"].click()
                    time.sleep(1)
                    return True
                else:
                    return False
            else:
                return False

class HostLivePanel(BasePanel):
    """Anchor live window
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.live.GoLiveActivity.*|com.ss.android.ugc.aweme.live.LiveBroadcastActivity.*"
                    "|com.ss.android.ugc.aweme.shortvideo.ui.VideoRecordNewActivity.*"
                    "|com.ss.android.ugc.aweme.adaptation.saa.SAAActivity"
                    "|com.ss.android.ugc.aweme.adaptation.saa.SAAActivity#1"
                    "|com.ss.android.ugc.aweme.live.LiveBroadcastSceneWrapperActivity"
                    "|com.ss.android.ugc.aweme.live.LivePlayActivity"
                    "|PopupWindow.*",
    }

    def get_locators(self):
        return {
            # 直播预览页
            "confirm_18": {"type": Control, "path": UPath(id_ == "positive_button", ~text_ == "Continue|继续")},
            "Go LIVE": {'type': Control, 'path': UPath(text_ == "开启直播")},
            "confidential_popup": {"type": Control, "path": UPath(id_ == "content_constraint_layout")},
            "confidential_popup_confirm": {"type": Control, "path": UPath(text_ == "OK", visible_ == True)},
            "setting_btn": {"type": Control, "path": UPath(text_ == "设置")},
            # "video_quality_btn": {"type": Control, "path": UPath(text_ == "video_quality_group", visible_ == True)},
            "video_quality_btn": {"type": Control, 'path': UPath(text_ == "视频质量")},

            "540": {"type": Control, "path": UPath(text_ == "540p")},

            "reback_icon": {"type": Control, "path": UPath(id_ == "ll_left_icon", visible_ == True)},

            # 暂停/恢复直播
            "pause_live_button": {"type": Control, "path": UPath(~text_ == "Pause LIVE|暂停直播", visible_ == True)},
            "pause_confirm": {"type": Control,
                              "path": UPath(id_ == "positive_button", ~text_ == "Pause|暂停", visible_ == True)},
            "resume_live_button": {"type": Control, "path": UPath(id_ == "btn_continue_live", visible_ == True)},
            # More 设置项
            "more_setting_button": {"type": Control, "path": UPath(id_ == "ttlive_toolbar_more", visible_ == True)},
            "more_setting_page": {"type": Control, "path": UPath(type_ == "android.widget.LinearLayout",
                                                                 id_ == "scroll_content", visible_ == True)},
            "flip_camera_button": {"type": Control, "path": UPath(~text_ == "Flip camera|翻转相机", visible_ == True)},
            # 特效设置项
            "toolbar":{'path':UPath(id_ == "ttlive_toolbar_broadcast_effect") / UPath(id_ == "toolbar_icon", depth=5)},
            "beautiful":{'path':UPath(text_ == "美颜")},
            "switch_beautiful":{'path':UPath(id_ == "beauty_switch_op2")},

            "toolbar_container": {"type": Control, "path": UPath(id_ == "toolbar_container", visible_ == True)},
            "effect_container": {"type": Control,
                                 "path": UPath(id_ == "effect_container", visible_ == True)},
            "clear_effect": {"type": Control,
                             "path": UPath(id_ == "bt_clear", visible_ == True)},
            "effect_open_button": {"type": Control,
                                   "path": UPath(id_ == "open_button", visible_ == True)},
            "enhance_button": {"type": Control,
                               "path": UPath(id_ == "ttlive_toolbar_broadcast_effect", visible_ == True)},
            "effect_button": {"type": Control,
                              "path": UPath(id_ == "toolbar_text", ~text_ == "Effects|特效", visible_ == True)},
            "effect_panel_popup": {"type": Control,
                                   "path": UPath(id_ == "bottom_function_button_container", visible_ == True)},
            "effect_content_container": {"type": EffectContainerItemView,
                                         "path": UPath(id_ == "sticker_dialog_page") / UPath(id_ == "content_container",
                                                                                             visible_ == True)},
            "effect_retry_icon": {"type": Control,
                                  "path": UPath(id_ == "sticker_dialog_retry_icon", visible_ == True)},

            # 关播
            "close_live": {"type": Control, "path": UPath(id_ == 'live_close_widget_container', visible_ == True)},
            "close_certain": {"type": Control, "path": UPath(~text_ == "End now|立即结束", visible_ == True)},
            "close_cancel": {"type": Control, "path": UPath(~text_ == "Cancel|取消", visible_ == True)},
            # 续播按钮
            "resume_button": {"type": Control, "path": UPath(id_ == "positive_button",
                                                             ~text_ == "Resume|恢复", visible_ == True)},
            "resume_cancle":{"path":UPath(text_ == "取消")},

            # 嘉宾连麦
            "guest_request_button":{'path':UPath(id_ == "fl_request_enhanced_container")},
            "request":{'path':UPath(id_ == "connect_switch_btn")},
            "accept":{'path':UPath(text_ == "接受")},
            "join":{'path':UPath(id_ == "加入")},
            "switch":{'path':UPath(text_ == "切换")},
            "layout_setting":{'path':UPath(id_ == "btn_layout_setting")},
            "folat_panel":{'patg':UPath(id_ == "layout_choose_im_panel")},
            "float_grid":{'path':UPath(id_ == "layout_choose_im_grid")},
            "fixed_panel":{'path':UPath(id_ == "layout_choose_im_fixed_panel")},
            "fixed_grid":{'path':UPath(id_ == "layout_choose_im_fixed_grid")},


            "multi_guest_button": {"type": Control,
                                   "path": UPath(tag_ == "MULTIGUEST", id_ == "ttlive_toolbar_default",
                                                 visible_ == True)},
            "disconnect_button": {"type": Control,
                                  "path": UPath(id_ == "btn_layout_kick_out_all", visible_ == True)},
            "btn_layout_setting": {"type": Control,
                                   "path": UPath(id_ == "btn_layout_setting", visible_ == True)},
            "disconnect_confirm": {"type": Control,
                                   "path": UPath(text_ == "Disconnect", visible_ == True)},
            "recycler_view": {"type": GuestItemContainer,
                              "path": UPath(id_ == "recycler_view", visible_ == True)},
            "expand_button_text": {"type": Control, "path": UPath(id_ == "text_expand", visible_ == True)},
            "expand_button": {"type": Control, "path": UPath(id_ == "container_expand", visible_ == True)},

            "multi_guest_mic_view": {"type": MultiGuestSingleMicView,
                                     "path": UPath(id_ == "ttlive_link_layout_sdk_container")},

            # 主播连麦
            "co_host_button": {'path':UPath(text_ == "+主播")},
            "co_host_search":{'path':UPath(id_ == "search_right_view")},
            "invite_name":{'path':UPath(id_ == "fl_intput_hint_container")},
            "search_user_list":{'path':UPath(id_ == "search_result_view")},
            "search_invite":{'path':UPath(id_ == "bt_invite_list_invite", enabled_ == True)},
            "invite_user_list": {'type': Control, 'path': UPath(id_ == "invite_user_list_view", visible_ == True)},
            "invite_list_expand_btn": {'type': Control,
                                       'path': UPath(id_ == "tv_invite_list_expand_collapse", visible_ == True)},
            "cohost_accept": {'type': Control, 'path': UPath(text_ == "接受")},
            "cohost_disconnect": {'type': Control, 'path': UPath(id_ == "right_view_container", visible_ == True)},
            "cohost_disconnect_confirm": {'type': Control,
                                          'path': UPath(~text_ == "Disconnect|断开连接", visible_ == True)},

            "multi_cohost_panel": {'type': Control, 'path': UPath(id_ == "view_link_user_list", visible_ == True)},
            "multi_cohost_empty_icon": {'type': Control, 'path': UPath(id_ == "view_empty_icon", index=0)},

            # 连麦状态
            "in_cohost_status": {"type": Control, "path": UPath(id_ == "iv_in_linkmic", visible_ == True)},

            # Match/PK
            "cohost_match_btn": {"type": Control,
                                 "path": UPath(tag_ == "INTERACTION_PK", visible_ == True)},
            "cohost_match_notice_popup": {"type": Control,
                                          "path": UPath(~text_ == "试试看|Try it", visible_ == True)},
            "cohost_match_invite_btn": {"type": Control,
                                        "path": UPath(id_ == "invite_btn", visible_ == True)},
            "cohost_match_accept_btn": {"type": Control,
                                        "path": UPath(id_ == "accept_btn", visible_ == True)},
            "cohost_match_leave_confirm_btn": {"type": Control,
                                               "path": UPath(id_ == "positive_button", visible_ == True)},
            "cohost_match_status": {"type": Control,
                                    "path": UPath(id_ == "view_health_bar", visible_ == True)},

            # 主播连麦断开评价页面
            "popup_coordinator": {"type": Control, "path": UPath(id_ == "popup_coordinator", visible_ == True)},
            "co_host_evaluation": {"type": Control,
                                   "path": UPath(~desc_ == "the technical performance of the co-host feature",
                                                 type_ == "com.lynx.FakeViews.FlattenView", visible_ == True)},

            "start_live": {"path": UPath(text_ == "直播")},
            'live_settings': {"path": UPath(desc_ == "设置") / UPath(id_ == "wv_pp", depth=5)},
            'close1': {'path': UPath(text_ == "管理员")},
            'video_resolution': {"path": UPath(text_ == "视频质量")},
            '1080p': {"path": UPath(text_ == "1080p")},
            '720p': {"path": UPath(text_ == "720p")},
            "关闭":{"path":UPath(text_ == "关闭")},
            "设置":{'path':UPath(text_ == "设置")},
            "Confirm":{'path':UPath(text_ == "确认")},
            "Okay":{'path':UPath(text_ == "Okay")},
        }

    def slide_back_for_dismiss_popup(self, timeout=5, sleep_time=5):
        """页面侧滑, 确保弹窗隐藏
        :param timeout:
        :param sleep_time:
        :return:
        """
        self.app.get_device().back()

        # 避免左滑导致退出直播
        if self["close_cancel"].wait_for_visible(timeout=timeout, raise_error=False):
            self["close_cancel"].click()
            time.sleep(sleep_time)

    def switch_beautiful(self,Flag=True):
        if Flag==True:
            if self["toolbar"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click effect button...")
                self["toolbar"].click()
                time.sleep(2)
            if self["beautiful"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click enhance button...")
                self["beautiful"].click()
                time.sleep(2)
            if self["switch_beautiful"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click effect open button...")
                self["switch_beautiful"].click()
                time.sleep(2)
        self.slide_back_for_dismiss_popup()


    def start_live(self, choose_quality=False,quality="",times: int = 5, wait_time=5, is_resume=False):
        try:
            self.app.open_scheme(con.live_preview)
        except RuntimeError:
            time.sleep(10)
            self.app.open_scheme(con.live_preview)
        time.sleep(2)
        if self["Confirm"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click 确认...")
            self["Confirm"].click()
            time.sleep(2)

        if self['resume_cancle'].wait_for_visible(timeout=5, raise_error=False):
            logger.info("取消续播...")
            self["resume_cancle"].click()
            time.sleep(2)
        if choose_quality==False:
            if self['Go LIVE'].wait_for_visible(timeout=5, raise_error=True):
                logger.info("Click Go LIVE button...")
                self['Go LIVE'].click()
                time.sleep(2)
        elif choose_quality==True:
            if self['setting_btn'].wait_for_visible(timeout=5, raise_error=True):
                logger.info("Click setting_btn...")
                self['setting_btn'].click()
                time.sleep(2)
                if self['关闭'].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click 关闭...")
                    self['关闭'].click()
                    time.sleep(2)
                if self['video_quality_btn'].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click video_quality_btn...")
                    self['video_quality_btn'].click()
                    if quality=="1080p":
                        if self['1080p'].wait_for_visible(timeout=5, raise_error=False):
                            logger.info("Click 1080p...")
                            self['1080p'].click()
                            time.sleep(2)
                    elif quality=="720p":
                        if self['720p'].wait_for_visible(timeout=5, raise_error=False):
                            logger.info("Click 720p...")
                            self['720p'].click()
                            time.sleep(2)
                    if self["reback_icon"].existing:
                        logger.info("Click reback_icon to back past page...")
                        self["reback_icon"].click()
                        time.sleep(2)
                    self.app.get_device().back()
                        # 避免左滑导致退出直播
                    if self["close_cancel"].wait_for_visible(raise_error=False):
                        self["close_cancel"].click()
            if self['Go LIVE'].wait_for_visible(timeout=5, raise_error=True):
                logger.info("Click Go LIVE button...")
                self['Go LIVE'].click()
                time.sleep(3)
            # 开播或续播后等待页面加载时长
            time.sleep(wait_time)


    def close_live(self):
        """
        :return:
        """
        if self["close_live"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click close_live...")
            self["close_live"].click()
        if self['close_certain'].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click close_certain...")
            self["close_certain"].click()
        # if self['Okay'].wait_for_visible(timeout=5, raise_error=False):
        #     logger.info("Click Okay...")
        #     self['Okay'].click()
        time.sleep(3)

    def set_video_quality(self, quality, sleep_time=3):
        """
        :param sleep_time:
        :param quality:
        :return:
        """
        if self["setting_btn"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click setting_btn...")
            self["setting_btn"].click()
            time.sleep(2)

            if self["video_quality_btn"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click video_quality_btn...")
                self["video_quality_btn"].click()
                time.sleep(2)

                new_quality = f"{quality}p"
                expect_quality_locator = {
                    new_quality: {"type": Control,
                                  "path": UPath(text_ == new_quality, id_ == "quality_title", visible_ == True)}}
                self.locators.update(expect_quality_locator)
                if self[new_quality].existing:
                    logger.info("Select video_quality = %s..." % new_quality)
                    self[new_quality].click()
                else:
                    # 开播测速建议分辨率
                    suggest_quality_locator = {
                        "suggest_quality": {"type": Control,
                                            "path": UPath(id_ == "suggest_label", text_ == "Suggested")}
                    }
                    self.locators.update(suggest_quality_locator)

                    if self["suggest_quality"].existing:
                        logger.info("Select suggest_quality...")
                        self[new_quality].click()

                time.sleep(sleep_time)

            # 隐藏相关弹窗, 避免阻塞后续开播操作
            if self["reback_icon"].existing:
                logger.info("Click reback_icon to back past page...")
                self["reback_icon"].click()
                time.sleep(2)

            if self["video_quality_btn"].existing:
                # 侧滑隐藏弹窗
                self.app.get_device().back()
                time.sleep(2)

    def go_live(self, times: int = 10, quality="", wait_time=5, is_resume=False):
        """Go LIVE through UI Click Operator
        :param quality: 开播视频质量
        :param is_resume: 是否续播
        :param wait_time: 开播或续播后等待页面加载时长
        :param times: 开播或续播 失败重试次数
        :return:
        """
        try:
            self.app.open_scheme(con.live_preview)
        except RuntimeError:
            time.sleep(10)
            self.app.open_scheme(con.live_preview)
        time.sleep(5)

        # 18岁检测
        self.click_by_name("confirm_18", timeout=5, sleep_time=5, raise_error=False)

        if not is_resume:
            # Go LIVE
            for _ in Retry(limit=times, raise_error=False):
                # 开播: 公司confidential弹窗
                if self["confidential_popup"].existing:
                    self["confidential_popup_confirm"].click()

                # 先判断是否处于续播状态
                if self["resume_button"].wait_for_visible(timeout=10, raise_error=False):
                    logger.info("Click Resume button back to LIVE")
                    self["resume_button"].click()
                else:
                    if self["Go LIVE"].wait_for_visible(timeout=10, raise_error=False):
                        logger.info("golive is ok")

                        # 判断是否需要设置开播分辨率
                        if quality != "":
                            logger.info("Set video_quality == %s..." % quality)
                            self.set_video_quality(quality=quality)

                        logger.info("Click go LIVE button...")
                        # 开播异常兜底, 避免APP异常退出, 续播逻辑影响
                        self["Go LIVE"].click()
                        time.sleep(3)

                        # 点击开播时, 18岁隐私弹窗确认
                        if self["confirm_18"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                            self["confirm_18"].click()

                time.sleep(wait_time)
                if self.is_living():
                    logger.info("Host go Live: SUCCESS...")
                    break
        else:
            # 预览页续播: 部分机型加载校验
            if self["resume_button"].wait_for_visible(timeout=40, raise_error=True):
                self["resume_button"].click()

                # 判断是否进入直播间, 若未进入, 再校验是否在预览页, 若在，则点击直播按钮开播
                if self["close_live"].wait_for_visible(timeout=15, raise_error=False):
                    logger.info("Host resume Live: SUCCESS...")

        # 开播或续播后等待页面加载时长
        time.sleep(wait_time)

    def requset_link_mic(self):
        if self["guest_request_button"].wait_for_visible(timeout=5, raise_error=True):
            logger.info("Click guest_request_button button...")
            self["guest_request_button"].click()
            time.sleep(2)
            if self["request"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click guest_request_confirm button...")
                self["request"].click()
                time.sleep(2)
    def accept_link_mic(self):
        if self["guest_request_button"].wait_for_visible(timeout=5, raise_error=True):
            logger.info("Click guest_request_button button...")
            self["guest_request_button"].click()
            time.sleep(2)
        if self["accept"].wait_for_visible(timeout=5, raise_error=False):
            logger.info("Click accept button...")
            self["accept"].click()
            time.sleep(2)

    def choose_layout(self,num):
        if self["guest_request_button"].wait_for_visible(timeout=5, raise_error=True):
            logger.info("Click guest_request_button button...")
            self["guest_request_button"].click()
            time.sleep(2)
        if self["layout_setting"].wait_for_visible(timeout=5, raise_error=True):
            logger.info("Click layout button...")
            self["layout_setting"].click()
            time.sleep(2)
            if num==1:
                if self["float_panel"].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click layout_1 button...")
                    self["float_panel"].click()
                    time.sleep(2)
            elif num==2:
                if self["float_grid"].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click layout_2 button...")
                    self["float_grid"].click()
                    time.sleep(2)
            elif num==3:
                if self["fixed_panel"].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click layout_3 button...")
                    self["fixed_panel"].click()
                    time.sleep(2)
            elif num==4:
                if self["fixed_grid"].wait_for_visible(timeout=5, raise_error=False):
                    logger.info("Click layout_4 button...")
                    self["fixed_grid"].click()
                    time.sleep(2)

            if self["switch"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click switch button...")
                self["switch"].click()
                time.sleep(2)
            self.slide_back_for_dismiss_popup()


    def pause_live(self, is_recover=False, times=5):
        """
        :param is_recover:
        :return:
        """
        if is_recover:
            logger.info("Click Resume button back to LIVE")
            self.click_by_name("resume_live_button", raise_error=False)
        else:
            logger.info("Click More button...")
            self.click_by_name("more_setting_button")
            for _ in Retry(limit=times, raise_error=False):
                if self["pause_live_button"].wait_for_visible(timeout=15, raise_error=False):
                    logger.info("Click pause_live_button button...")
                    self["pause_live_button"].click()
                    # 第一次点击"暂停"确认弹窗
                    if self["pause_confirm"].wait_for_visible(timeout=5, raise_error=False):
                        self["pause_confirm"].click()
                    break

                logger.info("Swipe up to look for the pause_live_button")
                self["more_setting_page"].swipe(y_direction=1, swipe_coefficient=5)
                time.sleep(2)

    def effect_dressup(self, is_clean=False, try_times=3, sleep_time=3):
        """随机选择特效并生效
        :return:
        """
        logger.info("Whether clean effect_dressup? %s..." % is_clean)
        if self["effect_open_button"].existing:  # 底部特效栏展开按钮
            self["effect_open_button"].click()
            time.sleep(sleep_time)
        elif self["enhance_button"].wait_for_visible(timeout=5, raise_error=False):  # Enhance -> Effect
            logger.info("Host Click '+Enhance'...")
            self["enhance_button"].click()

            if self["effect_button"].wait_for_visible(timeout=5, raise_error=False):
                logger.info("Click Effects button enter list...")
                self["effect_button"].click()
                time.sleep(sleep_time)

                # "长按预览特效" 弹窗
                if self["effect_panel_popup"].wait_for_visible(timeout=5, raise_error=False):
                    self["effect_panel_popup"].click()
                    time.sleep(sleep_time)

        # 特效面板展开, 随机选择或反选任意特效装扮
        if self["clear_effect"].wait_for_visible(timeout=5, raise_error=False):
            if not is_clean:
                for _ in Retry(limit=try_times, raise_error=False):
                    # 判断特效面板是否加载完成
                    if not self["effect_content_container"].wait_for_visible(timeout=5, raise_error=False):
                        # 网络异常, 点击重试icon加载面板
                        if self["effect_retry_icon"].wait_for_visible(timeout=5, raise_error=False):
                            self["effect_retry_icon"].click()
                            time.sleep(sleep_time)
                        continue

                    # 随机选择任一特效生效
                    effect_items = self["effect_content_container"].items()
                    if len(effect_items) > 0:
                        selected_icon = 0 if len(effect_items) < 8 else random.randint(0, len(effect_items) - 8)

                        try:
                            if effect_items[selected_icon]["effect_image"].wait_for_visible(timeout=5,
                                                                                            raise_error=False):
                                logger.info("Click the %d effect ..." % selected_icon)
                                effect_items[selected_icon]["effect_image"].click()
                        except Exception as e:
                            logger.error(
                                "Click the %d effect occur Exception, Detail: %s   ..." % (selected_icon, str(e)))

                            logger.info("Fallback: Get the 0 effect ...")
                            effect_items[0]["effect_image"].click()
                    # 退出循环
                    break
            else:
                logger.info("Click clear_effect button to close Effect...")
                self["clear_effect"].click()
            # loading
            time.sleep(sleep_time)

        # 隐藏特效面板, 避免影响后续操作
        if self["effect_container"].existing or self["toolbar_container"].existing:
            for _ in Retry(limit=try_times, raise_error=False):
                # 左滑隐藏弹窗
                logger.info("Hide Effect panel through device slide back...")
                self.slide_back_for_dismiss_popup(sleep_time=sleep_time)

                if not self["effect_container"].existing or not self["toolbar_container"].existing:
                    logger.info("effect_container/toolbar_container has been hided...")
                    break

    def camera_flip(self, sleep_time=3, times=3):
        """翻转相机
        :return:
        """
        logger.info("Click More button...")
        self.click_by_name("more_setting_button")

        if self["flip_camera_button"].wait_for_visible(timeout=10, raise_error=False):
            logger.info("Click flip_camera button...")
            self["flip_camera_button"].click()
            time.sleep(2)

            # 左滑隐藏弹窗
            for _ in Retry(limit=times, raise_error=False):
                logger.info("Hide More -> Tools page through device slide back...")
                self.slide_back_for_dismiss_popup(timeout=3, sleep_time=sleep_time)

                if self["flip_camera_button"].wait_for_invisible(timeout=3, raise_error=False):
                    logger.info("Hide More -> Tools page success...")
                    break

    def end_live(self, times=3, sleep_time=3):
        """End LIVE through UI Click Operator
        :return:
        """
        if self["close_live"].wait_for_visible(timeout=5, raise_error=False):
            # End LIVE
            for _ in Retry(limit=times, raise_error=False):
                self.click_by_name("close_live")

                # Confirm
                if self["close_certain"].wait_for_visible(timeout=5, raise_error=False):
                    self["close_certain"].click()
                    break
            time.sleep(sleep_time)

    def host_leave_channel(self, sleep_time=3):
        """The Host leave multi-guest channel
        :param sleep_time:
        :return:
        """
        logger.info("Host Click '+Hosts'")
        if self["multi_guest_button"].wait_for_visible(timeout=15):
            logger.info("Host Click '+Guests'")
            # 初步验证Android 连续点击2次嘉宾连麦按钮, 方可触发成功
            for _ in Retry(limit=3):
                self["multi_guest_button"].click()
                time.sleep(sleep_time)

                # 判断是否成功点击嘉宾连麦按钮
                if self["disconnect_button"].wait_for_visible(timeout=5, raise_error=False):
                    break

            logger.info("Host Click disconnect button")
            self.click_by_name("disconnect_button")

            logger.info("Host Click confirm disconnect")
            self.click_by_name("disconnect_confirm")
            time.sleep(sleep_time)

    def leave_co_host(self, sleep_time=3):
        """Leave Co-Host
        :param sleep_time:
        :return:
        """
        # Click Co-Host button
        # self.click_by_name("co_host_button")
        for _ in Retry(limit=3):
            self["co_host_button"].click()
            time.sleep(sleep_time)

            if self["cohost_disconnect"].existing:
                break

        logger.info("Host disconnect")
        self.click_by_name("cohost_disconnect")

        logger.info("Host disconnect confirm")
        self.click_by_name("cohost_disconnect_confirm")
        time.sleep(sleep_time)

    def co_host_invite(self, be_invited_user_name, times=10, sleep_time=5):
        """Host invite other host to co-host
        :param be_invited_user_name:
        :param times:
        :param sleep_time:
        :return:
        """
        logger.info("Host click co_host_button to invite other host...")
        if self["co_host_button"].wait_for_visible(timeout=15, raise_error=True):
           self["co_host_button"].click()

        if self["co_host_search"].wait_for_visible(timeout=5, raise_error=True):
            self["co_host_search"].click()
        if self['invite_name'].wait_for_visible(timeout=5, raise_error=True):
            self["invite_name"].input(be_invited_user_name)
            time.sleep(sleep_time)
        if self['search_invite'].wait_for_visible(timeout=5, raise_error=True):
            self["search_invite"].click()
            time.sleep(sleep_time)
    def host_invite_co_host(self, be_invited_user_name, times=10, sleep_time=3):
        """Host invite other host to co-host
        :param be_invited_user_name:
        :param times:
        :param sleep_time:
        :return:
        """
        logger.info("Host click co_host_button to invite other host...")
        if self["co_host_button"].wait_for_visible(timeout=5, raise_error=False):
            # self.click_by_name("co_host_button", timeout=15)
            for _ in Retry(limit=3):
                self["co_host_button"].click()
                time.sleep(sleep_time)

                if self["invite_user_list"].existing or self["multi_cohost_panel"].existing:
                    break

        # 多主播连麦(>2)邀请
        if self["multi_cohost_panel"].wait_for_visible(timeout=5, raise_error=False):
            # 校验是否有空麦位可连麦
            if self["multi_cohost_empty_icon"].wait_for_visible(timeout=5, raise_error=False):
                self["multi_cohost_empty_icon"].click()
            else:
                logger.info("Don't have enough position to co-host connect...")
                return

        # wait for invite_user_list
        if self["invite_user_list"].wait_for_visible(timeout=60):
            # Invite button tag format: {username},state:{state(0|1|2)}
            # Invite status: 0: 可邀请 1: 邀请中（点击可以撤销）2：不可用（灰色不可点击）3：MATURE_THEME
            invite_button_tag = "%s,state:0" % be_invited_user_name
            invite_button_locator = {
                be_invited_user_name: {"type": Control,
                                       "path": UPath(tag_ == invite_button_tag, id_ == "bt_invite_list_invite",
                                                     visible_ == True)}}
            self.locators.update(invite_button_locator)

            logger.info("Check the user list for be_invited_user\n")
            for i in range(times):
                # See more
                if self["invite_list_expand_btn"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                    logger.info("Click see more button for expand_collapse...")
                    self["invite_list_expand_btn"].click()
                    time.sleep(2)

                is_exist = False
                if self[be_invited_user_name].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                    if i > 0:
                        time.sleep(10)
                    is_exist = True
                else:
                    logger.info("Swipe up, in case the UI element be blocked...")
                    self["invite_user_list"].swipe(y_direction=1, swipe_coefficient=3)
                    time.sleep(2)
                    if self[be_invited_user_name].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                        is_exist = True
                    else:
                        logger.info("Swipe down, refresh invite_user_list page for %s..." % be_invited_user_name)
                        self["invite_user_list"].swipe(y_direction=-1, swipe_coefficient=8)
                        time.sleep(3)
                        self["invite_user_list"].swipe(y_direction=-1, swipe_coefficient=8)
                        time.sleep(3)

                # be_invited_user_name exist
                if is_exist:
                    logger.info("Host click the %s invite button" % be_invited_user_name)
                    self.click_by_name(be_invited_user_name)
                    time.sleep(sleep_time)
                    break
            else:
                raise Exception("Don't find Host(%s) in co-host list page..." % be_invited_user_name)

    def host_accept_co_host(self, timeout=60):
        """Host accept co-host connect
        :return:
        """
        # 主播侧等待接受弹窗
        self["cohost_accept"].wait_for_visible(timeout=timeout, interval=1, raise_error=True)

        # 主播接受邀请上麦
        self["cohost_accept"].click()

        # 等待接受连麦,RTC合流
        logger.info("Wait for accept linkmic, RTC Stream Merge...")
        self["in_cohost_status"].wait_for_visible(timeout=60, interval=1, raise_error=True)

    def close_cohost_lynxview_popup(self, times=3, sleep_time=3):
        """Check whether the lynxview popup exists, cancel popup
        :return:
        """
        # 注: 经验证lynxview校验不是很准, 所以需要在"左滑"后校验是否会退出直播
        if self["popup_coordinator"].wait_for_visible(timeout=10, raise_error=False):
            self["popup_coordinator"].click()
            time.sleep(2)

        # 校验是否关闭弹窗, 否则通过侧滑方式关闭
        for _ in Retry(limit=times, raise_error=False):
            # 左滑隐藏弹窗
            self.slide_back_for_dismiss_popup(timeout=10, sleep_time=sleep_time)

            if self["co_host_button"].existing:
                break

            time.sleep(sleep_time)

    def permit_apply_for_multi(self, nick_name="", wait_time=3):
        """嘉宾连麦: 主播接受观众上麦申请
        :return:
        """
        # 点击嘉宾连麦按钮
        if self["multi_guest_button"].wait_for_visible(timeout=10):
            logger.info("Click multi_guest_button...")
            # 初步验证Android 连续点击2次嘉宾连麦按钮, 方可触发成功
            for _ in Retry(limit=3):
                self["multi_guest_button"].click()
                time.sleep(wait_time)

                # 判断是否成功点击嘉宾连麦按钮
                if self["btn_layout_setting"].wait_for_visible(timeout=5, raise_error=False):
                    break

            # 选择嘉宾request按钮, 并点击 Accept 确认连麦
            if self["recycler_view"].wait_for_ui_stable():
                for cell in self["recycler_view"].items():
                    if not cell["nick_name"].existing or not cell["accept_btn_follow"].existing:
                        continue

                    request_nick_name = cell["nick_name"].text
                    logger.info("Guest request: %s..." % request_nick_name)
                    # 兜底: 指定用户或当用户昵称为空时
                    if nick_name == "" or nick_name == request_nick_name:
                        logger.info("Host[%s] accept guest request..." % nick_name)
                        cell["accept_btn_follow"].click()

                        # 若指定nickName, 则点击Accept按钮退出; 否则主播接受所有嘉宾的请求
                        if nick_name != "":
                            time.sleep(3)
                            break

    def change_guest_layout_for_multi(self, scene, nick_name=""):
        """嘉宾连麦: 主播放大嘉宾画面
        :param scene:
        :param nick_name:
        :return:
        """
        # 选择嘉宾request按钮, 并点击 Accept 确认连麦
        for cell in self["multi_guest_mic_view"].items():
            # 空麦位 或 主播麦位
            if cell["no_guest_view"].existing or not cell["nick_name"].existing or cell["host_tag"].existing:
                continue

            # 任一 或 指定嘉宾
            cell_nick_name = cell["nick_name"].text
            if nick_name == "" or nick_name == cell_nick_name:
                logger.info("Click Guest(%s) layout for %s..." % (cell_nick_name, scene))
                cell.click()

                expand_button_text = self["expand_button_text"].text
                logger.info("Expand Button text status: Expected=%s, Real=%s..." % (scene, expand_button_text))
                if expand_button_text == scene:
                    self["expand_button"].click()
                    time.sleep(3)

                return cell_nick_name
        else:
            return ""

    def is_cohost_status(self):
        """嘉宾: 根据当前画面UI状态判断是否处于连麦状态
        :return:
        """
        is_existing = self["in_cohost_status"].existing
        logger.info("whether Co-Host status is connected? %s..." % is_existing)

        return is_existing

    def is_living(self):
        """判断是否直播状态
        :return:
        """
        return self["close_live"].existing

    def check_match_status(self):
        """校验Match状态是否成功
        :return:
        """
        return self["cohost_match_status"].existing

    def match_for_co_host(self, scene):
        """主播连麦: Match(PK) 操作
        :param scene: invite / accept / leave
        :return:
        """
        if scene in ["invite", "leave"]:
            # 主播连麦: Match按钮
            if self["cohost_match_btn"].wait_for_visible(timeout=15):
                logger.info("click cohost_match_btn start Match...")
                for _ in Retry(limit=3):
                    self["cohost_match_btn"].click()
                    time.sleep(2)

                    if self["cohost_match_notice_popup"].existing:
                        break

            # 首次PK弹窗确认
            if self["cohost_match_notice_popup"].wait_for_ui_stable():
                logger.info("click cohost_match_notice_popup confirm 'Try it'...")
                self["cohost_match_notice_popup"].click()
                time.sleep(2)

            if scene == "invite":  # 开始PK
                logger.info("Click cohost_match_invite_btn...")
                # 点击开始PK
                self.click_by_name("cohost_match_invite_btn", timeout=5, sleep_time=3)
            elif scene == "leave":  # 提前离开PK
                logger.info("Click cohost_match_leave_confirm_btn...")
                # 点击确认离开
                self.click_by_name("cohost_match_leave_confirm_btn", timeout=5, sleep_time=3)
                # 校验是否成功退出PK状态
                if self.check_match_status():
                    logger.info("Leave Match: Success...")
        elif scene == "accept":
            if self["cohost_match_accept_btn"].wait_for_visible(timeout=10, raise_error=False):
                self["cohost_match_accept_btn"].click()
                time.sleep(2)

            # 校验是否成功退出PK状态
            if not self.check_match_status():
                logger.info("Accept Match: Success...")


class ViewerLivePanel(BasePanel):
    """Viewer live window
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.live.GoLiveActivity.*|com.ss.android.ugc.aweme.live.LiveBroadcastActivity.*"
                    "|com.ss.android.ugc.aweme.shortvideo.ui.VideoRecordNewActivity.*"
                    "|com.ss.android.ugc.aweme.adaptation.saa.SAAActivity|com.ss.android.ugc.aweme.live.LivePlayActivity.*"
    }

    def get_locators(self):
        return {
            "confirm_18": {"type": Control, "path": UPath(id_ == "positive_button", text_ == "Continue")},
            "close_cancel": {"type": Control, "path": UPath(~text_ == "Cancel|取消", visible_ == True)},
            # 连麦状态
            "disconnect_confirm": {"type": Control,
                                   "path": UPath(~text_ == "Got it|知道了", visible_ == True)},

            # 嘉宾连麦
            "multi_guest_button": {"type": Control,
                                   "path": UPath(tag_ == "MULTIGUEST", visible_ == True)},
            "multi_guest_request": {"type": Control,
                                    "path": UPath(id_ == "ttlive_text", text_ == "Request")},

            "micro_phone_icon": {"type": Control,
                                 "path": UPath(id_ == "mic_effect_view", visible_ == True)},
            "camera_icon": {"type": Control,
                            "path": UPath(tag_ == "MULTI_GUEST_VIDEO", id_ == "ttlive_toolbar_default",
                                          visible_ == True)},
            "camera_open_confirm": {"type": Control,
                                    "path": UPath(id_ == "btn_save", visible_ == True)},

            "micro_phone_mute_btn": {"type": Control,
                                     "path": UPath(id_ == "online_player_mute", visible_ == True)},

            "multi_guest_mic_view": {"type": MultiGuestSingleMicView,
                                     "path": UPath(id_ == "ttlive_link_layout_sdk_container")},
            # 嘉宾数据概览弹窗
            "spark_view_popup": {"type": MultiGuestSingleMicView,
                                 "path": UPath(type_ == "SparkView", id_ == "viewContainer")},
            "live_dialog_popup": {"type": MultiGuestSingleMicView,
                                  "path": UPath(id_ == "live_dialog_bottom_container", visible_ == True)},

            # 观众或嘉宾退出直播间
            "exit_room": {"type": Control, "path": UPath(id_ == "close_widget_container", visible_ == True)},
            "exit_room_confirm": {"path": UPath(text_ == "Confirm", visible_ == True)},
        }

    def is_enter_room(self):
        """校验观众是否进房
        :return:
        """
        try:
            return self["exit_room"].existing
        except Exception as e:
            logger.error("exit_room doesn't exit. Detail: %s" % str(e))
            return False

    def leave_room_by_ui(self, times=3, wait_time=3):
        """退出直播间
        :return:
        """
        if self["exit_room"].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Guest click exit button leave room...")
            self["exit_room"].click()
            time.sleep(wait_time)

            # 连麦期间, 直接退出直播间需要再次确认是否退出直播间
            if self["exit_room_confirm"].wait_for_visible(timeout=3, raise_error=False):
                for _ in Retry(limit=times, raise_error=False):
                    logger.info("Guest click '确认|Confirm' confirm exit LIVE room...")
                    self["exit_room_confirm"].click()
                    time.sleep(wait_time)

                    if not self["exit_room"].existing:
                        break

    def guest_confirm_disconnect(self):
        """The Guest confirm leave multi-guest channel
        :return:
        """
        # 校验是否有主播断开连麦弹窗, 若有则取消, 避免阻断发起连麦
        if self["disconnect_confirm"].wait_for_visible(timeout=3, raise_error=False):
            logger.info("Guest confirm 'Got it' of disconnect")
            self["disconnect_confirm"].click()

        elif self["live_dialog_popup"].wait_for_visible(timeout=3, raise_error=False):
            # 连麦期间, 主播异常下播, 超过30s 续播返回直播间, 连麦状态自动断开场景
            logger.info("Guest confirm 'OK' of auto disconnect by Host")
            self["live_dialog_popup"].click()

        # 侧滑返回上一层, 确保 嘉宾数据概览弹窗 被隐藏
        self.app.get_device().back()
        # 避免左滑导致退出直播
        if self["close_cancel"].wait_for_visible(timeout=5, raise_error=False):
            self["close_cancel"].click()
            time.sleep(2)

    def is_multi_guest_status(self):
        """嘉宾: 根据当前画面UI状态判断是否处于连麦状态
        :return:
        """
        # 校验是否在直播间内
        if not self["exit_room"].existing:
            logger.info("Doesn't in LIVE room...")
            return False

        is_existing = self["camera_icon"].existing
        logger.info("whether Multi-guest status is connected? %s..." % is_existing)

        return is_existing

    def apply_for_multi(self, position="-1", times=3):
        """申请嘉宾连麦
        :return:
        """
        if self.is_multi_guest_status():
            logger.info("Guest has been connected with Host...")
            return

        # 点击嘉宾连麦按钮
        self.click_by_name("multi_guest_button", sleep_time=2)

        # 发起确认连麦申请
        for _ in Retry(limit=times, raise_error=False):
            if self["multi_guest_request"].wait_for_visible(timeout=10, raise_error=False):
                # 点击发起request
                self["multi_guest_request"].click()

                # "18岁"弹窗
                if self["confirm_18"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                    self["confirm_18"].click()
                time.sleep(3)
                break
        else:
            raise Exception("The guest can't send guest request...")

    def check_audio_video_mute_status_by_name(self, nick_name, icon_type):
        """校验麦克风/摄像头状态
        :param icon_type: camera or micro
        :param nick_name:
        :return:
        """
        # 昵称为空, 则默认返回false, 暂不考虑其它
        if nick_name == "":
            return False

        # 选择嘉宾request按钮, 并点击 Accept 确认连麦
        for cell in self["multi_guest_mic_view"].items():
            # 空麦位
            if cell["no_guest_view"].existing or not cell["nick_name"].existing:
                continue

            # 校验嘉宾名称是否对应
            if nick_name == cell["nick_name"].text:
                return cell["mute_btn"].visible if icon_type == "micro" else cell["mute_video_bg"].visible
        else:
            return False

    def mute_local_audio_for_multi(self, mute, nick_name, times=10):
        """嘉宾侧: 麦克风状态切换
        若nick_name为空, 则无需关注按钮状态, 直接点击即可
        :param times:
        :param nick_name:
        :param mute: True 关闭麦克风, False 开启麦克风
        :return:
        """
        # 校验嘉宾是否正常上麦(麦克风按钮)
        self["micro_phone_icon"].wait_for_visible(timeout=20)

        # 根据控件状态和预期状态判断是否点击 “麦克风” 按钮, 改变状态
        mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="micro")
        for i in range(times):
            logger.info("The %d time check micro_phone_icon status..." % (i + 1))

            # 根据mute状态判断是否需要点击按钮
            if nick_name == "" or (mute and not mute_status) or (not mute and mute_status):
                logger.info("Click micro_phone_icon...")
                self["micro_phone_icon"].click()
                time.sleep(2)

            # 再次检测mute状态判断是否需要重试
            mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="micro")
            if nick_name == "" or (mute and mute_status) or (not mute and not mute_status):
                break

            time.sleep(2)

        logger.info("micro_phone_icon: expected == %s, mute_btn_exist == %s..." % (mute, mute_status))

    def mute_local_video_for_multi(self, mute, nick_name, times=10):
        """嘉宾侧: 摄像头状态切换
        若nick_name为空, 则无需关注按钮状态, 直接点击即可
        :param times: 重试次数
        :param nick_name:
        :param mute: True 关闭摄像头, False 开启摄像头
        :return:
        """
        # 校验嘉宾是否正常上麦(摄像头按钮)
        self["camera_icon"].wait_for_visible(timeout=20)

        # 根据控件状态和预期状态判断是否点击 “摄像头” 按钮, 改变状态
        mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="camera")
        for i in range(times):
            logger.info("The %d time check camera_icon status..." % (i + 1))

            # 根据mute状态判断是否需要点击按钮
            if nick_name == "" or (mute and not mute_status) or (not mute and mute_status):
                logger.info("Click camera_icon...")
                self["camera_icon"].click()
                time.sleep(2)

                # 摄像头打开确认弹窗
                if self["camera_open_confirm"].wait_for_visible(timeout=5, raise_error=False):
                    self["camera_open_confirm"].click()
                    time.sleep(2)

            # 再次检测mute状态判断是否需要重试
            mute_status = self.check_audio_video_mute_status_by_name(nick_name, icon_type="camera")
            if nick_name == "" or (mute and mute_status) or (not mute and not mute_status):
                break

            time.sleep(2)

        logger.info("camera_icon: expected == %s, mute_btn_exist == %s..." % (mute, mute_status))


class LiveCharactor(object):
    """Live Charactor UI object
    """

    def __init__(self, app):
        """
        :param app:
        :return:
        """
        self._host = HostLivePanel(root=app)
        self._viewer = ViewerLivePanel(root=app)

    @property
    def host(self):
        return self._host

    @property
    def viewer(self):
        return self._viewer
