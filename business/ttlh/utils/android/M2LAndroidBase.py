# -*- coding: utf8 -*-
import time

from shoots.retry import <PERSON><PERSON>
from testlib.test_role.link_mic_instance.link_mic_android import LinkMicAndroid

from business.ttlh.utils.accounts.m2l_account_mgr import *
from business.ttlh.utils.android.M2LAndroidLiveUI import LiveCharactor
from business.ttlh.utils.android.AndroidBaseUI import FeedPanel
from business.ttlh.utils.android import config as con


class M2LAndroidBase(LinkMicAndroid):
    """M2L Android基础类
    """

    def __init__(self, app):
        """
        :param app:
        """
        super().__init__(app)
        # init app ui operation object
        self.charactor = LiveCharactor(app=self.app)

    def get_uid(self):
        """获取账号Uid
        :return:
        """
        uid = self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi",
                                   method="getUid")
        return str(uid)

    def get_did(self):
        """获取设备did
        :return:
        """
        return self.app.get_device_did()

    def logout(self, times=3, sleep_time=5):
        """adb 退出登录, >=3350支持（自动重启）
        :return:
        """
        # 退出登录
        for _ in Retry(limit=times, raise_error=False):
            self.app.get_device().adb.shell_command("am broadcast -a com.bytedance.ttmock.action_cmd --es cmd \"logout\"")
            time.sleep(sleep_time)

            if not self.check_login():
                break

    @property
    def inner_room_id(self):
        return self.get_room_info()["data"]["room_id"]

    def start_live(self):
        """
        :return:
        """
        return super().start_live()

    def close_live(self, is_ui=False, sleep_time=5):
        """End LIVE
        :param sleep_time:
        :param is_ui:
        :return:
        """
        if is_ui:
            logger.info("End LIVE with UI Click")
            self.charactor.host.end_live()
        else:
            self.end_live()
        time.sleep(sleep_time)

        # close end live page, turn back
        logger.info("Close end live page, turn back")
        self.device.back()
        logger.info("DeepLink to feed")
        self.app.open_scheme("snssdk1180://feed")

    def enter_room(self, room_id, times=5, sleep_time=3, try_times=3):
        """
        :param try_times:
        :param sleep_time:
        :param times:
        :param room_id:
        :return:
        """
        # 校验是否已经进房, 若已进房, 则无需处理
        if self.charactor.viewer.is_enter_room():
            return

        for _ in Retry(limit=try_times, raise_error=False):
            logger.info("Enter room through API...")
            super().enter_room(room_id=room_id, times=times)
            time.sleep(sleep_time)

            if self.charactor.viewer.is_enter_room():
                logger.info("Enter room success...")
                break

            # 直播间跳转失败, 可能是由于弹窗阻断, 暂通过重启的方式规避
            self.restart()
            time.sleep(5)
        else:
            logger.error("Fallback: Jump to LIVE room through schema...")
            for _ in Retry(limit=try_times, raise_error=False):
                self.app.open_scheme(f"snssdk1180://live?room_id={room_id}")
                time.sleep(sleep_time)

                if self.charactor.viewer.is_enter_room():
                    logger.info("Enter room success...")
                    break
            else:
                raise Exception("Can't enter LIVE room...")

    def disconnect_co_host(self, is_ui=True):
        """Host disconnect Co-Host
        :param is_ui:
        :return:
        """
        logger.info("Host disconnect Co-Host...")
        if is_ui:
            self.charactor.host.leave_co_host()
        else:  # Todo
            pass

        # close co-host end page, turn back
        logger.info("The lynxview page exist, turn back to close this page")
        self.charactor.host.close_cohost_lynxview_popup()

    def clean_mock(self):
        """clean mock file(ttmock.json) when testcase done
        :return:
        """
        self.device.adb.rm_file(con.device_ttmock_path)

    def apply_for_multi(self, position="-1", is_ui=False):
        """
        :param is_ui:
        :param position:
        :return:
        """
        if not is_ui:
            super().apply_for_multi(position=position)
        else:
            self.charactor.viewer.apply_for_multi()

    def permit_apply_for_multi(self, status, room_id, guest_info, is_ui=True):
        """
        :param guest_info:
        :param status:
        :param room_id:
        :param is_ui:
        :return:
        """
        if guest_info is None:
            return None

        guest_uid, guest_nickname = guest_info["data"]["uid"], guest_info["data"]["nickName"]
        # UI点击, 暂无返回
        if is_ui:
            self.charactor.host.permit_apply_for_multi(guest_nickname)
            return {}

        return super().permit_apply_for_multi(status=status, room_id=room_id, guest_uid=guest_uid)

    def mute_local_audio_for_multi(self, mute, guest_info=None, is_ui=True):
        """嘉宾侧麦克风开关
        :param guest_info:
        :param mute:
        :param is_ui:
        :return:
        """
        if is_ui and guest_info is not None:
            guest_nickname = guest_info["data"].get("nickName", "")
            self.charactor.viewer.mute_local_audio_for_multi(mute, guest_nickname)
            return {}

        return super().mute_local_audio_for_multi(mute=mute)

    def mute_local_video_for_multi(self, mute, guest_info=None, is_ui=True, **kwargs):
        """
        :param guest_info:
        :param is_ui:
        :param mute:
        :param kwargs:
        :return:
        """
        if is_ui and guest_info is not None:
            guest_nickname = guest_info["data"].get("nickName", "")
            self.charactor.viewer.mute_local_video_for_multi(mute=mute, nick_name=guest_nickname)
            return {}

        return super().mute_local_video_for_multi(mute=mute, is_guest=kwargs.get("is_guest", True))

    def change_guest_layout_for_multi(self, scene, is_ui=True, layout_info=None):
        """切换连麦布局
        :param scene:
        :param is_ui:
        :param layout_info:
        :return:
        """
        if is_ui:
            return self.charactor.host.change_guest_layout_for_multi(scene)
        else:
            if layout_info is None:
                layout_info = ("1013", "宫格-非固定(最大9麦位)", "9")
            return self.change_layout_for_multi(layout_info[0], layout_info[2])

    def mock_video_for_live(self, is_enable=False, tos_url="", size=None):
        """mock直播视频流
        :param is_enable:
        :param size:
        :param tos_url:
        :return:
        """
        # 设置mock卡开关
        logger.info("Set mock status = %s" % is_enable)
        self.app.call_method(
            class_name="com.bytedance.android.livesdk.livesetting.broadcast.LiveAnchorEnableVideoMockPushStream",
            method="setValue", args=[is_enable])

        # 开启mock
        if is_enable:
            # 视频分辨率
            logger.info("Set mock video size = %s" % size)
            size = size if size is not None else [720, 1280]
            self.app.call_method(
                class_name="com.bytedance.android.livesdk.livesetting.broadcast.LiveAnchorMockVideoSize",
                method="setValue", args=size)

            # mock视频URL
            tos_url = con.MOCK_VIDEO_TOS_URL if tos_url == "" else tos_url
            logger.info("Set mock video url: %s" % tos_url)
            self.app.call_method(
                class_name="com.bytedance.android.livesdk.livesetting.broadcast.LiveAnchorVideoMockPath",
                method="setValue", args=[tos_url])

    def set_effect_with_effect_id(self, effect_resource_id, tab_key=""):
        """主播: 直播间内根据特效effect_resource_id 和 tab_key 使用特效效果
        :param effect_resource_id:
        :param tab_key:
        :return:
        """
        # 经调试, 暂不生效, 待后续调研处理
        self.app.call_method(class_name="com.bytedance.android.live.effect.LiveEffectTestHelper",
                             method="setEffect", args=[effect_resource_id, tab_key])

    def apply_ab_clone(self, abid="", wait_time=10):
        """
        :param wait_time:
        :param abid:
        :return:
        """
        if abid == "":
            return

        panel = FeedPanel(root=self.app)
        panel.set_ab_clone(abid=abid)

        logger.info("Restart APP for ABID take effect...")
        self.restart()

        time.sleep(wait_time)