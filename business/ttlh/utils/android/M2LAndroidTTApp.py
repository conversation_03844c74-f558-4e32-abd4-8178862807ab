# -*- coding: utf8 -*-
import time, random

import json, os, time
import wget

from utils.common.log_utils import logger
from shoots_android_byted import AndroidAppMixin
from shoots_android.androidapp import AndroidApp
# from api_test.app import APITestAppMixin, RPCServerNotRunning
from business.ttlh.utils.popup import *

from business.ttlh.utils.android import config as con
from business.ttlh.utils.android.popup import *


class M2LAndroidTTApp(APITestAppMixin, AndroidApp, AndroidAppMixin):
    """M2L 自定义 Android APP
    Refer: testlib.app.android_lib.android_base_app.py#AndroidTTLiveApp
    """

    app_spec = {
        # "package_name": "com.ss.android.ugc.trill",  # app package name
        "package_name": "com.zhiliaoapp.musically",
        "init_device": True,  # whether to wake up device
        "process_name": "",  # main process name of app
        "start_activity": "",  # leave it empty to be detected automatically
        "grant_all_permissions": True,  # grant all permissions before starting app
        "clear_data": False,  # pm clear app data
        "kill_process": True  # whether to kill previously started app
    }

    popup_rules = [CommonPopup, SyncContactsPopup, NotificationLaterPopup, AccountStatusPopup, CloseFeedLanguagesPopupNew,
                   CloseFriendsPostPopup, CloseFeedRestrictedPopup, CloseFollowFriendsPopup, AnchorLivePopup]

    def __init__(self, *args, **kwargs):
        """
        :param args:
        :param kwargs:
        """
        super(M2LAndroidTTApp, self).__init__(*args, **kwargs)
        # Init APP env
        self.init_env()

        # 授权权限
        self.grant_all_runtime_permissions()
        self.enable_system_alert_window()
        self.enable_media_permission()

    def init_env(self):
        """
        :return:
        """
        self._wake_up()
        self.skip_sso()
        self.set_mock()
        self.restart()

    def skip_sso(self):
        """ New way to skip SSO Authentication in Intranet
        """
        result = self.get_device().adb.shell_command(
            "settings put global feedbacker_sso_bypass_token default_sso_bypass_token"
        )
        logger.warning("Skip SSO Authentication Result: %s" % result)

    def _wake_up(self, times=20):
        """wake_up APP
        :param times:
        :return:
        """
        try:
            self.rpc.request("get_version", {})
        except RPCServerNotRunning:
            if times > 0:
                time.sleep(1)
                times -= 1
                self._wake_up(times)

    def request(self, method, params=None, timeout=None):
        """自定义的rpc request方法
        :param method:
        :param params:
        :param timeout:
        :return:
        """
        if params is None:
            params = {}

        logger.debug("[Android] [method: %s] request params: %s" % (method, json.dumps(params)))

        resp = self.rpc.request(method, params, timeout)
        logger.info("[Android] [method: %s] get resp: %s" % (method, json.dumps(resp)))

        return resp

    def set_mock(self):
        """
        :return:
        """
        # 本地文件路径
        ttmock_file = os.path.join(con.CUR_PATH, "ttmock.json")

        # ttmock资源文件地址
        ttmock_url = os.environ.get("ttmock_url", "")
        if ttmock_url != "":
            # 远程资源配置ttmock文件下载
            wget.download(ttmock_url, out=ttmock_file)
        else:
            # 默认配置
            with open(ttmock_file, "w+") as json_file:
                json_file.write(json.dumps(con.default_mock_config))

        # ttmock.json文件: 推送设备指定路径
        self.get_device().adb.push_file(ttmock_file, con.device_ttmock_path)
