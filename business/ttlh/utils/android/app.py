# -*- coding: utf8 -*-
import json
import os
import re
import time
import pkgutil
import importlib

from api_test.app import APITestAppMixin
from api_test.rpc_client import TCPJsonRPCError
from shoots.config import settings
from shoots_android.androidapp import AndroidApp
from shoots.retry import Retry
from shoots_android.androidapp import AndroidApp, AccessibilityApp
from shoots_android_byted import AndroidAppMixin
from shoots_libra.app import LibraAppMixin
from utils.common.log_utils import logger
from business.ttlh.utils.main import *
from business.ttlh.utils.popup import *
from shoots_cv.app import CVAppMixin
from shoots_lynx.app import LynxAndroidAppMixin

def load_popups_from_module_or_pkg(module_or_pkg, popups):
    if hasattr(module_or_pkg, "__path__"):  # package
        pkg = module_or_pkg
        for _, module_name, is_pkg in pkgutil.walk_packages(pkg.__path__, pkg.__name__ + "."):
            if is_pkg:
                continue
            sub_module = importlib.import_module(module_name, pkg)
            load_popups_from_module_or_pkg(sub_module, popups)
    else:
        module = module_or_pkg
        for key in dir(module):
            value = getattr(module, key)
            if isinstance(value, type):
                if issubclass(value, PopupBase) and not value == PopupBase:
                    popups.append(value)


def get_package_name():
    package_name = None
    print(settings.PROJECT_NAME)
    logger.warning("into get_package_name function")
    if hasattr(settings, "RUNNER_TASK_INFO"):
        app_info = settings.RUNNER_TASK_INFO.get("app_info", None)
        logger.warning("APP Info: %s" % app_info)
        if app_info:
            package_name = app_info.get("pkg_name", "com.ss.android.ugc.trill")
            logger.warning("package_name %s" % package_name)
    return package_name if package_name else "com.zhiliaoapp.musically"


# 通过app_info中带的channel区分是线下包还是线上包
def get_package_channel():
    package_name = None
    print(settings.PROJECT_NAME)
    if hasattr(settings, "RUNNER_TASK_INFO"):
        app_info = settings.RUNNER_TASK_INFO.get("app_info", None)
        if app_info:
            package_name = app_info.get("channel", "localtest")
    return package_name if package_name else "localtest"


def get_task_id():
    if hasattr(settings, "RUNNER_TASK_INFO"):
        task_id = settings.RUNNER_TASK_INFO.get("task_id", None)
    return task_id


class MusicallyApp(LynxAndroidAppMixin, APITestAppMixin, AndroidAppMixin, AndroidApp, LibraAppMixin, CVAppMixin):
    """
    Musically app base.
    """

    app_spec = {
        "package_name": "",  # app package name
        "package_names": ["com.ss.android.ugc.trill", "com.zhiliaoapp.musically"],
        "init_device": True,  # whether to wake up device
        "process_name": "",  # main process name of app
        "start_activity": "",  # leave it empty to be detected automatically
        "grant_all_permissions": True,  # grant all permissions before starting app
        "clear_data": True,  # pm clear app data
        "kill_process": True,  # whether to kill previously started app
        "libra_global_reverse": True,  # avoid to into any AB test groups
        "channel": get_package_channel()   # package channel: localtest/googleplay/google_play/beta
    }
    popup_rules = []
    import business.ttlh.utils.popup as popup_pkg
    load_popups_from_module_or_pkg(popup_pkg, popup_rules)

    def __init__(self, *args, **kwargs):
        super(MusicallyApp, self).__init__(*args, **kwargs)
        self._agree_privacy = False
        try:
            # 获取did并加入风控白名单
            time.sleep(2)
            self.testcase.log_record("TikTok Did: %s" % self.get_device_did())
        except:
            import traceback
            self.testcase.log_record("TikTok get DID Failed! caused by %s" % traceback.format_exc())
        if self.app_spec["kill_process"]:
            #   skip_new_user_journey=True   # whether to skip the new user journey
            if "skip_new_user_journey" in kwargs.keys():
                skip_new_user_journey = kwargs.pop("skip_new_user_journey")
            else:
                skip_new_user_journey = True
            if skip_new_user_journey:
                self.escape_sso()
                # below logic only work for local_test package
                if "test" in self.app_spec["channel"]:
                    if not kwargs.get("agree_privacy"):
                        self._agree_privacy = True
                    self.close_other_page()
            else:
                self.skip_sso()

    def skip_sso(self):
        """ New way to skip SSO Authentication in Intranet
        """
        result = self.get_device().adb.shell_command(
            "settings put global feedbacker_sso_bypass_token default_sso_bypass_token"
        )
        logger.warning("Skip SSO Authentication Result: %s" % result)

    def escape_sso(self):
        """
        如果存在飞书绕过文件，则在app启动前push到指定目录
        :return:
        """
        try:
            cur_dir = os.path.split(os.path.realpath(__file__))[0]
            validation_file = os.path.join(cur_dir, "validation.prop")
            cache_dest_path = "/sdcard/Android/data/{}/cache/validation.prop".format(self.package_name)
            if os.path.exists(validation_file):
                self.get_device().push_file(validation_file, cache_dest_path)
                self.testcase.log_record("force login skip file push result: %s" % str(self.get_device().adb.shell_command("ls %s" % "/sdcard/Android/data/{}/cache".format(self.package_name))))
        except Exception:
            pass
        self.restart()

    def try_get_did(self, timeout=15):
        for _ in Retry(timeout=timeout, interval=1, raise_error=False):
            try:
                did_num = self.get_device_did()
                self.testcase.log_info("TikTok Did: %s" % did_num)
                return did_num
            except:
                import traceback
                self.testcase.log_info("TikTok get DID Failed!Try again! Console error is %s" % traceback.format_exc())
        return ""

    def request(self, method, params=None, timeout=None):
        if params is None:
            params = {}
        resp = self.rpc.request(method, params, timeout)
        logger.info(f"api call {method} response:{resp}")
        if resp['data'] is not None and 'nameValuePairs' in resp['data']:
            resp['data'] = resp['data']['nameValuePairs']
        if str(resp["code"]) != "0":
            raise TCPJsonRPCError(
                resp["code"], resp["message"]
            )
        return resp
    def get_app_permission_status(self):
        try:
            return json.loads(self.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="getPermissionStatus"))
        except:
            import traceback
            self.testcase.log_record("get app permission failed: %s" % traceback.format_exc())
            return ""

    def close_other_page(self, times=5):
        try:
            time.sleep(3)
            closePnsUniversalPopup = ClosePnsUniversalPopup(root=self)
            closePnsUniversalPopup.handle()
            self.wait_for_activity(ForYouPanel.window_spec['activity'], timeout=3, interval=0.3)
            if not self._agree_privacy:
                self._agree_privacy = ForYouPanel(root=self).agree()
        except RuntimeError as e:
            if not hasattr(e, "current_activity"):
                raise e
            logger.warning(str(e))
            cur_activity = e.current_activity
            if cur_activity is None or cur_activity == ForYouPanel.window_spec['activity']:
                logger.warning("当前activity:{},不做处理".format(cur_activity))
            elif cur_activity == JourneyPanel.window_spec['activity']:
                journey = JourneyPanel(root=self)
                journey.skip()
                journey.start_watching()
            elif re.compile(I18nSignUpPanel.window_spec['activity']).match(cur_activity):
                I18nSignUpPanel(root=self).click_question_info(duration=3)
            else:
                logger.warning(
                    "当前activity:{}非首页activity，当前方法未对此弹窗做处理，抛出异常".format(cur_activity))
                raise e

            if times == 0:
                raise RuntimeError(
                    "当前activity:{}非首页activity".format(cur_activity))
            self.close_other_page(times - 1)

    # 打开特定页面
    def open_page(self, schema_url, activity=None, timeout=10):
        """
        :param schema_url: 页面的schema url，公司内以snssdk开头
        :param activity: schema对应的activity，跳转后校验是否在目标activty
        :param timeout:
        :return:
        """
        cmd = 'am start -a android.intent.action.VIEW -d "{}" -W {}'.format(
            schema_url, self.package_name)
        result = self.shell_command(cmd)
        if "timeout" in result:
            logger.warn("跳转schemaUrl失败，重试一次")
            result = self.shell_command(cmd)
        if not "ok" in result:
            raise RuntimeError("跳转schemaUrl失败，错误信息:{}".format(result))
        if activity:
            self.wait_for_activity(activity, timeout=timeout)
        time.sleep(2)

    def back(self, back_activity='com.ss.android.ugc.aweme.splash.SplashActivity', limit=5, interval=1):
        """
        @param limit: 重试次数
        @param back_activity: 要返回的页面的activity
        @param interval: 重试间隔
        """
        pattern = re.compile(back_activity)
        for _ in Retry(limit=limit, interval=interval, raise_error=False):
            current_activity = self.current_activity
            if current_activity == back_activity:
                time.sleep(2)
                return True
            if current_activity and pattern.match(current_activity):
                time.sleep(2)
                return True
            else:
                self._device.send_key(4)
        else:
            raise RuntimeError("未能正常返回")

    def multi_back(self, times=1):
        """
        fanhui
        """
        for _ in range(times):
            self.get_device().press_back()
            time.sleep(0.5)


class GoogleMapApp(AndroidApp):
    """
    GoogleMap app base.
    """

    app_spec = {
        "package_name": "com.google.android.apps.maps",  # app package name
        "init_device": True,  # whether to wake up device
        "process_name": "",  # main process name of app
        "start_activity": "",  # leave it empty to be detected automatically
        "grant_all_permissions": True,  # grant all permissions before starting app
        "clear_data": True,  # pm clear app data
        "kill_process": True,  # whether to kill previously started app
        "libra_global_reverse": True  # avoid to into any AB test groups
    }


class BrowserApp(AccessibilityApp):
    """
     safari browser
     """
    app_spec = {
        "package_name": "com.android.quicksearchbox",  # app package name
        "kill_process": True,  # whether to kill previously started app
        "start_activity": "",
    }


class ChromeApp(AccessibilityApp):
    """
     Chrome browser
     """
    app_spec = {
        "package_name": "com.android.chrome",  # app package name
        "kill_process": False,  # whether to kill previously started app
        "start_activity": "",
    }

    def start(self):
        pass


class ShareSDKApp(AndroidApp, AndroidAppMixin):
    """
    The Open Source ShareKitDemo App for TT4D
    """
    app_spec = {
        "package_name": "com.bytedance.sdk.demo.share",
        "kill_process": True,
        "timeout": 10
    }

class LauncherAndroidApp(AndroidApp):
    app_spec = {'package_name': 'com.sec.android.app.launcher', 'package_names': ['com.sec.android.app.launcher'], 'init_device': True, 'process_name': '', 'start_activity': '', 'grant_all_permissions': True, 'clear_data': False, 'kill_process': True}



