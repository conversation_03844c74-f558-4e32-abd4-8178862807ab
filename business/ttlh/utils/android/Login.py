# -*- coding: utf8 -*-
import time

from shoots_android.control import *
from uibase.upath import id_,text_,type_,UPath,visible_
import logging

logging.basicConfig(level=logging.INFO)
class LoginPanel(Window):
    """
    登录页面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.ui.MusCountryListActivity|com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivity|com.ss.android.ugc.aweme.account.login.v2.ui.SignUpOrLoginActivity|com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivityWithNoAnimation"

        }

    def get_locators(self):
        return {
            "main": {"path": UPath(id_ == "main_bottom_button_add") / UPath(type_ == "ImageView", depth=5)},
            "login":{'path':UPath(text_ == "使用手机号码/电子邮箱/用户名登录")},
            "下拉":{'path':UPath(id_ == "phone_input_view_country_layout", visible_ == True) / UPath(type_ == "TuxIconView", depth=5)},
            "search":{'path':UPath(id_ == "search_edit_text")},
            "日本":{'path':UPath(text_ == "日本", type_ == "TuxTextView")},
            "phone":{'path': UPath(id_ == "inputWithIndicatorEditText", visible_ == True)},
            "phone_input_view":{'path':UPath(type_ == "TuxEditText", visible_ == True)},
            "jixu":{'path':UPath(text_ == "继续", visible_ == True)},
            "send_code":{'path':UPath(id_ == "loading_button_text", visible_ == True)},
            "code":{'path':UPath(id_ == "inputCodeView")},
            "下一步":{'path':UPath(id_ == "cta_primary", visible_ == True)},
            "skip":{'path':UPath(id_ == "btn_skip_bottom")},
            "稍后再说":{'path':UPath(id_ == "action_area") / 0 / 1 / 1}
        }

    def if_need_login(self):
        if self["main"].wait_for_visible(timeout=5, raise_error=False):
            return True
        else:
            return False

    def login(self,account,code):
        if self.if_need_login() == False:
            if self["login"].wait_for_visible(timeout=5):
                self["login"].click()
                print("使用手机/邮箱/用户名登录")
            if self["下拉"].wait_for_visible(timeout=5, raise_error=False):
                self["下拉"].click()
            if self["search"].wait_for_visible(timeout=5, raise_error=False):
                self["search"].input("日本")
            if self["日本"].wait_for_visible(timeout=5, raise_error=False):
                self["日本"].click()
            if self["phone_input_view"].wait_for_visible(timeout=5, raise_error=True):
                self["phone_input_view"].input(account)
            if self["send_code"].wait_for_visible(timeout=5,raise_error=False):
                self["send_code"].click()
            if self["jixu"].wait_for_visible(timeout=5,raise_error=False):
                self["jixu"].click()
            if self["下一步"].wait_for_visible(timeout=5, raise_error=False):
                self["下一步"].click()
            if self["code"].wait_for_visible(timeout=5):
                self["code"].input(code)

            if self["下一步"].wait_for_visible(timeout=5,raise_error=False):
                self["下一步"].click()
            if self["skip"].wait_for_visible(timeout=5,raise_error=False):
                self["skip"].click()
            if self["稍后再说"].wait_for_visible(timeout=5,raise_error=False):
                self["稍后再说"].click()
            time.sleep(3)
        else:
            print("已登录")