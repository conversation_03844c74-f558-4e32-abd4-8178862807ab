from business.ttlh.utils.main.base import BasePanel, UPath, id_, text_, Control, type_, ScrollView
import time
import re
from utils.common.log_utils import logger
from shoots.retry import Retry

class LoginSDKAuthPanel(BasePanel):
    """ Login SDK Auth Panel
    """
    window_spec = {"activity": "com.zhiliaoapp.musically.openauthorize.AwemeAuthorizedActivity|com.ss.android.ugc.trill.openauthorize.AwemeAuthorizedActivity"}

    def get_locators(self):
        return {
            "auth_title": {"path": UPath(id_ == "txt_login_title")},
            "tiktok_icon": {
                "path": UPath(id_ == 'tiktok_app_icon')},
            "client_icon": {
                "path": UPath(id_ == 'icon_apply_auth_app')},
            "scope_list": {"type": Control,
                           "path": UPath(id_ == "scope_list_content")},
            "scope_footer_text": {"path": UPath(id_ == 'txt_remove_hint')},
            "edit_access": {"path": UPath(id_ == "txtEditAccess")},
            "Terms_and_cond_text": {"path": UPath(id_ == 'txt_terms_content')},
            "authorize_button": {"path": UPath(id_ == 'btn_login')},
            "cancel_button": {"path": UPath(id_ == 'btn_cancel')},
            "manage_access": {"path": UPath(id_ == 'titleTv')},
            "edit_scope_list": {"type": Control, "path": UPath(id_ == 'listScopes')},
            "required_scope": {"path": UPath(id_ == 'scopeName')},
            "region_block_popup_text": {"path": UPath(id_ == 'content_tv')},
            "region_block_popup_ok": {"path": UPath(id_ == 'action_area') / 0 / 1 / UPath(text_ == 'OK')},
            "edit_access_block": {"type": Control, "path": UPath(id_ == "auth_content") / UPath(type_ == "LinearLayout", index=0) / 
                                    UPath(type_ == "LinearLayout", index=1)},
            "kids_auth_block_popup_text": {"path": UPath(id_ == 'content_tv')},
            "kids_auth_block_popup_confirm": {"path": UPath(text_ == 'Confirm')},
            'scope_list_content': {'path': UPath(type_ == "ScrollView") / 0 / 1 / 1}
        }

    # ORDER DOES NOT MATTER
    permission_list = {
        "user.info.email": "Read your email address",
        "user.info.basic": "Read your profile info",
        "user.info.phone": "Read your phone number",
        "user.info.username": "Read your username.",
        "user.ue": "Read your interests",
        "music.collection": "Read songs added to your favorites on TikTok.",
        "share.sound.create": "Share your original sounds to TikTok",
        "video.list": "Read your public videos on TikTok",
        "video.list.manage": "Publish and manage your TikTok videos" 
    }

    def start_auth_page(self, scopes, package):
        activity_name = package + "/" + package + ".openauthorize.AwemeAuthorizedActivity"
        scope_list = ",".join(scopes)
        # scope_list = "user.info.basic,user.info.username,user.info.phone,user.info.email,user.info.account,user.account.upgrade_to_business,user.ue,user.insights,comment.list,comment.list.manage,video.list,video.list.manage,video.upload,video.publish,video.insights,feed.foryou.list,share.sound.create,message.list,message.list.manage,shop.product.manage,client.tcm,live.list,music.collection.sync,music.clip.list,music.profile.manage"
        extra = {
            "_bytedance_params_scope": scope_list,
            "_bytedance_params_client_key": "awbx37vxswqcvsf6",
        }
        # Client Key for TT4D Demo Web: awbx37vxswqcvsf6
        self.app.start_activity_internally(activity_name, None, None, None, None, extra)
        time.sleep(2)

    def title_exists(self):
        for _ in Retry(timeout=20, interval=1, raise_error=False):
            if self["auth_title"].wait_for_visible(timeout=10, interval=0.5, raise_error=False):
                logger.debug(self['auth_title'].text)
                return True
            
    def get_title_text(self):
        return self["auth_title"].text

    def tiktokicon_exists(self):
        for _ in Retry(timeout=20, interval=1, raise_error=False):
            if self['tiktok_icon'].wait_for_visible(timeout=10, interval=0.5, raise_error=False):
                return True

    def clienticon_exists(self):
        for _ in Retry(timeout=20, interval=1, raise_error=False):
            if self['client_icon'].wait_for_visible(timeout=10, interval=0.5, raise_error=False):
                return True

    def check_list(self, scopes):
        count = len(scopes)
        for item in self['scope_list'].children:
            scope_subtitle = Control(root=item, path=UPath(type_ == "com.bytedance.tux.input.TuxTextView"))
            for scope in scopes:
                if re.match(re.escape(self.permission_list[scope]), scope_subtitle.text):
                    count = count - 1
                    break
        return count == 0

    def check_edit_scopes(self, scopes):
        count = len(scopes)
        if "user.info.basic" in scopes:
            pass
        else:
            count = len(scopes) + 1
        if self["required_scope"].wait_for_existing(timeout=2, interval=0.5, raise_error=True):
            count = count - 1
        for item in self["edit_scope_list"].children:
            list = Control(root=item, path=UPath(id_ == 'scope'))
            if list.wait_for_existing(timeout=2, interval=0.5, raise_error=False):
                for s in scopes:
                    if s == "user.info.basic":
                        continue
                    scope = Control(root=list, path=UPath(id_ == 'title_tv'))
                    if scope.text == self.permission_list[s]:
                        logger.info(s + ": " + scope.text)
                        count = count - 1
                        break
        return count == 0

    def edit_scope(self, scopes):
        for item in self["edit_scope_list"].children:
            list = Control(root=item, path=UPath(id_ == 'scope'))
            if list.wait_for_existing(timeout=2, interval=0.5, raise_error=False):
                for s in scopes:
                    if s == "user.info.basic":
                        continue
                    button = Control(root=list, path=UPath(id_ == 'svw_item_right'))
                    button.long_click

    def edit_access_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["edit_access"].wait_for_visible(timeout=10, interval=0.5, raise_error=False):
                return True
        return False

    def auth_button_exists(self):
        self['authorize_button'].wait_for_ui_stable()
        for _ in Retry(timeout=20, interval=1, raise_error=False):
            if self['authorize_button'].wait_for_visible(timeout=10, interval=0.5, raise_error=False):
                return True

    def cancel_button_exists(self):
        for _ in Retry(timeout=20, interval=1, raise_error=False):
            if self['cancel_button'].wait_for_visible(timeout=10, interval=0.5, raise_error=False):
                return True

    def click_auth_button(self):
        self["authorize_button"].click()

    def click_cancel_button(self):
        self["cancel_button"].click()

    def swipe_scope_list_content(self):
        self['scope_list_content'].wait_for_ui_stable()
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['scope_list_content'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['scope_list_content'].drag(to_x=0,to_y=10)
        return False
    
    def click_edit_access(self):
        if self['edit_access'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            return self['edit_access'].click()
        return False

    def get_region_block_popup_text(self):
        self["region_block_popup_text"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        return self["region_block_popup_text"].text

    def click_ok_region_block_popup(self):
        self["region_block_popup_ok"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["region_block_popup_ok"].click()

    def get_kids_auth_block_popup_text(self):
        self["kids_auth_block_popup_text"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        return self["kids_auth_block_popup_text"].text

    def click_confirm_kids_auth_block_popup(self):
        self["kids_auth_block_popup_confirm"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["kids_auth_block_popup_confirm"].click()
