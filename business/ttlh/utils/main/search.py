# -*- coding:utf-8 _*-
import random
import time
from shoots.retry import Retry
from shoots_android.control import Window, ImageView, FrameLayout, TextView, ScrollView
from uibase.upath import id_, UPath, type_, visible_, text_, desc_, tag_
from business.ttlh.utils.main.base import Control
from . import ProfilePanel
from .base import FeedsList


class MTextEdit(TextView):
    def input(self, text):
        self.send_keys(text)


class VideoList(FeedsList):
    elem_path = UPath(id_ == "root_feed_cell")


class Video_card(Control):

    def get_locators(self):
        return {
        }


class Video_card_list(Control):
    elem_class = Video_card
    elem_path = UPath(id_ == "root_feed_cell", depth=1)


class Hot_Video_card_list(Control):
    elem_class = Video_card
    elem_path = UPath(type_ == "com.lynx.FakeViews.LynxView", visible_ == True, depth=1)


# 综搜直播卡
class Zs_live_card(Control):
    def get_locators(self):
        return {}


class Zs_live_card_list(Control):
    elem_class = Zs_live_card
    elem_path = UPath(id_ == "root_aladdin_item_video")


# top sounds
class Music_card(Control):
    def get_locators(self):
        return {
            "content": {"path": UPath(type_ == "android.widget.LinearLayout", index=0)},
            "play_btn": {"path": UPath(id_ == "iv_play_status")},
            "pause_btn": {"path": UPath(id_ == "iv_play_status")}
        }


class Music_card_list(Control):
    elem_class = Music_card
    elem_path = UPath(type_ == "android.widget.LinearLayout", depth=1)


class Result(Control):

    def get_locators(self):
        return {
        }


class Result_List(Control):
    elem_class = Result
    elem_path = UPath(type_ == "com.lynx.FakeViews.LynxView", visible_ == True)


class TrendingCard(Control):
    def get_locators(self):
        return {
            "ht_name": {"path": UPath(type_ == 'com.lynx.tasm.behavior.ui.text.AndroidText')},
            "record_btn": {"path": UPath(type_ == "com.lynx.tasm.behavior.ui.view.a", index=1)},
            "content": {"path": UPath(type_ == "com.lynx.tasm.behavior.ui.view.a", index=0)}
        }


class TrendingCard_List(Control):
    elem_class = TrendingCard
    elem_path = UPath(type_ == "com.bytedance.ies.xelement.BounceLayout")


class TrendingAreaCard(Control):
    def get_locators(self):
        return {
            "record_btn": {"path": UPath(type_ == "com.lynx.tasm.behavior.ui.view.a", index=1)},
            "play_btn_lynx": {"path": UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView")},
            "record_btn": {"path": UPath(type_ == "com.lynx.tasm.behavior.ui.view.a", index=1)},
            "play_btn": {"type": Control,
                         "path": UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView", visible_ == True, index=0)}
        }


class TrendingAreaCard_List(Control):
    elem_class = TrendingAreaCard
    elem_path = UPath(type_ == "com.lynx.FakeViews.LynxView", depth=1)


class History(Control):
    def get_locators(self):
        return {
            "video_desc": {"path": UPath(id_ == 'tv_content')}
        }


class History_List(Control):
    elem_class = History
    elem_path = UPath(type_ == "android.widget.LinearLayout", depth=1)


class Other_search_for(Control):
    def get_locators(self):
        return {
            "title": {"path": UPath(type_ == "com.bytedance.tux.input.TuxTextView")}
        }


class Other_search_for_list(Control):
    elem_class = Other_search_for
    elem_path = UPath(type_ == "android.widget.FrameLayout")


class Also_search(Control):
    def get_locators(self):
        return {
        }


class Also_search_list(Control):
    elem_class = Other_search_for
    elem_path = UPath(type_ == "com.bytedance.tux.input.TuxTextView")


class Search_Reaults(Control):
    def get_locators(self):
        return {
            "user_name": {"path": UPath(id_ == 'tv_username')},
            "content": {"path": UPath(type_ == "android.widget.LinearLayout", index=1)},
            "content1": {"path": UPath(id_ == "tv_music_author")},
            "play_btn": {"type": Control,
                         "path": UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'iv_play_status')}
        }


class Search_Reaults_list(Control):
    elem_class = Search_Reaults
    elem_path = UPath(~type_ == "android.widget.LinearLayout|android.widget.FrameLayout", depth=1)


class general_search_video_list(Control):
    def get_locators(self):
        return {
            "video_date": {"path": UPath(id_ == 'video_holder_time_label')},
            "creator_name": {"path": UPath(id_ == 'author_name')},
            "like_count": {"path": UPath(id_ == 'like_and_play_count')},
            "creator_avatar": {"path": UPath(id_ == 'author_avatar')},
            "voice_icon": {"path": UPath(id_ == "sound_icon")}
        }

    def get_video_date(self):
        return self["video_date"].text

    def get_creator_name(self):
        return self["creator_name"].text

    def get_like_count(self):
        return self["like_count"].text

    # 按控件位置截图
    def take_capture(self, area):
        time.sleep(5)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    def shut_down_sounds_btn(self):
        return self["voice_icon"].click()


class General_search_video_list(Control):
    elem_class = general_search_video_list
    elem_path = UPath(id_ == "search_video_container_layout")


# 综搜用户卡片
class Zs_user_card(Control):
    def get_locators(self):
        return {}


class Zs_user_card_list(Control):
    elem_class = Zs_user_card
    elem_path = UPath(type_ == 'android.widget.LinearLayout', depth=1)


# 垂搜音乐卡
class Cs_music_card(Control):
    def get_locators(self):
        return {
            "play_btn": {"type": Control,
                         "path": UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'iv_play_status')},
            "pause_btn": {"type": Control,
                          "path": UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'iv_play_status')},
            "content": {"path": UPath(id_ == 'tv_music_author')}
        }

    def click_play_button(self):
        time.sleep(2)
        return self.play_btn.click()


class Cs_music_list1(Control):
    elem_class = Control
    elem_path = UPath(id_ == 'iv_play_status', visible_ == True, depth=2)


class Cs_music_list2(Control):
    elem_class = Control
    elem_path = UPath(type_ == 'android.widget.LinearLayout', visible_ == True, depth=1)


class Cs_music_list3(Control):
    elem_class = Control
    elem_path = UPath(type_ =="com.lynx.FakeViews.LynxView", visible_ == True, depth=3)


class Cs_music_card_list(Control):
    elem_class = Cs_music_card
    elem_path = UPath(type_ == 'android.widget.LinearLayout', visible_ == True)


class Cs_music_card_list1(Control):
    elem_class = Control
    elem_path = UPath(id_ == 'iv_play_status', visible_ == True, depth=1)


class Cs_music_card_list2(Control):
    elem_class = Control
    elem_path = UPath(id_ == 'iv_play_status', visible_ == True, depth=2)


class Cs_music_card_list3(Control):
    elem_class = Control
    elem_path = UPath(id_ == 'iv_play_status', visible_ == True, depth=1)


class Cs_music_card_list4(Control):
    elem_class = Control
    elem_path = UPath(id_ == 'iv_play_status', visible_ == True)


# 特色帐号卡
class Charc_card(Control):
    def get_locators(self):
        return {
            "follow_btn": {"path": UPath(type_ == 'com.lynx.tasm.behavior.ui.view.a', index=2)},
            "icon": {"path": UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView")},
            "content": {"path": UPath(type_ == "com.lynx.tasm.behavior.ui.scroll.a")}
        }


class Charc_card_list(Control):
    elem_class = Charc_card
    elem_path = UPath(tag_ == 'lynxview')


# 高热话题卡
class Hot_hashtag_card(Control):
    def get_locators(self):
        return {
            "image": {"path": UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView")}
        }


class Hot_hashtag_card_list(Control):
    elem_class = Hot_hashtag_card
    elem_path = UPath(type_ == 'com.lynx.tasm.behavior.ui.view.a', depth=1)


class trending_sounds_card(Control):
    elem_class = Hot_hashtag_card
    elem_path = UPath(type_ == 'com.lynx.FakeViews.LynxView', depth=3)


class trending_sounds_area(Control):
    elem_class = Hot_hashtag_card
    elem_path = UPath(type_ == 'com.lynx.FakeViews.LynxView', depth=2)


class SugWord(Control):
    def get_locators(self):
        return {
            'nickname': {"path": UPath(id_ == 'tv_username')},
            "icon": {"path": UPath(id_ == 'iv_avatar')},
            "sug_words": {"path": UPath(~id_ == 'tv_username|tv_sug')},
            "arrow_key": {"path": UPath(id_ == 'iv_sug_completion_new')}
        }


class SugWord_List(Control):
    elem_class = SugWord
    elem_path = UPath(~type_ == "androidx.constraintlayout.widget.ConstraintLayout|android.widget.LinearLayout", depth=1)


class Suggested_searches(Control):
    def get_locators(self):
        return {
            "Suggested_icon": {"path": UPath(type_ == "com.lynx.FakeViews.LynxView")},
            "Suggested_searches_word": {"path": UPath(type_ == "com.lynx.FakeViews.FlattenView")}
        }

    @property
    def suggested_searches_word(self):
        return self["Suggested_searches_word"].text


class Suggested_searches_list(Control):
    elem_class = Suggested_searches
    elem_path = UPath(type_ == "com.lynx.FakeViews.LynxView", visible_ == True, depth=1)


class Search_account(Control):

    def get_locators(self):
        return {
            "title": {"path": UPath(id_ == "search_acc_sug_title")},
            "icon": {"path": UPath(id_ == "search_acc_sug_avatar")}
        }


class Search_account_list(Control):
    elem_class = Search_account
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")


class User_area_list_lynx(Control):
    def get_locators(self):
        return {
        }


class User_area_list_lynx(Control):
    elem_class = User_area_list_lynx
    elem_path = UPath(type_ == "com.lynx.tasm.ui.image.UIImage")


class Cs_music_card_lynx(Control):
    def get_locators(self):
        return {
            "play_btn": {"path": UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView")}
        }


class Cs_music_card_list_lynx(Control):
    elem_class = Cs_music_card_lynx
    elem_path = UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView")


class Trending_sounds_general(Control):
    def get_locators(self):
        return {
            "play_btn": {"path": UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView")},
            "content": {"path": UPath(type_ == "com.lynx.tasm.behavior.ui.view.a", index=1)}
        }


class Trending_sounds_general_list_lynx(Control):
    elem_class = Trending_sounds_general
    elem_path = UPath(type_ == "com.lynx.tasm.behavior.ui.list.e$a")


class Related_search_words(Control):
    def get_locators(self):
        return {
            "words": {"path": UPath(type_ == "LynxView",depth=4)}
        }


class RelatedSearchWords(Control):
    elem_class = Related_search_words
    elem_path = UPath(type_ == "com.lynx.FakeViews.LynxView")


class SearchResultPanel(Window):
    '''
    Search Results Panel
    '''

    window_spec = {"activity": "com.ss.android.ugc.aweme.search.pages.core.ui.activity.SearchResultActivity"}

    def get_locators(self):
        return {
            'video_list': {'type': VideoList, 'path': UPath(id_ == 'list_view')},
            'refresh_layout': {'type': Control, 'path': UPath(id_ == 'refresh_layout') / 0},
            'empty_result': {'type': Control,
                             'path': UPath(id_ == 'tv_title', text_ == "No results found", visible_ == True)},
            'user_photo': {'type': Control, 'path': UPath(id_ == 'layout_desc')},
            "users_tab": {'type': Control, 'path': UPath(id_ == 'mix_tab_layout')/UPath(text_ == "Users", visible_ == True)},
            "first_user_id": {'type': TextView,
                              'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'tv_aweme_id')},
            "enter_profile": {'type': Control,
                              'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'layout_desc')},
            "videos_tab": {'type': Control, 'path': UPath(id_ == 'mix_tab_layout')/UPath(text_ == 'Videos', visible_ == True)},
            "top_tab": {"path": UPath(text_ == 'Top', visible_ == True)},
            "sounds": {"type": Control, 'path': UPath(text_ == 'Sounds')},
            "sounds_title": {"type": TextView, 'path': UPath(id_ == 'title')},
            "sounds_tab": {'path': UPath(text_ == 'Sounds', visible_ == True)},
            "shop_tab": {"path": UPath(text_ == 'Shop', visible_ == True)},
            "live_tab": {"path": UPath(text_ == 'LIVE', visible_ == True, index=0)},
            "places_tab": {"type": Control, "path": UPath(desc_ == "Places", visible_ == True)},
            "play_btn": {"type": Control,
                         'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'iv_play_status')},
            "pause_btn": {"type": Control,
                          'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'iv_play_status')},
            "general_music_pause_btn": {"type": Control, 'path': UPath(
                id_ == 'list_view', visible_ == True) / 1 / UPath(id_ == 'iv_play_status')},
            "general_music_play_btn": {"type": Control, 'path': UPath(
                id_ == 'list_view', visible_ == True) / 1 / UPath(id_ == 'iv_play_status', index=0)},
            "result_music_play_btn_lynx": {"type": Control, "path": UPath(type_ == "SparkView", visible_ == True)},
            "trending_result_music_title": {"type": Control, "path": UPath(desc_ == "Trending sounds", visible_ == True)},
            "trending_sounds_play_btn": {"type": Control, 'path': UPath(
                id_ == 'list_view', visible_ == True) / 1 / UPath(id_ == 'iv_play_status')},
            "trending_sounds_play_btn_lynx": {"type": Control, 'path': UPath(type_ == "SparkView", visible_ == True) / 0 / 2 / 1 / 0 / 0 / 0 / 0 / 0 / 1},
            "tab_layout": {"type": Control,
                           "path": UPath(~id_ == "mix_tab_layout|new_tab_layout", index=0)},
            "hashtags_tab": {"type": Control, "path": UPath(text_ == "Hashtags", visible_ == True)},
            "first_hashtag_name": {"type": TextView, 'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(
                id_ == 'tv_challenge_name')},
            "first_users": {"path": UPath(id_ == 'list_view', visible_ == True) / 0},
            "second_users": {"path": UPath(id_ == 'list_view', visible_ == True) / 1},
            "recom_word_list": {"path": UPath(id_ == 'words_container', visible_ == True)},
            # top
            "user_title": {"type": TextView, 'path': UPath(id_ == 'search_mix_title', text_ == 'Users')},
            "products_tab": {"type": TextView, 'path': UPath(text_ == 'Visit shop', visible_ == True)},
            "exclamation_mark": {"path": UPath(type_ == "SparkView", visible_ == True) / UPath(type_ == "LynxView", index=0)},
            "tips_logo": {"path": UPath(id_ == "recycle_view") / UPath(type_ == "SparkView") / 0 / 0 / 0 / 0 / 0},
            "warm_tips_logo": {"path": UPath(id_ == "list_view") / 0 / 0 / 0 / 0 / 0 / 0 / 0},
            "warm_tips_title": {"path": UPath(desc_ == "Distressing content", type_ == "com.lynx.FakeViews.LynxView", visible_ == True)},
            "warm_cue_title": {"path": UPath(desc_ == "Inappropriate content", type_ == "com.lynx.FakeViews.LynxView", visible_ == True)},
            "tips_text": {"path": UPath(desc_ == 'The following content may be distressing for some viewers.', visible_ == True)},
            "warm_tips_text": {"path": UPath(desc_ == 'The following content may be inappropriate for some viewers. ', visible_ == True)},
            "show_result": {"path": UPath(desc_ == "Show results", visible_ == True)},
            "browsing_history_card_copy": {"path": UPath(desc_ == 'View your watch history', visible_ == True)},
            "arrow": {"path": UPath(type_ == "SparkView", visible_ == True) / UPath(type_ == "LynxView", index=2)},
            "shop_now": {"type": TextView, 'path': UPath(desc_ == 'Shop now', visible_ == True)},
            "filter_by_title": {"type": Control, "path": UPath(text_ == "Title")},
            "user_title_lynx": {
                'path': UPath(desc_ == "Users", index=1)},
            "top_username": {"type": TextView, 'path': UPath(id_ == 'tv_username')},
            "top_userid": {"type": TextView, 'path': UPath(id_ == 'tv_aweme_id')},
            "top_video": {"type": FrameLayout, 'path': UPath(id_ == 'card_list')},
            "top_video_like": {"type": TextView, 'path': UPath(id_ == 'card_list') / 0 / UPath(id_ == 'like_count')},
            "tab_rect": {"type": FrameLayout,
                         'path': UPath(type_ == 'com.bytedance.ies.dmt.ui.widget.tablayout.DmtTabLayout$e')},
            "user_info_rect": {"type": FrameLayout, 'path': UPath(id_ == 'search_mix_content') / 0 / 0 / 0},
            "user_title_all": {"type": FrameLayout,
                               'path': UPath(id_ == 'search_mix_user_root') / UPath(id_ == 'mix_head_container')},
            "music_card": {"type": FrameLayout, 'path': UPath(id_ == 'search_mix_content') / 0 / 0 / 4},
            "top_first_video": {"type": FrameLayout, 'path': UPath(id_ == 'card_list') / 0 / UPath(id_ == 'cover')},
            "sounds_play_btn": {"type": Control,
                                'path': UPath(id_ == 'music_card_list_view') / 0 / UPath(id_ == 'iv_play_status')},
            "general_play_btn": {"type": Control,
                                 "path": UPath(type_ == 'com.lynx.tasm.ui.image.FrescoImageView', visible_ == True)},
            "sounds_page": {"type": ImageView,
                            'path': UPath(id_ == 'music_card_list_view') / 0 / UPath(id_ == 'iv_avatar')},
            "sounds_author": {"type": TextView,
                              'path': UPath(id_ == 'music_card_list_view') / 0 / UPath(id_ == 'tv_music_author')},

            # see more btn
            "see_more": {"path": UPath(id_ == 'see_more_layout')},
            "see_more_music": {"path": UPath(desc_ == 'See more')},
            "see_more_music_lynx": {"path": UPath(id_ == "bullet_container") / 0 / 0 / 0 / 0 / 0 / 1},
            "see_more_user": {"path": UPath(id_ == 'search_mix_view_all_txt', visible_ == True)},
            "see_more_sounds": {"path": UPath(id_ == 'search_mix_user_root') / UPath(id_ == 'mix_head_container')},
            "see_more_hashtags": {"path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / UPath(desc_ == 'See more')},
            "see_more_hashtag_lynx": {"path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / UPath(
                desc_ == 'See more', depth=6)},
            "see_more_effects": {"path": UPath(desc_ == 'See more')},

            # list
            "treading_card_list": {"path": UPath(id_ == "list_view") / 2 / UPath(type_ == "SparkView") / 0 / 0},
            "trending_hashtags_video_list": {"type": Search_Reaults_list,
                                      "path": UPath(type_ == 'com.bytedance.hybrid.spark.page.g') / 0 / 0 / 0 / 7 / 1},
            "zs_treading_card_list": {"type": TrendingCard_List,
                                      "path": UPath(type_ == 'com.lynx.tasm.LynxView', visible_ == True)},
            "zs_result_list": {"path": UPath(id_ == 'list_view', visible_ == True)},
            "history_list": {"path": UPath(id_ == 'rn_fragment') / 0 / 0 / 0 / 0 / 0},
            "search_reaults_list": {"type": Search_Reaults_list, "path": UPath(id_ == 'list_view', visible_ == True)},
            "search_reaults_users_list": {"type": Search_Reaults_list,
                                          "path": UPath(id_ == 'list_view', visible_ == True)},
            "other_search_for_list": {"type": Other_search_for_list,
                                      "path": UPath(id_ == 'll_related_search_content')},
            "hot_card_sub_list": {"type": Result_List, "path": UPath(id_ == 'card_list', visible_ == True)},
            "also_search_list": {"type": Also_search_list, "path": UPath(id_ == 'words_container', visible_ == True)},
            "video_card_list": {"type": Hot_Video_card_list, "path": UPath(
                id_ == 'bullet_container') / 0 / 0 / 1 / 1 / 1 / 0 / 0},
            "video_card_list_lynx": {"path": UPath(id_ == 'card_list')},
            "hot_user_card_list_lynx": {"path": UPath(id_ == 'search_common_user_rl')},
            "zs_user_card_list": {"type": Zs_user_card_list, "path": UPath(id_ == "search_mix_content") / UPath(
                type_ == "androidx.recyclerview.widget.RecyclerView", visible_ == True)},
            "charc_card_list": {"path": UPath(id_ == 'list_view') / 2 / UPath(id_ == 'bullet_container') / 0 / 0 / 1},
            "cosplay_card_list": {"path": UPath(id_ == 'list_view') / 2 / UPath(id_ == 'bullet_container') / 0 / 0 / 1},
            "click_charc_card": {"path": UPath(id_ == "bullet_container", visible_ == True) / 0 / 0 / 1 / 2},
            "charc_card_list_lynx": {"type": Charc_card_list,
                                     "path": UPath(id_ == 'list_view') / 1 / UPath(id_ == 'bullet_container') / 0 / 0 / 1},
            "hot_card_list_lynx": {"path": UPath(type_ == 'com.bytedance.hybrid.spark.page.f') / 0 / 2 / 0 / 2 / 4 / 0 / 0},
            "the_first_video": {"path": UPath(id_ == 'list_view', visible_ == True) / 0},
            "user_area_list_lynx": {"type": User_area_list_lynx, "path": UPath(id_ == 'bullet_container') / 0},
            "user_area_card_list_lynx": {"type": Hot_hashtag_card_list,
                                         "path": UPath(type_ == 'com.lynx.tasm.LynxView') / 1 / 0 / 0 / 1 / UPath(
                                             type_ == 'com.bytedance.ies.xelement.LynxScrollView$h')},
            "trending_sounds_general_list_lynx": {"type": Trending_sounds_general_list_lynx,
                                                  "path": UPath(type_ == 'com.lynx.tasm.LynxView',
                                                                visible_ == True) / UPath(
                                                      type_ == 'androidx.recyclerview.widget.RecyclerView')},
            "trending_music_card_list": {"path": UPath(id_ == "list_view", visible_ == True)},
            "zs_sounds_card_list_lnyx": {"path": UPath(
                id_ == 'bullet_container', visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0 / 0 / 1},

            "hot_hashtag_card_whole_list": {"path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 1 / 1 / 0 / 0},
            "music_card_list": {"type": Music_card_list,
                                "path": UPath(id_ == 'refresh_layout', visible_ == True) / 1},
            "music_card_list_lynx": {"type": TrendingAreaCard_List,
                                     "path": UPath(id_ == 'bullet_container') / 0 / 2 / 0},
            "music_area_card_list": {"type": Music_card_list,
                                     "path": UPath(id_ == 'bullet_container', visible_ == True) / 0 / 0 / 0 / 8},
            "music_area_card_list_lynx": {"path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 1 / 0},
            "music_card_list_general1": {"type": Cs_music_card_list,
                                         "path": UPath(id_ == 'list_view', visible_ == True)},  # 音乐卡垂搜页前两个
            "music_card_list_general2": {"type": Cs_music_card_list2,
                                         "path": UPath(id_ == 'list_view', visible_ == True) / 3},  # 音乐卡垂搜页中间三个
            "music_card_list_general3": {"type": Cs_music_card_list3,
                                         "path": UPath(id_ == 'list_view', visible_ == True)},  # 音乐卡垂搜页下面两个
            "music_card_list_general4": {"type": Cs_music_card_list4,
                                         "path": UPath(id_ == 'list_view', visible_ == True)},  # 全部
            # "cs_music_card": {"type": Cs_music_card_list, "path": UPath(id_ == 'list_view', visible_ == True)},
            "cs_music_list1": {"type": Cs_music_list1,
                               "path": UPath(id_ == 'list_view', visible_ == True) / 0},  # 音乐垂搜页前三个音乐子卡
            "cs_music_list2": {"type": Cs_music_list2, "path": UPath(id_ == 'list_view', visible_ == True)},  # 下面三个
            "cs_music_list3": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 2},  # 全部
            "cs_music_card_lynx": {"type": Cs_music_card_list_lynx,
                                   "path": UPath(id_ == 'bullet_container', visible_ == True) / UPath(
                                       type_ == 'com.lynx.tasm.LynxView', visible_ == True)},
            "cs_hashtags_card": {"path": UPath(id_ == 'list_view', visible_ == True)},
            "general_video_card_list": {"path": UPath(id_ == 'card_list', visible_ == True)},
            "general_live_card_list": {"type": Zs_live_card_list, "path": UPath(id_ == 'card_list')},
            "user_area_list": {"path": UPath(type_ == 'androidx.recyclerview.widget.RecyclerView', visible_ == True)},
            "hashtag_area_list": {"type": TrendingAreaCard_List, "path": UPath(type_ == 'com.lynx.tasm.LynxView')},
            "hashtag_area_list_lynx1": {"type": TrendingAreaCard_List, "path": UPath(id_ == 'bullet_container') / 0},
            "hashtag_area_list_lynx": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1},
            "effects_card_list_lynx": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0},
            "tool_aladdin_phont": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0 / 0 / 0 / 2},
            "see_whats_trending_today": {"path": UPath(desc_ == "View what's trending today")},
            "other_search_for": {"path": UPath(desc_ == "Others searched for", index=0)},
            "other_search_for_mock": {"path": UPath(desc_ == "swag boy q", visible_ == True, index=0)},
            "other_search_for_mock_JP": {"path": UPath(desc_ == "myroom", visible_ == True)},
            "keyword_of_others_searched_list": {"type": RelatedSearchWords, "path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 0 / 1},
            "recom_word": {"path": UPath(id_ == 'words_container', visible_ == True) / 0},
            "tuxSwitch": {"path": UPath(text_ == "This week")},
            "apply_btn": {"path": UPath(text_ == 'Apply')},
            "correct_strong": {"path": UPath(id_ == 'tv_correct_strong')},
            "correct_strong_1": {"path": UPath(id_ == 'tv_correct_weak')},
            "correction_area": {"path": UPath(id_ == "strong_container")},
            "content": {"path": UPath(id_ == "refresh_layout", visible_ == True)},
            "right_icon": {"path": UPath(~id_ == "search_tv_layout|search_toolbar_ellipsis", index=0)},
            "search_filters": {"path": UPath(id_ == "title_tv", index=0)},
            "filter_title": {"path": UPath(id_ == "nav_bar_title", visible_ == True)},
            'title': {"path": UPath(id_ == 'recyclerView') / 2 / UPath(id_ == 'filter_select_icon')},
            "filter_area": {"path": UPath(id_ == "sheet_content_container")},
            "filter_cancel": {"path": UPath(text_ == "Cancel")},
            "unwatched": {"path": UPath(text_ == "Unwatched")},
            "watched": {"path": UPath(text_ == "Watched")},
            "back": {"path": UPath(~id_ == 'back_btn_left|start_action_container', visible_ == True)},
            "user_head_portrait": {"type": TextView,
                                   'path': UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarBorderView')},
            "trending_card_list": {"type": TrendingAreaCard_List, "path": UPath(id_ == 'bullet_container') / 0 / 0},
            # 音乐人阿拉丁list
            "aladdin_list": {"type": TrendingAreaCard_List, "path": UPath(id_ == 'bullet_container') / 0 / 2 / 0},

            # User
            "first_video_lynx": {"path": UPath(id_ == "bullet_container") / 0 / 2 / 0 / 2 / 1 / 0 / 0 / 0},
            "first_user_avatar": {"type": FrameLayout,
                                  'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(
                                      type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarBorderView')},
            "first_user_name": {"type": Control, 'path': UPath(type_ == "RecyclerView", visible_ == True) / 0 / UPath(id_ == "tv_username")},
            "hot_user_name": {"type": Control, 'path': UPath(id_ == "tv_username", visible_ == True)},
            # vedios
            "videos_rect": {"type": FrameLayout, 'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(
                id_ == 'root_feed_cell') / UPath(type_ == 'androidx.constraintlayout.widget.ConstraintLayout')},
            "video_time": {"type": TextView, 'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(
                id_ == 'video_holder_time_label')},
            "video_text": {"type": TextView,
                           'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'desc')},
            "video_author_avatar": {"type": FrameLayout,
                                    'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(
                                        id_ == 'author_avatar')},
            "video_author_name": {"type": TextView, 'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(
                id_ == 'author_name')},
            "video_like_count": {'type': TextView,
                                 'path': UPath(id_ == 'list_view', visible_ == True) / 0 / UPath(id_ == 'like_count')},
            "video_text_user": {"type": FrameLayout, 'path': UPath(id_ == 'list_view', visible_ == True) / 0 / 1},
            # sounds
            "sounds_music_card": {"type": FrameLayout, 'path': UPath(id_ == 'list_view', visible_ == True) / 0},
            "sound_rect": {"type": FrameLayout, 'path': UPath(id_ == 'list_view', visible_ == True)},
            "sounds_avatar": {"type": ImageView,
                              'path': UPath(id_ == 'content', type_ == 'android.widget.LinearLayout') / 0 / UPath(
                                  id_ == 'iv_avatar')},
            "sounds_music_title": {"type": TextView,
                                   "path": UPath(id_ == 'content', type_ == 'android.widget.LinearLayout') / 0 / UPath(
                                       id_ == 'tv_music_title')},
            "sounds_music_author": {"type": TextView, 'path': UPath(id_ == 'content',
                                                                    type_ == 'android.widget.LinearLayout') / 0 / UPath(
                id_ == 'tv_music_author')},
            "sounds_music_duration": {"type": TextView, 'path': UPath(id_ == 'content',
                                                                      type_ == 'android.widget.LinearLayout') / 0 / UPath(
                id_ == 'tv_music_duration')},
            "sounds_music_used_count": {"type": TextView, 'path': UPath(id_ == 'content',
                                                                        type_ == 'android.widget.LinearLayout') / 0 / UPath(
                id_ == 'tv_used_count')},
            # hashtags

            "first_hashtag": {'path': UPath(id_ == 'list_view', visible_ == True) / 0},
            "hashtags_btn": {"type": TextView, 'path': UPath(id_ == 'list_view', visible_ == True) / 4 / UPath(
                type_ == 'android.widget.RelativeLayout')},
            "charc_user_card_erea": {
                "path": UPath(id_ == 'list_view') / UPath(type_ == 'com.ss.android.ugc.aweme.discover.lynx.e.b',
                                                          index=1) / UPath(
                    type_ == 'com.bytedance.ies.xelement.LynxScrollView$h', depth=6) / 1 / UPath(
                    type_ == 'com.lynx.tasm.behavior.ui.view.b') / 3},
            "charc_user_card_erea_lynx": {
                "path": UPath(id_ == "list_view") / 1 / UPath(id_ == "bullet_container") / 0},
            "first_trending": {"path": UPath(id_ == 'bullet_container') / 0 / 0 / 0 / 2},
            # "first_hot_trending": {"path": UPath(type_ == 'com.lynx.tasm.LynxView') / 1 / UPath(
            #     type_ == 'com.lynx.tasm.behavior.ui.view.b') / UPath(
            #     type_ == 'com.bytedance.ies.xelement.LynxScrollView$h') / 0},
            "first_hot_trending": {"path": UPath(type_ == 'com.bytedance.hybrid.spark.page.g') / 0 / 0 / 0 / 7 / 1 / 1 / 0},
            "first_hot_trending_1": {"path": UPath(type_ == 'com.bytedance.hybrid.spark.page.b') / 0 / 2 / 1 / 0 / 0},
            "hashtag_area": {"path": UPath(desc_ == 'Hashtags', type_ == 'com.lynx.FakeViews.FlattenView')},
            "hashtag_area_list": {"path": UPath(id_ == 'list_view') / 3 / UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView') / 0 / 0},
            "first_user": {"path": UPath(id_ == "search_mix_content") / UPath(type_ == "RecyclerView") / 0},
            "second_user": {"path": UPath(type_ == 'androidx.recyclerview.widget.RecyclerView') / 1 / UPath(
                id_ == 'search_common_user_rl')},
            "first_user_lynx": {"path": UPath(id_ == 'bullet_container', visible_ == True) / 0 / 0 / 2},
            "second_user_lynx": {"path": UPath(type_ == 'androidx.recyclerview.widget.RecyclerView') / 1 / UPath(
                id_ == 'search_common_user_rl')},
            "musician_aladdin": {"path": UPath(id_ == 'search_mix_content') / 0 / 0 / 0},
            "musician_aladdin_lynx": {"path": UPath(id_ == 'bullet_container') / 0 / 2 / 0 / 0 / UPath(
                type_ == "com.lynx.tasm.ui.image.FrescoImageView")},
            "user_icon": {"path": UPath(id_ == 'fl_avatar', visible_ == True, index=0)},
            "user_icon_lynx": {"path": UPath(type_ == "RecyclerView", visible_ == True) / 0 / UPath(id_ == "fl_avatar")},
            "hot_user_icon_lynx": {"path": UPath(id_ == "fl_avatar", visible_ == True, index=3)},
            "sounds_area": {"path": UPath(id_ == 'search_mix_content', visible_ == True)},
            "sounds_area_lynx": {"path": UPath(desc_ == 'Sounds', index=1)},
            "effect_titile": {"path": UPath(desc_ == "Effects", visible_ == True)},
            "sounds_card_lynx": {"type": trending_sounds_area, "path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 1 / 0},
            "sounds_detail": {"path": UPath(id_ == 'music_card_list_view') / 0 / 1},
            "sounds_detail_lynx": {"path": UPath(id_ == 'bullet_container') / 0 / 2 / 0 / 7 / 0 / 1 / 1},
            "all_sounds": {"path": UPath(id_ == 'view_all')},
            "all_sounds_lynx": {"path": UPath(type_ == "com.bytedance.hybrid.spark.page.f") / 0 / 2 / 0 / 9 / 1},
            "music_play_btn": {"path": UPath(id_ == 'music_card_list_view') / 0 / UPath(id_ == 'iv_play_status')},
            "music_play_btn_lynx": {"path": UPath(id_ == 'bullet_container') / 0 / 2 / 0 / 7 / UPath(
                type_ == 'com.lynx.tasm.ui.image.FrescoImageView')},
            "hot_card_first_account": {"path": UPath(id_ == 'fl_avatar', visible_ == True)},
            "hot_card_first_account_lynx": {"path": UPath(
                id_ == 'bullet_container', visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0 / 0},
            "see_all": {"path": UPath(id_ == 'card_list') / UPath(
                type_ == "com.bytedance.ies.dmt.ui.widget.AutoRTLImageView")},
            "card_list_area": {"type": Control, "path": UPath(id_ == 'card_list')},
            "card_list": {"type": Control, "path": UPath(
                id_ == 'bullet_container', visible_ == True) / 0 / 0 / 1 / 1 / 1 / 0 / 0},
            "card_list_area_lynx": {"type": Control, "path": UPath(id_ == 'bullet_container') / 0 / 2 / 0 / 2},
            "see_all_lynx": {"type": Control, "path": UPath(desc_ == 'See all', visible_ == True)},
            "hashtag_card": {"type": Control, "path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 1},
            "live_enter1": {"path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 1 / 0 / 0},
            "live_enter1_lynx": {"path": UPath(id_ == 'list_view') / 1 / UPath(
                id_ == 'bullet_container') / 0 / 0 / 1 / 0 / 0},
            "live_enter2": {"path": UPath(id_ == 'bullet_container', visible_ == True) / 0 / 0 / 0 / 14},
            "live_enter2_lynx": {"path": UPath(id_ == 'item_cover')},
            "live_enter3": {"path": UPath(desc_ == "ウェザーニュースLiVE", visible_ == True, index=0)},
            "first_history_word": {"path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 0 / 0 / 0},
            "first_history_word_lynx": {"path": UPath(id_ == "rn_fragment") / 0 / 0 / 0 / 0},
            "follow_btn": {"path": UPath(type_ == 'com.bytedance.ies.xelement.LynxScrollView$h') / 2 / UPath(
                type_ == 'com.lynx.tasm.behavior.ui.view.b') / 3},

            "specific_room": {'path': UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarBorderView', index=0)},

            "normal_follow": {"type": Control, "path": UPath(text_ == "Follow", visible_ == True, index=0)},
            "normal_following": {"type": Control, "path": UPath(text_ == "Following", visible_ == True, index=0)},
            "follow_btn_lynx": {"type": Control, "path": UPath(text_ == 'Follow', visible_ == True, index=0)},
            "following_btn_lynx": {"type": Control, "path": UPath(text_ == 'Following', visible_ == True, index=0)},
            "hot_follow": {"type": Control, "path": UPath(desc_ == 'Follow', visible_ == True)},
            "hot_following": {"type": Control, "path": UPath(text_ == 'Following', visible_ == True)},
            "result_normal_follow": {"type": Control, "path": UPath(text_ == 'Follow', visible_ == True, index=0)},
            "result_normal_following": {"type": Control, "path": UPath(text_ == 'Following', visible_ == True)},
            "text_area": {"path": UPath(~id_ == "fl_intput_hint_container|search_container", visible_ == True)},
            "search_btn": {"path": UPath(~id_ == 'tv_search_textview|end_action_container|cta_container', visible_ == True)},
            "suggested_options": {"path": UPath(id_ == 'bullet_container') / 0 / 0 / 1 / 0 / 1 / 0 / 0 / 0},
            "suggested_options_lynx": {"path": UPath(type_ == 'com.bytedance.hybrid.spark.page.SparkView') / 0 / 0 / 1 / 0 / 1 / 0 / 0 / 0},
            "search_account_list": {"type": Search_account_list, "path": UPath(
                id_ == 'recycler_view', type_ == 'androidx.recyclerview.widget.RecyclerView')},
            "sugword_List": {"type": SugWord_List, "path": UPath(id_ == "recycler_view", visible_ == True)},
            "suggested_searches_list": {"type": Suggested_searches_list, "path": UPath(type_ == "SparkView") / 0 / 0 / 1 / 0 / 1 / 0 / 0},
            "clear_search_btn": {"path": UPath(~id_ == 'btn_clear_new|inner_end_container', visible_ == True)},
            "clear_middle_search_btn": {"path": UPath(id_ == 'iv_delete_new')},
            "clear_history_btn": {"path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 0 / 0 / 0 / 0 / 0 / 1},
            "delate-history_btn": {"path": UPath(type_ == "SparkView") / 0 / 0 / 0 / 0 / 0 / 0 / 0 / 1 / 0},
            "history_word_text": {"path": UPath(~id_ == 'tv_content|tv_username', visible_ == True, index=0)},
            "clear_history_btn_lynx": {
                "path": UPath(id_ == "bullet_container", visible_ == True) / 0 / 0 / 0 / 4},
            "search_textarea": {"path": UPath(id_ == 'tv_static_text', visible_ == True)},
            "search_text": {"path": UPath(~id_ == 'et_search_middle|search_edit_text', visible_ == True)},
            "suggested_searches_title": {"path": UPath(desc_ == "You may like")},
            "music_card_lynx": {"path": UPath(type_ == 'com.bytedance.hybrid.spark.page.SparkView', visible_ == True) / 0 / 0 / 1 / 0 / 0},
            "trending_music_card_not_cover": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0 / 0 / 0 / 1 / 0},
            "zs_play_music_lynx": {"path": UPath(type_ == 'com.bytedance.ies.xelement.LynxScrollView$h') / 0 / UPath(
                type_ == 'com.lynx.tasm.ui.image.FrescoImageView')},
            "music_play_btn_lynx_general": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0 / 0 / 0 / 0 / 0 / 0},
            "music_card_shooting_btn": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0 / 0 / 0 / 1 / 1},
            "result_music_card_shooting_btn": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 2 / 0 / 0 / 0 / 2},
            "music_card_general": {"path": UPath(id_ == "list_view") / 2 / UPath(id_ == "bullet_container")},
            "feed_list": {"path": UPath(id_ == "list_view")},
            "cs_music_card_content_lynx": {"path": UPath(id_ == 'list_view', visible_ == True) / 0},
            "result_search_music_card": {"path": UPath(id_ == 'list_view', visible_ == True) / 0 / 1},
            "result_search_music_card_lynx": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 2 / 0 / 0},
            "result_search_trending_music_card": {"path": UPath(id_ == 'list_view', visible_ == True) / 1 / 1},
            "result_search_trending_music_card_lynx": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 2 / 1 / 0 / 0 / 1},
            "play_trending_music_genreal": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0 / 0 / 0 / 0 / 0 / 0 / 1},
            "Cosplay_account_title": {"path": UPath(desc_ == "Cosplay account")},
            "click_charc_card_follow": {"type": Control, "path": UPath(desc_ == "Follow", visible_ == True, index=2)},
            "click_charc_card_following": {"path": UPath(desc_ == "Following", visible_ == True, index=0)},
            "click_user_card_follow": {"path": UPath(id_ == 'search_mix_title', text_ == 'Users')},
            "charc_card_follow_btn": {"path": UPath(id_ == 'profile_btn_extra')},
            "trending_sounds_card_list_lynx": {"path": UPath(id_ == 'list_view')},
            "video_card_list_lynx_musician": {"path": UPath(
                type_ == "com.bytedance.hybrid.spark.page.f") / 0 / 2 / 0 / 2 / 1 / 0},
            # "music_card_lynx": {"path": UPath(type_ == 'com.bytedance.ies.xelement.LynxScrollView$h')/UPath(type_
            # == 'com.lynx.tasm.behavior.ui.view.b')/1}, "zs_play_music_lynx": {"path": UPath(type_ ==
            # 'com.bytedance.ies.xelement.LynxScrollView$h')/0/UPath(type_ ==
            # 'com.lynx.tasm.ui.image.FrescoImageView')}, "cs_music_card_content_lynx": {"path": UPath(type_ ==
            # 'com.lynx.tasm.LynxView', visible_ == True)/UPath(type_ ==
            # 'androidx.recyclerview.widget.RecyclerView')/0/UPath(type_ == 'com.lynx.tasm.behavior.ui.view.b')/1},

            # safety
            "safty_tip_page": {"path": UPath(type_ == "SparkView", visible_ == True)},
            "safety_banner": {"path": UPath(id_ == "list_view") / 0 / 1 / 0 / 0 / 0 / 0 / 0 / 0},
            "safety_link": {"path": UPath(desc_ == "View resources", visible_ == True)},
            "safety_link_desc": {"type": Control, "path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 0 / 0 / 0 / 2},

            "safety_tel": {"path": UPath(id_ == "dynamic_fragment", visible_ == True) / 0 / 0 / 0 / 0 / 0 / 1 / 0 / 2},
            "safety_tel_icon": {"path": UPath(id_ == 'list_view', visible_ == True)/1/UPath(id_ == 'iv_icon')},
            "safety_tel_title": {"path": UPath(id_ == 'list_view', visible_ == True)/1/UPath(id_ == 'title')},
            "safety_tel_number": {"path": UPath(id_ == 'list_view', visible_ == True)/1/UPath(id_ == 'number')},
            "safety_tel_desc": {"path": UPath(desc_ == "Samaritans", visible_ == True)},
            "safety_tel_button": {"type": Control, "path": UPath(id_ == "list_view") / 0 / 1 / 0 / 0 / 0 / 0 / 1 / 0 / 2},

            "safety_sms": {"path": UPath(type_ == 'androidx.recyclerview.widget.RecyclerView', visible_ == True)/2},
            "safety_sms_icon": {"path": UPath(id_ == 'list_view', visible_ == True)/2/UPath(id_ == 'iv_icon')},
            "safety_sms_title": {"path": UPath(id_ == 'list_view', visible_ == True)/2/UPath(id_ == 'title')},
            "safety_sms_number": {"path": UPath(id_ == 'list_view', visible_ == True)/2/UPath(id_ == 'number')},
            "safety_sms_desc": {"path": UPath(id_ == 'list_view', visible_ == True)/2/UPath(id_ == 'desc')},
            "safety_sms_button": {"path": UPath(text_ == 'Send SMS', visible_ == True)},

            "safety_text": {"path": UPath(id_ == 'please_notice_desc_tv', visible_ == True)},
            "safety_show_results_type": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 0 / 0 / 3 / 1},
            "suicide_videos": {"path": UPath(desc_ == "Videos", visible_ == True, index=1)},

            "safety_icon": {"path": UPath(id_ == 'iv_empty', visible_ == True)},
            "safety_title": {"path": UPath(id_ == 'tv_title', visible_ == True)},
            "safety_content": {"path": UPath(id_ == 'tv_desc', visible_ == True)},

            # "safety_bullet_container": {"path": UPath(id_ == 'bullet_container', visible_ == True)/0/0/0/0},
            "safety_bullet_container": {"path": UPath(id_ == 'list_view')/1/UPath(id_ == 'bullet_container')/0/0/0/0},
            "safety_bullet_container_icon_us": {"type": Control, "path": UPath(id_ == 'list_view') / 1 / UPath(type_ == 'com.lynx.tasm.ui.image.FrescoImageView', depth=8)},
            "safety_bullet_container_title_us": {"type": Control, "path": UPath(id_ == 'list_view') / 1 / UPath(id_ == 'bullet_container') / 0 / 0 / 0 / 0 / 1 / 0},
            "safety_bullet_container_content_us": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'bullet_container')/0/0/0/0/1/1},
            "safety_bullet_container_gb": {"path": UPath(id_ == 'bullet_container', visible_ == True)/0/0/0/0},
            "safety_bullet_container_icon_gb": {"type": Control, "path": UPath(id_ == 'bullet_container', visible_ == True) / UPath(type_ == 'com.lynx.tasm.ui.image.FrescoImageView')},
            "safety_bullet_container_title_gb": {"type": Control, "path": UPath(id_ == 'bullet_container', visible_ == True) / 0 / 0 / 0 / 0 / 1 / 0},
            "safety_bullet_container_content_gb": {"type": Control, "path": UPath(id_ == 'bullet_container', visible_ == True) / 0 / 0 / 0 / 0 / 1 / 1},
            "safety_bullet_container_videos": {"type": Control, "path": UPath(id_ == 'search_mix_title')},

            "safety_recycle_view_title": {"type": Control, "path": UPath(id_ == 'recycle_view', visible_ == True)/UPath(id_ == 'bullet_container')/0/0/0/2},
            "safety_recycle_view_content": {"type": Control, "path": UPath(id_ == 'recycle_view', visible_ == True)/UPath(id_ == 'bullet_container')/0/0/0/3},
            "safety_button": {"type": Control, "path": UPath(id_ == 'recycle_view', visible_ == True)/UPath(id_ == 'bullet_container')/0/0/0/4},
            "safety_button_text": {"type": Control, "path": UPath(id_ == 'recycle_view', visible_ == True)/UPath(id_ == 'bullet_container')/0/0/0/4/UPath(type_ == 'com.lynx.FakeViews.FlattenView')},
            # "safety_distressing": {"type": Control, "path": UPath(id_ == 'bullet_container')/0/0/0/0},
            # "safety_distressing_icon": {"type": Control, "path": UPath(type_ == 'com.lynx.tasm.ui.image.FrescoImageView')},
            # "safety_distressing_title": {"type": Control, "path": UPath(id_ == 'bullet_container')/0/0/0/0/1/0},
            # "safety_distressing_content": {"type": Control, "path": UPath(id_ == 'bullet_container')/0/0/0/0/1/1},
            # "safety_distressing_videos": {"type": Control, "path": UPath(id_ == 'search_mix_title')},

            "video_play_list": {"path": UPath(id_ == "power_list", visible_ == True)},
            "music_card_whole": {"path": UPath(desc_ == "Sounds", index=1)},
            "sounds_card_area": {"path": UPath(id_ == 'bullet_container', visible_ == True) / 0 / 0 / 0 / 9 / 0 / 18},
            "products_title": {"path": UPath(desc_ == "Products", visible_ == True)},
            "shop_tab_filtter_icon": {"path": UPath(desc_ == "Best match", visible_ == True)},
            "wiki_mark": {"path": UPath(desc_ == "Wikipedia", visible_ == True)},
            "wiki_title": {"path": UPath(desc_ == "Taylor Swift", visible_ == True)},
            "wiki_text": {"path": UPath(
                id_ == "list_view") / 3 / UPath(type_ == "SparkView") / 0 / 0 / 1 / 0 / 1 / 1 / 0 / 0 / UPath(type_ == "LynxView")},
            "wiki_image": {"path": UPath(id_ == "list_view") / 3 / UPath(type_ == "SparkView") / 0 / 0 / 1 / 0 / 1 / 0},
            "commodity_card_list": {"path": UPath(id_ == "card_list", visible_ == True)},
            "charcl_user_card": {"path": UPath(id_ == "bullet_container", visible_ == True) / 0 / 3},
            "musician_first_video_1": {"path": UPath(id_ == "card_list") / UPath(id_ == "root_feed_cell", index=0) / UPath(id_ == "cover")},
            "musician_first_video": {"path": UPath(type_ == "com.bytedance.hybrid.spark.page.f",
                                                   visible_ == True) / 0 / 2 / 0 / 2 / 1 / 0 / 0 / 0},
            "hot_user_video_list": {"path": UPath(type_ == "com.bytedance.hybrid.spark.page.f",
                                                   visible_ == True) / 0 / 2 / 0 / 2 / 4 / 0},
            "user_card_general_title": {"path": UPath(id_ == 'search_mix_title', text_ == 'Users')},
            "user_card_general_title_lynx": {"path": UPath(desc_ == 'Users', type_ == 'com.lynx.FakeViews.FlattenView')},
            "user_card_general_area": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1 / 0 / 0 / 0},
            "the_second_video": {"path": UPath(id_ == 'card_list') / 1},
            "lynxview": {'type': LynxView,'path': UPath(type_ == 'com.bytedance.hybrid.spark.page.SparkView') / 0},
            "active_title_1": {"path": UPath(desc_ == '2023 FIA Formula One World Championship™')},
            "ask_more": {"path": UPath(id_ == "ask_more")},
            "tako_videos_list": {"path": UPath(id_ == "video_list", visible_ == True, index=0)},
            "live_list": {"path": UPath(desc_ == "LIVE", index=0)},
            "select_video": {"type": Control, "path": UPath(id_ == 'card_list') / 1 / UPath(~id_ == 'cover|user_video_cover')},
            "feature_answer_title": {"path": UPath(desc_ == "Featured answer")},
            "feature_answer_close": {"path": UPath(id_ == "engine_container") / UPath(type_ == "SparkView") / 0 / 0 / 1 / 0 / 0 / UPath(type_ == "LynxView")},
            "feature_answer_text_area": {"path": UPath(id_ == "engine_container") / UPath(type_ == "SparkView") / 0 / 0 / 1},
            "live_view": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 2},
            "close_feedback": {"path": UPath(id_ == "nav_end", visible_ == True)},
            "conversion_bar": {"path": UPath(desc_ == "2023 FIA Formula One World Championship™")},
            "activity_video_card": {"path": UPath(id_ == "list_view") / 1},
            "video_feedback_title": {"path": UPath(id_ == "sheet_nav_bar_container")},
            "feedback_content": {"path": UPath(id_ == "search_fb_options_fl")},
            "video_content": {"path": UPath(id_ == "search_fb_introduction_layout")}
        }

    def click_feature_answer_see_more(self):
        rect = self["feature_answer_title"].rect
        x = rect.left * 6 + rect.width + 27
        y = rect.top - 7 + rect.height * 5
        print("see more按钮的坐标是: ", x, y)
        self.app.get_device().click(x, y)

    def get_live_list(self):
        return self["live_list"].items()

    def click_first_live(self):
        rect = self["live_list"].rect
        x = rect.left - rect.width
        y = rect.top + rect.height * 3
        print("直播垂搜卡坐标", x, y)
        self.app.get_device().click(x, y)
        # if len(self.get_live_list()) > 0:
        #     self.get_live_list()[1].click()

    def get_tako_videos_list(self):
        self["tako_videos_list"].refresh()
        return self["tako_videos_list"].items()

    def click_ask_more(self):
        if self["ask_more"].wait_for_existing(timeout=5, raise_error=False):
            self["ask_more"].click()

    # def get_hot_user_list_lynx(self):
    #     num = 0
    #     if self["hot_user_card_list_lynx"].wait_for_existing(timeout=5, raise_error=False):
    #         for card in self["hot_user_card_list_lynx"].children:
    #             if card.elem_info["visible"]:
    #                 num = num + 1
    #         return num

    # 按控件位置截图
    def take_capture(self, area):
        time.sleep(5)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    def search_image(self, device, original_pic_name):
        # control = self["homepage_search_btn"]
        # self.BasePanel.element_compare_by_image(self, device, control, original_pic_name)
        from byted_cv.core import cv
        import os
        import cv2
        capture = cv.get_screenshot(device)
        self["active_title_1"].ensure_visible()
        rect = self["active_title_1"].rect
        target = capture[0][rect.top:(rect.top + rect.height), rect.left:(rect.left + rect.width)]
        basedir = os.path.abspath('.')
        pic_dir = os.path.join(basedir, device.serial)
        # os.mkdir(pic_dir)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        # original_pic_dir = os.path.join(os.path.abspath(os.path.join(os.getcwd(), "../../../../../..")), "resources")
        original_pic_dir = os.path.join(os.path.abspath(os.path.realpath(__file__) + os.path.sep + "../../../.."),
                                        "resources", "Search_image")
        print("路径：", original_pic_dir)
        original_pic_name = os.path.join(original_pic_dir, original_pic_name + ".png")
        # return ocr_helper.dhash_diff_pic(target_pic_name, original_pic_name)
        from shoots_cv.cv import CV
        cv_ = CV()
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']
        print(compare_result)
        return compare_result > 0.99999

    def get_hybrid_view(self):
        return self["lynxview"]

    def click_hot_card_list_lynx(self):
        if self["the_first_video"].wait_for_existing(timeout=5, raise_error=False):
            self["the_first_video"].click()
        else:
            self["hot_card_list_lynx"].click()

    def click_second_hot_card_list(self):
        if self["the_second_video"].wait_for_existing(timeout=5, raise_error=False):
            self["the_second_video"].click()
        else:
            self["second_hot_card_list_lynx"].click()

    def get_charc_user_card_list(self):
        # return self["cosplay_card_list"].items()
        num = 0
        if self["cosplay_card_list"].wait_for_existing(timeout=5, raise_error=False):
            for card in self["cosplay_card_list"].children:
                if card.elem_info["visible"]:
                    num = num + 1
            return num

    def find_music_card(self):
        for _ in Retry(timeout=50, interval=1):
            if self["music_card_whole"].existing:
                return
            self.swipe(y_direction=1, swipe_coefficient=3)

    def find_music_area(self):
        while not self["sounds_card_area"].wait_for_visible(timeout=2, raise_error=False):
            self.swipe(y_direction=1)
            time.sleep(2)
            if not self["sounds_card_area"].wait_for_existing(timeout=2, raise_error=False):
                self.swipe(y_direction=1, swipe_coefficient=3)
                time.sleep(2)
                # self["sounds_card_area"].refresh()
            else:
                self["sounds_card_area"].refresh()
        return self["sounds_card_area"].wait_for_existing(timeout=2, raise_error=False)

    def find_commodity_card(self):
        for _ in Retry(timeout=60, raise_error=False):
            if self["products_title"].wait_for_existing(timeout=2, raise_error=False):
                self.swipe(y_direction=1, swipe_coefficient=3)
                break
            else:
                self.swipe(y_direction=1, swipe_coefficient=3)

    def find_wiki_card(self):
        for _ in Retry(timeout=60, raise_error=False):
            if self["wiki_mark"].wait_for_existing(timeout=2, raise_error=False):
                # self.swipe(y_direction=1, swipe_coefficient=3)
                break
            else:
                self.swipe(y_direction=1, swipe_coefficient=3)

    def find_effects_card(self):
        while not self["effects_card_lynx"].wait_for_existing(timeout=2, raise_error=False):
            self.swipe(y_direction=1)
            time.sleep(2)
        return self["effects_card_lynx"].wait_for_existing(timeout=2, raise_error=False)

    def get_commodity_card_list(self):
        return self["commodity_card_list"].items()

    def commodity_card_swipe_for_more(self):
        for _ in Retry(timeout=60, raise_error=False):
            if self["products_title"].wait_for_invisible(timeout=2, raise_error=False):
                break
            else:
                self["commodity_card_list"].scroll(distance_x=500)

    # 关注综搜普通用户
    def click_normal_user_card_follow_btn(self):
        if self["normal_follow"].wait_for_existing(timeout=5, raise_error=False):
            self["normal_follow"].click()
        else:
            self["follow_btn_lynx"].click()

    # 取关综搜普通用户
    def click_normal_user_card_following_btn(self):
        if self["normal_following"].wait_for_existing(timeout=5, raise_error=False):
            self["normal_following"].click()
        else:
            self["following_btn_lynx"].click()

    # 关注综搜高热用户
    def click_hot_user_card_follow_btn(self):
        if self["hot_follow"].wait_for_existing(timeout=5, raise_error=False):
            self["hot_follow"].click()
        else:
            self["normal_follow"].click()

    # 取关综搜高热用户
    def click_hot_user_card_following_btn(self):
        self["hot_following"].wait_for_existing(timeout=5, raise_error=False)
        self["hot_following"].click()

    # 关注垂搜普通用户
    def click_result_normal_user_card_follow_btn(self):
        self["result_normal_follow"].wait_for_existing(timeout=5, raise_error=False)
        self["result_normal_follow"].click()

    # 取关垂搜普通用户
    def click_result_normal_user_card_following_btn(self):
        self["result_normal_following"].wait_for_existing(timeout=5, raise_error=False)
        self["result_normal_following"].click()

    def click_video_play_list(self):
        self["video_play_list"].wait_for_existing(timeout=5, raise_error=False)
        self["video_play_list"].children[1].click()

    def get_aladdin_list(self):
        return self["aladdin_list"].items()

    # 获取搜索结果account个数
    def get_search_account(self):
        sum = 0
        if len(self.get_search_account_list()) > 0:
            for i in self.get_search_account_list():
                if i.icon.existing:
                    sum += 1
            return sum

    # def click_first_trending_card(self):
    #     if len(self["trending_card_list"].items()) > 0:
    #         return self["trending_card_list"].items()[1].click()

    def get_musician_video_vard_list_lynx(self):
        num = 0
        if self["video_card_list_lynx_musician"].wait_for_existing(timeout=5, raise_error=False):
            for card in self["video_card_list_lynx_musician"].children:
                if card.elem_info["visible"]:
                    num = num + 1
            return num
        else:
            return len(self["video_card_list"].items())

    def get_trending_sounds_card_list_lynx(self):
        return len(self["music_area_card_list_lynx"].children)

    def get_zs_sounds_card_list_lnyx(self):
        return self['zs_sounds_card_list_lnyx'].items()

    def get_trending_sounds_general_list_lynx(self):
        return self["trending_sounds_general_list_lynx"].items()

    def get_trending_music_card_list(self):
        return len(self["trending_music_card_list"].children)

    # def click_hot_card_list_lynx(self):
    #     if self["the_first_video"].wait_for_existing(timeout=5,raise_error=False):
    #         self["the_first_video"].click()
    #     else:
    #         self["hot_card_list_lynx"].click()

    # 点击普通sug
    def click_normal_sug(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if not i.icon.existing:
                    i.click()
                    return

    # 点击任意sug词
    def click_any_sug(self):
        if len(self.get_sugword_List()) > 0:
            lst = self.get_sugword_List()
            random.choice(lst).sug_words.click()

    # 点击sug补全箭头并且输出文案
    def click_input_sug_arrow_key(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if i.arrow_key.existing:
                    i.arrow_key.click()
                    print(i.sug_words.text)
                    return i.sug_words.text

    # 输出历史词文字
    def output_history_words_text(self):
        time.sleep(2)
        return self["history_word_text"].text

    def into_suggested_searches(self):
        self["search_text"].refresh()
        self["search_text"].click()

    def click_clear_search_btn(self):
        self["clear_search_btn"].click()
        time.sleep(5)

    def click_clear_history_btn(self):
        if self["clear_history_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["clear_history_btn"].click()
        else:
            self["clear_history_btn_lynx"].click()

    def click_clear_search_btn_history(self):
        self["clear_search_btn"].click()

    def click_clear_middle_search_btn_history(self):
        self["clear_middle_search_btn"].click()
        time.sleep(5)

    def get_user_area_list(self):
        self["user_area_list"].refresh()
        return self["user_area_list"].items()

    def get_user_area_list_lynx(self):
        self["user_card_general_area"].refresh()
        return self["user_card_general_area"].items()

    def get_zs_treading_card_list(self):
        return self["zs_treading_card_list"].items()

    def click_suggested_options(self):
        # suggested_options_text = self["suggested_options_lynx"].text
        self["suggested_options_lynx"].click()
        # return suggested_options_text

    def click_first_you_may_like_words(self):
        if len(self.get_suggested_searches_list()) > 0:
            return self.get_suggested_searches_list()[1].click()

    def click_second_you_may_like_words(self):
        if len(self.get_suggested_searches_list()) > 0:
            return self.get_suggested_searches_list()[2].click()

    def get_hashtag_area_list(self):
        self["hashtag_area_list_lynx"].refresh()
        return self["hashtag_area_list_lynx"].items()

    def get_hashtag_area_list1(self):
        self["hashtag_area_list_lynx1"].refresh()
        return self["hashtag_area_list_lynx1"].items()

    def get_hashtag_card_list(self):
        return len(self["hashtag_area_list"].children) - 1

    def get_hashtag_area_list_lynx(self):
        return self["hashtag_area_list_lynx"].items()

    def click_first_hashtag_area_card_lynx(self):
        if len(self.get_hashtag_area_list_lynx()) > 0:
            return self.get_hashtag_area_list_lynx()[1].click()

    def click_first_hashtag_area_card(self):
        if len(self.get_hashtag_area_list_lynx()) > 0:
            return self.get_hashtag_area_list_lynx()[0].click()

    def pull_the_video_to_the_right(self):
        self["card_list_area"].swipe(x_direction=1, swipe_coefficient=3)

    def click_hashtag_area_list_record(self):
        # if len(self.get_hashtag_area_list()) > 0:
        #     return self.get_hashtag_area_list()[0].record_btn.click()
        self["tool_aladdin_phont"].wait_for_existing(timeout=5, raise_error=False)
        self["tool_aladdin_phont"].click()

    def get_effects_card_list(self):
        return self["effects_card_list_lynx"].items()

    def get_suggested_searches_list(self):
        return self["suggested_searches_list"].items()[0:11]

    def get_sugword_List(self):
        return self["sugword_List"].items()

    def click_first_sugword(self):
        if len(self.get_sugword_List()) > 0:
            return self.get_sugword_List()[0].click()

    # 计算富sug词数量
    def rich_sug_count(self):
        sum = 0
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if i.icon.existing:
                    sum += 1
            return sum

    # 计算普通sug词数量
    def normal_sug_count(self):
        sum = 0
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if not i.icon.existing:
                    sum += 1
            return sum

    def get_search_account_list(self):
        return self["search_account_list"].items()

    # 点击第一个account
    def click_first_account(self):
        if len(self.get_search_account_list()) > 0:
            for i in self.get_search_account_list():
                if i.title.existing:
                    return i.click()

    def get_search_account(self):
        sum = 0
        if len(self.get_search_account_list()) > 0:
            for i in self.get_search_account_list():
                if i.title.existing:
                    sum += 1
            return sum

    def put_in_search(self, text):
        self["text_area"].wait_for_visible(timeout=5)
        self["text_area"].input(text)

    def click_suggested_options_text(self):
        if self["suggested_options_lynx"].wait_for_existing(timeout=5, raise_error=False):
            # suggested_options_text = self["suggested_options_lynx"].text
            self["suggested_options_lynx"].click()
        else:
            # suggested_options_text = self["suggested_options"].text
            self["suggested_options"].click()
        # return suggested_options_text

    def get_sugword_List(self):
        return self["sugword_List"].items()

    # 普通sug
    def click_normal_sug_from_input(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if not i.icon.existing:
                    i.click()
                    print(i.nickname)
                    return i.nickname

    # 富sug
    def click_rich_sug_from_input(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if i.icon.existing:
                    i.click()
                    return i.nickname

    # 富sug
    def click_rich_sug(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if i.icon.existing:
                    i.click()
                    return

    # @property
    # def search_area_text(self):
    #     return self["text_area"].text

    def into_text_area(self):
        self["text_area"].refresh()
        self["text_area"].click()

    def click_search_btn(self):
        if self["search_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["search_btn"].click()

    def search_something(self, text):
        self["text_area"].input(text)
        time.sleep(5)
        self["search_btn"].click()
        time.sleep(3)

    def get_trending_card_list(self):
        self["trending_card_list"].refresh()
        return self["trending_card_list"].items()

    def check_video_list(self):
        return self["back_video_list"].wait_for_existing(raise_error=False, timeout=5)

    def get_history_words_list(self):
        return self["history_list"].items()

    # 搜索直播间并进入
    def search_and_enter(self, text):
        self["text_area"].input(text)
        self["search_btn"].click()
        time.sleep(3)
        self['specific_room'].click()
        time.sleep(3)

    def click_first_history_word(self):
        if self["first_history_word"].wait_for_existing(timeout=5, raise_error=False):
            self["first_history_word"].click()
        else:
            self["first_history_word_lynx"].click()

    def click_see_more_effects(self):
        return self["see_more_effects"].click()

    def click_live_enter1(self):
        if self["live_enter1_lynx"].wait_for_existing(timeout=5, raise_error=False):
            self["live_enter1_lynx"].click()
        else:
            self["live_enter1"].click()

    def click_live_enter2(self):
        if self["live_enter2_lynx"].wait_for_existing(timeout=5, raise_error=False):
            self["live_enter2_lynx"].click()
        else:
            self["live_enter2"].click()

    def click_live_enter3(self):
        return self["live_enter3"].click()

    def click_hashtag_card(self):
        return self["hashtag_card"].click()

    def click_user_icon(self):
        # if self["hot_user_icon_lynx"].wait_for_existing(timeout=2, raise_error=False):
        #     return self["hot_user_icon_lynx"].click()
        if len(self.get_user_area_list_lynx()) > 0:
            self.get_user_area_list_lynx()[0].click()

    def click_normal_user_icon(self):
        if self["user_icon_lynx"].wait_for_existing(timeout=2, raise_error=False):
            return self["user_icon_lynx"].click()
        else:
            return self["user_icon"].click()

    def click_shop_now_btn(self):
        return self["shop_now"].click()

    def check_normal_user_element(self):
        self["user_icon_lynx"].wait_for_existing(timeout=3)
        self["first_user_name"].wait_for_existing(timeout=6)
        self["follow_btn_lynx"].wait_for_existing(timeout=9)
        return True

    def check_hot_user_element(self):
        self["user_icon"].wait_for_existing(timeout=3)
        self["hot_user_name"].wait_for_existing(timeout=6)
        self["follow_btn_lynx"].wait_for_existing(timeout=9)
        return True

    def check_live_seller_element(self):
        self["user_title_lynx"].wait_for_existing(timeout=3)
        self["follow_btn_lynx"].wait_for_existing(timeout=6)
        return True

    def check_normal_user_element_following(self):
        return self["following_btn_lynx"].wait_for_existing(timeout=5, raise_error=False)

    def check_hot_user_element_following(self):
        return self["following_btn_lynx"].wait_for_existing(timeout=5, raise_error=False)

    def check_normal_user_element_follow(self):
        return self["follow_btn_lynx"].wait_for_existing(timeout=5, raise_error=False)

    def check_hot_user_element_follow(self):
        return self["follow_btn_lynx"].wait_for_existing(timeout=5, raise_error=False)

    def check_product_card_ui_element(self):
        self["user_title"].wait_for_existing(timeout=3)
        self["products_tab"].wait_for_existing(timeout=6)
        return True

    def check_browse_history_entry_card_element(self):
        return self["exclamation_mark"].visible and self["browsing_history_card_copy"].visible and self["arrow"].visible

    def check_wiki_card_element(self):
        return self["wiki_title"].visible and self["wiki_text"].visible and self["wiki_mark"].visible

    def check_warm_tips_wask_element(self):
        return self["tips_logo"].visible and self["tips_text"].visible and self["show_result"].visible

    def check_warm_tips_element(self):
        return self["warm_tips_logo"].visible and self["tips_text"].visible and self["warm_tips_title"].visible

    def check_warm_cue_card(self):
        return self["warm_tips_logo"].visible and self["warm_cue_title"].visible and self["warm_tips_text"]

    def click_arrow_btn(self):
        if self["arrow"].wait_for_existing(raise_error=False, timeout=20):
            self["arrow"].click()

    def click_wiki_card(self):
        if self["wiki_image"].wait_for_existing(raise_error=False, timeout=20):
            self["wiki_image"].click()

    def click_show_result_btn(self):
        if self["show_result"].wait_for_existing(raise_error=False, timeout=20):
            self["show_result"].click()

    def click_view_showcase(self):
        self["products_tab"].click()

    def find_live_single_merchandise_card(self, query_list):
        for query in query_list:
            self["text_area"].input(query)
            self["search_btn"].click()
            if self["shop_now"].wait_for_visible(timeout=5, raise_error=False):
                break
            self["clear_search_btn"].click()
            time.sleep(5)

    def get_user_area_card_list_lynx(self):
        self["user_area_card_list_lynx"].refresh()
        return self["user_area_card_list_lynx"].items()

    def find_see_all_and_click(self):
        # if self["card_list_area_lynx"].wait_for_existing(timeout=2, raise_error=False):
        #     self["card_list_area_lynx"].swipe(x_direction=1, swipe_coefficient=5)
        # while  self["card_list_area_lynx"].wait_for_existing(timeout=5, raise_error=False):
        #     self["card_list_area_lynx"].swipe(x_direction=1, swipe_coefficient=8)
        #     self["see_all_lynx"].click()
        # else:
        #     for _ in Retry(timeout=20):
        #         if self["see_all"].existing:
        #             return self["see_all"].click()
        #         self["card_list_area"].swipe(x_direction=1, swipe_coefficient=5)

        profile_panel = ProfilePanel(root=self.app)
        header = profile_panel.header
        while not header.wait_for_existing(timeout=1, raise_error=False):
            self["card_list_area"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(2)

    def find_see_all_and_click_lynx(self):
        profile_panel = ProfilePanel(root=self.app)
        header = profile_panel.header
        while not header.wait_for_existing(timeout=1, raise_error=False):
            self["card_list"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(2)

    def click_hot_card_first_account(self):
        if self["hot_card_first_account_lynx"].wait_for_existing(timeout=2, raise_error=False):
            return self["hot_card_first_account_lynx"].click()
        else:
            return self["hot_card_first_account"].click()

    def click_music_play_btn(self):
        if self["music_play_btn_lynx"].wait_for_existing(timeout=2, raise_error=False):
            return self["music_play_btn_lynx"].click()
        else:
            return self["music_play_btn"].click()

    def click_all_sounds(self):
        if self["all_sounds_lynx"].wait_for_existing(timeout=2, raise_error=False):
            self["all_sounds_lynx"].click()
        else:
            return self["all_sounds"].click()

    def click_sounds_detail(self):
        if self["sounds_detail_lynx"].wait_for_existing(timeout=2, raise_error=False):
            return self["sounds_detail_lynx"].click()
        else:
            return self["sounds_detail"].click()

    def get_hot_hashtag_card_whole_list(self):
        num = 0
        if self["hot_hashtag_card_whole_list"].wait_for_existing(timeout=5, raise_error=False):
            for card in self["hot_hashtag_card_whole_list"].children:
                if card.elem_info["visible"]:
                    num = num + 1
        return num+1

    def get_general_video_card_list(self):
        return self["general_video_card_list"].items()

    def get_general_live_card_list(self):
        return self["general_live_card_list"].items()

    # def click_first_general_video_card(self, device):
    #     if len(self["general_video_card_list"].children) > 0:
    #         care_rect = self["general_video_card_list"].children[9].rect
    # device.click(care_rect.center[0] + (care_rect.center[0] * 0.25), care_rect.center[1])

    def click_first_general_video_card(self):
        if len(self.get_general_video_card_list()) > 0:
            self.get_general_video_card_list()[1].click()

    def click_first_general_live_card(self):
        if len(self.get_general_live_card_list()) > 0:
            self.get_general_live_card_list()[0].click()

    def get_music_area_card_list(self):
        if self["music_area_card_list_lynx"].wait_for_existing(timeout=3, raise_error=False):
            return self["music_area_card_list_lynx"].children
        else:
            return self["music_area_card_list"].items()

    def get_music_card_general_list(self):
        return self.sounds_card_lynx.items()

    def get_music_area_card_list_lynx(self):
        return len(self["trending_sounds_card_list_lynx"].children)

    def get_music_card_list_general_list(self):
        return self["cs_music_list3"].items()

    # 点击第一张话题大卡
    def click_hot_hashtag_card(self):
        if self.get_hot_hashtag_card_whole_list() > 0:
            self["hot_hashtag_card_whole_list"].children[0].click()

    # 点击第一张音乐卡片进入详情页
    def click_music_card(self):
        if self["music_card_lynx"].wait_for_existing(timeout=3, raise_error=False):
            self["music_card_lynx"].click()
        else:
            self["music_area_card_list_lynx"].children[0].click()

    # 点击热榜音乐综搜非封面区域进入详情页
    def click_general_search_trending_music_card(self):
        if self["trending_music_card_not_cover"].wait_for_existing(timeout=3, raise_error=False):
            self["trending_music_card_not_cover"].click()

    # 点击第一张音乐卡片进入详情页
    def click_music_card_lynx(self):
        self["zs_sounds_card_list_lnyx"].wait_for_existing(timeout=5, raise_error=False)
        self["zs_sounds_card_list_lnyx"].click()

    # 点击第一张音乐卡片的播放按钮
    def play_music_card(self):
        if self["music_play_btn_lynx_general"].wait_for_existing(timeout=3, raise_error=False):
            self["music_play_btn_lynx_general"].click()
        # else:
        #     if len(self.get_music_area_card_list()) > 0:
        #         self.get_music_area_card_list()[0].general_play_btn.click()

    # 点击第一张音乐卡片的拍摄按钮
    def click_music_card_shooting_btn(self):
        if self["music_card_shooting_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["music_card_shooting_btn"].click()
        else:
            self["result_music_card_shooting_btn"].click()

    def find_music_card_list_general_lynx(self):
        if not self["music_card_general"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_list"].swipe(x_direction=1, swipe_coefficient=8)
        else:
            pass

    def click_music_play_btn_lynx_general(self):
        self["music_play_btn_lynx_general"].click()

    def play_trending_music_genreal(self):
        self["play_trending_music_genreal"].click()

    # 点击垂搜音乐/热榜垂搜音乐播放/暂停按钮
    def play_general_music_card(self):
        rect = self["result_music_play_btn_lynx"].rect
        x1 = rect.left + rect.width / 8
        y1 = rect.top + rect.height / 15
        x2 = rect.left + rect.width / 8
        y2 = rect.top + rect.height / 8
        if self["trending_result_music_title"].wait_for_existing(timeout=5, raise_error=False):  # 推荐upath播放暂停
            # self["result_music_play_btn_lynx"].click()
            self.app.get_device().click(x=x2, y=y2)
            print("热榜音乐播放键", x2, y2)
        else:
            self.app.get_device().click(x=x1, y=y1)
            print("普通音乐播放键", x1, y1)

    def play_trending_sounds_card(self):                                                     # 播放/暂停trending music
        if self["trending_sounds_play_btn"].wait_for_existing(timeout=5, raise_error=False):  # 推荐upath播放/暂停
            self["trending_sounds_play_btn"].click()
        else:
            return self["trending_sounds_play_btn_lynx"].click()

    # 获取垂搜话题卡的数量
    def get_cs_hashtags_count_card(self):
        return self["cs_hashtags_card"].items()

    # 获取垂搜音乐卡的数量
    def cs_music_card_count_lynx(self):
        return self["cs_music_card_lynx"].items()

    # 获取垂搜音乐卡的数量
    def get_cs_music_card(self):
        for _ in Retry(timeout=10, interval=1):
            if self["cs_music_list3"].wait_for_existing(raise_error=False, timeout=2):
                return self["cs_music_list3"].items()
            else:
                return self["cs_music_list2"].items()

    def get_music_card_list(self):
        for _ in Retry(timeout=10, interval=1):
            if self["music_card_list_lynx"].wait_for_existing(raise_error=False, timeout=2):
                return self["music_card_list_lynx"].items()
            else:
                return self["music_card_list"].items()

    def get_charc_card_list(self):
        # return self["charc_card_list"].items()
        num = 0
        if self["charc_card_list"].wait_for_existing(timeout=5, raise_error=False):
            for card in self["charc_card_list"].children:
                if card.elem_info["visible"]:
                    num = num + 1
            return num

    # 点击特色账号卡
    def click_charc_card(self):
        if self["click_charc_card"].wait_for_existing(timeout=5, raise_error=False):
            self["click_charc_card"].click()

    # 关注特色账号卡
    def follow_charc_card(self):
        if self["click_charc_card_follow"].wait_for_existing(raise_error=False, timeout=2):
            self["click_charc_card_follow"].click()
        else:
            self["click_charc_card_following"].click()

    def get_zs_user_card_list(self):
        return self["zs_user_card_list"].items()

    # 点击第一个视频卡片
    def click_first_video_card(self):
        if self["musician_first_video"].wait_for_existing(raise_error=False, timeout=2):
            self["musician_first_video"].click()
        else:
            self["musician_first_video_1"].click()

    def click_first_movei_lynx(self):
        self["the_first_video"].click()

    def click_first_video(self):
        self["the_first_video"].click()
        time.sleep(2)

    def get_video_card_list(self):
        for _ in Retry(timeout=10, interval=1):
            if self["video_card_list_lynx"].wait_for_existing(raise_error=False, timeout=2):
                return self["video_card_list_lynx"].items()
            else:
                return self["video_card_list"].items()

    def get_hot_user_list_lynx(self):
        num = 0
        if self["hot_user_card_list_lynx"].wait_for_existing(timeout=5, raise_error=False):
            for card in self["hot_user_card_list_lynx"].children:
                if card.elem_info["visible"]:
                    num = num + 1
            return num

    def click_musician_aladdin(self):
        if self["musician_aladdin_lynx"].wait_for_existing(raise_error=False, timeout=2):
            return self["musician_aladdin_lynx"].click()
        else:
            return self["musician_aladdin"].click()

    def click_first_user(self):
        if self["first_user_lynx"].wait_for_existing(raise_error=False, timeout=2):
            return self["first_user_lynx"].click()
        else:
            return self["first_user"].click()

    def click_second_user(self):
        if self["second_user_lynx"].wait_for_existing(raise_error=False, timeout=2):
            return self["second_user_lynx"].click()
        else:
            return self["second_user"].click()

    def get_also_search_list(self):
        return self["also_search_list"].items()

    def get_hot_card_sub_list(self):
        return self["hot_card_sub_list"].items()

    def get_other_search_for_list(self):
        return self["other_search_for_list"].items()

    # 寻找hashtag栏目
    def find_hashtag_area(self):
        for _ in Retry(timeout=50, interval=1):
            if self["hashtag_area"].existing:
                return
            self.swipe_up()

    # 寻找user title
    def find_user_title(self):
        for _ in Retry(timeout=50, interval=1):
            if self["user_title"].existing:
                return
            self.swipe_up()

    # 寻找user title lynx
    def find_user_title_lynx(self):
        for _ in Retry(timeout=50):
            if self["user_card_general_title"].existing:
                return
            self.swipe(y_direction=1, swipe_coefficient=5)

    # 寻找sounds栏目
    def find_sounds_area(self):
        for _ in Retry(timeout=50, interval=1):
            if self["sounds_area"].existing:
                return
            self.swipe_up()

    def click_first_trending(self):
        return self["first_trending"].click()

    # def get_trending_hashtags_video_list(self):
    #     return self(["trending_hashtags_video_list"].children[1]).children[0].items()
    #
    # def click_first_hot_trending(self):
    #     if len(self.get_trending_hashtags_video_list()) > 0:
    #         self(["trending_hashtags_video_list"].children[1]).children[0].click()

    # 点击第一张hashtag视频
    # def click_first_hot_trending(self):
    #     if self["first_hot_trending"].wait_for_visible(timeout=3, raise_error=False):
    #         self["first_hot_trending"].click()
    #     else:
    #         return self["first_hot_trending_1"].click()

    # 寻找特色账号
    def find_charc_user_card(self):
        for _ in Retry(timeout=50):
            if self["Cosplay_account_title"].existing:
                break
            self.swipe(y_direction=1, swipe_coefficient=4)

    # 寻找普通账号
    def find_user_card(self):
        for _ in Retry(timeout=50):
            if self["click_user_card_follow"].wait_for_visible(timeout=2, raise_error=False):
                # self.swipe(y_direction=1, swipe_coefficient=3)
                break
            self.swipe(y_direction=1, swipe_coefficient=8)

    def swipe_up_u(self, ratio=7):
        """上滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["content"].drag(to_x + 5, to_y, -5, +offset_y)
        time.sleep(1)

    # 寻找sound栏
    def find_sounds(self):
        for _ in Retry(timeout=50, interval=1):
            if self["sounds"].existing:
                return
            self.swipe_up()

    def find_sounds_lynx(self):
        for i in range(3):
            self.swipe_up()
        for _ in Retry(timeout=50, interval=1):
            if self["sounds_area_lynx"].existing:
                return
            self.swipe_up()

    def find_sounds_general(self):
        # for i in range(3):
        #     self.swipe_up()
        for _ in Retry(timeout=50, interval=1):
            if self["sounds_area_lynx"].existing:
                return
            self.swipe_up()

    def find_effect_card(self):
        for _ in Retry(timeout=50, interval=1):
            if self["effect_titile"].wait_for_existing(timeout=2, raise_error=False):
                # self.swipe(y_direction=1, swipe_coefficient=3)
                break
            self.swipe(y_direction=1, swipe_coefficient=4)

    def get_search_account_list(self):
        return self["search_account_list"].items()

    # def click_first_account(self):
    #     if len(self.get_search_account_list()) > 0:
    #         for i in range(len(self.get_search_account_list())):
    #             if self.get_search_account_list().title.existing:
    #                 self.get_search_account_list()[i-1].click()
    #                 return self.get_search_account_list()[i-1].title

    @property
    def search_area_text(self):
        return self["search_text"].text

    def get_zs_result_list(self):
        return self["zs_result_list"].items()

    def click_tab(self, text):
        for _ in Retry():
            if self[text].wait_for_existing(timeout=2, raise_error=False):
                self[text].click()
                return
            self["tab_layout"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(3)

    def click_correct_strong(self):
        if self["correct_strong"].wait_for_existing(timeout=5, raise_error=False):
            self["correct_strong"].click()
        else:
            self['correct_strong_1'].click()

    def click_blank_space(self):
        rect = self["filter_title"].rect
        x = rect.left + rect.width - 66
        y = rect.top - 34 - rect.height * 3
        print("空白区域的坐标是：", x, y)
        self.app.get_device().click(x, y)

    def click_search_filters(self):
        self["right_icon"].click()
        time.sleep(2)
        self["search_filters"].click()
        time.sleep(2)
        self["tuxSwitch"].click()
        time.sleep(2)
        self["apply_btn"].click()

    def click_search_music_filters(self):
        self["right_icon"].click()
        time.sleep(2)
        self["search_filters"].click()
        self["filter_by_title"].click()
        time.sleep(2)
        self["apply_btn"].click()

    def get_history_list(self):
        return self["history_list"].items()

    # 切换user_tab
    def click_users_tab(self):
        self["users_tab"].click()

    # 切换videos_tab
    def click_videos_tab(self):
        self["videos_tab"].click()

    # 切换sounds_tab
    def click_sounds_tab(self):
        self["sounds_tab"].click()

    # 切换hashtags_tab
    def click_hashtags_tab(self):
        if not self["hashtags_tab"].wait_for_visible(timeout=3, raise_error=False):
            self["tab_layout"].swipe(x_direction=1, swipe_coefficient=4)
        self["hashtags_tab"].click()

    # safety banner校验
    def get_safety_banner(self):
        return self["safety_banner"].wait_for_visible(timeout=3, raise_error=False)

    # safety banner底部desc校验
    def get_safety_link_desc(self):
        return self["safety_link_desc"].desc

    def click_safety_link_btn(self):
        time.sleep(2)
        self["safety_link"].click()

    # safety 电话icon校验
    def get_safety_tel_icon(self):
        return self["safety_tel_icon"].wait_for_visible(timeout=3, raise_error=False)

    # safety 电话title校验
    def get_safety_tel_title(self):
        if self["safety_tel_title"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_tel_title"].text
        else:
            return True

    # safety 电话号码校验
    def get_safety_tel_number(self):
        if self["safety_tel_number"].wait_for_visible(timeout=3,raise_error=False):
            return self["safety_tel_number"].text
        else:
            return True

    # safety 电话desc校验
    def get_safety_tel_desc(self):
        if self["safety_tel_desc"].wait_for_visible(timeout=3,raise_error=False):
            return self["safety_tel_desc"].text
        else:
            return True

    # safety 电话button校验
    def get_safety_tel_button(self):
        if self["safety_tel_button"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_tel_button"].text
        else:
            return True

    # safety 短信icon校验
    def get_safety_sms_icon(self):
        return self["safety_sms_icon"].wait_for_visible(timeout=3,raise_error=False)

    # safety 短信title校验
    def get_safety_sms_title(self):
        if self["safety_sms_title"].wait_for_visible(timeout=3,raise_error=False):
            return self["safety_sms_title"].text
        else:
            return True

    # safety 短信号码校验
    def get_safety_sms_number(self):
        if self["safety_sms_number"].wait_for_visible(timeout=3,raise_error=False):
            return self["safety_sms_number"].text
        else:
            return True

    # safety 短信desc校验
    def get_safety_sms_desc(self):
        if self["safety_sms_desc"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_sms_desc"].text
        else:
            return True

    # safety 短信button校验
    def get_safety_sms_button(self):
        if self["safety_sms_button"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_sms_button"].text
        else:
            return True

    # suicide_prevent校验
    def get_safety_text(self):
        if self["safety_text"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_text"].text
        else:
            return True

    # suicide 底部按钮文案校验
    def get_safety_show_results_type(self):
        if self["safety_show_results_type"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_show_results_type"].text
        else:
            return True

    # 点击suicide 底部按钮校验
    def click_safety_show_results_type(self):
        rect = self["safety_show_results_type"].rect
        x = rect.width - rect.left
        y = rect.top + rect.height - 20
        print("点击坐标", x, y)
        self.app.get_device().click(x=x, y=y)
        # self["users_video_second"].click()
        # if self["safety_show_results_type"].wait_for_visible(timeout=3, raise_error=False):
        #     self["safety_show_results_type"].click(offset_y=-25)
        time.sleep(3)

    # suicide正向视频校验
    def get_suicide_videos(self):
        return self["suicide_videos"].text

    # 重新搜索
    def search_result(self):
        self["search_text"].click()
        time.sleep(1)
        self["search_btn"].click()
        time.sleep(1)

    # safety icon校验
    def get_safety_icon(self):
        return self["safety_icon"].wait_for_visible(timeout=3, raise_error=False)

    # safety no results found校验
    def get_safety_title(self):
        if self["safety_title"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_title"].text

    # safety content校验
    def get_safety_content(self):
        if self["safety_content"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_content"].text

    # safety content_link校验（坐标定位点击）
    def click_safety_content_link(self):
        # 直接点击具体坐标貌似失效
        # rect = self["safety_content"].rect
        # x_1 = rect.left
        # x_2 = rect.left+rect.width
        # y_1 = rect.top
        # y_2 = rect.top+rect.height
        # print("结果：", x_1, x_2, y_1, y_2),
        # for x in range(x_1+80, x_2, 50):
        #     for y in range(y_1+30, y_2, 27):
        #         self._click(x, y)
        #         if not self["safety_content"].wait_for_visible(timeout=1, raise_error=False):
        #             return
        # 改为以某点附近范围进行点击
        rect = self["safety_content"].rect
        x_1 = 0 - rect.width // 2
        x_2 = rect.width // 2
        y_1 = 0 - rect.height // 2
        y_2 = rect.height // 2
        # print("结果：", x_1, x_2, y_1, y_2),
        for x in range(x_1, x_2, 50):
            for y in range(y_1, y_2, 27):
                self["safety_content"].click(offset_x=x, offset_y=y)
                if not self["safety_content"].wait_for_visible(timeout=1, raise_error=False):
                    return

    def get_safety_recycle_view_title(self):
        return self["safety_recycle_view_title"].desc

    def get_safety_recycle_view_content(self):
        return self["safety_recycle_view_content"].desc

    # 温馨提示浮层按钮
    def get_safety_button_text(self):
        return self["safety_button_text"].desc

    def get_safety_button_position(self):
        # 切换tab后点击会报错，换坐标点击
        # self["safety_button_text"].click()
        rect = self["safety_button_text"].rect
        x = rect.left+rect.width/2
        y = rect.top+rect.height/2
        print("点击坐标", x, y)
        return x, y

    def click_position(self, x, y):
        self._click(x, y)

    def judge_safety_recycle_view(self):
        if self.position_bottom(self["safety_recycle_view_title"]) <= self["safety_recycle_view_content"].rect.top \
            and self.position_bottom(self["safety_recycle_view_content"]) <= self["safety_button"].rect.top:
            return True

    # safety Inappropriate令人不适的  提示框校验
    def get_safety_bullet_container(self):
        if self["safety_bullet_container"].wait_for_visible(timeout=3, raise_error=False) or \
            self["safety_bullet_container_gb"].wait_for_visible(timeout=3, raise_error=False):
            return True

    # safety Inappropriate令人不适的  提示框icon校验
    def get_safety_bullet_container_icon(self):
        if self["safety_bullet_container_icon_us"].wait_for_visible(timeout=3, raise_error=False):
            return True
        elif self["safety_bullet_container_icon_gb"].wait_for_visible(timeout=3, raise_error=False):
            return True

    # safety Inappropriate令人不适的  提示框title校验
    def get_safety_bullet_container_title(self):
        if self["safety_bullet_container_title_us"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_bullet_container_title_us"].desc
        elif self["safety_bullet_container_title_gb"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_bullet_container_title_gb"].desc

    # safety Inappropriate令人不适的 提示框content校验
    def get_safety_bullet_container_content(self):
        if self["safety_bullet_container_content_us"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_bullet_container_content_us"].desc
        elif self["safety_bullet_container_content_gb"].wait_for_visible(timeout=3, raise_error=False):
            return self["safety_bullet_container_content_gb"].desc

    # safety Inappropriate令人不适的 提示框下videos校验
    def get_safety_bullet_container_videos(self):
        if not self["safety_bullet_container_videos"].wait_for_visible(timeout=3, raise_error=False):
            self["video_list"].swipe(y_direction=1, swipe_coefficient=6)
        return self["safety_bullet_container_videos"].text

    # 元素底部位置  rect  =rect.top +rect.height
    def position_bottom(self, position):
        result = position.rect.top + position.rect.height
        return result

    # 元素右边位置  rect  =rect.left +rect.width
    def position_right(self, position):
        result = position.rect.left + position.rect.width
        return result

    # safety banner、link的相对位置判断
    def judge_safety_element_position_banner(self):
        if self.position_bottom(self["top_tab"]) < self["safety_banner"].rect.top \
        and self.position_bottom(self["safety_banner"]) == self["safety_link"].rect.top:
            return True
        else:
            return False

    # safety tel的相对位置判断
    def judge_safety_element_position_tel(self):
        # banner下的link与tel相对位置
        if self.position_bottom(self["safety_link"]) <= self["safety_tel"].rect.top:
            result1 = True

        # tel icon的相对位置
        if self["safety_tel_icon"].rect.left >= self["safety_tel"].rect.left \
            and self["safety_tel_icon"].rect.top >= self["safety_tel"].rect.top \
            and self.position_bottom(self["safety_tel_icon"]) <= self.position_bottom(self["safety_tel"]) \
            and self.position_right(self["safety_tel_icon"]) <= self["safety_tel_title"].rect.left:
            result2 = True

        # tel title，number，desc相对位置
        if self["safety_tel_title"].rect.top == self["safety_tel"].rect.top \
            and self.position_bottom(self["safety_tel_title"]) <= self["safety_tel_number"].rect.top \
            and self.position_bottom(self["safety_tel_number"]) <= self["safety_tel_desc"].rect.top \
            and self["safety_tel_title"].rect.left == self["safety_tel_number"].rect.left \
            and self["safety_tel_number"].rect.left == self["safety_tel_desc"].rect.left:
            result3 = True

        # tel button的相对位置
        if self.position_right(self["safety_tel_button"]) == self.position_right(self["safety_tel"]) \
            and self["safety_tel_button"].rect.left >= self.position_right(self["safety_tel_title"]) \
            and self["safety_tel_button"].rect.left >= self.position_right(self["safety_tel_number"]) \
            and self["safety_tel_button"].rect.left >= self.position_right(self["safety_tel_desc"]) \
            and self["safety_tel_button"].rect.top >= self["safety_tel"].rect.top \
            and self.position_bottom(self["safety_tel_button"]) <= self.position_bottom(self["safety_tel"]):
            result4 = True

        if result1 == result2 == result3 == result4 == True:
            return True
        else:
            return False

    # safety sms的相对位置判断
    def judge_safety_element_position_sms(self):
        # tel和sms的相对位置
        if self.position_bottom(self["safety_tel"]) <= self["safety_sms"].rect.top:
            result1 = True

        # sms 的icon相对位置
        if self["safety_sms_icon"].rect.left >= self["safety_sms"].rect.left \
            and self.position_bottom(self["safety_sms_icon"]) <= self.position_bottom(self["safety_sms"]) \
            and self["safety_sms_icon"].rect.top >= self["safety_sms"].rect.top \
            and self.position_right(self["safety_sms_icon"]) <= self["safety_sms_title"].rect.left:
            result2 = True

        # sms title,number,desc的相对位置
        if self["safety_sms_title"].rect.top == self["safety_sms"].rect.top \
            and self.position_bottom(self["safety_sms_title"]) <= self["safety_sms_number"].rect.top \
            and self.position_bottom(self["safety_sms_number"]) <= self["safety_sms_desc"].rect.top \
            and self["safety_sms_title"].rect.left == self["safety_sms_number"].rect.left \
            and self["safety_sms_number"].rect.left == self["safety_sms_desc"].rect.left:
            result3 = True

        # sms button的相对位置
        if self.position_right(self["safety_sms_button"]) == self.position_right(self["safety_sms"]) \
            and self["safety_sms_button"].rect.left >= self.position_right(self["safety_sms_title"]) \
            and self["safety_sms_button"].rect.left >= self.position_right(self["safety_sms_number"]) \
            and self["safety_sms_button"].rect.left >= self.position_right(self["safety_sms_desc"]) \
            and self["safety_sms_button"].rect.top >= self["safety_sms"].rect.top \
            and self.position_bottom(self["safety_sms_button"]) <= self.position_bottom(self["safety_sms"]):
            result4 = True

        if result1 == result2 == result3 == result4 == True:
            return True
        else:
            return False

    # safety prevent文案的相对位置判断(存在两种联系方式)
    def judge_safety_element_position_button(self):
        if self["safety_sms"].wait_for_visible(timeout=3, raise_error=False) \
            and self["safety_text"].rect.top >= self.position_bottom(self["safety_sms"])  \
            and self["safety_show_results_type"].rect.top >= self.position_bottom(self["safety_text"]):
            return True
        elif self["safety_text"].rect.top >= self.position_bottom(self["safety_tel"])  \
            and self["safety_show_results_type"].rect.top >= self.position_bottom(self["safety_text"]):
            return True
        else:
            return False

    # safety 大屠杀页面 的相对位置判断
    def judge_safety_element_position_holocaust(self):
        if self["safety_title"].rect.top >= self.position_bottom(self["safety_icon"]) \
            and self["safety_content"].rect.top >= self.position_bottom(self["safety_title"]):
            return True
        else:
            return False

    # safety 令人不适和少人不宜页面 的相对位置判断
    def judge_safety_element_position_bullet_container(self):
        # 提示框整体位置
        if self["safety_bullet_container"].wait_for_visible(timeout=3, raise_error=False):
            if self["safety_bullet_container"].rect.top > self.position_bottom(self["top_tab"]):
                print('1')
                result1 = True
        # icon相对位置校验
            if self["safety_bullet_container_icon_us"].rect.left >= self["safety_bullet_container"].rect.left \
                and self["safety_bullet_container_icon_us"].rect.top >= self["safety_bullet_container_title_us"].rect.top \
                and self.position_right(self["safety_bullet_container_icon_us"]) <= self["safety_bullet_container_title_us"].rect.left \
                and self.position_bottom(self["safety_bullet_container_icon_us"]) < self.position_bottom(self["safety_bullet_container"]):
                print('2')
                result2 = True
        # title相对位置校验
            if self["safety_bullet_container_title_us"].rect.top >= self["safety_bullet_container"].rect.top \
                and self["safety_bullet_container_title_us"].rect.left == self["safety_bullet_container_content_us"].rect.left \
                and self.position_right(self["safety_bullet_container_title_us"]) <= self.position_right(self["safety_bullet_container_content_us"]) \
                and self.position_bottom(self["safety_bullet_container_title_us"]) <= self["safety_bullet_container_content_us"].rect.top:
                print('3')
                result3 = True
            if self.position_right(self["safety_bullet_container_content_us"]) <= self.position_right(self["safety_bullet_container"]) \
                and self.position_bottom(self["safety_bullet_container_content_us"]) <= self.position_bottom(self["safety_bullet_container"]):
                print('4')
                result4 = True

            if result1 == result2 == result3 == result4 == True:
                return True

        if self["safety_bullet_container_gb"].wait_for_visible(timeout=3, raise_error=False):
            if self["safety_bullet_container_gb"].rect.top > self.position_bottom(self["top_tab"]):
                print('5')
                result5 = True
        # icon相对位置校验
            if self["safety_bullet_container_icon_gb"].rect.left >= self["safety_bullet_container_gb"].rect.left \
                and self["safety_bullet_container_icon_gb"].rect.top >= self["safety_bullet_container_title_gb"].rect.top \
                and self.position_right(self["safety_bullet_container_icon_gb"]) <= self["safety_bullet_container_title_gb"].rect.left \
                and self.position_bottom(self["safety_bullet_container_icon_gb"]) < self.position_bottom(self["safety_bullet_container_gb"]):
                print('6')
                result6 = True
        # title相对位置校验
            if self["safety_bullet_container_title_gb"].rect.top >= self["safety_bullet_container_gb"].rect.top \
                and self["safety_bullet_container_title_gb"].rect.left == self["safety_bullet_container_content_gb"].rect.left \
                and self.position_right(self["safety_bullet_container_title_gb"]) <= self.position_right(self["safety_bullet_container_content_gb"]) \
                and self.position_bottom(self["safety_bullet_container_title_gb"]) <= self["safety_bullet_container_content_gb"].rect.top:
                print('7')
                result7 = True
            if self.position_right(self["safety_bullet_container_content_gb"]) <= self.position_right(self["safety_bullet_container_gb"]) \
                and self.position_bottom(self["safety_bullet_container_content_gb"]) <= self.position_bottom(self["safety_bullet_container_gb"]):
                print('8')
                result8 = True

            if result5 == result6 == result7 == result8 == True:
                return True

    def get_search_reaults_list(self):
        self["search_reaults_list"].refresh()
        return self["search_reaults_list"].items()

    # 点击第一个搜索结果
    def click_first_search_result(self):
        if self["cs_music_card_content_lynx"].wait_for_existing(raise_error=False, timeout=2):
            return self["cs_music_card_content_lynx"].click()
        else:
            if len(self.get_search_reaults_list()) > 0:
                return self.get_search_reaults_list()[0].content.click()

    # 点击普通音乐卡垂搜下的第一张音乐子卡
    def click_result_search_music_card(self):
        if self["result_search_music_card"].wait_for_existing(raise_error=False, timeout=2):
            return self["result_search_music_card"].click()
        else:
            return self["result_search_music_card_lynx"].click()

    # 点击热榜音乐垂搜下的第一张子卡的非封面区域
    def click_result_search_trending_music_card(self):
        if self["result_search_trending_music_card"].wait_for_existing(raise_error=False, timeout=2):
            return self["result_search_trending_music_card"].click()
        else:
            return self["result_search_trending_music_card_lynx"].click()

    def click_first_video(self):
        if self["cs_music_card_content_lynx"].wait_for_existing(raise_error=False, timeout=5):
            return self["cs_music_card_content_lynx"].click()
        else:
            if len(self["search_reaults_list"].children) > 0:
                return self["search_reaults_list"].children[1].click()

    # 点击第一个lynx搜索结果
    def click_first_search_result_lynx(self):
        if len(self.get_trending_sounds_general_list_lynx()) > 0:
            return self.get_trending_sounds_general_list_lynx()[1].content.click()

    # 点击sounds第一个搜索结果的播放按钮
    def play_first_search_result(self):
        if len(self.get_search_reaults_list()) > 0:
            return self.get_search_reaults_list()[0].play_btn.click()

    def click_history(self):
        if len(self.get_history_list()) > 0:
            self.get_history_list()[0].click()

    def click_back(self):
        return self["back"].click()

    # 寻找相关搜索词
    def find_other_search_for_text(self):
        for _ in Retry(timeout=50, interval=1):
            if self["other_search_for"].existing:
                return self["other_search_for"].click()
                # return self["other_search_for"].text
            self.swipe_up()

    def click_keyword_in_others_searched_for(self):
        # while not self["keyword_of_others_searched_list"].wait_for_existing(timeout=2, raise_error=False):
        #     self.swipe(y_direction=1, swipe_coefficient=8)
        #     time.sleep(2)
        #     if not self["keyword_of_others_searched_list"].wait_for_existing(timeout=2, raise_error=False):
        #         self.swipe(y_direction=1, swipe_coefficient=8)
        #         time.sleep(2)
        #     else:
        #         self["keyword_of_others_searched_list"].refresh()
        # # keyword_of_others_searched_text = self["keyword_of_others_searched_list"].items()[0].keyword.text
        # self["keyword_of_others_searched_list"].items()[0].click()
        # # return keyword_of_others_searched_text
        for _ in Retry(timeout=50, interval=1):
            if self["other_search_for"].existing:
                rect = self["other_search_for"].rect
                x = rect.left + rect.width / 3
                y = rect.top + rect.height * 2
                print("RS词坐标", x, y)
                return self.app.get_device().click(x, y)
                # return self["other_search_for"].click(offset_y=200)
            self.swipe_up()

    # 寻找相关搜索词mock
    def find_other_search_for_mock(self):
        for _ in Retry(timeout=50, interval=1):
            if self["other_search_for_mock"].existing:
                return self["other_search_for_mock"].click()
            time.sleep(4)
            self.swipe(y_direction=1, swipe_coefficient=3)

    # 是否存在搜索词
    def is_find_other_search_for(self):
        for _ in Retry(timeout=15, interval=1):
            if self["other_search_for"].existing:
                self["other_search_for"].click()
                return False
            return True

    # 是否存在搜索词mock
    def is_other_search_for_mock_exist(self):
        for _ in Retry(timeout=50, interval=1):
            if self["other_search_for_mock_JP"].existing:
                # self["other_search_for_mock_JP"].click()
                break
            self.swipe_up()
        # while not self["other_search_for_mock_JP"].wait_for_visible(timeout=2, raise_error=False):
        #     time.sleep(3)
        #     if not self["other_search_for_mock_JP"].wait_for_existing(timeout=2, raise_error=False):
        #         self.swipe(y_direction=1, swipe_coefficient=2)
        #     else:
        #         self["other_search_for_mock_JP"].refresh()
        #         time.sleep(2)
        # return self["other_search_for_mock_JP"].wait_for_existing(timeout=2, raise_error=False)

    # 是否存在搜索词
    def is_other_search_for_exist(self):
        while not self["other_search_for"].wait_for_visible(timeout=2, raise_error=False):
            self.swipe(y_direction=1)
            time.sleep(2)
            if not self["other_search_for"].wait_for_existing(timeout=2, raise_error=False):
                self.swipe(y_direction=1, swipe_coefficient=3)
                time.sleep(2)
                # self["other_search_for"].refresh()
            else:
                self["other_search_for"].refresh()
                time.sleep(2)
        return self["other_search_for"].wait_for_existing(timeout=2, raise_error=False)

    # 点击相关搜索词对角视频
    def click_other_search_for_diagonally_video(self):
        if self.is_other_search_for_exist():
            for i in range(len(self.get_zs_result_list())):
                if self.get_zs_result_list()[i - 1].title.existing:
                    self.get_zs_result_list()[i - 2].click()
                    time.sleep(8)
                    break

    def click_see_whats_trending_today(self):
        self["see_whats_trending_today"].wait_for_existing()
        return self["see_whats_trending_today"].click()

    def check_see_whats_trending_today(self):
        if self["see_whats_trending_today"].existing:
            return True
        else:
            return False

    def get_treading_card_list(self):
        self["treading_card_list"].refresh()
        return self["treading_card_list"].items()

    # 获取垂搜用户列表

    def get_search_reaults_users_list(self):
        self["search_reaults_users_list"].refresh()
        return self["search_reaults_users_list"].items()

    # 播放第一个用户
    def click_first_users(self):
        if self["first_users"].wait_for_existing(raise_error=False, timeout=2):
            return self["first_users"].click()
        else:
            if len(self["search_reaults_list"].children) > 0:
                return self["search_reaults_list"].children[0].click()
            time.sleep(5)

    # 点击第二个用户
    def click_second_users(self):
        if self["second_users"].wait_for_existing(raise_error=False, timeout=2):
            return self["second_users"].click()
        else:
            if len(self["search_reaults_list"].children) > 0:
                return self["search_reaults_list"].children[1].click()
            time.sleep(5)

    # 点击第一个视频
    # def click_first_videos(self):
    #     if len(self.get_search_reaults_list()) > 0:
    #         self.get_search_reaults_list()[1].click()
    #         time.sleep(5)
    def click_second_videos(self):
        if len(self["search_reaults_list"].children) > 0:
            return self["search_reaults_list"].children[1].click()
            time.sleep(5)

    # 点击第一个话题
    def click_first_hashtag(self):
        if len(self.get_search_reaults_list()) > 0:
            self.get_search_reaults_list()[0].click()

    # 点击第一个热榜词
    # def click_first_treading(self):
    #     if len(self.get_treading_card_list()) > 0:
    #         return self.get_treading_card_list()[1].click()

    # 点击第一条effects的录制按钮
    def click_record_btn(self):
        if len(self.get_treading_card_list()) > 0:
            return self.get_treading_card_list()[0].record_btn.click()

    # 点击第一条effects
    def click_first_effect(self):
        # if len(self.get_treading_card_list()) > 0:
        #     return self.get_treading_card_list()[1].click()
        # # self["treading_card_list"].click()
        rect = self["effect_titile"].rect
        x = rect.left * 3 + rect.width * 2
        y = rect.top + rect.height * 4
        print("视频详情页话题坐标", x, y)
        self.app.get_device().click(x, y)

    # 点击推荐词
    def click_recom_word(self):
        if len(self["recom_word_list"].children) > 0:
            return self["recom_word_list"].children[1].click()
        # self["recom_word_list"].click()
        # return self["recom_word_list"].text

    def click_see_more(self):
        self["see_more"].click()
        time.sleep(3)

    def click_see_more_user(self):
        if not self["see_more_user"].wait_for_existing(timeout=5, raise_error=False):
            self.swipe(x_direction=1, swipe_coefficient=8)
        self["see_more_user"].click()
        time.sleep(3)

    def click_see_more_music(self):
        if self["see_more_music"].wait_for_visible(timeout=5, raise_error=False):
            self["see_more_music"].click()
        else:
            self["see_more_music_lynx"].click()
        time.sleep(3)

    def click_see_more_hashtags(self):
        if self["see_more_hashtags"].wait_for_existing(timeout=5, raise_error=False):
            self["see_more_hashtags"].click()
        else:
            self["see_more_hashtag_lynx"].click()
        time.sleep(3)

    def get_first_hashtag_name(self):
        return self["first_hashtag_name"].text

    def hashtag_swipe_up(self, ratio=1):
        """上滑
        """
        rect = self["hashtags_btn"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["hashtags_btn"].drag(to_x + 16, to_y, -16, +offset_y)
        time.sleep(1)
        self["hashtags_btn"].click()

    def click_hashtags(self):
        self["user_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["hashtags_tab"].click()

    def click_sounds_music_title(self):
        self["sounds_music_title"].click()

    def music_user_count(self):
        count_str = self["sounds_music_used_count"].text
        count_str = count_str.split(' ')[0]
        print(count_str)
        if count_str[-1] == "K":
            count = float(count_str[:-1]) * 1000
        elif count_str[-1] == "M":
            count = float(count_str[:-1]) * 1000000
        else:
            count = float(count_str)
        return int(count)

    def get_sounds_music_duration(self):
        return self["sounds_music_author"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_sounds_music_author(self):
        return self["sounds_music_author"].text

    def get_sounds_music_title(self):
        return self["sounds_music_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_sounds_avatar(self):
        return self["sounds_avatar"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def sounds_swipe_up(self, ratio=4):
        """上滑
        """
        rect = self["sound_rect"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["sound_rect"].drag(to_x + 16, to_y, -16, +offset_y)
        time.sleep(1)
        self["sound_rect"].click()

    def videos_swipe_up(self, ratio=2):
        """上滑
        """
        rect = self["videos_rect"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["videos_rect"].drag(to_x + 26, to_y, -26, +offset_y)
        time.sleep(1)
        self["videos_rect"].click()

    def get_sounds_title(self):
        return self["sounds_title"].text

    def click_sounds_tab(self):
        self["sounds"].click()

    def click_video(self):
        self["videos_rect"].click()

    def get_video_like_count(self):
        return self["video_like_count"].text

    def get_video_author_name(self):
        return self["video_author_name"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_video_author_avatar(self):
        return self["video_author_avatar"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_video_text(self):
        return self["video_text"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_video_time(self):
        return self["video_time"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_video_cover(self):
        return self["videos_rect"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def Judgment_video_presentation(self):
        if self["tab_rect"].rect.width - self["videos_rect"].rect.width * 2 < self["videos_rect"].rect.width:
            return True
        else:
            return False

    def click_videos_tab(self):
        self["videos_tab"].click()

    def click_first_user_avatar(self):
        self["first_user_avatar"].click()

    def get_first_user_id(self):
        return self["first_user_id"].text

    def get_first_user_name(self):
        return self["first_user_name"].text

    def get_user_head_portrait(self):
        return self["first_user_avatar"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def click_user_tab(self):
        self["user_tab"].click()

    def click_user_head_portrait(self):
        self["user_head_portrait"].click()

    def get_sounds_author_name(self):
        return self["sounds_author"].text

    def get_sounds_page(self):
        return self["sounds_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_sounds_play_btn(self):
        return self["sounds_play_btn"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def click_top_first_video(self):
        self["top_first_video"].click()

    def compare_vertical_relative_position(self, control1, control2):
        """
        如果control1的上顶点大于或者等于control2的下顶点（ control2.rect.top + control2.rect.height）
        返回 True
        :param control1:
        :param control2:
        :return:
        """
        if control1.rect.top >= control2.rect.top + control2.rect.height:
            return True
        else:
            return False

    def judge_UserMusicCard_elements_position(self):
        result1 = self.compare_vertical_relative_position(self["music_card"], self["top_video"])
        result2 = self.compare_vertical_relative_position(self["top_video"], self["user_info_rect"])
        result3 = self.compare_vertical_relative_position(self["user_info_rect"], self["user_title_all"])
        result4 = self.compare_vertical_relative_position(self["user_title_all"], self["tab_rect"])

        return result1 and result2 and result3 and result4

    def judge_user_at_top(self):
        print("坐标元素", self["first_user"].rect.top)
        print("坐标元素", self["tab_rect"].rect.top + self["tab_rect"].rect.height)
        result = self.compare_vertical_relative_position(self["first_user"], self["tab_rect"])
        return result

    def judge_video_text_position(self):
        result = self.compare_vertical_relative_position(self["video_text_user"], self["videos_rect"])
        return result

    def judge_sounds_music_card_position(self):
        print("坐标元素", self["sounds_music_card"].rect.top)
        print("坐标元素", self["tab_rect"].rect.top + self["tab_rect"].rect.height)
        self["sounds_music_card"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        result = self.compare_vertical_relative_position(self["sounds_music_card"], self["tab_rect"])
        return result

    def judge_hashtag_position(self):
        print("坐标元素", self["first_hashtag"].rect.top)
        print("坐标元素", self["tab_rect"].rect.top + self["tab_rect"].rect.height)
        result = self.compare_vertical_relative_position(self["first_hashtag"], self["tab_rect"])
        return result

    def get_top_video_like_number(self):
        print("text", self["top_video_like"].text)
        print("type", self["top_video_like"].text)
        return self["top_video_like"].text

    def get_top_video(self):
        return self['top_video'].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def get_top_userid(self):
        return self["top_userid"].text

    def get_top_username(self):
        return self["top_username"].text

    def get_user_title(self):
        return self["user_title"].text

    def get_user_photo(self):
        return self['user_photo'].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def wait_for_loading(self):
        self["refresh_layout"].wait_for_invisible(timeout=10, raise_error=False)

    def open_video(self, index=0):
        time.sleep(2)
        video_list = self["video_list"].items()
        video_list[index].click()

    def wait_for_empty_result(self):
        return self["empty_result"].wait_for_visible(raise_error=False)

    def swipe_up(self, ratio=4):
        """上滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["content"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)


class Sounds_card(Control):

    def get_locators(self):
        return {
            "play_btn": {"path": UPath(id_ == 'iv_status')},
            "content": {"path": UPath(id_ == 'rl_right')},
            "collect_btn": {"path": UPath(id_ == 'iv_music_collect')},
        }


class Sounds_card_List(Control):
    elem_class = Sounds_card
    elem_path = UPath(id_ == 'll_music_item', visible_ == True)


# 音乐创作页音乐卡片列表
class Create_music_video(Control):
    def get_locators(self):
        return {
            "add_btn": {"path": UPath(type_ == "com.lynx.FakeViews.LynxView", index=1)}
        }


class Create_music_video_list(Control):
    elem_class = Create_music_video
    elem_path = UPath(type_ == 'com.lynx.FakeViews.LynxView')


class ChooseMusicPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.choosemusic.activity.ChooseMusicActivity'}

    def get_locators(self):
        return {
            "search_input": {"path": UPath(id_ == 'rl_search', visible_ == True)},
            "search_input1": {"path": UPath(id_ == 'rl_search_container', visible_ == True)},
            "first_choose": {"path": UPath(id_ == 'listView', visible_ == True) / 0},
            "sounds_list": {"type": Result_List, "path": UPath(id_ == 'listView', visible_ == True)},
            "search_btn": {"path": UPath(id_ == 'tv_search_action', visible_ == True)},
            "create_music_video_list": {"path": UPath(type_ == "SparkView") / 0 / 0 / 0 / 0 / 1},
            "sounds_card_list": {"type": Sounds_card_List, "path": UPath(id_ == 'listView', visible_ == True)},
            "create_music_video_play_btn": {"path": UPath(type_ == "SparkView") / 0 / 0 / 1 / 1 / 0 / 0 / 0 / 0 / 2},
            "create_music_video_play_btn_mock": {"path": UPath(type_ == "SparkView") / 0 / 0 / 2 / 0 / 0 / 0 / 0 / 0 / 2},
            "add_btn_1": {"path": UPath(type_ == "SparkView") / 0 / 0 / 0 / 0 / 1 / 1 / 0 / 0 / 0 / 0 / 4 / 1}

        }

    def get_create_music_video_list(self):
        return self["create_music_video_list"].items()

    def click_add_btn(self):
        if self["add_btn_1"].wait_for_existing(timeout=5, raise_error=False):
            # if len(self.get_create_music_video_list()) > 0:
            #     self.get_create_music_video_list()[1].add_btn.click()
            self["add_btn_1"].click()

    # 点击第一个创作音乐视频卡
    def click_create_music_video(self):
        if len(self.get_create_music_video_list()) > 0:
            return self.get_create_music_video_list()[1].click()
        # if self["create_music_video_play_btn"].wait_for_existing(timeout=5, raise_error=False):
        #     self["create_music_video_play_btn"].click()
        # else:
        #     self["create_music_video_play_btn_mock"].click()

    def get_normal_sounds_card_list(self):
        return self["sounds_card_list"].items()

    def get_sounds_list(self):
        return self["sounds_list"].items()

    def get_sounds_cards_list(self):
        return self["sounds_card_list"].items()

    # 点击sounds第一个搜索结果的播放按钮
    def play_first_search_result(self):
        if len(self.get_sounds_cards_list()) > 0:
            return self.get_sounds_cards_list()[0].play_btn.click()

    # 点击sounds第一个搜索结果的收藏按钮
    def collect_first_search_result(self):
        if len(self.get_sounds_cards_list()) > 0:
            return self.get_sounds_cards_list()[0].collect_btn.click()

    def click_first_choose(self):
        return self["first_choose"].click()

    def search_sounds(self, text):
        self['search_input'].click()
        self['search_input1'].input(text)
        time.sleep(2)

    def click_search_btn(self):
        return self["search_btn"].click()


class TTShopPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.ecommerce.showcase.shop.ShopActivity'}

    def get_locators(self):
        return {
        "back_btn": {"path": UPath(id_ == 'nav_start', visible_ == True)},
    }


class ABClonePanel(Window):
    window_spec = {'activity': 'com.bytedance.ies.abmock.debugtool.submain.CloneActivity'}

    def get_locators(self):
        return {
        "sure_btn": {"path": UPath(id_ == 'button1', visible_ == True)},
        "restart_title": {"path": UPath(id_ == 'alertTitle', visible_ == True)},
        "config_id": {"type": Control, "path": UPath(id_ == 'idInput', visible_ == True)},
        "download_icon": {"type": Control, "path": UPath(id_ == 'downloadBtn', visible_ == True)},

    }

    def input_ab_clone(self, content):
        self["config_id"].input(content)
        time.sleep(1)
        self["download_icon"].click()

    def click_restart(self):
        for _ in Retry(timeout=20):
            if self["restart_title"].existing:
                self["sure_btn"].click()
                break


class LivePlayPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.live.LivePlayActivity|com.ss.android.ugc.aweme.detail.ui.DetailActivity'}

    def get_locators(self):
        return {"back": {"path": UPath(id_ == 'close_widget_container')},
                "blank_space": {"path": UPath(id_ == 'bottom_sheet_container')},
                'content': {'type': Control, 'path': UPath(id_ == 'content', visible_ == True, index=0)},
                "live_room_user_icon": {"path": UPath(id_ == "head", visible_ == True)},
                "user_head": {"path": UPath(id_ == "avatar_default_border", visible_ == True)},
                "share_btn": {'type': Control, 'path': UPath(id_ == "toolbar_container") / 4 / UPath(id_ == "toolbar_icon")},
                "share_top_panel": {"type": ScrollView, 'path': UPath(~id_ == 'recycle_view|rv_share_panel_avatar')},
                "long_press_panel": {"type": ScrollView, 'path': UPath(id_ == "share_long_press_dialog_container")},
                "more_btn": {'path': UPath(id_ == 'name_tv', text_ == "More")},
                "search_bar": {'path': UPath(id_ == 'search_et')},
                "select_user": {'path': UPath(id_ == 'contact_list_recyclerview')/0},
                "send_btn": {'path': UPath(id_ == 'tv_send')},
                "close_live": {'path': UPath(id_ == 'close_widget_container')},
                "user_name": {'path': UPath(id_ == 'll_has_data')/UPath(id_ == 'name')},
                "nickname": {'path': UPath(id_ == "user_base") / UPath(id_ == "user_name")},
                "enter_profile": {'path': UPath(id_ == 'anchor_info_container')},
                "touch_outside": {'path': UPath(id_ == 'touch_outside')},
                "product_recommendation": {'path': UPath(id_ == 'ecommercelive_product_preview')},
                "store_icon": {"path": UPath(id_ == 'seller_shop_icon', visible_ == True)},
                "back_btn": {"path": UPath(id_ == 'nav_start', visible_ == True)},
                "shop_icon": {"path": UPath(id_ == 'toolbar_icon_content', visible_ == True)},
                'content': {'type': Control, 'path': UPath(id_ == 'content', type_ == 'android.widget.FrameLayout')},
                "header": {'type': Control, "path": UPath(id_ == "caption_delete")},
                "more_friends": {'type': Control, "path": UPath(text_ == "More friends")},
                "gift_panel": {'type': Control, "path": UPath(id_ == "gift_panel_list")},
                "gift_bottom_panel": {'type': Control, "path": UPath(id_ == "tab_panel_bottom")},
                "bottom_bar": {'type': Control, "path": UPath(id_ == "toolbar_container")},
                "send_to_bar": {'type': Control, "path": UPath(id_ == "tv_title", visible_ == True)},
                }

    def swipe_down(self, ratio=4):
        """下滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] + offset_y
        self["content"].drag(to_x - 15, to_y, 15, -offset_y)
        time.sleep(1)

    def live_back_to_general_search(self):
        if self["blank_space"].wait_for_existing(timeout=10, raise_error=False):
            self.swipe_down()
            # self["blank_space"].click()
            self["back"].click()

    def click_live_room_user_icon(self):
        if self["live_room_user_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["live_room_user_icon"].click()
            time.sleep(3)
            self["user_head"].click()

    def click_share_btn(self):
        self["share_btn"].click()

    def long_press_live(self):
        if self.handle_live_gift_lift():
            return
        self["content"].long_click(duration=5)
        self.handle_live_gift_lift()

    def click_live(self):
        self["content"].click()

    def click_product_recommendation(self):
        for _ in Retry(timeout=90):
            self.swipe_up()
            if self["product_recommendation"].wait_for_existing(timeout=3, raise_error=False):
                self["product_recommendation"].click()
                time.sleep(5)
                break
            self.swipe_up()
            time.sleep(6)

    def swipe_up(self, ratio=6):
        """上滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y * 2
        self["content"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)

    def swipe_up_to_next_live(self):
        """上滑
        """
        self["content"].swipe(y_direction=1, swipe_coefficient=16)
        time.sleep(1)

    def send_to_user(self, user):
        for _ in Retry(limit=8, interval=1):
            if self["more_btn"].wait_for_visible(timeout=5, raise_error=False):
                break
            self["share_top_panel"].swipe(x_direction=1, swipe_coefficient=8)
            time.sleep(1)
        more_rect = self["more_btn"].parent.rect.center
        self.app.testcase.device.click(more_rect[0], more_rect[1])
        self["search_bar"].click()
        self["search_bar"].input(user)
        self["select_user"].click()
        self["send_btn"].click()

    def send_to_user_in_long_press_panel(self, user):
        if self.send_to_bar.wait_for_visible(timeout=8, raise_error=False):
            return self.send_to_user(user)
        self["long_press_panel"].swipe(y_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["long_press_panel"].swipe(y_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["long_press_panel"].swipe(y_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["more_friends"].click()
        self["search_bar"].click()
        self["search_bar"].input(user)
        self["select_user"].click()
        self["send_btn"].click()

    def click_close_live(self):
        if self["touch_outside"].existing:
            self["touch_outside"].click()
        self["close_live"].click()

    def enter_profile(self):
        # self["enter_profile"].click()
        time.sleep(3)

    def handle_live_gift_lift(self):
        if self.gift_panel.wait_for_visible(timeout=5, raise_error=False) or \
            self.gift_bottom_panel.wait_for_visible(timeout=1, raise_error=False):
            self.app.testcase.log_info("handle gift lift")
            self.app.testcase.device.back()
        if self.bottom_bar.wait_for_visible(timeout=3, raise_error=False):
            rect = self.bottom_bar.rect
            self.app.testcase.device.click(rect.right - 30, rect.center[1])
            time.sleep(3)
            return True


class EffectListPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.search.pages.result.effectlist.core.ui.SearchEffectListActivity'}

    def get_locators(self):
        return {"shoot_btn": {"type": Control, "path": UPath(id_ == "camera_button", visible_ == True, index=0)},
                "effect_list": {"path": UPath(id_ == "list", visible_ == True)},
                "back_btn": {"path": UPath(~id_ == "back_btn|nav_start", visible_ == True)}
        }

    def click_effect_detail_page_back_btn(self):
        self["back_btn"].wait_for_existing(timeout=5, raise_error=False)
        self["back_btn"].click()

    def get_fake_result_search_effect_list(self):
        return self["effect_list"].items()

    def click_fake_result_search_prop_card(self):
        if len(self.get_fake_result_search_effect_list()) > 0:
            self["effect_list"].items()[0].click()

    def click_fake_result_search_prop_card_shoot_btn(self):
        return self["shoot_btn"].click()


class Trending_sheet(Control):

    def get_locators(self):
        return {
        }


class Trending_sheet_list(Control):
    elem_class = Trending_sheet
    elem_path = UPath(type_ == 'androidx.constraintlayout.widget.ConstraintLayout', depth=1)


class filterVideoinfo(Control):
    def get_locators(self):
        return {
            "filter_like_count": {"path": UPath(id_ == "tv_search_sidebar_filter_like", visible_ == True)},
            "filter_like_icon": {"path": UPath(id_ == "tv_search_sidebar_filter_like", visible_ == True, index=0)},
            "play_or_pause_icon": {"path": UPath(id_ == "iv_search_ds_filter_item_play_state", visible_ == True)},
        }

    def get_filter_like_count(self):
        return self["filter_like_count"].text

    # 按控件位置截图
    def take_capture(self, area):
        time.sleep(5)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    def play_or_pause_filter_video(self):
        return self["play_or_pause_icon"].click()


class filtervideolist(Control):
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")
    elem_class = filterVideoinfo


class TrendingDetailPanel(Window):
    window_spec = {'activity':
    'com.ss.android.ugc.aweme.trending.ui.TrendingDetailActivity|com.ss.android.ugc.aweme.search.activity.SearchResultActivity'}

    def get_locators(self):
        return {
            "trending_bar": {"path": UPath(id_ == 'trending_list_group')},
            "trending_sheet_list": {"type": Trending_sheet_list, "path": UPath(id_ == 'trending_sheet_list')},
            "general_video_card_list": {"type": Zs_live_card_list, "path": UPath(id_ == 'card_list')},
            "treading_card_list": {"path": UPath(id_ == 'bullet_container') / 0 / 0 / 0},
        }

    def click_trending_bar(self):
        return self["trending_bar"].click()

    def get_trending_sheet_list(self):
        return self["trending_sheet_list"].items()

    def get_treading_card_list(self):
        return self["treading_card_list"].items()

    def click_first_treading(self):
        if len(self.get_treading_card_list()) > 0:
            return self.get_treading_card_list()[1].click()

    def get_general_video_card_list(self):
        return self["general_video_card_list"].items()

    def click_first_general_video_card(self):
        if len(self.get_general_video_card_list()) > 0:
            self.get_general_video_card_list()[1].click()


class TrendingDetail2Panel(Window):
    window_spec = {'activity':
    'com.ss.android.ugc.aweme.detail.ui.DetailActivity|com.ss.android.ugc.aweme.search.pages.core.ui.activity.SearchResultActivity'}

    def get_locators(self):
        return {
            "trending_bar": {"path": UPath(id_ == 'bottom_new', visible_ == True)},
            "trending_sheet_list": {"type": Trending_sheet_list, "path": UPath(id_ == 'trending_sheet_list')},
            "hot_user_video_card_list": {"path": UPath(id_ == 'card_list')},
            "the_first_video": {"path": UPath(id_ == 'card_list') / 0},
            "the_second_video": {"path": UPath(id_ == 'card_list') / 1},
            "the_first_video_card": {"path": UPath(id_ == "list_view", visible_ == True) / 0},
            "the_second_video_card": {"path": UPath(id_ == "list_view", visible_ == True) / 1},
            "the_third_video_card": {"path": UPath(id_ == "list_view", visible_ == True) / 2},
            "the_forth_video_card": {"path": UPath(id_ == "list_view", visible_ == True) / 3},
            "first_hot_trending": {
                "path": UPath(type_ == 'com.bytedance.hybrid.spark.page.g') / 0 / 0 / 0 / 7 / 1 / 1 / 0},
            "first_hot_trending_1": {"path": UPath(type_ == 'com.bytedance.hybrid.spark.page.b') / 0 / 2 / 1 / 0 / 0},
            "trending_hashtags_video_list": {"type": Search_Reaults_list, "path": UPath(
                type_ == 'com.bytedance.hybrid.spark.page.SparkView') / 0 / 0 / 0 / 4 / 1 / 1},
            "hot_card_list_lynx": {"path": UPath(
                id_ == 'bullet_container', visible_ == True) / 0 / 0 / 1 / 1 / 1 / 0 / 0 / 0},
            "second_hot_card_list_lynx": {
                "path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 1 / 1 / 1 / 0 / 0 / 1},
            "hot_hashtag_card_whole_list": {"path": UPath(type_ == "LynxView", index=0)},
            "tab_layout": {"type": Control,
                           "path": UPath(id_ == "mix_tab_layout")},
            "cs_music_card_content_lynx": {"path": UPath(type_ == 'com.lynx.tasm.LynxView', visible_ == True) / UPath(
                type_ == 'androidx.recyclerview.widget.RecyclerView') / 0 / UPath(
                type_ == 'com.lynx.tasm.behavior.ui.view.b') / 1},
            "search_reaults_list": {"type": General_search_video_list, "path": UPath(id_ == 'list_view', visible_ == True)},
            "videos_tab": {'type': Control, 'path': UPath(id_ == 'mix_tab_layout')/UPath(text_ == 'Videos', visible_ == True)},
            "reply_bar": {"path": UPath(id_ == 'reply_message_tux_text')},
            "video_post_time": {"path": UPath(id_ == "tv_post_time", visible_ == True)},
            "creator_nick_name": {"path": UPath(id_ == "title", visible_ == True)},
            "like_count": {"path": UPath(id_ == "digg_count", visible_ == True)},
            "user_avatar": {"path": UPath(id_ == "user_avatar", visible_ == True)},
            "video_detail_view": {"path": UPath(id_ == "viewpager", visible_ == True, index=1)},
            "back": {"path": UPath(id_ == "back_btn", visible_ == True)},
            "like_btn": {"path": UPath(id_ == "digg_container", visible_ == True)},
            "video_desc": {"path": UPath(id_ == "desc", visible_ == True)},
            "filter_videos_list": {"type": filtervideolist, "path": UPath(id_ == "power_list", visible_ == True)},
            "filter_left_video": {"type": Control, "path": UPath(id_ == "search_detail_filter_content")},
            "filter_videos_area": {"path": UPath(id_ == "power_list", visible_ == True)},
            "feedback_area": {"path": UPath(id_ == "sheet_container")}
        }

    def click_trending_bar(self):
        return self["trending_bar"].click()

    def get_trending_sheet_list(self):
        return self["trending_sheet_list"].items()

    def get_search_reaults_list(self):
        self["search_reaults_list"].refresh()
        return self["search_reaults_list"].items()

    def get_hot_hashtag_card_whole_list(self):
        num = 0
        if self["hot_hashtag_card_whole_list"].wait_for_existing(timeout=5, raise_error=False):
            for card in self["hot_hashtag_card_whole_list"].children:
                if card.elem_info["visible"]:
                    num = num + 1
        return num + 1

    def click_hot_hashtag_card(self):
        if self.get_hot_hashtag_card_whole_list() > 0:
            self["hot_hashtag_card_whole_list"].children[0].click()

    def click_hot_card_list_lynx(self):
        if self["the_first_video"].wait_for_existing(timeout=5, raise_error=False):
            self["the_first_video"].click()
        else:
            self["hot_card_list_lynx"].click()

    def click_second_hot_card_list_lynx(self):
        if self["the_second_video"].wait_for_existing(timeout=5, raise_error=False):
            self["the_second_video"].click()
        else:
            self["second_hot_card_list_lynx"].click()

    def click_tab(self, text):
        for _ in Retry():
            if self[text].wait_for_existing(timeout=2, raise_error=False):
                self[text].click()
                return
            self["tab_layout"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(3)

    def get_trending_hashtags_video_list(self):
        num = 0
        if self["trending_hashtags_video_list"].wait_for_existing(timeout=5, raise_error=False):
            for card in self["trending_hashtags_video_list"].children:
                if card.elem_info["visible"]:
                    num = num + 1
        return num + 1

    def click_first_hot_trending(self):
        if self.get_trending_hashtags_video_list() > 0:
            return self["trending_hashtags_video_list"].children[0].click()

    def click_the_second_video(self):
        if len(self["search_reaults_list"].children) > 0:
            return self["search_reaults_list"].children[1].click()

    def click_the_first_video(self):
        if self["cs_music_card_content_lynx"].wait_for_existing(raise_error=False, timeout=2):
            return self["cs_music_card_content_lynx"].click()
        else:
            if len(self["search_reaults_list"].children) > 0:
                return self["search_reaults_list"].children[0].click()

    def click_the_third_videos(self):
        if self["the_third_video_card"].wait_for_existing(raise_error=False, timeout=2):
            return self["the_third_video_card"].click()
        else:
            if len(self["search_reaults_list"].children) > 0:
                return self["search_reaults_list"].children[2].click()
            time.sleep(5)

    def click_the_fourth_videos(self):
        if len(self.get_search_reaults_list()) > 0:
            return self.get_search_reaults_list()[3].click()
        else:
            if len(self["search_reaults_list"].children) > 0:
                return self["search_reaults_list"].children[3].click()
            time.sleep(5)

    def play_hot_user_video_card(self, index):
        if len(self.get_hot_user_video_card_list()) > 0:
            self.get_hot_user_video_card_list()[index].click()

    def get_hot_user_video_card_list(self):
        self["hot_user_video_card_list"].refresh()
        return self["hot_user_video_card_list"].items()

    def get_video_detail_page_video_post_time(self):
        return self["video_post_time"].text

    def get_video_detail_page_creator_nick_name(self):
        return self["creator_nick_name"].text

    def get_video_detail_page_like_count(self):
        self["like_count"].refresh()
        return self["like_count"].text

    def give_a_like_to_video(self):
        if self["like_btn"].wait_for_existing(timeout=20, raise_error=False):
            self["like_btn"].click()

    # 按控件位置截图
    def take_capture(self, area):
        time.sleep(5)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    def back_to_general_search(self):
        if self["back"].wait_for_existing(raise_error=False, timeout=2):
            return self["back"].click()

    def get_video_detail_page_filter_video_list(self):
        self["filter_videos_list"].refresh()
        return self["filter_videos_list"].items()

    def swipe_down_filter_left_video(self):
        if self["filter_left_video"].wait_for_existing(timeout=20, raise_error=False):
            self["filter_left_video"].swipe(y_direction=-1, swipe_coefficient=8)

    def swipe_up_filter_left_video(self):
        if self["filter_left_video"].wait_for_existing(timeout=20, raise_error=False):
            self["filter_left_video"].swipe(y_direction=1, swipe_coefficient=8)

# find friends页
class FindFriendsPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.relation.ffp.ui.FindFriendsPageActivity'}

    def get_locators(self):
        return {
            "find_friends_follow": {"type": Control, "path": UPath(id_ == "search_list") / 0 / UPath(id_ == "relationBtn")},
            "find_friends_following": {"type": Control, "path": UPath(text_ == "Following", index=0)},
            "search_account_box": {"path": UPath(id_ == 'layout_search_container')},
            "text_area3": {"path": UPath(~id_ == 'edit_text|search_container|search_bar', index=0)},
            "background": {"path": UPath(id_ == 'guide_lottie_view')}

        }

    # 点击取消弹框
    def wait_for_background(self):
        self["background"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)
        # self["background"].click()
        return self["background"].wait_for_disappear(timeout=3, interval=0.5, raise_error=False)

    # 关注find friends页用户后取关
    def click_find_friends_follow_btn(self):
        if self["find_friends_follow"].wait_for_existing(timeout=5, raise_error=False):
             self["find_friends_follow"].click()
             time.sleep(5)
             self["find_friends_following"].click()

    # 搜索用户
    def search_account(self, text):
        self["text_area3"].wait_for_existing(timeout=5, interval=0.1)
        self["text_area3"].input(text)
        time.sleep(5)


# 自杀suicide预防支持页面
class SearchSuicidePreventionSupportPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.crossplatform.platform.webview.SingleWebView'}

    def get_locators(self):
        return {
            "suicide_prevention_support": {
                "path": UPath(type_ == 'com.ss.android.ugc.aweme.crossplatform.platform.webview.SingleWebView')},
        }

    def get_suicide_prevention_support(self):
        return self["suicide_prevention_support"].wait_for_invisible(timeout=10, raise_error=False)


# 厌食症预防支持页面
class SearchEatingDisorderSupportPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.crossplatform.platform.webview.SingleWebView'}

    def get_locators(self):
        return {
            "eating_disorder_support": {
                "path": UPath(type_ == 'com.ss.android.ugc.aweme.crossplatform.platform.webview.SingleWebView')},
        }

    def get_eating_discover_support(self):
        return self["eating_disorder_support"].wait_for_invisible(timeout=10, raise_error=False)


# 大屠杀预防支持页面
class SearchCoimmunityGuidelinesPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.crossplatform.platform.webview.SingleWebView'}

    def get_locators(self):
        return {
            "eating_discover_support": {
                "path": UPath(type_ == 'com.ss.android.ugc.aweme.crossplatform.platform.webview.SingleWebView')},
        }

    def get_community_guidelines_support(self):
        return self["eating_discover_support"].wait_for_invisible(timeout=10, raise_error=False)


# 获取页面title
class SearchPageTitle(Window):
    window_spec = {'activity': "com.ss.android.ugc.aweme.crossplatform.activity.CrossPlatformActivity"}

    def get_locators(self):
        return {
            "page_title": {"path": UPath(id_ == 'title')},
            "back_icon": {"path": UPath(id_ == 'close_custom')},
        }

    @property
    def get_page_title(self):
        if self["page_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            time.sleep(5)
            return self["page_title"].text

    def click_back_icon(self):
        self["back_icon"].click()


# 浏览历史卡片详情页
class SearchHistoryCardDetailPage(Window):
    window_spec = {'activity': "com.ss.android.ugc.aweme.watch.history.core.WatchHistoryActivity"}

    def get_locators(self):
        return {"watch_history": {"path": UPath(text_ == 'Watch history')}

        }

    def check_watch_history_details_page_element(self):
        return self["watch_history"].wait_for_visible(timeout=10, raise_error=False)

