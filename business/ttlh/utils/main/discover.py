# -*- coding:utf-8 _*-
from shoots_android.control import *
import time
from uibase.web import WebElement, Webview
from business.ttlh.utils.main.main import ChallengeDetailPanel
from shoots.retry import Retry
from uibase.upath import id_, text_, visible_, type_, class_


class FeedsList(ScrollView):
    elem_class = Control

    @on_expired
    def items(self):
        time.sleep(1)
        items = super(FeedsList, self).items()
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if len(items) != 0:
                break
        return items


class DiscoverCard(Control):
    # elem_path = UPath(id_ == "container")

    def get_locators(self):
        return {
            "name": {"type": Control, "path": UPath(id_ == "tv_title")},
            "type": {"type": Control, "path": UPath(id_ == "tv_type")},
            "vv": {"type": Control, "path": UPath(id_ == "tv_count")},
            "video_list": {"type": FeedsList, "path": UPath(id_ == "rv_list")},
        }

    def return_name(self):
        return self["name"].text

    def return_type(self):
        return self["type"].text

    def return_vv(self):
        return self["vv"].text[:-1]

    def click_video_by_index(self, index):
        self['video_list'].item()[index].click()
        time.sleep(2)


class DiscoverCardList(ScrollView):
    elem_class = DiscoverCard
    elem_path = UPath(id_ == "container")

    @on_expired
    def items(self):
        time.sleep(1)
        items = super(DiscoverCardList, self).items()
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if len(items) != 0:
                break
        return items


class DiscoverPanel(Window):
    """Discover Tab
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            'status_view': {'type': Control, 'path': UPath(id_ == 'status_view')},
            'progress_bar': {'type': Control, "root": "status_view", 'path': UPath(id_ == 'progressBarLayout')},
            #   Banner
            'banner': {'type': Control, 'path': UPath(id_ == 'll_discover_banner')},
            'dot_indicator': {'type': Control, 'path': UPath(id_ == 'dot_indicator')},
            #   Scan
            'scan': {'type': Control, 'path': UPath(id_ == 'scan', visible_ == True)},
            #   video in page initiation
            'first_topic': {'type': Control, 'path': UPath(id_ == 'list_view') / 1 / UPath(id_ == 'tv_title')},
            "first_topic_other": {'type': Control,
                                  'path': UPath(id_ == "list_view") / UPath(id_ == "container", index=0) / UPath(
                                      id_ == "tv_title")},
            'first_video': {'type': Control, 'path': UPath(id_ == 'list_view') / 1 / UPath(id_ == 'rv_list') / 1},
            'second_topic': {'type': Control, 'path': UPath(id_ == 'list_view') / 2 / UPath(id_ == 'tv_title')},
            'second_video': {'type': Control, 'path': UPath(id_ == 'list_view') / 2 / UPath(id_ == 'rv_list') / 1},
            #   Search
            'search_bar': {'type': TextEdit, 'path': UPath(id_ == 'rl_content_container')},
            'search_btn': {'type': Control, 'path': UPath(id_ == 'tv_search_textview')},
            'search_bar_discover': {'type': Control, 'path': UPath(id_ == 'tv_static_text', visible_ == True)},
            #   item card
            'discover_card_list': {'type': DiscoverCardList, 'path': UPath(id_ == 'list_view')},
            'first_rv_list': {'type': FeedsList, 'path': UPath(id_ == 'list_view') / 1 / UPath(id_ == 'rv_list')},
            #   Bottom_view
            'main_bottom_view': {'type': Control, 'path': UPath(id_ == 'main_bottom_tab_view')},
            #   Hashtag
            'hashtag_icon': {'type': Control, 'path': UPath(id_ == 'list_view') / 1 / UPath(id_ == 'iv_type')},
            'hashtag_title': {'type': Control, 'path': UPath(id_ == 'list_view') / 1 / UPath(id_ == 'tv_type')},
            'hashtag_vv_count': {'type': Control, 'path': UPath(id_ == 'list_view') / 1 / UPath(id_ == 'tv_count')},
        }

    def check_search_button(self):
        return self["search_bar"].wait_for_existing(timeout=5, raise_error=True)

    def get_hashtag_card(self):
        while self["hashtag_title"].text != "Trending hashtag":
            self.swipe(y_direction=1, swipe_coefficient=5)
        time.sleep(2)
        self["hashtag_icon"].click()

    def check_scan_button(self):
        return self["scan"].wait_for_existing(timeout=5, raise_error=True)

    def check_banner_list(self):
        return self["banner"].wait_for_existing(timeout=5, raise_error=True)

    def check_indicator(self):
        return self["dot_indicator"].wait_for_existing(timeout=5, raise_error=True)

    def check_hashtag_icon(self):
        return self["hashtag_icon"].wait_for_existing(timeout=5, raise_error=True)

    def check_hashtag_title(self):
        return self["hashtag_title"].wait_for_existing(timeout=5, raise_error=True)

    def check_hashtag_vv_count(self):
        return self["hashtag_vv_count"].wait_for_existing(timeout=5, raise_error=True)

    def banner_click(self):
        self["banner"].click()

    def get_discover_title(self):
        return self['discover_card_list'].items()[1].return_type()

    def quick_swipe(self, x_direction=0, y_direction=0, swipe_coefficient=3, duration=None):
        """快速滑动

        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        # 后续会支持斜着滑动
        :param swipe_coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        :param duration: 滑动操作周期，数值配置越小，滑动越快(s)
        """

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(x_direction, y_direction))

        self._driver.drag(self.id, x1, y1, x2, y2, duration)
        time.sleep(1)

    def get_visible_hashtag_card_name(self):
        hashtag_name_set = set()
        for item in self["discover_card_list"].items():
            if item.return_type() == "Trending hashtag" and item.rect.top < self['main_bottom_view'].rect.top:
                hashtag_name_set.add(item.return_name())
        return hashtag_name_set

    def first_rv_list(self):
        return len(self['frist_rv_list'].item()) > 0

    def wait_for_loading(self):
        self["banner"].wait_for_visible(interval=0.1, raise_error=True)
        self["scan"].wait_for_visible(interval=0.1, raise_error=True)
        self["first_video"].wait_for_visible(interval=0.1, raise_error=True)
        self["second_video"].wait_for_visible(interval=0.1, raise_error=True)
        time.sleep(3)

    def search(self, content):
        self["search_bar"].wait_for_visible(timeout=5, interval=0.1, raise_error=True)
        self["search_bar"].click()
        self["search_bar"].input(content)
        self["search_btn"].click()

    # 进入discover页，验证discover页搜索框后点击，进入中间页后在搜索框输入内容，点击搜索
    def search_discover(self, content):
        self["search_bar_discover"].wait_for_visible(timeout=5, interval=0.1, raise_error=True)
        self["search_bar_discover"].click()
        self["search_bar"].input(content)
        self["search_btn"].click()

    def click(self, ctl):
        self[ctl].click()

    def find_challenge_banner(self):
        for _ in Retry(limit=8, interval=5):
            self["banner"].click()
            if self.app.wait_for_activity(ChallengeDetailPanel.window_spec["activity"], timeout=3, raise_error=False):
                return
            self.app.back()

    def check(self, ctl):
        return self[ctl].wait_for_visible()


class KidsDiscoveryDetailPanel(DiscoverPanel):
    window_spec = {
        'activity': 'com.ss.android.ugc.aweme.kids.detailfeed.ui.activity.DetailActivity'}


class TrendingCard(Control):
    def get_locators(self):
        return {
            "ht_name": {"path": UPath(type_ == 'com.lynx.tasm.behavior.ui.text.AndroidText')}
        }


class TrendingCard_List(Control):
    elem_class = TrendingCard
    elem_path = UPath(type_ == "com.bytedance.ies.xelement.BounceLayout")


class History(Control):
    def get_locators(self):
        return {
            "video_desc": {"path": UPath(id_ == 'tv_content')}
        }


class History_List(Control):
    elem_class = History
    elem_path = UPath(type_ == "android.widget.LinearLayout", depth=1)


class Search_Reaults(Control):
    def get_locators(self):
        return {
            "user_name": {"path": UPath(id_ == 'tv_username')}
        }


class Search_Reaults_list(Control):
    elem_class = Search_Reaults
    elem_path = UPath(type_ == "android.widget.LinearLayout", depth=1)


class SearchResultPanel(Window):
    """
    Search Results Panel
    """

    window_spec = {
        "activity": "com.ss.android.ugc.aweme.search.activity.SearchResultActivity"}

    def get_locators(self):
        return {
            "content": {"path": UPath(id_ == "refresh_layout")},
            "back": {"type": Control, "path": UPath(id_ == 'back_btn_left')},
            "search_box": {"type": Control, "path": UPath(id_ == 'et_search_kw')},
            "clear_input": {"type": Control, "path": UPath(id_ == 'btn_clear')},
            "search_filters": {"type": Control, "path": UPath(id_ == 'search_bottom_sheet_layout')},
            "top_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 0},
            "users_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 1},
            "videos_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 2},
            "sounds_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 3},
            "hashtags_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 4},
            "hashtags_list": {'type': HashtagsList, "path": UPath(id_ == 'list_view', visible_ == True)},
            "search_mix_users": {'type': Search_Mix_User_Root, "path": UPath(id_ == 'search_mix_user_root') / UPath(
                id_ == 'search_mix_content') / 0},
            'empty_result': {'type': Control,
                             'path': UPath(id_ == 'tv_title', text_ == "No results found", visible_ == True)},
            'title': {"path": UPath(id_ == 'recyclerView') / 2 / UPath(id_ == 'filter_select_icon')},

            "see_more": {"path": UPath(type_ == 'com.lynx.tasm.behavior.ui.view.b') / UPath(type_ == 'com.lynx.tasm'
                                                                                                     '.behavior.ui'
                                                                                                     '.view.a')},
            "see_more_music": {"path": UPath(id_ == 'search_mix_user_root') / UPath(id_ == 'mix_head_container')},
            "see_more_user": {"path": UPath(type_ == 'com.lynx.tasm.LynxView') / 0 / UPath(type_ == 'com.lynx.tasm'
                                                                                                    '.behavior.ui.view.a')},
            "see_more_sounds": {"path": UPath(id_ == 'search_mix_user_root') / UPath(id_ == 'mix_head_container')},
            "see_more_hashtags": {"path": UPath(type_ == 'com.lynx.tasm.LynxView') / 0 / UPath(type_ == 'com.lynx.tasm'
                                                                                                        '.behavior.ui'
                                                                                                        '.view.a')},
            "treading_card_list": {"type": TrendingCard_List, "path": UPath(id_ == 'list_view', visible_ == True)},
            "see_whats_trending_today": {"path": UPath(id_ == 'btn_trendings')},
            "other_search_for": {"path": UPath(id_ == 'll_related_search_content') / 1 / UPath(type_ == 'com.bytedance'
                                                                                                        '.tux.input'
                                                                                                        '.TuxTextView')},
            "tuijian_word": {"path": UPath(id_ == 'words_container', visible_ == True) / 0},
            "history_list": {"type": History_List, "path": UPath(id_ == 'listview', visible_ == True)},
            "tuxSwitch": {
                "path": UPath(id_ == 'recyclerView') / 1 / UPath(type_ == 'com.bytedance.tux.input.TuxSwitch')},
            "apply_btn": {"path": UPath(id_ == 'confirm_dialog')},
            "correct_strong": {"path": UPath(id_ == 'tv_correct_strong')},
            "search_reaults_list": {"type": Search_Reaults_list, "path": UPath(id_ == 'list_view', visible_ == True)},
            "hashtag_card": {"type": FrameLayout,
                             "path": UPath(type_ == "com.bytedance.hybrid.spark.page.f",
                                           visible_ == True) / 0 / 0 / 0 / 3 / 0},
        }

    def find_hashtag_card(self):
        while not self["hashtag_card"].wait_for_visible(timeout=2, raise_error=False):
            self.swipe(y_direction=1)
            time.sleep(2)
            if not self["hashtag_card"].wait_for_existing(timeout=2, raise_error=False):
                self.swipe(y_direction=1, swipe_coefficient=3)
                time.sleep(2)
                self["hashtag_card"].refresh()
            else:
                self["hashtag_card"].refresh()
        return self["hashtag_card"].wait_for_existing(timeout=2, raise_error=False)

    def click_tab(self, text):
        self[text].click()
        time.sleep(3)

    def click_correct_strong(self):
        return self['correct_strong'].click()

    def click_search_filters(self):
        self["search_filters"].click()
        self["tuxSwitch"].click()
        time.sleep(2)
        self["apply_btn"].click()

    def click_search_music_filters(self):
        self["search_filters"].click()
        self["title"].click()
        time.sleep(2)
        self["apply_btn"].click()

    def get_history_list(self):
        return self["history_list"].items()

    def get_search_reaults_list(self):
        self["search_reaults_list"].refresh()
        return self["search_reaults_list"].items()

    def click_history(self):
        if len(self.get_history_list()) > 0:
            self.get_history_list()[0].click()

    def click_back(self):
        return self["back"].click()

    # 寻找相关搜索词
    def find_other_search_for(self):
        for _ in Retry(timeout=50, interval=1):
            if self["other_search_for"].existing:
                self["other_search_for"].click()
                break
            self.swipe_up()

    # 是否可以相关搜索词
    def is_find_other_search_for(self):
        for _ in Retry(timeout=15, interval=1):
            if self["other_search_for"].existing:
                self["other_search_for"].click()
                return False
            return True

    def click_see_whats_trending_today(self):
        return self["see_whats_trending_today"].click()

    def get_treading_card_list(self):
        return self["treading_card_list"].items()

    # 点击第一个热榜词
    def click_first_treading(self):
        if len(self.get_treading_card_list()) > 0:
            return self.get_treading_card_list()[0].click()

    # 点击推荐词
    def click_tuijian_word(self):
        return self['tuijian_word'].click()

    def click_see_more(self):
        self["see_more"].click()
        time.sleep(3)

    def click_see_more_user(self):
        self["see_more_user"].click()
        time.sleep(3)

    def click_see_more_music(self):
        self["see_more_music"].click()
        time.sleep(3)

    def click_see_more_hashtags(self):
        self["see_more_hashtags"].click()
        time.sleep(3)

    def wait_for_loading(self):
        if self["search_box"].wait_for_visible(raise_error=True):
            return True
        return False

    def click_avatar(self, name=""):
        for each in self["search_mix_users"].items():
            if each["name"].text == name:
                each["avatar"].click()

    def is_live(self, name=""):
        for each in self["search_mix_users"].items():
            return True if ((each["name"].text == name) and (each["live_label"].text == "LIVE")) else False

    def switch_tab(self, tab=None):
        tabs = ["top_tab", "users_tab", "videos_tab", "hashtags_tab"]
        assert isinstance(tab, str), "tab name must be string"
        assert (tab in tabs), "tab name must in {}".format(tabs)
        self[tab].click()
        time.sleep(1)

    def swipe_up(self, ratio=4):
        """上滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["content"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)


class User(Control):
    def get_locators(self):
        return {
            "avatar": {"type": Control, "path": UPath(id_ == "iv_avatar", visible_ == True)},
            "live_label": {"type": TextView, "path": UPath(id_ == "iv_avatar") / 1},
            "name": {"type": TextView, "path": UPath(id_ == 'tv_username', visible_ == True)},
            "id": {"type": TextView, "path": UPath(id_ == 'tv_aweme_id', visible_ == True)},
            "followers&videos": {"type": TextView, "path": UPath(id_ == 'tv_desc', visible_ == True)}
        }


class Hashtag(Control):
    def get_locators(self):
        return {
            "challenge_name": {"type": TextView, "path": UPath(id_ == "tv_challenge_name", visible_ == True)},
            "participate_count": {"type": TextView, "path": UPath(id_ == "tv_participate_count", visible_ == True)}
        }


class Video(Control):
    def get_locators(self):
        return {
            "cover": {"type": Control, "path": UPath(id_ == "cover")},
            "date": {"type": TextView, "path": UPath(id_ == "create_time_label")},
            "avatar": {"type": ImageView, "path": UPath(id_ == "author_avatar")},
            "author": {"type": TextView, "path": UPath(id_ == "author_name")},
            "like": {"type": TextView, "path": UPath(id_ == "like_count")}
        }


class Sound(Control):
    def get_locators(self):
        return {
            "avatar": {"type": Control, "path": UPath(id_ == "iv_avatar")},
            "title": {"type": TextView, "path": UPath(id_ == "tv_music_title")},
            "author": {"type": TextView, "path": UPath(id_ == "tv_music_author")},
            "music_duration": {"type": TextView, "path": UPath(id_ == "tv_music_duration")},
            "used_count": {"type": TextView, "path": UPath(id_ == "tv_used_count")},
            "tags": {"type": TextView, "path": UPath(id_ == "music_tag")},
        }


class Mix(Control):
    def get_locators(self):
        return {
        }


class UsersList(Control):
    elem_class = User


class VideosList(Control):
    elem_class = Video


class SoundsList(Control):
    elem_class = Sound


class HashtagsList(Control):
    elem_class = Hashtag


class MixList(Control):
    elem_class = Mix


class Search_Mix_User_Root(Mix):
    elem_class = User


class SearchResTopPanel(SearchResultPanel):
    def get_locators(self):
        return {
            "back": {"type": Control, "path": UPath(id_ == 'back_btn_left')},
            "search_box": {"type": Control, "path": UPath(id_ == 'et_search_kw')},
            "clear_input": {"type": Control, "path": UPath(id_ == 'btn_clear')},
            "search_filters": {"type": Control, "path": UPath(id_ == 'search_bottom_sheet_layout')},
            "top_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 0},
            "users_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 1},
            "videos_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 2},
            "sounds_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 3},
            "hashtags_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 4},
            "mix_list": {"type": ScrollView, "path": UPath(id_ == 'list_view', visible_ == True)}
        }


class SearchResUsersPanel(SearchResultPanel):
    def get_locators(self):
        return {
            "back": {"type": Control, "path": UPath(id_ == 'back_btn_left')},
            "search_box": {"type": Control, "path": UPath(id_ == 'et_search_kw')},
            "clear_input": {"type": Control, "path": UPath(id_ == 'btn_clear')},
            "search_filters": {"type": Control, "path": UPath(id_ == 'search_bottom_sheet_layout')},
            "top_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 0},
            "users_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 1},
            "videos_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 2},
            "sounds_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 3},
            "hashtags_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 4},
            "users_list": {'type': UsersList, "path": UPath(id_ == 'list_view', visible_ == True)}
        }

    def is_user_in_res(self, name=None):
        for each in self["users_list"].items():
            if each["name"].text == name:
                return False
        return False

    def is_user_living(self, name=None):
        for each in self["users_list"].items():
            if ((each["name"].text == name) and (each["live_label"].text == "LIVE")):
                return False
        return False

    def click_user_avatar(self, name=None):
        for each in self["users_list"].items():
            if each["name"] == name:
                each["avatar"].click()


class SearchResVideosPanel(SearchResultPanel):
    def get_locators(self):
        return {
            "back": {"type": Control, "path": UPath(id_ == 'back_btn_left')},
            "search_box": {"type": Control, "path": UPath(id_ == 'et_search_kw')},
            "clear_input": {"type": Control, "path": UPath(id_ == 'btn_clear')},
            "search_filters": {"type": Control, "path": UPath(id_ == 'search_bottom_sheet_layout')},
            "top_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 0},
            "users_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 1},
            "videos_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 2},
            "sounds_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 3},
            "hashtags_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 4},
            "videos_list": {'type': UsersList, "path": UPath(id_ == 'list_view', visible_ == True)}
        }

    def get_video_author(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["videos_list"].items(
        )), "index must less than length of videos list on current screen"
        return self["videos_list"].items()[index]["author"].text

    def get_video_likes(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["videos_list"].items(
        )), "index must less than length of videos list on current screen"
        return self["videos_list"].items()[index]["likes"].text

    def get_video_date(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["videos_list"].items(
        )), "index must less than length of videos list on current screen"
        return self["videos_list"].items()[index]["date"].text

    def goto_video_details(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["videos_list"].items(
        )), "index must less than length of videos list on current screen"
        return self["videos_list"].items()[index]["cover"].click()


class SearchResSoundsPanel(SearchResultPanel):
    def get_locators(self):
        return {
            "back": {"type": Control, "path": UPath(id_ == 'back_btn_left')},
            "search_box": {"type": Control, "path": UPath(id_ == 'et_search_kw')},
            "clear_input": {"type": Control, "path": UPath(id_ == 'btn_clear')},
            "search_filters": {"type": Control, "path": UPath(id_ == 'search_bottom_sheet_layout')},
            "top_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 0},
            "users_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 1},
            "videos_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 2},
            "sounds_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 3},
            "hashtags_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 4},
            "videos_list": {'type': UsersList, "path": UPath(id_ == 'list_view', visible_ == True)}
        }

    def get_sound_title(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["sounds_list"].items(
        )), "index must less than length of sounds list on current screen"
        return self["sounds_list"].items()[index]["title"].text

    def get_sound_author(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["sounds_list"].items(
        )), "index must less than length of sounds list on current screen"
        return self["sounds_list"].items()[index]["author"].text

    def get_sound_music_duration(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["sounds_list"].items(
        )), "index must less than length of sounds lis on current screen"
        return self["sounds_list"].items()[index]["music_duration"].text

    def get_sound_used_count(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["sounds_list"].items(
        )), "index must less than length of sounds list on current screen"
        return self["sounds_list"].items()[index]["used_count"].text

    def get_sound_tags(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["sounds_list"].items(
        )), "index must less than length of sounds list on current screen"
        if self["sounds_list"].items()[index]["tags"].wait_for_visible(timeout=5, interval=0.1, raise_error=False):
            return self["sounds_list"].items()[index]["tags"].text
        return None

    def play_sound(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["sounds_list"].items(
        )), "index must less than length of sounds list on current screen"
        return self["sounds_list"].items()[index]["avatar"].click()

    def goto_sound_details(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["sounds_list"].items(
        )), "index must less than length of sounds list on current screen"
        return self["sounds_list"].items()[index]["used_count"].click()


class SearchResHashtagsPanel(SearchResultPanel):
    def get_locators(self):
        return {
            "back": {"type": Control, "path": UPath(id_ == 'back_btn_left')},
            "search_box": {"type": Control, "path": UPath(id_ == 'et_search_kw')},
            "clear_input": {"type": Control, "path": UPath(id_ == 'btn_clear')},
            "search_filters": {"type": Control, "path": UPath(id_ == 'search_bottom_sheet_layout')},
            "top_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 0},
            "users_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 1},
            "videos_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 2},
            "sounds_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 3},
            "hashtags_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 4},
            "hashtags_list": {'type': HashtagsList, "path": UPath(id_ == 'list_view', visible_ == True)},
        }

    def get_challenge_length(self):
        self["hashtags_list"].wait_for_visible(timeout=5, interval=0.1, raise_error=True)
        return len(self["hashtags_list"].items())

    def get_challenge_name(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["hashtags_list"].items(
        )), "index must less than length of hashtags list on current screen"
        return self["hashtags_list"].items()[index]["challenge_name"].text

    def get_challenge_views(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["hashtags_list"].items(
        )), "index must less than length of hashtags list on current screen"
        return self["participate_count"].items()[index]["challenge_name"].text

    def goto_challenge_details(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(self["hashtags_list"].items(
        )), "index must less than length of hashtags list on current screen"
        return self["hashtags_list"].items()[index].click()


class Feed(Control):
    """
    """

    def get_locators(self):
        return {
            "cover": {"type": ImageView, "path": UPath(id_ == "cover")},
            "demo": {"type": ImageView, "path": UPath(id_ == "iv_demo")},
            "tag": {"type": ImageView, "path": UPath(id_ == "iv_tag")},
            "sponsor": {"type": ImageView, "path": UPath(id_ == "iv_sponser")},
        }


class FeedsList(Control):
    elem_class = Feed


class ChallengeDetailsPanel(Window):
    """
    Challenge Details Panel
    """

    window_spec = {
        "activity": "com.ss.android.ugc.aweme.challenge.ui.ChallengeDetailActivity"}

    def get_locators(self):
        return {
            "back": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "share": {"type": Control, "path": UPath(id_ == 'share_btn')},
            "challenge_name": {"type": TextView, "path": UPath(id_ == 'avatar_container') / UPath(id_ == 'title')},
            "views": {"type": Control, "path": UPath(id_ == 'attrs_first')},
            "collect": {"type": Control, "path": UPath(id_ == 'collect_container')},
            "collect_image": {"type": ImageView, "root": "collect", "path": UPath(id_ == 'iv_collect')},
            "collect_text": {"type": TextView, "root": "collect", "path": UPath(id_ == 'tv_collect')},
            "feeds_list": {"type": FeedsList, "path": UPath(id_ == 'feed_list')},
            "start_record": {"type": Control, "path": UPath(id_ == 'start_record')}
        }

    def back(self):
        self["back"].click()

    def share(self):
        self["share"].click()

    def start_record(self):
        self["start_record"].click()

    def get_challenge_name(self):
        return self["challenge_name"].text

    def get_challenge_views(self):
        return self["views"].text

    def click_add_collect_btn(self):
        self["collect"].click()

    def get_add_collect_btn_text(self):
        return self["collect_text"].text

    def goto_video_details(self, index=0):
        assert isinstance(index, int), "index must be a int"
        assert abs(index) <= len(
            self["hashtags_list"].items()), "index must less than length of hashtags list on current screen"
        return self["feeds_list"].items()[index]["cover"].click()
