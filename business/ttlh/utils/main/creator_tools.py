import time
from shoots_android.control import *
from uibase.web import *
from uibase.controls import Control
from uibase.upath import UPath, type_, desc_, text_, class_, lang_
from uibase.web import Webview, WebElement


class CreatorToolsAnalyticsWebview(Webview):
    """A class for the Video Analysis Webview Page Creator Tools"""
    view_spec = {
        "url": "https://www.tiktok.com.*|https://inapp.tiktokv.com/creator-tools/analytics.*" 
    }

    def get_locators(self):
        return {
            "overview": {"type": WebElement, "path":UPath(type_ == "SPAN", text_ == "Overview")},
            "Content": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Content")},
            "followers": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Followers")},
            "total_followers_field": {"type":WebElement, "path": UPath(type_ == "SPAN", text_ == "Total followers")},
            "net_followers_field": {"type":WebElement, "path": UPath(type_ == "SPAN", text_ == "Net followers")},
            "gender_card": {"type":WebElement, "path":UPath(type_ == "SPAN", text_ == "Gender")},
            "age_card": {"type":WebElement, "path":UPath(class_ == "border", text_ == "Age")},
            "top_cities": {"type":Warning, "path":UPath(class_ == "border", text_ == "Top cities")},
            "key_metrics": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Key metrics")},
            "Live": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "LIVE")},
            "video_views": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Video views", 
            class_ == "H4-Bold")},
            "video_views_card": {"type": WebElement, "path": UPath(class_ == "P1-Semibold", 
            class_ == "text-color-ConstTextInverse", text_ == "Video views")},
            "profile_views_card": {"type":WebElement, "path": UPath(text_ == "Profile views", type_ == "SPAN")},
            "likes_card": {"type":WebElement, "path": UPath(text_ == "Likes", type_ == "SPAN")},
            "comments_card": {"type":WebElement, "path": UPath(text_ == "Comments", type_ == "SPAN")},
            "unique_viwers": {"type":WebElement, "path": UPath(text_ == "Unique viewers", type_ == "SPAN")},
            "shares_card": {"type": WebElement, "path": UPath(text_ == "Shares", type_  == "SPAN")},
            "inspiration": {"type": WebElement, "path": UPath(class_ == "H3-Semibold", text_ == "For your inspiration")},
            "inspiration_view_all": {"type": WebElement, "path": UPath(class_ == "H4-Regular",text_ == "View all", 
            visible_ == True)},
            "inspiration_tab_video": {"type": WebElement, "path": UPath(class_ == "mt-24")},

            "key_metrics_area":{"type": WebElement, "path": UPath(class_ == "inline-flex", class_ == "flex-row")},
            "Last_7_days":{"type": WebElement, "path": UPath(class_ == "P1-Regular", text_ == "Last 7 days")},
            "Trending_last7days":{"type": WebElement, "path": UPath(class_ == "P2-Regular", visible_ == True)},
            "Click_video_IMG":{"type": WebElement, "path": UPath(class_ == "h-auto", text_ == "15") / UPath(type_ == "IMG")},
            "video_analysis":{"type": WebElement, "path": UPath(class_ == "truncate", text_ == "Video analysis")}
            
        }
    
class CreatorToolsInspirationFeedWebview(Webview):
    """A class for the Video Analysis Webview Page Creator Tools"""
    view_spec = {
        "url": "https://www.tiktok.com/web-inapp/analytics/inspiration?.*"
    }

    def get_locators(self):
        return {
            "inspiration_title": {"type": WebElement, "path":
                UPath(class_ == "flex-3", text_ == "For your inspiration")},
        }
    
    
    
class CreatorAnalyticsTabPage(Window):
    
    window_spec = {"activity": "com.ss.android.ugc.aweme.*"}
    

    def get_locators(self):
        return {
            "webview": {"type": CreatorToolsAnalyticsWebview, "path": UPath(id_ == "webview_root")},
        }
    
    @property
    def check_overview_tab(self):
        return self["webview"]["overview"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_content_tab(self):
        return self["webview"]["Content"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_followers_tab(self):
        return self["webview"]["followers"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_live_tab(self):
        return self["webview"]["Live"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_key_metrics_tab(self):
        return self["webview"]["key_metrics"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_video_views_tab(self):
        return self["webview"]["video_views"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_inspiration_tab(self):
        return self["webview"]["inspiration"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_video_views_card_tab(self):
        return self["webview"]["video_views_card"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_profile_views_card_tab(self):
        return self["webview"]["profile_views_card"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_likes_card_tab(self):
        return self["webview"]["likes_card"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_comment_card_tab(self):
        return self["webview"]["comments_card"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_uniqueviewers_card_tab(self):
        return self["webview"]["unique_viwers"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_shares_card(self):
        return self["webview"]["shares_card"].wait_for_visible(timeout=10, interval=1, raise_error=True)

    @property
    def check_view_all_inspiration(self):
        return self["webview"]["inspiration_view_all"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_inspiration_tab_video(self):
        return self["webview"]["inspiration_tab_video"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_total_followers_field(self):
        return self["webview"]["total_followers_field"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_net_followers_label_present(self):
        return self["webview"]["net_followers_field"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_gender_card(self):
        return self["webview"]["gender_card"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_age_card(self):
        return self["webview"]["age_card"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    @property
    def check_top_cities_card(self):
        return self["webview"]["top_cities"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    def click_view_all_inspiration(self):
        if self.check_view_all_inspiration:
            self["webview"]["inspiration_view_all"].click()

    def click_view_content(self):
        if self.check_content_tab:
            self["webview"]["Content"].click()

    def click_followers_tab(self):
        if self.check_followers_tab:
            self["webview"]["followers"].click() 

    def check_last_7_days(self):
        return self["webview"]["Last_7_days"].wait_for_visible(timeout=10, interval=1, raise_error=True)
    
    def check_trending_last_7_days_text(self):
        return self["webview"]["Trending_last7days"].wait_for_visible(timeout=10, interval=1, raise_error=True)
   
    def click_video_in_content_tab(self):
        self["webview"]["Click_video_IMG"].click()
    
    def check_in_video_analysis_page(self):
        return self["webview"]["video_analysis"].wait_for_visible(timeout=10, interval=1, raise_error=True)

   
    # Method for swiping 
    def get_webview(self):
        return self['webview'] 
