# # -*- coding:utf-8 _*-
# import time
# import os
#
# from shoots_android.androidapp import AccessibilityApp
# from shoots_android.control import *
# from shoots_android.upath import tag_
# from shoots_lynx.lynx import LynxView, LynxElement
# from uibase.controls import *
# from uibase.upath import *
# from uibase.web import *
# from business.ttlh.utils.app import *
#
# from business.ttlh.utils.main import BasePanel
# from dmttest.musically_local.Regression.Money_Platform.test_utils.mp_test_utils import MPTestUtils
#
# placeholder_ = StringPredicate("placeholder")
# value_ = StringPredicate("value")
#
# PAGE_LOAD_TIMEOUT =  int(os.environ.get("PAGE_LOAD_TIMEOUT")) if os.environ.get("PAGE_LOAD_TIMEOUT") else 50
#
# def input_with_back(control, control_key, input_value):
#     control[control_key].input(input_value)
#     control.app.get_device().press_back()
#     time.sleep(1)
#
# def send_keys_with_back(control, control_key, input_value):
#     control[control_key].click()
#     time.sleep(0.5)
#     control[control_key].send_keys(input_value)
#     time.sleep(0.5)
#     control.app.get_device().press_back()
#     time.sleep(0.5)
#
# class schemaURL:
#     AU_TAX_INFO_URL = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Finapp.tiktokv.com%2Fweb-inapp%2Fincome-wallet%2Ftax-flow%3F__status_bar%3Dtrue%26hide_nav_bar%3D1%26should_full_screen%3D1%26awe_falcon%3Dsh%26business_type%3DTCM_AU'
#     br_tax_info_schema_url = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Finapp.tiktokv.com%2Fweb-inapp%2Ftax%2Fus-w8%3F__status_bar%3Dtrue%26hide_nav_bar%3D1%26should_full_screen%3D1%26awe_falcon%3Dsh%26type%3Dcreate'
#     CA_TAX_INFO_URL = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Ftiktok.com%2Fweb-inapp%2Fincome-wallet%2Ftax-flow%3F__status_bar%3Dtrue%26hide_nav_bar%3D1%26should_full_screen%3D1%26awe_falcon%3Dsh%26business_type%3DTCM_CA'
#     EU_TAX_INFO_URL = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Fwww.tiktok.com%2Fweb-inapp%2Fincome-wallet%2Ftax-flow%3Fbusiness_type%3Dcf%26__status_bar%3Dtrue%26hide_nav_bar%3D1%26should_full_screen%3D1'
#     JP_TAX_INFO_URL = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Finapp.tiktokv.com%2Fweb-inapp%2Fincome-wallet%2Ftax-info-input%3F__status_bar%3Dtrue%26hide_nav_bar%3D1%26should_full_screen%3D1%26awe_falcon%3Dsh%26business_type%3DTCM_JP'
#     KR_TAX_INFO_URL = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Finapp.tiktokv.com%2Fweb-inapp%2Fincome-wallet%2Ftax-flow%3F__status_bar%3Dtrue%26hide_nav_bar%3D1%26should_full_screen%3D1%26awe_falcon%3Dsh%26business_type%3DTCM_KR'
#     tax_info_schema_url = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Finapp.tiktokv.com%2Fweb-inapp%2Fincome-wallet%2Ftax-info-id-input%3F__status_bar%3Dtrue%26should_full_screen%3D1%26awe_falcon%3Dsh%26hide_nav_bar%3D1%26business_type%3DTIKTOK_SHOP_ID%26only_tax%3D1'
#     US_TAX_INFO_SCHEMA_URL = 'snssdk1233://webview?hide_status_bar=0&hide_nav_bar=1&url=https%3A%2F%2Fwww.tiktok.com%2Fweb-inapp%2Fincome-wallet%2Ftax-flow%3Fbusiness_type%3Dcf%26__status_bar%3Dtrue%26hide_nav_bar%3D1%26should_full_screen%3D1'
#
# class PIPOOnboardingFormPanel(BasePanel):
#     window_spec = {
#         "activity": "com.bytedance.hybrid.spark.page.SparkActivity",
#         "process_name": "com.zhiliaoapp.musically"
#     }
#
#     def get_locators(self):
#         return {
#             'WebView': {"type": PIPOOnboardingFormWebView, 'path': UPath(id_ == "activity_container")}
#         }
#
# class PIPOOnboardingFormWebView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/pipo-account-info-input.*"
#     }
#
#     def get_locators(self):
#         return {
#             "transaction_info_page_banner_text": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Transaction account info", index = 0)},
#             "submit_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Submit", index = 0)},
#         }
#
#     def assert_page_loaded(self):
#         time.sleep(10)
#         self["transaction_info_page_banner_text"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=False)
#         self["submit_btn"].wait_for_visible(timeout=30, interval=0.5, raise_error=False)
#
# class TaxFormPanel(BasePanel):
#     window_spec = {
#         "activity": "com.bytedance.hybrid.spark.page.SparkActivity",
#         "process_name": "com.zhiliaoapp.musically"
#     }
#     def get_locators(self):
#         return {
#             'WebView': {"type": TaxFormView, 'path': UPath(type_ == "SparkView")}
#         }
#
# class TaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-info-id-input.*"
#     }
#
#     def get_locators(self):
#         print(PAGE_LOAD_TIMEOUT)
#         return {
#             "legal_name": {"type": WebElement, "path": UPath(placeholder_ == "Legal name") / UPath(type_ == "INPUT")},
#             "billing_address": {"type": WebElement, "path": UPath(placeholder_ == "Billing address") / UPath(type_ == "INPUT")},
#             "email": {"type": WebElement, "path": UPath(placeholder_ == "Email") / UPath(type_ == "INPUT")},
#             "tax_id": {"type": WebElement, "path": UPath(placeholder_ == "Optional") / UPath(type_ == "INPUT")},
#             "continue_btn": {"type": WebElement, "path": UPath(text_ == "Continue", type_ == "BUTTON") / UPath(type_ == "DIV")},
#         }
#
#     def fill_tax_form(self):
#         self["legal_name"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=False)
#         send_keys_with_back(self,"legal_name","[Automation Test] Legal Name")
#         send_keys_with_back(self,"billing_address","[Automation Test] Billing Address")
#         send_keys_with_back(self,"email","<EMAIL>")
#         send_keys_with_back(self,"tax_id","123451234512345")
#
#
#     def assert_tax_form_page(self):
#         time.sleep(5)
#         self["legal_name"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["billing_address"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
#         self["email"].wait_for_existing(timeout=5, interval=0.5, raise_error=True)
#         self["tax_id"].wait_for_existing(timeout=5, interval=0.5, raise_error=True)
#         self["continue_btn"].wait_for_existing(timeout=5, interval=0.5, raise_error=True)
#
#     def submit_tax_form(self):
#         self["continue_btn"].click()
#
# class TaxSubmittedPanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": TaxSubmittedView, 'path': UPath(type_ == "SparkView")}
#         }
#
# class TaxSubmittedView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-complete.*"
#     }
#
#     def get_locators(self):
#         return {
#             "tax_submit_success_lbl": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Tax information saved")},
#             "done_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Done")},
#         }
#
#     def assert_tax_form_submission_success(self):
#         self["tax_submit_success_lbl"].wait_for_existing(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=False)
#         self["done_btn"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
#
# class USTaxTypePanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": USTaxTypeView, 'path': UPath(type_ == "SparkView")}
#         }
#
# class USTaxTypeView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-select*"
#     }
#
#     def get_locators(self):
#         return {
#             "individual_rdo": {"type": WebElement, "path": UPath(class_ == "items-start", text_ == "Individual") / UPath(type_ == "svg")},
#             "entity_rdo": {"type": WebElement, "path": UPath(class_ == "items-start", text_ == "Entity") / UPath(type_ == "svg")},
#             "continue_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Continue")},
#         }
#
#     def choose_tax_type(self):
#         time.sleep(5)
#         self["individual_rdo"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=False)
#         self["individual_rdo"].click()
#
#     def submit_tax_type(self):
#         self["continue_btn"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["continue_btn"].click()
#
#     def assert_us_tax_type_page(self):
#         time.sleep(5)
#         self["individual_rdo"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["entity_rdo"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["continue_btn"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
#
# class USTaxFormPanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": USTaxFormView, 'path': UPath(type_ == "activity_container")}
#         }
#
# class USTaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-info-us-input*"
#     }
#
#     def get_locators(self):
#         return {
#             "first_name_txt": {"type": WebElement, "path": UPath(placeholder_ == "First name") / UPath(type_ == "INPUT")},
#             "last_name_txt": {"type": WebElement, "path": UPath(placeholder_ == "Last name") / UPath(type_ == "INPUT")},
#             "email_txt": {"type": WebElement, "path": UPath(placeholder_ == "Email") / UPath(type_ == "INPUT")},
#             "continue_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Continue")},
#         }
#
#     def fill_tax_form(self):
#         self["first_name_txt"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=False)
#         self["first_name_txt"].input("QA First Name")
#         self.app.get_device().press_back()
#
#         self["last_name_txt"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["last_name_txt"].input("QA Last Name")
#         self.app.get_device().press_back()
#
#         self["email_txt"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["email_txt"].input("<EMAIL>")
#         self.app.get_device().press_back()
#
#     def submit_tax_form(self):
#         self["continue_btn"].click()
#         time.sleep(30)
#
# class W9TaxFormPanel(Window):
#     window_spec = {
#         "activity":  'org.chromium.chrome.browser.ChromeTabbedActivity',
#         "process_name": 'com.android.chrome'
#     }
#
#     def get_locators(self):
#         return {
#             'address_line_1_txt': {'path': UPath(id_ == '60')},
#             'city_txt': {'path': UPath(id_ == '62')},
#             'state_txt': {'path': UPath(id_ == '63')},
#             'postal_code_txt': {'path': UPath(id_ == '64')},
#             'country_ddl': {'path': UPath(type_ == "ListView")},
#             'united_states_lbl': {'path': UPath(text_ == "United States")},
#             'confirm_address_chk': {'path': UPath(id_ == "row_1") / 6 / 1},
#             'us_tin_txt': {'path': UPath(id_ == '66Masked')},
#             'us_tin_chk': {'path': UPath(id_ == "row_2") / 1 / 1},
#             'irs_chk': {'path': UPath(id_ == "row_3") / 1 / 1},
#             'irs_backup_chk': {'path': UPath(id_ == "row_4") / 1 / 1},
#             'next_btn': {'path': UPath(text_ == "Next")},
#         }
#
#     def fill_w9_form(self):
#         self["address_line_1_txt"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=False)
#         self["address_line_1_txt"].input("[QA Automation] Address Line 1")
#         time.sleep(1)
#         self.app.get_device().press_back()
#
#         self["city_txt"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["city_txt"].input("LA")
#         time.sleep(1)
#         self.app.get_device().press_back()
#
#         self["state_txt"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["state_txt"].input("CA")
#         time.sleep(1)
#         self.app.get_device().press_back()
#
#         self["postal_code_txt"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["postal_code_txt"].input("90014")
#         time.sleep(1)
#         self.app.get_device().press_back()
#
#         self["country_ddl"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["country_ddl"].click()
#         time.sleep(1)
#
#         self["united_states_lbl"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["united_states_lbl"].click()
#         time.sleep(1)
#
#         self["confirm_address_chk"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["confirm_address_chk"].click()
#         time.sleep(5)
#
#         self["us_tin_txt"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["us_tin_txt"].input("936909114")
#         time.sleep(1)
#         self.app.get_device().press_back()
#
#         self["us_tin_chk"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["us_tin_chk"].click()
#         time.sleep(5)
#
#         self["irs_chk"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["irs_chk"].click()
#         time.sleep(5)
#
#         self["irs_backup_chk"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["irs_backup_chk"].click()
#         time.sleep(5)
#
#         self["next_btn"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
#         self["next_btn"].click()
#         time.sleep(5)
#
# class BRTaxFormPanel(BasePanel):
#     window_spec = {
#         "activity": "com.bytedance.hybrid.spark.page.SparkActivity",
#         "process_name": "com.zhiliaoapp.musically"
#     }
#
#     def get_locators(self):
#         return {
#             'WebView': {"type": BRTaxFormView, 'path': UPath(type_ == "SparkView")},
#             'TaxSubmittedWebView': {"type": TaxSubmittedView, 'path': UPath(type_ == "SparkView")},
#         }
#
# class BRTaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/tax/us-w8*"
#     }
#
#     def get_locators(self):
#         return {
#             "first_name_txt": {"type": WebElement, "path": UPath(placeholder_ == "As shown on your income tax return", index=0)},
#             "last_name_txt": {"type": WebElement, "path": UPath(placeholder_ == "As shown on your income tax return", index=1)},
#             "email_txt": {"type": WebElement, "path": UPath(placeholder_ == "For receiving tax-related documents") / UPath(type_ == "INPUT")},
#             "continue_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Continue")},
#         }
#
#     def assert_br_tax_form_page(self):
#         self["first_name_txt"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["last_name_txt"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["email_txt"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["continue_btn"].wait_for_existing(timeout=5, interval=0.5, raise_error=True)
#
#     def fill_tax_info(self):
#         self["first_name_txt"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         send_keys_with_back(self, "first_name_txt", MPTestUtils.generate_random_text(12))
#         send_keys_with_back(self, "last_name_txt", MPTestUtils.generate_random_text(12))
#         send_keys_with_back(self,"email_txt","abc{@}xyzak.com")
#         time.sleep(1)
#
#     def submit_tax_info(self):
#         self["continue_btn"].click(offset_x = 50, offset_y = 10)
#         time.sleep(10)
#
# class JPTaxFormPanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": JPTaxFormView, 'path': UPath(type_ == "SparkView")}
#         }
#
# class JPTaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-info-input*"
#     }
#
#     def get_locators(self):
#         return {
#             "continue_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Continue", index = 0)},
#             "legal_name_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your legal name")},
#             "local_address_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your local address")},
#             "my_number_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Optional")},
#             "nickname_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your nickname")},
#             "tax_type_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Tax type")},
#             "tax_type_individual_radio_button": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Individual", index=2) / UPath(type_ == "LABEL") / UPath(type_ == "svg", index=0)},
#             "tax_type_entity_radio_button": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Entity", index=2) / UPath(type_ == "LABEL") / UPath(type_ == "svg", index=0)},
#             "transaction_account_info_header_text": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Transaction account info")},
#         }
#
#     def assert_tax_form_page(self):
#         self["nickname_input"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["legal_name_input"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["local_address_input"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["my_number_input"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["tax_type_input"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["continue_btn"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#
#     def fill_tax_details(self):
#         self["nickname_input"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=False)
#         self["nickname_input"].input(MPTestUtils.generate_random_text(30))
#         self.app.get_device().press_back()
#         time.sleep(1)
#         self["legal_name_input"].input(MPTestUtils.generate_random_text(90))
#         self.app.get_device().press_back()
#         time.sleep(1)
#         self["local_address_input"].input(MPTestUtils.generate_random_text(220,True))
#         self.app.get_device().press_back()
#         time.sleep(1)
#         self["my_number_input"].input(MPTestUtils.generate_random_number(40))
#         self.app.get_device().press_back()
#
#     def fill_tax_details_individual(self):
#         self.fill_tax_details()
#         self["tax_type_input"].click()
#         time.sleep(1)
#         self["tax_type_individual_radio_button"].click()
#         time.sleep(1)
#
#     def fill_tax_details_entity(self):
#         self.fill_tax_details()
#         self["tax_type_input"].click()
#         time.sleep(1)
#         self["tax_type_entity_radio_button"].click()
#         time.sleep(1)
#
#     def submit_tax_form(self):
#         self["continue_btn"].click()
#
# class KRTaxFormPanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": KRTaxFormView, 'path': UPath(type_ == "SparkView")}
#         }
#
# class KRTaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-info-input*"
#     }
#
#     def get_locators(self):
#         return {
#             "continue_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Continue", index = 0)},
#             "legal_name_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your legal name")},
#             "local_address_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your local address")},
#             "vat_status_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Select your VAT status")},
#         }
#
#     def assert_tax_form_page(self):
#         self["legal_name_input"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["local_address_input"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["vat_status_input"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["continue_btn"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#
# class CATaxFormPanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": CATaxFormView, 'path': UPath(type_ == "SparkView")}
#         }
#
# class CATaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-info-input*"
#     }
#
#     def get_locators(self):
#         return {
#             "continue_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Continue", index = 0)},
#             "gst_hst_id_input": {"type": WebElement, "path": UPath(type_ == "INPUT",name_ == "tax_number")},
#             "local_address_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your local address")},
#             "legal_name_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your legal name")},
#             "province_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Select your province")},
#             "province_alberta_option": {"type": WebElement, "path": UPath(class_ == "items-start", text_ == "Alberta") / UPath(type_ == "svg")},
#         }
#
#     def assert_tax_form_page(self):
#         self["legal_name_input"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["local_address_input"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["gst_hst_id_input"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["province_input"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["continue_btn"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#
#     def fill_tax_details(self):
#         self["legal_name_input"].wait_for_visible(timeout=50, interval=0.5, raise_error=False)
#         self["legal_name_input"].input(MPTestUtils.generate_random_text(15))
#         self.app.get_device().press_back()
#         time.sleep(1)
#         self["local_address_input"].input(MPTestUtils.generate_random_text(60))
#         self.app.get_device().press_back()
#         time.sleep(1)
#         self["gst_hst_id_input"].input(MPTestUtils.generate_random_number(9))
#         self.app.get_device().press_back()
#         time.sleep(1)
#         self["province_input"].click()
#         time.sleep(1)
#         self["province_alberta_option"].click()
#         time.sleep(1)
#
#     def submit_tax_form(self):
#         self["continue_btn"].click()
#
# class AUTaxFormPanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": AUTaxFormView, 'path': UPath(type_ == "SparkView")
# }
#         }
#
# class AUTaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-info-input*"
#     }
#
#     def get_locators(self):
#         return {
#             "abn_status_input": {"type": WebElement, "path": UPath(placeholder_ == "Select your ABN status")},
#             "continue_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Continue", index = 0)},
#             "local_address_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your local address")},
#             "legal_name_input": {"type": WebElement, "path": UPath(type_ == "INPUT",placeholder_ == "Enter your legal name")},
#         }
#
#     def assert_tax_form_page(self):
#         self["legal_name_input"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["local_address_input"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["abn_status_input"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#         self["continue_btn"].wait_for_existing(timeout=30, interval=0.5, raise_error=True)
#
# class EUTaxFormPanel(TaxFormPanel):
#     def get_locators(self):
#         return {
#             'WebView': {"type": EUTaxFormView, 'path': UPath(type_ == "SparkView")},
#             'Step1WebView': {"type": EUTaxFormStep1View, 'path': UPath(type_ == "SparkView")},
#             'SelfBillingWebView': {"type": EUTaxFormSelfBillingView, 'path': UPath(type_ == "SparkView")},
#             'TaxSubmittedWebView': {"type": TaxSubmittedView, 'path': UPath(type_ == "SparkView")},
#         }
#
# class EUTaxFormView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/create-account*"
#     }
#
#     def get_locators(self):
#         return {
#             "country_of_residence_text": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "United Kingdom", index = 0)},
#             "currency_text": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "British Pound Sterling", index = 0)},
#             "next_btn": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "Next", index = 0)},
#             "terms_and_conditions_link": {"type": WebElement, "path": UPath(type_ == "A", id_ == "incomeTermsId")},
#             "terms_and_conditions_option_button": {"type": WebElement, "path": UPath(type_ == "FOOTER") / UPath(type_ == "DIV", ~text_ == "By continuing.*", index = 0) / UPath(type_ == "DIV", index = 0) / UPath(type_ == "LABEL") / UPath(type_ == "DIV")},
#         }
#
#     def assert_tax_form_page(self):
#         self["country_of_residence_text"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["currency_text"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["terms_and_conditions_link"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#         self["next_btn"].wait_for_visible(timeout=30, interval=0.5, raise_error=True)
#
#     def accept_terms_and_conditions(self):
#         self["country_of_residence_text"].wait_for_visible(timeout=60, interval=0.5, raise_error=True)
#         self["terms_and_conditions_option_button"].click()
#         time.sleep(2)
#         self["next_btn"].click()
#
# class EUTaxFormStep1View(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/tax-info-step-one/create*"
#     }
#
#     def get_locators(self):
#         return {
#             "vat_registered_option_button": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "I am registered for VAT", index = 0) / UPath(type_ == "DIV", index = 0)  / UPath(type_ == "LABEL") / UPath(type_ == "svg")},
#             "legal_name_input": {"type": WebElement, "path": UPath(type_ == "INPUT", placeholder_ == "Enter your full name")},
#             "billing_address_input": {"type": WebElement, "path": UPath(type_ == "INPUT", placeholder_ == "Enter your billing address")},
#             "postal_code_input": {"type": WebElement, "path": UPath(type_ == "INPUT", placeholder_ == "Enter your postal code")},
#             "vat_number_input": {"type": WebElement, "path": UPath(type_ == "INPUT", placeholder_ == "Enter your VATIN")},
#             "next_btn": {"type": WebElement, "path": UPath(type_ == "BUTTON", text_ == "Next", index = 0)},
#         }
#
#     def submit_tax_info(self):
#         self["vat_registered_option_button"].wait_for_visible(timeout=60, interval=0.5, raise_error=True)
#         self["vat_registered_option_button"].click()
#         time.sleep(1)
#         send_keys_with_back(self, "legal_name_input", MPTestUtils.generate_random_text(12))
#         send_keys_with_back(self, "billing_address_input", MPTestUtils.generate_random_text(25))
#         time.sleep(2)
#         self.scroll(coefficient_y=0.9)
#         time.sleep(2)
#         send_keys_with_back(self, "postal_code_input", "DE55 4PB")
#         send_keys_with_back(self, "vat_number_input", "GB392194868")
#         time.sleep(1)
#         self["next_btn"].click()
#
#
# class EUTaxFormSelfBillingView(Webview):
#     view_spec = {
#         "url": "https://inapp.tiktokv.com/web-inapp/income-wallet/self-billing*"
#     }
#
#     def get_locators(self):
#         return {
#             "accept_button": {"type": WebElement, "path": UPath(type_ == "BUTTON", text_ == "Accept")},
#         }
#
#     def accept_self_billing(self):
#         self["accept_button"].wait_for_visible(timeout=PAGE_LOAD_TIMEOUT, interval=0.5, raise_error=True)
#         self["accept_button"].click()
#         time.sleep(5)