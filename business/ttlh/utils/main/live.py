# -*- coding: utf8 -*-

import json
import time

from shoots_android.control import *
from uibase.upath import tag_

from business.ttlh.utils.main import VideoRecordPanel


from uibase.upath import UPath, class_


class AudienceLivePanel(Window):
    """live window
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LivePlayActivity.*"}

    def get_locators(self):
        return {'gift': {'type': Control, 'path': UPath(id_ == 'gift_icon_image')},
                'send': {'type': Control, 'path': UPath(id_ == 'item_send_btn', visible_ == True)},
                'ranklist_entrance': {'type': Control, 'path': UPath(id_ == 'rank_entrance_text', visible_ == True)},
                'ranklist_anchor': {'type': Control,
                                    'path': UPath(id_ == 'recycler_view', visible_ == True) / 0 / UPath(
                                        id_ == 'user_avatar')},
                'historical_ranklist': {'type': Control, 'path': UPath(id_ == 'last_text', visible_ == True)},
                'tab_switch': {'type': Control, 'path': UPath(text_ == "Rising Stars")},
                'back': {'type': Control, 'path': UPath(id_ == 'btn_back')},
                'FAQ': {'type': Control, 'path': UPath(id_ == 'rank_help', visible_ == True)},
                'ranklist_back': {'type': Control, 'path': UPath(id_ == 'audience_top_empty_view')},

                # gift effect
                'effect_gift': {'path': UPath(text_ == 'fireworks')},
                'special_video_gift_view': {'path': UPath(id_ == 'render_sdk_view')},
                'gift_select_area': {'type': Control,
                                     'path': UPath(id_ == 'ttlive_item_new_gift_bound', visible_ == True)},

                # UI
                'big_gift': {'type': Control, 'path': UPath(text_ == 'Duck', visible_ == True)},
                "small_gift": {'type': Control,
                               'path': UPath(id_ == "new_gift_name", text_ == 'Rose', visible_ == True)},
                'description_gift': {'type': Control, 'path': UPath(text_ == 'Lion', visible_ == True, index=0)},
                'color_gift': {'type': Control, 'path': UPath(text_ == 'Sunglasses', visible_ == True)},
                'quick_gift': {'type': Control, 'path': UPath(id_ == 'toolbar_text', text_ == "Rose")},
                'universe_gift': {'type': Control,
                                  'path': UPath(text_ == 'TikTok Universe', visible_ == True, index=0)},
                'combo': {'type': Control, 'path': UPath(id_ == 'combo_title_1', visible_ == True)},
                'quick_combo': {'type': Control, 'path': UPath(id_ == 'combo_title_1', visible_ == True)},
                'combo_count_view': {'type': Control, 'path': UPath(id_ == 'combo_count_bg', visible_ == True)},
                'combo_count_1': {'type': Control,
                                  'path': UPath(id_ == 'combo_count_tv_1', text_ == "1", visible_ == True)},
                'combo_count_2': {'type': Control,
                                  'path': UPath(id_ == 'combo_count_tv_1', text_ == "2", visible_ == True)},
                'gift_panel': {'type': Control, 'path': UPath(id_ == "gift_panel_list")},

                'gift_container_avatar': {'type': Control,
                                          'path': UPath(id_ == 'user_avatar_iv_new', visible_ == True)},
                "gift_nickname": {'type': Control, 'path': UPath(id_ == 'sender_name', visible_ == True)},
                "gift_name": {'type': Control, 'path': UPath(id_ == 'send_gift_disc', visible_ == True)},
                "gift_picture": {'type': Control, 'path': UPath(id_ == 'gift_icon_iv', visible_ == True)},
                'gift_container_num': {'path': UPath(type_ == 'com.bytedance.android.live.design.widget.LiveTextView',
                                                     id_ == 'comb_count_tv_style_aweme', visible_ == True)},
                'description_label': {
                    'path': UPath(id_ == 'left_icon', type_ == 'androidx.appcompat.widget.AppCompatImageView',
                                  visible_ == True)},
                'description_content': {'path': UPath(id_ == 'gift_description', visible_ == True)},
                'color_area': {'path': UPath(id_ == "color_indicator", visible_ == True)},
                'color_change_index_1': {
                    'path': UPath(id_ == 'text', type_ == 'com.bytedance.android.live.design.widget.LiveTextView',
                                  index=0)},
                'color_change': {
                    'path': UPath(type_ == 'androidx.constraintlayout.widget.ConstraintLayout', id_ == 'background',
                                  index=1)},
                'universe_dynamic_effect': {'path': UPath(id_ == 'gift_dynamic_preview', visible_ == True)},
                'universe_icon': {
                    'path': UPath(id_ == 'left_icon', type_ == 'androidx.appcompat.widget.AppCompatImageView')},
                'universe_description': {'path': UPath(id_ == 'gift_description',
                                                       text_ == "A celebratory message will be displayed with the host's and your username as well as the gift name in all LIVE videos in your region.",
                                                       visible_ == True)},
                'poll_progress_panel': {'path': UPath(id_ == 'live_gift_poll_view')},
                'quick_poll_panel': {'path': UPath(id_ == 'select_poll_items', visible_ == True)},
                'quick_poll_gift_left': {'path': UPath(text_ == 'Thumbs Up')},
                'quick_poll_gift_right': {'path': UPath(text_ == 'Heart', index=0)},
                'send_btn': {'path': UPath(id_ == "gift_price", text_ == "Send for 1 Coin")},
                'universe_gift_send_toast': {'path': UPath(id_ == "notification_text_normal",
                                                           ~text_ == 'testmvczmyamuj sent TikTok Universe to testcpxbavdxse')},
                'gift_guide_send_btn': {
                    'path': UPath(type_ == "android.widget.LinearLayout", id_ == "send_button_comb")},
                'gift_guide_popup': {
                    'path': UPath(id_ == "gift_guide_container") / UPath(id_ == "white_area", visible_ == True)},

                # 'speed_gift': {'path': UPath(type_ == 'com.bytedance.android.livesdk.p.b')/UPath(id_ == 'toolbar_icon')},
                'speed_gift': {'path': UPath(id_ == 'toolbar_text', text_ == 'Rose')},
                "speed_gift_confirm": {'path': UPath(id_ == "giftPriceLayout")},
                # 观众退出直播间-右上角X
                "leave_room": {"path": UPath(id_ == "close_widget_container")},
                # 连麦&PK
                "score_bar": {"path": UPath(id_ == "view_progressbar")},
                "left_score": {"path": UPath(id_ == 'left_text')},
                "right_score": {"path": UPath(id_ == 'right_text')},
                # "left_win_combo": {"path": UPath(id_ == 'layout_left_winning_streak_icon')},
                "left_win_combo": {"path": UPath(id_ == 'layout_winning_streak_icon', index=0)},
                # "right_win_combo": {"path": UPath(id_ == 'layout_right_winning_streak_icon')},
                "right_win_combo": {"path": UPath(id_ == 'layout_winning_streak_icon', index=1)},
                "left_contributor": {"path": UPath(id_ == 'layout_left_container') / UPath(id_ == 'iv_user_stroke')},
                "right_contributor": {"path": UPath(id_ == 'layout_right_container') / UPath(id_ == 'iv_user_stroke')},
                "count_down": {"path": UPath(id_ == "layout_battle_countdown")},

                # 宝箱
                'treasure_box': {'type': Control, 'path': UPath(text_ == 'Treasure Box', visible_ == True)},
                'send_treasure_box': {'type': Control, 'path': UPath(desc_ == "Send", visible_ == True,
                                                                     type_ == "com.lynx.FakeViews.LynxView")},
                'treasure_short_touch': {'type': Control, 'path': UPath(id_ == "short_touch_view_widget_container")},
                'open_treasure_box': {'type': Control, 'path': UPath(desc_ == "Open", visible_ == True,
                                                                     type_ == "com.lynx.FakeViews.FlattenView")},
                "no_internet": {'type': Control, 'path': UPath(visible_ == True, desc_ == "No internet connection")},
                "retry": {'type': Control, 'path': UPath(visible_ == True, desc_ == "Retry")},
                "ok": {'type': Control, 'path': UPath(visible_ == True, ~desc_ == "OK|Select Gift")},

                "win_face": {"path": UPath(id_ == 'iv_left_result')},
                "lose_face": {"path": UPath(id_ == "iv_right_result")},
                "victory_lap": {"path": UPath(id_ == 'layout_battle_punish_countdown', visible_ == True)},
                "time_label": {"path": UPath(id_ == 'tv_pk_punish_countdown_value', visible_ == True)},

                # 用户等级
                "room_enter_content": {"path": UPath(id_ == "cl_grade_content_view", visible_ == True)},
                "room_enter_name": {"path": UPath(id_ == "tv_grade_barrage_name", visible_ == True)},
                "comment_badge": {"path": UPath(id_ == "badge_view_reverse", visible_ == True)},
                "profile_card": {"path": UPath(id_ == "content_layout", visible_ == True)},

                "comment_add": {"path": UPath(id_ == "edit_btn_audience", visible_ == True)},
                "comment_edit": {"path": UPath(id_ == "edit_text")},
                "comment_send": {"path": UPath(id_ == "send_message", visible_ == True)},
                "share_button": {"path": UPath(id_ == "toolbar_icon", visible_ == True)},
                "action_list": {"path": UPath(id_ == "action_list", visible_ == True)},
                "setting": {"path": UPath(text_ == "Settings", visible_ == True)},
                "user_level_title": {"path": UPath(id_ == "user_level_title")},
                "user_level_button": {"path": UPath(id_ == "switch_level", visible_ == True)},
                "gift_icon_image": {"path": UPath(id_ == "gift_icon_image", visible_ == True)},
                "gifts_tab": {"path": UPath(text_ == "Gifts", visible_ == True)},
                "rest_period": {"path": UPath(id_ == "need_to_upgrade")},
                "need_to_upgrade": {"path": UPath(id_ == "need_to_upgrade", visible_ == True)},
                "rewards": {"path": UPath(desc_ == "Rewards", visible_ == True)},
                "head": {"path": UPath(id_ == "head", visible_ == True)},
                "negative_button": {"path": UPath(id_ == "negative_button", visible_ == True)},

                # 送礼引导
                "guide_popup": {
                    "path": UPath(id_ == "gift_guide_container") / UPath(id_ == "white_area", visible_ == True)},
                "guide_popup_close": {"path": UPath(id_ == "cross_close")},
                "guide_popup_badge_avatar": {"path": UPath(id_ == "user_avatar")},
                "guide_popup_badge_title_text": {"path": UPath(id_ == "tvTitle")},
                "guide_popup_badge_content_text": {"path": UPath(id_ == "description_tv")},
                "guide_popup_send_btn": {"path": UPath(id_ == "send_button_comb")},
                "gift_tray": {"path": UPath(id_ == "base_gift_view_new", visible_ == True)},

                # new gifter
                "first_gift_tray": {"path": UPath(id_ == "first_send_gift", visible_ == True)},
                "new_gifter_badge": {"path": UPath(id_ == "badge_view", visible_ == True)},
                "new_gifter_1_avatar": {"path": UPath(id_ == "message_avatar")},
                "new_gifter_badge_on_person_card": {"path": UPath(id_ == "badge_view", visible_ == True)},
                "new_gifter_webview_page": {"path": UPath(type_ == "WebKitView")},

                #first recharge page
                "recharge_btn": {'path': UPath(id_ == "recharge_tv")},
                "first_recharge_package_name": {"path": UPath(text_ == "Gift Pack", id_ == "new_gift_name")},
                "first_recharge_package_icon": {"path": UPath(text_ == "For you", id_ == "new_gift_coin")},
                "first_recharge_package_label": {"path": UPath(id_ == "top_recharge_label")},
                "first_recharge_package_choose_status": {"path": UPath(id_ == "item_send_btn", text_ == "View", visible_ == True)},

                }

    def click_by_name(self, ctrl_name, timeout=60, offset_x=0):
        if self[ctrl_name].wait_for_visible(timeout=timeout):
            self[ctrl_name].click(offset_x=offset_x)
            time.sleep(3)
            return True
        return False

    def click_through_name(self, ctrl_name, timeout=60, offset_x=0):
        if self[ctrl_name].wait_for_existing(timeout=timeout):
            self[ctrl_name].click(offset_x=offset_x)
            time.sleep(3)
            return True
        return False

    def wait_by_name(self, ctrl_name, timeout=60, raise_error=False):
        if self[ctrl_name].wait_for_visible(timeout, raise_error=raise_error):
            return True
        return False

    def swipe_up(self):
        rect = self.app.get_device().screen_rect
        self.scroll(distance_y=rect.height * 0.75)

    def swipe_down(self, coefficient=6):
        self.swipe(y_direction=1, swipe_coefficient=coefficient)

    # 校验大礼物特效
    def check_video_gift_effect(self):
        if self["special_video_gift_view"].wait_for_existing(timeout=10, raise_error=False):
            return True
        return False

    def check_gift_exist_and_choose(self, ctrl_name):
        """
        判断当前面板是否存在指定礼物，若存在再判断是否已经选中
        """
        if self[ctrl_name].wait_for_existing(timeout=3, raise_error=False):
            select_id = self["gift_select_area"]._get_element_ids()
            self[ctrl_name].click()
            if self["gift_select_area"].wait_for_existing(timeout=3, raise_error=False) and self[
                "gift_select_area"]._get_element_ids() != select_id:
                return True
            else:
                return False
        return False

    # 根据礼物名称选中相应礼物(安卓端用该方法即可)
    def gift_choose(self, ctrl_name):
        if self[ctrl_name].wait_for_existing(timeout=3, raise_error=False):
            self[ctrl_name].click()
            return True
        else:
            return False

    # 发送礼物（用于UI校验时），由于需要校验combo动效，因此缩短sleep时长
    def send_gifts(self, ctrl_name="send"):
        if self[ctrl_name].wait_for_existing(timeout=10, raise_error=False):
            self[ctrl_name].click()
            time.sleep(1)

    def send_speed_gift(self):
        self.click_by_name("speed_gift")
        if self["speed_gift_confirm"].wait_for_existing(timeout=60, interval=0.5, raise_error=False):
            self.click_by_name("speed_gift_confirm")
        time.sleep(3)

    def combo_gifts(self, ctrl_name="combo"):
        if self[ctrl_name].wait_for_existing(timeout=10, raise_error=False):
            self[ctrl_name].click()
            time.sleep(1)

    # 校验是否存在combo动效
    def have_combo_effect(self, timeout=10, ctrl_name="combo"):
        return self[ctrl_name].wait_for_existing(timeout=timeout, interval=0.1, raise_error=False)

    # 校验combo数字
    def judge_combo_count(self, timeout=10, combo_num=1):
        """
        combo_num: combo个数
        """
        if combo_num == 1:
            return self["combo_count_view"].wait_for_existing(timeout=timeout, interval=0.1, raise_error=False) and \
                   self["combo_count_1"].wait_for_existing(timeout=timeout, interval=0.1, raise_error=False)
        else:
            return self["combo_count_view"].wait_for_existing(timeout=timeout, interval=0.1, raise_error=False) and \
                   self["combo_count_2"].wait_for_existing(timeout=timeout, interval=0.1, raise_error=False)

    # 礼物托盘元素校验
    def check_gift_container_avatar(self, timeout=10):
        """
        校验礼物托盘的具体组成元素：头像
        """
        return self["gift_container_avatar"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_gift_container_nickname(self, timeout=10):
        """
        校验礼物托盘的具体组成元素：昵称
        返回此时礼物托盘上用户的昵称
        """
        if self["gift_nickname"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False):
            return self["gift_nickname"].text

    def check_gift_container_name(self, timeout=10):
        """
        校验礼物托盘的具体组成元素：礼物名称
        返回 send+礼物名称
        """
        if self["gift_name"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False):
            return self["gift_name"].text

    def check_gift_container_picture(self, timeout=10):
        """
        校验礼物托盘的具体组成元素：礼物图片
        """
        return self["gift_picture"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_gift_container_gift_num(self, timeout=10):
        """
        针对小礼物的校验
        校验礼物托盘的具体组成元素：礼物数量
        """
        if self["gift_container_num"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False):
            return self["gift_container_num"].text

    def check_description_area_label(self, timeout=10):
        """
        校验礼物说明区域，具体校验元素：角标
        """
        return self["description_label"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_description_area_content(self, timeout=10):
        """
        校验礼物说明区域，具体校验元素：文案
        """
        if self["description_content"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False):
            return self["description_content"].text

    def check_universe_gift_dynamic_effect(self, timeout=10):
        """
        校验universe礼物的动态效果
        """
        return self["universe_dynamic_effect"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_universe_icon(self, timeout=10):
        """
        校验universe说明中的label
        """
        return self["universe_icon"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_universe_description(self, timeout=10):
        """
        校验universe说明文案
        """
        return self["universe_description"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_color_area(self, timeout=10):
        """
        校验换色礼物是否存在换色区域
        """
        return self["color_area"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_color_change(self, timeout=10):
        """
        校验换色礼物是否可以成功换色
        当第一种颜色的text控件visible为True，表示目前选中的是第一种颜色；而当其为False，表示颜色已成功切换
        """
        return self["color_change_index_1"].wait_for_visible(timeout=timeout, raise_error=False)

    # 投票进度栏校验
    def check_poll_progress_panel(self, timeout=10):
        return self["poll_progress_panel"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 快捷投票栏校验
    def check_quick_poll_panel(self, timeout=10):
        return self["quick_poll_panel"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_UniverseGift_send_toast(self, timeout=10):
        """
        校验universe_gift送出后的高级消息
        """
        return self["universe_gift_send_toast"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_quick_gift_popup(self, timeout=10):
        """
        校验快捷礼物弹窗是否存在
        """
        return self["send_btn"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def comment(self, content):
        """直播间发送评论
        :param content 内容
        """
        self["comment_add"].click()
        time.sleep(1)
        self["comment_edit"].input(content)
        time.sleep(2)
        if self["comment_send"].wait_for_existing(timeout=3, raise_error=False):
            self["comment_send"].click()
            time.sleep(2)

    def open_settings(self):
        self.click_by_name("share_button")
        action_list = {"action_list": {'type': Control, 'path': UPath(id_ == "action_list", visible_ == True)}}
        self.locators.update(action_list)
        self.action_list.swipe(x_direction=1, swipe_coefficient=5)
        self["setting"].click()


class AnchorLivePanel(Window):
    """Anchor live window
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.live.LiveBroadcastActivity.*|com.ss.android.ugc.aweme.live.GoLiveActivity.*"}

    def get_locators(self):
        return {'ranklist_entrance': {'type': Control, 'path': UPath(id_ == 'rank_entrance_text', visible_ == True)},
                'ranklist_anchor': {'type': Control,
                                    'path': UPath(id_ == 'recycler_view', visible_ == True) / 0 / UPath(
                                        id_ == 'user_avatar')},
                'historical_ranklist': {'type': Control, 'path': UPath(id_ == 'last_text', visible_ == True)},
                'tab_switch': {'type': Control, 'path': UPath(text_ == "Rising Stars")},
                'FAQ': {'type': Control, 'path': UPath(id_ == 'rank_help', visible_ == True)},
                'back': {'type': Control, 'path': UPath(id_ == 'btn_back')},
                'match_accept': {'type': Control, 'path': UPath(id_ == 'bt_accept')},
                'cohost': {'type': Control, 'path': UPath(text_ == 'Co-host', visible_ == True)},
                'invite': {'type': Control,
                           'path': UPath(id_ == 'invite_user_list_view') / 1 / UPath(id_ == 'bt_invite_list_invite')},
                'match': {'type': Control, 'path': UPath(text_ == 'Match', visible_ == True)},

                'close_live': {'type': Control, 'path': UPath(id_ == 'live_close_widget_container', visible_ == True)},
                'close_certain': {'type': Control, 'path': UPath(id_ == 'positive_button', visible_ == True)},
                'more': {'type': Control, 'path': UPath(id_ == "ttlive_toolbar_more", visible_ == True)},
                # 连麦
                "cohost_invite_decline": {"type": Control,
                                          "path": UPath(id_ == "tv_invitee_refuse", visible_ == True)},
                "cohost_invite_withdraw": {"type": Control, "path": UPath(~text_ == "Withdraw.*", visible_ == True)},
                'cohost_accept': {'type': Control, 'path': UPath(text_ == 'Accept', visible_ == True)},
                'cohost_button': {'type': Control,
                                  'path': UPath(id_ == 'fl_cohost_reminding_container', visible_ == True)},
                'cohost_confirm': {'type': Control, 'path': UPath(text_ == 'Continue', visible_ == True)},
                'invite_btn': {'type': Control,
                               'path': UPath(visible_ == True, id_ == 'bt_invite_list_invite', index=0)},  # 邀请第一个在线好友
                "anchor_invitee_list": {"type": AnchorInviteeList,
                                        "path": UPath(id_ == 'invite_user_list_view')},
                "invite_user_list": {'type': Control, 'path': UPath(id_ == "invite_user_list_view", visible_ == True)},
                "anchor_invite_list_outside": {"type": Control, "path": UPath(id_ == "outside", visible_ == True)},
                "linkmic_success": {"path": UPath(id_ == 'layout_link_mic', visible_ == True)},
                "linkmic_1v1": {"path": UPath(id_ == "iv_container_click_view", visible_ == True)},
                "linkmic_1v2_up": {"path": UPath(type_ == "LHG") / 0 / 4},
                "linkmic_1v2_down": {"path": UPath(type_ == "LHG") / 0 / 2},
                # "anchor_screen": {"path": UPath(id_ == "left_side_layout")},
                "anchor_screen": {"path": UPath(id_ == "cl_container_root", visible_ == True, index=0)},
                # "invitee_screen": {"path": UPath(id_ == "right_side")},
                "invitee_screen": {"path": UPath(id_ == "cl_container_root", index=1)},
                # "invitee_profile": {"path": UPath(id_ == "layout_profile")},
                "invitee_profile": {"path": UPath(id_ == "cl_container_root", visible_ == True, index=1) / UPath(
                    id_ == 'tv_anchor_display_id')},
                "match_message": {"path": UPath(id_ == "content", visible_ == True, type_ == "ConstraintLayout")},
                "quick_pair_btn": {"path": UPath(id_ == 'bt_random_match_entrance_content')},
                # invite status: 0: 可邀请 1: 邀请中（点击可以撤销）2：不可用（灰色不可点击）3：MATURE_THEME
                "invite_button_test7512": {
                    "path": UPath(~tag_ == "test7512,state:0|test007512,state:0", visible_ == True)},
                "user_1992678": {"path": UPath(tag_ == "test1992678,state:0", visible_ == True)},
                "user_1992680": {"path": UPath(tag_ == "test1992680,state:0", visible_ == True)},
                "user_1992682": {"path": UPath(tag_ == "test1992682,state:0", visible_ == True)},
                "user_1992680_withdraw": {"path": UPath(tag_ == "test1992680,state:1", visible_ == True)},
                # PK
                # 'match_button': {"path": UPath(id_ == "toolbar_desc", type_ == "AppCompatImageView")},
                'match_button': {"path": UPath(text_ == "Match", visible_ == True)},
                'start_match': {"path": UPath(id_ == "ttlive_text", visible_ == True, text_ == "Start match")},
                'match_accept': {'path': UPath(text_ == "Accept", visible_ == True)},
                'match_confirm': {'type': Control, 'path': UPath(id_ == "btn_try", visible_ == True)},  # 首次发起PK的弹窗处理
                # "score_bar": {"path": UPath(id_ == 'view_profile')},
                "score_bar": {"path": UPath(id_ == 'view_progressbar', visible_ == True)},
                "left_score": {"path": UPath(id_ == 'left_text')},
                "right_score": {"path": UPath(id_ == 'right_text')},
                # "left_win_combo": {"path": UPath(id_ == 'layout_left_winning_streak_icon')},
                "left_win_combo": {"path": UPath(id_ == 'layout_winning_streak_icon', visible_ == True, index=0)},
                # "right_win_combo": {"path": UPath(id_ == 'layout_right_winning_streak_icon')},
                "right_win_combo": {"path": UPath(id_ == 'layout_winning_streak_icon', visible_ == True, index=1)},
                "left_contributor": {
                    "path": UPath(id_ == 'layout_left_container', visible_ == True) / UPath(id_ == 'iv_user_stroke')},
                "right_contributor": {
                    "path": UPath(id_ == 'layout_right_container', visible_ == True) / UPath(id_ == 'iv_user_stroke')},
                "count_down": {"path": UPath(id_ == "layout_battle_countdown", visible_ == True)},
                # PK结束  win lose表情   victory lap    得分
                "win_face": {"path": UPath(id_ == 'iv_right_result')},
                "lose_face": {"path": UPath(id_ == 'iv_left_result')},
                "victory_lap": {"path": UPath(id_ == 'tv_pk_punish_countdown_value', visible_ == True)},
                "time_label": {"path": UPath(id_ == 'tv_pk_countdown_value', visible_ == True)},
                "rematch_btn": {"path": UPath(id_ == 'btn_battle_rematch', visible_ == True)},
                # 宝箱
                "treature_box_bar": {"path": UPath(id_ == 'toolbar_text', text_ == 'Treasure Box')},
                # 宝箱
                'treasure_box': {'type': Control, 'path': UPath(text_ == 'Treasure Box', visible_ == True)},
                'send_treasure_box': {'type': Control, 'path': UPath(desc_ == "Send", visible_ == True,
                                                                     type_ == "com.lynx.FakeViews.LynxView")},
                'treasure_short_touch': {'type': Control, 'path': UPath(id_ == "short_touch_view_widget_container")},
                'open_treasure_box': {'type': Control, 'path': UPath(desc_ == "Open", visible_ == True,
                                                                     type_ == "com.lynx.FakeViews.FlattenView")},
                "no_internet": {'type': Control, 'path': UPath(visible_ == True, desc_ == "No internet connection")},
                "retry": {'type': Control, 'path': UPath(visible_ == True, desc_ == "Retry")},
                "ok": {'type': Control, 'path': UPath(visible_ == True, ~desc_ == "OK|Select Gift")},

                # UI
                'gift_container': {'path': UPath(id_ == 'base_gift_view_new', visible_ == True)},
                'interact_btn': {'path': UPath(id_ == 'toolbar_text', text_ == 'Interact', visible_ == True)},
                'toolbar_more': {'path': UPath(id_ == 'toolbar_text', text_ == 'More', visible_ == True)},
                'poll_btn': {'path': UPath(id_ == 'poll_group', visible_ == True)},
                'gift_tab_choose': {'path': UPath(id_ == 'tab_item_text', text_ == 'Gifts')},
                'poll_start': {'path': UPath(id_ == 'ttlive_text', text_ == 'Start', visible_ == True)},
                'poll_progress_panel': {'path': UPath(id_ == 'live_gift_poll_view')},
                'gift_icon': {'path': UPath(id_ == 'toolbar_title', text_ == 'Gift')},
                'quick_poll_panel': {'path': UPath(id_ == 'select_poll_items', visible_ == True)},
                'quick_poll_panel_left': {'path': UPath(id_ == 'poll_gifts_layout', visible_ == True, index=0)},
                'quick_poll_panel_right': {'path': UPath(id_ == 'poll_gifts_layout', visible_ == True, index=1)},
                'quick_poll_gift_left': {'path': UPath(text_ == 'Thumbs Up')},
                'quick_poll_gift_right': {'path': UPath(text_ == 'Heart', index=0)},
                'progress_text_left_0': {'path': UPath(id_ == 'poll_left_text', text_ == 'x 0 ')},
                'progress_text_left_1': {'path': UPath(id_ == 'poll_left_text', text_ == 'x 1 ')},
                'progress_text_right_0': {'path': UPath(id_ == 'poll_right_text', text_ == 'x 0 ')},
                'progress_text_right_1': {'path': UPath(id_ == 'poll_right_text', text_ == 'x 1 ')},
                "head": {"path": UPath(id_ == "head", visible_ == True)},
                "iv_ceremony_border": {"path": UPath(id_ == "iv_ceremony_border",  visible_ == True)},
                "action_container": {"path": UPath(id_ == "action_container", visible_ == True)},
                "perks": {"path": UPath(desc_ == "Perks", visible_ == True)},
                "top_20_frame": {"path": UPath(desc_ == "Weekly top 10", visible_ == True)},
                "privilege_center_block": {"path": UPath(id_ == "privilege_center_block") / UPath(type_ == "AppCompatImageView")},
                "avatar_border": {"path": UPath(id_ == "avatar_border", visible_ == True)}
                }

    def open_top_avatar(self):
        self.iv_ceremony_border.wait_for_existing()
        self.head.wait_for_existing()
        time.sleep(3)
        self.head.click()

    def get_invitee_list(self):
        return self['anchor_invitee_list'].items()

    def click_accept_btn(self):
        self.click_by_name(ctrl_name='cohost_accept')

    def check_cohost_1v1_success(self, timeout=30):
        return self['linkmic_1v1'].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_cohost_1v2_success(self,timeout=30):
        if self['linkmic_1v2_up'].wait_for_existing(raise_error=False) & self['linkmic_1v2_down'].wait_for_existing(raise_error=False):
            return True
        else:
            return False

    def check_cohost_finish_success(self, timeout=30):
        return self['linkmic_1v1'].wait_for_disappear(timeout=timeout, raise_error=False)

    def click_by_name(self, ctrl_name, timeout=60):
        if self[ctrl_name].wait_for_existing(timeout=timeout):
            self[ctrl_name].click()
            time.sleep(3)

    def swipe_down(self):
        self.swipe(y_direction=1, swipe_coefficient=6)

    def open_privilege_center(self, from_type):
        if from_type == "avatar":
            time.sleep(1)
            self.avatar_border.wait_for_existing()
            self.action_container.wait_for_existing()
            self.action_container.click()
            time.sleep(1)
        elif from_type == "gift_panel":
            self.privilege_center_block.wait_for_existing()
            self.privilege_center_block.click()
        else:
            print("from_type类型错误")

    def check_privilege_center(self):
        self.perks.wait_for_existing()
        self.top_20_frame.wait_for_existing()

    # 主播侧打开礼物面板
    def anchor_open_gift_panel(self):
        self.toolbar_more.click()
        self.gift_icon.wait_for_existing(timeout=5)
        self.gift_icon.click()

    # 点击直播间空白处：关闭profile页面、权益中心页面、礼物面板页面等
    def close_panel(self):
        device_rect = self.app.get_device().screen_rect
        device = self.app.get_device()
        device.click(device_rect.width * 0.6, device_rect.height * 0.08)
        time.sleep(1.5)

    # 打开礼物投票
    def start_gift_poll(self):
        self["interact_btn"].click()
        time.sleep(1)
        self["poll_btn"].click()
        time.sleep(1)
        self["gift_tab_choose"].click()
        time.sleep(1)
        self["poll_start"].click()
        time.sleep(3)

    # 礼物托盘校验
    def check_gift_container(self, timeout=10):
        """
        校验礼物托盘是否存在
        """
        return self["gift_container"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 快捷投票礼物校验
    def check_quick_poll_gift(self, timeout=10):

        return self["quick_poll_panel_left"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False) \
               and self["quick_poll_panel_right"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 快捷投票栏校验
    def check_quick_poll_panel(self, timeout=10):
        return self["quick_poll_panel"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 投票进度栏校验
    def check_poll_progress_panel(self, timeout=10):
        return self["poll_progress_panel"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 打开礼物面板
    def open_gift_panel(self):
        self["more"].click()
        time.sleep(1)
        self["gift_icon"].click()
        time.sleep(2)

    # 校验礼物面板中是否存在两种投票礼物
    def check_poll_gift_in_gift_panel(self, timeout=10):
        return self["quick_poll_gift_left"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False) \
               and self["quick_poll_gift_right"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 校验投票栏进度是否符合预期
    def check_poll_progress(self, poll_type=1, timeout=10):
        """
        poll_type: 代表是第几次投票
        1：代表是投的右边
        2：代表投的是左边
        """
        if poll_type == 1:
            return self["progress_text_left_0"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False) \
                   and self["progress_text_right_1"].wait_for_existing(timeout=timeout, interval=0.5,
                                                                       raise_error=False)
        elif poll_type == 2:
            return self["progress_text_left_1"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False) \
                   and self["progress_text_right_0"].wait_for_existing(timeout=timeout, interval=0.5,
                                                                       raise_error=False)

    def start_live(self, case):
        case.start_step("进入开播预览页")
        case.app.open_page("snssdk1233://openRecord?tab=live&enter_from=direct_shoot")
        video_record_panel = VideoRecordPanel(root=self.app)
        case.wait_for_equal("start window not visible", video_record_panel, "visible", True)
        case.take_screen_shot(case.device)

        case.start_step("GO LIVE")
        video_record_panel.click_by_name("GO_LIVE")
        case.wait_for_equal("my live window not visible", self, "visible", True)
        time.sleep(10)
        case.take_screen_shot(case.device)

    def anchor_close_room(self, case):
        case.start_step("用例结束，关播")
        self.click_by_name('close_live')
        self.click_by_name('close_certain')
        time.sleep(5)
        case.take_screen_shot(case.device)

    def anchor_close_room_for_app(self, case, app):
        case.start_step("device {}: 用例结束，关播".format(app.serial_no))
        if self["close_live"].wait_for_existing(30, 0.5, False):
            self["close_live"].click()
            self["close_certain"].wait_for_existing(30, 0.5, True)
            self["close_certain"].click()
            time.sleep(3)
        case.take_screen_shot(app.device)


# 连麦主播信息
class AnchorInvitee(Control):
    def get_locators(self):
        return {
            "invite_button": {"path": UPath(id_ == 'bt_invite_list_invite')},  # 邀请连麦按钮
            "avatar": {"path": UPath(id_ == 'iv_invite_list_user_avatar')},  # 主播头像
            "live_status": {"path": UPath(id_ == 'iv_invite_list_living')},  # 开播状态
            "anchor_name": {"path": UPath(id_ == 'hlp')}  # 主播名字
        }

    def invite(self):
        self.invite_button.click()

    def get_invitee_name(self):
        return self.anchor_name.text


# 连麦主播信息列表
class AnchorInviteeList(Control):
    elem_class = AnchorInvitee
    elem_path = UPath(id_ == 'layout_invite_list_user_v2')

    def check_under_age_entrance(self):
        return not self.ranklist_entrance.wait_for_existing(raise_error=False)


class AnchorAcceptCohostPanel(Window):
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.live.LivePlayActivity#1|com.ss.android.ugc.aweme.live.LivePlayActivity#2"}

    def get_locators(self):
        return {
            'cohost_accept': {'type': Control, 'path': UPath(text_ == 'Accept', visible_ == True)},
        }

    def click_by_name(self, ctrl_name, timeout=20):
        if self[ctrl_name].wait_for_existing(timeout=timeout):
            time.sleep(3)
            self[ctrl_name].click()


class AudienceRankListWindow(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LivePlayActivity|com.ss.android.ugc.aweme.live.GoLiveActivity.*"}

    def get_locators(self):
        return {
            # 榜单入口
            "entrance": {"path": UPath(id_ == "rank_entrance_text_and_icon", visible_ == True)},
            "entrance_text": {"path": UPath(id_ == "rank_entrance_text", visible_ == True)},
            "weekly_title": {"path": UPath(id_ == "text1", text_ == "Weekly Ranking", visible_ == True)},
            "stars_title": {"path": UPath(id_ == "text1", text_ == "Rising Stars", visible_ == True)},
            "daily_title": {"path": UPath(id_ == "text1", text_ == "Daily Ranking", visible_ == True)},
            "top3_items": {"type": RankListTopThreeItems, "path": UPath(id_ == "recycler_view", visible_ == True) / UPath(type_ == "LinearLayout", index=0)},
            "top1_live_item": {"path": UPath(id_ == "ttlive_rank_1", visible_ == True) / UPath(id_ == "iv_avatar_border", visible_ == True)},
            "top2_live_item": {"path": UPath(id_ == "ttlive_rank_2", visible_ == True) / UPath(id_ == "iv_avatar_border", visible_ == True)},
            "top3_live_item": {"path": UPath(id_ == "ttlive_rank_3", visible_ == True) / UPath(id_ == "iv_avatar_border", visible_ == True)},
            "rank_items": {"type": RankListItems, "path": UPath(id_ == "recycler_view", visible_ == True)},
            "top20_text": {"path": UPath(id_ == "recommend_text")},
            "audience_entrance": {"path": UPath(id_ == "online_rank_info_layout", visible_ == True) / UPath(type_ == "ConstraintLayout", visible_ == True)},
            "audience_num": {"path": UPath(id_ == "tv_online_audience_num", visible_ == True)},
            "close_widget_container": {"path": UPath(id_ == "close_widget_container", visible_ == True)},

            # 榜单元素
            "countdown_text": {"path": UPath(id_ == "tv_countdown_text", visible_ == True)},
            "countdown": {"path": UPath(id_ == "tv_countdown", visible_ == True)},
            "FAQ": {"path": UPath(id_ == "rank_help", visible_ == True)},

            # 吸底bar
            "bar_num": {"path": UPath(tag_ == "CurrentBottomBar", visible_ == True)},
            "bar_nickname": {"path": UPath(id_ == "name", visible_ == True)},
            "bar_img": {"path": UPath(id_ == "user_avatar", visible_ == True)},
            "bar_text": {"path": UPath(id_ == "tv_gap_description", visible_ == True)},
            "bar_text2": {"path": UPath(id_ == "tv_not_in_rookie_rank", visible_ == True)},
            "audience_rank_bar": {"path": UPath(type_ == "SparkView", visible_ == True) / 0 / 0 / 2 / 2 / 0},
            "bar_gift": {"path": UPath(id_ == "btn_send_gift", visible_ == True)},
            "bar_diamonds": {"path": UPath(id_ == "rl_info_container", visible_ == True) / UPath(id_ == "ticket_count", visible_ == True)},
            "bar_gift_lynx": {"path": UPath(desc_ == "Gift", type_ == "com.lynx.FakeViews.FlattenView", visible_ == True)},
            "bar_num_lynx_No1": {"path": UPath(class_ == "H4-SemiBold", visible_ == True)},
            "bar_text_lynx": {"path": UPath(desc_ == "Send a Gift and have a chance to get a badge and be shown at the top of the LIVE", visible_ == True)},
            "bar_text_lynx2": {"path": UPath(desc_ == "Send a Gift to have a chance to get a top gifter badge.", visible_ == True)},

            # last week
            "last_week": {"path": UPath(id_ == "last_text", visible_ == True)},
            "last_week_title": {"path": UPath(id_ == "single_title", visible_ == True, text_ == "Last Week’s Ranking")},
            "last_week_subtitle": {"path": UPath(class_ == "count_down_container", visible_ == True) / UPath(id_ == "tv_countdown_text", visible_ == True)},

            # online user
            "online_entrance": {"path": UPath(id_ == "online_rank_info_layout",  visible_ == True)},
            "online_num": {"path": UPath(id_ == "tv_online_audience_num")},
            "online_FAQ": {"path": UPath(id_ == "engine_container") / UPath(type_ == "SparkView") / 0 / 0 / 0 / UPath(type_ == "b")},
            "online_bar_gift": {"path": UPath(desc_ == "Gift", visible_ == True)},

            # houlrly
            "hourly_title": {"path": UPath(id_ == "single_title")},
            "hourly_FAQ_panel": {"path": UPath(id_ == "web_view_container") / UPath(type_ == "RoundRectWebView")},

            # common element
            "feed": {"path": UPath(id_ == "live_video_container", visible_ == True)},

            "send_gift_button": {"path": UPath(id_ == "btn_send_gift", visible_ == True)/UPath(id_ == "ttlive_text", text_ == "Gift")},
            "gift_panel": {"path": UPath(id_ == "gift_panel_list")},
            "rank_panel": {"path": UPath(id_ == "recycler_view", visible_ == True)},
            "FAQ_panel": {"path": UPath(id_ == "container_coordinator")},
            "user_card": {"path": UPath(id_ == "living_view", visible_ == True)},
            "room_name": {"path": UPath(id_ == "name_layout") / UPath(id_ == "user_base") / UPath(id_ == "user_name", visible_ == True)},

            "ticket_count": {"path": UPath(id_ == "ticket_count", visible_ == True)},
            "gift_entrance": {"path": UPath(type_ == "LiveGiftIconView", visible_ == True) / UPath(type_ == "LinearLayout", visible_ == True)},
            "gift_guide_container": {"path": UPath(id_ == "gift_guide_container", visible_ == True) / UPath(id_ == "white_area", visible_ == True)},
            "gift_button_comb": {"path": UPath(id_ == "send_button_comb", visible_ == True)},
            "top1_badge": {"path": UPath(id_ == "engine_container", visible_ == True) / 0 / UPath(type_ == "FrameLayout", visible_ == True) / 64 / UPath(desc_ == "No. 1", visible_ == True)},
            "rank_No1_2": {"path": UPath(id_ == "ttlive_rank_1", visible_ == True)},
            "rank_No1_living": {"path": UPath(id_ == "ttlive_rank_1", visible_ == True) /UPath(id_ == "iv_on_going", visible_ == True)},
            "rank_No1": {"path": UPath(id_ == "ttlive_rank_1", visible_ == True)},
            # 个人资料卡的"Follow"按钮
            "follow_button":{"path": UPath(id_ == "ttlive_text", visible_ == True, ~text_ == "Follow.*")},
            "avatar":{"path": UPath(id_ == "avatar", visible_ == True)},
            "nick_name":{"path": UPath(id_ == "nick_name", visible_ == True)},
            "at":{"path": UPath(id_ == "at", visible_ == True)},
            "action_container":{"path": UPath(id_ == "action_container", visible_ == True)/UPath(id_ == "action_icon", visible_ == True)},
            "bottom_bar_rank_num": {"path": UPath(id_ == "rank_num_container") / UPath(id_ == "rank_num", visible_ == True)},
            "gift_panel_list": {"path": UPath(id_ == "gift_panel_list", visible_ == True)}
        }


    def close_panel(self):
        time.sleep(1)
        device_rect = self.app.get_device().screen_rect
        device = self.app.get_device()
        device.click(device_rect.width * 0.6, device_rect.height * 0.08)
        time.sleep(1)

    def switch_weekly_tab(self):
        self.weekly_title.click()
        time.sleep(3)

    def switch_stars_tab(self):
        self.stars_title.click()
        time.sleep(3)

    def switch_last_week(self):
        self.last_week.click()
        time.sleep(3)

    def open_rank_panel(self, rank_type: str = None, retry=20):
        entrance_text = ""
        if rank_type == "weekly":
            for i in range(retry):
                entrance_text = self.entrance_text.text

                if "Weekly" in entrance_text:
                    self.entrance.click()
                    ret = self.weekly_title.wait_for_existing(raise_error=False)
                    if ret:
                        self.switch_weekly_tab()
                        break
                else:
                    self.next_room()
            time.sleep(3)
        elif rank_type == "last":
            for i in range(retry):
                self.entrance_text.wait_for_existing()
                entrance_text = self.entrance_text.text
                if "Weekly" in entrance_text:
                    self.entrance.click()
                    ret1 = self.weekly_title.wait_for_existing(timeout=5, raise_error=False)
                    if ret1:
                        self.switch_weekly_tab()
                        self.last_week.click()
                        break
                elif "Rising" in entrance_text:
                    self.entrance.click()
                    ret2 = self.weekly_title.wait_for_existing(timeout=5, raise_error=False)
                    if ret2:
                        self.switch_weekly_tab()
                        self.last_week.click()
                        break
                else:
                    self.next_room()
            time.sleep(3)

        elif rank_type == "hourly":
            for i in range(retry):
                entrance_text = self.entrance_text.text
                if "Hourly" in entrance_text:
                    self.entrance.wait_for_existing()
                    self.entrance.click()
                    break
                else:
                    self.next_room()
            time.sleep(3)
        elif rank_type == "stars":
            for i in range(retry):
                entrance_text = self.entrance_text.text
                if "Stars" in entrance_text or "Daily" in entrance_text or "Weekly" in entrance_text:
                    self.entrance.click()
                    self.switch_stars_tab()
                    break
                else:
                    self.next_room()
            time.sleep(3)
        elif rank_type == "online":
            self.online_entrance.click()
            time.sleep(3)

    def top1_is_living(self):
        return self.rank_No1_living.wait_for_existing(timeout=6, raise_error=False)

    def click_not_living_top1(self):
        self.rank_No1.wait_for_existing(timeout=6, raise_error=False)
        self.rank_No1.click()

    def check_single_profile(self):
        a = self.follow_button.wait_for_existing(timeout=8, raise_error=False)
        b = self.avatar.wait_for_existing(timeout=3, raise_error=False)
        c = self.nick_name.wait_for_existing(timeout=3, raise_error=False)
        d = self.at.wait_for_existing(timeout=3, raise_error=False)
        e = self.action_container.wait_for_existing(timeout=3, raise_error=False)
        return a and b and c and d and e


    def open_FAQ_panel(self, rank_type: str = None):

        if rank_type == "online":
            self.online_FAQ.click()
            time.sleep(10)

        if rank_type in ("weekly", "hourly", "stars"):
            self.FAQ.click()
            time.sleep(10)

    def open_gift_panel(self, rank_type: str = None):
        if rank_type == "online":
            self.online_bar_gift.click()
            time.sleep(3)
        if rank_type in ("weekly", "hourly", "stars"):
            self.bar_gift.click()
            time.sleep(3)

    def ranklist_send_gift(self):
        self.send_gift_button.wait_for_existing()
        self.send_gift_button.click()
        time.sleep(3)

    def send_first_gift(self, times: int = 1):
        for i in range(times):
            first_gift = self.gift_panel_list.items()[0]
            first_gift.click()
            time.sleep(0.5)

    def get_live_on_status(self, num: int = 0):
        live_on_status = False
        list_items = self.rank_items.items()
        list_num = len(list_items)

        if num == 0:
            top3_items = self.top3_items.items()
            top3_num = len(top3_items)
            print("开始校验top3的直播状态")
            for top3 in range(top3_num):
                print("第{}次校验".format(top3))

                if top3_items[top3].top3_on_status.wait_for_existing(raise_error=False):
                    live_on_status = True
                    print("已找到top3内的直播状态")
                    return live_on_status
                else:
                    print(str(top3_items[top3].top3_on_status.items()))
                    print("top3 未找到直播状态")

        for item in range(list_num):
            print("开始校验列表内的直播状态")
            print("第{}次校验".format(item))
            if list_items[item].list_on_status.wait_for_existing(raise_error=False):
                live_on_status = True
                print("已找到列表内的直播状态")
                return live_on_status
            else:
                print("top3 未找到直播状态")

        return live_on_status

    def get_live_off_status(self, num: int = 0):
        live_off_status = False
        list_items = self.rank_items.items()
        list_num = len(list_items)

        if num == 0:
            top3_items = self.top3_items.items()
            top3_num = len(top3_items)
            print("开始校验top3的非直播状态")
            for top3 in range(top3_num):
                print("第{}次校验".format(top3))
                if top3_items[top3].top3_off_status.wait_for_existing(raise_error=False):
                    live_off_status = True
                    return [live_off_status, "榜单中存在未开播用户:" + str(top3_items[top3])]
                else:
                    print("top3 未找到非直播状态")

        print("开始校验列表内的非直播状态")
        for item in range(list_num):
            print("第{}次校验".format(item))
            if list_items[item].list_off_status.wait_for_existing(raise_error=False):
                live_off_status = True
                return [live_off_status, "榜单中存在未开播用户:" + str(list_items[item])]
            else:
                print("列表内 未找到非直播状态")

        return [live_off_status]

    def scroll_rank_list(self, num: int = 1, item_num: int = 1):
        items = self.rank_items.items()
        items_rect = items[len(items) - 1].rect

        for i in range(num):
            self.rank_panel.scroll(distance_x=0, distance_y=items_rect.height * item_num)
            time.sleep(3)

    def check_live_status(self):
        live_off_status = False
        live_on_status = False
        log_msg = "榜单中未检查出存在未开播状态用户"

        if not live_on_status:
            print("开始校验直播中状态")
            if self.get_live_on_status():
                live_on_status = True

        # if not live_off_status:
        #     print("开始校验未直播状态")
        #     check_result = self.get_live_off_status()
        #     if check_result[0]:
        #         log_msg = check_result[1]
        #         return [live_off_status, log_msg]

        return [live_on_status and not live_off_status, log_msg]

    def check_rank_list_user(self, rank_type: str = None):
        live_on_status = False
        live_off_status = False
        room_name = self.room_name.text
        items = self.rank_items.items()
        items_rect = items[0].rect
        top = items_rect.top
        left = items_rect.left

        if rank_type in ("weekly", "last", "stars"):
            live_on_status = False
            live_off_status = False
        if rank_type == "hourly":
            live_on_status = False
            live_off_status = True

        for i in range(90):
            self.app.get_device().click(x=left, y=top)
            time.sleep(2)
            room_name2 = self.room_name.text

            if not live_on_status:
                # 通过直播间返回按钮和直播间标题不同判断是否是直播中的用户
                if self["user_card"].wait_for_existing(raise_error=False):
                    self.close_panel()

                if room_name != room_name2:
                    print("直播间校验成功")
                    live_on_status = True
                    self.open_rank_panel(rank_type=rank_type)
                    time.sleep(3)
                    for num in range(i):
                        self["rank_panel"].scroll(distance_x=0,
                                                  distance_y=items_rect.height + items_rect.height * 0.15)
                        time.sleep(3)

            if not live_off_status:
                # 通过调起的用户卡和当前房间的名称判断是否是未开播用户
                if self["user_card"].wait_for_existing(raise_error=False):
                    print("用户卡校验成功")
                    live_off_status = True
                    self.close_panel()

            if live_on_status and live_off_status:
                break
            else:
                self["rank_panel"].scroll(distance_x=0, distance_y=items_rect.height + items_rect.height * 0.15)

        return live_on_status and live_off_status

    def enter_top3_room(self, non_rank: int = 0):
        if non_rank != 1:
            if self.top1_live_item.wait_for_existing(timeout=4, raise_error=False):
                self.top1_live_item.click()
        elif non_rank != 2:
            if self.top2_live_item.wait_for_existing(timeout=4, raise_error=False):
                self.top2_live_item.click()
        elif non_rank != 3:
            if self.top3_live_item.wait_for_existing(timeout=4, raise_error=False):
                self.top3_live_item.click()

    def check_live_on_user(self):
        live_on_status = False
        room_name = self.room_name.text
        top3_items = self.top3_items.items()
        top3_num = len(top3_items)

        print("开始寻找top3内的开播状态主播")
        for top3 in range(top3_num):
            print("第{}次判断".format(top3))
            if top3_items[top3].top3_on_status.wait_for_existing(raise_error=False):
                top3_items[top3].click()
                time.sleep(3)
                check_name = self.room_name.text

                if check_name != room_name:
                    live_on_status = True
                    return live_on_status
            else:
                print("top3内未找到直播状态主播")

        print("开始在列表内寻找开播状态主播")
        if not live_on_status:
            for i in range(20):
                list_items = self.rank_items.items()
                list_num = len(list_items)

                for items in range(list_num):
                    if list_items[items].list_on_status.wait_for_existing(raise_error=False):
                        list_items[items].click()
                        time.sleep(3)
                        check_name = self.room_name.text

                        if check_name != room_name:
                            live_on_status = True
                            return live_on_status

                self.scroll_rank_list(num=1, item_num=5)

        return live_on_status

    def check_live_off_user(self):
        live_off_status = False
        top3_items = self.top3_items.items()
        top3_num = len(top3_items)
        print("开始寻找top3内的未开播状态的主播")
        for top3 in range(top3_num):
            print("第{}次判断".format(top3))
            if top3_items[top3].top3_off_status.wait_for_existing(raise_error=False):
                top3_items[top3].click()
                time.sleep(3)
                live_off_status = True
                return live_off_status
            else:
                print("top3内未找到非直播主播")

        print("开始在列表内寻找未开播状态主播")
        if not live_off_status:
            for i in range(20):
                list_items = self.rank_items.items()
                list_num = len(list_items)

                for items in range(list_num):
                    if list_items[items].list_off_status.wait_for_existing(raise_error=False):
                        list_items[items].click()
                        time.sleep(3)
                        live_off_status = True
                        return live_off_status

            self.scroll_rank_list(num=1, item_num=5)

        return live_off_status

    def check_top20_text(self):
        status = False

        for i in range(25):

            print("执行第 {} 次".format(i))

            if self.top20_text.wait_for_existing(raise_error=False) and "Top 20 hosts will" in self.top20_text.text:
                status = True
                return status

            self.scroll_rank_list(num=1, item_num=3)
            time.sleep(3)

        return status

    def next_room(self):
        self.feed.scroll(distance_x=0, distance_y=700)
        time.sleep(3)

    def check_entrance(self, rank_type: str = None):
        if rank_type in ("weekly", "last", "stars", "hourly"):
            return self.entrance.wait_for_existing(raise_error=False)

    def check_entrance_text(self, rank_type: str = None):
        text_status = False
        ranking_text = self["entrance_text"].text

        print(ranking_text)

        if rank_type in ("weekly", "stars", "last"):

            if "Daily Ranking" in ranking_text:
                print("日榜排名99+文案校验通过：" + ranking_text)
                text_status = True

            if "Daily No." in ranking_text:
                print("日榜排名99内文案校验通过：" + ranking_text)
                text_status = True

            if "Weekly Ranking" in ranking_text:
                print("周榜排名99+文案校验通过：" + ranking_text)
                text_status = True

            if "Weekly No." in ranking_text:
                print("周榜排名99内文案校验通过：" + ranking_text)
                text_status = True

            if "Rising Star No." in ranking_text:
                print("新星榜文案校验通过：" + ranking_text)
                text_status = True

            if "Rising Stars" in ranking_text:
                print("新星榜文案校验通过：" + ranking_text)
                text_status = True

        if rank_type == "hourly":

            if "Hourly" in ranking_text:
                text_status = True
                print("小时榜排名99内文案校验通过：" + ranking_text)

        return text_status

    def check_bar_text(self, rank_type: str = None):
        bar_text = ""
        rank_num = ""
        check_text = ""
        status = False

        if rank_type in ("weekly", "hourly"):
            rank_num = self["bar_num"].text
            bar_text = self["bar_text"].text
            print(bar_text)
            print(rank_num)

        if rank_type == "stars":
            rank_num = self["bar_num"].text
            print(rank_num)

            if self.bar_text2.wait_for_existing(raise_error=False):
                check_text = "This ranking shows the hosts who received fewer than"
                bar_text = self["bar_text2"].text
                print(bar_text)
            else:
                bar_text = self["bar_text"].text
                print(bar_text)

        if rank_num == "-":
            if rank_type == "stars":
                if check_text in bar_text:
                    status = True
            if rank_type == "weekly":
                if "Ranking turned off by host" or "Ranking off" in bar_text:
                    status = True
        elif rank_num == "99+":
            if rank_type == "weekly":
                if "Diamonds to reach 99" in bar_text:
                    status = True
                    print("当前主播排名99+,文案校验通过：", bar_text)
            if rank_type == "hourly":
                if "Diamonds needed to be an hourly top host" in bar_text:
                    status = True
                    print("当前主播排名99+,小时榜文案校验通过：", bar_text)
            if rank_type == "stars":
                if "Diamonds to reach 99" in bar_text or check_text in bar_text:
                    status = True
                    print("当前主播排名99+,文案校验通过：", bar_text)

        elif int(rank_num) in range(2, 99 + 1):
            if "Diamonds to reach" in bar_text:
                status = True
                print("当前主播排名2~99,文案校验通过：", bar_text)

        # elif int(rank_num) in range(4, 10 + 1):
        #     if "Diamonds needed to be a top 3" in bar_text:
        #         status = True
        #         print("当前主播排名4~10,文案校验通过：", bar_text)

        # elif int(rank_num) == 2:
        #     if "Diamonds to be top 1" in bar_text:
        #         status = True
        #         print("当前主播排名2,文案校验通过：", bar_text)

        elif int(rank_num) == 1:
            if "Diamonds ahead of No. 2" in bar_text:
                status = True
                print("当前主播排名1,文案校验通过：", bar_text)
        else:
            status = False

        return status

    # def get_online_cion_num(self):
    #     number = self.online_coins_num.text
    #
    #     if number is not None:
    #         print("送礼:" + number)
    #
    #     time.sleep(3)

    # def get_online_user_num(self):
    #     time.sleep(3)
    #     print("观众数量:" + self.online_user_num.text)

    def check_countdown_text(self):
        countdown_text = self.countdown_text.text
        status = False
        if countdown_text == "Refresh in":
            status = True

        return status

    def check_countdown(self):
        return self.countdown.wait_for_existing(raise_error=False)

    def check_FAQ(self):
        return self.FAQ.wait_for_existing(raise_error=False)

    def check_last_week(self):
        return self.last_week.wait_for_existing(raise_error=False)

    def check_weekly_tab(self):
        weekly_title = self.weekly_title.text
        status = False
        if weekly_title == "Weekly Ranking":
            status = True
        return status

    def check_stars_tab(self):
        stars_title = self.stars_title.text
        status = False
        if stars_title == "Rising Stars":
            status = True
        return status

    def check_gift_panel(self):
        return self.gift_panel.wait_for_existing(raise_error=False)

    def check_FAQ_panel(self):
        return self.FAQ_panel.wait_for_existing(raise_error=False)

    def check_last_week_title(self):
        return self.last_week_title.wait_for_existing(raise_error=False)

    def check_last_week_subtitle(self):
        self.last_week_subtitle.wait_for_existing(raise_error=False)
        last_title = self.last_week_subtitle.text
        return "Top 99 LIVE creators in the last week" in last_title

    def check_bar_num(self, rank_type: str = None):
        if rank_type in ("weekly", "hourly", "last", "stars"):
            return self["bar_num"].wait_for_existing(raise_error=False)

    def check_bar_img(self, rank_type: str = None):
        if rank_type in ("weekly", "hourly", "last", "stars"):
            return self["bar_img"].wait_for_existing(raise_error=False)

    def check_bar_nickname(self, rank_type: str = None):
        if rank_type in ("weekly", "hourly", "last", "stars"):
            print(self["bar_nickname"].text)
            return self["bar_nickname"].wait_for_existing(raise_error=False)

    def check_bar_diamonds(self):
        diamonds = self.bar_diamonds.text
        return diamonds is not None

    def check_bar_gift(self, rank_type: str = None):
        if rank_type == "stars":
            rank_num = self["stars_bar_num"].text
            if rank_num == "-":
                pass
            else:
                return self.bar_gift.wait_for_existing(raise_error=False)
        else:
            return self.bar_gift.wait_for_existing(raise_error=False)

    def check_hourly_title(self):
        hourly_title = self.hourly_title.text
        status = False
        if hourly_title == "Hourly Ranking":
            status = True
        return status

    def check_online_num(self):
        self.online_num.wait_for_existing()
        user_num = self.online_num.text
        status = False
        if user_num is not None:
            status = True
        return status

    def check_under_age_entrance(self):
        return not self.entrance.wait_for_existing(raise_error=False)

    # def check_top3_function(self):
    #     top3 = self.top3_items.items()
    #
    #     for i in range(len(top3)):
    #         if top3[i].top3_on_status.wait_for_existing(raise_error=False):
    #             print("找到")
    #     for i in range(len(top3)):
    #         if top3[i].top3_off_status.wait_for_existing(raise_error=False):
    #             print("没找到")


class AnchorRankListWindow(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LiveBroadcastActivity|com.ss.android.ugc.aweme.live.GoLiveActivity.*"}

    def get_locators(self):
        return {
            # 榜单入口
            "entrance": {"path": UPath(id_ == "rank_entrance_text_and_icon", visible_ == True)},
            "entrance_text": {"path": UPath(id_ == "rank_entrance_text", visible_ == True)},
            "weekly_title": {"path": UPath(id_ == "text1", text_ == "Weekly Ranking", visible_ == True)},
            "stars_title": {"path": UPath(text_ == "Rising Stars", visible_ == True)},
            "daily_title": {"path": UPath(text_ == "Daily Ranking", visible_ == True)},
            "top3_items": {"type": RankListTopThreeItems, "path": UPath(id_ == "recycler_view", visible_ == True) / UPath(type_ == "LinearLayout", index=0)},
            "rank_items": {"type": RankListItems, "path": UPath(id_ == "recycler_view", visible_ == True)},
            "top20_text": {"path": UPath(id_ == "recommend_text")},
            "first_rank_type": {"path": UPath(tag_ == "Selected", visible_ == True)},
            "audience_entrance": {"path": UPath(id_ == "online_rank_info_layout", visible_ == True) / UPath(type_ == "ConstraintLayout", visible_ == True)},
            "audience_num": {"path": UPath(id_ == "tv_online_audience_num", visible_ == True)},
            "check_champions_to_daily_btn": {"path": UPath(id_ == "tv_rank_entrance", text_ == "Daily Ranking", visible_ == True)},
            "check_daily_to_champions_btn": {"path": UPath(id_ == "tv_dialog_rank_entrance", text_ == "Champions Tournament", visible_ == True)},

            # 榜单元素
            "countdown_text": {"path": UPath(id_ == "tv_countdown_text", visible_ == True)},
            "countdown": {"path": UPath(id_ == "tv_countdown", visible_ == True)},
            "FAQ": {"path": UPath(id_ == "rank_help", visible_ == True)},
            "last_text": {"path": UPath(id_ == "last_text", visible_ == True)},

            # 吸底bar
            "bar_num": {"path": UPath(id_ == "rank_num", visible_ == True)},
            "bar_nickname": {"path": UPath(id_ == "name", visible_ == True)},
            "bar_container": {"path": UPath(id_ == "toolbar_container", visible_ == True)},
            "bar_img": {"path": UPath(id_ == "user_avatar", visible_ == True)},
            "bar_text": {"path": UPath(id_ == "tv_gap_description", visible_ == True)},
            "bar_text_rookie": {"path": UPath(id_ == "tv_not_in_rookie_rank", visible_ == True)},
            "bar_text_close_rank": {"path": UPath(id_ == "tv_info", visible_ == True)},
            "bar_gift": {"path": UPath(id_ == "btn_send_gift", visible_ == True)},
            "bar_diamonds": {"path": UPath(id_ == "ticket_count", visible_ == True)},

            # last week
            "last_week": {"path": UPath(id_ == "last_text", visible_ == True)},
            "last_week_title": {"path": UPath(id_ == "single_title", visible_ == True, text_ == "Last Week’s Ranking")},
            "last_week_subtitle": {"path": UPath(class_ == "count_down_container", visible_ == True) / UPath(id_ == "tv_countdown_text", visible_ == True)},

            # online user
            "online_entrance": {"path": UPath(id_ == "online_rank_info_layout") / UPath(type_ == "ConstraintLayout")},
            "online_num": {"path": UPath(id_ == "tv_online_audience_num")},
            "online_FAQ": {"path": UPath(id_ == "qa_icon")},
            "online_bar_gift": {"path": UPath(desc_ == "Gift", type_ == "FlattenUIText")},

            # houlrly
            "hourly_title": {"path": UPath(id_ == "single_title")},
            "hourly_FAQ_panel": {"path": UPath(id_ == "web_view_container") / UPath(type_ == "RoundRectWebView")},

            # common element
            "feed": {"path": UPath(id_ == "live_video_container", visible_ == True)},
            "gift_panel": {"path": UPath(id_ == "gift_panel_list")},
            "rank_panel": {"path": UPath(id_ == "recycler_view", visible_ == True)},
            "FAQ_panel": {"path": UPath(type_ == "LiveFrameLayout") / UPath(id_ == "fragment_container") / 0},
            "user_card": {"path": UPath(id_ == "content_layout")},

            # weekly top99
            "weekly_top99_icon": {"path": UPath(id_ == "iv_bulletin_icon", visible_ == True)},
            "weekly_top99_text": {"path": UPath(id_ == "tv_marquee_text", visible_ == True)},
            "ticket_count": {"path": UPath(id_ == "ticket_count", visible_ == True)},
            "rank01_ticket_count": {"path": UPath(tag_ == "testvvmnlzuxoq", visible_ == True)},
            "rank01_ticket_count2": {"path": UPath(tag_ == "test01", visible_ == True)},
            "rank03_ticket_count": {"path": UPath(tag_ == "testXFvcrjliSE", visible_ == True)},
            "rank03_ticket_count2": {"path": UPath(tag_ == "testxfvcrjlise", visible_ == True)},
            "user_name": {"path": UPath(id_ == "user_name", visible_ == True)},
            "toolbar_icon": {"path": UPath(id_ == "ttlive_toolbar_more", visible_ == True) / UPath(id_ == "toolbar_icon", visible_ == True)},
            "scroll_content": {"path": UPath(id_ == "scroll_content", visible_ == True)},
            "settings": {"path": UPath(text_ == "Settings", visible_ == True)},
            "rankings_setting": {"path": UPath(id_ == "rankings_setting", visible_ == True)},
            "gift_rank_switch": {"path": UPath(id_ == "gift_rank_switch", visible_ == True)},
            "iv_on_going": {"path": UPath(id_ == "iv_on_going", visible_ == True)},
            "rank_No1": {"path": UPath(text_ == "1", visible_ == True)},
            "rank_No1_2": {"path": UPath(id_ == "ttlive_rank_1", visible_ == True)},
            "bottom_bar_rank_num": {"path": UPath(id_ == "rank_num_container") / UPath(id_ == "rank_num", visible_ == True)},
            "action_container": {"path": UPath(id_ == "action_container", visible_ == True)},
        }

    def close_panel(self):
        device_rect = self.app.get_device().screen_rect
        device = self.app.get_device()
        device.click(device_rect.width * 0.6, device_rect.height * 0.08)
        time.sleep(3)

    def switch_weekly_tab(self):
        self.weekly_title.wait_for_existing(timeout=10, interval=1, raise_error=True)
        self.weekly_title.click()

    def switch_daily_tab(self):
        self.daily_title.wait_for_existing()
        self.daily_title.click()

    def switch_stars_tab(self):
        self.stars_title.wait_for_existing()
        self.stars_title.click()

    def switch_last_week(self):
        self.last_week.click()

    def open_rank_panel(self, rank_type: str = None):
        """
        rank_type: None-点击榜单入口；"weekly"-周榜；"daily"-日榜；"hourly"-小时榜；"stars'-新星榜；"last"-历史榜;"online-观众榜"
        """
        entrance_text = ""
        self.entrance.wait_for_existing()
        self.entrance_text.wait_for_existing()
        if rank_type is None:
            self.entrance.click()
        # 如果榜单类型与榜单入口对应，点击打开
        if rank_type == "weekly":
            self.entrance.click()
            self.switch_weekly_tab()
        elif rank_type == "daily":
            self.entrance.click()
            if self.check_champions_to_daily_btn.wait_for_visible(timeout=8, raise_error=False):
                self.check_champions_to_daily_btn.click()
            self.switch_daily_tab()
        elif rank_type == "last":
            entrance_text = self.entrance_text.text
            if "Weekly" in entrance_text:
                self.entrance.click()
                self.switch_weekly_tab()
            if "Daily" in entrance_text:
                self.entrance.click()
                self.switch_daily_tab()
                self.last_week.wait_for_visible()
                self.last_week.click()
        elif rank_type == "stars":
            self.entrance.click()
            if self.check_champions_to_daily_btn.wait_for_visible(timeout=8, raise_error=False):
                self.check_champions_to_daily_btn.click()
            self.switch_stars_tab()
        elif rank_type == "hourly":
            self.entrance.wait_for_existing()
            self.entrance.click()
            time.sleep(3)
        elif rank_type == "online":
            self.online_entrance.click()
        time.sleep(3)

    def open_FAQ_panel(self, rank_type: str = None):

        if rank_type == "online":
            self.online_FAQ.click()
            time.sleep(10)

        if rank_type in ("weekly", "hourly", "stars", "last"):
            self.FAQ.click()
            time.sleep(10)

    def open_gift_panel(self, rank_type: str = None):
        if rank_type == "online":
            self.online_bar_gift.click()
            time.sleep(3)
        if rank_type in ("weekly", "hourly", "stars"):
            self.bar_gift.click()
            time.sleep(3)

    def send_gift(self):
        self.send_gift_button.click()

    def get_live_on_status(self, num: int = 0):
        live_on_status = False
        list_items = self.rank_items.items()
        list_num = len(list_items)

        if num == 0:
            top3_items = self.top3_items.items()
            top3_num = len(top3_items)
            print("开始校验top3的直播状态")
            for top3 in range(top3_num):
                print("第{}次校验".format(top3))

                if top3_items[top3].top3_on_status.wait_for_existing(raise_error=False):
                    live_on_status = True
                    print("已找到top3内的直播状态")
                    return live_on_status
                else:
                    print(str(top3_items[top3].top3_on_status.items()))
                    print("top3 未找到直播状态")

        for item in range(list_num):
            print("开始校验列表内的直播状态")
            print("第{}次校验".format(item))
            if list_items[item].list_on_status.wait_for_existing(raise_error=False):
                live_on_status = True
                print("已找到列表内的直播状态")
                return live_on_status
            else:
                print("top3 未找到直播状态")

        return live_on_status

    def get_live_off_status(self, num: int = 0):
        live_off_status = False
        list_items = self.rank_items.items()
        list_num = len(list_items)

        if num == 0:
            top3_items = self.top3_items.items()
            top3_num = len(top3_items)
            print("开始校验top3的非直播状态")
            for top3 in range(top3_num):
                print("第{}次校验".format(top3))
                if top3_items[top3].top3_off_status.wait_for_existing(raise_error=False):
                    live_off_status = True
                    top3_items[top3].top3_off_status.click()
                    return [live_off_status, "榜单中存在未开播用户:" + str(top3_items[top3])]
                else:
                    print("top3 未找到非直播状态")

        print("开始校验列表内的非直播状态")
        for item in range(list_num):
            print("第{}次校验".format(item))
            if list_items[item].list_off_status.wait_for_existing(raise_error=False):
                live_off_status = True
                list_items[item].list_off_status.click()
                return [live_off_status, "榜单中存在未开播用户:" + str(list_items[item])]
            else:
                print("列表内 未找到非直播状态")

        return [live_off_status]

    def scroll_rank_list(self, num: int = 1, item_num: int = 1):
        items = self.rank_items.items()
        items_rect = items[len(items) - 1].rect

        for i in range(num):
            self.rank_panel.scroll(distance_x=0, distance_y=items_rect.height * item_num)
            time.sleep(3)

    def check_live_status(self):
        live_off_status = False
        live_on_status = False
        log_msg = "榜单中未检查出存在未开播状态用户"
        items = self.rank_items.items()

        for i in range(20):

            if not live_on_status:
                print("开始校验直播中状态")
                if self.get_live_on_status(num=i):
                    live_on_status = True

            if not live_off_status:
                print("开始校验未直播状态")
                check_result = self.get_live_off_status(num=i)
                if check_result[0]:
                    log_msg = check_result[1]
                    return [live_off_status, log_msg]

            self.scroll_rank_list(num=1, item_num=5)

        return [live_on_status and not live_off_status, log_msg]

    def check_rank_list_user(self, rank_type: str = None):

        live_on_status = False
        live_off_status = False
        room_name = self.room_name.text
        items = self.rank_items.items()
        items_rect = items[len(items) - 1].rect
        top = items_rect.top
        left = items_rect.left

        if rank_type in ("weekly", "last", "stars"):
            live_on_status = False
            live_off_status = False
        if rank_type == "hourly":
            live_on_status = False
            live_off_status = True

        for i in range(90):
            self.app.get_device().click(x=left, y=top)
            time.sleep(2)
            room_name2 = self.room_name.text

            if not live_on_status:
                # 通过直播间返回按钮和直播间标题不同判断是否是直播中的用户
                if self["back_room_button"].wait_for_existing(raise_error=False) and room_name != room_name2:
                    print("直播间校验成功")
                    live_on_status = True
                    self["back_room_button"].click()
                    time.sleep(3)
                    self.open_rank_panel(rank_type=rank_type)
                    time.sleep(3)
                    for num in range(i):
                        self["rank_panel"].scroll(distance_x=0,
                                                  distance_y=items_rect.height + items_rect.height * 0.15)
                        time.sleep(3)

            if not live_off_status:
                # 通过调起的用户卡和当前房间的名称判断是否是未开播用户
                if self["user_card"].wait_for_existing(raise_error=False) and room_name == self.room_name.text:
                    print("用户卡校验成功")
                    live_off_status = True
                    self.close_panel()

            if live_on_status and live_off_status:
                break
            else:
                self["rank_panel"].scroll(distance_x=0, distance_y=items_rect.height + items_rect.height * 0.15)

        return live_on_status and live_off_status

    def check_top20_text(self):
        status = False

        for i in range(20):

            print("执行第 {} 次".format(i))

            if self.top20_text.wait_for_existing(
                raise_error=False) and self.top20_text.text == "Top 20 hosts will be awarded bonus prizes":
                status = True
                return status

            self.scroll_rank_list(num=1, item_num=3)
            time.sleep(3)

        return status

    def next_room(self):
        self.feed.scroll(distance_x=0, distance_y=500)
        time.sleep(3)

    def check_entrance(self, rank_type: str = None):
        if rank_type in ("weekly", "last", "stars", "hourly"):
            return self.entrance.wait_for_existing(raise_error=False)

    def check_entrance_text(self, rank_type: str = None):

        text_status = False
        ranking_text = self["entrance_text"].text

        print(ranking_text)

        if rank_type in ("weekly", "stars"):
            if "Weekly Ranking" in ranking_text:
                print("周榜排名99+文案校验通过：" + ranking_text)
                text_status = True

            if "Weekly No." in ranking_text:
                print("周榜排名99内文案校验通过：" + ranking_text)
                text_status = True

            if "Rising Star No." in ranking_text:
                print("新星榜文案校验通过：" + ranking_text)
                text_status = True
        elif rank_type in ["daily"]:
            if "Daily Ranking" in ranking_text:
                print("日榜排名99+文案校验通过：" + ranking_text)
                text_status = True

            elif "Daily No." in ranking_text:
                print("日榜排名99内文案校验通过：" + ranking_text)
                text_status = True
        elif rank_type == "hourly":
            if "Hourly" in ranking_text:
                text_status = True
                print("小时榜排名99内文案校验通过：" + ranking_text)

        return text_status

    def check_bar_text(self, rank_type: str = None):
        bar_text = ""
        rank_num = ""
        check_text = ""
        status = False

        if rank_type in ("weekly", "hourly"):
            rank_num = self["bar_num"].text
            bar_text = self["bar_text"].text
            print(bar_text)
            print(rank_num)

        if rank_type == "stars":
            rank_num = self["bar_num"].text
            print(rank_num)

            if self.bar_text2.wait_for_existing(raise_error=False):
                check_text = "This ranking shows the hosts who received fewer than"
                bar_text = self["bar_text2"].text
                print(bar_text)
            else:
                bar_text = self["bar_text"].text
                print(bar_text)

        if rank_num == "-":
            if rank_type == "stars":
                if check_text in bar_text:
                    status = True
            if rank_type == "weekly":
                if "Ranking turned off by host" or "Ranking off" in bar_text:
                    status = True
        elif rank_num == "99+":
            if rank_type == "weekly":
                if "Diamonds to reach 99" in bar_text:
                    status = True
                    print("当前主播排名99+,文案校验通过：", bar_text)
            if rank_type == "hourly":
                if "Diamonds needed to be an hourly top host" in bar_text:
                    status = True
                    print("当前主播排名99+,小时榜文案校验通过：", bar_text)
            if rank_type == "stars":
                if "Diamonds to reach 99" in bar_text or check_text in bar_text:
                    status = True
                    print("当前主播排名99+,文案校验通过：", bar_text)

        elif int(rank_num) in range(2, 99 + 1):
            if "Diamonds to reach" in bar_text:
                status = True
                print("当前主播排名2~99,文案校验通过：", bar_text)

        # elif int(rank_num) in range(4, 10 + 1):
        #     if "Diamonds needed to be a top 3" in bar_text:
        #         status = True
        #         print("当前主播排名4~10,文案校验通过：", bar_text)

        # elif int(rank_num) == 2:
        #     if "Diamonds to be top 1" in bar_text:
        #         status = True
        #         print("当前主播排名2,文案校验通过：", bar_text)

        elif int(rank_num) == 1:
            if "Diamonds ahead of No. 2" in bar_text:
                status = True
                print("当前主播排名1,文案校验通过：", bar_text)
        else:
            status = False

        return status

    # def get_online_cion_num(self):
    #     number = self.online_coins_num.text
    #
    #     if number is not None:
    #         print("送礼:" + number)
    #
    #     time.sleep(3)

    # def get_online_user_num(self):
    #     time.sleep(3)
    #     print("观众数量:" + self.online_user_num.text)

    def check_countdown_text(self):
        countdown_text = self.countdown_text.text
        status = False
        if countdown_text == "Refresh in":
            status = True

        return status

    def check_countdown(self):
        return self.countdown.wait_for_visible(raise_error=False)

    def check_FAQ(self):
        return self.FAQ.wait_for_visible(raise_error=False)

    def check_last_week(self):
        return self.last_week.wait_for_visible(raise_error=False)

    def check_weekly_tab(self):
        weekly_title = self.weekly_title.text
        status = False
        if weekly_title == "Weekly Ranking":
            status = True
        return status

    def check_stars_tab(self):
        stars_title = self.stars_title.text
        status = False
        if stars_title == "Rising Stars":
            status = True
        return status

    def check_gift_panel(self):
        return self.gift_panel.wait_for_existing(raise_error=False)

    def check_FAQ_panel(self):
        return self.FAQ_panel.wait_for_existing(raise_error=False)

    def check_last_week_title(self):
        return self.last_week_title.wait_for_existing(raise_error=False)

    def check_last_week_subtitle(self):
        self.last_week_subtitle.wait_for_existing(raise_error=False)
        last_title = self.last_week_subtitle.text
        return "Top 99 LIVE creators in the last week" in last_title

    def check_bar_num(self, rank_type: str = None):
        if rank_type in ("weekly", "hourly", "last", "stars"):
            return self["bar_num"].wait_for_existing(raise_error=False)

    def check_bar_img(self, rank_type: str = None):
        if rank_type in ("weekly", "hourly", "last", "stars"):
            return self["bar_img"].wait_for_existing(raise_error=False)

    def check_bar_nickname(self, rank_type: str = None):
        if rank_type in ("weekly", "hourly", "last", "stars"):
            print(self["bar_nickname"].text)
            return self["bar_nickname"].wait_for_existing(raise_error=False)

    def check_bar_diamonds(self):
        diamonds = self.bar_diamonds.text
        status = False
        if diamonds is not None:
            status = True
        return status

    def check_bar_gift(self, rank_type: str = None):

        if rank_type == "stars":
            rank_num = self["stars_bar_num"].text
            if rank_num == "-":
                pass
            else:
                return self.bar_gift.wait_for_existing(raise_error=False)
        else:
            return self.bar_gift.wait_for_existing(raise_error=False)

    def check_hourly_title(self):
        hourly_title = self.hourly_title.text
        status = False
        if hourly_title == "Hourly Ranking":
            status = True
        return status

    def check_online_num(self):
        user_num = self.online_num.text
        status = False
        if user_num is not None:
            status = True
        return status

    def check_under_age_entrance(self):
        return not self.entrance.wait_for_existing(raise_error=False)


class RankList(Control):
    def get_locators(self):
        return {
            "list_on_status": {"path": UPath(id_ == "iv_avatar_border", visible_ == True)},
            "list_off_status": {"path": UPath(id_ == "iv_avatar_border", visible_ == False)},
            "top3_on_status": {"path": UPath(id_ == "iv_avatar_border", visible_ == True)},
            "top3_off_status": {"path": UPath(id_ == "iv_avatar_border", visible_ == False)},
        }


class RankListItems(Control):
    elem_class = RankList
    elem_path = UPath(type_ == "RelativeLayout", visible_ == True)


class RankListTopThreeItems(Control):
    elem_class = RankList
    elem_path = UPath(type_ == "LinearLayout", index=0) / UPath(type_ == "SinglePrimaryTopView", visible_ == True)


# class RisingStarsListItems(Control):
#     elem_class = RankList
#     elem_path = UPath(type_ == 'GBLRookieStarListCell', visible_ == True)


def call_method(method_name: str, args: dict, app) -> dict:
    class_name = "com.bytedance.android.livesdk.gift.debugservice.shoots.bridge.LiveClientShootsBridge"
    result_data = app.call_method(class_name=class_name, method="call", args=[
        method_name,
        json.dumps(args)
    ])
    if result_data == None:
        return None
    else:
        return json.loads(result_data)
