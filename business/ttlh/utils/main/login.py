# -*- coding: utf8 -*-
import time

from shoots_android.control import *
from uibase.upath import id_
from uibase.web import Webview, WebElement
from .base import FeedsList
from business.ttlh.utils.main.feedback import  FeedbackWebview


class DatePickerInSignUp(Control):
    """生日日期选择器
    产品代码：https://code.byted.org/ugc-android/TikTokAccount/blob/develop/awemeaccount/src/main/java/com/ss/android/ugc/aweme/account/common/widget/datepicker/DatePicker.java
    """
    def set_date(self, num):
        self._driver.call_object_method(self.id, '', 'setStartYear', 'void', num)

    def get_date(self):
        return self._driver.call_object_method(self.id, '', 'getYear', 'int')


class NumberPicker(ScrollView):
    """年份选择器
    产品代码：https://code.byted.org/ugc-android/TikTokAccount/blob/develop/awemeaccount/src/main/java/com/ss/android/ugc/aweme/account/common/widget/datepicker/NumberPicker.java
    """
    def set_date(self, num):
        self._driver.call_object_method(self.id, '', 'setCurrentNumber', '', num)


class LoginPanel(Window):
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.account.login.ui.MusCountryListActivity|com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivity|com.ss.android.ugc.aweme.account.login.v2.ui.SignUpOrLoginActivity|com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivityWithNoAnimation"}

    def get_locators(self):
        return {
            # Login
            "Sign_up_nav_bar_title": {"type": TextView, "path": UPath(id_ == 'nav_bar_title',  visible_ == True)},
            "phone": {"type": TextEdit, "path": UPath(id_ == 'inputWithIndicatorEditText', visible_ == True, index=0)},
            "email_title_login": {"type": Control, "path": UPath(id_ == 'phoneEmailLoginTab') / 0 / 1},
            "phone_region": {"path": UPath(id_ == 'phone_input_view_country_layout')},
            "region_code": {"type": TextView, "path": UPath(id_ == 'country_code_dd_name')},
            "phone_signup_disclaimer_us": {"type": TextView, "path": UPath(id_ == 'phoneSignUpDisclaimer')},
            "email_signup_disclaimer_us": {"path": UPath(id_ == 'emailSignUpDisclaimer')},
            "send_code": {"path": UPath(id_ == 'phoneLoginContinueBtn')},
            'code': {'type': TextEdit, 'path': UPath(id_ == 'inputCodeView')},
            'code_input_page_title': {"type": Control, "path": UPath(id_ == 'baseI18nContentTitle', visible_ == True)},
            'code_loading': {'path': UPath(id_ == 'inputCodeLoadingView', visible_ == True)},
            'verify_failed_text': {'path': UPath(id_ == 'result_indicator_group_text', visible_ == True)},
            'resend_code': {'path': UPath(id_ == 'inputCodeResendBtn')},
            "switch_password": {"path": UPath(id_ == 'inputCodeSwitchPasswordBtn')},
            "forget_password": {"path": UPath(id_ == 'inputPasswordForgot')},
            "forget_password_email": {"path": UPath(id_ == 'emailLoginForgotPassword')},
            "forget_password_by_phone_num": {"path": UPath(id_ == 'select_dialog_listview') / 0},
            "forget_password_by_email": {"path": UPath(id_ == 'select_dialog_listview') / 1},
            'password': {'type': TextEdit, 'path': UPath(id_ == 'inputWithIndicatorEditText')},
            'log_in': {'path': UPath(id_ == 'inputPasswordNextButton')},
            'switch_username': {'path': UPath(id_ == 'phoneEmailLoginTab') / 0 / 1},
            "username": {"type": TextEdit, "path": UPath(id_ == 'inputWithIndicatorEditText', visible_ == True)},
            "password2": {"type": TextEdit, "path": UPath(id_ == 'et_pwd_indicator', visible_ == True)},
            "username_log_in": {'path': UPath(id_ == 'emailLoginNextBtn')},
            # SignUp
            "birthday_selector": {"type": DatePickerInSignUp, "path": UPath(id_ == 'ageGateDatePicker')},
            "year_picker": {"type": NumberPicker, "path": UPath(id_ == 'year_picker')},
            "confirm": {"type": Button, "path": UPath(id_ == 'loading_button_text')},
            "sign_up_send_code": {"path": UPath(id_ == 'phoneSignUpContinueBtn', visible_ == True)},
            "sign_up_send_code1": {"type": Button, "path": UPath(id_ == "loading_button_text", visible_ == True)},
            "input_view": {"type": TextEdit, "path": UPath(id_ == 'inputWithIndicatorEditText', visible_ == True)},
            "skip_phone_number_bind": {"type": TextView, "path": UPath(text_ == "Skip", visible_ == True)},
            "email_title": {"type": Control, "path": UPath(id_ == 'phoneEmailSignUpTab') / 0 / 1},
            "email_input_confirm": {"type": Button, "path": UPath(id_ == 'emailSignUpContinueBtn') / 0},
            "email_input_clear": {"type": Button,
                                  "path": UPath(id_ == 'inputWithIndicatorViewContainer', visible_ == True)},
            "error_notice": {"type": TextView, "path": UPath(id_ == 'result_indicator_group_text', visible_ == True)},
            "password_title": {"type": Control, "path": UPath(id_ == 'createPasswordInput')},
            "password_input_confirm": {"type": Control, "path": UPath(id_ == 'loading_button_text')},
            "password_input_clear": {"type": Button,
                                     "path": UPath(id_ == 'inputWithMultipleIndicatorsViewContainer') / 0},
            "create_account": {"type": Button, "path": UPath(id_ == 'button1')},
            "back_btn": {"type": Control, "path": UPath(id_ == 'back_btn',visible_ == True)},
            "create_user_name_title": {"type": TextView, "path": UPath(id_ == 'baseI18nContentTitle')},
            "create_user_name_description" : {"type": TextView, "path": UPath(id_ == 'baseI18nContentDesc')},
            "create_user_name_edit_text":{"type": TextView, "path": UPath(id_ == 'inputWithIndicatorEditText', visible_ == True)},
            "Allow_personalized_ads": {"path": UPath(id_ == "title_tv")},
            "Allow_personalized_ads_text": {"path": UPath(id_ == "content_tv")},
            "Allow_personalized_ads_btn": {"path": UPath(text_ == "Accept")},
            "email_click_ca": {"path": UPath(text_ == "Email")},

            # TikTok is better with friends!
            "find_contract": {"type": Control, "path": UPath(text_ == "Skip", visible_ == True)},
            "select_region": {"path": UPath(text_ == "Select country or region", visible_ == True)},
            "region_txt": {"path": UPath(id_ == "region_text", text_ == "[US]The United States of America", visible_ == True)},
            "close_region": {"path": UPath(id_ == "nav_start", visible_ == True)},
            "close_login_panel": {"type": Control, "path": UPath(id_ == 'back_btn', visible_ == True)},
            "go_back_btn": {"type": Control, "path": UPath(id_ == 'button2')},
            "click_next_kr_signup": {"type": Control, "path":UPath(id_ == "btn_next")}
        }

    #   SignUp
    def create_username(self, username):
        self['create_user_name_edit_text'].input(username)
        time.sleep(1)
        return self['confirm'].click()

    def click_next(self):
        self["click_next_kr_signup"].click()

    def click_confirm(self):
        self["confirm"].click()

    def skip_phone_number_bind(self):
        return self['skip_phone_number_bind'].click()

    def allow_personalized_ads(self):
        return self['Allow_personalized_ads'].text

    @property
    def allow_personalized_ads_text(self):
        return self['Allow_personalized_ads_text'].text

    @property
    def allow_personalized_ads_agree_text(self):
        return self['Allow_personalized_ads_btn'].text

    @property
    def allow_personalized_ads_agree_btn(self):
        return self['Allow_personalized_ads_btn'].click()

    @property
    def create_user_name_title(self):
        return self['create_user_name_title'].text

    @property
    def create_user_name_description(self):
        return self['create_user_name_description'].text

    def back_btn_click(self):
        self["back_btn"].click()

    def skip_email_address_page(self):
        time.sleep(3)
        try:
            if self["find_contract"].wait_for_visible(timeout=5, raise_error=False):
                self["find_contract"].click()
        except:
            self.app.testcase.log_info("跳过email_address_page异常")

    def check_login_popup(self):
        if self["close_login_panel"].wait_for_existing(timeout=5, raise_error=False):
            self["close_login_panel"].click()
            time.sleep(3)

    def choose_region(self):
        time.sleep(2)
        self["select_region"].click()
        self["region_txt"].wait_for_visible(timeout=5)
        self["region_txt"].click()
        time.sleep(2)
        self["close_region"].click()

    def set_birthday(self, year):
        self["year_picker"].set_date(year + 1)
        rect_height = self["year_picker"].ensure_visible().height
        self["year_picker"].scroll_by_offset(0, -int(rect_height) / 3)
        self.refresh()
        current_year = self["birthday_selector"].get_date()
        return current_year == year

    def set_birthday_confirm(self):
        self["confirm"].click()
        return self["phone_region"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def enter_email_ca_login(self):
        self["email_click_ca"].click()

    def enter_email_signup(self):
        self["email_title"].click()
        return self["phone_region"].wait_for_invisible(timeout=2, interval=0.5, raise_error=True)

    def enter_email_signup_tab(self):
        self["email_title"].click()

    def input_email_right(self, email_address):
        while self["email_input_clear"].existing:
            self["email_input_clear"].click()
        self["input_view"].input(email_address)
        self["email_input_confirm"].click()
        return self["password_title"].wait_for_visible(timeout=20, interval=0.5, raise_error=False)

    def input_email_wrong(self, email_address):
        while self["email_input_clear"].existing:
            self["email_input_clear"].click()
        self["input_view"].input(email_address)
        self["email_input_confirm"].click()
        return self["error_notice"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def input_password_wrong(self, password):
        while self["password_input_clear"].visible:
            self["password_input_clear"].click()
        self["input_view"].input(password)
        return not self["password_input_confirm"].enabled

    def input_password_right(self, password):
        while self["password_input_clear"].visible:
            self["password_input_clear"].click()
        self["input_view"].input(password)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            return True
        else:
            return False
    def input_password(self, password):
        self["password"].text = password
        if self["password_input_confirm"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["password_input_confirm"].click()


    def enter_password(self, password):
        self["password2"].text = password
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()

    def enter_password_KR_signup(self, password):
        self["password"].text = password
        time.sleep(2)
        if self["confirm"].enabled:
            print("clicking next button")
            self["confirm"].click()
            return True
        else:
            print("wait for few seconds")
            return False

    @property
    def phone_signup_disclaimer(self):
        if self['phone_signup_disclaimer_us'].text == 'Your phone number will be used to improve your TikTok experience, including connecting you with people you may know, personalizing your ads experience, and more. If you sign up with SMS, SMS fees may apply. Learn more ' or self['phone_signup_disclaimer_us'].text== 'Your phone number may be used to connect you to people you may know, improve ads, and more, depending on your settings. If you sign up with SMS, SMS fees may apply. Learn more ':
            return True


    @property
    def nav_bar_title(self):
        return self['Sign_up_nav_bar_title'].text

    @property
    def email_signup_disclaimer(self):
        w = self['email_signup_disclaimer_us'].text
        print(f"this is test {w}")
        if self['email_signup_disclaimer_us'].text == "By continuing, you agree to TikTok’s  Terms of Service  and confirm that you have read TikTok’s  Privacy Policy. Your email will be used to improve your TikTok experience, including connecting you with people you may know and personalizing your ads experience.  Learn more" or self['email_signup_disclaimer_us'].text == "By continuing, you agree to TikTok’s  Terms of Service  and confirm that you have read TikTok’s  Privacy Policy. Your email address may be used to connect you to people you may know, improve ads, and more, depending on your settings. Learn more ":
            return True


    #   Login


    def verify_create_account(self):
        """判断当前输入手机号是否是未注册状态
        """
        self.refresh()
        return self["create_account"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def verify_page_loading(self):
        return self["phone"].wait_for_existing(raise_error=False)

    def switch_phone_region(self, region):
        country_panel = MusCountryPanel(root=self)
        self["phone_region"].wait_for_visible()
        for _ in Retry(timeout=20, interval=1, raise_error=False):
            if self["phone_region"].wait_for_visible(timeout=3, raise_error=False):
                self["phone_region"].click()
            elif country_panel.wait_for_visible(timeout=4, raise_error=False):
                break
        country_panel.search(region)

    def judge_current_region(self, code):
        self["region_code"].wait_for_visible()
        time.sleep(1)
        return self["region_code"].text == code

    def login_by_phone_from_signup(self, phone_number, password="", code=""):
        self["phone"].text = phone_number
        self.app.testcase.log_info("电话号码: {}".format(phone_number))
        time.sleep(2)
        if password:
            self["sign_up_send_code1"].click()
            self["switch_password"].click()
            self["password"].input(password)
            self["log_in"].click()
        elif code:
            self["sign_up_send_code1"].click()
            self["code_input_page_title"].wait_for_visible(timeout=160)
            time.sleep(1)
            if "4-digit" in self["code_input_page_title"].text:
                self["code"].text = code[2:]
            else:
                self["code"].text = code
            self.app.testcase.log_info("验证码: {}".format(code))
            self["code"].wait_for_disappear(raise_error=False)
        if self["find_contract"].wait_for_existing(timeout=3, raise_error=False):
            self["find_contract"].click()

    def login_by_phone(self, phone_number, password="", code=""):
        # self["phone"].input(phone_number)
        self["phone"].text = phone_number
        self.app.testcase.log_info("电话号码: {}".format(phone_number))
        time.sleep(1)
        if password:
            self["send_code"].click()
            self["switch_password"].click()
            self["password"].input(password)
            self["log_in"].click()
        elif code:
            if self["send_code"].wait_for_visible(timeout=5, raise_error=False):
                self["send_code"].click()
            elif self["sign_up_send_code"].existing:
                self["sign_up_send_code"].click()
            elif self["sign_up_send_code1"].existing:
                self["sign_up_send_code1"].click()
            self["code_input_page_title"].wait_for_visible(timeout=60)
            if "4-digit" in self["code_input_page_title"].text:
                # self["code"].input(code[2:])
                self["code"].text = code[2:]
            else:
                # self["code"].input(code)
                self["code"].text = code
            self.app.testcase.log_info("验证码: {}".format(code))
            self["code"].wait_for_disappear(raise_error=False)
        if self["find_contract"].wait_for_existing(timeout=3, raise_error=False):
            self["find_contract"].click()

    def login_by_phone_forget_PWD(self, phone_num, code, original_password, new_password):
        self["phone"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self.switch_phone_region("china")
        self["phone"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["phone"].input(phone_num)
        self["send_code"].click()
        self["switch_password"].wait_for_visible(timeout=160)
        self["switch_password"].click()
        self["forget_password"].click()
        self["forget_password_by_phone_num"].click()
        self["confirm"].click()
        self["code"].wait_for_visible(timeout=160)
        self["code_input_page_title"].wait_for_visible(timeout=160)
        if "4-digit" in self["code_input_page_title"].text:
            self["code"].input(code[2:])
        else:
            self["code"].input(code)

        self["password"].input(original_password)
        self["confirm"].click()
        if self["error_notice"].wait_for_existing(timeout=2, interval=0.5, raise_error=False) and \
            self["error_notice"].text == "Your new password can’t be the same as your old password":
            for i in range(18):
                self._device_driver.send_key(67)
            self["password"].input(new_password)
            self["confirm"].click()
            return True
        else:
            return False

    def login_by_email_forget_PWD(self, phone_num, code, original_password, new_password):
        self["email_title_login"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["email_title_login"].click()
        # self["input_view"].input(email)
        self["forget_password_email"].click()
        self["forget_password_by_phone_num"].click()
        self.switch_phone_region("china")
        self["phone"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["phone"].input(phone_num)
        self["confirm"].click()
        self["code"].wait_for_visible(timeout=160)
        self["code_input_page_title"].wait_for_visible(timeout=160)
        if "4-digit" in self["code_input_page_title"].text:
            self["code"].input(code[2:])
        else:
            self["code"].input(code)

        self["password"].input(original_password)
        self["confirm"].click()
        if self["error_notice"].wait_for_existing(timeout=2, interval=0.5, raise_error=False) and \
            self["error_notice"].text == "Your new password can’t be the same as your old password":
            for i in range(18):
                self._device_driver.send_key(67)
            self["password"].input(new_password)
            self["confirm"].click()
            return True
        else:
            return False

    @property
    def verify_failed_msg(self):
        self["verify_failed_text"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["verify_failed_text"].text

    def reset_code(self, code):
        self["code"].click()
        self.send_keys(67)
        if "4-digit" in self["code_input_page_title"].text:
            self["code"].input(code[2:])
        else:
            self["code"].input(code)
        self.app.testcase.take_screen_shot(self.app)

    def login_by_username(self, username, password):
        self["switch_username"].click()
        time.sleep(1)
        self["username"].text = username
        self["password2"].text = password
        self["username_log_in"].click()
        if self["find_contract"].wait_for_existing(timeout=3, raise_error=False):
            self["find_contract"].click()

    def reset_password(self, password):
        self["password2"].text = ""
        self["password2"].input(password)
        self.app.testcase.take_screen_shot(self.app)
        self["username_log_in"].click()

    def reset_password_phone(self, password):
        self["password"].text = ""
        self["password"].input(password)
        self["log_in"].click()

    def go_back_click(self):
        self["go_back_btn"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["go_back_btn"].click()



class TemplateDetailPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.shortvideo.mvtemplate.choosemedia.MvChoosePhotoActivity"}

    def get_locators(self):
        return {
            "all": {"type": Control, "path": UPath(id_ == 'tv_title')},
        }



class MusCountryPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.ui.MusCountryListActivity"}

    def get_locators(self):
        return {
            "search_edit": {"type": TextEdit, "path": UPath(id_ == 'search_edit')},
            "search_btn": {"type": TextEdit, "path": UPath(id_ == 'search_send')},
            "country_list": {"type": FeedsList, "path": UPath(id_ == 'rv_contacts')},
        }

    def search(self, country_or_region):
        self["search_edit"].input(country_or_region)
        time.sleep(5)
        self["search_btn"].click()
        time.sleep(2)
        self.app.testcase.log_info("修改区号为{}".format(country_or_region))
        self.app.testcase.take_screen_shot(self.app)
        self["country_list"].items()[0].click()


class ExistingAccountLoginPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.rememberaccount.LoginMethodListActivity"}

    def get_locators(self):
        return {
            "help": {"type": Control, "path": UPath(id_ == 'help')},
            "close": {"type": Control, "path": UPath(id_ == 'close')},
            "add_count": {"type": Control, "path": UPath(id_ == 'add_account', visible_ == True)},
            "first_account": {"type": Control, "path": UPath(id_ == 'method_list') / 1},
            "delete": {"type": Control, "path": UPath(id_ == 'delete')},
            "delete_confirm": {"type": Control, "path": UPath(id_ == 'text1', visible_ == True)},
            "delete_confirm_2": {"type": Control, "path": UPath(id_ == 'positive_button')},
            "cannotlogin": {"type": Control, "path": UPath(id_ == 'cant_login')},
            "signup": {"type": Control, "path": UPath(id_ == 'll_login_group')},

            "webview": {"type": FeedbackWebview, "path": UPath(id_ == "webview_root")},

        }

    def add_account(self):
        self["add_count"].click()

    def help_webview(self):
        self["help"].click()

    def close(self):
        self["close"].click()

    def sign_up(self):
        self["signup"].click()

    def cannot_login(self):
        self["cannotlogin"].click()

    def delete_account_info(self):
        self["delete"].click()
        self["delete_confirm"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["delete_confirm"].click()
        self["delete_confirm_2"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["delete_confirm_2"].click()
        return self["delete"].wait_for_not_existing(timeout=2, interval=0.5, raise_error=False)


class RecoverAccountPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.crossplatform.activity.CrossPlatformActivity"}

    def get_locators(self):
        return {

        }



class SignUpActionFlowPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.v2.ui.LoginActionFlowActivity"}

    def get_locators(self):
        return {
            "input_view": {"type": TextEdit, "path": UPath(id_ == 'inputWithIndicatorEditText', visible_ == True)},
            "password_input_confirm": {"type": Control, "path": UPath(id_ == 'loading_button_text', visible_ == True)},
            # "password_input_confirm_enabled": {"type": Control, "path": UPath(id_ == 'loading_button_text')},
            "skip": {"type": Control, "path": UPath(id_ == 'text_left')},
        }

    def get_suggest_username(self):
        time.sleep(3)
        self.refresh()
        if self["password_input_confirm"].enabled:
            return self["input_view"].text
        else:
            return ''

    def click_next_ca(self):
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()

    def input_username_wrong(self, username):
        self["input_view"].text = username
        self.refresh()
        return not self["password_input_confirm"].enabled

    def input_username_right(self, username):
        self["input_view"].text = username
        self.refresh()
        time.sleep(6)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            return True
        else:
            return False

    def skip_username_configure(self):
        self["skip"].click()


class KidSignUpPanel(Window):
    """For the Age gate screen panel for Kids Mode
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.account.agegate.activity.FtcActivity"}

    def get_locators(self):
        return {
            "input": {"type": TextEdit, "path": UPath(id_ == 'inputWithIndicatorEditText')},
            "username_input_clear": {"type": Control, "path": UPath(id_ == 'inputWithIndicatorViewContainer')},
            "username_taken_notice": {"type": TextView,
                                      "path": UPath(id_ == 'result_indicator_group_text', visible_ == True)},
            "next": {"type": Control, "path": UPath(id_ == 'loading_button_text')},
            "password_title": {"type": Control, "path": UPath(id_ == 'baseI18nContentTitle', visible_ == True)},
            "password_input_clear": {"type": Button,
                                     "path": UPath(id_ == 'inputWithMultipleIndicatorsViewContainer') / 0}
        }

    @property
    def age_gate_nav_bar_title_text(self):
        return self['password_title'].text
