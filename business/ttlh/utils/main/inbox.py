# -*- coding:utf-8 _*-
import time
import datetime

from PIL import Image
from shoots_cv.cv import CV
from shoots_android.control import *
from uibase.exceptions import UIAmbiguousError
from uibase.upath import id_, class_
from uibase.web import WebElement, Webview
from business.ttlh.utils.main.video import *
from settings import RESOURCE_PATH
from .base import FeedsList
from uibase.base import UIScene
from business.ttlh.utils.tt_popup import NudeContentPopup

# message type
HASHTAG_MSG = "hashtag"
STORY_MSG = "story"
QA_MSG = "Q&A"
LIVE_EVENT_MSG = "live_event"
REPLAY_MSG = "replay"
POI_MSG = "poi"
GROUP_INVITE_MSG = "group_invite"
TEXT_MSG = "text"
LYNX_PROFILE_MSG = "lynx_profile"


class InboxPanel(Window):
    """Inbox Tab
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    MUTE = "mute"
    UNMUTE = "unmute"
    REPORT = "report"
    BLOCK = "block"
    PIN = "pin"
    UNBLOCK = "unblock"

    def get_locators(self):
        return {
            'group_title': {'type': Control, 'path': UPath(id_ == 'tv_group_title')},
            'status_view': {'type': Control, 'path': UPath(id_ == 'status_view')},
            'progress_bar': {'type': Control, "root": "status_view", 'path': UPath(id_ == 'progressBarLayout')},
            'entrance_box': {'type': Control, 'path': UPath(~id_ == 'entrance_box|createChatView')},
            'message_box': {'type': Control, 'path': UPath(id_ == 'tv_message_box')},
            'new_from_tiktok': {'path': UPath(id_ == 'awemenotice_tuxtextview', text_ == 'New from TikTok')},
            'inbox_panel': {'type': ScrollView, 'path': UPath(id_ == 'inboxRecyclerView')},
            'chat_list': {'type': ChatList, 'path': UPath(id_ == 'inboxRecyclerView')},
            #   Contacts Sync Popup
            'contact_sync_popup_root': {'type': Control, 'path': UPath(id_ == 'visual_area')},
            'contact_sync_close': {'type': Control, 'root': 'contact_sync_popup_root',
                                   'path': UPath(text_ == 'Not now')},
            "notice_inbox_list": {"type": NoticeInboxList, "path": UPath(id_ == 'rv_list')},
            "first_notice": {"path": UPath(id_ == 'rv_list', visible_ == True) / 0},
            "suggest_account": {"path": UPath(id_ == 'tv_name', text_ == 'Suggested accounts', visible_ == True)},
            "creat_chat_view": {"path": UPath(id_ == "createChatView")},
            # For Inbox Redesign
            "only_chat_avatar": {"path": UPath(id_ == 'avatar_image')},
            "chat_user_name": {"path": UPath(id_ == 'user_name')},
            "last_chat_content": {"path": UPath(id_ == 'last_session_content')},
            "last_chat_timestamp": {"path": UPath(id_ == 'last_timestamp')},
            "chentest001_chat": {"path": UPath(id_ == 'user_name', text_ == 'ChenTest001')},
            #Follow requests
            "follow_requests": {"path": UPath(id_ == 'notification_root', type_ == 'android.widget.RelativeLayout')},

            "msg_load_test_user": {"path": UPath(id_ == 'user_name', text_ == 'msg_load_test')},
            "group_load_test_conv": {"path": UPath(id_ == 'user_name', text_ == 'Group Load Test')},
            "delete_conv_btn": {"path": UPath(text_ == 'Delete')},
            "delete_title": {"path": UPath(id_ == 'title_tv')},
            "delete_desc": {"path": UPath(id_ == 'content_tv')},
            "delete_cancel_btn": {"path": UPath(text_ == 'Cancel')},
            "session": {'type': Control, 'path': UPath(id_ == 'session_list_root_layout', index=0)},
            "delete_chat_btn": {'type': Control,
                                'path': UPath(id_ == 'action_sheet_action_content', text_ == 'Delete')},
            "delete_btn": {'type': Control, 'path': UPath(text_ == 'Delete')},
            'no_net_button': {'type': Control, 'path': UPath(id_ == "button")},
            "story_ring": {'path': UPath(id_ == 'story_ring')},
            "first_story_ring": {'path': UPath(id_ == 'story_ring', visible_ == True, index=0)},
            "your_story": {'type': Control, 'path': UPath(text_ == 'Your Story')},
            "first_show_in_head": {'path': UPath(type_ == "PowerList") / UPath(type_ == "ConstraintLayout", index=1)},

            "follow_requests_text": {'path': UPath(text_ == 'Follow requests', id_ == 'tv_title')},
            "new_followers": {'path': UPath(text_ == 'New followers', id_ == 'awemenotice_tuxtextview')},
            "new_activities": {'path': UPath(text_ == 'New activities', id_ == 'tv_activity_title')},
            "activity": {'path': UPath(~text_ == 'Activity|Activities', id_ == "tv_activity_title")},
            "messages": {'path': UPath(text_ == 'Messages', id_ == 'tv_message_title')},
            "contacts": {'path': UPath(text_ == "Contacts")},
            "inbox_head": {'path': UPath(type_ == 'com.bytedance.ies.powerlist.PowerList', visible_ == True)},
            "close_icon": {"path": UPath(id_ == "ib_im_top_notice_close_button")},
            "inbox_title": {"path": UPath(id_ == 'title', text_ == 'Inbox')},
            "chat_icon": {'type': Control, "path": UPath(id_ == 'createChatView', visible_ == True)},

            "bottom_delete_zh": {"path": UPath(text_ == "删除")},
            "bottom_delete_en": {"path": UPath(text_ == "Delete")},
            "left_full_screen": {"path": UPath(id_ == "icon_arrow_up_right_and_arrow_down_left")},
            "detail_btn": {'type': Control, 'path': UPath(id_ == 'icon_ellipsis_horizontal')},
            "share_group_video": {'type': Control, 'path': UPath(id_ == "content_layout", visible_ == True)},
            "report_chat_btn": {'type': Control, 'path': UPath(text_ == "Report")},
            "unmute_chat_btn": {'type': Control, 'path': UPath(text_ == "Unmute")},
            "mute_chat_btn": {'type': Control, 'path': UPath(text_ == "Mute")},
            "block_chat_btn": {'type': Control, 'path': UPath(text_ == "Block")},
            "unblock_chat_btn": {'type': Control, 'path': UPath(text_ == "Unblock")},

            "close_friend_pop": {'type': Control, 'path': UPath(id_ == "close_icon_view")},

            # ice break
            "ice_breaking_button": {'type': Control, 'path': UPath(id_ == "relation_button", visible_ == True)},
            "ice_breaking_popups": {'type': Control, 'path': UPath(id_ == "action_area")},
            "follow_back": {'type': Control, 'path': UPath(id_ == "name_append_follow_text")},

            "title_all_activity": {'path': UPath(id_ == "tv_title", visible_ == True, ~text_ == "All activity|Inbox")},
            "sign_up_btn": {'path': UPath(id_ == "unlogin_btn_login_signup", visible_ == True, text_ == "Log in or sign up")},
            "helpdesk": {'path': UPath(text_ == "TikTok Helpdesk", visible_ == True)},

            # inbox add search feature adapter
            "search_input": {'path': UPath(id_ == "search_et")},
            "send_btn": {'path': UPath(id_ == "send_btn")},
            "search_result_list": {'type': UserList, 'path': UPath(id_ == "result_list")},
            "btn_clear": {'path': UPath(id_ == "btn_clear")}
        }

    #inbox页面无网络页面点击Retey按钮
    def click_no_net_button(self):
        if self["no_net_button"].wait_for_existing(timeout=10, raise_error=False):
            self["no_net_button"].click()

    def click_chat_icon(self):
        if self["inbox_title"].wait_for_existing(timeout=5, raise_error=False):
            if self["chat_icon"].wait_for_existing(timeout=5, raise_error=False):
                self["chat_icon"].click()
            time.sleep(3)

    def check_helpdesk(self):
        return self["helpdesk"].wait_for_existing(timeout=5, raise_error=False)

    def click_helpdesk(self):
        self["helpdesk"].click()
        time.sleep(3)

    def wait_for_title_visible(self):
        return self["group_title"].wait_for_visible(raise_error=False)

    def wait_for_loading(self):
        self["progress_bar"].wait_for_invisible(raise_error=False)
        if self['contact_sync_popup_root'].wait_for_existing(timeout=10, raise_error=False):
            self['contact_sync_close'].click()

    def click_new_from_tiktok(self):
        self["new_from_tiktok"].wait_for_visible(timeout=20, interval=1, raise_error=False)
        self["new_from_tiktok"].click()

    def wait_for_message_box_invisible(self):
        return self["message_box"].wait_for_invisible(timeout=2, raise_error=True)

    def wait_for_title_all_activity_visible(self):
        from business.ttlh.utils.main import I18nSignUpPanel
        login_panel = I18nSignUpPanel(root=self.app)
        if login_panel["left_question_info"].wait_for_visible(timeout=3, raise_error=False):
            login_panel["left_question_info"].click()
        return self["title_all_activity"].wait_for_visible(raise_error=False)

    def wait_for_sign_up_btn_visible(self):
        return self["sign_up_btn"].wait_for_visible(raise_error=False)


    def enter_chat_room(self):
        time.sleep(2)
        self["message_box"].click()
        MessageListPanel(root=self.app).enter_entrance_box()
        RelationSelectPanel(root=self.app).select_friend()

    def enter_chat_list(self):
        time.sleep(2)
        self["message_box"].click()

    def enter_the_only_chat(self):
        if not self["only_chat_avatar"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["inbox_panel"].swipe(y_direction=1)
        self["only_chat_avatar"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["only_chat_avatar"].click()

    def enter_chentest001(self):
        if not self["chentest001_chat"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["inbox_panel"].swipe(y_direction=1)
        self["chentest001_chat"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["chentest001_chat"].click()

    def click_follow_requests(self):
        self["follow_requests"].click()
        time.sleep(3)

    def enter_entrance_box(self):
        self["entrance_box"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["entrance_box"].click()

    def enter_group_load_test_conv(self):
        self["group_load_test_conv"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["group_load_test_conv"].click()

    def delete_last_created_chat(self):
        if self["session"].wait_for_visible(timeout=2, raise_error=False):
            self["session"].long_click()
        self["delete_chat_btn"].click()
        self["delete_btn"].click()

    def delete_latest_chat(self, user):
        for item in self["chat_list"].items():
            if user in item["user_name"].text:
                time.sleep(2)
                item["user_name"].long_click()
                time.sleep(3)
                self["delete_chat_btn"].click()
                self["delete_btn"].click()
                break

    def check_group_chat_name(self, text):
        for item in self["chat_list"].items():
            if text in item["user_name"].text:
                return True
        return False

    def check_group_chat_not_exist(self, text):
        for item in self["chat_list"].items():
            if text in item["user_name"].text:
                return False
        return True

    def check_unread_message(self):
        flag = True
        chats = self['chat_list'].children
        for chat in chats:
            if chat.elem_info['id'] == "session_list_root_layout":
                left_dot = Control(root=chat, path=UPath(id_ == "iv_left_unread_dot"))
                right_dot = Control(root=chat, path=UPath(id_ == "iv_right_unread_dot"))
                if (left_dot.existing and left_dot.visible) or (right_dot.existing and right_dot.visible):
                    flag = False
        return flag

    def enter_last_created_chat(self):
        self["session"].click()

    def enter_your_story(self):
        self["your_story"].click()

    def get_last_timestamp(self, user):
        return str(datetime.datetime.now()).split(".")[0]

    def delete_recent_chats(self, chat_room_name):
        try:
            for _ in Retry(limit=15, interval=1):
                for item in self["chat_list"].items():
                    chat_room_text = item["user_name"].text
                    # self.app.testcase.log_info(f"chat room name:{chat_room_text}")
                    if chat_room_name == chat_room_text:
                        if not item["user_name"].visible:
                            self["chat_list"].swipe(y_direction=1, swipe_coefficient=3)
                            self["chat_list"].wait_for_ui_stable()
                        item["user_name"].long_click()
                        if self["bottom_delete_zh"].wait_for_existing(timeout=1, raise_error=False):
                            self["bottom_delete_zh"].click()
                            self["bottom_delete"].wait_for_existing(timeout=1)
                            self["bottom_delete_zh"].click()
                        elif self["bottom_delete_en"].wait_for_existing(timeout=1, raise_error=False):
                            self["bottom_delete_en"].click()
                            self["bottom_delete_en"].wait_for_existing(timeout=1)
                            self["bottom_delete_en"].click()
                        logger.info(f"delete group chat:{chat_room_name} success")
                self["chat_list"].swipe(y_direction=1, swipe_coefficient=4)
                self["chat_list"].wait_for_ui_stable()
        except:
            logger.info(f"delete {chat_room_name} execpt terminal")

    def enter_user_chat_room(self, user, inbox_list_flag=False, times=15, find_flag=False):
        """
        :param user:
        type:str
        :param inbox_list_flag:loop inbox list, find user enter chat room
        :type:bool
        :param times: loop find times
        :type:int
        :param find_flag: find user
        """
        flag = False
        break_flag = False
        screen_height = self.app.get_device().screen_rect.height
        self.control_inbox_list_before_page(entrance_box_flag=False)
        if inbox_list_flag:
            for _ in Retry(limit=5, interval=1, raise_error=False):
                self["inbox_panel"].swipe(y_direction=-1, swipe_coefficient=8)
                if self.new_followers.wait_for_visible(timeout=3, raise_error=False):
                    break
            for _ in Retry(limit=times, interval=1):
                for item in self["chat_list"].items():
                    if item["see_previous_msg"].wait_for_visible(timeout=3, raise_error=False) and \
                        "See previous messages" in item["see_previous_msg"].text:
                        item["see_previous_msg"].click()
                    if item["user_name"].wait_for_visible(timeout=3, raise_error=False):
                        chat_room_name = item["user_name"].text
                    logger.info(f"chat room name:{chat_room_name}")
                    if user == chat_room_name:
                        if find_flag:
                            break_flag = True
                            break
                        flag = True
                        item_height = item.rect.top + item.rect.height
                        logger.info(f"screen height:{screen_height}, item poistion:{item_height}")
                        if screen_height - item_height < 200:
                            self["chat_list"].swipe(y_direction=1, swipe_coefficient=3)
                        item.click()
                        break
                if flag or break_flag:
                    break
                self["chat_list"].swipe(y_direction=1, swipe_coefficient=3)
                self["chat_list"].wait_for_ui_stable()
        else:
            send_panel = RelationSelectPanel(root=self.app)
            if "RelationSelectActivity" not in self.app.current_activity and \
                self["entrance_box"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["entrance_box"].click()
            if not send_panel["search"].wait_for_visible(timeout=3, raise_error=False):
                msg_list_panel = MessageListPanel(root=self.app)
                msg_list_panel.enter_entrance_box()
            send_panel.enter_user_chat_room(user)

    def set_full_screen(self):
        if self["left_full_screen"].wait_for_existing(timeout=5, raise_error=False):
            self["left_full_screen"].click()

    def click_detail_btn(self):
        self["detail_btn"].wait_for_visible(timeout=10)
        self["detail_btn"].click()

    def check_dm_access(self):
        return self["entrance_box"].wait_for_existing(timeout=5, raise_error=False)

    def check_dm_section(self):
        return self["message_section"].wait_for_existing(timeout=5, raise_error=False)

    def check_dm_message(self):
        flag = True
        chats = self['chat_list'].children
        for chat in chats:
            if chat.elem_info['id'] == "session_list_root_layout":
                flag = False
        return flag

    def create_group_chat(self, group_member_list):
        self.enter_entrance_box()
        send_panel = RelationSelectPanel(root=self.app)
        if not send_panel["search"].wait_for_visible(timeout=3, raise_error=False):
            msg_list_panel = MessageListPanel(root=self.app)
            msg_list_panel.enter_entrance_box()
        send_panel.create_group_chat_room(member_list=group_member_list)

    def check_share_group_video(self):
        chat_rm_panel = ChatRoomPanel(root=self.app)
        if self["share_group_video"].wait_for_visible(timeout=5, raise_error=False):
            return True
        elif chat_rm_panel["latest_video"].wait_for_visible(timeout=3, raise_error=False):
            return True

    def chat_item_operation(self, chat_room, operation, item_long_flag=False):
        """
        chat list item operation
        :param chat_room:operation chat room name
        :type:str
        :param operation:mute,unmute,report,pin
        :type:str
        :param item_long_flag:long click flag, default False
        :type:bool
        """
        self.control_inbox_list_before_page(entrance_box_flag=False)
        try:
            for _ in Retry(limit=15, interval=1, raise_error=False):
                for item in self["chat_list"].items():
                    if item["see_previous_msg"].wait_for_visible(timeout=3, raise_error=False) and \
                        "See previous messages" in item["see_previous_msg"].text:
                        item["see_previous_msg"].click()
                    if item["user_name"].wait_for_visible(timeout=3, raise_error=False):
                        chat_room_name = item["user_name"].text
                    # logger.info(f"user name:{chat_room_name}")
                    if chat_room == chat_room_name:
                        if operation == self.PIN:
                            logger.info("Returns item to calculate the current item position")
                            if not item.visible:
                                self["chat_list"].swipe(y_direction=1, swipe_coefficient=3)
                            return item
                        else:
                            if not item_long_flag:
                                if self.check_item_icon_visible(item, operation):
                                    logger.info(f"{operation} icon visible, direct return")
                                    return True
                        # chat room item left operation
                        item.long_click(duration=3)
                        if self.MUTE == operation:
                            self["mute_chat_btn"].wait_for_existing(timeout=3)
                            self["mute_chat_btn"].click()
                        elif self.UNMUTE == operation:
                            self["unmute_chat_btn"].wait_for_existing(timeout=3)
                            self["unmute_chat_btn"].click()
                        elif self.REPORT == operation:
                            self["report_chat_btn"].wait_for_existing(timeout=3)
                            self["report_chat_btn"].click()
                        elif self.BLOCK == operation:
                            self["block_chat_btn"].wait_for_existing(timeout=3)
                            self["block_chat_btn"].click()
                            if self["block_chat_btn"].wait_for_existing(timeout=3, raise_error=False):
                                self["block_chat_btn"].click()
                        elif self.UNBLOCK == operation:
                            self["unblock_chat_btn"].wait_for_existing(timeout=3)
                            self["unblock_chat_btn"].click()
                            if self["unblock_chat_btn"].wait_for_existing(timeout=3, raise_error=False):
                                self["unblock_chat_btn"].click()
                        item.refresh()
                        return self.check_item_icon_visible(item, operation)
                self["chat_list"].swipe(y_direction=1, swipe_coefficient=3)
        except Exception as e:
            logger.info(f"{operation} {chat_room} except terminal,reason {e}")
        time.sleep(1)

    def check_item_icon_visible(self, item_control, operation):
        screen_height = self.app.get_device().screen_rect.height
        item_height = item_control.rect.top + item_control.rect.height
        if item_height > screen_height / 2:
            self["chat_list"].swipe(y_direction=1, swipe_coefficient=2.1)
            item_control.refresh()
        time.sleep(3)
        if operation == self.MUTE or operation == self.UNMUTE:
            icon_control = Control(root=item_control, path=UPath(id_ == "mute_view", visible_ == True))
        elif operation == self.BLOCK or operation == self.UNBLOCK:
            icon_control = Control(root=item_control, path=UPath(id_ == "block_view", visible_ == True))
        if "un" in operation:
            try:
                result = icon_control.visible
            except UINotFoundError as msg:
                logger.info(f"{operation[2:]} invisible, except msg:{msg}")
                result = True
        else:
            try:
                result = icon_control.visible
            except BaseException as e:
                logger.info(f"get {operation} icon state fail, reason:{e}")
                self["chat_list"].swipe(y_direction=1, swipe_coefficient=3)
                self["chat_list"].wait_for_ui_stable()
                result = icon_control.visible
        return result

    def check_pin_result(self, chat_room_name, pin_position=2, pin_flag=False):
        """
        通过聊天室位置判断pin是否成功
        :param chat_room_name:聊天室名称
        :type:str
        :param pin_position:pin位置，默认展示在聊天列表第3位
        :type:int
        :param pin_flag:默认pin关闭
        :type:bool
        """
        chat_list_content = []
        position = None
        flag = False
        for _ in Retry(limit=3, interval=1, raise_error=False):
            for item in self["inbox_panel"].items():
                user_name = Control(root=item, path=UPath(id_ == "user_name"))
                sys_name = Control(root=item, path=UPath(id_ == "awemenotice_tuxtextview"))
                if user_name.wait_for_visible(timeout=3, raise_error=False):
                    chat_name = user_name.text
                    chat_list_content.append(chat_name)
                    if chat_name == chat_room_name:
                        flag = True
                        break
                if sys_name.wait_for_visible(timeout=3, raise_error=False):
                    sys_name = sys_name.text
                    chat_list_content.append(sys_name)
            if flag:
                break
            self["inbox_panel"].swipe(y_direction=1, swipe_coefficient=3)
        logger.info(chat_list_content)
        for index, item in enumerate(chat_list_content):
            if chat_room_name == item:
                position = index
                break
        logger.info(f"{chat_room_name} position is {position}")
        if not pin_flag:
            if position >= pin_position:
                logger.info(f"{chat_room_name} not pin")
                return False
        else:
            if position == pin_position:
                logger.info(f"{chat_room_name} already pin")
                return True
            else:
                logger.info(f"{chat_room_name} not pin")
                return False

    def control_inbox_list_before_page(self, entrance_box_flag=True):
        donot_allow_control = Control(root=self, path=UPath(text_ == "Don't allow"))
        if donot_allow_control.wait_for_visible(timeout=5, raise_error=False):
            donot_allow_control.click()
        if self["close_friend_pop"].wait_for_visible(timeout=5, raise_error=False):
            self["close_friend_pop"].click()
        if entrance_box_flag and self["entrance_box"].wait_for_visible(timeout=5, raise_error=False):
            self["entrance_box"].click()

    def send_ice_breaking_btn(self, follow_back=False):
        if follow_back:
            if self["follow_back"].wait_for_visible(timeout=5):
                self["follow_back"].click()
                return True
            return False

        if self["ice_breaking_button"].wait_for_visible(timeout=5, raise_error=False):
            self["ice_breaking_button"].click()

        if self["ice_breaking_popups"].wait_for_visible(timeout=5, raise_error=False):
            self["ice_breaking_popups"].click()

    def create_chat_by_click_left_corner_icon(self, user):
        if self["search_input"].wait_for_visible(timeout=5, raise_error=False):
            if isinstance(user, list):
                for usr in user:
                    self["search_input"].text = usr
                    self.select_user(usr)
                    self["btn_clear"].click()
            else:
                self["search_input"].text = user
                self.select_user(user)
            if self["send_btn"].wait_for_visible(timeout=3, raise_error=False):
                self["send_btn"].click()
        nude_pop = NudeContentPopup(root=self.app)
        nude_pop.handle()

    def select_user(self, user):
        time.sleep(1)
        for item in self["search_result_list"].items():
            logger.info(f'=> search result:{item["user_name"].text}')
            if user == item["user_name"].text:
                item["user_name"].click()
                time.sleep(0.5)
                break

    def check_create_group_success(self):
        return self["send_btn"].wait_for_visible(timeout=3, raise_error=False)


class Chat(Control):
    def get_locators(self):
        return {
            "user_name": {"path": UPath(id_ == 'user_name')},
            "last_timestamp": {"path": UPath(id_ == "last_timestamp")},
            "see_previous_msg": {"path": UPath(type_ == "TuxTextView", visible_ == True)},
        }


class ChatList(Control):
    elem_path = UPath(id_ == "session_list_root_layout")
    elem_class = Chat


class NoticeInfo(Control):
    """
    inbox, all activity 页面 单条信息元素
    """

    def get_locators(self):
        return {
            "notification": {"path": UPath(id_ == 'notification_content', visible_ == True)},
            "avatar_expand": {"path": UPath(id_ == 'avatar_expand_layout', visible_ == True)},
            "more_tux": {"path": UPath(id_ == 'tuxMore', visible_ == True)},
            "date": {"path": UPath(id_ == 'tv_time')},
            'avatar4': {'type': NoticeList, 'path': UPath(id_ == 'ivAvatar4', visible_ == True)},
        }


class FollowersMessage(Window):
    """
        new followers 申请消息元素
    """

    def get_locators(self):
        return {
            "notification": {"path": UPath(id_ == 'notification_content', visible_ == True)},
        }


class FollowersList(Control):
    """
    new followers 聚合消息界面
    """

    elem_class = FollowersMessage
    elem_path = UPath(id_ == 'notification_root')


class NoticeList(Control):
    """
    inbox, all activity 页面 单条信息
    """

    elem_class = NoticeInfo
    elem_path = UPath(id_ == 'notification_root')


class InboxHostPanel(Window):
    """
        InboxHostPanel is class responsible for Host Panel activity which includes TikTok Platform,
        Account updates options.
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity|com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            # Share Video/Sound to TikTok from Web - In-App Notification Selector
            'video_sound_ready_to_edit': {'type': Control, 'path': UPath(~text_ == '.*TikTok Platform.*')},

            'title': {'path': UPath(id_ == 'nav_bar_title')},
            'view_all': {'path': UPath(text_ == 'View all')},
            'mes_list': {'type': NoticeList, 'path': UPath(id_ == 'rv_list')},
            'new_followers_list': {'type': FollowersList, 'path': UPath(id_ == 'followerRecyclerView')},
            'notification_reply_content': {'path': UPath(id_ == 'notification_reply_content', visible_ == True)},
            'tv_name': {'path': UPath(id_ == 'tv_name', visible_ == True)},
            'back_btn': {'path': UPath(id_ == 'back_btn')},
            'nav_start_TuxIconView': {'path': UPath(id_ == 'nav_start') / UPath(type_ == 'TuxIconView')},
            'user_name': {'path': UPath(id_ == 'user_name')},
            'rv_list_3_notification_name': {'path': UPath(id_ == 'rv_list') / 3 / UPath(id_ == 'notification_name')},
        }

    def click_video_sound_ready_to_edit(self):
        # Click on the 'TikTok Platform: Your video from <app name> is ready first level in-app notification
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["video_sound_ready_to_edit"].wait_for_visible(timeout=20, interval=1, raise_error=False):
                return self["video_sound_ready_to_edit"].click()

    def click_view_all(self):
        if self["view_all"].wait_for_existing(timeout=5, raise_error=False):
            self["view_all"].click()

    def find_message(self, video_type):
        """
        通过获取文案中的信息确定是什么类型视频, 聚合类型
        """
        self["mes_list"].wait_for_visible()
        for _ in Retry(timeout=180, interval=1):
            self.app.testcase.take_screenshot(self.app.get_device())
            notice_list = self["mes_list"].items()
            for notice in notice_list:
                if notice["notification"].existing and video_type in notice["notification"].text:
                    if video_type == "liked a video you reposted" and notice["avatar4"].existing:
                        self.app.testcase.take_screenshot(self.app.get_device())
                        return notice
                    if notice["more_tux"].existing and notice["more_tux"].visible:
                        list_bottom = self["mes_list"].rect.top + self["mes_list"].rect.height
                        notice_bottom = notice["avatar_expand"].rect.top + notice["avatar_expand"].rect.height
                        if list_bottom < notice_bottom:
                            self.scroll(coefficient_y=0.3)
                        return notice
            self.scroll(coefficient_y=0.5)

    def check_followers_message(self):
        """校验followers信息--时间排序
        """
        time_list = []
        time_dict = {"m": 1, "h": 2, "d": 3, "w": 4}
        self["new_followers_list"].wait_for_visible()
        time.sleep(2)
        self.app.testcase.take_screenshot(self.app.get_device())
        followers_list = self["new_followers_list"].items()
        for follower in followers_list:
            if follower["notification"].existing and follower["notification"].visible:
                time_info = follower["notification"].text.split(" ")[-1]
                time_list.append(time_info[2:-4:2] + time_info[-3])
        logger.info(time_list)
        for i in range(1, len(time_list)):
            if r"/" in time_list[i]:
                if r"/" not in time_list[i-1]:
                    continue
                else:
                    date_i = time_list[i].split(r"/")
                    date_last = time_list[i-1].split(r"/")
                    if int(date_i[0]) < int(date_last[0]):
                        continue
                    elif int(date_i[0]) > int(date_last[0]):
                        logger.info("时间校验异常")
                        return False
                    else:
                        if int(date_i[1]) <= int(date_last[1]):
                            continue
                        else:
                            logger.info("时间校验异常")
                            return False
            if time_dict.get(time_list[i][-1]) > time_dict.get(time_list[i-1][-1]):
                continue
            elif time_dict.get(time_list[i][-1]) == time_dict.get(time_list[i-1][-1]):
                if int(time_list[i][:-1]) < int(time_list[i-1][:-1]):
                    logger.info("时间校验异常")
                    return False
            else:
                logger.info("时间校验异常")
                return False
        return True

    def wait_for_notification_reply_content_text(self):
        self['notification_reply_content'].wait_for_text('^wtest_301: 还不到好多度😅$')

    def wait_for_tv_name_text(self):
        self['tv_name'].wait_for_text('^903$')

    def click_back_btn(self):
        self['back_btn'].click()

    def click_nav_start_tux_icon_view(self):
        self['nav_start_TuxIconView'].click()

    def wait_for_user_name_text(self):
        self['user_name'].wait_for_text('^wtest_301$')


class NotificationDetailPanel(Window):
    """
        聚合详情页
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.notification.LikeListDetailActivity|com.ss.android.ugc.aweme.notification.UserListActivity"}

    def get_locators(self):
        return {
            # Share Video/Sound to TikTok from Web - In-App Notification Selector
            'notification_list': {'path': UPath(id_ == 'notification_recycler_view')},
            'title': {'path': UPath(id_ == 'title_text')},
            'title_text': {'path': UPath(id_ == 'title_text')},
            'back_btn': {'path': UPath(id_ == 'back_btn')},
        }

    def wait_for_likes_title_text_text(self):
        self['title_text'].wait_for_text('^Likes$')

    def wait_for_Favorites_title_text_text(self):
        self['title_text'].wait_for_text('^Favorites$')

    def click_back_btn(self):
        self['back_btn'].click()

class InboxMusicDetailPanel(Window):
    """
        音乐相关聚合详情页
    """

    window_spec = {"activity": "com.bytedance.hybrid.spark.page.SparkActivity"}

    def get_locators(self):
        return {
            # Share Video/Sound to TikTok from Web - In-App Notification Selector
            'title': {'path': UPath(desc_ == 'Who used your song')},
            'user_list': {'path': UPath(type_ == 'com.bytedance.hybrid.spark.page.SparkView') / 0 / 0 / 0 / 0 / 2 / 0},
        }


class MessageListPanel(Window):
    """Inbox Tab
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.im.sdk.chatlist.ui.fragment.SessionListFragment.*"}

    def get_locators(self):
        return {
            'entrance_box': {'type': Control, 'path': UPath(~id_ == 'right_fl|right_container') / 0},
            'session': {'type': Control, 'path': UPath(id_ == 'session_list_root_layout', index=0)},
            'session_delete_button': {'type': Control,
                                      'path': UPath(id_ == 'actionsheet_actiongroups_container') / 0 / UPath(
                                          id_ == 'actionsheet_actiongroup_container') / UPath(
                                          id_ == 'actionsheet_text_action_content')},
            'delete_confirmation': {'type': Control, 'path': UPath(text_ == 'Delete')},
            "session_massage": {"path": UPath(id_ == 'session_list_root_layout', index=0)},
            "creat_group": {"path": UPath(id_ == "icon_create_group")},
            "only_chat_avatar": {"path": UPath(id_ == 'avatar_image')},
            "chat_user_name": {"path": UPath(id_ == 'user_name')},
            "last_chat_content": {"path": UPath(id_ == 'last_session_content')},
            "last_chat_timestamp": {"path": UPath(id_ == 'last_timestamp')},
            "back_btn": {"path": UPath(id_ == 'left_fl') / 0},
            "msg_load_test": {"path": UPath(text_ == 'msg_load_test')},
            "delete_conv_btn": {"path": UPath(text_ == 'Delete')},
            "delete_title": {"path": UPath(id_ == 'title_tv')},
            "delete_desc": {"path": UPath(id_ == 'content_tv')},
            "delete_cancel_btn": {"path": UPath(text_ == 'Cancel')},
            "group_load_test_conv": {"path": UPath(id_ == 'user_name', text_ == 'Group Load Test')},
            "chat_room_list": {'type': ChatList, 'path': UPath(id_ == "sessionRv")}
        }

    def find_massage(self):
        if self["session_massage"].wait_for_visible(timeout=2, raise_error=False):
            self["session_massage"].click()
        else:
            self["creat_group"].click()
            message_group = MessageGroupPanel(root=self.app)
            message_group["cduiauto002"].click()
            message_group["chat"].wait_for_visible()
            for _ in Retry(limit=3, interval=1.5):
                if message_group["chat"].existing:
                    message_group["chat"].click()
                    logger.info("进入聊天室")
                else:
                    break

    def enter_entrance_box(self):
        if self["entrance_box"].wait_for_visible(timeout=3, raise_error=False):
            self["entrance_box"].click()

    def delete_top_session(self):
        self["session"].long_click()
        if self['session_delete_button'].wait_for_existing(timeout=2, raise_error=False):
            self['session_delete_button'].click()
        else:
            self['delete_confirmation'].click()
        time.sleep(1)
        self['delete_confirmation'].refresh()
        self['delete_confirmation'].click()
        time.sleep(1)

    def enter_the_only_chat(self):
        self["only_chat_avatar"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["only_chat_avatar"].click()

    def click_back_btn(self):
        self["back_btn"].click()

    def click_delete_btn(self):
        if self["delete_confirmation"].wait_for_existing(timeout=5, raise_error=False):
            self["delete_confirmation"].click()

    def enter_group_load_test_conv(self):
        self["group_load_test_conv"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["group_load_test_conv"].click()


class MessageGroupPanel(Window):
    """friend panel
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.im.sdk.relations.ui.activity.RelationSelectActivity"}

    def get_locators(self):
        return {
            "cduiauto002": {"path": UPath(text_ == 'cduiauto002', id_ == 'name_tv')},
            "chat": {"path": UPath(text_ == 'Chat', visible_ == True)},
            "multiple_anchor": {"path": UPath(text_ == 'multipleanchor2', id_ == 'name_tv')},
        }

    def chat_cdu002(self):
        if self["cduiauto002"].wait_for_existing(timeout=5, raise_error=False):
            self["cduiauto002"].click()
        elif self["multiple_anchor"].wait_for_existing(timeout=5, raise_error=False):
            self["multiple_anchor"].click()
        time.sleep(2)
        self["chat"].click()


class FollowRequestsPanel(Window):
    """friend panel
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.notification.MusFollowRequestDetailActivity"}

    def get_locators(self):
        return {
            "follow_requests": {"path": UPath(text_ == 'Follow requests', id_ == 'title')},
            'recycler_view_0': {'path': UPath(id_ == 'recycler_view') / 0},
            'tv_nickname': {'path': UPath(id_ == 'tv_nickname')},
        }

    def click_recycler_view_0(self):
        self['recycler_view_0'].click()


class RelationSelectPanel(Window):
    """friend panel
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.im.sdk.relations.RelationSelectActivity|com.ss.android.ugc"
                               ".aweme.im.sdk.relations.ui.activity.RelationSelectActivity"}

    def get_locators(self):
        return {
            'search': {'type': TextEdit, 'path': UPath(id_ == 'search_et')},
            'first_search_result': {'type': Control, 'path': UPath(~id_ == 'result_list|recycle_view')/0},
            'second_search_result': {'type': Control, 'path': UPath(id_ == 'result_list')/1},
            'first_friend': {'type': Control, 'path': UPath(id_ == 'avatar_iv', index=0)},
            'chat': {'type': Control, 'path': UPath(~id_ == 'send_btn|share_confirm')},
            'receiver_friends': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'receiver_friends')},
            'receiver_no_one': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'receiver_no_one')},
            'content': {'type': Control, 'path': UPath(id_ == 'content')},
            'user_name': {'path': UPath(id_ == 'name_tv', visible_ == True)},
            'normal_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'normal_msg_receiver')},
            'text_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'text_receiver')},
            'navi_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'navi_receiver')},
            'emoji_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'emoji_receiver')},
            'system_emoji_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'system_emoji_receiver')},
            'sticker_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'sticker_receiver')},
            'gif_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'gif_receiver')},
            'video_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'video_msg_receiver')},
            'live_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'live_msg_receiver')},
            'music_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'music_msg_receiver')},
            'hashtag_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'hashtag_msg_receiver')},
            'playlist_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'playlist_msg_receiver')},
            'msg_load_test': {'type': ScrollView, 'path': UPath(id_ == 'name_tv', text_ == 'msg_load_test')},
            'effect_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'effect_msg_receiver')},
            'profile_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'profile_msg_receiver')},
            'h5_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'h5_msg_receiver')},
            'story_msg_receiver': {'type': Control, 'path': UPath(id_ == 'name_tv', text_ == 'story_msg_receiver')},
            'close_btn': {'type': Control, 'path': UPath(text_ == 'Close')},
            'btn_clear': {'type': Control, 'path': UPath(id_ == "btn_clear")},
            'clear_btn': {'type': Control, 'path': UPath(id_ == "btn_clear")},
        }

    def select_friend(self):
        self["first_friend"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_receiver_friends(self):
        self["receiver_friends"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_receiver_no_one(self):
        self["receiver_no_one"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_normal_msg_receiver(self):
        self["content"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(1)
        self["normal_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_text_receiver(self):
        self["content"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(1)
        self["text_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_emoji_receiver(self):
        self["emoji_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_system_emoji_receiver(self):
        self["system_emoji_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_sticker_receiver(self):
        self["sticker_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_gif_receiver(self):
        self["gif_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_video_msg_receiver(self):
        while not self["video_msg_receiver"].existing:
            self["content"].swipe(y_direction=1, swipe_coefficient=6)
        self["video_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_live_msg_receiver(self):
        self["live_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_story_msg_receiver(self):
        self["content"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(1)
        self["story_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_music_msg_receiver(self):
        self["music_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_hashtag_msg_receiver(self):
        self["hashtag_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_playlist_msg_receiver(self):
        self["content"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(1)
        self["playlist_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_msg_load_test(self):
        inbox_panel = InboxPanel(root=self.app)
        inbox_panel.create_chat_by_click_left_corner_icon("msg_load_test")

    def select_effect_msg_receiver(self):
        self["effect_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_profile_msg_receiver(self):
        self["content"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(1)
        self["profile_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def select_h5_msg_receiver(self):
        self["h5_msg_receiver"].click()
        self["chat"].wait_for_existing(timeout=2, raise_error=False)
        self["chat"].click()

    def send_to_multiple(self, name):
        if self["search"].wait_for_visible(timeout=3, raise_error=False):
            self["search"].input(name)
            self["first_search_result"].click()
            self["second_search_result"].click()
            time.sleep(1)
            self["chat"].click()
            time.sleep(2)
        else:
            inbox_panel = InboxPanel(root=self.app)
            inbox_panel.create_chat_by_click_left_corner_icon(name)

    def send_to_particular_user(self, name1, name2):
        if self["search"].wait_for_visible(timeout=3, raise_error=False):
            self["search"].input(name1)
            self["first_search_result"].click()
            time.sleep(1)
            self["btn_clear"].click()
            self["search"].input(name2)
            self["first_search_result"].click()
            time.sleep(1)
            self["chat"].click()
            time.sleep(3)
        else:
            inbox_panel = InboxPanel(root=self.app)
            inbox_panel.create_chat_by_click_left_corner_icon([name1, name2])

    def send_to_user(self, user):
        if self["search"].wait_for_existing(timeout=3, raise_error=False):
            self["search"].text = user
            self["first_search_result"].click()
        if self["chat"].existing:
            self["chat"].click()
            time.sleep(2)
        inbox_panel = InboxPanel(root=self.app)
        inbox_panel.create_chat_by_click_left_corner_icon(user)

    def click_close_btn(self):
        self["close_btn"].click()

    def enter_user_chat_room(self, user):
        if self["search"].wait_for_existing(timeout=3, raise_error=False):
            self["search"].text = user
            if self["first_search_result"].wait_for_visible(timeout=5, raise_error=False):
                self["first_search_result"].click()
            if self["chat"].wait_for_existing(timeout=3, raise_error=False):
                self["chat"].click()
        else:
            inbox_panel = InboxPanel(root=self.app)
            inbox_panel.create_chat_by_click_left_corner_icon(user)

    def create_group_chat_room(self, member_list):
        if self["search"].wait_for_existing(timeout=3, raise_error=False):
            x, y = 0, 0  # record first click point
            for member in member_list:
                if self["clear_btn"].wait_for_visible(timeout=2, raise_error=False):
                    self["clear_btn"].click()
                self["search"].text = member
                try:
                    self["user_name"].click()
                    rect = self["user_name"].rect
                    x, y = rect.center[0], rect.center[1]
                except UIAmbiguousError as error:
                    logger.info(error)
                    logger.info(f"available point:{(x, y)}")
                    self.app.testcase.device.click(x, y)
            self["chat"].wait_for_existing(timeout=1)
            self["chat"].click()
        else:
            inbox_panel = InboxPanel(root=self.app)
            inbox_panel.create_chat_by_click_left_corner_icon(member_list)


class Contact(Control):
    def get_locators(self):
        return {
            "user_name": {"path": UPath(id_ == "name_tv")},
        }


class ContactList(Control):
    elem_path = UPath(id_ == "content", visible_ == True)
    elem_class = Contact


class ChatRoomPanel(Window):
    """chat room
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.im.sdk.chat.ui.activity.ChatRoomActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            'msg_edit_text': {'type': TextEdit, 'path': UPath(id_ == 'msg_et')},
            'emoji_btn': {'type': Control, 'path': UPath(id_ == 'emoji_btn')},
            'navi_sticker_btn': {'type': Control, 'path': UPath(id_ == 'emoji_type_tab')/3},
            'sticker_btn': {'type': Control, 'path': UPath(id_ == 'emoji_type_tab')/4},
            'emoji_list': {'type': FeedsList, 'path': UPath(id_ == 'recyclerView', visible_ == True)},
            'rb_gif': {'type': Control, 'path': UPath(id_ == 'emoji_type_tab')/1},
            'git_panel': {'type': Control, 'path': UPath(type_ == 'com.airbnb.epoxy.EpoxyRecyclerView', visible_ == True)},
            'git_panel1': {'type': Control, 'path': UPath(id_ == 'recyclerView', visible_ == True)},
            'gif': {'type': Control, 'root': 'git_panel', 'path': UPath(id_ == 'emoji_iv', index=0)},
            'gif1': {'type': Control, 'root': 'git_panel1', 'path': UPath(id_ == 'emoji_iv', index=0)},
            'gif_list': {'type': FeedsList, 'path': UPath(id_ == 'recyclerView', visible_ == True)},

            'send_btn': {'type': Control, 'path': UPath(id_ == 'send_btn')},
            'forward_latest_btn': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'forward_icon', visible_ == True)},
            'forward_btn': {'type': Control,
                                   'path': UPath(id_ == "recycle_view") / 0 / UPath(id_ == "forward_icon")},
            'forward_btn1': {'type': Control,
                            'path': UPath(id_ == "recycle_view") / 1 / UPath(id_ == "forward_icon")},

            'back_btn': {'type': Control, 'path': UPath(~id_ == 'left_fl|left_container', visible_ == True)},
            'chat_page': {'type': ScrollView, 'path': UPath(id_ == 'recycle_view')},

            'latest_msg': {'type': Control, 'path': UPath(id_ == 'recycle_view') / 0},
            'latest_content': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'content')},
            'latest_content_layout': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'content_layout')},
            'latest_msg_title': {'type': Control, 'path': UPath(id_ == "recycle_view") / 0 / UPath(id_ == "title_tv")},

            'msg_tv': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'msg_tv')},
            'gif_content': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'picture_iv')},
            'sticker_content': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'picture_iv')},
            'latest_video': {'type': Control, "path": UPath(id_ == 'recycle_view')/0/UPath(id_ == 'iv_play_center')},
            'latest_hashtag': {'type': Control, "path": UPath(id_ == 'recycle_view')/0/UPath(id_ == 'content')},
            'last_sent_message': {'path': UPath(id_ == 'recycle_view')/0/UPath(id_ == 'content')},
            'latest_two_story': {"path": UPath(id_ == 'recycle_view') / 1 / UPath(id_ == 'iv_play_center')},
            'latest_two_hashtag': {"path": UPath(id_ == 'recycle_view') / 1 / UPath(id_ == 'content')},
            'music_title': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'title_tv')},
            'hashtag_title': {'type': Control, "root": "latest_msg", 'path': UPath(id_ == 'title_tv')},

            "poi_long_massage": {"path": UPath(id_ == 'recycle_view')/1/UPath(id_ == 'content_layout')},
            "poi_more_massage": {"path": UPath(id_ == 'recycle_view')/0/UPath(id_ == 'content_layout')},

            "sent_gif_msg": {'type': Control, 'path': UPath(id_ == 'picture_iv')},

            "title_layout": {'type': Control, 'path':UPath(id_ == 'chat_title_layout')},
            "username_title": {'type': Control, 'path': UPath(id_ == 'title_tv')},
            "report_btn": {'type': Control, 'path': UPath(id_ == 'icon_flag')},
            "detail_btn": {'type': Control, 'path': UPath(~id_ == 'icon_ellipsis_horizontal|icon_2')},

            "top_banner": {'type': Control, 'path': UPath(id_ == 'top_text')},
            "resend_btn": {'type': Control, 'path': UPath(~id_ == 'status_iv|message_status_label', visible_ == True)},

            "live_msg_name": {'type': Control, "root": "latest_content", 'path': UPath(id_ == 'bottom_name_tv')},
            "search_bar": {'type': Control, 'path': UPath(id_ == 'search_et')},
            "user_name": {'path': UPath(id_ == 'name_tv')},
            "send_to_user_button": {'path': UPath(id_ == 'tv_send')},
            "story_badge": {"root": "latest_msg", 'path': UPath(id_ == 'story_badge')},
            "new_chat_first_msg": {'type': Control, 'path': UPath(id_ == 'recycle_view')/1/UPath(id_ == 'msg_tv')},
            "message_poi_video": {'type': Control, "path": UPath(id_ == 'content_cover_iv', visible_ == True)},
            "msg_liked": {'type': Control, "root": "latest_msg", 'path': UPath(id_ == "ll_dm_reaction_content", visible_ == True)},
            "like_icon": {"type": Control, "path": UPath(id_ == "reaction_emoji")},
            "message_list_display": {"type": Control, "path": UPath(id_ == "recycle_view")},
            "settings": {"type": Control, "path": UPath(~id_ == "icon_ellipsis_horizontal|icon_2")},
            "title": {'type': Control, "path": UPath(id_ == "first_title")},
            # "new_chat_first_msg": {'type': TextEdit, 'path': UPath(id_ == "recycle_view")/1/UPath(id_ == "content")/UPath(id_ == 'msg_tv')},
            "like": {'type': Control, "path": UPath(id_ == "recycle_view")/0/UPath(id_ == "ll_dm_reaction", visible_ == True)},
            "reaction_username": {'type': Control, "path": UPath(id_ == "reaction_username")},
            "send_state_text": {'type': TextEdit, "root": "latest_msg", "path": UPath(id_ == "message_status_label")/0/UPath(id_ == "text", visible_ == True)},
            'latest_two_poi': {'type': Control, "path": UPath(id_ == "recycle_view")/1/UPath(id_ == "title_tv")},
            'latest_poi': {'type': Control, "path": UPath(id_ == "recycle_view")/0/UPath(id_ == "title_tv")},
            'latest_two_qa': {'type': Control, "path": UPath(id_ == "recycle_view")/1/UPath(id_ == "icon_tux")},
            'latest_qa': {'type': Control, "path": UPath(id_ == "recycle_view")/0/UPath(id_ == "icon_tux")},
            'latest_two_live_event': {'type': Control, "path": UPath(id_ == "recycle_view")/1/UPath(text_ == "LIVE Event")},
            'latest_live_event': {'type': Control, "path": UPath(id_ == "recycle_view")/0/UPath(text_ == "LIVE Event")},
            'latest_two_group': {'type': Control,
                                 "path": UPath(id_ == "recycle_view") / 1 / UPath(id_ == "content", type_ == "FrameLayout")},
            'latest_group': {'type': Control, "path": UPath(id_ == "recycle_view")/0/UPath(id_ == "content", type_ == "FrameLayout")},
            'right_corner_forward': {"path": UPath(id_ == "icon_share")},
            'bottom_user_list': {'type': Control, "path": UPath(id_ == "rv_share_panel_avatar", type_ == "RecyclerView")},
            'send': {'type': Control, "path": UPath(text_ == "Send")},
            'enter_group_chat': {'type': Control, "path": UPath(id_ == "btn_enter")},
            "right_fl": {"type": Control, "path": UPath(id_ == "right_fl")},
            'group_chat_name': {"path": UPath(id_ == "first_title")},
            "send_to_list": {'type': ContactList, "path": UPath(id_ == "contact_list_recyclerview", visible_ == True)},
            "text_msg": {"path": UPath(id_ == "msg_tv")},
            'tv_group_title': {'path': UPath(id_ == 'tv_group_title')},
            'backIconView': {'path': UPath(id_ == 'backIconView')},
            'nav_bar_title': {'path': UPath(id_ == 'nav_bar_title')},
            'nav_start_TuxIconView': {'path': UPath(id_ == 'nav_start') / UPath(type_ == 'TuxIconView')},
            'power_activity_container_0_2': {'path': UPath(id_ == 'power_activity_container') / 0 / 2},
            'ivBack': {'path': UPath(id_ == 'ivBack')},
            'first_title': {'path': UPath(id_ == 'first_title')},
            'left_fl_0': {'path': UPath(id_ == 'left_fl') / 0},
            'iv_filter_arrow': {'path': UPath(id_ == 'iv_filter_arrow')},
            'tv_see_all': {'path': UPath(id_ == 'tv_see_all')},
            'im_collapse_expand': {'path': UPath(id_ == 'im_collapse_expand')},
            'tv_time_head': {'path': UPath(id_ == 'tv_time_head')},
            'recommend_user_title': {'path': UPath(id_ == 'recommend_user_title')},
            'rv_list_2_SmartAvatarImageView': {
                'path': UPath(id_ == 'rv_list') / 2 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_2_notification_name': {'path': UPath(id_ == 'rv_list') / 2 / UPath(id_ == 'notification_name')},
            'rv_list_2_notification_content': {
                'path': UPath(id_ == 'rv_list') / 2 / UPath(id_ == 'notification_content')},
            'rv_list_2_notification_cover_right': {
                'path': UPath(id_ == 'rv_list') / 2 / UPath(id_ == 'notification_cover_right')},
            'user_id': {'path': UPath(id_ == 'user_id')},
            'nav_start_0': {'path': UPath(id_ == 'nav_start') / 0},
            'notification_head_single_SmartAvatarImageView': {
                'path': UPath(id_ == 'notification_head_single') / UPath(type_ == 'SmartAvatarImageView')},
            'notification_middle_view_notification_name': {
                'path': UPath(id_ == 'notification_middle_view') / UPath(id_ == 'notification_name')},
            'notification_middle_view_notification_content': {
                'path': UPath(id_ == 'notification_middle_view') / UPath(id_ == 'notification_content')},
            'notification_right_view': {'path': UPath(id_ == 'notification_right_view')},
            'rv_list_4_SmartAvatarImageView': {
                'path': UPath(id_ == 'rv_list') / 4 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_4_notification_name': {'path': UPath(id_ == 'rv_list') / 4 / UPath(id_ == 'notification_name')},
            'rv_list_4_notification_content': {
                'path': UPath(id_ == 'rv_list') / 4 / UPath(id_ == 'notification_content')},
            'rv_list_4_notification_cover_right': {
                'path': UPath(id_ == 'rv_list') / 4 / UPath(id_ == 'notification_cover_right')},
            'rv_list_5_notification_name': {'path': UPath(id_ == 'rv_list') / 5 / UPath(id_ == 'notification_name')},
            'rv_list_5_notification_content': {
                'path': UPath(id_ == 'rv_list') / 5 / UPath(id_ == 'notification_content')},
            'rv_list_6_notification_name': {'path': UPath(id_ == 'rv_list') / 6 / UPath(id_ == 'notification_name')},
            'rv_list_6_notification_content': {
                'path': UPath(id_ == 'rv_list') / 6 / UPath(id_ == 'notification_content')},
            'rv_list_6_right_notification_cover': {
                'path': UPath(id_ == 'rv_list') / 6 / UPath(id_ == 'right_notification_cover')},
            'rv_list_7_notification_head_ImageView': {
                'path': UPath(id_ == 'rv_list') / 7 / UPath(id_ == 'notification_head') / UPath(type_ == 'ImageView')},
            'rv_list_7_notification_name': {'path': UPath(id_ == 'rv_list') / 7 / UPath(id_ == 'notification_name')},
            'rv_list_7_notification_content': {
                'path': UPath(id_ == 'rv_list') / 7 / UPath(id_ == 'notification_content')},
            'rv_list_7_notification_cover_right': {
                'path': UPath(id_ == 'rv_list') / 7 / UPath(id_ == 'notification_cover_right')},
            'rv_list_5_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 5 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_6_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 6 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_7_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 7 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_5_notification_right_view': {'path': UPath(id_ == 'rv_list') / 5 / UPath(id_ == 'notification_right_view')},
            'rv_list_6_notification_right_view': {'path': UPath(id_ == 'rv_list') / 6 / UPath(id_ == 'notification_right_view')},
            'rv_list_8_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 8 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_8_notification_name': {'path': UPath(id_ == 'rv_list') / 8 / UPath(id_ == 'notification_name')},
            'rv_list_8_notification_content': {'path': UPath(id_ == 'rv_list') / 8 / UPath(id_ == 'notification_content')},
            'rv_list_8_notification_right_view': {'path': UPath(id_ == 'rv_list') / 8 / UPath(id_ == 'notification_right_view')},
            'rv_list_9_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 9 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_9_notification_name': {'path': UPath(id_ == 'rv_list') / 9 / UPath(id_ == 'notification_name')},
            'rv_list_9_notification_content': {'path': UPath(id_ == 'rv_list') / 9 / UPath(id_ == 'notification_content')},
            'rv_list_9_notification_cover_right': {'path': UPath(id_ == 'rv_list') / 9 / UPath(id_ == 'notification_cover_right')},
            'rv_list_10_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 10 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_10_notification_name': {'path': UPath(id_ == 'rv_list') / 10 / UPath(id_ == 'notification_name')},
            'rv_list_10_notification_content': {'path': UPath(id_ == 'rv_list') / 10 / UPath(id_ == 'notification_content')},
            'rv_list_10_notification_cover_right': {'path': UPath(id_ == 'rv_list') / 10 / UPath(id_ == 'notification_cover_right')},
            'rv_list': {'path': UPath(id_ == 'rv_list')},
            'rv_list_Reply_Comment_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 1 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_Reply_Comment_notification_name': {'path': UPath(id_ == 'rv_list') / 1 / UPath(id_ == 'notification_name')},
            'rv_list_Reply_Comment_notification_content': {'path': UPath(id_ == 'rv_list') / 1 / UPath(id_ == 'notification_content')},
            'rv_list_Reply_Comment_notification_cover_right': {'path': UPath(id_ == 'rv_list') / 1 / UPath(id_ == 'notification_cover_right')},
            'rv_list_1_notification_name': {'path': UPath(id_ == 'rv_list') / 1 / UPath(id_ == 'notification_name')},
            'rv_list_1_notification_content': {'path': UPath(id_ == 'rv_list') / 1 / UPath(id_ == 'notification_content')},
            'rv_list_2_notification_right_view': {'path': UPath(id_ == 'rv_list') / 2 / UPath(id_ == 'notification_right_view')},
            'rv_list_3_SmartAvatarImageView': {'path': UPath(id_ == 'rv_list') / 3 / UPath(type_ == 'SmartAvatarImageView')},
            'rv_list_3_notification_name': {'path': UPath(id_ == 'rv_list') / 3 / UPath(id_ == 'notification_name')},
            'rv_list_3_notification_content': {'path': UPath(id_ == 'rv_list') / 3 / UPath(id_ == 'notification_content')},
            'rv_list_3_notification_right_view': {'path': UPath(id_ == 'rv_list') / 3 / UPath(id_ == 'notification_right_view')},
            'rv_list_root1': {'type': Activity_multi_Btn,'path': UPath(id_ == 'rv_list', visible_ == True)},
            'rv_list_root11': {'type': Activity_multi_Btn1, 'path': UPath(id_ == 'rv_list', visible_ == True)},
            'rv_list_3_notification_cover_right': {'path': UPath(id_ == 'rv_list') / 3 / UPath(id_ == 'notification_cover_right')},
            'rv_list_4_notification_right_view': {'path': UPath(id_ == 'rv_list') / 4 / UPath(id_ == 'notification_right_view')},
            'rv_list_5_notification_cover_right': {'path': UPath(id_ == 'rv_list') / 5 / UPath(id_ == 'notification_cover_right')},
            'followerRecyclerView_2_SmartAvatarImageView': {'path': UPath(id_ == 'followerRecyclerView') / 2 / UPath(type_ == 'SmartAvatarImageView')},
            'followerRecyclerView_2_notification_name': {'path': UPath(id_ == 'followerRecyclerView') / 2 / UPath(id_ == 'notification_name')},
            'followerRecyclerView_2_notification_content': {'path': UPath(id_ == 'followerRecyclerView') / 2 / UPath(id_ == 'notification_content')},
            'followerRecyclerView_2_relationBtn': {'path': UPath(id_ == 'followerRecyclerView') / 2 / UPath(id_ == 'relationBtn')},
            '@testw_9': {'path': UPath(text_ == '@testw_906')},
            'followerRecyclerView_3_SmartAvatarImageView': {'path': UPath(id_ == 'followerRecyclerView') / 3 / UPath(type_ == 'SmartAvatarImageView')},
            'followerRecyclerView_3_notification_name': {'path': UPath(id_ == 'followerRecyclerView') / 3 / UPath(id_ == 'notification_name')},
            'followerRecyclerView_3_notification_content': {'path': UPath(id_ == 'followerRecyclerView') / 3 / UPath(id_ == 'notification_content')},
            'followerRecyclerView_3_relation_label': {'path': UPath(id_ == 'followerRecyclerView') / 3 / UPath(id_ == 'relation_label')},
            'followerRecyclerView_3_relationBtn': {'path': UPath(id_ == 'followerRecyclerView') / 3 / UPath(id_ == 'relationBtn')},

            'feed_list__1_cover': {'path': UPath(id_ == 'feed_list', visible_ == True) / 1 / UPath(id_ == 'cover')},
            'emoji_type_tab_3_tab_iv': {'path': UPath(id_ == 'emoji_type_tab') / 3 / UPath(id_ == 'tab_iv')},
            'Sticker ': {'path': UPath(text_ == 'Sticker sets')},
            'emoji_select_sticker_store': {'path': UPath(id_ == 'emoji_select_sticker_store')},
            'sticker_set_content_list__0_sticker_set_op_btn': {
                'path': UPath(id_ == 'sticker_set_content_list', visible_ == True) / 0 / UPath(
                    id_ == 'sticker_set_op_btn')},
            'emoji_select_sticker_store_red_dot': {'path': UPath(id_ == 'emoji_select_sticker_store_red_dot')},
            'Use': {'path': UPath(text_ == 'Use', visible_ == True)},
            'recyclerView__0': {'path': UPath(id_ == 'recyclerView', visible_ == True) / 0},
            'recycle_view_2_picture_iv': {'path': UPath(id_ == 'recycle_view') / 1 / UPath(id_ == 'picture_iv')},
            'last_sticker_msg': {'path': UPath(id_ == "recycle_view") / 0 / UPath(id_ == "content")},

            "reply_mode": {"path": UPath(id_ == "reply_layout", visible_ == True)},
            "quote_line": {"root": "reply_mode", "path": UPath(id_ == "quote_line", visible_ == True)},
            "reply_text": {"root": "reply_mode", "path": UPath(id_ == "reply_text", visible_ == True)},
            "close_reply_button": {"root": "reply_mode", "path": UPath(id_ == "close_reply", visible_ == True)},
            "latest_quoted_reply": {"root": "latest_msg", "path": UPath(id_ == "reply_layout_2", visible_ == True)},
            "latest_quoted_reply_text": {"root": "latest_quoted_reply", "path": UPath(id_ == "reply_text", visible_ == True)},
            "latest_reply_content": {"root": "latest_quoted_reply", "path": UPath(id_ == "reply_content", visible_ == True)},
            "latest_reply_quote_line": {"root": "latest_reply_content", "path": UPath(id_ == "quote_line", visible_ == True)},
            "latest_reply_video": {"root": "latest_reply_content", "path": UPath(id_ == "video_cover_container", visible_ == True)},
            'msg_et': {'path': UPath(id_ == 'msg_et')},
            'recycle_view_2': {'path': UPath(id_ == 'recycle_view') / 2},
            'chat_root_layout_proxy_0': {'path': UPath(id_ == 'chat_root_layout_proxy') / 0},
            'msg_tv_long rep': {'path': UPath(id_ == 'msg_tv', text_ == 'long replay')},
            'member_list_view_0_avatar_iv': {'path': UPath(id_ == 'member_list_view') / 0 / UPath(id_ == 'avatar_iv')},
            'top_set_btn': {'path': UPath(id_ == "top_set_btn")},
            'top_banner_close': {'path': UPath(id_ == "top_close")},
            'pop_set_btn': {'path':UPath(type_ == "Button")},
            "close_button": {'path':UPath(id_ == "close_button")},
            'system_dingbu_icon': {'path': UPath(id_ == 'recyclerView') / 0 / 0 / UPath(id_ == 'ivChannelIcon')},
            'system_dingbu_name': {'path': UPath(id_ == 'recyclerView') / 0 / 0 / UPath(id_ == 'tvChannelName')},
            'notice_channel_icon': {'path': UPath(id_ == 'recyclerView') / 1 / UPath(id_ == 'ivChannelIcon')},
            'notice_channel_name': {'path': UPath(id_ == 'recyclerView') / 1 / UPath(id_ == 'tvChannelName')},
            'notice_channel_title': {'path': UPath(id_ == 'recyclerView') / 1 / UPath(id_ == 'tvTitle')},
            'notice_channel_content': {'path': UPath(id_ == 'recyclerView') / 1 / UPath(id_ == 'tvContent')},
            'notice_channel_see_more': {'path': UPath(id_ == 'tvSeeMore', visible_ == True)},
            'notice_channel_view_more': {'path': UPath(id_ == "recyclerView") / 1 / UPath(id_ == "tvAction")},
            'notice_channel_dibu_icon': {'path': UPath(id_ == "recyclerView") / 1 / UPath(id_ == "ivArrow")},
            'system_channel_2_title': {'path': UPath(text_ == 'Account updates')},
            'system_channel_2_Back': {'path': UPath(id_ == 'ivBack')},
            'notice_channel_hide': {'path': UPath(id_ == 'tvHide', visible_ == True)},
            "toko_disclaimer_confirm": {"type": Control, "path": UPath(id_ == "disclaimer_confirm_btn")},
            "toko_disclaimer_quite": {"type": Control, "path": UPath(id_ == "disclaimer_quit_btn")},
            "feedback_text": {"type": Control, "path": UPath(id_ == "etFeedback")},
            "feedback_submit": {"type": Control, "path": UPath(id_ == "btnFeedbackSubmit")},
            "feedback_other": {"type": Control, "path": UPath(text_ == "Others")},
            'bottom_leave_group_chat': {'path': UPath(id_ == "tv_action", text_ == "Leave group chat")},
            'last_two_profile': {"type": Control, "path": UPath(id_ == "recycle_view") / 1 / UPath(id_ == 'content', type_ == "FrameLayout")},
            "toko_disclaimer_close": {"type": Control, "path": UPath(id_ == "nav_end")},
            'inner_start_icon': {'path': UPath(id_ == 'inner_start_icon')},
            'search_edit_text': {'path': UPath(id_ == 'search_edit_text')},
            'avatar': {'path': UPath(id_ == 'avatar')},
            'check_box': {'path': UPath(id_ == 'check_box')},
            'label_invited': {'path': UPath(id_ == 'label_invited')},
            'msg_list': {'path': UPath(id_ == "recycle_view")},
        }

    def get_notice_content(self):
        userlist = []
        for item in self["rv_list_root1"].items():
            userlist.append(item["multi_user_content"].text[:-11])
        return userlist

    def get_notice_content11(self):
        userlist = []
        for item in self["rv_list_root11"].items():
            userlist.append(item["multi_user_content11"].text[:-11])
        return userlist

    def ClickNoticePhoto(self,value):
        self["rv_list_root11"].items()[value]["right_notification_cover"].click()
        time.sleep(3)

    def get_notice_name(self):
        userlist = []
        for item in self["rv_list_root1"].items():
            userlist.append(item["multi_user_name"].text[2:-1])
        return userlist

    def click_multi_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].click()

    def existing_multi_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].wait_for_existing()

    def existing_multi_name11(self,index):
        self["rv_list_root11"].items()[index]["multi_user_name"].wait_for_existing()

    def visible_multi_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].wait_for_visible()

    def visible_multi_name11(self,index):
        self["rv_list_root11"].items()[index]["multi_user_name"].wait_for_visible()

    def text_multi_903_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].wait_for_text('^\u200e\u2068903\u2069')

    def text_multi_905_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].wait_for_text('^\u200e\u2068蜡笔小新\u2069$')

    def text_multi_904_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].wait_for_text('^\u200e\u2068哆啦A梦\u2069$')

    def text_multi_likes_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].wait_for_text('^\u200e\u2068派大星\u2069, \u2068哆啦A梦\u2069 and 3 others$')

    def text_multi_906_name(self,index):
        self["rv_list_root1"].items()[index]["multi_user_name"].wait_for_text('^\u200e\u2068派大星\u2069$')

    def text_multi_906_name11(self,index):
        self["rv_list_root11"].items()[index]["multi_user_name"].wait_for_text('^\u200e\u2068派大星\u2069$')

    def click_multi_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_content"].click()

    def click_multi_content11(self,index):
        self["rv_list_root11"].items()[index]["multi_user_content"].click()

    def existing_multi_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_content"].wait_for_existing()

    def existing_multi_content11(self,index):
        self["rv_list_root11"].items()[index]["multi_user_content"].wait_for_existing()

    def visible_multi_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_content"].wait_for_visible()

    def visible_multi_content11(self,index):
        self["rv_list_root11"].items()[index]["multi_user_content"].wait_for_visible()

    def existing_multi_reply_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_reply_content"].wait_for_existing()

    def visible_multi_reply_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_reply_content"].wait_for_visible()

    def text_multi_reply_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_reply_content"].wait_for_text('^wtest_301: 还不到好多度😅$')

    def text_multi_likes_reply_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_reply_content"].wait_for_text('^史迪仔很酷: 。精神百倍束手束脚$')

    def text_multi_QA_reply_content(self,index):
        self["rv_list_root1"].items()[index]["multi_user_reply_content"].wait_for_text('^史迪仔很酷: u好吧v方法$')

    def existing_multi_icon(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon"].wait_for_existing()

    def existing_multi_icon_single(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_single"].wait_for_existing()

    def existing_multi_icon_multi(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_multi"].wait_for_existing()

    def existing_multi_icon_double(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_double"].wait_for_existing()

    def visible_multi_icon(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon"].wait_for_visible()

    def click_multi_icon(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon"].click()

    def click_multi_icon_single(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_single"].click()

    def visible_multi_icon_single(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_single"].wait_for_visible()

    def visible_multi_icon_multi(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_multi"].wait_for_visible()

    def click_multi_icon_multi(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_multi"].click()

    def visible_multi_icon_double(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_double"].wait_for_visible()

    def click_multi_icon_double(self,index):
        self["rv_list_root1"].items()[index]["multi_user_icon_double"].click()

    def existing_multi_cover(self,index2):
        self["rv_list_root2"].items()[index2]["multi_user_cover"].wait_for_existing()

    def visible_multi_cover(self,index2):
        self["rv_list_root2"].items()[index2]["multi_user_cover"].wait_for_visible()

    def enter_detail_page(self):
        self["settings"].click()
        time.sleep(2)

    def check_group_chat_name(self, text):
        return text in self["title"].text

    def get_earliest_msg(self):
        self["message_list_display"].click()
        time.sleep(2)
        self["message_list_display"].swipe(y_direction=-1, swipe_coefficient=8)
        time.sleep(3)

    def send_msg(self, content):
        time.sleep(1)
        self["msg_edit_text"].text = content
        self["send_btn"].click()
        time.sleep(1)
        self["latest_msg"].refresh()
        self["msg_tv"].refresh()
        return self["msg_tv"].text == content

    def click_message_poi_video(self):
        if self["message_poi_video"].wait_for_existing(timeout=5, raise_error=False):
            self["message_poi_video"].click()
        time.sleep(2)

    def send_text_msg(self, message):
        time.sleep(1)
        self["msg_edit_text"].text = message
        self["msg_edit_text"].text = message
        self["send_btn"].wait_for_existing(timeout=1)
        self["send_btn"].click()
        time.sleep(1)
        self["latest_msg"].refresh()
        self["latest_msg"].wait_for_ui_stable()
        if self["msg_tv"].wait_for_existing(timeout=5, raise_error=False):
            self["msg_tv"].refresh()

    def send_emoji(self, index):
        self["emoji_btn"].click()
        self["emoji_list"].items()[index].click()
        self["send_btn"].click()
        time.sleep(1)
        self["latest_msg"].refresh()
        self["msg_tv"].refresh()
        return "😃" == self["msg_tv"].text

    def send_emoji_msg(self, index):
        self["emoji_btn"].click()
        self["emoji_list"].items()[index].click()
        self["send_btn"].click()
        time.sleep(1)
        self["latest_msg"].refresh()
        self["msg_tv"].refresh()

    def send_gif(self):
        if not self["rb_gif"].visible:
            self["emoji_btn"].click()
        self["rb_gif"].click()
        self["gif"].refresh()
        self["gif"].click()
        time.sleep(1)
        return self["gif_content"].wait_for_visible(timeout=10, raise_error=False)

    def send_gif_msg(self):
        self["emoji_btn"].click()
        if self["rb_gif"].wait_for_visible(timeout=3, raise_error=False):
            self["rb_gif"].click()
        time.sleep(5)
        if self["git_panel"].existing:
            self["gif"].wait_for_visible(timeout=6, raise_error=False)
            time.sleep(2)
            self["gif"].click()
        elif self["git_panel1"].existing:
            self["gif1"].wait_for_visible(timeout=6, raise_error=False)
            time.sleep(2)
            self["gif1"].click()

    def send_sticker(self, index):
        self["emoji_btn"].click()
        self["sticker_btn"].click()
        self["emoji_list"].items()[index].click()
        self["latest_msg"].refresh()

    def send_navi_sticker(self, index):
        self["emoji_btn"].click()
        self["navi_sticker_btn"].click()
        self["emoji_list"].items()[index].click()
        self["latest_msg"].refresh()

    def find_poi_more(self):
        for _ in Retry(limit=5, interval=2):
            if self["poi_more_massage"].wait_for_visible(timeout=15, raise_error=False):
                break
            else:
                self.app.get_device().press_back()
                MessageListPanel(root=self.app).find_massage()
        video_detail = VideoDetailPanel(root=self.app)
        for _ in Retry(limit=5, interval=2):
            self["poi_more_massage"].click()
            if video_detail.poi_icon_more.wait_for_existing(timeout=10, raise_error=False):
                break
            else:
                self.app.get_device().press_back()

    def find_poi_long(self):
        for _ in Retry(limit=5, interval=1):
            if self["poi_long_massage"].wait_for_visible(timeout=15, raise_error=False):
                break
            else:
                self.app.get_device().press_back()
                time.sleep(2)
                MessageListPanel(root=self.app).find_massage()
        video_detail = VideoDetailPanel(root=self.app)
        for _ in Retry(limit=5, interval=2):
            self["poi_long_massage"].click()
            if video_detail.poi_subtitle.wait_for_existing(timeout=10, raise_error=False):
                break
            else:
                self.app.get_device().press_back()

    def wait_for_resend_btn(self):
        return self["resend_btn"].wait_for_visible(timeout=2, raise_error=False)

    def swipe_to_latest_msg(self):
        self["chat_page"].swipe(y_direction=1, swipe_coefficient=5)

    def swipe_to_latest_msg_with_forward_icon(self):
        count = 6
        while not self["forward_btn"].wait_for_visible(timeout=2, raise_error=False) and count > 0:
            self["chat_page"].swipe(y_direction=1, swipe_coefficient=5)
            time.sleep(2)
            count = count - 1

    def long_press_last_msg(self):
        self["last_sent_message"].long_click()
        time.sleep(2)

    def long_press_latest_msg(self):
        self["latest_msg"].long_click()
        time.sleep(2)

    def long_press_latest_navi_sticker_msg(self):
        self["sticker_content"].long_click()
        time.sleep(2)

    def double_click_last_msg(self):
        print("rect of latest_msg_title", self["latest_msg_title"].rect)
        print("rect of device", self.rect)
        rect_target = self["latest_msg_title"].rect
        rect_device = self.rect
        x_target = rect_target.left + rect_target.width / 2
        y_target = rect_target.top + rect_target.height / 2
        x_device = rect_device.left + rect_device.width / 2
        y_device = rect_device.top + rect_device.height / 2
        x = x_target - x_device
        y = y_target - y_device
        self.double_click(x, y)
        time.sleep(2)
        return self["msg_liked"].wait_for_visible(timeout=5, raise_error=False)

    def double_click_last_navi_sticker(self):
        print("rect of sticker_content", self["sticker_content"].rect)
        print("rect of device", self.rect)
        rect_target = self["sticker_content"].rect
        rect_device = self.rect
        x_target = rect_target.left + rect_target.width / 2
        y_target = rect_target.top + rect_target.height / 2
        x_device = rect_device.left + rect_device.width / 2
        y_device = rect_device.top + rect_device.height / 2
        x = x_target - x_device
        y = y_target - y_device
        self.double_click(x, y)
        time.sleep(2)
        return self["msg_liked"].wait_for_visible(timeout=5, raise_error=False)

    def open_reaction_panel(self):
        self["msg_liked"].click()
        time.sleep(2)

    def check_msg_disliked(self):
        return self["msg_liked"].wait_for_visible(timeout=2, raise_error=False) == False

    def click_latest_live(self):
        rect = self["latest_msg"].rect
        self.app.testcase.device.click(rect.right-50, rect.center[1])
        time.sleep(5)

    def long_press_latest_live(self):
        self["latest_content"].long_click()
        time.sleep(1)

    def long_press_latest_gif(self):
        self["gif_content"].long_click()

    def long_press_latest_hashtag(self):
        self["hashtag_title"].long_click()

    def long_press_latest_video(self):
        self["latest_video"].long_click()

    def long_press_latest_content(self):
        self["latest_content"].long_click()

    def long_press_latest_content_layout(self):
        self["latest_content_layout"].long_click()

    def click_latest_video(self):
        time.sleep(2)
        self["latest_video"].click()

    def click_forward_latest(self):
        self["forward_latest_btn"].click()

    def click_forward(self):
        if self["forward_btn"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["forward_btn"].click()
            time.sleep(2)
            if not self["search_bar"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["forward_btn1"].click()
                time.sleep(2)
        else:
            self["forward_btn1"].click()
            time.sleep(2)

    def send_to_user(self, user):
        self["search_bar"].click()
        self["search_bar"].text = user
        time.sleep(2)
        for item in self["send_to_list"].items():
            username = item["user_name"].text
            if str(username) == str(user):
                item["user_name"].click()
                if not (self["send_to_user_button"].wait_for_visible(timeout=10, interval=1, raise_error=False)):
                    # If the keyboard pops up, the first click will only close the keyboard and the send button won't show, this line is to make sure the send button shows
                    item["user_name"].click()
                break

        self["send_to_user_button"].click()

    def click_back(self):
        self["back_btn"].click()

    def click_detail_btn(self):
        if self["detail_btn"].wait_for_visible(timeout=10, interval=1, raise_error=False):
            self["detail_btn"].click()
        if self["right_fl"].existing:
            logger.info("click point branch")
            rect = self["right_fl"].rect
            self.app.get_device().click(rect.center[0] + 35, rect.center[1])
        time.sleep(3)

    def pull_up_keyboard(self):
        self["msg_edit_text"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["msg_edit_text"].click()
        self["msg_edit_text"].input("")

    def enter_privacy_settings(self):
        self["top_banner"].click()

    def ocr_click(self, device, text):
        time.sleep(5)
        cv_ = CV()
        pic_path = device.screenshot()
        # target_pic = '/Users/<USER>/Desktop/target_pic.png'
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        # a = cv_.ocr_get_text(target_pic)
        # print(a)
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)

    def forward_latest_video(self):
        if self["forward_latest_btn"].existing:
            self.click_forward_latest()
        else:
            self.long_press_latest_video()
            reply_panel = ReplyMessagePanel(root=self.app)
            reply_panel.click_forward_button()
            time.sleep(2)

    def dislike(self):
        if self["like_icon"].existing:
            self["like_icon"].click()
            time.sleep(2)

    def swipe_to_first_msg(self):
        self["chat_page"].swipe(y_direction=-1, swipe_coefficient=5)

    def send_message_by_forward(self, friend, msg_type, timestamp_flag=False, timestamp_text=""):
        """
        send message//发送消息
        :param friend: send pepole
        :type:str
        :param msg_type:message type
        :type:str
        :param timestamp_flag:Send or not time stamp, default false(no send)//如果发送时间戳，转发消息为倒数第二条
        :type: str
        :param timestamp_text: only replay msg used
        :type: str
        """
        # if self["msg_edit_text"].wait_for_visible(timeout=3, raise_error=False):
        #     self["msg_edit_text"].swipe(y_direction=1, swipe_coefficient=3)
        self.msg_operation(msg_type, msg_operation="long", is_check=False, timestamp_flag=timestamp_flag)
        if msg_type in POI_MSG:
            self.send_msg_by_detail(msg_type=msg_type, friend=friend)
        elif msg_type in GROUP_INVITE_MSG:
            self["enter_group_chat"].wait_for_visible(timeout=10)
            self["enter_group_chat"].click()
            time.sleep(2)
            self.send_text_msg(timestamp_text)
            self["detail_btn"].wait_for_visible(timeout=5)
            self["detail_btn"].click()
            if self["right_fl"].existing:
                logger.info("click point branch")
                rect = self["right_fl"].rect
                self.app.get_device().click(rect.center[0]+35, rect.center[1])
            group_chat_panel = GroupChatDetailPanel(root=self.app)
            group_chat_panel.click_share_btn()
            group_chat_panel.search_friend_and_send(friend)
        else:
            reply_panel = ReplyMessagePanel(root=self.app)
            if msg_type in REPLAY_MSG:
                reply_panel.click_replay()
                self["msg_edit_text"].wait_for_visible(timeout=3)
                self["msg_edit_text"].text = "Replay " + timestamp_text
                self["send_btn"].wait_for_visible(timeout=1)
                self["send_btn"].click()
                return
            else:
                reply_panel.click_forward_button()
            self.send_to_user(friend)

    def msg_operation(self, msg_type, msg_operation, is_check=True, timestamp_flag=False):
        """
        message operation, like or dislike or long press//消息操作，喜欢、不喜欢、长按
        :param msg_type:msg type
        :type: str
        :param msg_operation:like, dislike, long
        :type: str
        :param is_check:check operation result
        :type: bool
        :param timestamp_flag: default false
        :type:str
        """
        time.sleep(1)
        if "like" == msg_operation:
            return self.like(msg_type)
        elif "dislike" == msg_operation:
            self["like"].click()
            self["reaction_username"].wait_for_existing(timeout=3)
            self["reaction_username"].click()
            result = self.like_icon_existing()
            logger.info(f'like icon {"visible" if result else "invisible"}')
            return result
        elif "long" in msg_operation:
            return self.long_msg_and_check(msg_type, is_check, timestamp_flag)

    def like(self, msg_type):
        if msg_type in HASHTAG_MSG:
            self["latest_hashtag"].double_click()
        elif msg_type in STORY_MSG:
            self["latest_video"].double_click()
        elif msg_type in POI_MSG:
            self["latest_poi"].double_click()
        elif msg_type in QA_MSG:
            self["latest_poi"].double_click()
        elif msg_type in LIVE_EVENT_MSG:
            self["latest_live_event"].double_click()
        elif msg_type in GROUP_INVITE_MSG:
            pass
        elif msg_type in REPLAY_MSG:
            pass
        elif msg_type in LYNX_PROFILE_MSG:
            self["last_sent_message"].double_click()
        result = self.like_icon_existing()
        logger.info(f'like icon {"visible" if result else "invisible"}')
        return result

    def like_icon_existing(self):
        self["latest_msg"].refresh()
        self["latest_msg"].wait_for_ui_stable()
        if self["like"].wait_for_existing(timeout=5, raise_error=False):
            return True if self["like"].visible else False
        return False

    def long_msg_and_check(self, msg_type, is_check=True, timestamp_flag=False):
        """
        Long press the msg and check the elements of long press panel
        :param msg_type: msg_type
        :param is_check: bool, default true
        :param timestamp_flag:
        :type:bool
        """
        if timestamp_flag:
            if msg_type in POI_MSG or msg_type in GROUP_INVITE_MSG:
                self.get_msg_control(msg_type, timestamp_flag).click()
            else:
                self.get_msg_control(msg_type, timestamp_flag).long_click()
        else:
            self.get_msg_control(msg_type, timestamp_flag).long_click(duration=4)

        if is_check:
            msg_panel = ReplyMessagePanel(root=self.app)
            result = msg_panel["emoji_bar"].wait_for_visible(timeout=5, raise_error=False)
            logger.info(f'long msg, emoji bar display {"normal" if result else "exception"}')
            self.app.testcase.take_screenshot(self.app.testcase.device, "emoji_bar.jpg")
            self.app.testcase.device.back()
            return result

    def verify_message_send(self, msg_type, timestamp_text):
        """
        verify that the message was successfully sent//验证消息是否发送成功
        校验最后一条消息为发送消息类型，倒数第二条消息与发送时间戳一致，无发送失败图标
        :param msg_type: msg type, eg:["text","hashtag","story","vedio"]
        :type: str
        :param timestamp_text:send time stamp text
        :type:str
        """
        time.sleep(3)
        msg_result = self.verify_msg_sending(self.get_msg_control(msg_type, timestamp_text))

        if msg_type not in GROUP_INVITE_MSG:
            time_text_msg = self["new_chat_first_msg"].text
            logger.info(f"last two msg:{time_text_msg}")
            result = msg_result and timestamp_text == time_text_msg
        else:
            result = msg_result
        logger.info(f'{msg_type} msg {"success" if result else "not found"}')
        return result

    def verify_msg_sending(self, msg_control):
        send_result = True
        send_state_control = self["send_state_text"]
        if send_state_control.wait_for_existing(timeout=5, raise_error=False):
            send_state = send_state_control.text
            logger.info(f'send state:{send_state}')
            if "Not sent" == send_state:
                send_result = False
        return msg_control.wait_for_existing(timeout=5) and send_result

    def get_msg_control(self, msg_type, timestamp_flag):
        if timestamp_flag is True:
            if HASHTAG_MSG in msg_type:
                control = self["latest_two_hashtag"]
            elif STORY_MSG in msg_type:
                control = self["latest_two_story"]
            elif POI_MSG in msg_type:
                control = self["latest_two_poi"]
            elif QA_MSG in msg_type:
                control = self["latest_two_qa"]
            elif LIVE_EVENT_MSG in msg_type:
                control = self["latest_two_live_event"]
            elif REPLAY_MSG in msg_type:
                control = self["msg_tv"]
            elif GROUP_INVITE_MSG in msg_type:
                control = self["latest_two_group"]
            elif LYNX_PROFILE_MSG in msg_type:
                control = self["last_two_profile"]
        else:
            if HASHTAG_MSG in msg_type:
                control = self["latest_hashtag"]
            elif STORY_MSG in msg_type:
                control = self["latest_video"]
            elif POI_MSG in msg_type:
                control = self["latest_poi"]
            elif QA_MSG in msg_type:
                control = self["latest_qa"]
            elif LIVE_EVENT_MSG in msg_type:
                control = self["latest_live_event"]
            elif REPLAY_MSG in msg_type or TEXT_MSG in msg_type:
                control = self["msg_tv"]
            elif GROUP_INVITE_MSG in msg_type:
                control = self["latest_two_group"]
            elif LYNX_PROFILE_MSG in msg_type:
                if type(timestamp_flag) is bool:
                    control = self["latest_msg"]
                else:
                    control = Control(root=self, path=UPath(id_ == "recycle_view")/0/UPath(id_ == "debug_info_tag"))

        if not self.element_invisible_control(control):
            for item in self["msg_list"].items():
                msg_control = Control(root=item, path=UPath(id_ == "content", visible_ == True)/0)
                if msg_control.existing and msg_control.visible:
                    if "Layout" in msg_control.type:
                        control = msg_control
                        break
                    elif STORY_MSG in msg_type and "ImageView" in msg_control.type:
                        control = msg_control
                        break
        return control

    def delete_unwanted_msg(self, story_flag=False):
        """
        删除非当前验证消息类型外的信息
        """
        clear_complete = False
        for _ in Retry(limit=8, interval=1, raise_error=False):  # swipe chat bottom
            self["chat_page"].swipe(y_direction=1, swipe_coefficient=8)

        reply_panel = ReplyMessagePanel(root=self.app)
        for _ in Retry(limit=15, interval=1, raise_error=False):
            if clear_complete:
                return
            self["chat_page"].refresh()
            self["chat_page"].wait_for_ui_stable()
            time.sleep(2)
            for _ in self["chat_page"].items():
                if self["msg_tv"].wait_for_existing(timeout=1, raise_error=False):
                    self["msg_tv"].long_click()
                    reply_panel.click_delete_button()
                elif self["latest_video"].existing and not story_flag:
                    self["latest_video"].long_click()
                    reply_panel.click_delete_button()
                else:
                    clear_complete = True
                break

    def recovery_env(self, msg_type, friend):
        """
        recovery script env
        """
        try:
            current_activity = self.app.current_activity
            if self.window_spec["activity"].split("|")[0] in current_activity or self.window_spec["activity"].split("|")[1] in current_activity:
                self.delete_unwanted_msg(True if msg_type in STORY_MSG else False)
                if self.like_icon_existing():
                    self.msg_operation(msg_type, msg_operation="dislike")
            else:
                for_you_panel = ForYouPanel(root=self.app)
                if not for_you_panel.check_is_expect_activity("SplashActivity"):
                    for_you_panel.go_home()
                for_you_panel.open_tab('Inbox')
                inbox_panel = InboxPanel(root=self.app)
                inbox_panel.enter_user_chat_room(friend)
                self.delete_unwanted_msg(True if msg_type in STORY_MSG else False)
                if self.like_icon_existing():
                    self.msg_operation(msg_type, msg_operation="dislike")
        except:
            logger.info(f"recovery env fail")

    def send_msg_by_detail(self, msg_type, friend):
        if "hashtag" in msg_type:
            hashtag_panel = ChallengeDetailPanel(root=self.app)
            hashtag_panel["share"].wait_for_visible(timeout=10)
            hashtag_panel["share"].click()
            hashtag_panel["bottom_user_list"].wait_for_ui_stable()
            user_list = hashtag_panel["bottom_user_list"].children
            for user in user_list:
                if user.children[1].text == friend:
                    user.click()
                    break
            hashtag_panel["send"].wait_for_visible(timeout=8)
            hashtag_panel["send"].click()
        elif "live_event" in msg_type:
            self.app.testcase.foryou.bottom_user_list.wait_for_ui_stable()
            user_list = self.app.testcase.foryou.bottom_user_list.children
            for user in user_list:
                logger.info(f"bottom friend:{user.children[1].text}")
                if friend[:9] in user.children[1].text:
                    user.click()
                    break
            self.app.testcase.foryou.send.wait_for_visible(timeout=8)
            self.app.testcase.foryou.send.click()
        else:
            if self["right_corner_forward"].wait_for_visible(timeout=10, raise_error=False):
                self["right_corner_forward"].click()
            self["bottom_user_list"].wait_for_ui_stable()
            user_list = self["bottom_user_list"].children
            for user in user_list:
                if user.children[1].text == friend:
                    user.click()
                    break
            self["send"].wait_for_visible(timeout=8)
            self["send"].click()
        time.sleep(1)
        self.app.testcase.device.back()
        time.sleep(3)
        self.app.testcase.take_screenshot(self.app.testcase.device, "Chat_room_page.jpg")

    def element_invisible_control(self, element):
        if not element.wait_for_visible(timeout=3, raise_error=False):
            logger.info(f"{element} invisible handle")
            if self.app.current_activity in self.window_spec["activity"]:
                return False
            self.app.testcase.device.back()
            time.sleep(1)

    def check_report_banner(self):
        """
        检查Message safe pop
        """
        time.sleep(5)
        if self["report_btn"].wait_for_visible(timeout=5, raise_error=False) or \
            self["mark_safe_btn"].wait_for_visible(timeout=5, raise_error=False):
            return True
        else:
            logger.info(f"report banner no display")
            return False

    def check_say_hi_msg(self):
        return self.msg_tv.wait_for_visible(timeout=5)

    def verify_video_undo(self):
        if self["latest_video"].wait_for_visible(timeout=5, raise_error=False):
            return self.click_latest_video()
        return True

    def wait_for_tv_group_title_existing(self):
        self['tv_group_title'].wait_for_existing()

    def wait_for_tv_group_title_visible(self):
        self['tv_group_title'].wait_for_visible()

    def wait_for_tv_group_title_text(self):
        self['tv_group_title'].wait_for_text('^All activity$')

    def click_back_icon_view(self):
        self['backIconView'].click()

    def wait_for_nav_bar_title_existing(self):
        self['nav_bar_title'].wait_for_existing()

    def wait_for_nav_bar_title_visible(self):
        self['nav_bar_title'].wait_for_visible()

    def wait_for_nav_bar_title_text(self):
        self['nav_bar_title'].wait_for_text('^New followers$')

    def wait_for_nav_bar_profile_views_title_text(self):
        self['nav_bar_title'].wait_for_text('^Profile views$')

    def click_nav_start_tux_icon_view(self):
        self['nav_start_TuxIconView'].click()

    def wait_for_power_activity_container_existing(self):
        self['power_activity_container_0_2'].wait_for_existing()

    def wait_for_power_activity_container_visible(self):
        self['power_activity_container_0_2'].wait_for_visible()

    def wait_for_power_activity_container_text(self):
        self['power_activity_container_0_2'].wait_for_text('^System notifications$')

    def click_iv_back(self):
        self['ivBack'].click()

    def wait_for_first_title_text(self):
        self['first_title'].wait_for_text('^spirti8045$')

    def wait_for_story_tianchuang_title_text(self):
        self['first_title'].wait_for_text('^testw_901$')

    def click_left_fl_0(self):
        self['left_fl_0'].click()

    def wait_for_iv_filter_arrow_existing(self):
        self['iv_filter_arrow'].wait_for_existing()

    def wait_for_iv_filter_arrow_visible(self):
        self['iv_filter_arrow'].wait_for_visible()

    def wait_for_tv_see_all_existing(self):
        self['tv_see_all'].wait_for_existing()

    def wait_for_tv_see_all_visible(self):
        self['tv_see_all'].wait_for_visible()

    def wait_for_tv_see_all_text(self):
        self['tv_see_all'].wait_for_text('^View all$')

    def wait_for_im_collapse_expand_existing(self):
        self['im_collapse_expand'].wait_for_existing()

    def wait_for_im_collapse_expand_visible(self):
        self['im_collapse_expand'].wait_for_visible()

    def wait_for_back_icon_view_existing(self):
        self['backIconView'].wait_for_existing()

    def wait_for_back_icon_view_visible(self):
        self['backIconView'].wait_for_visible()

    def wait_for_tv_time_head_existing(self):
        self['tv_time_head'].wait_for_existing()

    def wait_for_tv_time_head_visible(self):
        self['tv_time_head'].wait_for_visible()

    def wait_for_tv_time_head_text(self):
        self['tv_time_head'].wait_for_text('^Yesterday$')

    def wait_for_recommend_user_title_existing(self):
        self['recommend_user_title'].wait_for_existing()

    def wait_for_recommend_user_title_visible(self):
        self['recommend_user_title'].wait_for_visible()
    def check_reply_mode(self):
        return self["reply_mode"].wait_for_existing(timeout=5, raise_error=False)

    def check_reply_quote_line(self):
        return self["quote_line"].wait_for_existing(timeout=5, raise_error=False)

    def check_reply_text(self):
        return self["reply_text"].wait_for_existing(timeout=5, raise_error=False)

    def get_reply_text(self):
        if self["reply_text"].wait_for_existing(timeout=5, raise_error=False):
            return self["reply_text"].text
        else:
            return False

    def check_reply_mode_close_button(self):
        return self["close_reply_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_quoted_reply(self):
        return self["latest_quoted_reply"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_quoted_reply_text(self):
        return self["latest_quoted_reply_text"].wait_for_existing(timeout=5, raise_error=False)

    def get_latest_quoted_reply_text(self):
        return self["latest_quoted_reply_text"].text

    def check_latest_reply_content(self):
        return self["latest_reply_content"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_reply_quote_line(self):
        return self["latest_reply_quote_line"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_reply_video(self):
        return self["latest_reply_video"].wait_for_existing(timeout=5, raise_error=False)

    def long_press_latest_text_msg(self):
        self["msg_tv"].long_click()


    def wait_for_recommend_user_title_text(self):
        self['recommend_user_title'].wait_for_text('^Suggested accounts$')

    def wait_for_rv_list_mentioned_in_Story_smart_avatar_image_view_existing(self):
        self['rv_list_2_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_Story_smart_avatar_image_view_visible(self):
        self['rv_list_2_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_mentioned_in_Story_notification_name_existing(self):
        self['rv_list_2_notification_name'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_Story_notification_name_visible(self):
        self['rv_list_2_notification_name'].wait_for_visible()

    def wait_for_rv_list_mentioned_in_Story_notification_name_text(self):
        self['rv_list_2_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_mentioned_in_Story_notification_content_existing(self):
        self['rv_list_2_notification_content'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_Story_notification_content_visible(self):
        self['rv_list_2_notification_content'].wait_for_visible()

    def wait_for_rv_list_mentioned_in_Story_notification_content_text(self):
        self['rv_list_2_notification_content'].wait_for_text('^mentioned you in a Story$')

    def wait_for_rv_list_mentioned_in_Story_notification_cover_right_existing(self):
        self['rv_list_2_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_Story_notification_cover_right_visible(self):
        self['rv_list_2_notification_cover_right'].wait_for_visible()

    def click_rv_list_mentioned_in_Story_notification_name(self):
        self['rv_list_2_notification_name'].click()

    def wait_for_user_id_text(self):
        self['user_id'].wait_for_text('^@testw_903$')
    def wait_for_user_905_id_text(self):
        self['user_id'].wait_for_text('^@testw_905$')

    def wait_for_user_904_id_text(self):
        self['user_id'].wait_for_text('^@testw_904$')

    def wait_for_user_906_id_text(self):
        self['user_id'].wait_for_text('^@testw_906$')

    def wait_for_user_901_id_text(self):
        self['user_id'].wait_for_text('^@testw_901$')

    def click_nav_start_0(self):
        self['nav_start_0'].click()

    def click_rv_list_mentioned_in_Story_notification_content(self):
        self['rv_list_2_notification_content'].click()

    def wait_for_notification_head_single_smart_avatar_image_view_existing(self):
        self['notification_head_single_SmartAvatarImageView'].wait_for_existing()

    def wait_for_notification_head_single_smart_avatar_image_view_visible(self):
        self['notification_head_single_SmartAvatarImageView'].wait_for_visible()

    def wait_for_notification_middle_view_notification_name_existing(self):
        self['notification_middle_view_notification_name'].wait_for_existing()

    def wait_for_notification_middle_view_notification_name_visible(self):
        self['notification_middle_view_notification_name'].wait_for_visible()

    def wait_for_notification_middle_view_notification_name_text(self):
        self['notification_middle_view_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_notification_middle_view_notification_content_existing(self):
        self['notification_middle_view_notification_content'].wait_for_existing()

    def wait_for_notification_middle_view_notification_content_visible(self):
        self['notification_middle_view_notification_content'].wait_for_visible()

    def wait_for_notification_right_view_existing(self):
        self['notification_right_view'].wait_for_existing()

    def wait_for_notification_right_view_visible(self):
        self['notification_right_view'].wait_for_visible()

    def click_notification_middle_view_notification_name(self):
        self['notification_middle_view_notification_name'].click()

    def click_notification_middle_view_notification_content(self):
        self['notification_middle_view_notification_content'].click()

    def wait_for_rv_list_mentioned_in_comment_view_existing(self):
        self['rv_list_4_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_comment_view_visible(self):
        self['rv_list_4_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_mentioned_in_comment_name_existing(self):
        self['rv_list_4_notification_name'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_comment_name_visible(self):
        self['rv_list_4_notification_name'].wait_for_visible()

    def wait_for_rv_list_mentioned_in_comment_name_text(self):
        self['rv_list_4_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_notification_middle_view_notification_content_text(self):
        self['notification_middle_view_notification_content'].wait_for_text(
            '^tagged you in a video. \u200e\u202d1\u2060d\u2060\u202d$')

    def wait_for_rv_list_mentioned_in_comment_existing(self):
        self['rv_list_4_notification_content'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_comment_visible(self):
        self['rv_list_4_notification_content'].wait_for_visible()

    def wait_for_rv_list_mentioned_in_comment_text(self):
        self['rv_list_4_notification_content'].wait_for_text('^mentioned you in a comment$')

    def wait_for_rv_list_mentioned_in_comment_notification_cover_right_existing(self):
        self['rv_list_4_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_mentioned_in_comment_notification_cover_right_visible(self):
        self['rv_list_4_notification_cover_right'].wait_for_visible()

    def click_rv_list_mentioned_in_comment_notification_name(self):
        self['rv_list_4_notification_name'].click()

    def click_rv_list_mentioned_in_comment_notification_content(self):
        self['rv_list_4_notification_content'].click()

    def click_tv_see_all(self):
        self['tv_see_all'].click()

    def wait_for_rv_list_poll_notification_name_existing(self):
        self['rv_list_5_notification_name'].wait_for_existing()

    def wait_for_rv_list_poll_notification_name_visible(self):
        self['rv_list_5_notification_name'].wait_for_visible()

    def wait_for_rv_list_poll_notification_name_text(self):
        self['rv_list_5_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_poll_notification_content_existing(self):
        self['rv_list_5_notification_content'].wait_for_existing()

    def wait_for_rv_list_poll_notification_content_visible(self):
        self['rv_list_5_notification_content'].wait_for_visible()

    def click_rv_list_poll_notification_name(self):
        self['rv_list_5_notification_name'].click()

    def click_rv_list_poll_notification_content(self):
        self['rv_list_5_notification_content'].click()

    def wait_for_rv_list_comment_mentioned_notification_name_existing(self):
        self['rv_list_6_notification_name'].wait_for_existing()

    def wait_for_rv_list_comment_mentioned_notification_name_visible(self):
        self['rv_list_6_notification_name'].wait_for_visible()

    def wait_for_rv_list_comment_mentioned_notification_name_text(self):
        self['rv_list_6_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_comment_mentioned_notification_content_existing(self):
        self['rv_list_6_notification_content'].wait_for_existing()

    def wait_for_rv_list_comment_mentioned_notification_content_visible(self):
        self['rv_list_6_notification_content'].wait_for_visible()

    def wait_for_rv_list_6_right_notification_cover_existing(self):
        self['rv_list_6_right_notification_cover'].wait_for_existing()

    def wait_for_rv_list_6_right_notification_cover_visible(self):
        self['rv_list_6_right_notification_cover'].wait_for_visible()

    def click_rv_list_comment_mentioned_notification_name(self):
        self['rv_list_6_notification_name'].click()

    def click_rv_list_comment_mentioned_notification_content(self):
        self['rv_list_6_notification_content'].click()

    def wait_for_rv_list_7_notification_head_image_view_existing(self):
        self['rv_list_7_notification_head_ImageView'].wait_for_existing()

    def wait_for_rv_list_7_notification_head_image_view_visible(self):
        self['rv_list_7_notification_head_ImageView'].wait_for_visible()

    def wait_for_rv_list_caption_notification_name_existing(self):
        self['rv_list_7_notification_name'].wait_for_existing()

    def wait_for_rv_list_caption_notification_name_visible(self):
        self['rv_list_7_notification_name'].wait_for_visible()

    def wait_for_rv_list_caption_notification_name_text(self):
        self['rv_list_7_notification_name'].wait_for_text('^\u200e\u2068蜡笔小新\u2069$')

    def wait_for_rv_list_caption_notification_content_existing(self):
        self['rv_list_7_notification_content'].wait_for_existing()

    def wait_for_rv_list_caption_notification_content_visible(self):
        self['rv_list_7_notification_content'].wait_for_visible()

    def wait_for_rv_list_caption_notification_cover_right_existing(self):
        self['rv_list_7_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_caption_notification_cover_right_visible(self):
        self['rv_list_7_notification_cover_right'].wait_for_visible()

    def wait_for_rv_list_poll_smart_avatar_image_view_visible(self):
        self['rv_list_5_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_poll_smart_avatar_image_view_existing(self):
        self['rv_list_5_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_comment_mentioned_smart_avatar_image_view_existing(self):
        self['rv_list_6_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_comment_mentioned_smart_avatar_image_view_visible(self):
        self['rv_list_6_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_caption_smart_avatar_image_view_existing(self):
        self['rv_list_7_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_caption_smart_avatar_image_view_visible(self):
        self['rv_list_7_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_poll_notification_right_view_existing(self):
        self['rv_list_5_notification_right_view'].wait_for_existing()

    def wait_for_rv_list_poll_notification_right_view_visible(self):
        self['rv_list_5_notification_right_view'].wait_for_visible()

    def wait_for_rv_list_comment_mentioned_notification_right_view_existing(self):
        self['rv_list_6_notification_right_view'].wait_for_existing()

    def wait_for_rv_list_comment_mentioned_notification_right_view_visible(self):
        self['rv_list_6_notification_right_view'].wait_for_visible()

    def click_rv_list_caption_notification_name(self):
        self['rv_list_7_notification_name'].click()

    def click_rv_list_caption_notification_content(self):
        self['rv_list_7_notification_content'].click()

    def wait_for_rv_list_response_smart_avatar_image_view_existing(self):
        self['rv_list_8_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_response_smart_avatar_image_view_visible(self):
        self['rv_list_8_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_response_notification_name_existing(self):
        self['rv_list_8_notification_name'].wait_for_existing()

    def wait_for_rv_list_response_notification_name_visible(self):
        self['rv_list_8_notification_name'].wait_for_visible()

    def wait_for_rv_list_response_notification_name_text(self):
        self['rv_list_8_notification_name'].wait_for_text('^\u200e\u2068蜡笔小新\u2069$')

    def wait_for_rv_list_response_notification_content_existing(self):
        self['rv_list_8_notification_content'].wait_for_existing()

    def wait_for_rv_list_response_notification_content_visible(self):
        self['rv_list_8_notification_content'].wait_for_visible()

    def wait_for_rv_list_response_notification_right_view_existing(self):
        self['rv_list_8_notification_right_view'].wait_for_existing()

    def wait_for_rv_list_response_notification_right_view_visible(self):
        self['rv_list_8_notification_right_view'].wait_for_visible()

    def click_rv_list_response_notification_name(self):
        self['rv_list_8_notification_name'].click()
    def click_rv_list_response_notification_content(self):
        self['rv_list_8_notification_content'].click()

    def wait_for_rv_list_commented_story_smart_avatar_image_view_existing(self):
        self['rv_list_9_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_commented_story_smart_avatar_image_view_visible(self):
        self['rv_list_9_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_commented_story_notification_name_existing(self):
        self['rv_list_9_notification_name'].wait_for_existing()

    def wait_for_rv_list_commented_story_notification_name_visible(self):
        self['rv_list_9_notification_name'].wait_for_visible()

    def wait_for_rv_list_commented_story_notification_name_text(self):
        self['rv_list_9_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_commented_story_notification_content_existing(self):
        self['rv_list_9_notification_content'].wait_for_existing()

    def wait_for_rv_list_commented_story_notification_content_visible(self):
        self['rv_list_9_notification_content'].wait_for_visible()

    def wait_for_rv_list_commented_story_notification_cover_right_existing(self):
        self['rv_list_9_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_commented_story_notification_cover_right_visible(self):
        self['rv_list_9_notification_cover_right'].wait_for_visible()

    def click_rv_list_commented_story_notification_name(self):
        self['rv_list_9_notification_name'].click()

    def click_rv_list_commented_story_notification_content(self):
        self['rv_list_9_notification_content'].click()

    def wait_for_rv_list_reply_with_smart_avatar_image_view_existing(self):
        self['rv_list_10_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_reply_with_smart_avatar_image_view_visible(self):
        self['rv_list_10_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_reply_with_notification_name_existing(self):
        self['rv_list_10_notification_name'].wait_for_existing()

    def wait_for_rv_list_reply_with_notification_name_visible(self):
        self['rv_list_10_notification_name'].wait_for_visible()

    def wait_for_rv_list_reply_with_notification_name_text(self):
        self['rv_list_10_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_reply_with_notification_content_existing(self):
        self['rv_list_10_notification_content'].wait_for_existing()

    def wait_for_rv_list_reply_with_notification_content_visible(self):
        self['rv_list_10_notification_content'].wait_for_visible()

    def wait_for_rv_list_reply_with_notification_cover_right_existing(self):
        self['rv_list_10_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_reply_with_notification_cover_right_visible(self):
        self['rv_list_10_notification_cover_right'].wait_for_visible()

    def click_rv_list_reply_with_notification_name(self):
        self['rv_list_10_notification_name'].click()

    def click_rv_list_reply_with_notification_content(self):
        self['rv_list_10_notification_content'].click()

    def scroll_rv_list(self, coefficient_x=0, coefficient_y=0):
        self['rv_list'].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)
        time.sleep(1)

    def wait_for_rv_list_Reply_Comment_smart_avatar_image_view_existing(self):
        self['rv_list_Reply_Comment_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_Reply_Comment_smart_avatar_image_view_visible(self):
        self['rv_list_Reply_Comment_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_Reply_Comment_notification_name_existing(self):
        self['rv_list_Reply_Comment_notification_name'].wait_for_existing()

    def wait_for_rv_list_Reply_Comment_notification_name_visible(self):
        self['rv_list_Reply_Comment_notification_name'].wait_for_visible()

    def wait_for_rv_list_Reply_Comment_notification_name_text(self):
        self['rv_list_Reply_Comment_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_Reply_Comment_notification_content_existing(self):
        self['rv_list_Reply_Comment_notification_content'].wait_for_existing()

    def wait_for_rv_list_Reply_Comment_notification_content_visible(self):
        self['rv_list_Reply_Comment_notification_content'].wait_for_visible()

    def wait_for_rv_list_Reply_Comment_notification_cover_right_existing(self):
        self['rv_list_Reply_Comment_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_Reply_Comment_notification_cover_right_visible(self):
        self['rv_list_Reply_Comment_notification_cover_right'].wait_for_visible()

    def click_rv_list_Reply_Comment_notification_name(self):
        self['rv_list_Reply_Comment_notification_name'].click()
    def click_rv_list_Reply_Comment_notification_content(self):
        self['rv_list_Reply_Comment_notification_content'].click()

    def wait_for_rv_list_commented_now_smart_avatar_image_view_existing(self):
        self['rv_list_2_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_commented_now_smart_avatar_image_view_visible(self):
        self['rv_list_2_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_commented_now_notification_name_existing(self):
        self['rv_list_2_notification_name'].wait_for_existing()

    def wait_for_rv_list_commented_now_notification_name_visible(self):
        self['rv_list_2_notification_name'].wait_for_visible()

    def wait_for_rv_list_commented_now_notification_name_text(self):
        self['rv_list_2_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_commented_now_notification_content_existing(self):
        self['rv_list_2_notification_content'].wait_for_existing()

    def wait_for_rv_list_commented_now_notification_content_visible(self):
        self['rv_list_2_notification_content'].wait_for_visible()

    def wait_for_rv_list_commented_now_notification_right_view_existing(self):
        self['rv_list_2_notification_right_view'].wait_for_existing()

    def wait_for_rv_list_commented_now_notification_right_view_visible(self):
        self['rv_list_2_notification_right_view'].wait_for_visible()

    def wait_for_rv_list_tagged_smart_avatar_image_view_existing(self):
        self['rv_list_3_SmartAvatarImageView'].wait_for_existing()

    def wait_for_rv_list_tagged_smart_avatar_image_view_visible(self):
        self['rv_list_3_SmartAvatarImageView'].wait_for_visible()

    def wait_for_rv_list_tagged_notification_name_existing(self):
        self['rv_list_3_notification_name'].wait_for_existing()

    def wait_for_rv_list_tagged_notification_name_visible(self):
        self['rv_list_3_notification_name'].wait_for_visible()

    def wait_for_rv_list_tagged_notification_name_text(self):
        self['rv_list_3_notification_name'].wait_for_text('^\u200e\u2068903\u2069$')

    def wait_for_rv_list_tagged_notification_content_existing(self):
        self['rv_list_3_notification_content'].wait_for_existing()

    def wait_for_rv_list_tagged_notification_content_visible(self):
        self['rv_list_3_notification_content'].wait_for_visible()

    def wait_for_rv_list_tagged_notification_content_text(self):
        self['rv_list_3_notification_content'].wait_for_text('^tagged you in a video.*')

    def wait_for_rv_list_tagged_notification_right_view_existing(self):
        self['rv_list_3_notification_right_view'].wait_for_existing()

    def wait_for_rv_list_tagged_notification_right_view_visible(self):
        self['rv_list_3_notification_right_view'].wait_for_visible()

    def click_rv_list_tagged_notification_name(self):
        self['rv_list_3_notification_name'].click()

    def click_rv_list_tagged_notification_content(self):
        self['rv_list_3_notification_content'].click()

    def wait_for_rv_list_notification_cover_right_existing(self):
        self['rv_list_3_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_notification_cover_right_visible(self):
        self['rv_list_3_notification_cover_right'].wait_for_visible()

    def click_rv_list_notification_cover_right(self):
        self['rv_list_3_notification_cover_right'].click()

    def wait_for_rv_list_notification_right_view_existing(self):
        self['rv_list_4_notification_right_view'].wait_for_existing()

    def wait_for_rv_list_notification_right_view_visible(self):
        self['rv_list_4_notification_right_view'].wait_for_visible()

    def click_rv_list_notification_right_view(self):
        self['rv_list_4_notification_right_view'].click()

    def wait_for_rv_list_notification_cover_5_right_existing(self):
        self['rv_list_5_notification_cover_right'].wait_for_existing()

    def wait_for_rv_list_notification_cover_5_right_visible(self):
        self['rv_list_5_notification_cover_right'].wait_for_visible()

    def click_rv_list_notification_cover_5_right(self):
        self['rv_list_5_notification_cover_right'].click()

    def wait_for_follower_recycler_view_2_smart_avatar_image_view_existing(self):
        self['followerRecyclerView_2_SmartAvatarImageView'].wait_for_existing()

    def wait_for_follower_recycler_view_2_smart_avatar_image_view_visible(self):
        self['followerRecyclerView_2_SmartAvatarImageView'].wait_for_visible()

    def wait_for_follower_recycler_view_3_smart_avatar_image_view_existing(self):
        self['followerRecyclerView_3_SmartAvatarImageView'].wait_for_existing()

    def wait_for_follower_recycler_view_3_smart_avatar_image_view_visible(self):
        self['followerRecyclerView_3_SmartAvatarImageView'].wait_for_visible()

    def wait_for_follower_recycler_view_2_notification_name_existing(self):
        self['followerRecyclerView_2_notification_name'].wait_for_existing()

    def wait_for_follower_recycler_view_2_notification_name_visible(self):
        self['followerRecyclerView_2_notification_name'].wait_for_visible()

    def wait_for_follower_recycler_view_2_notification_name_text(self):
        self['followerRecyclerView_2_notification_name'].wait_for_text('^\u200e\u2068派大星\u2069$')

    def wait_for_follower_recycler_view_2_notification_content_existing(self):
        self['followerRecyclerView_2_notification_content'].wait_for_existing()

    def wait_for_follower_recycler_view_2_notification_content_visible(self):
        self['followerRecyclerView_2_notification_content'].wait_for_visible()

    def wait_for_follower_recycler_view_2_notification_content_text(self):
        self['followerRecyclerView_2_notification_content'].wait_for_text('^started following you. \u200e\u202d4\u2060/\u20601\u20609\u2060\u202d$')

    def wait_for_follower_recycler_view_2_relation_btn_existing(self):
        self['followerRecyclerView_2_relationBtn'].wait_for_existing()

    def wait_for_follower_recycler_view_2_relation_btn_visible(self):
        self['followerRecyclerView_2_relationBtn'].wait_for_visible()

    def wait_for_follower_recycler_view_2_relation_btn_text(self):
        self['followerRecyclerView_2_relationBtn'].wait_for_text('^Message$')

    def click_follower_recycler_view_2_notification_name(self):
        self['followerRecyclerView_2_notification_name'].click()

    def click_follower_recycler_view_2_notification_content(self):
        self['followerRecyclerView_2_notification_content'].click()

    def click_follower_recycler_view_2_relation_btn(self):
        self['followerRecyclerView_2_relationBtn'].click()

    def wait_for__at_testw_text(self):
        self['@testw_9'].wait_for_text('^@testw_906$')

    def wait_for_follower_recycler_view_3_notification_name_existing(self):
        self['followerRecyclerView_3_notification_name'].wait_for_existing()

    def wait_for_follower_recycler_view_3_notification_name_visible(self):
        self['followerRecyclerView_3_notification_name'].wait_for_visible()

    def wait_for_follower_recycler_view_3_notification_name_text(self):
        self['followerRecyclerView_3_notification_name'].wait_for_text('^\u200e\u2068今天星期五！怎么回事\u2069$')

    def wait_for_follower_recycler_view_3_notification_content_existing(self):
        self['followerRecyclerView_3_notification_content'].wait_for_existing()

    def wait_for_follower_recycler_view_3_notification_content_visible(self):
        self['followerRecyclerView_3_notification_content'].wait_for_visible()

    def wait_for_follower_recycler_view_3_notification_content_text(self):
        self['followerRecyclerView_3_notification_content'].wait_for_text('^started following you. \u200e\u202d4\u2060/\u20601\u20604\u2060\u202d$')

    def wait_for_follower_recycler_view_3_relation_label_existing(self):
        self['followerRecyclerView_3_relation_label'].wait_for_existing()

    def wait_for_follower_recycler_view_3_relation_label_visible(self):
        self['followerRecyclerView_3_relation_label'].wait_for_visible()

    def wait_for_follower_recycler_view_3_relation_label_text(self):
        self['followerRecyclerView_3_relation_label'].wait_for_text('^Friends with  $')

    def wait_for_follower_recycler_view_3_relation_btn_existing(self):
        self['followerRecyclerView_3_relationBtn'].wait_for_existing()

    def wait_for_follower_recycler_view_3_relation_btn_visible(self):
        self['followerRecyclerView_3_relationBtn'].wait_for_visible()

    def wait_for_follower_recycler_view_3_relation_btn_text(self):
        self['followerRecyclerView_3_relationBtn'].wait_for_text('^Follow back$')

    def click_follower_recycler_view_3_notification_name(self):
        self['followerRecyclerView_3_notification_name'].click()

    def click_follower_recycler_view_3_notification_content(self):
        self['followerRecyclerView_3_notification_content'].click()

    def click_follower_recycler_view_relation_btn(self):
        self['followerRecyclerView_3_relationBtn'].click()

    def wait_for_follower_recycler_view_3_send_relation_btn_text(self):
        self['followerRecyclerView_3_relationBtn'].wait_for_text('^Send a 👋$')

    def click_feed_list_cover(self):
        self['feed_list__1_cover'].click()

    def wait_for_emoji_btn_visible(self):
        self['emoji_btn'].wait_for_visible()

    def click_if_emoji_btn_exist(self):
        if self['emoji_btn'].wait_for_visible(timeout=5, raise_error=False):
            self['emoji_btn'].click()
            time.sleep(1)

    def wait_for_emoji_select_sticker_store_red_dot_visible(self):
        self['emoji_select_sticker_store_red_dot'].wait_for_visible()

    def click_if_emoji_select_sticker_store_red_dot_exist(self):
        if self['emoji_select_sticker_store_red_dot'].wait_for_visible(timeout=5, raise_error=False):
            self['emoji_select_sticker_store_red_dot'].click()

    def wait_for_sticker__visible(self):
        self['Sticker '].wait_for_visible()

    def click_if_sticker__exist(self):
        if self['Sticker '].wait_for_visible(timeout=5, raise_error=False):
            self['Sticker '].click()

    def click_if_sticker_set_content_list_sticker_set_op_btn_exist(self):
        if self['sticker_set_content_list__0_sticker_set_op_btn'].wait_for_visible(timeout=10, raise_error=False):
            self['sticker_set_content_list__0_sticker_set_op_btn'].click()
            time.sleep(3)

    def wait_for_use_text(self):
        if self['Use'].wait_for_visible(timeout=5, raise_error=False):
            self['Use'].wait_for_text('^Use$')

    def click_if_use_exist(self):
        if self['Use'].wait_for_visible(timeout=5, raise_error=False):
            self['Use'].click()

    def click_if_recycler_view_exist(self):
        if self['recyclerView__0'].wait_for_visible(timeout=10, raise_error=False):
            self['recyclerView__0'].click()

    def wait_for_recycle_view_picture_iv_visible(self):
        self['recycle_view_2_picture_iv'].wait_for_visible(raise_error=False) or self["last_sticker_msg"].wait_for_visible()

    def check_reply_mode(self):
        return self["reply_mode"].wait_for_existing(timeout=5, raise_error=False)

    def check_reply_quote_line(self):
        return self["quote_line"].wait_for_existing(timeout=5, raise_error=False)

    def check_reply_text(self):
        return self["reply_text"].wait_for_existing(timeout=5, raise_error=False)

    def get_reply_text(self):
        if self["reply_text"].wait_for_existing(timeout=5, raise_error=False):
            return self["reply_text"].text
        else:
            return False

    def check_reply_mode_close_button(self):
        return self["close_reply_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_quoted_reply(self):
        return self["latest_quoted_reply"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_quoted_reply_text(self):
        return self["latest_quoted_reply_text"].wait_for_existing(timeout=5, raise_error=False)

    def get_latest_quoted_reply_text(self):
        return self["latest_quoted_reply_text"].text

    def check_latest_reply_content(self):
        return self["latest_reply_content"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_reply_quote_line(self):
        return self["latest_reply_quote_line"].wait_for_existing(timeout=5, raise_error=False)

    def check_latest_reply_video(self):
        return self["latest_reply_video"].wait_for_existing(timeout=5, raise_error=False)

    def long_press_latest_text_msg(self):
        self["msg_tv"].long_click()

    def click_if_msg_et_exist(self):
        if self['msg_et'].wait_for_visible(timeout=5, raise_error=False):
            self['msg_et'].click()
            time.sleep(1)

    def click_recycle_view_2(self):
        self['recycle_view_2'].click()

    def scroll_chat_root_layout_proxy_0(self, coefficient_x=0, coefficient_y=0):
        self['chat_root_layout_proxy_0'].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)
        time.sleep(1)

    def click_left_fl_image_view(self):
        self['back_btn'].click()
        time.sleep(1)

    def send_multi_msg(self, content="A", msg_type="text", times=3):
        for _ in range(times):
            if "text" in msg_type:
                time.sleep(0.2)
                self["msg_edit_text"].text = content
                self["send_btn"].click()

    def scroll_replay(self, text):
        rect = self["msg_tv"].rect
        self.app.testcase.device.drag(rect.center[0], rect.center[1], 0, rect.center[1], duration=3)
        self["msg_edit_text"].wait_for_existing(timeout=3)
        self["msg_edit_text"].text = text
        self["send_btn"].wait_for_existing(timeout=1)
        self["send_btn"].click()

    def click_if_member_list_view_avatar_iv_exist(self):
        rect = self["msg_edit_text"].rect
        self.app.testcase.device.click(rect.center[0], rect.center[1])
        self.app.testcase.device.shell_command("input text @")
        if self['member_list_view_0_avatar_iv'].wait_for_visible(timeout=5, raise_error=False):
            self['member_list_view_0_avatar_iv'].click()
            time.sleep(1)
        self.app.testcase.take_screenshot(self.app.testcase.device, "Mention_sb.png")
        if self["msg_edit_text"].wait_for_existing(timeout=3, raise_error=False):
            date_str = str(datetime.datetime.now()).split(".")[0]
            self.app.testcase.device.shell_command(f"input text {date_str}")
            self["send_btn"].wait_for_existing(timeout=1)
            self["send_btn"].click()

    def check_no_one_permission(self, tip):
        tip_result = self.top_banner.wait_for_existing(timeout=5, raise_error=True) and self.top_banner.text == tip
        top_set_btn_result = self.top_set_btn.wait_for_visible(timeout=1, raise_error=True)
        top_close_result = self.top_banner_close.wait_for_visible(timeout=1, raise_error=True)
        return tip_result and top_set_btn_result and top_close_result

    def check_set_permission_pop(self):
        result = self.close_button.wait_for_visible(timeout=5, raise_error=True)
        self.close_button.click()
        return result

    def wait_for_dingbu_channel_icon_existing(self):
        self['system_dingbu_icon'].wait_for_existing()

    def wait_for_dingbu_channel_icon_visible(self):
        self['system_dingbu_icon'].wait_for_visible()

    def wait_for_dingbu_channel_icon_click(self):
        self['system_dingbu_icon'].click()

    def wait_for_dingbu_channel_name_existing(self):
        self['system_dingbu_name'].wait_for_existing()

    def wait_for_dingbu_channel_name_visible(self):
        self['system_dingbu_name'].wait_for_visible()

    def wait_for_dingbu_channel_name_text(self):
        self['system_dingbu_name'].wait_for_text('^Account updates$')

    def wait_for_dingbu_channel_name_click(self):
        self['system_dingbu_name'].click()

    def wait_for_notice_channel_icon_existing(self):
        self['notice_channel_icon'].wait_for_existing()

    def wait_for_notice_channel_icon_visible(self):
        self['notice_channel_icon'].wait_for_visible()

    def wait_for_notice_channel_name_existing(self):
        self['notice_channel_name'].wait_for_existing()

    def wait_for_notice_channel_name_visible(self):
        self['notice_channel_name'].wait_for_visible()

    def wait_for_notice_channel_name_text(self):
        self['notice_channel_name'].wait_for_text('^\u2068Account updates\u2069$')

    def wait_for_notice_channel_name_click(self):
        self['notice_channel_name'].click()

    def wait_for_notice_channel_title_existing(self):
        self['notice_channel_title'].wait_for_existing()

    def wait_for_notice_channel_title_visible(self):
        self['notice_channel_title'].wait_for_visible()

    def wait_for_notice_channel_title_text(self):
        self['notice_channel_title'].wait_for_text('^\u2068Your account logged in from a new device\u2069$')

    def wait_for_notice_channel_content_existing(self):
        self['notice_channel_content'].wait_for_existing()

    def wait_for_notice_channel_content_visible(self):
        self['notice_channel_content'].wait_for_visible()

    def wait_for_notice_channel_see_more_existing(self):
        self['notice_channel_see_more'].wait_for_existing()

    def wait_for_notice_channel_see_more_visible(self):
        self['notice_channel_see_more'].wait_for_visible()

    def wait_for_notice_channel_see_more_text(self):
        self['notice_channel_see_more'].wait_for_text('^See more$')

    def wait_for_notice_channel_see_more_click(self):
        self['notice_channel_see_more'].click()

    def wait_for_notice_channel_view_more_existing(self):
        self['notice_channel_view_more'].wait_for_existing()

    def wait_for_notice_channel_view_more_visible(self):
        self['notice_channel_view_more'].wait_for_visible()

    def wait_for_notice_channel_view_more_text(self):
        self['notice_channel_view_more'].wait_for_text('^\u200eView more$')

    def wait_for_notice_channel_dibu_existing(self):
        self['notice_channel_dibu_icon'].wait_for_existing()

    def wait_for_notice_channel_dibu_visible(self):
        self['notice_channel_dibu_icon'].wait_for_visible()

    def wait_for_system_channel_2_title_text(self):
        self['system_channel_2_title'].wait_for_text('^Account updates$')

    def click_system_channel_2_back(self):
        self['system_channel_2_Back'].click()

    def wait_for_notice_channel_hide_existing(self):
        self['notice_channel_hide'].wait_for_existing()

    def wait_for_notice_channel_hide_visible(self):
        self['notice_channel_hide'].wait_for_visible()

    def wait_for_notice_channel_hide_text(self):
        self['notice_channel_hide'].wait_for_text('^Hide$')

    def wait_for_notice_channel_hide_click(self):
        self['notice_channel_hide'].click()

    def click_toko_disclaimer_operation(self, action=1):
        """
        action:1-click confirm, 2-click quit, 3-click close
        """
        if self.toko_disclaimer_confirm.wait_for_visible(timeout=5, raise_error=False):
            if action == 1:
                self.toko_disclaimer_confirm.click()
            elif action == 2:
                self.toko_disclaimer_quite.click()
            elif action == 3:
                self.toko_disclaimer_close.click()

    def send_tako_msg(self, message, emoji_flag=False):
        time.sleep(1)
        nude_pop = NudeContentPopup(root=self.app)
        nude_pop.handle()
        self["msg_edit_text"].text = message
        self["msg_edit_text"].text = message
        if emoji_flag:
            self["emoji_btn"].click()
            time.sleep(1)
            self["emoji_list"].items()[1].click()
        self["send_btn"].wait_for_existing(timeout=5)
        self["send_btn"].click()
        time.sleep(1)

    def send_tako_emoji_msg(self, index):
        self["emoji_btn"].click()
        self["emoji_list"].items()[index].click()
        self["send_btn"].click()

    def click_quick_interaction_word(self, click_flag=True, dislike_flag=False):
        recommend_cell = Control(root=self, path=UPath(id_ == "bot_sug_word_list"))
        recommend_word_one = Control(root=self, path=UPath(id_ == "bot_sug_word_list") / 0)
        rect = self.app.testcase.device.screen_rect
        back_rect = self["back_btn"].rect
        for _ in Retry(limit=3, interval=1, raise_error=False):
            if not recommend_cell.wait_for_visible(timeout=3, raise_error=False):
                self.app.testcase.device.drag(rect.width / 2, rect.height / 3, rect.width / 2, back_rect.bottom,
                                              duration=2)
                time.sleep(1)
        if dislike_flag:
            self.app.testcase.device.click(rect.width / 2, back_rect.bottom + 50)
        if not click_flag:
            time.sleep(1)
            return
        if recommend_cell.wait_for_existing(timeout=10, raise_error=False):
            recommend_word_one.click()

    def tako_like_and_dislike_msg(self, like_flag=True):
        msg_list = Control(root=self, path=UPath(id_ == "recycle_view"))
        rect = self.app.testcase.device.screen_rect
        for item in msg_list.items():
            like_control = Control(root=item, path=UPath(id_ == "like", visible_ == True))
            dislike_control = Control(root=item, path=UPath(id_ == "dislike"))
            if like_flag:
                if like_control.wait_for_visible(timeout=3, raise_error=False):
                    like_rect = like_control.rect
                    logger.info(f"like height:{like_rect.bottom}")
                    if like_rect.bottom < rect.height/5:
                        continue
                    like_control.click()
                    if self["feedback_text"].wait_for_visible(timeout=3, raise_error=False):
                        self["feedback_text"].text = "test like"
                        self["feedback_submit"].click()
                        time.sleep(3)
                        break
            else:
                if dislike_control.wait_for_visible(timeout=3, raise_error=False):
                    dislike_rect = like_control.rect
                    logger.info(f"dislike height:{dislike_rect.bottom}")
                    if dislike_rect.bottom < rect.height / 5:
                        continue
                    dislike_control.click()
                    if self["feedback_text"].wait_for_visible(timeout=3, raise_error=False):
                        self["feedback_text"].text = "test dislike"
                    if self["feedback_other"].wait_for_visible(timeout=3, raise_error=False):
                        self["feedback_other"].click()
                        self["feedback_submit"].click()
                        break

    def check_group_exist(self):
        return not self["right_fl"].wait_for_visible(timeout=8, raise_error=False) \
               or self["bottom_leave_group_chat"].wait_for_visible(timeout=3, raise_error=False)

    def check_profile_card_name_and_follow(self):
        user_name, follow_copywriting = "", ""
        name_control = Control(root=self, path=UPath(id_ == "recycle_view")/0/UPath(id_ == "lynx_layout")/0/0/1)
        follow_control = Control(root=self, path=UPath(id_ == "recycle_view")/0/UPath(desc_ == "Follow", depth=7))
        if name_control.wait_for_visible(timeout=3, raise_error=False):
            user_name = name_control.desc
        if follow_control.wait_for_visible(timeout=3, raise_error=False):
            follow_copywriting = follow_control.desc
        logger.info(f"profile info:{[user_name, follow_copywriting]}")
        return len(user_name) > 0 and len(follow_copywriting) > 0

    def wait_for_inner_start_icon_visible(self):
        self['inner_start_icon'].wait_for_visible()

    def wait_for_search_edit_text_text(self):
        self['search_edit_text'].wait_for_text('.*')

    def wait_for_avatar_visible(self):
        self['avatar'].wait_for_visible()

    def wait_for_user_name_text(self):
        self['user_name'].wait_for_text('^代理团长$')

    def wait_for_check_box_visible(self):
        self['check_box'].wait_for_visible()

    def click_check_box(self):
        self['check_box'].click()

    def wait_for_label_invited_text(self):
        self['label_invited'].wait_for_text('^Invited$')


class ReplyMessagePanel(Window):
    """
    ReplyMessagePanel pops up after long pressing on message in a chat room
    """
    window_spec = {"activity": 'PopupWindow:.*', "process_name": None}

    def get_locators(self):
        return {
            # long press normal and emoji message panel
            'reply_action': {'type': Control, 'path': UPath(id_ == 'action_label', text_ == 'Reply')},
            'forward_action': {'type': Control, 'path': UPath(id_ == 'action_label', text_ == 'Forward')},
            'copy_action': {'type': Control, 'path': UPath(id_ == 'action_label', text_ == 'Copy')},
            'delete_action': {'type': Control, 'path': UPath(id_ == 'action_label', text_ == 'Delete')},
            'first_emoji': {'type': Control, 'path': UPath(id_ == 'emoji_selection_recycler_view') / 0},
            'second_emoji': {'type': Control, 'path': UPath(id_ == 'emoji_selection_recycler_view') / 1},
            'third_emoji': {'type': Control, 'path': UPath(id_ == 'emoji_selection_recycler_view') / 2},
            'fourth_emoji': {'type': Control, 'path': UPath(id_ == 'emoji_selection_recycler_view') / 3},
            'fifth_emoji': {'type': Control, 'path': UPath(id_ == 'emoji_selection_recycler_view') / 4},
            'sixth_emoji': {'type': Control, 'path': UPath(id_ == 'emoji_selection_recycler_view') / 5},

            'emoji_bar': {'type': Control, 'path': UPath(id_ == 'emoji_selection_recycler_view')},

            # long press other message types panel
            'delete_type2_action': {'type': Control, 'path': UPath(id_ == 'action_rv')/2},
            'forward_type2_action': {'type': Control, 'path': UPath(id_ == 'action_rv')/1},
            'action_list': {'type': Control, 'path': UPath(id_ == 'action_rv')},
        }

    def check_reply_button(self):
        self["reply_action"].wait_for_existing(timeout=1, raise_error=False)
        return self["reply_action"].text

    def check_forward_button(self):
        self["forward_action"].wait_for_existing(timeout=1, raise_error=False)
        return self["forward_action"].text

    def check_copy_button(self):
        self["copy_action"].wait_for_existing(timeout=1, raise_error=False)
        return self["copy_action"].text

    def check_delete_button(self):
        self["delete_action"].wait_for_existing(timeout=1, raise_error=False)
        return self["delete_action"].text

    def click_delete_button(self):
        if not self["delete_action"].wait_for_visible(timeout=3, raise_error=False):
            self["action_list"].swipe(x_direction=1, swipe_coefficient=3)
        self["delete_action"].click()

    def click_forward_button(self):
        self["forward_action"].wait_for_visible(timeout=5, raise_error=False)
        self["forward_action"].click()

    def check_first_emoji(self):
        self["first_emoji"].wait_for_visible(timeout=1, raise_error=False)
        return self["first_emoji"].text

    def check_second_emoji(self):
        self["second_emoji"].wait_for_visible(timeout=1, raise_error=False)
        return self["second_emoji"].text

    def check_third_emoji(self):
        self["third_emoji"].wait_for_visible(timeout=1, raise_error=False)
        return self["third_emoji"].text

    def check_fourth_emoji(self):
        self["fourth_emoji"].wait_for_visible(timeout=1, raise_error=False)
        return self["fourth_emoji"].text

    def check_fifth_emoji(self):
        self["fifth_emoji"].wait_for_visible(timeout=1, raise_error=False)
        return self["fifth_emoji"].text

    def check_sixth_emoji(self):
        self["sixth_emoji"].wait_for_visible(timeout=1, raise_error=False)
        return self["sixth_emoji"].text

    def click_replay(self):
        self["reply_action"].wait_for_existing(timeout=1)
        self["reply_action"].click()

    def check_long_emoji(self):
        allow_emoji = {
            "heart": "❤️",
            "Laugh": "😂",
            "Love": "🥰",
            "Cry": "😭",
            "Blush": "😳",
            "Anger": "😡",
            "Gift": "🎉",
            "Think": "🤔",
            "Bless": "🙏",
            "Pnaic": "😮",
            "Snot": "😢",
            "Angry": "😠",
            "Like": "👍",
        }
        result = {}
        for index, item in enumerate(self.emoji_bar.items()):
            if index == 0 or index == 1:
                allowed_emoji = [allow_emoji["heart"], allow_emoji["Laugh"]]
                compare_result = item.text in allowed_emoji
                result.update({str(index+1): compare_result})
                if compare_result is False:
                    logger.info(f'The {index+1} emoji {item.text} not allowed {allowed_emoji}')
            elif index == 2:
                allowed_emoji = [allow_emoji["Angry"], allow_emoji["Gift"], allow_emoji["Anger"], allow_emoji["Love"]]
                compare_result = item.text in allowed_emoji
                result.update({str(index + 1): compare_result})
                if compare_result is False:
                    logger.info(f'The {index+1} emoji {item.text} not allowed {allowed_emoji}')
            elif index == 3:
                allowed_emoji = [allow_emoji["Cry"], allow_emoji["Snot"]]
                compare_result = item.text in allowed_emoji
                result.update({str(index + 1): compare_result})
                if compare_result is False:
                    logger.info(f'The {index+1} emoji {item.text} not allowed {allowed_emoji}')
            elif index == 4:
                allowed_emoji = [allow_emoji["Like"], allow_emoji["Pnaic"], allow_emoji["Bless"], allow_emoji["Blush"]]
                compare_result = item.text in allowed_emoji
                result.update({str(index + 1): compare_result})
                if compare_result is False:
                    logger.info(f'{item.text} not allowed {allowed_emoji}')
            elif index == 5:
                allowed_emoji = [allow_emoji["Think"], allow_emoji["Bless"], allow_emoji["Like"]]
                compare_result = item.text in allowed_emoji
                result.update({str(index + 1): compare_result})
                if compare_result is False:
                    logger.info(f'The {index+1} emoji {item.text} not allowed {allowed_emoji}')

        logger.info(f"long emoji check result:{result}")
        if False in result.values():
            return False
        else:
            return True


class PrivateChatDetailPanel(Window):
    """
    Chat Detail Page For Private Message
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.im.sdk.chatdetail.ui.activity.FriendChatDetailActivity"}
    MUTE = "Mute"
    BLOCK = "Block"
    PIN_TO_TOP = "Pin"

    def get_locators(self):
        return {
            "user_avatar": {"type": Control, "path": UPath(id_ == 'avatar_iv')},
            "user_name_main": {"type": Control, "path": UPath(id_ == 'name_tv')},
            "user_name_detail": {"type": Control, "path": UPath(id_ == 'detail_tv')},
            "mute_copywrite": {"type": Control, "path": UPath(id_ == 'tv_mute')},
            "mute_btn": {"type": Control, "path": UPath(id_ == 'switch_mute')},
            "pin_copywrite": {"type": Control, "path": UPath(id_ == 'tv_stick_top')},
            "pin_btn": {"type": Control, "path": UPath(id_ == 'switch_stick_top')},
            "block_copywrite": {"type": Control, "path": UPath(id_ == 'block_tv', visible_ == True)},
            "block_btn": {"type": Control, "path": UPath(id_ == 'block_more_switch')},
            "report_copywrite": {"type": Control, "path": UPath(id_ == 'report_tv')},
            "create_group_chat": {"type": Control, "path": UPath(id_ == "layout_create_group")},
            "block_confirm_btn": {"type": Control, "path": UPath(id_ == "action_area")},
            "search_et": {"type": Control, "path": UPath(id_ == "search_et")},
            "send_btn": {"type": Control, "path": UPath(id_ == "send_btn")},
            "search_result_list": {"type": UserList, "path": UPath(id_ == "result_list")},
        }

    def chat_detail_operation(self, operation, toggle_flag=False):
        """
        group chat operation
        :param operation: leave,end,rename,pin,mute...
        :type:str
        :param toggle_flag:
        :type:bool
        """
        # switch toggle index
        if operation in self.MUTE:
            index = 0
        elif operation in self.PIN_TO_TOP:
            index = 1
        elif operation in self.BLOCK:
            index = 2
        group_chat_detail_panel = GroupChatDetailPanel(root=self.app)
        if "VideoRecordNewActivity" in self.app.current_activity:
            self.app.testcase.log_info("control enter add video record page")
            self.app.testcase.device.back()
        if operation == self.MUTE:
            switch_control = self["mute_btn"]
            switch_control.wait_for_visible(timeout=5)
            group_chat_detail_panel.switch_toggle_operation(switch_control, toggle_flag, index)
        elif operation == self.PIN_TO_TOP:
            switch_control = self["pin_btn"]
            switch_control.wait_for_visible(timeout=5)
            group_chat_detail_panel.switch_toggle_operation(switch_control, toggle_flag, index)
        elif operation == self.BLOCK:
            switch_control = self["block_btn"]
            switch_control.wait_for_visible(timeout=5)
            group_chat_detail_panel.switch_toggle_operation(switch_control, toggle_flag, index)
            if self["block_confirm_btn"].wait_for_visible(timeout=3, raise_error=False):
                rect = self["block_confirm_btn"].rect
                btn_width = rect.width/2
                self.app.testcase.device.click(rect.left+btn_width+btn_width/2, rect.center[1])
        time.sleep(1)
        self.app.get_device().screenshot(f"private_detail_{operation}_result.png")

    def recovery_chat_detail_env(self):
        try:
            for _ in Retry(limit=2, interval=1, raise_error=False):
                self.chat_detail_operation(self.MUTE, toggle_flag=False)
                time.sleep(1)
                self.chat_detail_operation(self.BLOCK, toggle_flag=False)
        except BaseException as e:
            logger.info(f"recovery chat datail env fail, reason:{e}")
        self.app.get_device().screenshot("recovery_env_result.png")

    def create_chat(self, user):
        if self["search_et"].wait_for_visible(timeout=3, raise_error=False):
            self["search_et"].text = user
            time.sleep(1)
            for item in self["search_result_list"].items():
                if item["user_name"].text == user:
                    item["user_name"].click()
                    break
        self["send_btn"].wait_for_visible(timeout=3)
        self["send_btn"].click()


class GroupChatDetailPanel(Window):
    """
    Chat Detail Page For Group Chat
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.im.sdk.chatdetail.group.activity.GroupChatDetailActivity"}
    MUTE = "Mute"
    RENAME = "rename"
    PIN_TO_TOP = "Pin"
    APPROVAL_JOIN = "Approval"
    CREATE_GROUP_CHAT = "Create"
    LEAVE = "Leave"
    END = "End"
    UNMUTE = "UnMute"
    REPORT = "REPORT"

    def get_locators(self):
        return {
            "detail_title": {"type": Control, "path": UPath(text_ == "Details")},
            "group_name": {"type": Control, "path": UPath(id_ == 'im_group_setting_title')},
            "share_invitation_copywriting": {"type": Control, "path": UPath(id_ == "im_detail_list") / 1 / UPath(id_ == "title_tv")},
            "share_invitation_arrow": {"type": Control, "path": UPath(id_ == 'im_segment_option_layout_arrow')},
            "mute_copywriting": {"type": Control, "path": UPath(id_ == 'im_detail_list')/2/UPath(id_ == 'title_tv')},
            "mute_btn": {"type": Control, "path": UPath(id_ == 'im_detail_list')/2/UPath(id_ == 'tux_textCell_accessory')},
            "pin_copywriting": {"type": Control, "path": UPath(text_ == 'Pin to top')},
            "pin_btn": {"type": Control, "path": UPath(id_ == 'im_detail_list')/3/UPath(id_ == 'tux_textCell_accessory')},
            "report_btn": {"type": Control, "path": UPath(text_ == 'Report')},
            "leave_group_btn": {"type": Control, "path": UPath(text_ == "Leave group chat")},
            "leave_group_sub": {"type": Control, "path": UPath(id_ == 'im_detail_list')/6/UPath(id_ == 'subtitle_tv')},
            "end_group_btn": {"type": Control, "path": UPath(text_ == "End group chat")},
            "end_group_sub": {"type": Control, "path": UPath(id_ == 'im_detail_list')/7/UPath(id_ == 'subtitle_tv')},
            "approval_copywriting": {"type": Control, "path": UPath(id_ == 'im_detail_list')/4/UPath(id_ == 'title_tv')},
            "approval_btn": {"type": Control, "path": UPath(id_ == 'im_detail_list')/4/UPath(id_ == 'tux_textCell_accessory')},
            "edit_icon": {"type": Control, "path": UPath(id_ == "im_group_setting_title_edit")},
            "editor": {"type": Control, "path": UPath(id_ == "im_group_setting_editor")},
            "done": {"type": Control, "path": UPath(text_ == "Done")},
            "back": {"type": Control, "path": UPath(id_ == "left_fl")},
            "leave": {"type": Control, "path":  UPath(text_ == "Leave")},
            "admin": {"type": Control, "path": UPath(text_ == "Choose admin")},
            "choose_and_leave": {"type": Control, "path": UPath(id_ == "choose_admin_dialog_confirm")},
            "admin_chosen": {"type": Control, "path": UPath(id_ == "choose_admin_item_nickname", text_ == "group_member_01")},

            "tiktok_friends": {"type": Control, "path": UPath(text_ == "TikTok friends")},
            "group_share_message": {"type": Control, "path": UPath(text_ == "Message", visible_ == True)},
            "search_input": {"type": Control, "path": UPath(id_ == "search_et")},
            "user_list": {"type": Control, "path": UPath(id_ == "contact_list_recyclerview")},
            "send": {"type": Control, "path": UPath(id_ == "tv_send")},
            "bottom_input": {"type": Control, "path": UPath(id_ == "edit_msg")},
            "send_to": {"type": Control, "path": UPath(id_ == "title", text_ == "Send to")},
            "report_item": {"type": Control, "path": UPath(text_ == "Report", visible_ == True)},
            "delete": {"type": Control, "path": UPath(text_ == "Delete")},
        }

    def edit_group_name(self, text):
        self["edit_icon"].click()
        self["editor"].input(text)
        time.sleep(1)
        self["done"].click()

    def exit_detail_page(self):
        self["back"].click()
        time.sleep(1)

    def leave_group_btn_without_admin(self):
        self["leave_group_btn"].click()
        time.sleep(1)
        self["leave"].click()
        time.sleep(2)

    def leave_group_chat_with_admin(self):
        self["leave_group_btn"].click()
        time.sleep(1)
        self["admin"].click()
        time.sleep(1)
        self["admin_chosen"].click()
        time.sleep(1)
        self["choose_and_leave"].click()
        time.sleep(2)

    def search_friend_and_send(self, friend):
        time.sleep(5)
        self["search_input"].wait_for_existing(timeout=3)
        self["search_input"].click()
        self["search_input"].text = friend
        time.sleep(1)
        rect = self["user_list"].rect
        self.app.testcase.device.click(rect.center[0], rect.top + 90)
        # self.app.testcase.foryou.ocr_click_text(self.app.testcase.device, friend, index=1)
        time.sleep(3)
        self["bottom_input"].wait_for_existing(timeout=1)
        self["bottom_input"].text = str(datetime.datetime.now()).split(".")[0]
        self["send"].click()
        self.app.testcase.device.back()
        self.app.testcase.device.back()

    def leave_group_chat(self):
        self["leave_group_btn"].wait_for_existing(timeout=3)
        self["leave_group_btn"].click()
        if self["leave"].wait_for_existing(timeout=3, raise_error=False):
            self["leave"].click()

    def group_chat_operation(self, operation, group_name="", group_member="", toggle_flag=False):
        """
        group chat operation
        :param operation: leave,end,rename,pin,mute...
        :type:str
        :param group_name:
        :type:str
        :param group_member:
        :type:str
        :param toggle_flag:
        :type:bool
        """
        # switch toggle index
        if operation in self.MUTE:
            index = 0
        elif operation in self.PIN_TO_TOP:
            index = 1

        if operation == self.MUTE:
            switch_control = self["mute_btn"]
            switch_control.wait_for_visible(timeout=5)
            self.switch_toggle_operation(switch_control, toggle_flag, index)
        elif operation == self.PIN_TO_TOP:
            switch_control = self["pin_btn"]
            switch_control.wait_for_visible(timeout=5)
            self.switch_toggle_operation(switch_control, toggle_flag, index)
        elif operation == self.REPORT:
            self["report_item"].wait_for_visible(timeout=5)
            self["report_item"].click()
        elif operation == self.LEAVE:
            self.leave_group_chat()
        elif operation == self.END:
            self["end_group_btn"].wait_for_visible(timeout=5)
            self["end_group_btn"].click()
            if self["delete"].wait_for_existing(timeout=3, raise_error=False):
                self["delete"].click()
        elif operation == self.RENAME:
            if len(group_name) == 0:
                raise BaseException(f"{group_name} not null")
            self["group_name"].wait_for_visible(timeout=5)
            self["group_name"].click()
            self.app.testcase.device.shell_command(f"input text {group_name}")
            self["done"].click()
            time.sleep(3)
        elif operation == self.CREATE_GROUP_CHAT:
            private_chat_panel = PrivateChatDetailPanel(root=self.app)
            private_chat_panel["create_group_chat"].wait_for_visible(timeout=3)
            private_chat_panel["create_group_chat"].click()
            private_chat_panel.create_chat(group_member)
        time.sleep(1)
        self.app.testcase.take_screenshot(self.app.testcase.device, f"{operation}_result.png")

    def check_member_relation(self, member_list, friend_flag=True):
        group_detail_control = Control(root=self, path=UPath(id_ == "im_detail_list"))
        group_detail_control.wait_for_visible(timeout=3)
        group_detail_control.swipe(y_direction=1, swipe_coefficient=5)
        group_detail_control.wait_for_visible(timeout=3)

        friends = "Friends"
        check_result = []
        for member in member_list:
            for group_item in group_detail_control.items():
                if "RelativeLayout" in group_item.type:
                    if len(group_item.children) > 2:
                        member_name = group_item.children[1].children[0].text
                        if "com.bytedance.tux.icon.TuxIconView" in group_item.children[1].children[1].type:
                            continue
                        member_relation = group_item.children[1].children[1].text
                        logger.info(f"{member_name},{member_relation}")
                        if member in member_name:
                            if friend_flag:
                                if friends not in member_relation:
                                    logger.info(f"{member} no muf friend")
                                    check_result.append(member)
                            else:
                                if friends in member_relation and group_item.children[1].children[1].visible:
                                    logger.info(f"{member} is muf friend")
                                    check_result.append(member)
        return check_result

    def switch_toggle_status(self, path, target_path, index=0, similarity=0.6):
        cv_tool = CV(self.app.get_device())
        try:
            res = cv_tool.universal_ui_detection(path)
            logger.info(f"compare result:{res}")
            position = res["result"]["Switch"][index]
            self.img_crop(path, position)
            result = self.compare_pic(path, target_path)
            if index == 0:
                toggle_name = "Mute"
            elif index == 1:
                toggle_name = "Pin"
            elif index == 2:
                if "private" in self.app.testcase.test_name:
                    toggle_name = "Block"
                else:
                    toggle_name = "Approval"
            if result["similarity"] > similarity:
                logger.info(f"{toggle_name} switch toggle state is open")
                return True
            else:
                logger.info(f"{toggle_name} switch toggle state is close")
                return False
        except Exception as e:
            logger.info(f"get switch toggle state fail, reason:{e}")
            return False

    def img_crop(self, path, box):
        try:
            img = Image.open(path)
        except:
            logger.info("open img path fail")
        try:
            image1 = img.crop(box)
            image1.save(path)
        except:
            logger.info("crop img fail")

    def switch_toggle_operation(self, switch_control, toggle_flag=False, index=0):
        time.sleep(3)
        img_path = self.app.testcase.device.screenshot()
        open_path = RESOURCE_PATH + r"/switch_open.jpg"
        state = self.switch_toggle_status(img_path, open_path, index)
        time.sleep(1)
        if toggle_flag:
            if not state:
                switch_control.click()
        else:
            if state:
                switch_control.click()
        time.sleep(3)

    def compare_pic(self, target_pic_path, src_pic_path):
        """
        Compare the similarity of two pictures
        """
        logger.info(f"tar path:{target_pic_path}, src path:{src_pic_path}")
        cv_tool = CV()
        try:
            result = cv_tool.fg_sim(target_pic_path, src_pic_path)
            logger.info(f"compare result:{result}")
            return result
        except Exception as e:
            logger.info(f"compare fail reason:{e}")
            return False

    def recovery_group_detail_env(self):
        try:
            self.group_chat_operation(self.MUTE, toggle_flag=False)
        except BaseException as e:
            logger.info(f"recovery group detail env fail, reason:{e}")

    def click_share_btn(self):
        self["share_invitation_copywriting"].wait_for_visible(timeout=3)
        self["share_invitation_copywriting"].click()
        share_pop = Control(root=self, path=UPath(id_ == "share_channel_list"))
        share_pop.wait_for_visible(timeout=3)
        for share_item in share_pop.items():
            if "LinearLayoutCompat" in share_item.type:
                if len(share_item.children) == 2:
                    share_way = share_item.children[1].text
                    if "TikTok" in share_way or "Message" in share_way:
                        share_item.click()
                        break


class TikTokPlatformNotification(Window):
    """
    TikTok Platform Notification is responsible for displaying all the secondary in-app notifications
    that includes share video and share sound notifications
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.notification.MusNotificationDetailActivity"}

    def get_locators(self):
        return {
            "notification_refresh_layout": {"type": FrameLayout, "path": UPath(id_ == "notification_loading_layout")},
            # Share Video to TikTok from Web - Secondary Level In-App Notification Selector "TikTok Platform"
            "video_ready_to_edit": {"type": Control, "path": UPath(~text_ == ".*Your video from.*", index=0)},
            # Share Sound to TikTok from Web - Secondary Level In-App Notification Selector "TikTok Platform"
            "sound_ready_to_edit": {"type": Control,
                                    "path":
                                        UPath(id_ == "notification_root") / UPath(id_ == "notification_right_view") /
                                            UPath(id_ == "notification_button", visible_ == True, index=0)}
        }

    def click_video_ready_to_edit(self):
        # Click on the second level in-app notification: Your video from <app name> is ready:
        # Edit your video before sharing to TikTok
        self["video_ready_to_edit"].wait_for_visible(timeout=20, interval=1, raise_error=True)
        self["video_ready_to_edit"].wait_for_ui_stable()
        self["video_ready_to_edit"].click()
        self["video_ready_to_edit"].wait_for_invisible(timeout=20, interval=1, raise_error=False)

    def click_sound_ready_to_edit(self):
        # Click on the second level in-app notification: Your sound from <app name> is ready:
        # Create your TikTok video
        self["sound_ready_to_edit"].wait_for_visible(timeout=20, interval=1, raise_error=True)
        self["sound_ready_to_edit"].click()
        self["sound_ready_to_edit"].wait_for_invisible(timeout=20, interval=1, raise_error=False)


class NoticeCard(Control):
    """
    单条信息控件
    """
    def get_locators(self):
        return{
            "time_head": {"path": UPath(id_ == 'tv_time_head', visible_ == True)},
            # 左侧头像
            "head": {"path": UPath(~id_ == 'notification_head.*', visible_ == True, index=0)},
            # "bell_head": {"path": UPath(id_ == 'notification_head', type_ == 'com.ss.android.ugc.aweme.base.ui.AvatarImageView')},
            # "single_head": {"path": UPath(id_ == 'notification_head_single')},
            # "double_head": {"path": UPath(id_ == 'notification_head_double', visible_ == True)},
            # "comment_head": {"path": UPath(type_ == 'com.ss.android.ugc.aweme.notification.view.NotificationRootLayout')/UPath(id_ == 'notification_head')},

            # 中间内容
            "notification_middle_view": {"path": UPath(~id_ == 'notification_middle_view|notification_text_container', visible_ == True)},
            # 中间内容组成
            "name": {"path": UPath(id_ == 'notification_name', visible_ == True)},
            "mutual_relation": {"path": UPath(id_ == 'mutual_relation_view', visible_ == True)},
            "content": {"path": UPath(id_ == 'notification_content', visible_ == True)},
            "reply": {"path": UPath(id_ == 'notification_reply_content', visible_ == True)},
            "name_title": {"path": UPath(id_ == 'title_container', visible_ == True)},
            "left_container": {"path": UPath(id_ == 'notification_left_container', visible_ == True)},
            # "middle_view_none": {"path": UPath(type_ == 'android.widget.LinearLayout', visible_ == True)},  # 这个有问题
            # "text_container": {"path": UPath(id_ == 'notification_text_container', visible_ == True)},
            # "cover_bottom": {"path": UPath(~id_ == 'notification_cover(_bottom)?')},

            # 右侧图标
            "right_cover": {"path": UPath(id_ == 'right_notification_cover', visible_ == True)},
            "cover": {"path": UPath(id_ == 'notification_cover_right', visible_ == True)},
            "btn_follow": {"path": UPath(id_ == 'btn_follow_user', visible_ == True)},
            "watch": {"path": UPath(id_ == 'notification_button', visible_ == True)},
            # "arrow": {"path": UPath(id_ == 'notification_arrow', visible_ == True)},

        }

    # 左侧头像
    def get_head_rect(self):
        if self["head"].existing:
            return self["head"].rect

    # 中间内容
    def get_middle_view_rect(self):
        if self["notification_middle_view"].existing:
            return self["notification_middle_view"].rect
        if not self["name"].existing:
            return self.get_notification_content_rect()
        # elif self["name_title"].existing and self["content"].existing:
        #     from uibase.common import Rectangle
        #     return Rectangle(self["name_title"].rect.left, self["name_title"].rect.top,
        #                      max(self["name_title"].rect.width, self["name_title"].rect.width),
        #                      self["name_title"].rect.height+self["name_title"].rect.height)
        else:
            return None

    # 中间内容 - name
    def get_notification_name_rect(self):
        if self["name"].existing:
            return self["name"].rect
        else:
            return None

    # 中间内容 - multi_relation
    def get_notification_mutual_relation_rect(self):
        if self["mutual_relation"].existing:
            return self["mutual_relation"].rect
        else:
            return None

    # 中间内容 - content
    def get_notification_content_rect(self):
        if self["content"].existing:
            return self["content"].rect
        else:
            return None

    # 中间内容 - reply
    def get_notification_reply_rect(self):
        if self["reply"].existing:
            return self["reply"].rect
        else:
            return None

    # 右侧图片
    def get_right_cover_rect(self):
        if self["right_cover"].existing:
            return self["right_cover"].rect
        elif self["cover"].existing:
            return self["cover"].rect
        else:
            return None

    # 右侧按钮
    def get_right_btn_rect(self):
        if self["btn_follow"].existing:
            return self["btn_follow"].rect
        elif self["watch"].existing:
            return self["watch"].rect
        else:
            return None

    def general_check(self):
        head_rect = self.get_head_rect()
        middle_view_rect = self.get_middle_view_rect()
        notification_name_rect = self.get_notification_name_rect()
        notification_mutual_relation_rect = self.get_notification_mutual_relation_rect()
        notification_content_rect = self.get_notification_content_rect()
        notification_reply_rect = self.get_notification_reply_rect()

        # 获取中间内容组件的高度(阈值为48dp)
        middle_view_over_height = False
        if middle_view_rect and middle_view_rect.height > 48 * 3:
            middle_view_over_height = True
        else:
            if notification_name_rect:
                height = notification_name_rect.height
                if notification_content_rect:
                    height += notification_content_rect.height
                    if notification_mutual_relation_rect:
                        height += notification_mutual_relation_rect.height
                        if notification_reply_rect:
                            height += notification_reply_rect.height
                if height > 48 * 3:
                    middle_view_over_height = True
            if self["name_title"].existing:
                height = self["name_title"].rect.height
                if notification_content_rect:
                    height += notification_content_rect.height
                if height > 48 * 3:
                    middle_view_over_height = True

        right_cover_rect = self.get_right_cover_rect()
        right_btn_rect = self.get_right_btn_rect()

        # 全局高度检查 - 中间内容文案高度低于48dp时，全局高度为68dp
        if not middle_view_over_height:
            # 高度为68dp
            if self.rect.height != 68 * 3:
                ui_scene = UIScene(self).dump()
                logger.info("Notice控件高度异常，全局控件 = {}, 中间控件 = {}".format(self.rect, middle_view_rect))
                return False, ui_scene
        # 头像检查 - 左侧头像宽度和高度均为48dp，距离上边缘10dp，左边缘16dp
        if head_rect.width != 48 * 3 or head_rect.height != 48 * 3:
            ui_scene = UIScene(self).dump()
            logger.info("头像控件大小异常，头像控件 = {}".format(self["header"].rect))
            return False, ui_scene
        if head_rect.top - self.rect.top != 10 * 3:
            ui_scene = UIScene(self).dump()
            logger.info("头像控件与上侧边界距离异常，全局控件 = {}, 头像控件 = {}".format(self.rect, head_rect))
            return False, ui_scene
        if head_rect.left - self.rect.left != 16 * 3:
            ui_scene = UIScene(self).dump()
            logger.info("头像控件与左侧边界距离异常，全局控件 = {}, 头像控件 = {}".format(self.rect, head_rect))
            return False, ui_scene
        # 头像检查 - 中间内容组件高度低于48dp时，距离下边缘也是10dp
        if not middle_view_over_height:
            if self.rect.bottom - head_rect.bottom != 10 * 3:
                ui_scene = UIScene(self).dump()
                logger.info("头像控件与下侧边界距离异常，全局控件 = {}, 头像控件 = {}".format(self.rect, head_rect))
                return False, ui_scene

        # 中间内容组件检查
        if middle_view_rect:
            # 中间内容中间内容组件检查 - 判断中间内容控件与左组件间距为12dp
            if middle_view_rect.left - head_rect.right != 12 * 3:
                ui_scene = UIScene(self).dump()
                logger.info("头像控件与中间内容组件距离异常，头像控件 = {}, 内容组件 = {}".format(head_rect, middle_view_rect))
                return False, ui_scene
            # 判断中间内容控件与右组件间距为12dp
            if right_cover_rect and right_cover_rect.left - middle_view_rect.right != 12 * 3 and right_cover_rect.left - self["left_container"].rect.right != 12 * 3:
                ui_scene = UIScene(self).dump()
                logger.info("中间内容组件与右侧封面控件距离异常，中间内容组件 = {}, 封面控件 = {}".format(middle_view_rect, right_cover_rect))
                return False, ui_scene
            # 中间内容组件高度大于48dp时，中间内容控件与头像控件齐高，距离上边缘10dp
            if middle_view_over_height and head_rect.top != middle_view_rect.top:
                ui_scene = UIScene(self).dump()
                logger.info("头像控件与中间内容组件高度未对齐，头像控件 = {}, 内容组件 = {}".format(head_rect, middle_view_rect))
                return False, ui_scene
            # 中间内容组件高度大于48dp时，距离下边缘也是10dp
            if middle_view_over_height and self.rect.bottom - middle_view_rect.bottom != 10 * 3:
                ui_scene = UIScene(self).dump()
                logger.info("中间内容组件与下侧边界距离异常，全局控件 = {}, 中间内容组件 = {}".format(self.rect, middle_view_rect))
                return False, ui_scene
            # 中间内容组件检查 - 具体内容元素之间检查
            if notification_name_rect and notification_content_rect:
                # 如果multi_relation组件不存在，则name和content组件无间隙
                if not notification_mutual_relation_rect and notification_name_rect.bottom != notification_content_rect.top:
                    ui_scene = UIScene(self).dump()
                    logger.info("name控件与content控件距离异常，name = {}, content = {}".format(notification_name_rect, notification_content_rect))
                    return False, ui_scene
                # 如果multi_relation组件存在，则距离上下各为2dp
                if notification_mutual_relation_rect:
                    if notification_mutual_relation_rect.top - notification_name_rect.bottom != 2 * 3:
                        ui_scene = UIScene(self).dump()
                        logger.info("name控件与mutual_relation控件距离异常，name = {}, mutual_relation = {}".format(notification_name_rect, notification_mutual_relation_rect))
                        return False, ui_scene
                    if notification_content_rect.top - notification_mutual_relation_rect.bottom != 2 * 3:
                        ui_scene = UIScene(self).dump()
                        logger.info("mutual_relation控件与content控件距离异常，mutual_relation = {}, content = {}".format(notification_mutual_relation_rect, notification_content_rect))
                        return False, ui_scene
                # 如果存在reply，reply控件与content间距4dp
                if notification_reply_rect and notification_reply_rect.top - notification_content_rect.bottom != 4 * 3:
                    ui_scene = UIScene(self).dump()
                    logger.info("reply控件与content控件距离异常，reply = {}, content = {}".format(
                        notification_reply_rect, notification_content_rect))
                    return False, ui_scene

        # 右侧封面检查 - 封面为42dp*56dp，右边缘为16dp
        if right_cover_rect:
            if right_cover_rect.width != 42 * 3 or (right_cover_rect.height != 167 and right_cover_rect.height != 168):
                ui_scene = UIScene(self).dump()
                logger.info("右侧封面控件大小异常，封面控件 = {}".format(right_cover_rect))
                return False, ui_scene
            if self.rect.right - right_cover_rect.right != 16 * 3:
                ui_scene = UIScene(self).dump()
                logger.info("封面控件与右侧边界距离异常，全局控件 = {}, 封面控件 = {}".format(self.rect, right_cover_rect))
                return False, ui_scene
            # 右侧封面检查 - 中间内容文案高度大于48dp时，距离上边缘是10dp
            if right_cover_rect and middle_view_over_height:
                if right_cover_rect.top - self.rect.top != 10 * 3:
                    ui_scene = UIScene(self).dump()
                    logger.info("封面控件与上侧边界距离异常，全局控件 = {}, 封面控件 = {}".format(self.rect, right_cover_rect))
                    return False, ui_scene
            # 右侧封面检查 - 中间内容文案高度低于48dp时，距离上下边缘也是6dp
            if right_cover_rect and not middle_view_over_height:
                if self.rect.bottom - right_cover_rect.bottom != 6 * 3 and self.rect.bottom - right_cover_rect.bottom != 19:
                    ui_scene = UIScene(self).dump()
                    logger.info("封面控件与下侧边界距离异常，全局控件 = {}, 封面控件 = {}".format(self.rect, right_cover_rect))
                    return False, ui_scene
                if right_cover_rect.top - self.rect.top != 6 * 3:
                    ui_scene = UIScene(self).dump()
                    logger.info("封面控件与上侧边界距离异常，全局控件 = {}, 封面控件 = {}".format(self.rect, right_cover_rect))
                    return False, ui_scene
        ui_scene = UIScene(self).dump()
        return True, ui_scene

    def middle_message_num(self):
        mes_num = 0
        if self["name_title"].existing:
            mes_num += 1
        if self["content"].existing:
            if self["content"].rect.height < 60:
                mes_num += 1
            else:
                mes_num += 2
        if self["mutual_relation"].existing:
            mes_num += 1
        if self["reply"].existing:
            mes_num += 1
        if self["cover_bottom"].existing:
            mes_num += 3
        return mes_num

    def middle_center_check(self):
        result = True
        head_rect = self.get_head_rect()
        right_view_rect = self.get_right_cover_rect()
        content_rect = self.get_notification_content_rect()
        name_rect = self.get_notification_name_rect()
        if self.middle_message_num() < 3:
            if right_view_rect:
                center_result = head_rect.center[1] - right_view_rect.center[1]
                if center_result > 0.5 or center_result < -0.5:
                    logger.info(
                        "头像和右侧控件中间高度不一致，head_rect = {}, bnt_rect = {}".format(head_rect, right_view_rect))
                    result = False
            if not self.head_rect_check():
                logger.info("头异常")
                result = False
            if not self.cover_rect_check():
                logger.info("cover异常")
                result = False
            if name_rect:
                if name_rect.top <= head_rect.top:
                    logger.info(
                        "头像低于名字高度，head.rect = {}, name.rect = {}".format(head_rect, name_rect))
                    result = False
                if name_rect.bottom != head_rect.center[1]:
                    logger.info(
                        "头像中间和名字底部高度不一致，head.rect = {}, name.rect = {}".format(head_rect, name_rect))
                    result = False
            else:
                if content_rect.top <= head_rect.top:
                    logger.info(
                        "头像低于中间信息高度，head.rect = {}, content.rect = {}".format(head_rect, content_rect))
                    result = False
                if content_rect.center[1] != head_rect.center[1]:
                    logger.info(
                        "头像和中间信息中间高度不一致，head.rect = {}, content.rect = {}".format(head_rect, content_rect))
                    result = False
        else:
            if right_view_rect:
                if self["cover"].existing or self["right_cover"].existing:
                    if right_view_rect.top != head_rect.top:
                        logger.info(
                            "头像和cover高度不一致,多行信息，head.rect = {}, btn.rect = {}".format(head_rect, right_view_rect))
                        result = False
                    if not head_rect.height < right_view_rect.height:
                        logger.info(
                            "cover身高小于头像,多行信息，head.rect = {}, btn.rect = {}".format(head_rect, right_view_rect))
                        result = False
                else:
                    if head_rect.center[1] != right_view_rect.center[1]:
                        logger.info(
                            "头像和右侧控件中间高度不一致,多行信息，head.rect = {}, btn.rect = {}".format(head_rect, right_view_rect))
                        result = False
            if name_rect:
                if head_rect.top != name_rect.top:
                    logger.info(
                        "头像和名字高度不一致,多行信息，head_rect = {}, name_rect = {}".format(head_rect, name_rect))
                    result = False
            else:
                if content_rect.top != head_rect.top:
                    logger.info(
                        "头像和中间信息高度不一致，多行信息，head.rect = {}, content.rect = {}".format(head_rect, content_rect))
                    result = False
            if head_rect.top - self.rect.bottom != 30:
                logger.info(
                    "框架和头像高度差不一致，多行信息，self.rect = {}, head.rect = {}".format(self.rect, head_rect))
                result = False
            if not self["cover_bottom"].existing:
                if self["left_container"].existing:
                    left_container_rect = self["left_container"].rect
                    if self.rect.bottom - left_container_rect.bottom != 30:
                        logger.info("框架和中间信息高度差异常，多行信息，right_btn = {}, left_container.rect = {}".format(right_view_rect, left_container_rect))
                        result = False
                else:
                    if self.rect.bottom - content_rect.bottom != 30:
                        logger.info(
                            "框架和中间信息高度差异常，多行信息，self.rect = {}, content.rect = {}".format(self.rect, content_rect))
                        result = False
        return result


class NoticeInboxList(Control):
    elem_path = UPath(id_ == "notification_root")
    elem_class = NoticeCard

class SuggestedAccountList(BasePanel):

    """inbox suggested account"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            'suggest_account_title': {'type': Control, 'path': UPath(id_ == 'tv_group_title')}, # suggest account 模块
            'suggest_account_others_ava': {'type': Control, 'path': UPath(id_ == 'mainTv')}, #第三个：他人头像
            'suggest_account_follow': {'type': Control, 'path': UPath(id_ == 'relationBtn', text_ == 'Follow')}, #第三个：follow按钮
            'suggest_account_unfollow': {'type': Control, 'path': UPath(id_ == 'relationBtn', text_ == 'Following')},   # 第三个：following按钮
            'suggest_account_delete': {'type': Control, 'path': UPath(id_ == 'blockIv')}, #第一个：❎按钮
            'suggest_account_followback': {'type': Control, 'path': UPath(id_ == 'relationBtn', visible_ == True)},   # followback按钮
            'suggest_account_message': {'type': Control, 'path': UPath(text_ == 'Message')},
            'suggest_account_othersprofile_follow': {'type': Control, 'path': UPath(id_ == 'profile_btn_extra')},
            'suggest_account_show': {'type': Control, 'path': UPath(id_ == 'tv_name')},
            'close_suggest_popup_btn': {'type': Control, 'path': UPath(id_ == 'close')}
        }

    def click_message_btn(self):
        self['suggest_account_message'].click()

    def click_others_ava(self):
        self['suggest_account_others_ava'].click()

    def click_follow_btn(self):
        self['suggest_account_follow'].click()

    def click_unfollow_btn(self):
        self['suggest_account_unfollow'].click()

    def click_delete(self):
        self["suggest_account_delete"].wait_for_visible(timeout=5, interval=1, raise_error=True)
        self['suggest_account_delete'].click()

    def click_followback_btn(self):
        self['suggest_account_followback'].click()

    def close_suggest_popup(self):
        value = self["close_suggest_popup_btn"].wait_for_existing(timeout=5, interval=1, raise_error=False)
        if value is True:
            self["close_suggest_popup_btn"].click()

class Follow_requests(Window):
    """关注请求页面"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.notification.MusFollowRequestDetailActivity"}

    def get_locators(self):
        return {
            'user_header': {'type': Control, 'path': UPath(id_ == 'iv_header')},
            'user_name': {'type': Control, 'path': UPath(id_ == 'tv_username')},
            'nick_name': {'type': Control, 'path': UPath(id_ == 'tv_nickname')},
            'delete_button': {'type': Control, 'path': UPath(id_ == 'bt_delete')},
            'accept_button': {'type': Control, 'path': UPath(id_ == 'bt_accept')}
        }

    def check_user_header(self):
        return self["user_header"].wait_for_existing(timeout=5, raise_error=False)

    def check_user_name(self):
        return self["user_name"].wait_for_existing(timeout=5, raise_error=False)

    def check_nick_name(self):
        return self["nick_name"].wait_for_existing(timeout=5, raise_error=False)

    def check_delete_button(self):
        return self["delete_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_accept_button(self):
        return self["accept_button"].wait_for_existing(timeout=5, raise_error=False)


class ToastPop(Window):
    """
    创建群聊失败toast
    """
    window_spec = {"activity": ".*"}

    def get_locators(self):
        return{
            'toast': {"type": Control, "path": UPath(id_ == "tv_content")},
        }

    def check_toast_exist(self):
        toast_text = "You can't add group_member_04"
        if self['toast'].wait_for_visible(timeout=10, raise_error=False):
            if toast_text in self["toast"].text:
                return True
        return False


class ChallengeDetailPanel(Window):
    """
    hashtag detail page
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.challenge.ui.ChallengeDetailActivity.*"}

    def get_locators(self):
        return {
            "share": {"path": UPath(id_ == "share_btn")},
            "bottom_user_list": {"path": UPath(id_ == "recycle_view")},
            "send": {"path": UPath(id_ == "send")},
        }
class Activity_multi_Upath(Control):
    def get_locators(self):
        return {
            "multi_user_icon": {"path": UPath(id_ == "notification_head")},
            "multi_user_icon_single": {"path": UPath(id_ == "notification_head_single")},
            "multi_user_icon_multi": {"path": UPath(id_ == "notification_head_multi")},
            "multi_user_icon_double": {"path": UPath(id_ == "notification_head_double")},
            "multi_user_name": {"path": UPath(id_ == "notification_name")},
            "multi_user_content": {"path": UPath(id_ == "notification_content")},
            "multi_user_reply_content": {"path": UPath(id_ == "notification_reply_content")},
            "multi_user_cover": {"path": UPath(id_ == "notification_cover_right")}
        }


class Activity_multi_Btn(Control):
    elem_path = UPath(id_ == "notification_root", visible_ == True) / UPath(id_ == "notification_left_container", visible_ == True)
    elem_class = Activity_multi_Upath


class Activity_multi_Upath1(Control):
    def get_locators(self):
        return {
            "right_notification_cover": {"path": UPath(id_ == "right_notification_cover", visible_ == True)},
            "multi_user_content11": {"path": UPath(id_ == "notification_content")},
            "multi_user_name": {"path": UPath(id_ == "notification_name")},
            "multi_user_icon": {"path": UPath(id_ == "notification_head")}
        }


class Activity_multi_Btn1(Control):
    elem_path = UPath(id_ == "notification_root", visible_ == True)
    elem_class = Activity_multi_Upath1






class VideoPlayerReplyPanel(Window):
    """
    Reply panel in video panel
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.detail.ui.DetailActivity.*"}

    def get_locators(self):
        return {
            "reply_input_container": {"path": UPath(id_ == "reply_input_container", visible_ == True)},
            # quick emoji panel
            "quick_emoji": {"root": "reply_input_container", "path": UPath(id_ == "quick_emoji_layout")},
            # reply mode panel
            "video_reply_layout": {"root": "reply_input_container", "path": UPath(id_ == "video_ref_layout", visible_ == True)},
            "vid_thumbnail": {"root": "video_reply_layout", "path": UPath(id_ == "video_thumbnail", visible_ == True)},
            "vid_reply_text": {"root": "video_reply_layout", "path": UPath(id_ == "video_reply_text", visible_ == True)},
            # reply input panel
            "edit_input_layout": {"root": "reply_input_container", "path": UPath(id_ == "edit_input_layout", visible_ == True)},
            "message_input": {"root": "edit_input_layout", "path": UPath(id_ == "msg_et", visible_ == True)},
            "emoji_btn": {"root": "edit_input_layout", "path": UPath(id_ == "emoji_btn", visible_ == True)},
            "send_btn": {"root": "edit_input_layout", "path": UPath(id_ == "send_btn", visible_ == True)},

        }

    def check_reply_input_container(self):
        return self["reply_input_container"].wait_for_existing(timeout=5, raise_error=False)

    def check_quick_emoji_panel(self):
        return self["quick_emoji"].wait_for_existing(timeout=5, raise_error=False)

    def check_video_reply_layout(self):
        return self["video_reply_layout"].wait_for_existing(timeout=5, raise_error=False)

    def check_video_thumbnail(self):
        return self["vid_thumbnail"].wait_for_existing(timeout=5, raise_error=False)

    def check_video_reply_text(self):
        return self["vid_reply_text"].wait_for_existing(timeout=5, raise_error=False)

    def get_video_reply_text(self):
        return self["vid_reply_text"].text

    def check_reply_input_panel(self):
        return self["edit_input_layout"].wait_for_existing(timeout=5, raise_error=False)

    def check_emoji_btn(self):
        return self["emoji_btn"].wait_for_existing(timeout=5, raise_error=False)

    def check_send_btn(self):
        return self["send_btn"].wait_for_existing(timeout=5, raise_error=False)

    def click_send_btn(self):
        self["send_btn"].click()

    def send_reply_message(self, content):
        self["message_input"].input(content)
        self.click_send_btn()


class User(Control):
    def get_locators(self):
        return {
            "user_name": {"path":UPath(id_ == "name_tv", visible_ == True)}
        }


class UserList(Control):
    elem_path = UPath(type_ == "ConstraintLayout")
    elem_class = User

