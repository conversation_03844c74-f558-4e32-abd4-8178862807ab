# -*- coding:utf-8 _*-
import datetime
import time

from shoots_android.control import *
from shoots_cv.cv import CV
from uibase.controls import PopupBase
from uibase.upath import id_
from uibase.upath import UPath, class_
from uibase.web import WebElement, Webview, WebTextEdit


class DebugPanel(Window):
    """Debug页
    """

    window_spec = {"activity": "com.bytedance.ies.assistant.AssistantActivity|com.ss.android.ugc.aweme.local_test.impl.search.SearchDebugActivity"}

    def get_locators(self):
        return {
            'search': {'type': TextEdit, 'path': UPath(id_ == "search_edit", visible_ == True)},
            "search_config": {'type': TextEdit, 'path': UPath(id_ == 'search')},
            'first_search_result': {'type': Control, 'path': UPath(id_ == 'list', visible_ == True) / 0},
            'toggle_switch': {'type': Control, 'path': UPath(id_ == 'toggle', visible_ == True)},
            'treatment_v5': {'type': Control, 'path': UPath(text_ == 'Treatment V5')},
            'sure': {'type': Control, 'path': UPath(id_ == 'sure')},
            'sure_restart': {'type': Control, 'path': UPath(id_ == 'button1')},
            "nickname_libra": {'type': Control, 'path': UPath(id_ == 'title', visible_ == True)},
            "libra_switch": {'type': Control,
                             'path': UPath(id_ == 'content', type_ == 'android.widget.LinearLayout') / UPath(
                                 id_ == 'toggle')},
            "nickname_03_libra": {'type': Control, 'path': UPath(id_ == 'group') / 2 / UPath(id_ == 'text')},

            'change_locale': {'type': TextEdit, 'path': UPath(id_ == 'input')},
            'change_locale_input': {'type': TextEdit, 'path': UPath(id_ == 'input', index=-1)},
            'change_locale_save': {'type': Button, 'path': UPath(id_ == 'save')},
            'CAStore_Switch': {'type': Button, 'path': UPath(id_ == 'switcher', visible_ == True)},
            'Sky_Eye': {'type': TextEdit, 'path': UPath(text_ == 'Sky Eye')},
            'back_btn': {'type': Button, 'path': UPath(id_ == 'back')},
            "search_debug_tool": {"type": Button, "path": UPath(id_ == 'list', visible_ == True)},
            "check": {"type": Button, "path": UPath(id_ == 'btn_check')},
            "done": {"type": Control, "path": UPath(id_ == 'tv_proress', visible_ == True)},
            "open": {"type": Button, "path": UPath(id_ == 'toggle', visible_ == True)},
            "normal": {"type": Control, "path": UPath(text_ == 'normal')},
            # PPE 泳道配置
            "config_tab": {"type": Control, "path": UPath(desc_ == 'Config')},
            "ppe_switcher": {"type": Control, "path": UPath(text_ == 'PPE Env')},
            "ppe_lane_entrance": {'type': Control, 'path': UPath(id_ == 'input')},
            # BOE 泳道配置
            "boe_switcher": {"type": Control, "path": UPath(text_ == 'BOE Env')},
            "boe_lane_entrance": {'type': Control, 'path': UPath(id_ == 'input')},
            "boe_lane_config": {"type": TextEdit,
                                "path": UPath(id_ == 'input', type_ == 'androidx.appcompat.widget.AppCompatEditText')},
            "ppe_lane_config": {"type": TextEdit, "path": UPath(id_ == 'input', type_ == 'androidx.appcompat.widget.AppCompatEditText')},
            "save": {"type": Control, "path": UPath(~id_ == 'save|sure')},
            "tool_title": {"type": Control, "path": UPath(text_ == "Enable HybridDevTool", visible_ == True)},
            "feed_debug": {"type": Control, "path": UPath(id_ == 'switcher', visible_ == True)},
            "back": {"type": Control, "path": UPath(id_ == 'back')},
            "feed_debug_search": {"type": Control, "path": UPath(id_ == "search_edit", visible_ == True)},
            # BPEA
            "bpea_tool": {"path": UPath(text_ == 'BPEA Tool', visible_ == True)},
            'Config': {'path': UPath(text_ == 'Config')},
            'config_search': {'type': TextEdit, 'path': UPath(id_ == 'search')},
            "open_debug_title": {"path": UPath(id_ == "openDebugTitle")},
            'inbox_redesign_phase3': {'path': UPath(text_ == "Inbox Redesign Phase 3", id_ == "title")},
            'toggle': {'path': UPath(id_ == 'toggle', visible_ == True)},
            'Control': {'path': UPath(text_ == 'Control')},
            'v1': {'path': UPath(~text_ == 'V1.*')},
            'v3': {'path': UPath(~text_ == 'V3.*')},
            'restart': {'path': UPath(text_ == 'RESTART')},
            # AB Clone
            "ab_clone": {"type": Control,
                         "path": UPath(type_ == 'com.bytedance.ies.abmock.debugtool.submain.ABCloneView') / 0},
            "config_text_field_val": {"type": Control, "path": UPath(id_ == "editTextJson")},

            # Mock GPS
            "mock_switcher": {"type": Control, "path": UPath(id_ == 'switcher', visible_ == True)},
            "open_mock_page": {"type": Control, "path": UPath(id_ == 'title', visible_ == True)},

            'edit_frame': {'type': TextEdit, 'path': UPath(id_ == "editTextJson")},

            "search_input": {'type': TextEdit, 'path': UPath(id_ == "searchET")},
            "toggle_btn": {"type": Control, "path": UPath(id_ == "toggle_btn")},
            "sure_btn": {"type": Control, "path": UPath(id_ == "sure_button_view")},
        }

    def wait_for_done(self):
        for _ in Retry(timeout=1000, message="check result failed"):
            time.sleep(500)
            if self["done"].wait_for_visible(timeout=1000, raise_error=False):
                return True
            else:
                break

    def output_result_path_text(self):
        time.sleep(10)
        return self["done"].text

    def config_search_pager_fragment_preinflate(self):
        self["config_tab"].click()
        time.sleep(3)
        self["search_config"].input("search pager fragment preinflate")
        time.sleep(3)
        self["nickname_libra"].click()
        time.sleep(2)
        self["open"].click()
        time.sleep(1)
        self["normal"].click()
        time.sleep(1)
        self["save"].click()

    def check_search_debug_tool_btn(self):
        return self["search_debug_tool"].wait_for_existing(timeout=5, raise_error=False)

    def click_search_debug_tool(self):
        self["search"].input("Search Debug Tool")
        time.sleep(3)
        self["search_debug_tool"].click()

    def click_check_btn(self):
        self["check"].click()

    def close_CAStore(self):
        self["search"].input("CAStore")
        time.sleep(1)
        self['CAStore_Switch'].click()
        time.sleep(1)

    def click_ab_clone(self):
        self["config_tab"].click()
        if self["ab_clone"].wait_for_existing(timeout=5, raise_error=False):
            self["ab_clone"].click()
            time.sleep(2)

    def change_region(self, region):
        self["search"].text = "change locale"
        self["change_locale"].click()
        time.sleep(1)
        self["change_locale_input"].text = region
        self["change_locale_save"].click()
        # region_input = RegionInputPanel(root=self.app)
        # region_input.input_region(region)
        time.sleep(1)

    def open_privtrust_sensitive_api(self):
        self['search'].input("sky")
        time.sleep(1)
        self['Sky_Eye'].click()
        time.sleep(1)

    def change_tool(self):
        self["search"].input("HybridDevTool")
        self["tool_title"].click()
        time.sleep(2)

    def config_ppe_lane(self, env):
        self["search"].input("ppe")
        time.sleep(1)
        self["ppe_switcher"].click()
        time.sleep(1)
        self["ppe_lane_entrance"].click()
        time.sleep(1)
        self["ppe_lane_config"].text = env
        time.sleep(1)
        self["save"].click()

    def close_feed_debug(self):
        self["feed_debug_search"].text = "FeedDebug"
        time.sleep(1)
        self["feed_debug"].click()
        time.sleep(3)
        self["back"].click()

    def change_story_setting(self):
        self["config_tab"].click()
        self["search_config"].input("Story distribute and interaction experiment")
        self["first_search_result"].click()
        time.sleep(1)
        self["toggle_switch"].click()
        time.sleep(1)
        self["treatment_v5"].click()
        time.sleep(1)
        self["sure"].click()

    def config_boe_lane(self, env):
        self["search"].input("boe")
        time.sleep(1)
        if env != "boe":
            self["boe_lane_entrance"].click()
            time.sleep(1)
            self["boe_lane_config"].text = env
            time.sleep(1)
            self["save"].click()
        self["boe_switcher"].click()

    def enable_bpea(self):
        self["search"].text = "bpea"
        self["bpea_tool"].click()
        self["enable_bpea"].click()

    def bpea_check(self):
        self["bpea_tool"].click()
        self["cert_record"].click()
        self["enable_bpea"].click()

    def config_send_key(self,value):
        self["config_tab"].click()
        self["search_config"].input(value)
        time.sleep(1)
        self["nickname_libra"].click()
        time.sleep(2)
        self["libra_switch"].click()
        time.sleep(2)
        self["nickname_03_libra"].click()
        time.sleep(2)
        self["sure"].click()
        time.sleep(2)
        self["sure_restart"].click()
        time.sleep(2)

    def config_send_key_text(self, value, key):
        self["config_tab"].click()
        self["search_config"].input(value)
        time.sleep(1)
        self["nickname_libra"].click()
        time.sleep(2)
        self["libra_switch"].click()
        time.sleep(2)
        self["config_text_field_val"].click()
        time.sleep(3)
        for i in range(18):
            self._device_driver.send_key(67)
        time.sleep(3)
        self["config_text_field_val"].input(key)
        time.sleep(2)
        self.click_sure()

    def click_sure(self):
        self["sure"].click()
        time.sleep(2)

    def click_back(self):
        self["back"].click()
        time.sleep(2)

    def open_bpea(self):
        if self["bpea_tool"].wait_for_visible(timeout=2, raise_error=False):
            self["bpea_tool"].click()
        else:
            self["search"].text = "bpea"
            self["bpea_tool"].click()

    def click_config(self):
        self['Config'].click()
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["open_debug_title"].wait_for_visible(timeout=2, raise_error=False):
                break
            else:
                self['Config'].click()

    def text_search(self, text=""):
        if self['config_search'].wait_for_visible(timeout=3, raise_error=False):
            self['config_search'].text = text
        elif self["search_input"].wait_for_visible(timeout=3, raise_error=False):
            self["search_input"].text = text

    def click_inbox_redesign_phase3(self):
        self['inbox_redesign_phase3'].click()

    def click_toggle(self):
        self['toggle'].click()

    def click_control(self):
        self['Control'].click()

    def click_button1(self):
        self['sure_restart'].click()

    def inbox_redesign_phase(self, control):
        self.click_config()
        self.text_search(text='Inbox Redesign Phase 3')
        self.click_inbox_redesign_phase3()
        self.click_toggle()
        self[control].click()
        self.app.testcase.take_screen_shot(self.app)
        self.click_sure()
        time.sleep(10)
        try:
            self.refresh()
            if self.wait_for_visible(timeout=2, raise_error=False):
                self["sure_restart"].refresh()
                if self["sure_restart"].wait_for_visible(timeout=2, raise_error=False):
                    self.app.testcase.take_screen_shot(self.app)
                    self.click_button1()
                    time.sleep(10)
        except:
            pass

    def ab_test(self, ab_text, ab_test):
        self.click_config()
        self.text_search(text=ab_text)
        test_btn = Control(root=self, path=UPath(id_ == "title", visible_ == True, text_ == ab_text, index=-1))
        test_btn.wait_for_visible()
        print(test_btn.rect)
        print(test_btn.elem_info)
        test_btn.click()
        for _ in Retry(timeout=5, interval=0.5):
            if self['toggle'].existing:
                self.click_toggle()
                break
        ab_btn = Control(root=self, path=UPath(~text_ == ".*" + ab_test + ".*", index=-1))
        ab_btn.wait_for_visible()
        ab_btn.click()
        self.app.testcase.take_screen_shot(self.app)
        self.click_sure()
        time.sleep(10)
        try:
            self.refresh()
            if self.wait_for_existing(timeout=2, raise_error=False):
                self["sure_restart"].refresh()
                if self["sure_restart"].wait_for_visible(timeout=2, raise_error=False):
                    self.app.testcase.take_screen_shot(self.app)
                    self.click_button1()
                    time.sleep(10)
        except:
            pass

    def set_poi_config(self, value):
        enable_poi = "Enable visit poi content"
        self.click_config()
        self.text_search(text=enable_poi)
        enable_poi_tab = Control(root=self, path=UPath(id_ == "title", visible_ == True, text_ == enable_poi))
        enable_poi_tab.wait_for_ui_stable()
        enable_poi_tab.click()
        self["toggle_switch"].click()
        self["edit_frame"].text = value
        self["sure"].click()

    def open_lynx_profile_toggle(self):
        ab = "Verify the performance of lynx in DM"
        self.click_config()
        self.text_search(ab)
        self.click_ab_libra(ab)
        self.click_toggle_btn()
        lynx_item = Control(root=self, path=UPath(text_ == "use lynx card"))
        lynx_item.click()
        self.click_sure_btn()

    def open_tako_ab(self):
        ab = "Feature experiment of tikbot"
        self.click_config()
        self.text_search(ab)
        self.click_ab_libra(ab)
        self.click_toggle_btn()
        open_item = Control(root=self, path=UPath(id_ == "group") / 1 / UPath(id_ == "text"))
        open_item.click()
        self.click_sure_btn()

    def click_ab_libra(self, ab):
        ab_control = Control(root=self, path=UPath(id_ == "title", visible_ == True, text_ == ab))
        ab_control.wait_for_ui_stable()
        ab_control.click()

    def click_toggle_btn(self):
        if self["toggle_switch"].wait_for_visible(timeout=3, raise_error=False):
            self["toggle_switch"].click()
        elif self["toggle_btn"].wait_for_visible(timeout=2, raise_error=False):
            self["toggle_btn"].click()

    def click_sure_btn(self):
        if self["sure"].wait_for_visible(timeout=3, raise_error=False):
            self["sure"].click()
        elif self["sure_btn"].wait_for_visible(timeout=2, raise_error=False):
            self["sure_btn"].click()
        time.sleep(1)
        if self['sure_restart'].wait_for_visible(timeout=3, raise_error=False):
            self.click_button1()


class LocationMockPanel(Window):
    """Mock Gps
    """

    window_spec = {"activity": "com.bytedance.bdlocation.locationmock.OverseaLocationMockActivity"}

    def get_locators(self):
        return {
            'latitude': {'type': Control, 'path': UPath(id_ == 'et_oversea_lat')},
            'longitude': {'type': Control, 'path': UPath(id_ == 'et_oversea_lng')},
            'mock_btn': {"type": Control, "path": UPath(id_ == 'bt_oversea_mock')},
        }

    def input_gps_info(self, latitude, longitude):
        self["latitude"].text = latitude
        time.sleep(2)
        self["longitude"].text = longitude
        time.sleep(2)
        self["mock_btn"].click()


class RegionInputPanel(Window):
    """Debug页
    """

    window_spec = {"activity": "com.bytedance.ies.assistant.AssistantActivity.*"}

    def get_locators(self):
        return {
            'change_locale_input': {'type': TextEdit, 'path': UPath(id_ == 'input', index=-1)},
            'change_locale_save': {'type': Button, 'path': UPath(id_ == 'save')},
        }

    def input_region(self, region):
        self["change_locale_input"].text = region
        self["change_locale_save"].click()


class AnyWhereDoorAuthorizePanel(Window):
    """AnyWhereDoor Authorize Panel
    """
    window_spec = {"activity": "com.ss.android.anywheredoor.ui.activity.webview.WebViewActivityForAnyDoor"}

    def get_locators(self):
        return {
            'AnyWhereDoorAuthorizeWebview': {'type': AnyWhereDoorAuthorizeWebview, 'path': UPath(id_ == 'webview')},
        }

    def authorize(self):
        return self["AnyWhereDoorAuthorizeWebview"].authorize()


class AnyWhereDoorAuthorizeWebview(Webview):
    view_spec = {"title": "任意门"}

    def get_locators(self):
        return {
            "authorize": {"type": WebElement, "path": UPath(~class_ == 'submitBtn')},
            "authorize_success": {"type": WebElement, "path": UPath(class_ == 'semi-toast-content')},
        }

    def authorize(self):
        self["authorize"].click()
        return self["authorize_success"].wait_for_existing(timeout=15, raise_error=False)


class AnyWhereDoorAuthorizePanel(Window):
    """AnyWhereDoor Authorize Panel
    """
    window_spec = {"activity": "com.ss.android.anywheredoor.ui.activity.webview.WebViewActivityForAnyDoor"}

    def get_locators(self):
        return {
            'AnyWhereDoorAuthorizeWebview': {'type': AnyWhereDoorAuthorizeWebview, 'path': UPath(id_ == 'webview')},
        }

    def authorize(self):
        return self["AnyWhereDoorAuthorizeWebview"].authorize()


class AnyWhereDoorAuthorizeWebview(Webview):

    view_spec = {"title": "任意门"}

    def get_locators(self):
        return {
            "authorize": {"type": WebElement, "path": UPath(~class_ == 'submitBtn')},
            "authorize_success": {"type": WebElement, "path": UPath(class_ == 'semi-toast-content')},
        }

    def authorize(self):
        self["authorize"].click()
        return self["authorize_success"].wait_for_existing(timeout=15, raise_error=False)


class BpeaSettingPanel(Window):
    """BPEA Panel
    """
    window_spec = {"activity": "com.bytedance.bpea.debug_tool.ui.BPEADebugMainActivity"}

    def get_locators(self):
        return {
            "bpea_enable": {"path": UPath(text_ == 'enable collect stack', visible_ == True)},
            "cert_record": {"path": UPath(id_ == 'btn_record', visible_ == True)},

        }

    def enable_bpea(self):
        time.sleep(2)
        self["bpea_enable"].wait_for_visible()
        default_pic = self["bpea_enable"].capture()
        self["bpea_enable"].click()
        time.sleep(1)
        clicked_pic = self["bpea_enable"].capture()
        cv = CV()
        for _ in Retry(limit=3, interval=1):
            if cv.match_pair(clicked_pic, default_pic)["distance"] < 0.25:
                self["bpea_enable"].click()
                clicked_pic = self["bpea_enable"].capture()
            else:
                break
        time.sleep(1)

    def check_record(self):
        self["cert_record"].wait_for_visible()
        self["cert_record"].click()


class RecordInfo(Control):

    def get_locators(self):
        return {
            "time": {"path": UPath(id_ == 'time')},
            "cert_token": {"path": UPath(id_ == 'tv_cert')},
            "status": {"path": UPath(id_ == 'tv_status')},
            "date": {"path": UPath(id_ == 'tv_time')},
        }

    def authorize(self):
        return self["AnyWhereDoorAuthorizeWebview"].authorize()

class RecordList(Control):

    elem_class = RecordInfo
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")


class BpeaDebugPanel(Window):
    """BPEA debug_tool Panel
    """
    window_spec = {"activity": "com.bytedance.bpea.debug_tool.ui.RecordActivity"}

    def get_locators(self):
        return {
            "data_type": {"path": UPath(id_ == 'spinner_dataType')/UPath(id_ == 'tv_content', visible_ == True)},
            "record_list": {"type": RecordList, "path": UPath(id_ == 'listView', visible_ == True)},
        }

    def select_data_type(self):
        self["data_type"].wait_for_visible()
        self["data_type"].click()
        bpeaDebugPopup = BpeaDebugPopup(root=self.app)
        if bpeaDebugPopup.permission.wait_for_existing(timeout=5, raise_error=False):
            bpeaDebugPopup.permission.click()
        elif bpeaDebugPopup.latitudeAndLongitude.wait_for_existing(timeout=5, raise_error=False):
            bpeaDebugPopup.latitudeAndLongitude.click()
        time.sleep(2)

    def check_bpea(self, token, action_time):
        from utils.common.log_utils import logger
        for _ in Retry(limit=10, interval=0.5, raise_error=False):
            bpea_list = self["record_list"].items()
            for bpea in bpea_list:
                token_id = bpea["cert_token"].text.split("-")[1]
                logger.info("发现{}证书".format(token_id))
                if token != token_id:
                    continue
                record_time = bpea["date"].text + "/" + bpea["time"].text
                logger.info("证书时间{}".format(record_time))
                if not self.time_check(action_time, record_time):
                    logger.info("证书时间校验不通过，action_time记录为{}".format(action_time))
                    continue
                if bpea["status"].text.split(":")[-1] != "0":
                    logger.info("{}证书， status显示为{}".format(token, bpea["status"].text))
                else:
                    logger.info("{}证书验证通过".format(token))
                    self.app.testcase.take_screen_shot(self.app, "界面截图")
                    return True
            if len(bpea_list) <= 2:
                break
            self["record_list"].scroll(coefficient_y=0.5)
        logger.info("{}证书验证失败".format(token))
        return False

    def time_check(self, action_time, record_time):
        action_time = datetime.datetime.strptime(action_time, "%Y-%m-%d/%H:%M:%S")
        start = action_time - datetime.timedelta(seconds=5)
        end = action_time + datetime.timedelta(seconds=5)
        record_time = datetime.datetime.strptime(record_time, "%Y-%m-%d/%H:%M:%S")
        if start < record_time < end:
            return True
        else:
            return False


class BpeaDebugPopup(PopupBase):
    """BPEA debug_tool Panel
    """
    window_spec = {"activity": "PopupWindow:.*"}

    def get_locators(self):
        return {
            "latitudeAndLongitude": {"path": UPath(text_ == 'latitudeAndLongitude', visible_ == True)},
            "permission": {"path": UPath(text_ == 'permission', visible_ == True)}
        }

    def authorize(self):
        return self["AnyWhereDoorAuthorizeWebview"].authorize()


class CollectionPopup(PopupBase):
    """
    视频收藏Toast
    """
    window_spec = {"activity": "PopupWindow:.*"}

    def get_locators(self):
        return {
            "collection_toast": {"path": UPath(id_ == 'content_layout', visible_ == True)},
            "added_to_favourites": {"path": UPath(id_ == 'message', visible_ == True)},
            "manage_text": {"path": UPath(id_ == 'right_message', visible_ == True)},
            "added_toast":{"path": UPath(id_ == 'message', visible_ == True)},
            "added_icon": {"path": UPath(id_ == 'icon', visible_ == True)},
            "no_interest_popup": {"type": Control, "path": UPath(~id_ == 'toast_layout|message', visible_ == True)},
            "manage_btn": {"type": Control, "path": UPath(~id_ == 'end_slot|right_message', visible_ == True)},
        }


    def check_added_favourites_text(self):
        if self.added_to_favourites.text == "Added to Favorites":
            return True
        return False

    def check_no_interest_popup(self):
        return self["no_interest_popup"].wait_for_existing(timeout=3, raise_error=False)

    def check_added_icon(self):
        return self["added_icon"].wait_for_existing(timeout=3, raise_error=False)

    def check_added_toast(self):
        if self["added_toast"].text[0:8] == "Added to":
            return True
        return False
