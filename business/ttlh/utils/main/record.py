import random
import time
import uuid

from shoots_android.control import Window, Control
from uibase.upath import UPath, id_, text_
from shoots.retry import Retry
from uibase.base import UIScene

class VideoRecorderPanel(Window):
    """
    录制视频页面
    """
    windoe_spec = {"activity": "com.ss.android.ugc.aweme.shortvideo.ui.VideoRecordNewActivity"}

    def get_locators(self):
        return {
            "record_btn": {'type': Control, 'path': UPath(id_ == 'rl_record_tool_bottom') / UPath(id_ == 'record_container')},
            "close_record": {'type': Control,'path': UPath(id_ == 'img_close_record')},
            "start_over_button": {'type': Control, 'path': UPath(text_ == 'Start over')},
            "cancel_button": {'type': Control, 'path': UPath(text_ == 'Cancel')},
            "discard_button": {'type': Control, 'path': UPath(text_ == 'Discard')},
            "60s": {'type': Control, 'path': UPath(text_ == '60s', id_ == 'tab_item_text')},
            "btn_next": {'type': Control, 'path': UPath(id_ == 'btn_next')},
            "back": {'type': Control, 'path': UPath(id_ == 'back')},
            # 直播场景
            "live_btn": {'type': Control, 'path': UPath(id_ == 'tab_edge_transparent_view') / UPath(id_ == 'container') / 3},
            "go_live": {'type': Control, 'path': UPath(text_ == 'Go LIVE')},
            "exit_live": {'type': Control, 'path': UPath(id_ == 'live_close_widget_container')},
            "exit_live2": {'type': Control, 'path': UPath(id_ == '0x4e')},
            "end_now_exit_live": {'type': Control, 'path': UPath(id_ == 'positive_button')},
            "close_live": {'type': Control, 'path': UPath(id_ == 'adapted_end_container') / 0 / 16},
            "cancel_btn": {'type': Control, 'path': UPath(id_ == 'button2')},
        }

    def cancel_btn(self, device):
        x = self['cancel_btn'].rect.center[0]
        y = self['cancel_btn'].rect.center[1]
        device.click(x, y)

    def close_record(self):
        self["close_record"].click()

    def click_record(self):
        self["record_btn"].click()

    def back(self):
        self["back"].click()

    def click_start_over_button(self):
        self["start_over_button"].click()

    def click_cancel_button(self):
        self["cancel_button"].click()

    def click_discard_button(self):
        self["discard_button"].click()

    def exchange_60s_record(self):
        self["60s"].click()

    def click_btn_next(self):
        self["btn_next"].wait_for_visible(
            timeout=5,
            interval=0.5,
            raise_error=False
            )
        self["btn_next"].click()

    # 直播场景
    def live_btn(self):
        self["live_btn"].click()
        time.sleep(1)

    def go_live(self):
        self["go_live"].click()
        time.sleep(10)

    def exit_live(self):
        self["exit_live"].click()
        time.sleep(1)

    def exit_live2(self):
        self["exit_live2"].click()
        time.sleep(1)

    def end_now_exit_live(self):
        self["end_now_exit_live"].click()
        time.sleep(1)

    def close_live(self):
        self["close_live"].click()
        time.sleep(1)


class VideoEditPanel(Window):
    """
    视频编辑页面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity"}

    def get_locators(self):
        return {
            "next_button": {'type': Control, 'path': UPath(text_ == 'Next')},
            "back_button": {'type': Control, 'path': UPath(id_ == 'back')},
            'close_button': {'type': Control, 'path': UPath(id_ == "close_button")}
        }

    def get_edit_page_scene(self):
        return UIScene(self).dump()

    def click_next_button(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['next_button'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self["next_button"].click()
                return True
        return False

    def click_back_button(self):
        self["back_button"].click()

    def click_close_button_if_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['close_button'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['close_button'].click()



class VideoPublishPanel(Window):
    """
    视频发布页面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.shortvideo.ui.VideoPublishActivity"}

    def get_locators(self):
        return {
            "drafts_button": {'type': Control, 'path': UPath(id_ == 'draft_txt')},
            "post_button": {'type': Control, 'path': UPath(id_ == 'publish_txt')},
            "add_location": {'type': Control, 'path': UPath(id_ == 'cl_add_location_container')},
            "back_button": {'type': Control, 'path': UPath(id_ == 'back')},
            "cancel_button": {'type': Control, 'path': UPath(id_ == 'cancel')},
            "description" : {'type': Control, 'path': UPath(id_ == "editor_text")},
            "post_public": {"type": Control, "path": UPath(text_ == "Post Now")},
        }

    def click_drafts_button(self):
        self["drafts_button"].click()

    def click_post_button(self):
        self["post_button"].click()

    def add_location(self):
        self["add_location"].click()
        time.sleep(1)

    def click_back_button(self):
        self["back_button"].click()

    def input_description(self):
        random_str = str(uuid.uuid1())[:8]
        description = "%s 录制时长两分半的视频" % random_str
        # self["description"].input(description)
        self["description"].text = description
        return description

    def swipe_down(self):
        self.scroll(distance_y=-500)

    def get_description(self):
        return self['description'].text

    def click_post_public(self):
        if self["post_public"].existing and self["post_public"].visible:
            self["post_public"].click()


class KidVideoPublishPanel(Window):
    """
    儿童场景下控价元素信息
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.ftc.FTCVideoRecordNewActivity"}

    def get_locators(self):
        return {
            "record_btn": {'type': Control, 'path': UPath(id_ == 'rl_record_tool_bottom') / UPath(id_ == 'record_container')},
            "close_record": {'type': Control, 'path': UPath(id_ == 'img_close_record')},
        }

    def close_record(self):
        self["close_record"].click()

    def click_record(self):
        self["record_btn"].click()
