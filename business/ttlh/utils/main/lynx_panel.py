from shoots_lynx.lynx import LynxView, LynxElement
from shoots_android.control import *
from shoots_android.upath import *
import time
from shoots_lynx.upath import bindtap_
from utils.common.log_utils import logger
from shoots_lynx.upath import lynx_test_tag_

class LynxWindow(Window):
    """
    native window which contains lynxview for Shoots-Lynx Test
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LivePlayActivity"}

    def get_locators(self):
        return {
            "live_goal_banner_lynx_view": {"type": LiveGoalBannerLynxView, "path": UPath(id_ == "spark_view_container") / UPath(type_ == "SparkView") / 0},
            "random_page_lynx_view": {"type": RandomPageLynxView, "path": UPath(id_ == "engine_container", index=0) / UPath(type_ == "com.lynx.FakeViews.LynxView", index=0)},
            "random_popup_lynx_view": {"type": RandomPopupLynxView, "path": UPath(id_ == "popup_inner_container", index=-1) / UPath(type_ == "com.lynx.FakeViews.LynxView", index=0)},
            "random_collection_page_lynx_view": {"type": RandomCollectionPageLynxView, "path": UPath(id_ == "popup_inner_container", index=-1) / UPath(type_ == "com.lynx.FakeViews.LynxView", index=0)},

            "live_goal_page_lynx_view": {"type": LiveGoalPageLynxView, "path": UPath(id_ == "engine_container") / UPath(type_ == "com.lynx.FakeViews.LynxView", index=0)},
            "first_recharge_page": {"type": FirstRechargePage, "path": UPath(id_ == "engine_container", index=0) / UPath(type_ == "com.lynx.FakeViews.LynxView", index=0)},
        }
    def wait_by_name(self, ctrl_name, timeout=60, raise_error=False):
        if self[ctrl_name].wait_for_existing(timeout, raise_error=raise_error):
            return True
        return False

class RandomPageLynxView(LynxView):
    def get_locators(self):
        return {
            "send_random_gift": {"path": UPath(text_ == "Send")},
            "random_gift_anchor_avatar": {"path": UPath(class_ == "rounded-half")},
            "random_gift_collection_anchor_name": {"path": UPath(class_ == "P1-Semibold") / UPath(type_ == "RAW-TEXT")},
            "random_gift_completed_tours": {"path": UPath(text_ == "Completed tours")},
            "random_gift_collection_page_picture": {"path": UPath(class_ == "absolute right-0")},
            "random_gift_collection_middle_title": {"path": UPath(text_ == "Travel around the world")},
            "random_gift_collection_middle_progress_bar": {"path": UPath(name_ == "SRC/PAGES/COLLECTION/COMPONENTS/PROGRESS-BAR/INDEX-PROGRESSBAR")},
            "random_gift_collection_middle_progress_left": {"path": UPath(class_ == "ml-8 align-bottom") / 0},
            "random_gift_collection_middle_progress_right": {"path": UPath(text_ == "/9")},
            "random_gift_collection_middle_tour_num": {"path": UPath(class_ == "H2-SemiBold")},
            "random_gift_collection_picture_unlock_lottie_1": {"path": UPath(class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 0 / UPath(name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_2": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 1 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_3": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 2 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_4": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 3 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_5": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 4 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_6": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 5 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_7": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 6 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_8": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 7 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},
            "random_gift_collection_picture_unlock_lottie_9": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 8 / UPath(
                name_ == "LOTTIE-VIEW", depth=6)},

            "random_gift_collection_picture_unlock_static_1": {"path": UPath(src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_pisa.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_2": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_rome.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_3": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_london.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_4": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_munich.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_5": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_dubai.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_6": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_santorini.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_7": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_seoul.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_8": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_phoenix.png~tplv-obj.awebp")},
            "random_gift_collection_picture_unlock_static_9": {"path": UPath(
                src_ == "https://p16-webcast.tiktokcdn.com/img/alisg/webcast-sg/random_city_atlantis.png~tplv-obj.awebp")},
            "random_gift_collection_picture_lock_static_1" : {"path": UPath(class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 0 / UPath(class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_2": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 1 / UPath(
                class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_3": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 2 / UPath(
                class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_4": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 3 / UPath(
                class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_5": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 4 / UPath(
                class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_6": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 5 / UPath(
                class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_7": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 6 / UPath(
                class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_8": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 7 / UPath(
                class_ == "relative flex-col w-full")},
            "random_gift_collection_picture_lock_static_9": {"path": UPath(
                class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 8 / UPath(
                class_ == "relative flex-col w-full")},
            "FAQ": {"path": UPath(class_ == "flex absolute top-0 left-16 items-center") / UPath(name_ == "IMAGE")},

            #"random_gift_collection_picture_audience_avater_1": {"path": UPath(class_ == "w-full flex justify-between flex-row flex-wrap flex-1 mt-10 px-16") / 0 / UPath(src_ == "https://p16-sign-sg.tiktokcdn.com/aweme/100x100/tos-alisg-avt-0068/5e0f7dcabba23d505546a86a196a4153.webp?x-expires=1681286400&x-signature=ALMsMVPfyE8glS4gp6vw3gD1V90%3D")},


        }
    def click_by_ctrlname(self, ctrl_name):
        if self[ctrl_name].wait_for_existing(timeout=10, raise_error=False):
            self[ctrl_name].click()
            time.sleep(3)
            return True
        return False

    def get_random_page_lynx_perf(self):
        return self.get_lynx_perf()

    def check_random_gift_collection_page_anchor_avatar(self, timeout=10):
        """
        校验随机礼物收集页中的主播头像
        """
        return self["random_gift_anchor_avatar"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_random_gift_collection_page_completed_tours(self, timeout=10):
        """
        校验随机礼物收集页的历史轮次
        """
        return self["random_gift_completed_tours"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_random_gift_collection_page_picture(self, timeout=10):
        """
        校验随机礼物收集页上方的礼物图片
        """
        return self["random_gift_collection_page_picture"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_random_gift_collection_page_middle_title(self, timeout=10):
        """
        校验随机礼物收集页中间部分的标题文案
        """
        return self["random_gift_collection_middle_title"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def check_random_gift_collection_page_middle_progress_bar(self, timeout=10):
        """
        校验随机礼物收集页中间部分的进度条
        """
        return self["random_gift_collection_middle_progress_bar"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    def random_gift_collection_num_and_location(self, timeout=10, random_picture_name=""):
        """
        统计已解锁的随机礼物图片个数及位置
        """
        num = 0
        num_list = []
        for i in range(9):
            unlock_random_picture_name_current = random_picture_name + str(i+1)
            if self[unlock_random_picture_name_current].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False):
                num += 1
                num_list.append(i)
        return num, num_list

class RandomPopupLynxView(LynxView):
    def get_locators(self):
        return {
            "random_gift_picture_content": {"path": UPath(class_ == "P2-SemiBold text-color-ConstTextInverse text-center") / UPath(name_ == "RAW-TEXT")},
            "random_gift_picture_travel_content": {"path": UPath(class_ == "overflow-hidden P3-Regular text-color-TextSecondary color-TextSecondary overflow-ellipsis whitespace-nowrap") / UPath(name_ == "RAW-TEXT")},
            "random_gift_picture_avatar": {"path": UPath(class_ == "w-40 h-40 absolute")},
            "random_gift_picture_close": {"path": UPath(bindtap_ == "close")}
        }

    def click_by_ctrlname(self, ctrl_name):
        if self[ctrl_name].wait_for_existing(timeout=10, raise_error=False):
            self[ctrl_name].click()
            time.sleep(3)
            return True
        return False

class RandomCollectionPageLynxView(LynxView):
    def get_locators(self):
        return {
            "random_no_collection_page_icon": {"path": UPath(src_ == "https://lf16-gecko-source.tiktokcdn.com/obj/byte-gurd-source-sg/musically/fe/live/tiktok_live_revenue_random_gift/resource/images/no-access.bdf8170b.png")},
            "random_no_collection_page_content": {"path": UPath(class_ == "flex-col items-center justify-center h-full px-32") / UPath(name_ == "RAW-TEXT")},
            "random_collection_page": {"path": UPath(class_ == "w-full flex justify-between flex-row flex-wrap")},
        }

    def click_by_ctrlname(self, ctrl_name):
        if self[ctrl_name].wait_for_existing(timeout=10, raise_error=False):
            self[ctrl_name].click()
            time.sleep(3)
            return True
        return False


class LiveGoalPageLynxView(LynxView):
    def get_locators(self):
        return {
            "live_goal_page_progress": {"path": UPath(class_ == "flex flex-row number-max-with") / UPath(name_ == "TEXT", index=0) / UPath(name_ == "RAW-TEXT")},
            "live_goal_page_target": {"path": UPath(class_ == "flex flex-row number-max-with") / UPath(name_ == "TEXT", index=2) / UPath(name_ == "RAW-TEXT")},
            "live_goal_no_supporter": {"path": UPath(text_ == "No contributors yet")},
            "live_goal_gift_coins": {"path": UPath(class_ == "gift-price-number") / UPath(name_ == "RAW-TEXT")},
            "live_goal_supporter_coins": {"path": UPath(class_ == "flex h-40 justify-center items-center") / UPath(name_ == "RAW-TEXT")},
            "live_goal_supporter_coins_icon": {"path": UPath(src_ == "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAMAAAAM7l6QAAAAclBMVEUAAAD/65f/7Jr/7Jv/7Jv/7Jv/7Jv/65r/65v/7Jr/7Jz/65v/65//65r6zhX/7Jv+9c37vhb+54n/6pP82Ef6xRX94XH60B783Vj60yz91Vj8zkf6yRX+8bj72EH94Wf932D912D843z92mj97ar85HxqzVi0AAAADnRSTlMAIIDvz7+gkN+vUEAQjws9fswAAAFASURBVCjPjZPncoMwEIRNNyUcEk0gih0n7/+KWZUgwP7BjscDfLN3t+i4XVaRJ3EUpX72CXpJQFZB7J2pTwf5R2tIUP0apRy7mqDQ29E7HnDJrCTH7d1x5X3iedtO09Ti4qn8+74jY1NpNTE2uv6e9rYGDcuA/1b7TfkYfUGNePVdao7+Xxojr1TU4nnQXCJ/AZohESsdrmbR44IhX24G69odhria72WqpxjboIUTMF9/FC4xfAwcETWadvABd7ZOQxTt8ICi4h0jl1C3v7qlw4IoBU6IVpe43nBn3luOYCrJqhIvVbVYjGAZcBGY5v1cVfiRax2A6upc2QfC6Cik1XPk2o7kYc8D0OihjsQdqCgPEvuFCje/ray84WmZmn/Y2GU6r2InmkasdhWvLzLkxe4zSGB9V+anURQneXG7qj/Q2SYCXKjQ5QAAAABJRU5ErkJggg==")},
            "live_goal_send_btn": {"path": UPath(text_ == "Send")},
            "live_goal_supporter_name": {"path": UPath(class_ == "items-center min-h-40 flex") / 2 / UPath(name_ == "RAW-TEXT")},
            "live_goal_page_description": {"path": UPath(class_ == "flex flex-col flex-1") / 1 / UPath(name_ == "RAW-TEXT")},
            "live_goal_page_gift_name": {
                "path": UPath(class_ == "flex justify-between items-end") / 0 / UPath(name_ == "RAW-TEXT")},
            "live_goal_page_gift_icon": {"path": UPath(src_ == "https://p16-webcast.tiktokcdn.com/img/maliva/webcast-va/eba3a9bb85c33e017f3648eaf88d7189~tplv-obj.png")},
            #"live_goal_page_gift_icon": {"path": UPath(class_ == "gift-image")},

            "live_goal_page_faq": {"path": UPath(class_ == "question-icon", bindtap_ == "go2Faq")},
            "live_goal_page_contribution_title": {"path": UPath(class_ == "mt-24") / UPath(name_ == "RAW-TEXT")},
            "live_goal_page_contributor_avater": {"path": UPath(class_ == "items-center min-h-40 flex") / UPath(bindtap_ == "openProfile")},
        }
    def click_by_ctrlname(self, ctrl_name):
        if self[ctrl_name].wait_for_existing(timeout=10, raise_error=False):
            self[ctrl_name].click()
            time.sleep(3)
            return True
        return False

    def get_random_page_lynx_perf(self):
        return self.get_lynx_perf()

    def get_live_goal_page_progress(self):
        return self["live_goal_page_progress"].text + "/" + self["live_goal_page_target"].text

    # 查看目标详情页中贡献榜单标题描述
    def check_goal_description(self):
        return self["live_goal_page_description"].text

    # 查看目标详情页中目标礼物的名称
    def check_goal_gift_name(self):
        return self["live_goal_page_gift_name"].text

    # 校验目标详情页中目标礼物的icon图片
    def check_goal_gift_icon(self, timeout=10):
        return self["live_goal_page_gift_icon"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 校验目标详情页中的faq是否存在
    def check_goal_faq(self, timeout=10):
        return self["live_goal_page_faq"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 校验贡献榜单标题
    def check_goal_contribution_title(self):
        return self["live_goal_page_contribution_title"].text

    # 校验贡献榜中贡献者的头像
    def check_goal_contributor_avater(self, timeout=10):
        return self["live_goal_page_contributor_avater"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 校验目标进度面板没有支持者
    def check_goal_no_supporter(self, timeout=10):
        return self["live_goal_no_supporter"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 查看目标详情页中贡献人所支持的coins数
    def get_supporter_coins(self):
        return self["live_goal_supporter_coins"].text

    # 查看目标详情页中贡献coins数前的icon是否存在
    def get_supporter_coins_icon(self, timeout=10):
        return self["live_goal_supporter_coins_icon"].wait_for_existing(timeout=timeout, interval=0.5, raise_error=False)

    # 查看目标礼物的coins价格
    def get_live_goal_gift_coins(self):
        return self["live_goal_gift_coins"].text

    # 查看目标详情页的支持者的名称
    def get_supporter_name(self):
        return self["live_goal_supporter_name"].text

class LiveGoalBannerLynxView(LynxView):
    def get_locators(self):
        return {
            "live_goal_banner_progress": {"path": UPath(class_ == "w-full h-full flex items-center overflow-hidden") / UPath(name_ == "RAW-TEXT", index=2)},
            "live_goal_banner_target": {"path": UPath(class_ == "w-full h-full flex items-center overflow-hidden") / UPath(name_ == "RAW-TEXT", index=1)},

        }

    # 获取目标指示器上的目标进度
    def get_live_goal_banner_progress(self):

        return self["live_goal_banner_progress"].text + "/" + self["live_goal_banner_target"].text

class FirstRechargePage(LynxView):
    def get_locators(self):
        return {
            "page_close_btn": {"path": UPath(bindtap_ == "handleCloseClick")},
            "recharge_page_title": {"path": UPath(text_ == "First recharge package")},
            "recharge_page_sub_title_1": {"path": UPath(text_ == "99% off a unique Gift")},
            "recharge_page_sub_content_1": {'path': UPath(text_ == "Send this 100-Coin Gift for only 1 Coin (valid for 24 hours and 3 times)")},
            "recharge_page_sub_title_2_left": {'path': UPath(class_ == "h-full flex-col justify-start items-start flex-1", index=1) / 0 / UPath(name_ == "RAW-TEXT", index=0)},
            "recharge_page_sub_title_2_right": {'path': UPath(class_ == "h-full flex-col justify-start items-start flex-1", index=1) / 0 / UPath(name_ == "RAW-TEXT", index=3)},
            "recharge_page_sub_content_2": {"path": UPath(text_ == "Use Coins to send Gifts during LIVE videos. Recharge to get extra Coins.")},
            "recharge_page_package_select": {"path": UPath(text_ == "Select recharge amount")},
            "recharge_page_petty_package_coin_icon": {"path": UPath(bindtap_ == "__id_46896000_onClick_0", index=0) / UPath(src_ == "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQySURBVHgB1Vc9bBRHFP7ezP1yJ84HtoOJIp0hTSgIhCqpQIgqRUBpg4glkoiKSEhpcdooBWmClCAFFLoowlLSWQgquhBC4VRwBgTm197DYJu72xne7O7t7tzu+c52gfikdz/zdub7Zt6bmbfAGwZhjdBPzu4HxB6Q/DBqJAdo3wHUDRo5cRVrwEAC9NzZGjK5kxDqS2gM9RnRYUFTaLW+p7ETs9iIAF3/bQib26eZ9FusC3S+n5CeAvTjX/ZA4hLPppZwuivQzQXo5TlAFjkaBVBxjH8XUhhoFs3WgV4iqDc5XbGWm0nV/D9QjRnoF3WkDlYeh9iyj+2jbpcDpQ/Q6Nc3+grw4p3L/BsnN4Tu3T941k7USwamA2sH38adq0K8+ylEZVdfEZQkl1fiy64eXobL5j2ZZ8sGloYWW5Ptlf9XbDsIyRaxcTiE3EvVCafTJKwBstnTqeSGsMK2aRVyBL4Sm1m7XKx/OENdg+KkjoGs2WczYXBV43+49d+BInzrAaocgijuhlq6Cf182nYu+5Z5/zjnx46ovdUe7ySlsGYfg7r/V19ybwAmNyLk8BdJZ9Dfvfunl8QRlwy3dSwE+nBIPn+d88npS24h+w7E1nQRWi/A5R0UgY5ZAvzjNcp6s928eK8RgldBbjsF2nzIdnBe6MWZeMuQfvLr/lCAd7Z3YA6ZVr07PftDvfS+vHCMnepSxgKadTsMUB5nxu9FtU6zd7rl0zlIlEDVI6BNu32t974LfXrxGtTyTYjyJzzqaLJz1oiY5xNzezCYf5n5ApQeggg2hFrptCbIxXs/gAo7/YbWo8QzujENtzGNVBgBKw8jAQESVJqWUvvT8NGQXD27yIPdwlpB2eRdkRBA+e3pncsfh+Tq6UWsC5YA1TCffqoRwvOZMtXUvsTbzEC/um07ZAmDgnKxyWk9GwmAiC4IwVdrYTzRWQcxNwePJSq/M11YN3meT0KKr4DP6Qmgka+uwtxWHVdxV2IAtXDJH6h6GJKTUY5+A1H72RfBW1C/uIbVIMrWFe0EnNZJeCF8uLSPQ2FXXnphikVM+SJ4G3rbkXeGIXfnfgxXKA0mrKIUF0BT4a+QoOsy0iu30X58LjkYkwuTkKIM3X4EPc9FU3AI9YIcOcqr+kHUELuM7Hrg6bkz/Hmy8181+DptXMZGICpcE1QOxqdwnoaPT4R+62kpJ0F+dqZ33iC5KUi4SI0/kyzJTD0ouB6MXU56aQau8zcvuYNBYGIut37uZ37E5KDZ3ttdnPYuSrtEGKiX1z0z+ZFKXNjhxZpMEouCTe4OWJSGIlLqw9Bn7ovmA5Op/HuZtyKX5JktNmnIwMvuqiNp5KsKCEXIzCRnyjGsC/QTFuUkjU/0jN3gr2ZGiMRnA72auXQBbuvMhl/NUsV4lQwXE1rWeGUqkcP9zxyvnRPurcFrvSOXSZ2GBvMAAAAASUVORK5CYII=")},
            "recharge_page_petty_package_recommend_icon": {"path": UPath(bindtap_ == "__id_46896000_onClick_0", index=0) / UPath(src_ == "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAAA6CAYAAAAA0F95AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQXSURBVHgB5Vo9c9NAEN29U4RD+BAQZswMhejckcwAEzrTUaak5Gfwc1JSUtKRjkxgJqFzh0rPOIAIgRjbuuXWjkBxZOkky0pyvEkmnsttonva2327dwgW4ffHOy1AfKEGo63lp2FgYiPAEnzb8zxCes6f0XU2P3/2GyZ21hBwNZJtBOHxZwTw7vUO2yZ2VhAw2PXWtOuvJcdQio3j956fZ2sFAUqKdto4XnGe55hefgKO91b/uv40kKDZ372dSQLCJQPpYPdzCP6SkL4Oev6sxZ+yAQiRKNAfO79k1L21Hobx786NAI7Sfhg2As/rP3gQ9LPm8h4nB1ughK/3ulF0z8KYEIy6OHI6tRPAgUm6sk2APiQeSCrYX3p8sD09n975jcGNo1ewAIyJgBrB+1UoaEPGAx2L0VbSRRmDT7c3aCRyA1pRoMA3tQVBduOsxTM4f68ouTk97j78uqMEbEOF0M+y7a739uvLAo6zYTKNt8Zg9+7a9Pjyut4eRPtQAXjx8XarhYCxTNUpydhAqrW0YffH9beI0IV5oKJOMtbUQoCWqeaLh4kXpGl5fBb0l75f2yJQIZQAxxh39eab5FgtBCBSbq6ext1eL13caBKQMIASIJ1pcCrl1kJApESJ3H11tteIcloAU+zq8YASDyxkNNNGEZUjgNT5EKD3bKEYwMAMr5Fl1aA4BwJYyQlAHyqESnmTRs+i5Jm44sCCMVwJW1X/mzMFEFE/EirQwXZneT0Mxq0x0qlUyNaU4RniFk4A1+qL0tukNYH+7jS86zvJ6H7l0ZeO/tHhynEI4CslxiWzJqheAsZVnEG5moaIcHauJ3qtZNTntz0ZOEidhpOagtXj/vFeendoYQSw+pswXw4yGs4k4OQNF8I/sk5jIUGQVdyyEi+x5NtndJvefJLXEJUTwIu/9+1orsWzZM1rklSFSrfA0e5K0/l6+GKexTNO2le1oJIAzW/9/sHRhq7Z21ABOLqjosJbQHHgHI0C01MhRmkCeNHNbthEd6mFnHMr6NVVhVmdpTSgFg2busZOdVlC1efGofukd6oRwTbTBxEXDaYkOLwQmvlXpP6i1vDDqpdsIkza0Re7o55or21lzTPKAkqSUTvrooEbK3nHY2ZpkC7O/i4KckVmJWrN6fAsYE4zxnoCSKhMQWW/BwxUpp6wnoC8msJqAvRZRDevprDbAzC/fW41AVqiB3lzrCbAlVFuQWUtAeOzf4NiyFoC+AaIyTxrCYgMzw/t9YAcARTDWgL+u7vCSSCY9xStJECBMO4n2ukBBgIohpUEmAigGNYRYCqAYlhHgKkAimEdAVHBC1T2eYChAIphHQFFjsUYVhFQRADFsIqASBW/QGkVAQ1nVPgytRkBSLVcVpgH+hhsp0j+j/EHt0qO6Hd2fLgAAAAASUVORK5CYII=")},
            "recharge_page_giving_coins_symbol": {"path": UPath(class_ == "P3-SemiBold giving-text") / UPath(name_ == "RAW-TEXT", index=0)},
            "recharge_page_giving_coins_number": {"path": UPath(class_ == "P3-SemiBold giving-text") / UPath(name_ == "RAW-TEXT", index=1)},
            "recharge_page_tip": {"path": UPath(text_ == "By continuing, you agree to the ")},
            "recharge_page_policy": {"path": UPath(text_ == "Virtual Items Policy")},
            "recharge_page_jp_left": {"path": UPath(text_ == "資金決済法に基づく表記")},
            "recharge_page_jp_right": {"path": UPath(text_ == "特定商取引法に基づく表記")},
            "recharge_page_btn": {"path": UPath(bindtap_ == "__id_47861050_onClick_0", class_ == "ButtonView flex-1 mb-24")},
            "recharge_page_the_second_package": {"path": UPath(bindtap_ == "__id_46896000_onClick_0", index=1)},
        }

    def wait_by_name(self, ctrl_name, timeout=60, raise_error=False):
        if self[ctrl_name].wait_for_existing(timeout, raise_error=raise_error):
            return True
        return False

    def click_through_name(self, ctrl_name, timeout=60, offset_x=0):
        if self[ctrl_name].wait_for_existing(timeout=timeout):
            self[ctrl_name].click(offset_x=offset_x)
            time.sleep(3)
            return True
        return False
