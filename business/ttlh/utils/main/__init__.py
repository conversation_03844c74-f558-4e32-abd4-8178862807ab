# -*- coding: utf8 -*-
from shoots_android.androidapp import AndroidApp
from shoots_android.androidapp import AccessibilityApp
from business.ttlh.utils.main.base import *
from business.ttlh.utils.main.debug_panel import *
from business.ttlh.utils.main.discover import *
from business.ttlh.utils.main.feedback import *
from business.ttlh.utils.main.inbox import *
from business.ttlh.utils.main.login import *
from business.ttlh.utils.main.login_auth_panel import *
from business.ttlh.utils.main.main import *
from business.ttlh.utils.main.mine import *
from business.ttlh.utils.main.music import *
from business.ttlh.utils.main.profile import *
from business.ttlh.utils.main.search import *
from business.ttlh.utils.main.setting import *
from business.ttlh.utils.main.splash import *
from business.ttlh.utils.main.video import *
from business.ttlh.utils.main.live import *
from business.ttlh.utils.main.money_platform import *
from business.ttlh.utils.main.comment import *
from business.ttlh.utils.main.story import *
from business.ttlh.utils.main.creator_tools import *
from business.ttlh.utils.main.friend_tab import *

class SystemuiAccessibilityApp(AccessibilityApp):
    app_spec = {'package_name': 'com.android.systemui', 'package_names': ['com.android.systemui'], 'init_device': True, 'process_name': '', 'start_activity': '', 'grant_all_permissions': True, 'clear_data': False, 'kill_process': True}

class LauncherAccessibilityApp(AccessibilityApp):
    app_spec = {'package_name': 'com.sec.android.app.launcher', 'package_names': ['com.sec.android.app.launcher'], 'init_device': True, 'process_name': '', 'start_activity': '', 'grant_all_permissions': True, 'clear_data': False, 'kill_process': True}

class LauncherAndroidApp(AndroidApp):
    app_spec = {'package_name': 'com.sec.android.app.launcher', 'package_names': ['com.sec.android.app.launcher'], 'init_device': True, 'process_name': '', 'start_activity': '', 'grant_all_permissions': True, 'clear_data': False, 'kill_process': True}
