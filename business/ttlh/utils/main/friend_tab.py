# -*- coding:utf-8 _*-
import time

from shoots_android.control import *
from uibase.upath import id_, class_
from uibase.web import WebElement, Webview
from business.ttlh.utils.main import *
from .base import FeedsList
from uibase.base import UIScene
from business.ttlh.utils.main.main import ForYouPanel
from uibase.upath import *
from uibase.controls import Window



class FriendsTabPageRecommendPermissionIcon(Control):
    def get_locators(self):
        return {
            "relation_icon": {"path": UPath(id_ == "permission_icon", visible_ == True)},
        }

class FriendsTabPageRecommendPermissionIconList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "permission_icon", visible_ == True)
    elem_class = FriendsTabPageRecommendPermissionIcon

class FriendsTabPageRecommendPermissionRelation(Control):
    def get_locators(self):
        return {
            "relation_btn": {"path": UPath(id_ == "find_text_view", visible_ == True)},
        }

class FriendsTabPageRecommendPermissionRelationList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "find_text_view", visible_ == True)
    elem_class = FriendsTabPageRecommendPermissionRelation

class FriendsTabPageRecommendPermissionTitle(Control):
    def get_locators(self):
        return {
            "relation_title": {"path": UPath(id_ == "permission_title_tv", visible_ == True)},
            "relation_title_desc": {"path": UPath(id_ == "permission_desc_tv", visible_ == True)},
        }

class FriendsTabPageRecommendPermissionTitleList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "permission_content_layout")
    elem_class = FriendsTabPageRecommendPermissionTitle

class FriendsTabPageRecommendUserhead(Control):
    def get_locators(self):
        return {
            "user_head": {"path": UPath(id_ == "avatar_view")},
        }

class FriendsTabPageRecommendUserheadList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "start_area_layout") / UPath(id_ == "avatar_view")
    elem_class = FriendsTabPageRecommendUserhead

class FriendsTabPageRecommendUsername(Control):
    def get_locators(self):
        return {
            "user_name": {"path": UPath(id_ == "nickNameView")},
            "user_label": {"path": UPath(id_ == "relation_label", visible_ == True)},
        }

class FriendsTabPageRecommendUsernameList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "middle_area_layout") / UPath(id_ == "nickNameView")
    elem_class = FriendsTabPageRecommendUsername

class FriendsTabPageRecommendRelation(Control):
    def get_locators(self):
        return {
            "relationBtn": {"path": UPath(id_ == "relationBtn", visible_ == True)},
        }

class FriendsTabPageRecommendRelatioList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "relationBtn", visible_ == True)
    elem_class = FriendsTabPageRecommendRelation

class FriendsTabPageRecommendDelete(Control):
    def get_locators(self):
        return {
            "delete_btn": {"path": UPath(id_ == "deleteIconView", visible_ == True)},
        }

class FriendsTabPageRecommendDeleteList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "deleteIconView", visible_ == True)
    elem_class = FriendsTabPageRecommendDelete


class Friend_tab_page(BasePanel):
    """friend tab
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.splash.SplashActivity#1"}

    def get_locators(self):
        return {
            "content": {"type": Control, "path": UPath(id_ == "content")},
            "sync_contact_popup": {"type": Control, "path": UPath(text_ == "Don't allow")},
            "suggested_account_text": {'type': Control, 'path': UPath(id_ == 'recommend_user_title')},
            "suggested_account_i_button": {'type': Control, 'path': UPath(id_ == 'info_icon_view')},
            "suggested_account_head": {'type': Control, 'path': UPath(id_ == 'empty_page_power_list')/2/UPath(id_ == 'avatar_view')},
            "suggested_account_head1": {'type': Control, 'path': UPath(id_ == 'empty_page_power_list')/2/UPath(id_ == 'avatarView')},
            "suggested_account_nickname": {'type': Control, 'path': UPath(id_ == 'empty_page_power_list')/2/UPath(id_ == 'nickNameView')},
            #第二个mock账号
            "suggested_account_nickname2": {'type': Control, 'path': UPath(id_ == 'empty_page_power_list')/3/UPath(id_ == 'nickNameView')},
            "suggested_account_reason": {'type': Control, 'path': UPath(id_ == 'empty_page_power_list')/2/UPath(id_ == 'reasonView')},
            "suggested_account_follow_button": {'type': Control, 'path': UPath(id_ == 'empty_page_power_list')/2/UPath(id_ == 'relationBtn')},
            "suggested_account_delete": {'type': Control, 'path': UPath(id_ == 'empty_page_power_list')/2/UPath(id_ == 'deleteIconView')},
            "find_friends_icon": {'type': Control, 'path': UPath(id_ == 'cl_left_icon_container')},
            "find_friends_search_bar": {'type': Control, 'path': UPath(id_ == 'll_search_bar', visible_ == True)},
            "find_friends_search_text": {'type': Control, 'path': UPath(id_ == 'tv_search_find', text_ == 'Find friends')},
            "find_friends_info": {'type': Control, 'path': UPath(id_ == 'cl_right_icon_container')},
            "upload_post_progress": {'type': Control, 'path': UPath(id_ == 'iv_loading')},
            "video_description": {'type': Control, 'path': UPath(id_ == 'desc', visible_ == True)},
            'friends_tab_elem_list': {'type': FriendsTabElementList, 'path': UPath(id_ == 'main_bottom_button_friend')},
            "friends_tab_panel": {'type': Control, 'path': UPath(id_ == 'sheet_container', visible_ == True)},
            "panel_title": {'root': "friends_tab_panel", 'path': UPath(id_ == 'friends_tab_intro_title', visible_ == True)},
            "panel_desc1": {'root': "friends_tab_panel", 'path': UPath(id_ == 'friends_tab_intro_desc1', visible_ == True)},
            "panel_desc2": {'root': "friends_tab_panel", 'path': UPath(id_ == 'friends_tab_intro_desc2', visible_ == True)},
            "viewed_all_new_posts": {'type': Control, 'path': UPath(text_ == "You've viewed all new posts from your friends", visible_ == True)},

            "friends_tab": {"path": UPath(id_ == "main_bottom_button_friend") / UPath(type_ == "ImageView")},
            #empyy
            "friends_tab_page_title": {"path": UPath(id_ == "tab_title", visible_ == True)},
            "friends_tab_add_friends": {"path": UPath(id_ == "iv_left_first") / UPath(id_ == "icon_custom")},
            "friends_tab_search_box": {"path": UPath(id_ == "ll_search_bar", visible_ == True)},
            "friends_tab_search_box_text": {"path": UPath(id_ == "tv_search_find",visible_ == True)},
            "friends_tab_search_box_btn": {"path": UPath(id_ == "ll_search_bar") / UPath(type_ == "TuxIconView", visible_ == True)},
            "friends_tab_search_box_btn1": {"path": UPath(id_ == "iv_right_first") / UPath(id_ == "icon_custom")},
            "friends_tab_scan": {"path": UPath(id_ == "iv_right_first") / UPath(id_ == "icon_custom", visible_ == True)},
            "friends_tab_recommend_text": {"path": UPath(id_ == "friends_feed_empty_main_text",visible_ == True)},
            "friends_tab_recommend_permission": {'type': FriendsTabPageRecommendPermissionIconList, "path": UPath(id_ == "empty_page_power_list")},
            "friends_tab_recommend_permission_relation": {'type': FriendsTabPageRecommendPermissionRelationList, "path": UPath(id_ == "empty_page_power_list")},
            "friends_tab_recommend_permission_title": {'type': FriendsTabPageRecommendPermissionTitleList, "path": UPath(id_ == "empty_page_power_list")},
            "friends_tab_recommend_user_head": {'type': FriendsTabPageRecommendUserheadList, "path": UPath(id_ == "empty_page_power_list")},
            "friends_tab_recommend_user_name": {'type': FriendsTabPageRecommendUsernameList, "path": UPath(id_ == "empty_page_power_list")},
            "friends_tab_recommend_relation_btn": {'type': FriendsTabPageRecommendRelatioList, "path": UPath(id_ == "empty_page_power_list")},
            "friends_tab_recommend_delete_btn": {'type': FriendsTabPageRecommendDeleteList, "path": UPath(id_ == "empty_page_power_list")},
        }
    def CheckFriendsTabPageTitle(self):
        if self["friends_tab_page_title"].wait_for_existing(raise_error=False):
            return self["friends_tab_page_title"].wait_for_existing(raise_error=False)

    def GetFriendsTabPageTitleText(self):
        if self["friends_tab_page_title"].wait_for_existing():
            return self["friends_tab_page_title"].text

    def CheckEmptyRecommendPermissonIcon(self):
        list = []
        for item in self["friends_tab_recommend_permission"].items():
            list.append(str(item["relation_icon"].visible))
        self.app.testcase.log_info(list)
        return list

    def GetEmptyRecommendPermissonTitle(self):
        list = []
        for item in self["friends_tab_recommend_permission_title"].items():
            list.append(str(item["relation_title"].text))
        self.app.testcase.log_info(list)
        return list

    def GetEmptyRecommendPermissonDesc(self):
        list = []
        for item in self["friends_tab_recommend_permission_title"].items():
            list.append(str(item["relation_title_desc"].text))
        self.app.testcase.log_info(list)
        return list

    def CheckEmptyRecommendPermissonRelationBtn(self):
        list = []
        for item in self["friends_tab_recommend_permission_relation"].items():
            list.append(str(item["relation_btn"].visible))
        self.app.testcase.log_info(list)
        return list

    def GetEmptyRecommendPermissonRelationBtn(self):
        list = []
        for item in self["friends_tab_recommend_permission_relation"].items():
            list.append(str(item["relation_btn"].text))
        self.app.testcase.log_info(list)
        return list

    def CheckEmptyRecommendUserhead(self):
        list = []
        for item in self["friends_tab_recommend_user_head"].items():
            list.append(str(item["user_head"].visible))
        self.app.testcase.log_info(list)
        return list

    def ClickEmptyRecommendUserhead(self,value=0):
        self["friends_tab_recommend_user_head"].items()[value]["user_head"].click()

    def CheckEmptyRecommendUsername(self):
        list = []
        for item in self["friends_tab_recommend_user_name"].items():
            list.append(str(item["user_name"].text))
        self.app.testcase.log_info(list)
        return list

    def CheckEmptyRecommendUserLabel(self):
        list = []
        for item in self["friends_tab_recommend_user_name"].items():
            list.append(str(item["user_label"].text))
        self.app.testcase.log_info(list)
        return list

    def ClickEmptyRecommendUsername(self,value=0):
        self["friends_tab_recommend_user_name"].items()[value]["user_name"].click()
        time.sleep(2)

    def ClickEmptyRecommendUserLabel(self,value=0):
        self["friends_tab_recommend_user_name"].items()[value]["user_label"].click()
        time.sleep(2)

    def GetEmptyRecommendRelationBtn(self):
        list = []
        for item in self["friends_tab_recommend_relation_btn"].items():
            list.append(str(item["relationBtn"].text))
        self.app.testcase.log_info(list)
        return list

    def ClickEmptyRecommendUserRelation(self,value=0):
        self["friends_tab_recommend_relation_btn"].items()[value]["relationBtn"].click()
        time.sleep(2)

    def CheckEmptyRecommendDeleteBtn(self):
        list = []
        for item in self["friends_tab_recommend_delete_btn"].items():
            list.append(str(item["delete_btn"].visible))
        self.app.testcase.log_info(list)
        return list

    def ClickEmptyRecommendUserDelete(self,value=0):
        self["friends_tab_recommend_delete_btn"].items()[value]["delete_btn"].click()
        time.sleep(2)

    def CheckEmptyRecommendAddFriendBtn(self):
        return self["friends_tab_add_friends"].wait_for_existing()

    def CheckEmptyRecommendSearchBox(self):
        return self["friends_tab_search_box"].wait_for_existing()

    def GetEmptyRecommendSearchBoxText(self):
        return self["friends_tab_search_box_text"].text

    def CheckEmptyRecommendSearchBoxBtn(self):
        if self["friends_tab_search_box_btn"].wait_for_existing(raise_error=False):
            return self["friends_tab_search_box_btn"].wait_for_existing()
        else:
            self["friends_tab_search_box_btn1"].wait_for_existing()
            return self["friends_tab_search_box_btn1"].wait_for_existing()

    def CheckEmptyRecommendScanBtn(self):
        return self["friends_tab_scan"].wait_for_existing()

    def CheckEmptyRecommendTitle(self):
        return self["friends_tab_recommend_text"].text


    def dismiss_sync_contacts_popup(self):
        if self["sync_contact_popup"].wait_for_existing(timeout=5, raise_error=False):
            self["sync_contact_popup"].click()

    def swipe_up(self):
        """上滑
        """
        self["content"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(2)

    def swipe_down(self):
        """下滑
        """
        self["content"].swipe(y_direction=-1, swipe_coefficient=6)
        time.sleep(2)

    def get_suggested_account_text(self):
        return self["suggested_account_text"].text

    def check_suggested_account_i_button(self):
        return self["suggested_account_i_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_suggested_account_head(self):
        return self["suggested_account_head"].wait_for_existing(timeout=5, raise_error=False)

    def click_suggested_account_head(self):
        if self["suggested_account_head"].wait_for_existing(timeout=5, raise_error=False):
            self["suggested_account_head"].click()
            time.sleep(3)
        else:
            self["suggested_account_head1"].click()

    def get_suggested_account_nickname(self):
        return self["suggested_account_nickname"].text

    def get_suggested_account_nickname2(self):
        print("-----------------:" + str(self["suggested_account_nickname2"].wait_for_existing(timeout=5, raise_error=False)))
        if self["suggested_account_nickname2"].wait_for_existing(timeout=5, raise_error=False):
            return self["suggested_account_nickname2"].text
        else:
            forYouPanel = ForYouPanel(root=self.app)
            forYouPanel.swipe(0,1,3)
            return self["suggested_account_nickname2"].text

    def get_suggested_account_reason(self):
        return self["suggested_account_reason"].text

    def get_suggested_account_follow_button_text(self):
        return self["suggested_account_follow_button"].text

    def check_suggested_account_delete(self):
        return self["suggested_account_delete"].wait_for_existing(timeout=5, raise_error=False)

    def search(self):
        self["find_friends_search_bar"].wait_for_visible(timeout=5, interval=0.1, raise_error=True)
        self["find_friends_search_bar"].click()

    def click_friends_tab(self):
        if self["friends_tab"].wait_for_existing(timeout=5, raise_error=False):
            self["friends_tab"].click()


class PopUpWindowShareOption(Window):
    """friend tab
    """

    window_spec = {"activity": "PopupWindow:.*"}

    def get_locators(self):
        return {
            "share_option_text": {'type': Control, 'path': UPath(id_ == 'tv_des', text_ == 'Video posted. You can now share it')},
            "share_option_after_post_success": {'type': Control, 'path': UPath(id_ == 'rl_publish_success_bg')},
            'mus_new_popview_notification_content': {'path': UPath(id_ == 'mus_new_popview_notification_content')},
            'mus_new_popview_notification_like_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_likes")},
            'mus_new_popview_notification_comment_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_comment")},
            'mus_new_popview_notification_at_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_at")},
            'mus_new_popview_notification_follow_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_follow")},
            'mus_new_popview_notification_message_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_message")},
            'mus_new_popview_notification_upvote_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_upvote")},
            'mus_new_popview_notification_live_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_live")},
            'mus_new_popview_notification_qna_content': {'path': UPath(id_ == "mus_new_popview_notification_tv_qna")},
        }

    def wait_for_mus_new_popview_notification_content_visible(self):
        self['mus_new_popview_notification_content'].wait_for_visible()

    def wait_for_mus_new_popview_notification_content_existing(self):
        self['mus_new_popview_notification_content'].wait_for_existing()

    def wait_for_mus_new_popview_notification_content_existing(self):
        self['mus_new_popview_notification_content'].wait_for_existing()

    def inbox_qipao_like_existing(self):
        self['mus_new_popview_notification_like_content'].wait_for_existing()

    def inbox_qipao_like_visible(self):
        self['mus_new_popview_notification_like_content'].wait_for_visible()

    def inbox_qipao_comment_existing(self):
        self['mus_new_popview_notification_comment_content'].wait_for_existing()

    def inbox_qipao_comment_visible(self):
        self['mus_new_popview_notification_comment_content'].wait_for_visible()

    def inbox_qipao_at_existing(self):
        self['mus_new_popview_notification_at_content'].wait_for_existing()

    def inbox_qipao_at_visible(self):
        self['mus_new_popview_notification_at_content'].wait_for_visible()

    def inbox_qipao_follow_existing(self):
        self['mus_new_popview_notification_follow_content'].wait_for_existing()

    def inbox_qipao_follow_visible(self):
        self['mus_new_popview_notification_follow_content'].wait_for_visible()

    def inbox_qipao_message_existing(self):
        self['mus_new_popview_notification_message_content'].wait_for_existing()

    def inbox_qipao_message_visible(self):
        self['mus_new_popview_notification_message_content'].wait_for_visible()

    def inbox_qipao_upvote_existing(self):
        self['mus_new_popview_notification_upvote_content'].wait_for_existing()

    def inbox_qipao_upvote_visible(self):
        self['mus_new_popview_notification_upvote_content'].wait_for_visible()
class FriendsTabElementList(Control):
    elem_path = UPath(type_ == "android.widget.ImageView", visible_ == True)
    elem_class = Control
