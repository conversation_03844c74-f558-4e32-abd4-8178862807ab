# -*- coding:utf-8 _*-
import time
from utils.common.log_utils import logger
from shoots.retry import Retry
from shoots_android.control import Control, TextEdit, TextView, Window
from uibase.upath import id_, class_, enabled_, text_, UPath, visible_, type_, tag_
from uibase.web import WebElement, Webview
from .base import BasePanel
from business.ttlh.utils.main.feedback import FeedbackPanel


class SettingsPanel(BasePanel):
    """
        设置界面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.setting.ui.I18nSettingNewVersionActivity|com.ss.android.ugc.aweme.setting.ui.SettingContainerActivity.*|com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(SettingsPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "title": {"type": Control, "path": UPath(id_ == "title")},
            "logout": {"type": Control, "path": UPath(id_ == 'cell')},
            "logout_btn": {"type": Control, "path": UPath(text_ == "Log out")},
            "manage_account": {"type": Control, "path": UPath(tag_ == 'account_and_safety')},
            "privacy_manager": {"type": Control, "path": UPath(tag_ == "privacy_manager")},
            "switch_account": {"type": Control, "path": UPath(tag_ == 'switch_account')},
            "settings_privacy": {"type": Control, "path": UPath(text_ == "Settings and privacy")},
            "security_btn": {"type": Control, "path": UPath(text_ == "Security")},
            "creator_tools": {"type": Control, "path": UPath(id_ == 'i18n_list') / 7},
            "account_1": {"type": Control, 'path': UPath(id_ == 'sheet_content_container') / 0 / 1 / 0},
            "account_2": {"type": Control, 'path': UPath(id_ == 'sheet_content_container') / 0 / 1},
            "add_account": {"type": Control, 'path': UPath(id_ == 'sheet_content_container') / -1},
            "logout_confirm_1_logout": {"type": Control, "path": UPath(text_ == "Log out", id_ == "action_sheet_action_content")},
            "logout_confirm_1_switch": {"type": Control, "path": UPath(id_ == "switch_account_btn")},
            "save_login_info_title": {"type": Control, "path": UPath(id_ == 'caption_area') / UPath(id_ == 'title_tv')},
            "save_login_info_save": {"type": Control, "path": UPath(id_ == "positive_button")},
            "save_login_info_notnow": {"type": Control, "path": UPath(text_ == 'Not now')},
            "logout_confirm_2_title": {"type": Control, "path": UPath(text_ == 'Log out?')},
            "logout_confirm_2_logout": {"type": Control, "path": UPath(text_ == "Log out")},
            "logout_confirm_2_cancel": {"type": Control, "path": UPath(text_ == 'Cancel')},
            "bind_phone_or_email_yes": {"type": Control, "path": UPath(id_ == 'positive_button')},
            "bind_phone_or_email_no": {"type": Control, "path": UPath(id_ == 'negative_button')},
            "bind_phone_or_email_title": {"type": Control, "path": UPath(id_ == 'tv_title')},
            "bind_phone_or_email_text": {"type": Control, "path": UPath(id_ == 'tv_content')},
            "privacy_settings_back": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "app_language": {"path": UPath(tag_ == 'common_protocol')},
            "app_language_1": {"path": UPath(~text_ == ".*语言|.*語言")},
            "language": {"path": UPath(text_ == "Language")},
            "translation_language": {"path": UPath(text_ == "Translation language")},
            "translation_language_panel": {"path": UPath(id_ == "list_language")},
            "translation_language_done": {"path": UPath(text_ == "Done", visible_ == True)},
            "translation_language_cancel": {"path": UPath(text_ == "Cancel", visible_ == True)},
            "agree_and_continue": {"path": UPath(text_ == "同意并继续")},
            "language_zh": {"path": UPath(text_ == "中文（简体）")},
            "language_en": {"path": UPath(text_ == "English")},
            "translation_language_set": {"path": UPath(id_ == "power_list") / 3 / UPath(id_ == "title_tv")},
            "preferred_languages_set": {"path": UPath(id_ == 'power_list') / 1 / UPath(id_ == 'title_tv')},
            "cn_title": {"path": UPath(text_ == '中文（繁體）', visible_ == True)},
            "report_problem": {"type": Control, "path": UPath(text_ == "Report a problem", visible_ == True)},
            "content": {"type": Control, "path": UPath(id_ == "content")},
            "balance_btn": {"type": Control, "path": UPath(text_ == "Balance", visible_ == True)}

        })

    def enter_choose_language_panel(self):
        if self["agree_and_continue"].wait_for_existing(timeout=5, raise_error=False):
            self["agree_and_continue"].click()
            time.sleep(2)

        if not self["app_language_1"].wait_for_existing(timeout=3, raise_error=False) and not self["app_language"].existing:
            self.swipe(y_direction=1, swipe_coefficient=3)

        if self["app_language_1"].wait_for_existing(timeout=5, raise_error=False):
            self["app_language_1"].click()
        else:
            self["app_language"].click()
        time.sleep(2)

    def find_report_problem_entry(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["report_problem"].wait_for_existing(timeout=5, raise_error=False):
                break
            self["content"].swipe(y_direction=1, swipe_coefficient=6)
            time.sleep(1)

    def click_balance_btn(self):
        if self["balance_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["balance_btn"].click()
        time.sleep(2)


    def click_report_problem_entry(self):
        self["report_problem"].click()
        time.sleep(5)

    def confirm_btn(self):
        if self["translation_language_done"].wait_for_existing(timeout=5, raise_error=False):
            self["translation_language_done"].click()
        time.sleep(2)

    def change_preferred_and_translation_language(self):
        if self["preferred_languages_set"].wait_for_existing(timeout=5, raise_error=False):
            self["preferred_languages_set"].click()
            while not self["cn_title"].wait_for_existing(timeout=1, interval=0.5, raise_error=False):
                self.swipe(y_direction=1)
                self.wait_for_ui_stable()
                self.refresh()
            self["cn_title"].click()
            self.confirm_btn()
        self.set_translation_language_cn()

    def judge_into_settings_page(self):
        self["title"].wait_for_visible()
        return self["title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def entering_into_settings_page(self):
        self["manage_account"].wait_for_visible()
        return self["manage_account"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def add_account_from_switchpanel(self):
        for _ in Retry(timeout=60, interval=1, raise_error=False):
            if not self["switch_account"].wait_for_visible(timeout=1, raise_error=False):
                self.swipe(y_direction=1)
                self.wait_for_ui_stable()
                self.refresh()
        self["switch_account"].click()
        self["add_account"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["add_account"].click()

    def find_manage_account_and_click(self):
        self["manage_account"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["manage_account"].click()

    def enter_privacy_manager_page(self):
        self["privacy_manager"].click()

    def back_profile_page(self):
        self["title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["privacy_settings_back"].click()
        time.sleep(2)

    def find_switch_account_and_click(self):
        while not self["switch_account"].existing:
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        self["switch_account"].click()

    def find_security_and_click(self):
        while not self["settings_privacy"].visible:
            self.swipe(y_direction=1, swipe_coefficient=3)
            self.wait_for_ui_stable()
            self.refresh()
        self["settings_privacy"].click()

    def click_security_button(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['security_btn'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return self['security_btn'].click()

    def find_creator_tools_and_click(self):
        while not self["creator_tools"].visible:
            self.swipe(y_direction=1, swipe_coefficient=3)
            self.wait_for_ui_stable()
            self.refresh()
        self["creator_tools"].click()

    def switch_account(self):
        self["account_2"].click()

    def find_logout_and_click(self):
        while not self["logout_btn"].existing:
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        self["logout_btn"].click()
        return self["logout_confirm_1_logout"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def log_out_confirm_1_logout(self):
        self["logout_confirm_1_logout"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["logout_confirm_1_logout"].click()
        return self["save_login_info_title"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def log_out_confirm_1_switch(self):
        self["logout_confirm_1_switch"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["logout_confirm_1_switch"].click()
        return self["save_login_info_title"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def log_out_save_loginfo_save(self):
        self["save_login_info_save"].click()
        return self["logout_confirm_2_title"].text == "Log out?"

    def log_out_save_loginfo_notnow(self):
        self["save_login_info_notnow"].click()
        time.sleep(3)
        self["logout_confirm_2_title"].refresh()
        print(self["logout_confirm_2_title"].text)
        return self["logout_confirm_2_title"].text == "Log out?"

    def log_out_confirm_2_logout(self):
        self["logout_confirm_2_logout"].refresh()
        self["logout_confirm_2_logout"].click()

    def log_out_confirm_2_cancel(self):
        self["logout_confirm_2_logout"].refresh()
        self["logout_confirm_2_cancel"].click()

    def check_bind_phone_or_email_popup(self):
        return self["bind_phone_or_email_yes"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def bind_phone_or_email_title_check(self):
        print(self["bind_phone_or_email_title"].text)
        return self["bind_phone_or_email_title"].text == "Your account needs to be updated"

    def bind_phone_or_email_text_check(self):
        print(self["bind_phone_or_email_text"].text)
        return self["bind_phone_or_email_text"].text == "To keep your account secure, link your phone number or " \
                                                        "email address before you log out."

    def bind_phone_or_email_yes(self):
        self["bind_phone_or_email_yes"].click()

    def bind_phone_or_email_no(self):
        self["bind_phone_or_email_no"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["bind_phone_or_email_no"].click()

    def into_language_select_panel(self):
        time.sleep(2)
        self["language"].click()
        time.sleep(1)

    def into_translation_language_select_panel(self):
        if self["translation_language"].wait_for_existing(timeout=5, raise_error=False):
            self["translation_language"].click()
        time.sleep(1)

    def set_translation_language(self, content_language):
        content_language_ele = Control(UPath(text_ == content_language, visible_ == True), root=self.app)
        while not content_language_ele.wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        content_language_ele.click()
        self["translation_language_done"].click()

    def set_translation_language_zh(self):
        while not self["language_zh"].wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        self["language_zh"].click()
        self["translation_language_done"].click()

    def set_translation_language_cn(self):
        while not self["cn_title"].wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        self["cn_title"].click()
        self.confirm_btn()

    def set_translation_language_en(self):
        while not self["language_en"].wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()
        self["language_en"].click()
        self["translation_language_done"].click()


class BindPhonePanel(BasePanel):
    """手机号绑定界面

    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.account.ui.BindOrModifyPhoneActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(BindPhonePanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "input_phone_number": {"type": TextEdit,
                                   "path": UPath(id_ == 'inputWithIndicatorEditText', visible_ == True, index=0)},
            "use_email": {"type": Control, "path": UPath(id_ == 'change_step1')},
            "send_code": {"type": Control, "path": UPath(id_ == 'loading_button_text')},
            "back_btn": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "error_message": {"type": Control, "path": UPath(id_ == 'result_indicator_group_text', visible_ == True)},

        })

    def judge_into_bind_phone_page(self):
        return self["input_phone_number"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def add_phone_number(self, phone_number):
        self["input_phone_number"].input(phone_number)
        time.sleep(1)
        self["send_code"].click()

    def check_error_message(self):
        return self["error_message"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def switch_to_email_verification(self):
        # 因为整个控件中只有右边红字能点击，所以设置了偏移量（相对控件中心的偏移）
        self["use_email"].click(300, 0)

    def back_to_previous_page(self):
        self["back_btn"].click()


class BindEmailPanel(BasePanel):
    """邮箱绑定界面

    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.v2.base.CommonFlowActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(BindEmailPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "input_email": {"type": TextEdit, "path": UPath(id_ == 'inputWithIndicatorEditText')},
            "use_phone": {"type": Control, "path": UPath(id_ == 'change_step1')},
            "send_code": {"type": Control, "path": UPath(id_ == 'loading_button_text')},
            "back_btn": {"type": Control, "path": UPath(id_ == 'back_btn')},

        })

    def judge_into_bind_email_page(self):
        return self["input_email"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def add_email(self, email):
        self["input_email"].input(email)
        self["send_code"].click()

    def use_phone(self):
        self["use_phone"].click()

    def back_to_previous_page(self):
        self["back_btn"].click()


class InputSMSCodePanel(BasePanel):
    """短信验证码输入界面

    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.account.ui.BindOrModifyPhoneActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(InputSMSCodePanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "input_sms_code": {"type": TextEdit, "path": UPath(id_ == 'inputCodeView')},
            "resend_code": {"type": Control, "path": UPath(id_ == 'inputCodeResendBtn', enabled_ == True)},
            "back_btn": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "error_message": {"type": Control, "path": UPath(id_ == 'result_indicator_group_text')},

        })

    def judge_into_sms_code_page(self):
        return self["input_sms_code"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def input_sms_code(self, verification_code):
        self["input_sms_code"].input(verification_code)

    def back_to_previous_page(self):
        self["back_btn"].click()

    def resend_code(self):
        self["resend_code"].click()

    def check_error_message(self):
        time.sleep(2)
        return self["error_message"].text == "Verification failed. Please click Resend and try again."


class ManageAccountPanel(BasePanel):
    """账号管理界面
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.setting.I18nSettingManageMyAccountActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ManageAccountPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "delete_account": {"type": Control, "path": UPath(id_ == 'power_list', visible_  == True) / 9 / UPath(id_ ==
                                                                                               'common_item_view')
                               },
            "change_phone_num": {"type": Control, "path": UPath(id_ == 'power_list', visible_  == True) / 1 / UPath(id_ ==
                                                                                                 'common_item_view')
                                 },
            "change_phone_num_send_code": {"type": Control, "path": UPath(id_ == 'button1')},
            "phone_num": {"type": Control, "path": UPath(id_ == 'tvw_item_right', visible_ == True)},
            "email": {"type": Control, "path": UPath(id_ == "power_list") / 2 / UPath(id_ == "cell_container")},
            "change_email_send_code": {"type": Control, "path": UPath(id_ == 'button1')},
            "business_account_entry": {"type": Control, "path": UPath(id_ == 'switch_account_to_ca_or_ba')},
            "change_phone_pop_up": {"path": UPath(id_ == 'visual_area')},
            "change_phone_click": {"path": UPath(text_ == "Change phone")},
            "account_information": {"path": UPath(id_ == "power_list") / 0 / UPath(id_ == "title_tv")},
            "acct_email": {"path": UPath(text_ == "Email")},
            "password": {"path": UPath(text_ == "Password")},
            "date_of_birth": {"path": UPath(text_ == "Date of birth")},
        })

    def delete_account(self):
        self["delete_account"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["delete_account"].click()

    def change_password(self):
        self["password"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["password"].click()

    def change_phone_num(self):
        self["change_phone_num"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["change_phone_num"].click()
        self["change_phone_pop_up"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        self["change_phone_click"].click()
        # self["change_phone_num_send_code"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        # self["change_phone_num_send_code"].click()

    def get_phone_num(self):
        self.refresh()
        num_str = self["phone_num"].text
        return num_str.split('*')[-1][0:2]

    def new_bind_email(self):
        self["account_information"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["account_information"].click()
        self["acct_email"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["acct_email"].click()


    def change_email(self):
        self["email"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["email"].click()
        self["change_email_send_code"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["change_email_send_code"].click()

    def business_account_and_click(self):
        self["business_account_entry"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["business_account_entry"].click()
        time.sleep(5)

    def enter_edit_age(self):
        self["account_information"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["account_information"].click()
        self["date_of_birth"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["date_of_birth"].click()

    def enter_edit_age_from_account(self):
        self["date_of_birth"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["date_of_birth"].click()


class ChangePasswordPanel(Window):
    """修改密码界面
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.account.login.v2.base.CommonFlowActivity"}

    def get_locators(self):
        return {
            #   code
            "input_code": {"type": TextEdit, "path": UPath(id_ == 'inputCodeView')},
            "wrong_notice": {"type": TextView, "path": UPath(id_ == 'result_indicator_group_text', visible_ == True)},
            "resend": {"type": Control, "path": UPath(id_ == 'inputCodeResendBtn')},
            "code-page-four-six": {"type": TextEdit, "path": UPath(id_ == 'baseI18nContentTitle')},
            #   password
            "password_title": {"type": TextView, "path": UPath(id_ == 'baseI18nContentTitle', visible_ == True)},
            "input_password": {"type": TextEdit,
                               "path": UPath(id_ == 'inputWithIndicatorEditText', visible_ == True, index=0)},
            "password_input_clear": {"type": Control,
                                     "path": UPath(id_ == 'inputWithIndicatorViewContainer') / 0},
            "password_input_confirm": {"type": Control, "path": UPath(id_ == 'loading_button_text')},
            #   email
            "email_input_clear": {"type": Control, "path": UPath(id_ == 'inputWithIndicatorViewContainer')},
            "cannot_access_email": {"type": Control, "path": UPath(id_ == 'change_step1')},
            "check_box": {"type": Control, "path": UPath(id_ == 'check_box')},
        }

    def change_auth_to_code(self, code):
        self["cannot_access_email"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["cannot_access_email"].click()
        self["input_code"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)
        self["input_code"].click()
        if "4-digit" in self["code-page-four-six"].text:
            self["input_code"].input(code[2:])
        else:
            self["input_code"].input(code)
        return self["input_password"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def input_email_wrong(self, email):
        while self["email_input_clear"].visible:
            self["email_input_clear"].click()
        self["input_password"].input(email)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            self.refresh()
            return self["wrong_notice"].text == "This email is already taken"
        else:
            return False

    def input_email_right(self, email):
        while self["email_input_clear"].visible:
            self["email_input_clear"].click()
        self["input_password"].input(email)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            self.refresh()
            return self["input_code"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        else:
            return False

    def input_wrong_code(self, code):
        self["input_code"].input(code)
        return self["wrong_notice"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def resend_code(self):
        if self["resend"].enabled:
            self["resend"].click()
            self.refresh()
            time.sleep(2)
            return self["resend"].enabled
        else:
            return True

    def input_right_code(self, code):
        self["input_code"].click()
        self.send_keys(67)
        if "4-digit" in self["code-page-four-six"].text:
            self["input_code"].input(code[2:])
        else:
            self["input_code"].input(code)
        return self["password_title"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def input_password_wrong(self, password):
        self["input_password"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        if self["password_input_clear"].visible:
            while self["password_input_clear"].visible:
                self["password_input_clear"].click()
        self["input_password"].input(password)
        return not self["password_input_confirm"].enabled

    def input_password_right(self, password):
        self["input_password"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        if self["password_input_clear"].visible:
            while self["password_input_clear"].visible:
                self["password_input_clear"].click()
        self["input_password"].input(password)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            return True
        else:
            return False

    def update_new_password(self, password):
        self["input_password"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        self["input_password"].input(password)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            return True
        else:
            return False

    def input_password_original(self, password):
        while self["password_input_clear"].visible:
            self["password_input_clear"].click()
        self["input_password"].input(password)
        if self["password_input_confirm"].enabled:
            self["password_input_confirm"].click()
            self.refresh()
            return self["wrong_notice"].text == "Your new password can’t be the same as your old password"
        else:
            return False

    def click_check_box(self):
        self['check_box'].wait_for_visible()
        self['check_box'].click()



class BindorModifyPhonePanel(Window):
    """绑定或者修改手机号界面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.account.ui.BindOrModifyPhoneActivity"}

    def get_locators(self):
        return {
            "code_input": {"type": TextEdit, "path": UPath(id_ == 'inputCodeView')},
            "new_phone_region": {"type": Control, "path": UPath(id_ == 'phone_input_view_country_layout')},
            "phone_num_input": {"type": TextEdit, "path": UPath(id_ == 'inputWithIndicatorEditText')},
            "confirm": {"type": Control, "path": UPath(id_ == 'loading_button_text')},
            "wrong_notice": {"type": TextView, "path": UPath(id_ == 'result_indicator_group_text', visible_ == True)},
            "use_email": {"type": Control, "path": UPath(id_ == 'change_step1')},
            "code-page-four-six": {"type": TextEdit, "path": UPath(id_ == 'baseI18nContentTitle')},
        }

    def input_code_right(self, code):
        self["code_input"].click()
        time.sleep(1)
        if "4-digit" in self["code-page-four-six"].text:
            self["code_input"].input(code[2:])
        else:
            self["code_input"].input(code)
        return self["new_phone_region"].wait_for_visible(raise_error=False)

    def input_code_wrong(self, code):
        self["code_input"].input(code)
        return self["wrong_notice"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def use_email(self):
        self["use_email"].click()

    def input_new_phone_num(self, phone_num):
        self["phone_num_input"].click()
        self["phone_num_input"].input(phone_num)
        if self["confirm"].enabled:
            self["confirm"].click()
            return True
        else:
            return False

class SecurityAndLoginPanel(Window):
    """安全及账号设备管理界面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "security_alerts": {"type": Control, "path": UPath(id_ == 'list') / 0},
            "manage_device": {"type": Control, "path": UPath(id_ == 'list') / 1},
            "manage_app_permissions": {"type": Control, "path": UPath(id_ == "list") / UPath(id_ == "title_tv",
            text_ == 'Manage app permissions')},
            "no_app_icon": {"type": Control, "path": UPath(id_ == 'iv_empty')},
        }

    def manage_device(self):
        if self["manage_device"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["manage_device"].click()
            return True
        else:
            return False

    def security_check(self):
        if self["security_alerts"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["security_alerts"].click()
            return True
        else:
            return False

    def manage_app_permissions(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["manage_app_permissions"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return self["manage_app_permissions"].click()
        return False


class ManageAppPermissionPanel(Window):
    """Manage app pemission page
        """
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {"no_app_icon": {"type": Control, "path": UPath(id_ == 'icon_iv')},
                "no_app_authorized_title": {"type": TextView, "path": UPath(id_ == 'status_view_flex_layout') /
                                                                      UPath(id_ == 'title_tv')},
                "no_app_authorized_desc": {"type": Control, "path": UPath(id_ == 'message_tv')},
                "list_auth_app": {'path': UPath(id_ == "list_auth_app")}
                }

    def no_app_authorized_title(self):
        time.sleep(1)
        return self["no_app_authorized_title"].text == "No apps authorized yet"

    def no_app_authorized_desc(self):
        time.sleep(1)
        return self["no_app_authorized_desc"].text \
               == "Apps with permission to access your TikTok data will appear here."

    def checkManageAppIcon(self):
        return self["no_app_icon"].visible

    def check_app_permission_panel(self):
        time.sleep(2)
        if self['list_auth_app'].visible:
            logger.debug("There are apps authorized")
            return True
        elif self['list_auth_app'].visible == False:
            logger.debug("There are no Apps authorized")
        return ""

    def get_authorized_apps(self):
        for item in self['list_auth_app'].children:
            authorized_app = Control(root=item, path=UPath(id_ == "title_tv", type_ == "com.bytedance.tux.input.TuxTextView"))
            if authorized_app.wait_for_visible(timeout=3, raise_error=False):
                logger.debug("app: " + authorized_app.text)

class SecurityCheckWebview(Webview):
    view_spec = {
        "url": "passport\/safe\/api\/account_protect\/index"
    }

    def get_locators(self):
        return {
            "title": {"type": WebElement, "path": UPath(class_ == 'bar-title', visible_ == True)},
        }

    def page_loading(self):
        return self["title"].wait_for_existing()


class SecurityCheckPanel(FeedbackPanel):

    def get_locators(self):
        return {"security_check_webview": {"type": SecurityCheckWebview, "path": UPath(id_ == 'webview_root')},
                }

    def security_check(self):
        return self["security_check_webview"].wait_for_existing()


class ManageDevicePanel(FeedbackPanel):

    def get_locators(self):
        return {
            "manage_device_panel": {"type": ManageDeviceWebview, "path": UPath(id_ == 'webview_root')},
        }

    def delete_device(self):
        return self["manage_device_panel"].delete_device()


class ManageDeviceWebview(Webview):
    view_spec = {
        "url": "passport\/safe\/login_device\/index"
    }

    def get_locators(self):
        return {
            "current_device": {"type": WebElement, "path": UPath(class_ == 'component-device-item isOwn')},
            "device_list": {"type": WebElement, "path": UPath(class_ == 'device-list')},
            "first_device_login_time": {"type": WebElement, "path": UPath(class_ == 'device-list') / 1 / UPath(
                class_ == 'content-login-time P2-Regular')},
            "delete_first_device": {"type": WebElement,
                                    "path": UPath(class_ == 'device-list') / 1 / UPath(type_ == 'I')},
            "delete_device_confirm": {"type": WebElement, "path": UPath(text_ == 'Remove')},
            "deleting": {"type": WebElement, "path": UPath(class_ == 'component-common-loading')},
            "delete_success_title": {"type": WebElement, "path": UPath(class_ == 'toast-content', visible_ == True,
                                                                       text_ == "Removed device")},

        }

    def delete_device(self):
        page_loading = self["current_device"].wait_for_visible()
        history_device = self["device_list"].wait_for_visible()
        if page_loading and history_device:
            first_login_info = self["first_device_login_time"].text
            self["delete_first_device"].click()
            self.refresh()
            self["delete_device_confirm"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
            self["delete_device_confirm"].click()
            self.refresh()
            self["deleting"].wait_for_disappear()
            self.refresh()
            self["first_device_login_time"].refresh()
            new_first_login_info = self["first_device_login_time"].text
            return new_first_login_info != first_login_info or self["delete_success_title"].wait_for_existing(
                timeout=2, raise_error=False)
        else:
            return False

class BlockedAccountedPageUsernameUpath(Control):
    def get_locators(self):
        return {
            "username": {"path": UPath(id_ == "black_item_name", visible_ == True)},
        }

class BlockedAccountedPageUsername(Control):
    elem_path = UPath(id_ == "black_list_recycler_view") / UPath(type_ == "FrameLayout") / UPath(id_ == "black_item_name", visible_ == True)
    elem_class = BlockedAccountedPageUsernameUpath

class BlockedAccountedPageUnblockUpath(Control):
    def get_locators(self):
        return {
            "unblock": {"path": UPath(id_ == "black_item_button", visible_ == True)},
        }
class BlockedAccountedPageUnblock(Control):
    elem_path = UPath(id_ == "black_list_recycler_view") / UPath(type_ == "FrameLayout") / UPath(id_ == "black_item_button", visible_ == True)
    elem_class = BlockedAccountedPageUnblockUpath

class PrivacyManagerPanel(BasePanel):
    """隐私设置页面

    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity|com.ss.android.ugc.aweme.setting.ui.SettingContainerActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(PrivacyManagerPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "title": {"type": Control, "path": UPath(id_ == "title")},
            "settings_and_privacy_title": {"type": Control, "path": UPath(type_ == "Yq2") / UPath(type_ == "TuxTextView", visible_ == True)},
            "new_settings_and_privacy_title": {"type": Control, "path": UPath(id_ == "i18n_list") / 0},
            "private_account": {"type": Control,
                                "path": UPath(id_ == 'list') / 1 / UPath(id_ == 'cell_container')},
            "public_account_yes": {"type": Control, "path": UPath(id_ == "button1")},
            "public_account_no": {"type": Control, "path": UPath(id_ == "button2")},
            "privacy_back": {"type": Control, "path": UPath(id_ == 'nav_start')},
            "suggest_account_entry": {"type": TextView, "path": UPath(id_ == 'list') / 2 / UPath(id_ == 'title_tv')},
            "suggest_contacts": {"type": TextView, "path": UPath(text_ == 'Contacts')},
            "suggest_facebook": {"type": TextView, "path": UPath(id_ == 'list') / 1 / UPath(id_ == 'title_tv')},
            "suggest_mutual": {"type": TextView, "path": UPath(id_ == 'list') / 2 / UPath(id_ == 'title_tv')},
            "suggest_open_link": {"type": TextView, "path": UPath(id_ == 'list') / 3 / UPath(id_ == 'title_tv')},
            "suggest_interested": {"type": TextView, "path": UPath(id_ == 'list') / 4 / UPath(id_ == 'title_tv')},
            "download_entry": {"type": Control, "path": UPath(text_ == 'Downloads')},
            "download_setting": {"type": Control, "path": UPath(id_ == 'cell_container')},
            "detail_page_back": {"type": Control, "path": UPath(id_ == 'nav_start')},
            "download_status_on": {"type": TextView, "path": UPath(text_ == 'On')},
            "download_status_off": {"type": TextView, "path": UPath(text_ == 'Off')},
            "download_setting_desc": {"type": TextView, "path": UPath(id_ == 'title_tv')},
            "ad_auth": {"type": Control, "path": UPath(id_ == 'list') / 7 / UPath(id_ == 'cell_container')},
            "following_list_entry": {"type": Control,
                                     "path": UPath(id_ == 'list') / 12 / UPath(id_ == 'cell_container')},
            "liked_video_entry": {"type": Control, "path": UPath(id_ == 'list') / 12 / UPath(id_ == 'title_tv')},
            "blocked_account": {"type": Control, "path": UPath(text_ == "Blocked accounts")},
            "sync_contacts_and_facebook_friends": {"type": Control, "path": UPath(id_ == 'list')/3/UPath(id_ == 'title_tv')},
            "sync_contacts_and_facebook_friends1": {"type": Control, "path": UPath(id_ == 'list')/4/UPath(id_ == 'title_tv')},
            "facebook_off": {"type": Control, "path": UPath(id_ == 'list')/4/UPath(type_ == 'com.bytedance.tux.input.TuxSwitch')},
            "contact_off": {"type": Control, "path": UPath(id_ == 'list')/1/UPath(type_ == 'com.bytedance.tux.input.TuxSwitch')},
            "qr_code": {"type": Control, "path": UPath(text_ == 'QR code')},
            "blocked_username": {"type": Control, "path": UPath(id_ == "black_list_recycler_view") / UPath(type_ == "FrameLayout", index=0) / UPath(id_ == "black_item_name")},
            "unblock_button": {"type": Control, "path": UPath(id_ == "black_list_recycler_view") / UPath(type_ == "FrameLayout", index=0) / UPath(id_ == "black_item_button")},
            "group_chat_entry": {"type": TextView, "path": UPath(id_ == 'list') / 14 / UPath(id_ == 'title_tv')},
            "set_everyone": {"type": TextView, "path": UPath(text_ == 'Everyone')},
            "set_friends": {"type": TextView, "path": UPath(text_ == 'Friends')},
            "set_no_one": {"type": TextView, "path": UPath(text_ == 'No one')},
            "set_onlyme": {"type": TextView, "path": UPath(text_ == 'Only me')},
            "message_entry": {"type": TextView, "path": UPath(id_ == 'list') / 13 / UPath(id_ == 'title_tv')},
            "comment_entry": {"type": Control, "path": UPath(text_ == 'Comments')},
            "location_services": {"path": UPath(text_ == 'Location Services', visible_ == True)},
            "location_services_note": {"path": UPath(id_ == 'list')/4/UPath(id_ == 'subtitle_tv')},
            "location_settings_title": {"path": UPath(id_ == 'nav_bar_title')},
            "blocked_accounts_username": {"type": BlockedAccountedPageUsername,"path": UPath(id_ == 'black_list_recycler_view', visible_ == True)},
            "blocked_accounts_unblock": {"type": BlockedAccountedPageUnblock,"path": UPath(id_ == 'black_list_recycler_view', visible_ == True)},
        })
    def GetUsername(self):
        list = []
        for item in self["blocked_accounts_username"].items():
            list.append(item["username"].text)
        return list

    def GetUnblock(self,value):
        print(self["blocked_accounts_unblock"].items())
        self["blocked_accounts_unblock"].items()[value].click()
        # for item in self["blocked_accounts_unblock"].items():
        #     item["unblock"][value].click()
        # key = self["blocked_accounts_unblock"].items()
        # key[value].click()

    def click_contact_off(self):
        if self["contact_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["contact_off"].click()
            time.sleep(3)

    def check_location_settings_title(self):
        return self["location_settings_title"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def click_facebook_off(self):
        if self["facebook_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["facebook_off"].click()
            time.sleep(3)

    def click_sync_contacts_and_facebook_friends(self):
        if self["sync_contacts_and_facebook_friends"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["sync_contacts_and_facebook_friends"].click()
            time.sleep(3)

    def get_sync_contacts_and_facebook_friends(self):
        return self["sync_contacts_and_facebook_friends"].text

    def click_sync_contacts_and_facebook_friends1(self):
        if self["sync_contacts_and_facebook_friends1"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["sync_contacts_and_facebook_friends1"].click()
            time.sleep(3)

    def judge_into_privacy_page(self):
        return self["title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def sug_contacts_exist(self):
        return self["suggest_contacts"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def sug_interested_exist(self):
        return self["suggest_interested"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def sug_contacts_and_click(self):
        self["suggest_contacts"].click()
        time.sleep(5)

    def sug_facebook_and_click(self):
        self["suggest_facebook"].click()
        time.sleep(5)

    def sug_mutual_and_click(self):
        self["suggest_mutual"].click()
        time.sleep(5)

    def sug_open_link_and_click(self):
        self["suggest_open_link"].click()
        time.sleep(5)

    def find_suggest_and_click(self):
        self["suggest_account_entry"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["suggest_account_entry"].click()
        time.sleep(5)

    def find_comment_and_click(self):
        self["comment_entry"].click()

    def find_group_chat_and_click(self):
        self["group_chat_entry"].click()

    def find_message_chat_and_click(self):
        self["message_entry"].click()

    def find_liked_video_and_click(self):
        self["liked_video_entry"].click()

    def group_chat_exist(self):
        if self["group_chat_entry"].visible:
            return True
        else:
            return False

    def message_chat_exist(self):
        if self["message_entry"].visible:
            return True
        else:
            return False

    def set_friends_and_click(self):
        self["set_friends"].click()
        time.sleep(5)

    def set_no_one_and_click(self):
        self["set_no_one"].click()
        time.sleep(5)

    def set_everyone_and_click(self):
        self["set_everyone"].click()
        time.sleep(5)

    def set_everyone_exist(self):
        if self["set_everyone"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return True
        else:
            return False

    def choose_everyone_exist(self):
        return self["set_everyone"]

    def set_onlyme_and_click(self):
        self["set_onlyme"].click()
        time.sleep(5)

    def swipe_to_bottom(self):
        while not self["blocked_account"].visible:
            self.swipe(y_direction=1)
            self.wait_for_ui_stable()
            self.refresh()

    def click_blocked_account(self):
        self["blocked_account"].click()
        time.sleep(3)

    def get_blocked_account(self):
        return self["blocked_account"].text

    def get_blocked_username(self):
        username = self["blocked_username"].text
        time.sleep(3)
        if "@" in username:
            return str(username[1:])
        else:
            return username

    def click_unblock_button(self):
        self["unblock_button"].click()
        time.sleep(3)

    def blocked_account_entry(self):
        return self["blocked_account"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def following_list_entry_and_clcik(self):
        self["following_list_entry"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["following_list_entry"].click()
        time.sleep(5)

    def find_private_account_and_click(self):
        self["private_account"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["private_account"].click()
        time.sleep(10)

    def public_account_confrim(self):
        self["public_account_yes"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["public_account_yes"].click()

    def public_account_cancel(self):
        self["public_account_no"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["public_account_no"].click()

    def find_download_entry_and_click(self):
        self["download_entry"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["download_entry"].click()
        self["download_setting"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def download_entry_not_exist(self):
        if self["download_entry"].visible:
            return False
        else:
            return True

    def find_download_setting_and_click(self):
        self["download_setting"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["download_setting"].click()
        time.sleep(3)

    def back_and_click(self):
        self["detail_page_back"].click()

    def download_status_on_or_off(self):
        if self["download_status_on"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return True
        if self["download_status_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return False

    def download_status_off(self):
        if self["download_status_off"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return True

    def download_setting_desc_show(self):
        return self["download_setting_desc"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def find_ad_auth_and_click(self):
        self["ad_auth"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["ad_auth"].click()
        time.sleep(2)

    def privacy_back_and_click(self):
        self["private_account"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["privacy_back"].click()

    def get_title_text(self):
        if self["settings_and_privacy_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return self["settings_and_privacy_title"].text

    def GetNewSettingsAndPrivacyTitle(self):
        if self["new_settings_and_privacy_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            return self["new_settings_and_privacy_title"].text

    def click_qr_cede(self):
        self["qr_code"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["qr_code"].click()
        time.sleep(3)


class AdsConfirmPop(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "ad_turn_off": {"type": Control, "path": UPath(type_ == 'com.bytedance.tux.widget.FairLayout') / 0},
            "ad_turn_off_title": {"type": Control, "path": UPath(id_ == 'caption_area') / UPath(id_ == 'title_tv')},
        }

    def ad_turn_off_title_show(self):
        self["ad_turn_off_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["ad_turn_off_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def find_ad_turn_off_and_click(self):
        self["ad_turn_off"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["ad_turn_off"].click()
        time.sleep(5)


class ProConfirmPop(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "private_cancel": {"type": Control, "path": UPath(text_ == 'Cancel')},
            "pro_private_popup_title": {"type": Control,
                                        "path": UPath(id_ == 'caption_area') / UPath(id_ == 'title_tv')},
        }

    def pro_private_popup_title_show(self):
        self["pro_private_popup_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["pro_private_popup_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def private_cancel_and_click(self):
        self["private_cancel"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["private_cancel"].click()
        time.sleep(5)


class SugAccountPop(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "sug_confirm": {"type": Control, "path": UPath(text_ == 'OK')},
            "sug_pop_title": {"type": Control, "path": UPath(id_ == 'caption_area') / UPath(id_ == 'title_tv')},
        }

    def sug_pop_exist(self):
        return self["sug_pop_title"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def sug_confirm_and_click(self):
        self["sug_confirm"].click()
        time.sleep(5)


class CreatorToolsPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.creatortools.CreatorToolsActivity"}

    def get_locators(self):
        return {
            "play_list": {"type": Control, "path": UPath(id_ == 'power_list', visible_  == True) / 5 / UPath(id_ == 'common_item_view')},
            "title": {"type": Control, "path": UPath(id_ == 'title')},
            "analytics": {"type": Control,
                          "path": UPath(id_ == 'title_tv', text_ == 'Analytics')},
            "creators_portal": {"type": Control,
                                "path": UPath(id_ == 'creator_tools_creators_portal') /
                                        UPath(id_ == 'tvw_left_content')},
            "promote": {"type": Control,
                        "path": UPath(id_ == 'creator_tools_promote') /
                                UPath(id_ == 'tvw_left_content')},
            "qa_item": {"type": Control,
                        "path": UPath(id_ == 'creator_tools_qa_item') /
                                UPath(id_ == 'tvw_left_content')},
            "creator_marketplace": {"type": Control,
                                    "path": UPath(id_ == 'creator_tools_creator_marketplace') /
                                            UPath(id_ == 'tvw_left_content')},
            "playlists": {"type": Control,
                          "path": UPath(id_ == 'creator_tools_playlists') /
                                  UPath(id_ == 'tvw_left_content')},
            "creator_fund_item": {"type": Control,
                                  "path": UPath(id_ == 'creator_tools_creator_fund_item') /
                                          UPath(id_ == 'tvw_left_content')},
            "shop": {"type": Control,
                     "path": UPath(id_ == 'creator_tools_shop') / UPath(id_ == 'tvw_left_content')},
            "shoutouts": {"type": Control,
                          "path": UPath(id_ == 'creator_tools_shoutouts') /
                                  UPath(id_ == 'tvw_left_content')},
            "back_btn": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "creator_tools_list": {"type": Control, "path": UPath(id_ == 'll_setting_list')},
        }

    def play_list_option_visible(self):
        return self["play_list"].visible

    def click_play_list_option(self):
        self["play_list"].click()

    def tools(self, index=0):
        time.sleep(1)
        items = self["creator_tools_list"].items()
        return items[index] if items else None

    def tool_title(self, tool):
        return tool.text

    def back_to_previous_page(self):
        self["back_btn"].click()

    def tools_num(self):
        return len(self["creator_tools_list"].items())

    def creator_tools_title_check(self):
        print(self["title"].text)
        return self["title"].text == "Creator tools"

    def get_analytics(self):
        print(self["analytics"].text)
        return self["analytics"]

    def get_creators_portal(self):
        print(self["creators_portal"].text)
        return self["creators_portal"]

    def get_promote(self):
        print(self["promote"].text)
        return self["promote"]

    def get_qa_item(self):
        print(self["qa_item"].text)
        return self["qa_item"]

    def get_creator_marketplace(self):
        print(self["creator_marketplace"].text)
        return self["creator_marketplace"]

    def get_playlists(self):
        print(self["playlists"].text)
        return self["playlists"]

    def get_creator_fund_item(self):
        print(self["creator_fund_item"].text)
        return self["creator_fund_item"]

    def get_shop(self):
        print(self["shop"].text)
        return self["shop"]

    def get_shoutouts(self):
        print(self["shoutouts"].text)
        return self["shoutouts"]

    def click_analytics_tab(self):
        self["analytics"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        self["analytics"].click()


class ChooseLanguageViewController(Window):
    """choose the language for app
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity|com.ss.android.ugc.aweme.setting.ui.SettingContainerActivity"}

    def get_locators(self):
        return {
            "list_language": {"type": TextView, "path": UPath(id_ == 'list_language')},
            "Eng": {"type": TextView, "root": "list_language", "path": UPath(text_ == "English")},
            "done": {"type": TextView, "path": UPath(~text_ == '完成|Done', visible_ == True)},
            "应用语言": {"path": UPath(~text_ == "应用语言|應用程式語言", visible_ == True)},
            "language": {"path": UPath(text_ == '语言')}
                }

    def select_language(self, language):
        for _ in Retry(timeout=5):
            if self["Eng"].existing and self["Eng"].visible:
                break
            elif self["应用语言"].existing and self["应用语言"].visible:
                self["应用语言"].click()
                break
        if language == "EN":
            self["Eng"].click()
            time.sleep(1)
        self["done"].click()
        time.sleep(1)

class NoticeCard(Control):
    """
    单条信息控件
    """
    def get_locators(self):
        return{
            "poi_entrance": {"path": UPath(id_ == 'notification_cover_right')},
            "time_head": {"path": UPath(id_ == 'tv_time_head')},
        }

class NoticeInboxList(Control):
    elem_path = UPath(id_ == "notification_root")
    elem_class = NoticeCard

class POIDetailPanel(Window):
    """poi detail panel
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity|com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "poi_title": {"path": UPath(~id_ == "poi_name", visible_ == True)},
            "poi_map_icon": {"path": UPath(id_ == 'fl_location_north', visible_ == True)},
            "location_title": {"path": UPath(id_ == 'root')/0/1/0},
            "map_area": {"path": UPath(id_ == 'fl_map_view_container', visible_ == True)},
            "content": {"path": UPath(id_ == 'content')},
            "sign_icon": {"path": UPath(type_ == 'com.bytedance.tux.icon.TuxIconView', visible_ == True, index=0)},
            "get_directions": {"path": UPath(id_ == 'design_bottom_sheet')},
            "copy_address": {"path": UPath(text_ == "Copy address")},
            "google_maps": {"path": UPath(text_ == "Google Maps")},
            "poi_address": {"path": UPath(type_ == 'android.widget.LinearLayout', visible_ == True)/UPath(type_ == 'com.bytedance.tux.input.TuxTextView', visible_ == True, index=1)},
            "add_favourite": {"path": UPath(text_ == "Add to Favorites")},
            "added_favourite": {"path": UPath(text_ == "Added to Favorites")},
            "add_favourite_image": {"path": UPath(id_ == 'poi_iv_collect_icon')},
            "back": {"path": UPath(id_ == "back")},
            # 位置权限弹窗
            "turn_on_location": {"path": UPath(text_ == "Turn on your location?")},
            "continue": {"path": UPath(text_ == 'Continue')},
            "notice_inbox_list": {"type": NoticeInboxList, "path": UPath(id_ == 'rv_list')},
            'user_avatar': {'path': UPath(id_ == 'user_avatar', visible_ == True)},
            'all_activity': {'path': UPath(id_ == 'tv_group_title')},
            "inbox_username": {"type": Control, "path": UPath(text_ == 'cduiauto002', visible_ == True)},
            "location_icon": {"type": Control, "path": UPath(id_ == "poi_address_container", visible_ == True)}

        }

    @property
    def poi_name(self):
        return self["poi_title"].text[0:5]

    def swipe_up(self, ratio=4):
        """上滑
        """
        rect = self["map_area"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["map_area"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)

    def click_poi_long_inbox(self, vdwindow):
        for _ in Retry(timeout=50):
            if self["user_avatar"].existing:
                self.app.get_device().press_back()
            notice_inbox_list = self["notice_inbox_list"].items()
            if len(notice_inbox_list) > 0:
                for item in notice_inbox_list:
                    if item.poi_entrance.wait_for_visible(timeout=1, raise_error=False):
                        item.poi_entrance.click()
                        if vdwindow.poi_anchor_comment.wait_for_visible(timeout=1, raise_error=False):
                            self.app.get_device().press_back()
                        if vdwindow.poi_subtitle.wait_for_visible(timeout=2, raise_error=False):
                            return item.poi_entrance
                        else:
                            self.app.get_device().press_back()

    def click_poi_more_inbox(self, vdwindow):
        self["notice_inbox_list"].wait_for_visible(timeout=60)
        for _ in Retry(limit=3):
            if self["user_avatar"].existing:
                self.app.get_device().press_back()
            notice_inbox_list = self["notice_inbox_list"].items()
            if len(notice_inbox_list) > 0:
                for item in notice_inbox_list:
                    if item.poi_entrance.wait_for_visible(timeout=1, raise_error=False):
                        item.poi_entrance.click()
                        if vdwindow.poi_anchor_comment.wait_for_visible(timeout=1, raise_error=False):
                            self.app.get_device().press_back()
                        if vdwindow.poi_icon_more.wait_for_visible(timeout=2, raise_error=False):
                            return item.poi_entrance
                        if self["all_activity"].existing:
                            pass
                        else:
                            self.app.get_device().press_back()

    #按控件位置截图
    def take_capture(self, area):
        time.sleep(5)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    def map_title(self):
        self.poi_title.click()
        for _ in Retry(limit=7, interval=2, raise_error=False):
            if self["map_area"].wait_for_visible(timeout=50, raise_error=False):
                break
            self.app.testcase.take_screenshot(self.app.get_device())
            if not self.poi_title.wait_for_visible(timeout=3, raise_error=False):
                self.app.get_device().press_back()
            self.poi_title.click()
        self["map_area"].wait_for_visible(timeout=50)

    def check_map(self):
        if self["google_maps"].wait_for_visible(timeout=5, raise_error=False):
            self["google_maps"].click()
            for _ in Retry(timeout=20, interval=0.1):
                if "map" in self.app.get_device().get_current_activity():
                    break
        else:
            logger.info("未发现谷歌地图选项")


class TikTokShopPanel(Window):
    """TikTok Shop"""
    window_spec = {
        "activity": "com.bytedance.hybrid.spark.page.SparkActivity"
    }

    def get_locators(self):
        return {
            "activity_container": {"type": Control, "path": UPath(id_ == 'activity_container')},
            "back_btn": {"type": Control, "path": UPath(id_ == "ic_nav_back", visible_ == True)},
            "wiki_detail_page_title": {"path": UPath(text_ == "Taylor Swift - Wikipedia", visible_ == True)}
        }

    def check_activity(self):
        if self["activity_container"].wait_for_existing(timeout=5, raise_error=False):
            return True

    def check_wiki_card_detail_page_element(self):
        return self["wiki_detail_page_title"].wait_for_visible(timeout=10, raise_error=False)


class AddLocationPanel(Window):
    """poi搜索界面"""
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"
    }

    def get_locators(self):
        return {
            "poi_list": {"type": PoiAddressList, "path": UPath(type_ == 'com.bytedance.ies.powerlist.PowerList', visible_==True)},
            "no_found": {"path": UPath(~id_ == "tv_title|title_tv", visible_ == True)},
            "search": {"path": UPath(id_ == 'et_input', visible_ == True)},
            "first_address": {"path": UPath(id_ == 'power_list')/0},
            "turn_on_location": {"path": UPath(text_ == 'Turn on your location?')},
            "continue": {"path": UPath(text_ == 'Continue')},
            "turn_on_btn": {"path": UPath(text_ == 'Turn on')},
            "close_btn": {"path": UPath(id_ == 'iv_back')},
            "open_settings": {"path": UPath(text_ == 'Open settings')},
            "recommended_location": {"path": UPath(type_ == "PowerList") / 4 / UPath(id_ == "tv_poi_name", depth=12)}
        }

    def get_location_address(self):
        for _ in Retry(limit=6, interval=2):
            if self["poi_list"].existing:
                self.find_log_address().click()
                break
            elif self["no_found"].existing:
                self["search"].text = "AAA"
                self["recommended_location"].click()
                break

    def find_log_address(self):
        for _ in Retry(timeout=20):
            for poi_item in self["poi_list"].items():
                address_name = poi_item["poi_name"].text
                if len(address_name) > 35:
                    return poi_item
            self["poi_list"].scroll(coefficient_y=0.7)

    def close_page(self):
        """
        关闭当前poi搜索页返回上个界面
        """
        for _ in Retry(timeout=7, interval=0.5):
            if self["close_btn"].existing:
                self["close_btn"].click()
            else:
                break


class PoiInfo(Control):

    def get_locators(self):
        return {
            "poi_name": {"path": UPath(type_ == 'com.bytedance.tux.input.TuxTextView', index=0)},
            "poi_address": {"path": UPath(type_ == 'com.bytedance.tux.input.TuxTextView', index=1)}
        }


class PoiAddressList(Control):
    elem_class = PoiInfo
    elem_path = UPath(type_ == "android.widget.LinearLayout")




class Qr_code_page(Window):
    """添加QR_code界面"""
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.share.qrcode.UserQRCodeActivity"
    }

    def get_locators(self):
        return {
            "nickname": { "path": UPath(id_ == 'qr_code_user_nickname')},
        }

    def get_nickname(self):
        return self["nickname"].text

