# -*- coding:utf-8 _*-
import time

from shoots_android.control import *
from uibase.upath import id_, class_
from uibase.web import WebElement, Webview, WebTextEdit
from .base import BasePanel


class MusicDetailPanel(BasePanel):
    """音乐页面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.music.ui.MusicDetailActivity|com.ss.android.ugc.aweme.splash.SplashActivity"}
    schema_url = "snssdk1233://music/detail/6647277225529838341"

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(MusicDetailPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "collect": {"path": UPath(type_ == 'com.bytedance.tux.button.TuxButton', visible_ == True)},
            "collect_new": {"path": UPath(id_ == "tv_music_collect", visible_ == True)},
            "added_title": {"path": UPath(id_ == 'tv_title', text_ == "Added to Favorites", visible_ == True)},
            "ok_btn": {"path": UPath(~id_ == 'positive_button|action_area')},
            "title": {"path": UPath(id_ == 'music_title')},
            "author": {"path": UPath(id_ == 'tv_nickname')},
            "video_count": {"path": UPath(id_ == 'used_count')},
            "original_label": {"path": UPath(id_ == 'feed_list')/0/UPath(id_ == 'iv_starter')},
            "share_btn": {"path": UPath(id_ == 'share_btn')},
            "share_panel_root": {"path": UPath(id_ == 'share_panel_root')},
            "start_record": {"path": UPath(id_ == 'start_record')},
            "play_on_cover": {"path": UPath(id_ == 'music_play_iv')},
            "stop_on_cover": {"path": UPath(id_ == 'music_stop_iv')},
            "use_sound": {"path": UPath(id_ == 'start_record_video_img')},
            "use_sound_1": {"path": UPath(id_ == 'start_record_title')},
            "use_sound_2": {"path": UPath(id_ == 'start_record_out_ring')},
            #   UGC contains PGC
            "contain_panel": {"path": UPath(id_ == 'tv_pgc_metadata_info')},
            #   PGC
            "author_link_panel": {"path": UPath(id_ == 'music_owner_rv_list')/0/0},
            #   Share Panel
            "first_friend": {"path": UPath(id_ == 'recycle_view')/0/1},
            "message": {"path": UPath(id_ == 'multi_share_et')},
            "send": {"path": UPath(id_ == 'send')},
            "message_send_feedback": {"path": UPath(id_ == 'tv_notice')},
            "copy_link": {"path": UPath(text_ == 'Copy link')},
            "copy_link_feedback": {"path": UPath(index=1, depth=1)/0/0},
            "qrcode": {"path": UPath(text_ == 'QR code')},
            "report": {"path": UPath(text_ == 'Report sound')},
            "cancel": {"path": UPath(id_ == 'share_panel_cancel')},
            "start_record": {"path": UPath(id_ == 'start_record_btn_vg')},
            "music_cover": {"path": UPath(id_ == 'music_cover')},
            "music_detail_list": {"path": UPath(id_ == "feed_list", visible_ == True)}

        })

    def get_normal_music_detail_list(self):
        self["music_detail_list"].refresh()
        return self["music_detail_list"].items()

    def play_normal_music_detail_video(self):
        if len(self.get_normal_music_detail_list()) > 0:
            return self.get_normal_music_detail_list()[4].click()

    def contain_panel_exist(self):
        return self["contain_panel"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def contain_panel_click(self):
        self["contain_panel"].click()
        time.sleep(2)

    def click_video(self, index=0):
        self["feeds"].items()[index].click()

    def author_panel_exist(self):
        return self["author_link_panel"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def click_author_name(self):
        self["author"].click()
        time.sleep(2)

    def click_author_panel(self):
        self["author_link_panel"].click()

    def music_original_visible(self):
        return self["original_label"].visible

    def music_author(self):
        return self["author"].text

    def music_user_count(self):
        count_str = self["video_count"].text
        count_str = count_str.split(' ')[0]
        print(count_str)
        if count_str[-1] == "K":
            count = float(count_str[:-1]) * 1000
        elif count_str[-1] == "M":
            count = float(count_str[:-1]) * 1000000
        else:
            count = float(count_str)
        return int(count)

    def play_on_cover(self):
        self["play_on_cover"].click()
        time.sleep(3)
        self["stop_on_cover"].wait_for_visible(raise_error=False)
        return self["stop_on_cover"].visible and not self["play_on_cover"].visible

    def stop_on_cover(self):
        self["stop_on_cover"].click()
        self["play_on_cover"].wait_for_visible(raise_error=False)
        return self["play_on_cover"].visible and not self["stop_on_cover"].visible

    def wait_for_favorites_dialog(self):
        return self["added_title"].wait_for_visible(raise_error=False)

    def close_dialog(self):
        if self["ok_btn"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["ok_btn"].click()
        else:
            pass

    def music_title(self):
        time.sleep(1)
        return self["title"].text

    def add_to_favorite(self):
        if self["collect"].wait_for_existing(timeout=5, raise_error=False):
            self["collect"].click()
        elif self["collect_new"].wait_for_existing(timeout=5, raise_error=False):
            self["collect_new"].click()
        time.sleep(2)
        self.close_dialog()

    def check_collect_text(self):
        if self.return_favorite_text() == "Added to Favorites":
            self.add_to_favorite()
        time.sleep(2)

    def return_favorite_text(self):
        if self["collect"].existing and self["collect"].text is not None:
            return self["collect"].text
        else:
            return self["collect_new"].text

    def remove_favorite(self):
        self.add_to_favorite()
        time.sleep(2)

    def share(self):
        self["share_btn"].click()
        self["cancel"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def wait_for_share_panel(self):
        return self["share_panel_root"].wait_for_visible()

    def share_music_to_friend(self):
        self["first_friend"].click()
        self["message"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["message"].input("this is very interesting music")
        self["send"].click()
        return self["message_send_feedback"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)

    def share_by_copy_link(self):
        self["copy_link"].click()
        return self["copy_link_feedback"].wait_for_existing(timeout=5, interval=0.2, raise_error=False)

    def share_QRcode(self):
        self["qrcode"].click()

    def share_report(self):
        self["report"].click()
        time.sleep(2)

    def share_cancel(self):
        self["cancel"].click()
        self["use_sound"].refresh()
        return self["use_sound"].visible

    def record(self):
        self["start_record"].click()

    def use_sound_format_1(self):
        return self["use_sound_1"].visible and not self["use_sound_2"].visible

    def use_sound_format_2(self):
        return self["use_sound_2"].visible and not self["use_sound_1"].visible

    def use_sound(self):
        self["use_sound"].click()
        time.sleep(2)

    def click_music_cover_animation(self):
        self['music_cover_animation'].click()


class QRcodePanel(BasePanel):
    window_spec = {"activity": "com.ss.android.ugc.aweme.qrcode.QRCodeActivityV2"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(QRcodePanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "title": {"path": UPath(id_ == 'foreground')/UPath(id_ == 'title')},
            "user_count": {"path": UPath(id_ == 'desc')},
        })

    def get_title(self):
        return self["title"].text

    def user_count(self):
        return self["user_count"].text


class ReasonList(ControlList):
    elem_class = WebElement
    elem_path = UPath(~class_ == "p1 content.*")


class MusicReportPanel(MusicDetailPanel):
    """音乐举报页面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.compliance.business.report.ReportWebPageDialogActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(MusicReportPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "webview": {"type": SoundReportWebview, "path": UPath(id_ == "webview_root")},
            "done_webview": {"type": ReportDoneWebview, "path": UPath(id_ == "webview_root")},
        })

    def share_report_configure(self):
        self.webview.choose_to_report()
        return self.done_webview.get_feedback()

    def done_click(self):
        self.done_webview.done_click()
        time.sleep(2)


class SoundReportWebview(Webview):
    view_spec = {
        "description": "\"attached\":true.*\"visible\":true"
    }

    def get_locators(self):
        return {
            "one_reason": {"type": WebElement, "path": UPath(text_ == "Violent and graphic content", type_ == 'SPAN')},
            "close": {"type": WebElement, "path": UPath(class_ == 'navbar-btn', visible_ == True)},
            # "reason_list": {"type": ControlList, "path": UPath(type_ == 'ul')},
            "submit": {"type": WebElement, "path": UPath(id_ == 'submitBtn')},
        }

    def choose_to_report(self):
        self["one_reason"].click()
        self["submit"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)
        self["submit"].click()

    @property
    def reason_content_list(self):
        reason_list = self["reason_list"].items()
        # return [reason.text for reason in reason_list]
        return reason_list


class ReportDoneWebview(Webview):
    view_spec = {
        "description": "\"attached\":true.*\"visible\":true"
    }

    def get_locators(self):
        return {
            "done": {"type": WebElement, "path": UPath(type_ == 'BUTTON')},
            "feedback": {"type": WebElement, "path": UPath(text_ == "Thanks for reporting")},
        }

    def get_feedback(self):
        self.refresh()
        return self["feedback"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def done_click(self):
        self["done"].click()
