from business.ttlh.utils.main.base import *
from shoots.retry import Retry
from uibase.base import UIScene


class ShareKitDemoPanel(BasePanel):
    """
    ShareKitDemo Panel
    """
    window_spec = {"activity": "com.bytedance.sdk.demo.share.MainActivity|com.bytedance.sdk.demo.share.SelectMediaActivity|com.bytedance.sdk.demo.share.ShareActivity"}

    def get_locators(self):
        return {
            # First Panel
            'share_button': {'path': UPath(id_ == "share_button")},
            'toggle_CK': {'path': UPath(id_ == "toggle")},
            'CK_field': {'type': TextEdit, 'path': UPath(id_ == "edittext", type_ == "androidx.appcompat.widget.AppCompatEditText")},
            # Second Panel
            'select_image_btn': {'path': UPath(id_ == "select_image")},

            # Third Panel
            'publish_btn': {'path': UPath(id_ == "share_button")},
            'toggle_GS': {'path': UPath(id_ == "toggle")}
        }

    def click_share(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['share_button'].wait_for_existing(timeout=10, interval=1, raise_error=False):
                return self['share_button'].click()
        
    def click_select_image_btn(self):
        for _ in Retry(timeout=10, interval=1, raise_error=True):
            if self['select_image_btn'].wait_for_existing(timeout=10, interval=1, raise_error=False):
                return self['select_image_btn'].click()

    def click_publish_btn(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['publish_btn'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['publish_btn'].click()
        
    def click_CK_toggle(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['toggle_CK'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['toggle_CK'].click()
            
    def input_CK(self, client_key):
        self['CK_field'].text = ""
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['CK_field'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self['CK_field'].input(client_key)
                return True
        return False
    
    def click_GS_toggle(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['toggle_GS'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['toggle_GS'].click()


class MediaGallery(Window):
    """
    Media Gallery for ShareKitDemo App
    """
    window_spec = {"activity": 'com.android.documentsui.picker.PickActivity', 
        "process_name":'com.google.android.documentsui'}

    def get_locators(self):
        return {
            # Album Panel
            'image': {'path': UPath(id_ == "dir_list") / 0 / UPath(id_ == "icon_thumb")},
            'choose_btn': {'path': UPath(id_ == "option_menu_search", desc_ == "搜索")},
            'select_btn': {'path': UPath(id_ == "action_menu_select")}
        }
    def click_image(self):
        self.wait_for_ui_stable()
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['image'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['image'].click()

    def choose_or_select_btn_exist(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['choose_btn'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return True
            elif self['select_btn'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return True
        return False
    
    def click_choose_or_select_btn(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['choose_btn'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return self['choose_btn'].click()
            elif self['select_btn'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return self['select_btn'].click()
        return False
    
    def get_choose_btn_scene(self):
        return UIScene(self).dump()
    
    def get_media_gallery_scene(self):
        return UIScene(self).dump()