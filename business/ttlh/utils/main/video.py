# -*- coding:utf-8 _*-
import os
from shoots_cv.cv import CV
from shoots_android.control import *
from re import search
from uibase.web import Webview, WebElement
from business.ttlh.utils.main.main import *
from uibase.upath import id_, text_, UPath, visible_, type_, href_, class_
from shoots_android.control import *
import time
import json


class Comment(Control):
    def get_locators(self):
        return {
            "content_msg": {"type": Control, "path": UPath(id_ == "content")},
            "digg_count": {"type": Control, "path": UPath(id_ == "tv_digg_count")},
            "iv_digg": {"type": Control, "path": UPath(id_ == "iv_digg")},
        }

    def long_click(self):
        rect = self["content_msg"].rect
        x = rect.left - 8 + rect.width / 5
        y = rect.top + rect.height - 11
        print("长按删除的坐标是: ", x, y)
        self.app.get_device().long_click(x, y, duration=3)

    def click_user_in_comment(self):
        rect = self["content_msg"].rect
        x = rect.left * 3 + rect.width / 4 - 20
        y = rect.top + rect.height - 23
        print("用户名的坐标是: ", x, y)
        self.app.get_device().click(x, y)

    def content(self):
        return self["content_msg"].text

    def digg_count(self):
        return int(self["digg_count"].text)

    def digg(self):
        self["iv_digg"].click()
        time.sleep(1)


class FilterCommentList(Control):
    elem_path = UPath(id_ == "container_bg")
    elem_class = Comment


class VideoDetailPanel(ForYouPanel):
    window_spec = {
        'activity': 'com.ss.android.ugc.aweme.detail.ui.DetailActivity|com.ss.android.ugc.aweme.detail.ui.DetailActivity#1|com.ss.android.ugc.aweme.splash.*'}

    report_schema_url = "snssdk1233://aweme/detail/6838566508914920709" \
                        "?push_id=6229344&label=official&gd_label=click_push_recommend"
    schema_url = "snssdk1233://aweme/detail/6905452329835826437" \
                 "?push_id=6229344&label=official&gd_label=click_push_recommend"

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ForYouPanel, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'content': {'type': Control, 'path': UPath(id_ == 'content', visible_ == True, index=0)},
            "origin_music_cover": {"type": Control, "path": UPath(id_ == 'origin_music_cover', visible_ == True)},
            "music_cover": {"type": Control, "path": UPath(id_ == 'music_cover', visible_ == True)},
            'comment_view_layout': {"type": Control, 'path': UPath(id_ == 'comment_view_layout', visible_ == True)},
            # 'comment_list_panel': {"type": Control, 'path':  UPath(id_ == "root_layout")/UPath(id_ == 'fl_comment_bg')},
            'comment_list_panel': {'path': UPath(id_ == "rlt_fragment_container") /
                                                            UPath(id_ == 'fl_comment_bg', visible_ == True)},

            "user_avatar": {"path": UPath(id_ == 'user_avatar', visible_ == True)},
            'follow_iv': {"type": Button, "path": UPath(id_ == "follow_iv")},
            'profile_btn_extra': {"type": Button, "path": UPath(id_ == "profile_btn_extra")},
            "follow": {"path": UPath(id_ == 'follow', visible_ == True)},
            "like": {"path": UPath(id_ == 'digg', visible_ == True)},
            "video_digg_count": {'path': UPath(id_ == 'digg_count', visible_ == True)},
            "comment": {"path": UPath(id_ == 'comment_image', visible_ == True)},
            "comment_edit": {"type": MTextEdit, "path": UPath(id_ == "interact_right_area", visible_ == True) / 3},
            "filter_comment_edit": {"path": UPath(id_ == "layout_edit_box_background", visible_ == True)},
            'comment_panel': {'root': 'comment_list_panel',
                              "path": UPath(id_ == 'llt_commment_list_container', visible_ == True)},
            'comment_list': {"type": CommentList, 'root': 'comment_panel', 'path': UPath(id_ == 'recyclerView')},
            'filter_video_comment_list': {"type": FilterCommentList, 'path': UPath(id_ == 'recyclerView', visible_ == True)},
            "comment_publish": {"type": CommentList, "path": UPath(id_ == 'comment_publish', visible_ == True)},
            "comment_delete": {"path": UPath(id_ == 'text1', text_ == 'Delete')},
            "comment_count": {"path": UPath(id_ == 'comment_count', visible_ == True)},
            "delete_video_btn": {"path": UPath(id_ == 'more_action_label', text_ == 'Delete')},
            "title": {"path": UPath(id_ == 'title', visible_ == True)},
            "user_name": {"path": UPath(id_ == 'layout_author_info', visible_ == True) / UPath(id_ == 'title')},
            "music_roll_name": {"path": UPath(id_ == "music_title_static", index=0)},
            "use_sound": {"path": UPath(id_ == 'record_text')},
            'iv_guide_test': {'path': UPath(text_ == "Swipe up for more", visible_ == True)},
            "iv_at": {"path": UPath(id_ == 'iv_at')},
            "iv_gift": {"path": UPath(id_ == "iv_gift", visible_ == True)},
            "iv_emoji": {"path": UPath(id_ == 'iv_emoji')},
            # share panel
            "share_entrance": {"type": Control, "path": UPath(id_ == 'share_iv', visible_ == True)},
            "more_entrance": {"type": Control, "path": UPath(id_ == 'more_iv', visible_ == True)},
            "share_panel_title": {"path": UPath(id_ == 'share_panel_title')},
            "one_friend": {"path": UPath(id_ == 'recycle_view') / 1},
            "share_top_panel": {"type": ScrollView, "path": UPath(id_ == 'recycle_view')},
            "send": {"path": UPath(id_ == 'send')},
            "send_confirm": {"path": UPath(id_ == 'share_complete_tips_rv', visible_ == True)},
            "report_entrance": {"path": UPath(id_ == 'share_action_label', text_ == 'Report')},
            'share_action_layout': {"type": Control,
                                    'path': UPath(~id_ == 'share_panel_action_container|im_action_container')},
            'action_list': {"type": ActionList, "root": "share_action_layout",
                            'path': UPath(~id_ == 'action_list|action_vertical_list')},
            "action_list_v2": {"type": Control, "path": UPath(id_ == "action_list")},
            'download_progress_bar': {'type': Control, 'path': UPath(id_ == 'root_download_progress_bar')},
            'download_success': {'type': Control, 'root': 'download_progress_bar',
                                 'path': UPath(text_ == 'Video saved')},
            'share_panel': {"type": ScrollView, "root": "share_action_layout",
                            'path': UPath(id_ == 'share_panel_action_bar')},
            "save_share_cancel": {"path": UPath(id_ == 'share_panel_cancel')},
            "save_video": {"path": UPath(text_ == 'Save video')},
            "add_to_favorites": {"type": Control,
                                 "path": UPath(id_ == 'action_vertical_list') / 2 / UPath(id_ == 'more_action_label')},
            "favorite_count": {"path": UPath(id_ == "favorite_count", visible_ == True)},  #Favorite icon count
            "add_to_playlist": {"type": Control,
                                "path": UPath(id_ == 'action_vertical_list') / 1 / UPath(id_ == 'more_action_label')},
            "add": {"path": UPath(text_ == "Add")},
            "added": {"path": UPath(text_ == "Added")},
            "privacy_settings": {"type": Control,
                                 "path": UPath(id_ == 'action_vertical_list') / 4 / UPath(id_ == 'more_action_label')},
            "add_to_favorites": {"type": Control, "path": UPath(id_ == 'action_vertical_list')/2/UPath(id_ == 'more_action_label')},
            "add_to_playlist": {"type": Control, "path": UPath(id_ == 'action_vertical_list')/1/UPath(id_ == 'more_action_label')},
            "privacy_settings": {"type": Control, "path": UPath(id_ == 'action_vertical_list')/4/UPath(id_ == 'more_action_label')},
            "action_vertical_list": {"type": Control, "path": UPath(id_ == 'action_vertical_list')},
            "video_status": {"type": Control, "path": UPath(id_ == 'label_tv', visible_ == True)},
            "more_panel_button": {"type": Control, "path": UPath(id_ == 'more_action_label')},
            "make_private": {"type": Control, "path": UPath(id_ == 'title_tv')},
            "confirm_change": {"type": Control, "path": UPath(text_ == 'Confirm')},
            "only_me": {"type": Control, "path": UPath(text_ == 'Only me', visible_ == True)},
            "back_privacy_settings": {"type": Control, "path": UPath(id_ == 'nav_end', visible_ == True) / UPath(
                type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "back_profile_page": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "close_comment": {"type": Control, "path": UPath(desc_ == "Close comments")},
            "share_panel_action_bar": {"type": Control, "path": UPath(id_ == 'share_panel_action_bar')},
            "share_panel_privacy_settings": {"type": Control, "path": UPath(id_ == 'action_list') / 0 / UPath(
                id_ == 'share_action_icon')},
            "privacy_settings_v2": {"type": Control, "path": UPath(id_ == 'share_panel_action_bar') / UPath(
                text_ == "Privacy settings")},
            "iv_close": {"type": Control, "path": UPath(id_ == "iv_close")},
            "analytics_tab": {"type": Control, "path": UPath(text_ == "Analytics", visible_ == True)},
              # report panel
            "share_panel_privacy_settings": {"type": Control, "path": UPath(id_ == 'action_list')/0/UPath(id_ == 'share_action_icon')},
            "privacy_settings_v2": {"type": Control, "path": UPath(id_ == 'share_panel_action_bar')/UPath(text_ == "Privacy settings")},
            # report panel

            #   Thanks for reporting
            "video_mask_view": {"path": UPath(id_ == 'video_mask_view', visible_ == True)},
            "show_video": {"path": UPath(id_ == 'button', visible_ == True)},
            "watch_anyway": {"path": UPath(id_ == 'button1', visible_ == True)},
            "back_btn": {"path": UPath(id_ == 'back_btn', visible_ == True)},
            "digg_count": {'path': UPath(id_ == 'digg_count', visible_ == True)},
            "trending_bar": {"path": UPath(id_ == 'trending_list_text_switcher', visible_ == True)},

            "allow_duet": {"path": UPath(id_ == 'duet_react_setting_item')},
            "allow_stitch": {"path": UPath(id_ == 'stitch_setting_item')},
            "allow_comment": {"path": UPath(id_ == 'comments_setting_item')},
            "video_setting": {"path": UPath(id_ == 'private_hint')},
            "video_everyone": {"path": UPath(id_ == 'tv_permission_open')},
            "video_friends": {"path": UPath(text_ == 'Friends')},
            "video_only_me": {"path": UPath(id_ == 'private_tv')},
            "privacy_title": {"path": UPath(id_ == 'nav_bar_title')},
            "privacy_confirm": {"path": UPath(text_ == 'Confirm')},
            "privacy_close": {"path": UPath(id_ == 'nav_end', visible_ == True) /
                                      UPath(type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "privacy_status_friends": {"path": UPath(id_ == 'tv_label')},
            "comment_setting": {"path": UPath(id_ == 'comments_setting_item') / UPath(id_ == 'tvw_left_content')},

            "comment_send": {"path": UPath(id_ == 'comment_send', visible_ == True)},
            "mention_list": {"path": UPath(id_ == 'rv_mention_list')},
            "comment_reply_button": {"path": UPath(id_ == 'comment_reply_button', text_ == 'Reply')},
            "reply_with_video": {"path": UPath(id_ == 'reply_with_video_new', visible_ == True)},
            "out_view": {"path": UPath(id_ == 'out_view')},
            "comment_out_view": {"path": UPath(id_ == 'comment_out_view', visible_ == True)},
            "confirm_delete": {"path": UPath(id_ == 'button1', ~text_ == 'Delete|删除')},
            "confirm_delete_processing": {"path": UPath(text_ == 'Delete')},
            "confirm_cancel": {"path": UPath(id_ == 'button1', text_ == 'Cancel')},
            "anchor_feed": {"path": UPath(id_ == "anchor_tag_title", visible_ == True)},
            "anchor_feed_panel_list": {"path": UPath(id_ == 'anchor_select_root')},
            "button_list": {"type": Control, "path": UPath(id_ == 'gradual_bottom', visible_ == True)},
            "detail_add_to_playlist": {"type": Control,
                                       "path": UPath(id_ == 'action_list') / 1 / UPath(id_ == 'share_action_icon')},
            "feed_playlist_icon": {"type": Control, "path": UPath(id_ == 'feed_report_warn_ll', visible_ == True)},
            'content_comment': {'path': UPath(id_ == 'fl_comment_container')},
            'video_no_comments': {"type": Control,
                                  "path": UPath(id_ == 'rlt_fragment_container') / UPath(id_ == 'title')},
            'message_tv': {'path': UPath(id_ == 'message_tv', visible_ == True)},
            'like_message_list': {'path': UPath(id_ == "list")},
            "video_view": {"path": UPath(id_ == "comment_edit_layout_new_root")},
            "view_video_name": {"path": UPath(id_ == "tv_name")},
            "video_favorites_icon": {"type": Control, "path": UPath(id_ == 'favorite_view_layout', visible_ == True)},

            # POI
            "poi_tag_root": {"path": UPath(id_ == 'anchor_tag_root', visible_ == True)},
            "poi_icon": {"path": UPath(id_ == 'anchor_tag_icon', visible_ == True)},
            "poi_title": {"path": UPath(id_ == 'anchor_tag_title', visible_ == True)},
            "poi_subtitle": {"path": UPath(id_ == 'layout_anchor_tag', visible_ == True)},
            "poi_area": {"path": UPath(id_ == "aweme_intro_ll", visible_ == True)},
            "poi_area_comment": {"path": UPath(id_ == 'header_anchor', visible_ == True)},
            "poi_comment_area": {"path": UPath(id_ == 'tag_flow_layout', visible_ == True)},

            # 多锚点展开页
            "poi_icon_more": {"path": UPath(id_ == 'anchor_tag_icon_more', visible_ == True)},  # 多锚点下拉图标
            "poi_select_more": {"path": UPath(id_ == 'anchor_select_root')},  # 多锚点展开区域
            "poi_anchor_icon": {"path": UPath(id_ == 'anchor_container') / 0 / UPath(id_ == 'common_anchor_icon')},
            "poi_anchor_title": {"path": UPath(id_ == 'anchor_container') / 0 / UPath(id_ == 'common_anchor_title')},
            "poi_anchor_subtitle": {
                "path": UPath(id_ == 'anchor_container') / 0 / UPath(id_ == 'common_anchor_subtitle')},
            "poi_anchor_cancel": {"path": UPath(id_ == 'anchor_select_cancel')},

            # Content Mobility - CLA related
            "video_pause": {"path": UPath(id_ == "tux_status_view")},
            "fyp_video_pause": {"path": UPath(id_ == "long_press_layout", visible_ == True)},
            # "captions_visible": {"path": UPath(id_ == "caption_text_view")},
            "captions_visible": {"path": UPath(id_ == "biz_top_container", visible_ == True) / UPath(id_ == "caption_text_view", depth=8)},
            "creator_caption": {"path": UPath(id_ == "tv_caption")},
            "hide_caption_tooltip": {"path": UPath(text_ == "Hide captions", visible_ == True)},
            "hide_caption_tooltip": {"path": UPath(type_ == "f")},
            "edit_caption_tooltip": {"path": UPath(text_ == "Edit captions")},

            "hide_caption_tooltip": {"path": UPath(text_ == "Hide captions", visible_ == True)},
            "edit_caption_tooltip": {"path": UPath(text_ == "Edit captions")},

            "caption_icon": {"path": UPath(id_ == "translation_source_icon", visible_ == True)},
            "hide_caption_icon": {"path": UPath(id_ == "collapse_caption_idle_view", visible_ == True)},
            "caption_root": {"path": UPath(id_ == 'caption_root_view', visible_ == True)},
            "caption_layout": {"path": UPath(id_ == "content_layout", visible_ == True)},
            "creator_cap_hide_caption_icon": {"path": UPath(id_ == "iv_caption", visible_ == True)},
            "see_translation": {"type": TextView, "path": UPath(text_ == 'See translation')},
            "translating": {"type": TextView, "path": UPath(text_ == "Translating...", visible_ == True)},
            "see_original": {"type": TextView, "path": UPath(text_ == 'See original', visible_ == True)},
            "description_text": {"type": TextView, "path": UPath(id_ == 'desc', visible_ == True)},
            "description_text_view": {"type": TextView, "path": UPath(id_ == "descView")},
            "video_captions_button": {"path": UPath(id_ == "btn_dismiss")},
            "long_press_panel_captions_button": {"path": UPath(text_ == "Captions")},
            "caption_toggle": {"path": UPath(desc_ == "Captions") / UPath(~type_ == "TuxSwitch|Twt|VIC")},
            "translation_toggle": {"path": UPath(id_ == "power_list") / 2 / UPath(~type_ == "TuxSwitch|Twt|VIC")},
            "close_combined_entrance": {"path": UPath(id_ == "nav_end") / UPath(type_ == "TuxIconView")},

            # 评论区锚点
            "poi_anchor_comment": {
                "path": UPath(~type_ == 'com.ss.android.ugc.aweme.poi.a.e|com.ss.android.ugc.aweme.poi.a.a.f')},
            "poi_anchor_comment_1": {"path": UPath(id_ == "multi_anchor_view")},
            "change_cancel_button": {"path": UPath(text_ == "Cancel")},
            "poi_anchor_icon_comment": {"path": UPath(type_ == 'com.ss.android.ugc.aweme.poi.a.e') / 0},
            "poi_anchor_title_comment": {"path": UPath(type_ == 'com.ss.android.ugc.aweme.poi.a.e') / 1},
            "poi_anchor_title_comment2": {
                "path": UPath(id_ == 'tag_flow_layout') / UPath(id_ == 'anchor_tag_root', visible_ == True)},
            "poi_anchor_subtitle_comment": {"path": UPath(type_ == 'com.ss.android.ugc.aweme.poi.a.e') / 2},
            "comment_top_edit": {"path": UPath(id_ == 'container', type_ == 'android.widget.LinearLayout') / UPath(
                id_ == 'comment_edit_new')},
            "new_comment_top_edit": {
                "path": UPath(id_ == 'container', visible_ == True) / UPath(id_ == 'comment_container')},
            "comment_reply_with_video": {"path": UPath(id_ == 'reply_with_video_new', visible_ == True)},
            "vote_root": {"path": UPath(id_ == 'layout_dialog_root', type_ == 'android.widget.LinearLayout')},
            "vote_title": {"path": UPath(id_ == 'title', type_ == 'com.bytedance.tux.input.TuxTextView')},
            "check_reply_text": {"path": UPath(id_ == 'descView', visible_ == True)},
            "comment_edit_input": {"path": UPath(id_ == 'comment_edit_new', visible_ == True)},
            "send_comment": {"path": UPath(id_ == "comment_send_new_area", visible_ == True)},
            "see_more_hashtags": {"path": UPath(id_ == "tv_toggle", visible_ == True)},
            "video_hashtags": {"path": UPath(id_ == "desc", visible_ == True)},
            "search_box": {"path": UPath(id_ == "ll_search_content", visible_ == True)},
            "search_text_area": {"path": UPath(id_ == "fl_intput_hint_container", visible_ == True)},
            "search_button": {"path": UPath(id_ == "tv_bar_search", visible_ == True)},
            "comment_top_words": {"path": UPath(id_ == "keyword", visible_ == True)},
            "profile_videos_list": {"path": UPath(id_ == "feed_list", visible_ == True)},
            "close_btn": {"type": Control, "path": UPath(id_ == 'close_iv')},
            "delete_btn": {"type": Control, "path": UPath(~text_ == 'Delete|删除')},
            "feed_debug_icon": {"type": Control, "path": UPath(id_ == 'group_176', visible_ == True)},
            "feed_debug_btn": {"type": Control, "path": UPath(id_ == "feed_debug_icon")},
            "copy_aweme_id":{"type": Control, "path":UPath(id_ == "copy_aweme_id")},
            "add_to_feed": {"type": Control, "path": UPath(text_ == 'Add to feed')},
            "story_label": {"type": Control, "path": UPath(id_ == 'story_tag_layout')},
            "share_panel_root": {"type": Control, "path": UPath(id_ == 'share_panel_root', visible_ == True)},
            "story_comment_panel": {"type": Control,
                                    "path": UPath(id_ == 'llt_comment_list_container', visible_ == True)},
            "back_button": {"type": Control,
                            "path": UPath(id_ == 'fragment_detail_top_bar_layout') / UPath(id_ == 'back_btn',
                                                                                           visible_ == True)},
            "poi_name": {"path": UPath(~id_ == 'poi_name|tag_flow_layout', visible_ == True)},
            "detail_comment_input": {"path": UPath(id_ == 'container', type_ == 'android.widget.LinearLayout') / UPath(
                id_ == 'comment_edit_new', depth=6)},
            "detail_comment_input_v2": {"path": UPath(id_ == 'comment_edit_new', visible_ == True)},

            # GiftPanel
            "close_gift_pop_up": {"type": Control,
                                  "path": UPath(id_ == "close_button", type_ == "com.bytedance.tux.icon.TuxIconView")},
            "gift_panel": {"path": UPath(id_ == "layout_input") / UPath(id_ == "container") / 1 /
                                   UPath(id_ == "gift_panel_container", index=0)},
            "gift_1": {"path": UPath(type_ == "RecyclerView", visible_ == True) /
                               UPath(type_ == "ConstraintLayout", index=0) /
                               UPath(id_ == "gift_image")},
            "selected_gift_image": {"path": UPath(id_ == "selected_gift_image", visible_ == True)},
            "close_gift_icon": {"path": UPath(id_ == "remove_gift_icon", visible_ == True)},
            "iv_head": {"path": UPath(id_ == 'iv_avatar')},
            "add_comment": {"path": UPath(id_ == 'tv_fake_edit')},
            "emoji_1": {"path": UPath(id_ == 'll_emoji_container') / 0},
            "emoji_2": {"path": UPath(id_ == 'll_emoji_container') / 1},
            "emoji_3": {"path": UPath(id_ == 'll_emoji_container') / 2},
            "hide_this_tool": {"path": UPath(id_ == "hide_this_tool")},
            "timeTv": {"path": UPath(~id_ == "timeTv|tv_post_time", visible_ == True)},
            "gift_coin_balance": {"path": UPath(id_ == "coin_number")},
            "gift_send_button": {
                "path": UPath(type_ == "com.bytedance.android.live.design.widget.LiveTextView", id_ == "ttlive_text",
                              visible_ == True)},
            # Photo mode
            "self_profile_red_bubble": {
                "path": UPath(id_ == "comment_bubble_list", visible_ == True)},
            "self_profile_red_bubble_tittle": {
                "path": UPath(type_ == "com.bytedance.tux.input.TuxTextView", id_ == "content_title",
                              visible_ == True)},
            "self_profile_red_bubble_description": {"path": UPath(id_ == "descView", visible_ == True)},
            "self_profile_red_bubble_avatar": {"path": UPath(id_ == "avatarIv", visible_ == True)},
            "self_profile_red_bubble_username": {"path": UPath(type_ == "com.bytedance.tux.input.TuxTextView", id_ == "nameTv", visible_ == True)},
            "self_profile_red_bubble_time": {
                "path": UPath(type_ == "com.bytedance.tux.input.TuxTextView", id_ == "timeTv", visible_ == True)},
            "see_more": {
                "path": UPath(~id_ == "tv_toggle|see_more", visible_ == True)},
            "more": {
                "path": UPath(~id_ == "tv_toggle|more", visible_ == True)},
            "photomode_feed_layout": {"path": UPath( id_ == "biz_top_container", visible_ == True)},
            "photomode_title": {"path": UPath(id_ == "post_mode_title", visible_ == True, enabled_ == True)},
            "donation_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(text_ == "Act to Change", visible_ == True)},
            # Q&A
            "qa_banner": {"path": UPath(id_ == "feed_report_warn_ll", visible_ == True)},
            "qa_banner_icon": {"path": UPath(id_ == "qna_feed_banner_icon", visible_ == True)},
            "qa_banner_arrow": {"path": UPath(id_ == "qna_feed_banner_arrow", visible_ == True)},
            "qa_banner_text": {"path": UPath(id_ == "qna_feed_banner_tv", visible_ == True)},
            "qa_desc": {"path": UPath(id_ == "desc", visible_ == True)},
            'notice_Inbox': {'path': UPath(text_ == "Inbox", visible_ == True)},
            'main_bottom_button_inbox_ImageView': {'path': UPath(id_ == 'main_bottom_button_inbox') / UPath(type_ == 'ImageView', visible_ == True)},
            'navigationBarBackground': {'path': UPath(id_ == 'navigationBarBackground')},
            'inboxRecyclerView_3': {'path': UPath(id_ == 'inboxRecyclerView') / 5},
            'inboxRecyclerView_2_awemenotice_tuxiconview': {'path': UPath(id_ == 'inboxRecyclerView') / 2 / UPath(id_ == 'awemenotice_tuxiconview')},
            'Activiti': {'path': UPath(text_ == 'Activities')},
            'inboxRecyclerView_2_iv_arrow': {'path': UPath(id_ == 'inboxRecyclerView') / 2 / UPath(id_ == 'iv_arrow')},
            'inboxRecyclerView_1_awemenotice_tuxiconview': {'path': UPath(id_ == 'inboxRecyclerView') / 1 / UPath(id_ == 'awemenotice_tuxiconview')},
            'inboxRecyclerView_1_iv_arrow': {'path': UPath(id_ == 'inboxRecyclerView') / 1 / UPath(id_ == 'iv_arrow')},
            'New foll': {'path': UPath(text_ == 'New followers')},
            'inboxRecyclerView_3_awemenotice_tuxiconview': {'path': UPath(id_ == 'inboxRecyclerView') / 3 / UPath(id_ == 'awemenotice_tuxiconview')},
            'inboxRecyclerView_3_awemenotice_tuxtextview': {'path': UPath(id_ == 'inboxRecyclerView') / 3 / UPath(id_ == 'awemenotice_tuxtextview')},
            'list_item_activity_time': {'path': UPath(id_ == 'list_item_activity_time', visible_ == True)},
            'inboxRecyclerView_2_root_layout': {'path': UPath(id_ == 'inboxRecyclerView') / 2 / UPath(id_ == 'root_layout')},
            'inboxRecyclerView_1_root_layout': {'path': UPath(id_ == 'inboxRecyclerView') / 1 / UPath(id_ == 'root_layout')},
            'inboxRecyclerView_3_root_layout': {'path': UPath(id_ == 'inboxRecyclerView') / 3 / UPath(id_ == 'root_layout')},
            'permission_title_tv': {'path': UPath(id_ == 'permission_title_tv')},
            'QfK': {'path': UPath(type_ == 'QfK')},
            'permission_find_btn': {'path': UPath(id_ == 'permission_find_btn')},
            'OK': {'path': UPath(text_ == 'OK')},
            '9zB_0_inbox_iv_cover': {'path': UPath(type_ == '9zB') / 0 / UPath(id_ == 'inbox_iv_cover')},
            'Symphony': {'path': UPath(text_ == 'Symphony Crystal')},
            'name_tv_spirti80': {'path': UPath(id_ == 'name_tv', text_ == 'spirti8045')},
            'key_js_object_global_props': {'path': UPath(type_ == "9zB") / 0 / UPath(id_ == "inbox_icon_tag")},
            'inboxRecyclerView_0_10_avatar_iv': {'path': UPath(id_ == 'inboxRecyclerView') / 0 / 10 / UPath(id_ == 'avatar_iv')},
            'close_iv': {'path': UPath(id_ == 'close_iv')},
            'tagged_tv_label': {'path': UPath(id_ == 'tv_label')},
            "activity_back_btn": {"path": UPath(id_ == "back_btn", visible_ == True)},
            'Inbox': {'path': UPath(text_ == 'Inbox', visible_ == True)},
            'expose_top_text': {'path': UPath(id_ == 'expose_top_text')},
            'activity_iv_close': {'path': UPath(id_ == "iv_close")},
            'comment_back_layout_back_btn': {'path': UPath(id_ == 'comment_back_layout') / UPath(id_ == 'back_btn')},
            'See tran': {'path': UPath(text_ == 'See translation')},
            'back_btn_TuxIconView': {'path': UPath(id_ == 'back_btn', type_ == 'TuxIconView')},
            'back_btn_juhe': {'path': UPath(id_ == "comment_back_layout") / UPath(id_ == "back_btn")},
            'Reply_with_video_desc': {'path': UPath(id_ == 'desc')},
            'post_testw_905': {'path': UPath(text_ == 'testw_905')},
            'Commented_Story_testw_903': {'path': UPath(text_ == 'testw_903', visible_ == True)},
            'Reply_Comment_text': {'path': UPath(text_ == "大哭说你难受难受", visible_ == True)},
            'inboxRecyclerView_2_list_item_activity_content': {'path': UPath(id_ == 'inboxRecyclerView') / 2 / UPath(id_ == 'list_item_activity_content')},
            'inboxRecyclerView_1_list_item_activity_content': {'path': UPath(id_ == 'inboxRecyclerView') / 1 / UPath(id_ == 'list_item_activity_content')},
            'inboxRecyclerView_3_list_item_activity_content': {'path': UPath(id_ == 'inboxRecyclerView') / 3 / UPath(id_ == 'list_item_activity_content')},
            'tv_label': {'path': UPath(id_ == 'tv_label')},
            'content_\u200e不得不说计算机': {'path': UPath(id_ == 'content', text_ == '\u200e不得不说计算机专业')},
            'container_bg_ConstraintLayout_title': {'path': UPath(id_ == 'container_bg', type_ == 'ConstraintLayout') / UPath(id_ == 'title')},
            'recyclerView__1_title': {'path': UPath(id_ == 'recyclerView', visible_ == True) / 1 / UPath(id_ == 'title')},
            'recyclerView__0_title': {'path': UPath(id_ == 'recyclerView', visible_ == True) / 0 / UPath(id_ == 'title')},
            "notice_title": {"path": UPath(id_ == 'title', visible_ == True)},
            'tv_upvote': {'path': UPath(id_ == 'tv_upvote', visible_ == True)},
            'tv_name': {'path': UPath(id_ == 'tv_name')},
            'repost_comment': {'path': UPath(text_ == 'testw_903')},
            '\u200e。精神百倍束手': {'path': UPath(text_ == '\u200e。精神百倍束手束脚')},
            'inbox_view': {'type': entrance_list_Btn, 'path': UPath(id_ == "inboxRecyclerView", visible_ == True)},
            'inboxRecyclerView': {'path': UPath(id_ == 'inboxRecyclerView')},
            'skylight_name_list': {'type': skylight_name_Btn, 'path': UPath(id_ == "inboxRecyclerView", visible_ == True)},
            'skylight_icon_list': {'type': skylight_icon_Btn, 'path': UPath(id_ == "inboxRecyclerView", visible_ == True)},
            'story_tianchuang_name_list': {'type': story_tianchuang_name_Btn, 'path': UPath(id_ == "inboxRecyclerView", visible_ == True)},
            'story_tianchuang_icon_list': {'type': story_tianchuang_icon_Btn, 'path': UPath(id_ == "inboxRecyclerView", visible_ == True)},
            'story_tianchuang_ring_list': {'type': story_tianchuang_ring_Btn, 'path': UPath(id_ == "inboxRecyclerView", visible_ == True)},
            '9NE': {'path': UPath(type_ == 'X.9NE')},
            'notice_inbox_hongdian': {'path': UPath(id_ == "main_bottom_button_inbox") / 4},
            'notice_inbox_weidu_hongdian': {'path': UPath(id_ == "main_bottom_button_inbox") / 3},
            'user_avatar_layout_indicator': {'path': UPath(id_ == 'user_avatar_layout_indicator', visible_ == True)},
            'comment_image': {'path': UPath(id_ == 'comment_image')},
            'recyclerView_0_avatar': {'path': UPath(id_ == 'recyclerView') / 0 / UPath(id_ == 'avatar')},
            'v_touch_area': {'path': UPath(id_ == 'v_touch_area', visible_ == True)},
            '\u200eYou rep': {'path': UPath(text_ == '\u200eYou reposted*')},
            'recyclerView_2_digg_and_hate_view_0': {'path': UPath(id_ == 'recyclerView') / 2 / UPath(id_ == 'digg_and_hate_view') / 0},

            # video reply
            "emoji_recommend_panel": {"path": UPath(~id_ == "emoji_recommendation_panel|emoji_panel", visible_ == True, index=0)},
            "reply_input_container": {"path": UPath(id_ == "reply_message_tux_text", visible_ == True)},
            "input_area": {"type": Control, "path": UPath(id_ == "background")}
        })

    def click_emoji_icon_and_comment(self, content):
        if self["iv_emoji"].wait_for_existing(timeout=5, raise_error=False):
            self["iv_emoji"].click()
            time.sleep(2)
        if self["emoji_recommend_panel"].wait_for_existing(timeout=5, raise_error=False):
            self["emoji_recommend_panel"].items()[1].click()
            time.sleep(2)
        return self["comment_edit_input"].input(content)

    def at_user_in_comment(self, content):
        if self["iv_at"].wait_for_existing(timeout=5, raise_error=False):
            self["iv_at"].click()
            time.sleep(3)
            self["filter_comment_edit"].click()
            time.sleep(3)
        return self["filter_comment_edit"].input(content)

    def get_profile_videos_list(self):
        self["profile_videos_list"].refresh()
        return self["profile_videos_list"].items()

    def play_normal_user_profile_video(self):
        if len(self.get_profile_videos_list()) > 0:
            return self.get_profile_videos_list()[4].click()

    def click_video_detail_page_comments_top_words(self):
        if self["comment_top_words"].wait_for_existing(timeout=5, raise_error=False):
            self["comment_top_words"].click()

    def click_video_detail_page_search_button(self):
        if self["search_button"].wait_for_existing(timeout=5, raise_error=False):
            self["search_button"].click()

    # **Start Photo mode**
    def check_photomode_title(self):
        return self["photomode_title"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_donation_anchor(self):
        return self["donation_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def is_self_profile_red_bubble_exist(self):
        return self["self_profile_red_bubble"].wait_for_visible(timeout=5, interval=1, raise_error=False)
    def click_video_detail_page_search_box(self):
        self["search_box"].wait_for_existing(timeout=5, interval=0.1, raise_error=True)
        self["search_box"].click()

    def is_self_profile_red_bubble_avatar_exist(self):
        return self["self_profile_red_bubble_avatar"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def is_self_profile_red_bubble_username_exist(self):
        return self["self_profile_red_bubble_username"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def is_self_profile_red_bubble_title_exist(self):
        return self["self_profile_red_bubble_tittle"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def is_self_profile_red_bubble_time_displayed(self):
        return self["self_profile_red_bubble_time"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_video_hashtags(self):
        rect = self["video_hashtags"].rect
        x = rect.left + rect.width / 2
        y = rect.top + rect.height / 1.5
        print("视频详情页话题坐标", x, y)
        self.app.get_device().click(x, y)
        # if self["see_more_hashtags"].wait_for_existing(timeout=5, raise_error=False):
        #     self["see_more_hashtags"].click()
        #     time.sleep(3)
        # self["video_hashtags"].click(offset_y=-20)
        time.sleep(3)

    # 获取评论区sug列表
    def get_mention_list(self):
        self["mention_list"].refresh()
        return self["mention_list"].items()

    # 点击评论区第一个sug
    def click_first_mention(self):
        if len(self.get_mention_list()) > 0:
            return self.get_mention_list()[0].click()

    # 视频详情页评论
    def comment(self, content):
        """评论
        :param content 内容
        """
        time.sleep(1)
        self["comment_edit"].refresh()
        self["comment_edit"].click()
        time.sleep(1)
        # self["ok_button"].click()
        # time.sleep(1)
        self["comment_edit_input"].input(content)
        time.sleep(2)

    # **End Photo mode**

    def check_correct_video(self, aweme_id):
        # Retry for only 15 times as it is the amount of feed that gets pulled down from recommendation per refresh
        time.sleep(5)
        for _ in Retry(limit=15, interval=1):
            feed_video_details = self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="copyContent")
            feed_video_details = json.loads(feed_video_details)
            curr_vid_aweme = feed_video_details["aweme_id"]
            time.sleep(3)
            logger.info("Current AwemeID: " + curr_vid_aweme)
            logger.info("Current AwemeID: " + aweme_id)
            if (str(aweme_id) == str(curr_vid_aweme)):
                logger.info("Matching AwemeID: " + curr_vid_aweme)
                return True
            self.swipe_up()
        return False
    # 视频垂搜页滑动
    def wait_for_loading(self):
        # if self["iv_guide"].wait_for_existing(timeout=5, interval=0.2, raise_error=False):
        self.swipe_up()

    def video_pause(self):
        self["video_pause"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["video_pause"].click()

    def click_poi_name(self):
        if self["detail_comment_input"].wait_for_existing(timeout=5, raise_error=False):
            if self["detail_comment_input_v2"].wait_for_existing(timeout=5, raise_error=False):
                self["detail_comment_input_v2"].click()
            time.sleep(2)
            other_window_panel = OtherWindow(root=self.app)
            if other_window_panel.paste_btn.wait_for_existing(timeout=5, raise_error=False):
                self.app.get_device().press_back()
            time.sleep(2)
            self.app.get_device().press_back()
        time.sleep(2)
        self["poi_name"].click()

    def fyp_video_toggle(self):
        self["fyp_video_pause"].click()

    def wait_for_caption_visible(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        self["captions_visible"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def click_creator_caption_when_visible(self):
        if self["creator_cap_hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["creator_cap_hide_caption_icon"].click()
        self["creator_caption"].wait_for_visible(timeout=10, interval=1, raise_error=False)
        for _ in Retry(limit=30, interval=1):
            if self["creator_caption"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["creator_caption"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["creator_caption"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    self["creator_caption"].click()
                    break
                self.fyp_video_toggle()

    def captions_visible(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        if self["captions_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            return True
        return False

    def click_folded_caption_icon(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()

    def click_captions_button_in_long_press_panel(self):
        self["long_press_panel_captions_button"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["long_press_panel_captions_button"].click()

    def click_caption_toggle(self):
        self["caption_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["caption_toggle"].click()

    def caption_toggle_visibility(self):
        self["caption_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        return self["caption_toggle"].visible

    def click_translation_toggle(self):
        self["translation_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["translation_toggle"].click()

    def translation_toggle_visibility(self):
        self["translation_toggle"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        return self["translation_toggle"].visible

    def close_combined_entrance(self):
        self["close_combined_entrance"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["close_combined_entrance"].click()

    def pause_video_when_caption_is_displayed(self):
        for _ in Retry(limit=30, interval=1):
            if self['captions_visible'].visible == True:
                self.fyp_video_toggle()
                break

    def asr_caption_visible(self):
        return self["captions_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def creator_caption_visible(self):
        return self["creator_caption"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_asr_caption(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        # Trying to click on the caption for 90 seconds, with each time waiting for the caption to be visible with timeout=5 interval=1
        for _ in Retry(limit=30, interval=1):
            if self["captions_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["captions_visible"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["captions_visible"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    self["captions_visible"].click()
                    break
                self.fyp_video_toggle()

    def click_asr_caption_when_pause(self):
        self["captions_visible"].click()

    def click_creator_caption_when_pause(self):
        self["creator_caption"].click()

    def asr_caption_tooltip_visible(self):
        return self["hide_caption_tooltip"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def edit_caption_tooltip_visible(self):
        return self["edit_caption_tooltip"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_edit_caption_tooltip(self):
        if self["edit_caption_tooltip"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["edit_caption_tooltip"].click()
            time.sleep(3)

    def creator_caption_tooltip_visible(self):
        popup = UploadStoryPopupWindow(root=self.app)
        return popup.creator_caption_tooltip_visible()

    def click_asr_caption_tooltip(self):
        if self["hide_caption_tooltip"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            self["hide_caption_tooltip"].click()

    def click_creator_caption(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        # Trying to click on the caption for 90 seconds, with each time waiting for the caption to be visible with timeout=5 interval=1
        for _ in Retry(limit=30, interval=1):
            if self["creator_caption"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["creator_caption"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["creator_caption"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    self["creator_caption"].click()
                    break
                self.fyp_video_toggle()

    def hide_asr_caption(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        # Trying to click on the caption for 90 seconds, with each time waiting for the caption to be visible with timeout=3 interval=1
        for _ in Retry(limit=30, interval=1):
            if self["captions_visible"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
                self["captions_visible"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["captions_visible"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    self["captions_visible"].click()
                    if self["hide_caption_tooltip"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                        self["hide_caption_tooltip"].click()
                        break
                self.fyp_video_toggle()

    def fold_asr_caption_when_caption_visible(self):
        if self["captions_visible"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            self["captions_visible"].click()
            if self["hide_caption_tooltip"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                self["hide_caption_tooltip"].click()

    def fold_creator_caption_when_caption_visible(self):
        if self["creator_caption"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            self["creator_caption"].click()
            popup = UploadStoryPopupWindow(root=self.app)
            popup.click_tooltip()

    def hide_creator_caption(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        # Trying to click on the caption for 90 seconds, with each time waiting for the caption to be visible with timeout=3 interval=1
        for _ in Retry(limit=30, interval=1):
            if self["creator_caption"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
                self["creator_caption"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["creator_caption"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    self["creator_caption"].click()
                    if self["video_captions_button"].wait_for_visible(timeout=3, interval=1, raise_error=False):
                        self["video_captions_button"].click()
                        self["creator_caption"].click()
                    popup = UploadStoryPopupWindow(root=self.app)
                    popup.click_tooltip()
                    break
                self.fyp_video_toggle()

    def first_time_creator_caption_panel(self):
        if self["video_captions_button"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            self["video_captions_button"].click()

    def caption_icon(self):
        # return self["caption_icon"].visible
        return self["caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def collapsed_icon_visibility(self):
        self["collapse_caption_idle_view"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        return self['collapse_caption_idle_view'].visible

    def caption_visibility(self):
        for _ in Retry(limit=30, interval=1):
            if self['captions_visible'].wait_for_existing(timeout=15, interval=0.2, raise_error=False):
                if self['captions_visible'].wait_for_visible(timeout=15, interval=1, raise_error=False):
                    return True
                else:
                    return False
            else:
                return False

    def see_translation_button_visibility(self):
        self["see_translation"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        if self["see_translation"].visible:
            return self["see_translation"].visible
        else:
            self["see_translation"].wait_for_visible(timeout=5, interval=1, raise_error=False)
            return False

    def click_on_captions(self):
        self["captions_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["captions_visible"].click()

    def click_see_original(self):
        if self["see_original"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["see_original"].click()

    def see_translation(self):
        self["see_translation"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["see_translation"].click()

    def translating_exist(self):
        if self["see_translation"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["see_translation"].click()
            return self["translating"].wait_for_existing(interval=0.1, raise_error=False)
        else:
            return False

    def caption_folded(self):
        if self["caption_root"].wait_for_existing(timeout=5, interval=1, raise_error=False):
            self["caption_root"].click()
            time.sleep(2)
            if not self["caption_layout"].existing:
                self["caption_root"].click()
                time.sleep(2)
                self["caption_layout"].click()
        return self["hide_caption_icon"].wait_for_visible(timeout=7, interval=1, raise_error=False)

    def pause_asr_caption(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        for _ in Retry(limit=30, interval=1):
            if self["captions_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["captions_visible"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["captions_visible"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    break
                self.fyp_video_toggle()

    def pause_asr_caption_caption_on(self):
        for _ in Retry(limit=30, interval=1):
            if self["captions_visible"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["captions_visible"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["captions_visible"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    break
                self.fyp_video_toggle()

    def pause_creator_caption(self):
        if self["hide_caption_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_caption_icon"].click()
        for _ in Retry(limit=30, interval=1):
            if self["creator_caption"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["creator_caption"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["creator_caption"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    break
                self.fyp_video_toggle()

    def pause_creator_caption_caption_on(self):
        for _ in Retry(limit=30, interval=1):
            if self["creator_caption"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self["creator_caption"].wait_for_ui_stable()
                self.fyp_video_toggle()
                if self["creator_caption"].wait_for_visible(timeout=0.5, interval=0.5, raise_error=False):
                    break
                self.fyp_video_toggle()

    def see_original(self):
        self["see_original"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["see_original"].click()

    def click_video_favorites_icon(self):
        if self["video_favorites_icon"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["video_favorites_icon"].click()

    def return_video_title(self):
        if self["title"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            return self["title"].text

    def check_nickname_v1(self, content):
        collection_nickname_v1 = self.return_video_title()
        if content == collection_nickname_v1:
            return True
        return False

    def check_nickname_v2(self, content):
        collection_nickname_v2 = self.return_video_title()
        if content == collection_nickname_v2:
            return False
        return True

    def caption_text(self):
        text = self["captions_visible"].text
        return text

    def description_text(self):
        desc_text = self["description_text"].text
        return desc_text

    def translation_button_text(self):
        button_text = self["see"]

    def check_description_text(self):
        self["description_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["description_text"].existing and self["description_text"].visible

    def description_text_view(self):
        return self["description_text_view"].text

    def click_middle_pot(self):
        return self["poi_select_more"].click(offset_y=self["poi_select_more"].rect.height * (-0.6))

    # 按控件位置截图
    def take_capture(self, area):
        time.sleep(2)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    # 寻找长锚点
    def find_poi_long(self):
        for _ in Retry(timeout=90):
            if self.poi_subtitle.wait_for_existing(timeout=1.5, raise_error=False):
                break
            self.swipe_up()

    # 寻找多锚点
    def find_poi_more(self):
        for _ in Retry(timeout=90):
            if self.poi_icon_more.wait_for_existing(timeout=1.5, raise_error=False):
                break
            self.swipe_up()

    def click_trending_bar(self):
        return self["trending_bar"].click()

    def click_more_entrance(self):
        self["share_entrance"].click()

    def check_share_menu_panel_caption_on(self):
        button = self.get_caption_share_action("Turn off captions")
        return button.existing and button.visible

    def check_can_turn_on_caption(self):
        button = self.get_caption_share_action("Turn on captions")
        return button.existing and button.visible

    def check_can_manage_caption(self):
        button = self.get_caption_share_action("Manage captions")
        return button.existing and button.visible

    def check_share_menu_panel_caption_enabled(self):
        button = self.get_caption_share_action("Turn off captions")
        button.click()
        return self["action_list_v2"].existing and self["action_list_v2"].visible

    def check_share_menu_panel_caption_on_disabled(self):
        button = self.get_caption_share_action("Turn on captions")
        button.click()
        return self["action_list_v2"].existing and self["action_list_v2"].visible

    def click_turn_off_caption(self):
        self.get_caption_share_action("Turn off captions").click()
        return

    def click_turn_on_caption(self):
        self.get_caption_share_action("Turn on captions").click()
        return

    def click_manage_caption(self):
        self.get_caption_share_action("Manage captions").click()
        time.sleep(3)
        return

    def click_qna_banner(self):
        if self["qa_banner"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["qa_banner"].click()

    def return_qna_desc(self):
        if self["qa_desc"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            print("rect of qa_desc", self["qa_desc"].rect)
            print("rect of device", self.rect)
            rect_target = self["qa_desc"].rect
            rect_device = self.rect
            x_target = rect_target.left + rect_target.width * 0.7
            y_target = rect_target.top + rect_target.height / 2
            # x_device = rect_device.left + rect_device.width / 2
            # y_device = rect_device.top + rect_device.height / 2
            # x = x_target - x_device
            # y = y_target - y_device
            logger.info("x_target:%s, y_target:%s" % (x_target, y_target))
            # self.click(x_target, y_target)
            return x_target, y_target

    def retuen_qna_text(self):
        return self["qa_desc"].text

    def change_privacy_settings(self):
        self["share_panel_action_bar"].swipe(x_direction=1, swipe_coefficient=8)
        self["privacy_settings_v2"].click()
        if self["video_status"].text != "Only me":
            self["video_status"].click()
            self["only_me"].click()
            # if self["make_private"].wait_for_existing(timeout=5, raise_error=False):
            #     self["confirm_change"].click()

    def click_detail_add_to_playlist(self):
        self["detail_add_to_playlist"].click()
        time.sleep(3)

    def check_private_permission(self):
        if self["feed_playlist_icon"].wait_for_existing(timeout=5, raise_error=False):
            return False
        else:
            return True

    def action_vertical_list(self):
        action_list = self["action_vertical_list"].items()
        for item in action_list:
            for children in item.children:
                if children.text == "Privacy settings":
                    return True

    def click_select_button(self):
        self["more_panel_button"].click()

    def return_profile_page(self):
        self["back_privacy_settings"].click()
        self["back_profile_page"].click()
        time.sleep(2)

    def element_compare_by_image(self, device, original_pic_name):
        import cv2
        path = device.screenshot()
        capture = cv2.imread(path)
        self["save_video"].ensure_visible()
        rect1 = self["save_video"].rect
        self["add_to_favorites"].ensure_visible()
        rect2 = self["add_to_favorites"].rect
        target = capture[(rect1.top + rect1.height):  rect2.top, :]
        basedir = os.path.abspath('.')
        pic_dir = os.path.join(basedir, device.serial)
        # os.mkdir(pic_dir)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        from shoots_cv.cv import CV
        cv_ = CV()
        # original_pic_dir = os.path.join(os.path.abspath(os.path.join(os.getcwd(), "../../../../..")), "resources")
        original_pic_dir = os.path.join(os.path.abspath(os.path.realpath(__file__) + os.path.sep + "../../../../.."),
                                        "resources")
        print("Path：", original_pic_dir)
        original_pic_name = os.path.join(original_pic_dir, original_pic_name + ".png")
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']

        print(compare_result)
        return compare_result > 0.8

    def click_back_btn(self):
        return self['back_btn'].click()

    def get_fav_count(self):
        print("Favorites count", self["favorite_count"].text)
        return self["favorite_count"].text

    def digg_count(self):
        print("点赞数", self["digg_count"].text)
        return self["digg_count"].text

    def back_btn_click(self):
        self['back_btn'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["back_btn"].click()

    def click_music_tag_VDP(self):
        if self["music_cover"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["music_cover"].click()
        elif self["origin_music_cover"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["origin_music_cover"].click()
        time.sleep(2)

    def click_comment_detail_panel(self):
        if self.iv_guide_test.wait_for_existing(raise_error=False, timeout=3):
            self.swipe(y_direction=1)
        self["comment_view_layout"].click()
        self["comment_list_panel"].wait_for_visible()

    def show_video_after_report(self):
        self["video_mask_view"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        while self["video_mask_view"].existing:
            self["show_video"].click()
            time.sleep(2)
            self.refresh()
        # return not self["video_mask_view"].existing

    def enter_profile(self):
        self["user_avatar"].click()
        time.sleep(2)

    def check_userhead(self):
        return self["user_avatar"].wait_for_existing(timeout=5, raise_error=False)

    def follow(self):
        self["follow"].click()
        time.sleep(2)

    def like(self):
        self["like"].click()
        time.sleep(2)

    def unlike(self):
        self["like"].click()
        time.sleep(2)

    def video_digg_count(self):
        print("点赞数", self["video_digg_count"].text)
        return self["video_digg_count"].text

    def comment_exist(self):
        return self["comment"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)

    def comments(self, index=0):
        """第几条评论
        """
        time.sleep(1)
        items = self["comment_list"].items()
        return items[index] if items else None

    def filter_video_comments(self):
        self["filter_video_comment_list"].refresh()
        return self["filter_video_comment_list"].items()

    def check_video_reply(self):
        return "Reply" in self["check_reply_text"].text

    def comment_click(self):
        for _ in Retry(limit=3, interval=1):
            self["comment"].click()
            if self["comment_edit"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                break
            elif self["hide_this_tool"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                self["hide_this_tool"].click()
        if self["comment_top_edit"].wait_for_visible(timeout=1, raise_error=False):
            self.app.get_device().press_back()

    def comment_back(self):
        for _ in Retry(limit=3, interval=1):
            if self["new_comment_top_edit"].wait_for_visible(timeout=2, raise_error=False):
                self.app.get_device().press_back()
            elif self["comment_reply_with_video"].wait_for_visible(timeout=2, raise_error=False):
                self.app.get_device().press_back()
            else:
                break

    def comment_input(self, content):
        self["comment_edit"].refresh()
        while not self["comment_edit"].click():
            self.device.back()
            self.swipe_up()
        time.sleep(1)
        self["comment_edit"].input(content)
        time.sleep(1)
        self["comment_publish"].click()
        time.sleep(1)
        return

    def delete_comment(self, comment):
        comment.long_click(duration=2)
        time.sleep(1)
        self["comment_delete"].click()

    def share(self):
        self["share_entrance"].refresh()
        self["share_entrance"].click()
        return self["share_panel_title"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)

    def click_iv_close(self):
        self["iv_close"].click()

    def share_to_friend(self):
        self["one_friend"].click()
        self["send"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)
        self["send"].click()
        self["send_confirm"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)
        self["send_confirm"].click()

    def save_video(self):
        self.find_downloadable_video()
        self.wait_for_download_finish()
        self["download_success"].wait_for_disappear(timeout=5, interval=0.5, raise_error=False)

    def save_share_cancel(self):
        self["save_share_cancel"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)
        self["save_share_cancel"].click()
        return self["save_share_cancel"].wait_for_disappear(timeout=3, interval=0.5, raise_error=False)

    def get_share_action(self, action_name):
        """返回一个指定action name的控件
         """
        self["action_list"].refresh()
        save_button = Control(root=self, path=UPath(text_ == "Save video"))
        if action_name == "Save video" and save_button.wait_for_visible(timeout=3, raise_error=False):
            return save_button
        for _ in Retry(limit=3, interval=1, raise_error=False):
            action_list = self["action_list"].items()
            # time.sleep(2)
            for action in action_list:
                # time.sleep(5)
                if action_name in action.action_name:
                    # time.sleep(2)
                    return action
            # time.sleep(2)
            self["share_panel"].swipe(x_direction=1, swipe_coefficient=5)
        return None

    def get_caption_share_action(self, action_name):
        for _ in Retry(limit=4, interval=1, raise_error=False):
            self["action_list_v2"].refresh()
            action_list = self["action_list_v2"].items()
            for action in action_list:
                for children in action.children:
                    if children.type == "com.bytedance.tux.input.TuxTextView":
                        if children.text == action_name:
                            return action
            self["action_list_v2"].swipe(x_direction=1)
        return None

    def find_downloadable_video(self):
        download_action = self.get_share_action("Save video")
        while download_action is None:
            self._device_driver.send_key(4)
            self.swipe(y_direction=1)
            time.sleep(1)
            self.share()
            download_action = self.get_share_action("Save video")
        return download_action.click()

    def wait_for_download_finish(self):
        self["download_progress_bar"].wait_for_visible()
        return self["download_success"].wait_for_visible(timeout=120, raise_error=False)

    def get_music_name_on_feed(self):
        return self["music_roll_name"].text

    def go_to_music_detail_page(self):
        self["music_cover"].click()

    def swipe_video(self):
        old_video = self.video_profile_name
        self.swipe(y_direction=1)
        self.refresh()
        self.show_video_after_report()
        new_video = self.video_profile_name
        return old_video != new_video

    def report(self):
        self.get_share_action("Report").click()
        time.sleep(2)

    def allow_duet_setting(self):
        self["allow_duet"].click()
        time.sleep(5)

    def allow_stitch_setting(self):
        self["allow_stitch"].click()
        time.sleep(5)

    def allow_comment_setting(self):
        self["allow_comment"].click()
        time.sleep(5)

    def private_video_setting_entry(self):
        time.sleep(5)
        self["video_setting"].click()
        time.sleep(5)

    def video_setting_everyone(self):
        self["video_everyone"].click()
        time.sleep(8)

    def video_setting_friends(self):
        self["video_friends"].click()
        time.sleep(8)

    def video_setting_only_me(self):
        self["video_only_me"].click()
        time.sleep(8)

    def privacy_confirm_click(self):
        self["privacy_confirm"].click()
        time.sleep(5)

    def privacy_close_click(self):
        self["privacy_close"].click()
        time.sleep(5)

    def privacy_status_friends(self):
        return self["privacy_status_friends"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)

    def comment_setting_click(self):
        self["comment_setting"].click()
        time.sleep(5)

    @property
    def video_profile_name(self):
        if not self["title"].wait_for_existing(timeout=5, raise_error=False):
            self.swipe(y_direction=1, swipe_coefficient=8)
        self["title"].refresh()
        time.sleep(1)
        return self["title"].text

    def judge_use_sound_visible(self):
        return self["use_sound"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def video_reply_to_comment(self, comment):
        comment.click()
        time.sleep(1)
        self["reply_with_video"].click()

    def click_analytics_tab(self):
        self["analytics_tab"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        self["analytics_tab"].click()

    def delete_video(self, back=True):
        self.share()
        delete_btn = self.get_share_action("Delete")
        delete_btn.click()
        time.sleep(1)
        self.confirm_pop_up("Delete")
        time.sleep(1)
        if back:
            self._device_driver.send_key(4)

    def swipe_to_delete(self):
        for _ in range(10):
            self["action_list_v2"].swipe(x_direction=1)
            if self["delete_btn"].existing and self["delete_btn"].visible:
                return True
        return False

    def wait_for_comment_panel_and_focus(self):
        if self["out_view"].existing:
            self["out_view"].click()
            time.sleep(1)
        return self["comment_edit"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)

    def confirm_pop_up(self, confirmation):
        if confirmation == "Delete":
            if self["confirm_delete"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                self["confirm_delete"].click()
            else:
                self["confirm_delete_processing"].click()
        else:
            self["confirm_cancel"].click()

    def is_anchor_feed_exists(self):
        return self["anchor_feed"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_anchor_feed(self, index=0):
        # If there are multiple anchors, then the function will click on the anchor link based on the index provided
        self.is_anchor_feed_exists()
        self["anchor_feed"].click()
        if self["anchor_feed_panel_list"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            anchor_link = \
                Control(root=self["anchor_feed_panel_list"], path=UPath(id_ == "common_anchor_icon", index=index))
            anchor_link.click()

    def find_prop_video(self):
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if self["anchor_feed"].existing and self["anchor_feed"].text != "CapCut · Editing made easy":
                break
            self.swipe_up()

    def click_video_prop(self):
        self["anchor_feed"].click()

    # def get_video_id(self):
    #     self["debug_icon"].click()
    #     time.sleep(2)
    #     self["copy_vid"].click()
    #     self.click_video_detail_page_search_box()
    #     time.sleep(2)
    #     middle_panel = SearchResultPanel(root=self.app)
    #     middle_panel["text_area"].long_click()
    #     pop_panel = OtherWindow(root=self.app)
    #     pop_panel["paste"].wait_for_visible()
    #     for _ in Retry(limit=3, interval=1):
    #         if pop_panel["paste"].existing:
    #             pop_panel["paste"].click()
    #         else:
    #             break
    #     return self["search_text_area"].text

    def swipe_up_comment(self, ratio=4):
        """上滑
        """
        rect = self["content_comment"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["content_comment"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)

    def swipe_up(self, ratio=4):
        """上滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["content"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)

    def paste_address(self):
        self["comment_edit"].wait_for_visible()
        self["comment_edit"].click()
        time.sleep(1)
        if self["comment_top_edit"].wait_for_visible(timeout=20, raise_error=False):
            self["comment_top_edit"].long_click(duration=2)
        elif self["comment_edit"].wait_for_visible(timeout=4, raise_error=False):
            self["comment_edit"].long_click(duration=2)
        pop_panel = OtherWindow(root=self.app)
        if pop_panel["paste"].wait_for_visible(timeout=10, raise_error=False):
            pop_panel["paste"].click()
        else:
            for i in range(30):
                self._device_driver.send_key(67)
            self._device_driver.send_key(279)

    def paste_address_v2(self):
        self["comment_edit"].wait_for_visible()
        self["comment_edit"].click()
        time.sleep(1)
        for i in range(30):
            self._device_driver.send_key(67)
        self._device_driver.send_key(279)

    def return_paste_address(self):
        content = ""
        if self["comment_top_edit"].wait_for_visible(timeout=20, raise_error=False):
            content = self["comment_top_edit"].text
        elif self["comment_edit"].wait_for_visible(timeout=4, raise_error=False):
            content = self["comment_edit"].text
        return content

    def click_poi_comment_title(self):
        if not self["poi_anchor_title_comment"].wait_for_existing(timeout=2, raise_error=False):
            self["poi_anchor_title_comment2"].click()
        else:
            self["poi_anchor_title_comment"].click()

    def click_close(self):
        if self["close_btn"].wait_for_existing(timeout=2, raise_error=False):
            self["close_btn"].click()

    def click_delete(self):
        self["delete_btn"].click()
        time.sleep(1)

    def swipe_left(self):
        self["content"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(1)

    def swipe_right(self):
        self["content"].swipe(x_direction=-1, swipe_coefficient=5)
        time.sleep(1)

    def click_debug_icon(self):
        self["feed_debug_icon"].click()
        time.sleep(2)

    def click_debug_btn(self):
        self["feed_debug_btn"].click()

    def click_add_to_feed(self):
        self["add_to_feed"].click()
        time.sleep(2)

    def swipe_previous_story(self, count):
        for i in range(count):
            self["content"].swipe(x_direction=-1, swipe_coefficient=5)

    def check_story_labels(self, count):
        for i in range(count):
            self["content"].swipe(x_direction=1, swipe_coefficient=5)
            if not self["story_label"].existing:
                return False
        return True

    def enter_comment_panel(self):
        self["comment_view_layout"].click()
        time.sleep(1)

    def click_video_back_btn(self):
        self["back_button"].click()
        time.sleep(1)

    def close_gift_pop_up(self):
        if self.iv_guide_test.wait_for_existing(raise_error=False, timeout=3):
            self.swipe(y_direction=1)
        if self["close_gift_pop_up"].wait_for_existing(raise_error=False, timeout=3):
            self["close_gift_pop_up"].click()
        time.sleep(2)

    def check_gift_icon(self):
        return self["iv_gift"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)

    def click_gift_icon(self):
        self["iv_gift"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)
        self["iv_gift"].click()

    def check_gift_panel(self):
        if self["gift_panel"].wait_for_visible(timeout=5, interval=0.5, raise_error=True):
            return self["gift_1"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        return False

    def click_gift_image(self):
        self["gift_1"].wait_for_visible(timeout=15, interval=0.5, raise_error=True)
        self["gift_1"].click()

    def check_gift_selected(self):
        return self["selected_gift_image"].wait_for_visible(timeout=15, interval=0.5, raise_error=False)

    def check_selected_panel_closed(self):
        return self["selected_gift_image"].wait_for_invisible(timeout=15, interval=0.5, raise_error=False)

    def click_close_selected_gift(self):
        self["close_gift_icon"].wait_for_visible(timeout=15, interval=0.5, raise_error=False)
        self["close_gift_icon"].click()

    def return_gift_coin_balance(self):
        self["gift_coin_balance"].wait_for_visible(timeout=15, interval=0.5, raise_error=True)
        return self["gift_coin_balance"].text

    def return_gift_send_button_text(self):
        self["gift_send_button"].wait_for_visible(timeout=15, interval=0.5, raise_error=True)
        return self["gift_send_button"].text

    def share_video_to_friend(self, friend):
        friend_list = Control(root=self, path=UPath(~id_ == "rv_share_panel_avatar|recycle_view"))
        find_friend = False
        for item in friend_list.items():
            for children in item.children:
                if "com.bytedance.tux.input.TuxTextView" in children.type:
                    if children.text in friend:
                        item.click()
                        find_friend = True
                        break
            if find_friend:
                break
        self["send"].wait_for_visible(timeout=3, interval=0.5, raise_error=False)
        self["send"].click()
        self["send_confirm"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def click_main_bottom_button_inbox_image_view(self):
        self['main_bottom_button_inbox_ImageView'].click()


    def click_inbox(self):
        self['Inbox'].click()

    def wait_for_main_bottom_button_inbox_image_view_existing(self):
        self['main_bottom_button_inbox_ImageView'].wait_for_existing()

    def wait_for_main_bottom_button_inbox_image_view_visible(self):
        self['main_bottom_button_inbox_ImageView'].wait_for_visible()

    def wait_for_inbox_text(self):
        self['notice_Inbox'].wait_for_text('^Inbox$')

    def inbox_shuzi_hongdian_existing(self):
        self['notice_inbox_hongdian'].wait_for_existing()

    def inbox_shuzi_hongdian_visible(self):
        self['notice_inbox_hongdian'].wait_for_visible()

    def inbox_shuzi_hongdian_text(self):
        self['notice_inbox_hongdian'].wait_for_text('2')

    def inbox_shuzi_weidu_hongdian_existing(self):
        self['notice_inbox_weidu_hongdian'].wait_for_existing()

    def inbox_shuzi_weidu_hongdian_visible(self):
        self['notice_inbox_weidu_hongdian'].wait_for_visible()

    def wait_for_inbox_recycler_view_awemenotice_tuxiconview_visible(self):
        self['inboxRecyclerView_2_awemenotice_tuxiconview'].wait_for_visible()

    def wait_for_activiti_existing(self):
        self['Activiti'].wait_for_existing()

    def wait_for_activiti_visible(self):
        self['Activiti'].wait_for_visible()


    def wait_for_inbox_recycler_view_iv_arrow_existing(self):
        self['inboxRecyclerView_2_iv_arrow'].wait_for_existing()

    def wait_for_inbox_recycler_view_iv_arrow_visible(self):
        self['inboxRecyclerView_2_iv_arrow'].wait_for_visible()

    def wait_for_inbox_recycler_view_awemenotice_tuxiconview_existing(self):
        self['inboxRecyclerView_2_awemenotice_tuxiconview'].wait_for_existing()

    def wait_for_new_foll_existing(self):
        self['New foll'].wait_for_existing()

    def wait_for_new_foll_visible(self):
        self['New foll'].wait_for_visible()

    def wait_for_new_foll_text(self):
        self['New foll'].wait_for_text('^New followers$')
    def wait_for_inbox_recycler_view_list_followers_content_existing(self):
        self['inboxRecyclerView_1_list_item_activity_content'].wait_for_existing()

    def wait_for_inbox_recycler_view_list_followers_content_visible(self):
        self['inboxRecyclerView_1_list_item_activity_content'].wait_for_visible()

    def wait_for_inbox_recycler_view_list_followers_content_text(self):
        self['inboxRecyclerView_1_list_item_activity_content'].wait_for_text('^\u200eHello byee started following you$')

    def wait_for_inbox_recycler_view_awemenotice_tuxtextview_existing(self):
        self['inboxRecyclerView_3_awemenotice_tuxtextview'].wait_for_existing()

    def wait_for_inbox_recycler_view_awemenotice_tuxtextview_visible(self):
        self['inboxRecyclerView_3_awemenotice_tuxtextview'].wait_for_visible()

    def wait_for_inbox_recycler_view_awemenotice_tuxtextview_text(self):
        self['inboxRecyclerView_3_awemenotice_tuxtextview'].wait_for_text('^System notifications$')
    def wait_for_inbox_recycler_view_list_System_content_existing(self):
        self['inboxRecyclerView_3_list_item_activity_content'].wait_for_existing()

    def wait_for_inbox_recycler_view_list_System_content_visible(self):
        self['inboxRecyclerView_3_list_item_activity_content'].wait_for_visible()

    def wait_for_inbox_recycler_view_list_System_content_text(self):
        self['inboxRecyclerView_3_list_item_activity_content'].wait_for_text('^\u200ePromote Assistant: Join the hottest trends and earn 20% off coupon!$')

    def wait_for_list_item_activity_time_existing(self):
        self['list_item_activity_time'].wait_for_existing()

    def wait_for_list_item_activity_time_visible(self):
        self['list_item_activity_time'].wait_for_visible()

    def wait_for_list_item_activity_time_text(self):
        self['list_item_activity_time'].wait_for_text('^3/26$')

    def wait_for_activiti_text(self):
        self['Activiti'].wait_for_text('^Activities$')
    def wait_for_inbox_recycler_view_list_activity_content_existing(self):
        self['inboxRecyclerView_2_list_item_activity_content'].wait_for_existing()

    def wait_for_inbox_recycler_view_list_activity_content_visible(self):
        self['inboxRecyclerView_2_list_item_activity_content'].wait_for_visible()

    def wait_for_inbox_recycler_view_list_activity_content_text(self):
        self['inboxRecyclerView_2_list_item_activity_content'].wait_for_text('^\u200eHello byee posted a Now.$')

    def click_inbox_recycler_view_awemenotice_tuxiconview(self):
        self['inboxRecyclerView_2_awemenotice_tuxiconview'].click()

    def click_inbox_recycler_view_root_layout(self):
        self['inboxRecyclerView_2_root_layout'].click()

    def wait_for_inbox_new_followers_existing(self):
        self['inboxRecyclerView_1_awemenotice_tuxiconview'].wait_for_existing()

    def wait_for_inbox_new_followers_visible(self):
        self['inboxRecyclerView_1_awemenotice_tuxiconview'].wait_for_visible()

    def wait_for_inbox_new_followers_iv_arrow_existing(self):
        self['inboxRecyclerView_1_iv_arrow'].wait_for_existing()

    def wait_for_inbox_new_followers_iv_arrow_visible(self):
        self['inboxRecyclerView_1_iv_arrow'].wait_for_visible()

    def click_inbox_new_followers_tuxiconview(self):
        self['inboxRecyclerView_1_awemenotice_tuxiconview'].click()

    def click_inbox_new_followers_root_layout(self):
        self['inboxRecyclerView_1_root_layout'].click()

    def wait_for_inbox_System_notifications_existing(self):
        self['inboxRecyclerView_3_awemenotice_tuxiconview'].wait_for_existing()

    def wait_for_inbox_System_notifications_visible(self):
        self['inboxRecyclerView_3_awemenotice_tuxiconview'].wait_for_visible()

    def click_inbox_System_notifications(self):
        self['inboxRecyclerView_3_awemenotice_tuxiconview'].click()

    def wait_for_title_existing(self):
        self['title'].wait_for_existing()

    def wait_for_title_visible(self):
        self['title'].wait_for_visible()

    def wait_for_title_text(self):
        self['title'].wait_for_text('^Inbox$')

    def wait_for_903_title_text(self):
        self['notice_title'].wait_for_text('^testw_903$')

    def wait_for_title_repost_text(self):
        self['notice_title'].wait_for_text('^donmeheychik$')
    def click_inbox_System_notifications_root_layout(self):
        self['inboxRecyclerView_3_root_layout'].click()

    @property
    def cv(self):
        if not hasattr(self, '_cv'):
            self._cv = CV(device=self.app.get_device())
        return self._cv

    def wait_for_inbox_recycler_view_awemenotice_tuxiconview_pic_match(self):
        self.cv.wait_for_pic_match(self['inboxRecyclerView_2_awemenotice_tuxiconview'], 'inbox_notice_icon/activity.jpg')
    def wait_for_inbox_new_followers_pic_match(self):
        self.cv.wait_for_pic_match(self['inboxRecyclerView_1_awemenotice_tuxiconview'], 'inbox_notice_icon/new_followers.jpg')
    def wait_for_inbox_System_notifications_pic_match(self):
        self.cv.wait_for_pic_match(self['inboxRecyclerView_3_awemenotice_tuxiconview'], 'inbox_notice_icon/system_notifications.jpg')

    def click_permission_title_tv(self):
        self['permission_title_tv'].click()

    def wait_for_qf_k_visible(self):
        self['QfK'].wait_for_visible()

    def click_permission_find_btn(self):
        self['permission_find_btn'].click()

    def click_if_permission_find_btn_exist(self):
        if self['permission_find_btn'].wait_for_visible(timeout=5, raise_error=False):
            self['permission_find_btn'].click()

    def click_if_ok_exist(self):
        if self['OK'].wait_for_visible(timeout=5, raise_error=False):
            self['OK'].click()

    def wait_for_9z_b_inbox_iv_cover_existing(self):
        self['9zB_0_inbox_iv_cover'].wait_for_existing()

    def wait_for_9z_b_inbox_iv_cover_visible(self):
        self['9zB_0_inbox_iv_cover'].wait_for_visible()

    def wait_for_symphony_existing(self):
        self['Symphony'].wait_for_existing()

    def wait_for_symphony_visible(self):
        self['Symphony'].wait_for_visible()

    def click_9z_b_inbox_iv_cover(self):
        self['9zB_0_inbox_iv_cover'].click()



    def wait_for_name_tv_spirti80_existing(self):
        self['name_tv_spirti80'].wait_for_existing()

    def wait_for_name_tv_spirti80_visible(self):
        self['name_tv_spirti80'].wait_for_visible()

    def wait_for_name_tv_spirti80_text(self):
        self['name_tv_spirti80'].wait_for_text('^spirti8045$')


    def click_name_tv_spirti80(self):
        self['name_tv_spirti80'].click()

    def wait_for_key_js_object_global_props_pic_match(self):
        self.cv.wait_for_pic_match(self['key_js_object_global_props'], 'inbox_notice_icon/LIVE.jpg')

    def wait_for_inbox_recycler_view_avatar_iv_existing(self):
        self['inboxRecyclerView_0_10_avatar_iv'].wait_for_existing()

    def wait_for_inbox_recycler_view_avatar_iv_visible(self):
        self['inboxRecyclerView_0_10_avatar_iv'].wait_for_visible()

    def click_inbox_recycler_view_avatar_iv(self):
        self['inboxRecyclerView_0_10_avatar_iv'].click()

    def click_activiti(self):
        self['Activiti'].click()

    def click_close_iv(self):
        self['close_iv'].click()

    def wait_for_tv_label_text(self):
        self['tagged_tv_label'].wait_for_text("^You're tagged$")

    def click_activity_back_btn(self):
        self['activity_back_btn'].click()

    def wait_for_expose_top_text_text(self):
        self['expose_top_text'].wait_for_text('^903 mentioned you$')
    def click_activity_iv_close_btn(self):
        self['activity_iv_close'].click()

    def click_comment_back_layout_back_btn(self):
        self['comment_back_layout_back_btn'].click()

    def wait_for_see_tran_text(self):
        self['See tran'].wait_for_text('^See translation$')


    def click_back_btn_tux_icon_view(self):
        self['back_btn_TuxIconView'].click()

    def click_back_btn_juhe_icon_view(self):
        self['back_btn_juhe'].click()

    def wait_for_Reply_with_video_desc_text(self):
        self['Reply_with_video_desc'].wait_for_text('^回复 @wtest_301 $')

    def wait_for_post_testw_text(self):
        self['post_testw_905'].wait_for_text('^testw_905$')
    def wait_for_Commented_Story_testw_text(self):
        self['Commented_Story_testw_903'].wait_for_text('^testw_903$')

    def wait_for_crying_that_you_are_suffering_text(self):
        self['Reply_Comment_text'].wait_for_text('^大哭说你难受难受$')

    def wait_for_content_have_to_say_computer_text(self):
        self['content_\u200e不得不说计算机'].wait_for_text('^\u200e不得不说计算机专业$')

    def wait_for_container_bg_constraint_layout_title_text(self):
        self['container_bg_ConstraintLayout_title'].wait_for_text('^testw_903$')

    def wait_for_recycler_view_title_text(self):
        self['recyclerView__1_title'].wait_for_text('^testw_903$')

    def wait_for_recycler_view_title_user_text(self):
        self['recyclerView__0_title'].wait_for_text('^user4876629305607$')

    def wait_for_tv_name_text(self):
        self['tv_name'].wait_for_text('^@testw_903$')

    def wait_for_testw_text(self):
        self['repost_comment'].wait_for_text('^testw_903$')

    def wait_for__full_of_spirit_text(self):
        self['\u200e。精神百倍束手'].wait_for_text('^\u200e。精神百倍束手束脚$')

    def click_new_foll(self):
        self['New foll'].click()

    def get_entrance_content(self):
        userlist = []
        for item in self["inbox_view"].items():
            userlist.append(item["entrance_content"].text[1:])
        return userlist

    def existing_entrance_shuzi_hongdian(self,index):
        self["inbox_view"].items()[index]["entrance_hongdian"].wait_for_existing()

    def visible_entrance_shuzi_hongdian(self,index):
        self["inbox_view"].items()[index]["entrance_hongdian"].wait_for_visible()

    def text_entrance_followers_shuzi_hongdian(self,index):
        print(self["inbox_view"].items()[index]["entrance_hongdian"])


    def existing_entrance_icon(self,index):
        self["inbox_view"].items()[index]["entrance_icon"].wait_for_existing()

    def visible_entrance_icon(self,index):
        self["inbox_view"].items()[index]["entrance_icon"].wait_for_visible()

    def existing_entrance_name(self,index):
        self["inbox_view"].items()[index]["entrance_name"].wait_for_existing()

    def visible_entrance_name(self,index):
        self["inbox_view"].items()[index]["entrance_name"].wait_for_visible()

    def text_entrance_followers_name(self,index):
        self["inbox_view"].items()[index]["entrance_name"].wait_for_text('^New followers$')

    def text_entrance_activity_name(self,index):
        self["inbox_view"].items()[index]["entrance_name"].wait_for_text('^Activities$')

    def text_entrance_System_name(self,index):
        self["inbox_view"].items()[index]["entrance_name"].wait_for_text('^System notifications$')

    def click_entrance_name(self,index):
        self["inbox_view"].items()[index]["entrance_name"].click()

    def existing_entrance_content(self,index):
        self["inbox_view"].items()[index]["entrance_content"].wait_for_existing()

    def visible_entrance_content(self,index):
        self["inbox_view"].items()[index]["entrance_content"].wait_for_visible()

    def click_entrance_content(self,index):
        self["inbox_view"].items()[index]["entrance_content"].click()

    def existing_entrance_btnicon(self,index):
        self["inbox_view"].items()[index]["entrance_btnicon"].wait_for_existing()

    def visible_entrance_btnicon(self,index):
        self["inbox_view"].items()[index]["entrance_btnicon"].wait_for_visible()

    def existing_entrance_time(self,index):
        self["inbox_view"].items()[index]["entrance_time"].wait_for_existing()

    def visible_entrance_time(self,index):
        self["inbox_view"].items()[index]["entrance_time"].wait_for_visible()

    def text_entrance_time(self,index):
        self["inbox_view"].items()[index]["entrance_time"].wait_for_text('^3/26$')

    def scroll_inbox_recycler_view(self, coefficient_x=0, coefficient_y=0):
        self['inboxRecyclerView'].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)
        time.sleep(1)

    def get_skylight_name(self):
        userlist = []
        for item in self["skylight_name_list"].items():
            userlist.append(item["skylight_name"].text)
        return userlist

    def existing_skylight_name(self,index):
        self["skylight_name_list"].items()[index]["skylight_name"].wait_for_existing()

    def visible_skylight_name(self,index):
        self["skylight_name_list"].items()[index]["skylight_name"].wait_for_visible()

    def click_skylight_name(self,index):
        self["skylight_name_list"].items()[index]["skylight_name"].click()

    def existing_story_tianchuang_name(self,index):
        self["story_tianchuang_name_list"].items()[index]["story_tianchuang_name"].wait_for_existing()

    def visible_story_tianchuang_name(self,index):
        self["story_tianchuang_name_list"].items()[index]["story_tianchuang_name"].wait_for_visible()

    def click_story_tianchuang_name(self,index):
        self["story_tianchuang_name_list"].items()[index]["story_tianchuang_name"].click()

    def get_skylight_icon(self):
        userlist = []
        for item in self["skylight_icon_list"].items():
            userlist.append(item["skylight_icon"].visible)
        return userlist

    def click_skylight_icon(self,index):
        self["skylight_icon_list"].items()[index]["skylight_icon"].click()

    def existing_skylight_icon(self,index):
        self["skylight_icon_list"].items()[index]["skylight_icon"].wait_for_existing()

    def visible_skylight_icon(self,index):
        self["skylight_icon_list"].items()[index]["skylight_icon"].wait_for_visible()

    def click_story_tianchuang_icon(self,index):
        self["story_tianchuang_icon_list"].items()[index]["story_tianchuang_icon"].click()

    def existing_story_tianchuang_icon(self,index):
        self["story_tianchuang_icon_list"].items()[index]["story_tianchuang_icon"].wait_for_existing()

    def visible_story_tianchuang_icon(self,index):
        self["story_tianchuang_icon_list"].items()[index]["story_tianchuang_icon"].wait_for_visible()

    def existing_story_tianchuang_ring(self,index):
        self["story_tianchuang_ring_list"].items()[index]["story_tianchuang_ring"].wait_for_existing()

    def visible_story_tianchuang_ring(self,index):
        self["story_tianchuang_ring_list"].items()[index]["story_tianchuang_ring"].wait_for_visible()

    def scroll_ne(self, coefficient_x=0, coefficient_y=0):
        self['9NE'].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)
        time.sleep(1)

    def check_rec_emoji_panel(self):
        return self["emoji_recommend_panel"].wait_for_existing(timeout=5, raise_error=False)

    def check_reply_input_container(self):
        return self["reply_input_container"].wait_for_existing(timeout=5, raise_error=False)

    def click_reply_msg_container(self):
        self["reply_input_container"].click()
        time.sleep(2)


class skylight_icon_Upath(Control):
    def get_locators(self):
        return {
            "skylight_icon": {"path": UPath(id_ == "avatar_iv")},
        }


class skylight_icon_Btn(Control):
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout", visible_ == True) / UPath(id_ == "avatar_iv", visible_ == True)
    elem_class = skylight_icon_Upath

class story_tianchuang_icon_Upath(Control):
    def get_locators(self):
        return {
            "story_tianchuang_icon": {"path": UPath(id_ == "avatar_iv")},
        }


class story_tianchuang_icon_Btn(Control):
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout", visible_ == True) / UPath(id_ == "avatar_iv", visible_ == True)
    elem_class = story_tianchuang_icon_Upath

class story_tianchuang_ring_Upath(Control):
    def get_locators(self):
        return {
            "story_tianchuang_ring": {"path": UPath(id_ == "story_ring")},
        }


class story_tianchuang_ring_Btn(Control):
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout", visible_ == True) / UPath(id_ == "story_ring", visible_ == True)
    elem_class = story_tianchuang_ring_Upath
class entrance_list_Upath(Control):
    def get_locators(self):
        return {
            "entrance_icon": {"path": UPath(id_ == "awemenotice_tuxiconview")},
            "entrance_name": {"path": UPath(id_ == "awemenotice_tuxtextview")},
            "entrance_content": {"path": UPath(id_ == "list_item_activity_content")},
            "entrance_btnicon": {"path": UPath(id_ == "iv_arrow")},
            "entrance_time": {"path": UPath(id_ == "list_item_activity_time")},
            "entrance_hongdian": {"path": UPath(id_ == "list_item_activity_red_point")},
        }


class entrance_list_Btn(Control):
    elem_path = UPath(id_ == "root_layout", visible_ == True)
    elem_class = entrance_list_Upath

class skylight_name_Upath(Control):
    def get_locators(self):
        return {
            "skylight_name": {"path": UPath(id_ == "name_tv")},
        }


class skylight_name_Btn(Control):
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout", visible_ == True) / UPath(id_ == "name_tv", visible_ == True)
    elem_class = skylight_name_Upath

class story_tianchuang_name_Upath(Control):
    def get_locators(self):
        return {
            "story_tianchuang_name": {"path": UPath(id_ == "name_tv")},
        }


class story_tianchuang_name_Btn(Control):
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout", visible_ == True) / UPath(id_ == "name_tv", visible_ == True)
    elem_class = story_tianchuang_name_Upath

class Stickerinfo(Control):
    def get_locators(self):
        return {
            "img_sticker_loading": {'type': Control, "path": UPath(id_ == "img_sticker_loading")},
            "sticker_logo": {'type': Control, 'path': UPath(id_ == "effect_resource_image", visible_ == True, index=0)},
            "shot_btn": {'type': Control, 'path': UPath(id_ == "use_effect", visible_ == True)}

        }

    def click_shot_btn(self):
        self["shoot_btn"].click()

    def click(self, offset_x=0, offset_y=0):
        super(Stickerinfo, self).click(offset_x, offset_y)
        time.sleep(1)
        self["img_sticker_loading"].wait_for_invisible(raise_error=False)


class StickerList(FeedsList):
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")
    elem_class = Stickerinfo


class Sounds_card(Control):

    def get_locators(self):
        return {
            "play_btn": {"path": UPath(id_ == 'iv_status')},
            "content": {"path": UPath(id_ == 'rl_right')},
            "collect_btn": {"path": UPath(id_ == 'iv_music_collect', visible_ == True)},
        }


class Sounds_card_List(Control):
    elem_class = Sounds_card
    elem_path = UPath(id_ == 'll_music_item', visible_ == True)


# 音乐创作页音乐卡片列表
class Create_music_video(Control):
    def get_locators(self):
        return {
            "add_btn": {"path": UPath(type_ == "com.lynx.tasm.ui.image.FrescoImageView", index=1)}
        }


class Create_music_video_list(Control):
    elem_class = Create_music_video
    elem_path = UPath(type_ == 'com.lynx.FakeViews.LynxView')


class Result(Control):

    def get_locators(self):
        return {
        }


class Result_List(Control):
    elem_class = Result
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")


class VideoRecordPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.shortvideo.ui.VideoRecordNewActivity.*'}
    schema_url = "snssdk1233://studio/create?type=use_sticker&enter_from=feedads&sticker_id=135971"

    def get_locators(self):
        return {
            "filters_icon": {'type': Control, "path": UPath(text_ == 'Filters')},
            "filter": {'type': Control, "path": UPath(id_ == 'filter_recyclerView') / 1},  # 滤镜
            "effects_icon": {'type': Control, "path": UPath(id_ == 'effect_container')},
            "record_background": {'type': Control, 'path': UPath(id_ == "video_record_new_activity_root", visible_ == True)},
            "video_time": {"type": Control, 'path': UPath(text_ == "15s")},
            "shot_search_prop": {'type': Control, 'path': UPath(id_ == "img_discover_effect_search_frame", visible_ == True)},
            "img_sticker_like": {'type': Control, "path": UPath(id_ == 'img_sticker_like')},
            "sticker_list": {"type": StickerList, "path": UPath(id_ == "search_result_recyclerview", visible_ == True)},  # 贴纸
            "record_btn": {"path": UPath(id_ == "rl_record_tool_bottom") / UPath(id_ == "record_container")},
            "record_btn": {"path": UPath(id_ == 'record_container', visible_ == True, index=0)},
            "video_templates": {"type": Control, "path": UPath(id_ == 'parent_layout')},
            "module_change": {"type": Control, "path": UPath(id_ == 'container', visible_ == True)},
            "btn_next": {"path": UPath(id_ == 'next_step')},
            "quit": {'type': Control, "path": UPath(id_ == 'button1', text_ == 'Quit')},
            "stories_tutorial": {"path": UPath(id_ == 'story_tutorial')},
            "close_tutorial_btn": {"path": UPath(id_ == 'story_tutorial_close_button')},
            "add_sound": {"path": UPath(id_ == "choose_music_docker_content_view", visible_ == True)},
            "add_sound_libra": {"path":UPath(id_ == "tool_box_container") / 3},
            "add_sound_tt4d_demo_marquee": {"path": UPath(~text_ == "^Made in TT4D Demo Web.*", visible_ == True)},
            "search_input": {"path": UPath(~id_ == 'rl_search|rl_search_container')},
            "search_input1": {"path": UPath(id_ == 'rl_search_container')},
            "search_btn": {"path": UPath(~id_ == 'tv_search_action', visible_ == True)},
            "sounds_title": {"path": UPath(text_ == "Sounds", index=1)},
            "sounds_card_list": {"path": UPath(type_ == "SparkView") / 0 / 0},
            "create_music_video_list": {"path": UPath(type_ == "SparkView") / 0 / 0 / 0 / 0 / 1},
            "create_music_video_play_btn": {"path": UPath(type_ == "SparkView") / 0 / 0 / 1 / 1 / 0 / 0 / 0 / 0 / 2},
            "create_music_video_play_btn_mock": {"path": UPath(type_ == "SparkView") / 0 / 0 / 2 / 0 / 0 / 0 / 0 / 0 / 2},
            "add_btn_1": {"path": UPath(type_ == "SparkView") / 0 / 0 / 0 / 0 / 1 / 1 / 0 / 0 / 0 / 0 / 4 / 1},
            "add_btn_2": {"path": UPath(type_ == "SparkView") / 0 / 0 / 3 / 0 / 0 / 2 / 2},
            "close_btn": {"path": UPath(id_ == "story_tutorial_close_button", visible_ == True)},
            "play_btn": {"path": UPath(id_ == 'power_list', visible_ == True) / 2 / UPath(id_ == 'iv_status')},
            "first_choose": {"path": UPath(id_ == 'power_list', visible_ == True) / 0},
            "stories_pop": {"path": UPath(id_ == "root", type_ == "ConstraintLayout")},
            "sounds_list": {"type": Result_List, "path": UPath(id_ == 'power_list', visible_ == True)},
            "tick_mark_next": {"path": UPath(id_ == "btn_next")},
            "create_story_button": {"path": UPath(id_ == 'create_story_button')},
            "video_btn": {"path": UPath(text_ == 'Video', visible_ == True)},
            "camera": {"path": UPath(~text_ == 'Camera|相机')},
            "close_effect": {"type": Control, "path": UPath(id_ == "close_container") / UPath(type_ == "AppCompatImageView")},
            "top_tab": {'type': Control, "path": UPath(id_ == 'tab_layout') / 0 / 0},
            "ok_btn": {"path": UPath(id_ == "ok_button")},
            "ok": {"path": UPath(text_ == "OK")},

            "bottom_tab": {"type": Control, "path": UPath(id_ == "bottom_tab_host", visible_ == True)},

            'LIVE': {'type': Control, 'path': UPath(text_ == 'LIVE', visible_ == True)},
            'GO_LIVE': {'type': Control, 'path': UPath(text_ == 'Go LIVE', visible_ == True)},

            # 订阅相关控件
            "subscription_icon": {'type': Control, "path": UPath(text_ == 'Subscription')},
            "edit_badges": {'type': Control, "path": UPath(text_ == 'Badges')},
            "edit_emotes": {'type': Control, "path": UPath(text_ == 'Emotes')},
            "sub_faq": {'type': Control, "path": UPath(id_ == 'subscription_faq_icon')},

            "story_btn": {"path": UPath(text_ == 'Story')},
            "story_video_tab": {'type': Control, 'path': UPath(text_ == 'Video', id_ == 'tab_item_text')},
            "story_photo_tab": {'type': Control, 'path': UPath(text_ == 'Photo', id_ == 'tab_item_text')}
        }

    def click_by_name(self, ctrl_name):
        if self[ctrl_name].wait_for_existing(timeout=10, raise_error=False):
            self[ctrl_name].click()
            time.sleep(3)

    def click_camera_tab(self):
        self["camera"].click()
        time.sleep(3)
        self["video_time"].click()

    def click_first_choose(self):
        return self["first_choose"].click()

    def get_normal_sounds_card_list(self):
        self["sounds_card_list"].refresh()
        return self["sounds_card_list"].items()

    def get_sounds_list(self):
        return self["sounds_list"].items()

    def get_sounds_cards_list(self):
        return self["sounds_card_list"].items()

    def click_camera_icon(self):
        if self["camera"].wait_for_existing(timeout=5, raise_error=False):
            self["camera"].click()

    # 点击sounds第一个搜索结果的播放按钮
    # def play_first_search_result(self):
    #     if len(self.get_sounds_cards_list()) > 0:
    #         return self.get_sounds_cards_list()[0].play_btn.click()

    # 点击sounds第一个搜索结果的收藏按钮
    def collect_first_search_result(self):
        # rect = self["sounds_title"].rect
        # x = rect.left + 20 + rect.width * 2
        # y = rect.top + 20 + rect.height * 7
        # print("点击普通音乐卡收藏按钮", x, y)
        # self.app.get_device().click(x=x, y=y)
        if len(self.get_sounds_cards_list()) > 0:
           self["sounds_card_list"].children[3].children[0].children[0].children[2].children[1].click()

    # 点击sounds第一个搜索结果的播放按钮
    def play_first_search_result(self):
        if len(self.get_sounds_cards_list()) > 0:
            return self.get_sounds_cards_list()[3].click()

    def get_create_music_video_card(self) -> Control:
        return self["create_music_video_list"]

    def get_create_music_video_list(self):
        return self["create_music_video_list"].items()
        # num = 0
        # if self["create_music_video_list"].wait_for_existing(timeout=5, raise_error=False):
        #     for i in self["create_music_video_list"].items():
        #         if i.elem_info["visible"]:
        #             num = num+1
        #     return num-1

    # 点击第一个创作音乐视频卡
    def click_create_music_video(self):
        if len(self.get_create_music_video_list()) > 0:
            return self.get_create_music_video_list()[1].click()
        # if self["create_music_video_play_btn"].wait_for_existing(timeout=5, raise_error=False):
        #     self["create_music_video_play_btn"].click()
        # else:
        #     self["create_music_video_play_btn_mock"].click()

    def click_search_btn(self):
        self["search_btn"].click()
        time.sleep(5)

    def click_add_btn(self):
        # rect = self["sounds_title"].rect
        # x = rect.left - 2 + rect.width * 2
        # y = rect.top + rect.height * 6
        # print("点击视频音乐卡添加按钮", x, y)
        # self.app.get_device().click(x=x, y=y)
        if self["add_btn_1"].wait_for_existing(timeout=5, raise_error=False):
        # if len(self.get_create_music_video_list()) > 0:
        #     self.get_create_music_video_list()[1].add_btn.click()
            self["add_btn_1"].click()

    # 点击创作页音乐卡开拍按钮
    def click_music_card_add_btn(self):
        self["add_btn_2"].click()

    def search_sounds(self, text):
        self['search_input'].click()
        self['search_input1'].input(text)
        time.sleep(2)

    def click_add_sound(self):
        # if self["stories_pop"].wait_for_visible(timeout=10, raise_error=False):
        #     self["close_btn"].click()
        if self["record_background"].wait_for_existing(timeout=5, raise_error=False):
              self["record_background"].click()
        elif self["video_time"].wait_for_existing(timeout=5, raise_error=False):
            self["video_time"].click()
        time.sleep(3)
        if self["add_sound_libra"].wait_for_visible(timeout=5, raise_error=False):
            self["add_sound_libra"].click()
        elif self["add_sound"].wait_for_existing(timeout=5, raise_error=False):
            self["add_sound"].click()

    def click_next(self):
        if self["ok_btn"].wait_for_visible(timeout=3, interval=1, raise_error=False):
            self["ok_btn"].click()
        if self["btn_next"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["btn_next"].click()
            if not self["btn_next"].wait_for_invisible(timeout=4, raise_error=False):
                self["btn_next"].click()
        elif self["tick_mark_next"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["tick_mark_next"].click()

    def just_record(self):
        self["record_btn"].click()
        time.sleep(15)

    def add_filter(self):
        self["filters_icon"].click()
        self["filter"].click()
        self._device_driver.send_key(4)

    def click_shot_effect_search_btn(self):
        self["effects_icon"].click()
        time.sleep(2)
        self["shot_search_prop"].click()

    def choose_a_sticker(self):
        sticker_items = self["sticker_list"].items()
        sticker_items[0].shot_btn.click()

    def add_sticker(self):
        self["effects_icon"].click()
        time.sleep(1)
        sticker_items = self["sticker_list"].children
        sticker_items[1].click()
        self._device_driver.send_key(4)

    def record(self, elapsed=6):
        if self["video_templates"].wait_for_existing(timeout=5, raise_error=False):
            time.sleep(3)
            self["module_change"].swipe(x_direction=-1, swipe_coefficient=3)
        self.quit_stories_panel()
        if self["record_background"].wait_for_existing(timeout=5, raise_error=False):
            self["record_background"].click()
        if self["video_time"].wait_for_existing(timeout=5, raise_error=False):
            self["video_time"].click()
        self["record_btn"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["record_btn"].click()
        time.sleep(elapsed)

    def add_to_favorite(self):
        self["effects_icon"].click()
        time.sleep(2)
        while not self["img_sticker_like"].wait_for_visible(timeout=3, raise_error=False):
            self["sticker_list"].items()[0].click()
        self["img_sticker_like"].click()

    def quit_editing_draft(self):
        self._device_driver.send_key(4)
        self["quit"].click()

    def quit_stories_panel(self):
        if self["stories_tutorial"].wait_for_visible(timeout=10, interval=1, raise_error=False):
            self["close_tutorial_btn"].click()

    def is_add_sound_tt4d_demo_marquee_exists(self):
        # Return true if the marquee text "Made in TT4D Demo Web ..." appears on the top of the video edit screen
        return self["add_sound_tt4d_demo_marquee"].wait_for_visible(timeout=20, interval=1, raise_error=False)

    def click_story(self):
        self["story_btn"].click()
        time.sleep(1)

    def record_story1(self, duration):
        self["record_btn"].click()
        time.sleep(duration)
        self["record_btn"].click()

    def record_story2(self, duration):
        self["record_btn"].long_click(duration=duration)

    def record_video(self, duration):
        self["record_btn"].click()
        time.sleep(duration)
        self["record_btn"].click()
        self["tick_mark_next"].click()
        time.sleep(3)


class VideoTrimmer(Window):
    """
        VideoTrimmer (Adjust clip) provides the capability for the user to trim the shared video
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.shortvideo.cut.VECutVideoActivity"}

    def get_locators(self):
        return {
            "next_button": {"type": Control, "path": UPath(id_ == "tvNext")}
        }

    def click_next(self):
        # Click on the next button in the Video Trimmer (Adjust Clip) screen
        self["next_button"].wait_for_visible(timeout=20, interval=1, raise_error=True)
        self["next_button"].click()


class VideoPublishPopup(Window):
    """视频发布界面
    """
    window_spec = {"activity": 'com.ss.android.ugc.aweme.shortvideo.ui.VideoPublishActivity#1'  # 所在的Activity,不能为空
                   }

    def get_locators(self):
        return {
            "not_now_btn": {"path": UPath(id_ == "btn_not_now")},
        }

    def click_not_now_btn(self):
        if self["not_now_btn"].wait_for_visible(timeout=5, raise_error=False):
            self["not_now_btn"].click()


class VideoEditPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity|com.ss.android.ugc.aweme.shortvideo.edit.VEVideoPublishEditActivity#1|com.ss.android.ugc.aweme.adaptation.saa.SAAActivity.*'}

    def get_locators(self):
        return {
            "text_icon": {"path": UPath(id_ == 'bottom_bar_container') / 2},  # 添加文字
            "text_input": {"type": Control, "path": UPath(id_ == 'et_input')},
            "done_btn": {"path": UPath(id_ == 'tv_sure')},
            "next_step_btn": {"path": UPath(id_ == 'next_step')},
            "discard": {"path": UPath(id_ == 'button1', text_ == 'Continue')},
            "record_btn": {"path": UPath(id_ == 'rl_record_tool_bottom') / UPath(id_ == 'record_container')},
            "btn_next": {"path": UPath(id_ == 'next_step')},
            "not_now_btn": {"path": UPath(id_ == "btn_not_now")},
            "add_sound": {"path": UPath(id_ == "tv_top_text")},
            "search_btn": {"path": UPath(~id_ == 'find_more', visible_ == True)},
            'content': {'type': Control, 'path': UPath(id_ == 'content', visible_ == True, index=0)},
            "ok_btn": {"path": UPath(id_ == "ok_button")},
            "add_sound_recommended_favorites": {"path": UPath(id_ == 'sheet_container')},
            "post_to_story_btn": {"path": UPath(~text_ == 'Post to Story|Your Story')},
            "post_now_btn": {"path": UPath(text_ == 'Post Now')},
            "sticker_container": {"path": UPath(id_ == 'info_viewpager')},
            "sticker_list": {"type": ScrollView, "path": UPath(id_ == 'sticker_list_container')},
            "stickers_btn": {"path": UPath(text_ == 'Stickers')},
            "mention_sticker": {"path": UPath(id_ == 'mention_input_layout')},
            "close_btn": {"path": UPath(id_ == 'close_button', visible_ == True)}
        }

    def click_next_button(self):
        self["next_step_btn"].click()

    def swipe_down(self, ratio=4):
        """下滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] + offset_y
        self["content"].drag(to_x - 15, to_y, 15, -offset_y)
        time.sleep(1)

    def click_search_btn(self):
        self["search_btn"].click()
        time.sleep(5)

    def click_next(self):
        if self["close_btn"].wait_for_visible(timeout=3, raise_error=False):
            self["close_btn"].click()
        self["btn_next"].wait_for_visible(timeout=10, interval=1, raise_error=True)
        self["btn_next"].wait_for_ui_stable()
        self["btn_next"].click()
        self["btn_next"].wait_for_invisible(timeout=5, interval=1, raise_error=False)

    def click_add_sound(self):
        # Click on the "add sound" that appears on the top of the video edit screen
        self["add_sound"].wait_for_visible(timeout=10, interval=1, raise_error=True)
        self["add_sound"].wait_for_ui_stable()
        self["add_sound"].click()

    def is_add_sound_exists(self):
        # Return true if the "add sound" button appears on the top of the video edit screen
        return self["add_sound"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def dismiss_add_sound_recommended_favorites(self):
        if self["add_sound_recommended_favorites"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self._device_driver.send_key(4)

    def is_add_sound_recommended_favorites_exists(self):
        return self["add_sound_recommended_favorites"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def add_text(self, content):
        self["text_icon"].click()
        self["text_input"].text = content
        time.sleep(1)
        self["done_btn"].click()
        time.sleep(1)
        self["next_step_btn"].click()

    def discard_edits(self):
        self._device_driver.send_key(4)
        self["discard"].click()

    def click_post_to_story(self):
        self["post_to_story_btn"].click()
        time.sleep(1)

    def click_post_now(self):
        if self["post_now_btn"].wait_for_existing(timeout=2, raise_error=False):
            self["post_now_btn"].click()
        time.sleep(1)

    def click_stickers(self):
        self["stickers_btn"].click()


class VideoPublishPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.shortvideo.ui.VideoPublishActivity'}

    def get_locators(self):
        return {
            "desc_input": {"path": UPath(id_ == "editor_text", visible_ == True)},
            "at_hashtag": {"path": UPath(id_ == "at_hashtag")},
            "hash_tag_list": {"type": FeedsList, "path": UPath(id_ == "hash_tag_list")},
            "post_btn": {"path": UPath(id_ == "publish_container")},
            "post_video": {'path': UPath(text_ == "Post video", visible_ == True)},
            "draft_btn": {"path": UPath(id_ == "draft_container")},
            "sounds_btn": {"path": UPath(id_ == "bottom_bar_container") / 0},
            "more": {"path": UPath(id_ == "item_view") / UPath(
                type_ == "com.ss.android.ugc.aweme.shortvideo.sticker.StickerImageView")},
            "follower": {"path": UPath(id_ == 'layout_permission_open')},
            "select_permission_post": {"path": UPath(id_ == 'select_permission_post')},
            "post_title": {"path": UPath(id_ == 'top_bar')/UPath(text_ == 'Post')},
            "ok_btn": {"path": UPath(id_ == 'btn_dismiss')},
            "iv_close": {"path": UPath(id_ == "iv_close")},
            "mention_btn": {"path": UPath(id_ == 'at_friends')},
        }

    def click_more(self):
        if self["more"].wait_for_existing(timeout=10, raise_error=False):
            return self["more"].click()

    def click_sounds_btn(self):
        return self["sounds_btn"].click()

    def click_hashtag_at(self, text):
        self["at_hashtag"].click()
        self["desc_input"].input(text)

    def add_video_desc(self, text):
        self["desc_input"].click()
        self["desc_input"].input(text)
        # self["at_hashtag"].click()
        time.sleep(2)
        # self["hash_tag_list"].items()[0].click()

    def get_desc_input(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['desc_input'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['desc_input'].text

    def click_post(self):
        self["post_btn"].click()

    def swipe_down(self):
        self.scroll(distance_y=-500)

    def post_video(self):
        self["post_btn"].click()
        if self["post_video"].wait_for_visible(timeout=3, raise_error=False):
            self["post_video"].click()
            # self["select_permission_post"].click()

    def draft_video(self):
        self["draft_btn"].click()

    def get_hashtag_text(self):
        # Return the hash tag contents as plain text without any white spaces at the leading or trailing end
        self["desc_input"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        hashtag_text = self["desc_input"].text
        return hashtag_text.strip()

    def click_mention_btn(self):
        self["mention_btn"].click()


class VideoPostPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.shortvideo.ui.VideoPublishActivity'}

    def get_locators(self):
        return {
            "desc_input": {"path": UPath(id_ == "editor_text")},
            "at_hashtag": {"path": UPath(id_ == "at_hashtag")},
            "first_choose": {"path": UPath(id_ == "hash_tag_list") / 0},
            "lebron_anchor_mark_view":
                {"path": UPath(id_ == "private_content") / UPath(text_ == "See LeBron James stats")},
            "mlbb_anchor_mark_view":
                {"path": UPath(id_ == "private_content") / UPath(text_ == "Mobile Legends: Bang Bang")},
            "drafts": {"path": UPath(id_ == "draft_container")},
            "not_now_btn": {"path": UPath(type_ == "com.bytedance.tux.input.TuxTextView",
                                          id_ == "btn_not_now", visible_ == True)},
            "close_btn": {"path": UPath(id_ == 'iv_close')},
            "at_friends": {"path": UPath(id_ == 'at_friends')},
            "ok_btn": {"path": UPath(text_ == 'OK')},
        }

    def at_friends(self):
        if self["at_friends"].wait_for_existing(timeout=5, raise_error=False):
            # self["close_btn"].click()
            self["at_friends"].click()

    def click_first_choose(self):
        return self["first_choose"].click()

    def click_hashtag_at(self, text):
        if self["at_hashtag"].wait_for_existing(timeout=5, raise_error=False):
            self["at_hashtag"].click()
            time.sleep(3)
            self["desc_input"].input(text)
            time.sleep(3)

    def is_anchor_added_to_video(self, anchor_locator_key):
        # Returns true iff the provided anchor is added to the video via AddLink
        return self[anchor_locator_key].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def click_drafts(self):
        self["drafts"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["drafts"].click()
        self["drafts"].wait_for_invisible(timeout=5, interval=1, raise_error=False)


class CreationAtFriendsPanel(Window):
    window_spec = {'activity': 'com.ss.android.ugc.aweme.mention.activity.VideoCaptionMentionActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity'}

    def get_locators(self):
        return {"text_area": {"path": UPath(~id_ == 'searchET|search_container|editor_text', visible_ == True)},
                "friends_list": {"path": UPath(~id_ == 'recyclerView|vc_mention_search_list', visible_ == True)},
                }

    def search_friends(self, text):
        self["text_area"].input(text)

    def get_friends_list(self):
        # self["friends_list"].refresh()
        return self["friends_list"].items()

    def click_first_friends(self):
        if len(self.get_friends_list()) > 0:
            return self.get_friends_list()[1].click()


class AnchorPanel(Window):
    window_spec = {
        "activity":
            "com.ss.android.ugc.aweme.shortvideo.ui.VideoPublishActivity"
    }

    def get_locators(self):
        return {
            "add_link_close_button": {"path": UPath(id_ == "iv_close_anchor", visible_ == True)},
            "add_link_title": {"path": UPath(id_ == "tv_panel_title", visible_ == True)},
            "add_click_area": {"path": UPath(id_ == "add_click_area", visible_ == True)},
            "anchor_recyclerview__index_0__depth_1": {
                "path":
                    UPath(id_ == "anchor_recyclerview") /
                    UPath(visible_ == True, depth=1, index=0)
            },
            "post_video": {"path": UPath(id_ == "publish_txt", visible_ == True)},
            "stat_muse": {"path": UPath(text_ == "StatMuse", visible_ == True)},
            "game_anchor": {"path": UPath(text_ == "Game", visible_ == True)},
            "table_of_anchors": {"path": UPath(id_ == "design_bottom_sheet") / UPath(id_ == "anchor_recyclerview")},
            "add_to_playlist": {"type": Control, "path": UPath(id_ == 'publish_add_to_mixlist')/UPath(type_ == 'com.bytedance.tux.input.TuxTextView', visible_ == True)},

            "statemuse": {
                "path": UPath(text_ == 'StatMuse', visible_ == True)
            },
            "add_location": {"path": UPath(~text_ == 'Tag location|Add location|Location', visible_ == True)},
            "add_link_item_button": {"path": UPath(id_ == 'anchor_recyclerview')/0},
            "add_location_icon": {"type": Control, "path": UPath(id_ == 'poi_item_root')},
            "suggest_address": {"path": UPath(id_ == "ll_container") / UPath(type_ == "TuxTextView", index=0)},
            "address_close": {"path": UPath(id_ == 'com.ss.android.ugc.trill.df_poi:id/d0')},
            "poi_title": {"path": UPath(id_ == 'tv_poi_title')},
            "turn_on_location": {"path": UPath(id_ == 'tv_add_location_hint')},
            "poi_close": {"path": UPath(id_ == 'iv_poi_close')},
            "open_settings": {"path": UPath(text_ == 'Open settings')},
        }

    def click_anchor(self, anchor_locator_key):
        # Clicks the provided anchor from the AddLink popup
        if self.is_anchor_visible(anchor_locator_key=anchor_locator_key):
            self[anchor_locator_key].click()
        else:
            raise Exception("Anchor is not available in the AddLink for the logged in user - " + anchor_locator_key)

    def is_anchor_visible(self, anchor_locator_key):
        # Returns true iff the provided anchor is available in the AddLink popup of the video post page
        self.scroll_to_anchor(anchor_locator_key=anchor_locator_key)
        return self[anchor_locator_key].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def scroll_to_anchor(self, anchor_locator_key):
        # Scroll down the AddLink popup until the provided anchor is visible in the view
        scroll_y_distance = 250
        table_of_anchors = self["table_of_anchors"]
        counter = 0
        threshold_limit = 20
        # Break the loop when the anchor gets visible or exceeds the threshold limit
        while not self[anchor_locator_key].wait_for_visible(timeout=5, interval=1, raise_error=False):
            table_of_anchors.scroll(distance_y=scroll_y_distance)
            table_of_anchors.wait_for_ui_stable()
            counter += 1
            if counter > threshold_limit:
                break

    def check_add_location_icon(self):
        return self["add_location_icon"].wait_for_visible(timeout=5, raise_error=False)

    def click_add_link(self):
        self["add_click_area"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        # The sleep was added to suppress the disappearing popup hints in the post page
        time.sleep(5)
        self["add_click_area"].click()

    def add_link_exists(self):
        return self["add_click_area"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def add_link_title_exists(self):
        return self["add_link_title"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def close_add_link_button_exists(self):
        return self["add_link_close_button"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def publish_video(self):
        self["post_video"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["post_video"].click()

    def check_add_to_playlist(self):
        return self["add_to_playlist"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)


class GameAnchorSelectGamePanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.anchor.AnchorBaseActivity"}

    def get_locators(self):
        return {
            "back_arrow_button": {"path": UPath(id_ == "img_back")},
            "mlbb_game_anchor": {"path": UPath(text_ == "Mobile Legends: Bang Bang")}
        }

    def click_back_button(self):
        # Click on the back button located on the top of the Select game screen
        self["back_arrow_button"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["back_arrow_button"].click()

    def select_game(self, anchor_locator_key):
        # Hit the select button for the provided game anchor
        self[anchor_locator_key].wait_for_visible(timeout=10, interval=1, raise_error=False)
        game_entry_element = self[anchor_locator_key].parent
        select_button = None
        # The element obtained from the anchor_locator_key is the text view. Using the text view element,
        # we then traverse the tree and obtain the corresponding Select button
        for child_element in game_entry_element.children:
            if search("Button", child_element.type):
                select_button = child_element
                break
        if select_button is None:
            raise Exception("No game anchor was found for the provided locator key - " + anchor_locator_key)
        else:
            select_button.click()


class StatMuseWebview(Webview):
    view_spec = {'title': '.*'}

    def get_locators(self):
        return {
            "LebronStats":
                {"path": UPath(href_ == '/tt/nba/ask/lebron-playoff-stats') / UPath(type_ == 'P', visible_ == True)},
            "烧肉": {"type": WebElement, "path": UPath(class_ == 'rsttop-genre-list__list') / 0},
            "body": {"type": WebElement, "path": UPath(class_ == 'breathwrk-breath-site', visible_ == True)},
            "calm": {"type": WebElement, "path": UPath(text_ == 'Calm')},
            "filmarks": {"type": WebElement, "path": UPath(class_ == 'l-main')},
            "first_movie": {"type": WebElement,
                            "path": UPath(class_ == 'p-content-carousel__inner', index=0) / 0 / UPath(
                                class_ == 'c-content__jacket') / UPath(type_ == 'IMG')}
        }

    def add_link(self):
        if self["body"].wait_for_existing(timeout=60, raise_error=False):
            self["body"].wait_for_visible(timeout=60)
            self["calm"].wait_for_visible(timeout=60)
            self["calm"].click()
        elif self["filmarks"].wait_for_existing(timeout=60, raise_error=False):
            self["filmarks"].wait_for_visible(timeout=60)
            self["first_movie"].wait_for_visible(timeout=60)
            self["first_movie"].click()


class StateMuseWindowPanel(Window):
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.wiki.AddWikiActivity"
    }

    def get_locators(self):
        return {
            "StatmuseWebviewStat": {
                "type": StatMuseWebview,
                "path": UPath(type_ == 'com.ss.android.ugc.aweme.crossplatform.platform.webview.SingleWebView',
                              visible_ == True)},
            "addtovideo": {"path": UPath(id_ == 'add_btn', visible_ == True)},
            "add_anyway": {"path": UPath(text_ == "Add to video")},
            "anchor_subtitle": {"path": UPath(id_ == "nav_sub_title", visible_ == True)},
        }

    def click_lebron_stat(self):
        for _ in Retry(timeout=10, interval=1, message="Lebron Stats Web View click retry", raise_error=False):
            if self["StatmuseWebviewStat"]["LebronStats"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
                self["StatmuseWebviewStat"]["LebronStats"].click()

    def lebron_stat_exists(self):
        return self["StatmuseWebviewStat"]["LebronStats"].wait_for_visible(timeout=20, interval=1, raise_error=False)

    def click_add_to_video(self):
        if self["add_anyway"].wait_for_visible(timeout=25, interval=1):
            for _ in Retry(timeout=120, interval=3):
                if self["add_anyway"].existing:
                    self["add_anyway"].click()
                else:
                    break

    def add_to_video_exists(self):
        return self["addtovideo"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def get_anchor_subtitle(self):
        self["anchor_subtitle"].wait_for_visible(timeout=10, interval=1, raise_error=False)
        return self['anchor_subtitle'].text


class UploadedWindowPanel(Window):
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity"
    }

    def get_locators(self):
        return {
            "uploaded_video_image": {"path": UPath(id_ == 'iv_img', visible_ == True)},
            "upload_percentage": {
                "path": UPath(id_ == 'long_press_layout', visible_ == True)
            },
            "anchor_tag_title": {
                "path": UPath(id_ == 'anchor_tag_title', visible_ == True)
            }
        }

    def wait_for_upload(self):
        time.sleep(25)

    def check_anchor_exists(self):
        return self["anchor_tag_title"].visible

    def get_anchor_title(self):
        self["anchor_tag_title"].wait_for_visible(raise_error=True)
        return self["anchor_tag_title"].text == "See LeBron James stats"


class TrendingVideoDetail(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.trending.ui.TrendingDetailActivity|com.ss.android.ugc.aweme.detail.ui.DetailActivity"}

    def get_locators(self):
        return {
            "back_btn": {"path": UPath(id_ == "back_btn")},
            'content': {'type': Control, 'path': UPath(id_ == 'content', visible_ == True, index=0)},
            "debug_icon": {"type": Control,
                           "path": UPath(id_ == 'group_176') / UPath(type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "add_to_feed": {"type": Control, "path": UPath(text_ == 'Add to feed')}
        }

    def click_video_detail_page_debug_btn(self):
        self["debug_icon"].click()
        time.sleep(2)
        self["add_to_feed"].click()

    def click_back_btn(self):
        self["back_btn"].click()

    def video_add_to_feed(self):
        if self["feed_debug_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_debug_icon"].click()
        time.sleep(2)
        self["add_to_feed"].click()

    def video_add_to_feed(self):
        if self["feed_debug_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_debug_icon"].click()
        time.sleep(2)
        self["add_to_feed"].click()

    def debug_feed_get_video_id(self):
        time.sleep(2)
        self["feed_debug_icon"].click()
        self["copy_video_id"].click()

    def swipe_up(self, ratio=4):
        """上滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["content"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)

    def check_trending_playlist(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["trending_playlist"].wait_for_existing(timeout=5, raise_error=False):
                self.swipe_up()
            else:
                break


class UploadStoryPopupWindow(Window):
    window_spec = {"activity": "PopupWindow:.*"}

    def get_locators(self):
        return {
            "story_uploaded": {"path": UPath(text_ == 'Your Story was uploaded!', visible_ == True)},
            "hide_caption_tooltip": {"path": UPath(type_ == "f")},
            "popup_msg": {"path": UPath(id_ == "message", visible_ == True)},
        }

    def click_tooltip(self):
        self["hide_caption_tooltip"].wait_for_ui_stable()
        if self["hide_caption_tooltip"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
            self["hide_caption_tooltip"].click()

    def creator_caption_tooltip_visible(self):
        return self["hide_caption_tooltip"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_popup(self):
        return self["popup_msg"].wait_for_existing(timeout=5, raise_error=False)

    def get_popup_message_text(self):
        return self["popup_msg"].text

    def check_popup_disappear(self):
        return self["popup_msg"].wait_for_invisible(timeout=5, raise_error=False)


class VideoMentionPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.mention.activity.VideoCaptionMentionActivity"}

    def get_locators(self):
        return {

        }

class CaptionEditPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.creatoredit.CreatorCaptionEditActivity"}

    def get_locators(self):
        return {
            "edit_caption_title": {"path": UPath(id_ == "editor_control_title_bar")},
            "cancel": {"path": UPath(id_ == "text_start", visible_ == True)},

        }

    def check_caption_container(self):
        return self["edit_caption_title"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def cancel_edit_caption(self):
        self["cancel"].click()
        time.sleep(3)


class QnaDetailPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.question.QuestionDetailActivity"}

    def get_locators(self):
        return {
            "container": {"path": UPath(id_ == "rl_question_container")},
            "back": {"path": UPath(id_ == "back_btn")},
            'back_btn': {'path': UPath(id_ == 'back_btn')},
            'title_text': {'path': UPath(id_ == 'title_text')},

        }

    def check_container(self):
        return self["container"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def back(self):
        self["back"].click()
        time.sleep(3)

    def click_back_btn(self):
        self['back_btn'].click()

    def wait_for_title_text_text(self):
        self['title_text'].wait_for_text('^testw_903$')


class SuggestedQnaPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "title": {"path": UPath(id_ == "qna_header_title")},
            "back": {"path": UPath(id_ == "qna_back_btn")},

        }

    def check_title(self):
        return self["title"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def back(self):
        self["back"].click()
        time.sleep(3)

