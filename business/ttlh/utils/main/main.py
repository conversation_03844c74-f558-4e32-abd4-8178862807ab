# -*- coding: utf8 -*-
import os
import time
from shoots_android.control import *
from shoots.context import current_testcase
from shoots_android.control import <PERSON><PERSON>, ScrollView, TextView, Retry
from uibase.controls import Window, Control
from uibase.upath import id_, class_, enabled_, text_, UPath, visible_, type_, desc_
from business.ttlh.utils.main.mine import MinePanel
from .base import BasePanel, FeedsList, Control
from shoots_cv.controls import CV<PERSON>indow
from shoots_cv.upath import tpl_, feature_, ocr_, match_
from utils.common.log_utils import logger
from shoots_cv.cv import CV

from business.ttlh.utils.tt_popup import CommonPopup,ClosePnsUniversalPopup


class Comment(Control):
    def get_locators(self):
        return {
            "content_msg": {"type": Control, "path": UPath(id_ == "content")},
            "digg_count": {"type": Control, "path": UPath(id_ == "tv_digg_count")},
            "iv_digg": {"type": Control, "path": UPath(id_ == "iv_digg")},
        }

    @property
    def content(self):
        return self["content_msg"].text

    @property
    def digg_count(self):
        return int(self["digg_count"].text)

    def digg(self):
        self["iv_digg"].click()
        time.sleep(1)


class CommentList(FeedsList):
    elem_path = UPath(id_ == "container_bg")
    elem_class = Comment


class NoticeCard(Control):
    """
    单条信息控件
    """

    def get_locators(self):
        return {
            "poi_entrance": {"path": UPath(id_ == 'notification_cover_right')},
            "time_head": {"path": UPath(id_ == 'tv_time_head')},
        }


class NoticeInboxList(Control):
    elem_path = UPath(id_ == "notification_root")
    elem_class = NoticeCard


class Feed_video(Control):
    def get_locators(self):
        return {}


class Feed_video_list(FeedsList):
    elem_path = UPath(id_ == "container", visible_ == True, depth=1)
    elem_class = Feed_video


class MTextEdit(TextView):
    def input(self, text):
        self.send_keys(text)


class SugWord(Control):
    def get_locators(self):
        return {
            'nickname': {"path": UPath(id_ == 'tv_nickname')},
            "icon": {"path": UPath(id_ == 'iv_avatar')}
        }


class SugWord_List(Control):
    elem_class = SugWord
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout", depth=1)


class Action(Control):
    def get_locators(self):
        return {
            'share_action_label': {"type": Control, 'path': UPath(id_ == 'share_action_label')},
        }

    @property
    def action_name(self):
        return self["share_action_label"].text


class ActionList(FeedsList):
    elem_class = Action


class Channel(Control):
    def get_locators(self):
        return {
            'share_channel_label': {"type": Control, 'path': UPath(id_ == 'share_channel_label')},
        }

    @property
    def channel_name(self):
        return self["share_channel_label"].text


class ChannelList(FeedsList):
    elem_class = Channel


class Search_account(Control):

    def get_locators(self):
        return {
            "title": {"path": UPath(id_ == "search_acc_sug_title")},
            "icon": {"path": UPath(id_ == "search_acc_sug_avatar")}
        }


class Search_account_list(Control):
    elem_class = Search_account
    elem_path = UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")


class Suggested_searches(Control):
    def get_locators(self):
        return {
            "Suggested_searches_word": {"path": UPath(id_ == "tv_word")}
        }

    @property
    def suggested_searches_word(self):
        return self["Suggested_searches_word"].text


class Suggested_searches_list(Control):
    elem_class = Suggested_searches
    elem_path = UPath(id_ == "root_layout")


class FeedDebugDetailPanel(BasePanel):
    """Feed Debug面板
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.detail.ui.DetailActivity"}

    def get_locators(self):
        return {
            "feed_debug_icon": {"type": Control, "path": UPath(id_ == 'feed_debug_icon', visible_ == True)},
            "add_to_feed": {"type": Control, "path": UPath(text_ == 'Add to feed')},
            "inner_nick": {
                "path": UPath(id_ == "root_layout", visible_ == True) / UPath(id_ == "title", visible_ == True)},
            "view_rootview": {"type": Control, "path": UPath(id_ == 'view_rootview', visible_ == True)},

        }

    def video_add_to_feed(self):
        if self["feed_debug_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_debug_icon"].click()
        time.sleep(2)
        # self.app.testcase.take_screen_shot(self.app, "debug插入视频")
        self["add_to_feed"].click()

    def videos_add_to_feed(self):
        for _ in Retry(limit=2, raise_error=False):
            if self["feed_debug_icon"].wait_for_existing(timeout=5, raise_error=False):
                self["feed_debug_icon"].click()
            time.sleep(2)
            self.app.testcase.take_screen_shot(self.app, "debug插入视频")
            if self["add_to_feed"].wait_for_existing(timeout=5, raise_error=False):
                self["add_to_feed"].click()
            time.sleep(2)
            self["view_rootview"].swipe(y_direction=1, swipe_coefficient=6)

    def get_video_detail_nick(self):
        if self["inner_nick"].wait_for_existing(timeout=5, raise_error=False):
            nick = self["inner_nick"].text
            return nick


class FeedDebugPanel(BasePanel):
    """Feed Debug面板
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.offline.feed.debug.ui.SearchResourceActivity"}

    def get_locators(self):
        return {
            'input_vid': {'type': TextView, 'path': UPath(id_ == 'input_id', visible_ == True)},

        }

    def search_video(self, content):
        self["input_vid"].text = content
        self["input_vid"].perform_editor_action(EnumEditorAction.SEARCH)
        if len(list(content.split(","))) > 1:
            FeedDebugDetailPanel(root=self.app).videos_add_to_feed()
        else:
            FeedDebugDetailPanel(root=self.app).video_add_to_feed()


class FeedDebugTool(Control):
    """
        Feed debug 工具
    """

    def set_feed(self, feed_id_list: str):
        return self.driver.call_object_method(self.id, '', 'storeAwemeIdForDebug', '', feed_id_list)
        # return self.driver.call_object_method(self.id, '', 'shouldDelayChildPressedState', '')
        # return self.app.get_driver()[""].call_static_method("com.ss.android.ugc.aweme.offline.feed.debug.ui.FeedDebugButtonView", "storeAwemeIdForDebug", None, "boolean",
        #                                          feed_id_list)
        # return self.call_method(method='storeAwemeIdForDebug', args=[feed_id_list])
        # return self.driver.call_method(object_id=self.id, method_name='storeAwemeIdForDebug', ret_type='boolean', args=[feed_id_list])
        # print(type(self.driver))
        # return self.driver.call_method('storeAwemeIdForDebug', 'boolean', [feed_id_list])


class ForYouPanel(BasePanel):
    """App启动后默认在该页面
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.splash.SplashActivity#1|com.ss.android.ugc.aweme.hybridkit.spark.TranslucentActivity|com.ss.android.ugc.aweme.pns.universalpopup.core.ui.UniversalPopupActivity"}
    tab_map = {"Home": 0,
               "Discover": 1,
               "Add": 2,
               "Inbox": -2,
               "Me": -1}

    def get_locators(self):
        return {
            "home": {'type': Control, 'path': UPath(id_ == 'main_bottom_button_home') / UPath(
                type_ == 'com.bytedance.tux.input.TuxTextView')},
            'content': {'type': Control, 'path': UPath(id_ == 'content', type_ == 'android.widget.FrameLayout')},
            # "contacts_sync": {"path": UPath(id_ == 'action_area', visible_==True)},
            # "not_allow": {"root": "contacts_sync", "path": UPath(text_ == "Don't allow")},
            'agree_btn': {'type': Control, 'path': UPath(id_ == 'btn_agree')},
            "agree_and_continue": {'type': Control, 'path': UPath(id_ == 'btn_agree')},
            'video_list_empty_hint': {'type': Control, 'path': UPath(id_ == 'tv_desc', text_ == 'Hot videos list is empty')},
            'loading_view': {'type': Control, 'path': UPath(id_ == 'double_loading_view', visible_ == True)},
            'top_tabs': {'type': Control, 'path': UPath(id_ == 'rl_tab_container', visible_ == True) / 0 / 0},
            'following': {'type': Control, "root": "top_tabs", 'path': UPath(index=0)},
            'for_you': {'type': Control, "root": "top_tabs", 'path': UPath(index=1)},
            'save_photo_btn': {'path': UPath(text_ == "Save photo")},
            'for_you1': {'type': Control, "root": "top_tabs", 'path': UPath(text_ == "For You", visible_ == True)},
            'for_you_feed': {'path': UPath(text_ == 'For You')},
            'video_feed': {"type": Control, "path": UPath(id_ == 'view_rootview', visible_ == True)},
            "like_tab": {"path": UPath(id_ == 'tab_container', visible_ == True) / 3},
            "collect_tab": {"path": UPath(id_ == 'tab_container', visible_ == True) / 2},
            "collect_tab_layout": {"path": UPath(id_ == 'tab_layout', visible_ == True)},
            "private_liked_videos": {"path": UPath(id_ == 'tab_container', visible_ == True) / 2},
            "privacy_tab": {"path": UPath(desc_ == "Private videos") / UPath(id_ == "icon", visible_ == True)},
            "tap_again": {"path": UPath(id_ == "scroll_view", visible_ == True) / UPath(id_ == "status_view") / 2},
            "person_video": {"path": UPath(desc_ == "Videos")},
            "user_name": {"path": UPath(id_ == 'layout_author_info', visible_ == True) / UPath(id_ == 'title')},
            "search_bar_word": {"path": UPath(id_ == "search_label")},
            "see_more_hashtags": {"path": UPath(id_ == "desc", visible_ == True)},
            "feed_hashtags": {"path": UPath(id_ == "desc", visible_ == True)},
            'play_icon': {'type': Control, 'path': UPath(id_ == 'play_iv', visible_ == True)},
            "retry_connect": {"path": UPath(text_ == "Retry")},

            'music_tag': {'type': Control, 'path': UPath(id_ == 'origin_music_cover', visible_ == True)},
            'music_desp': {'type': Control, 'path': UPath(id_ == 'music_tag_ll', visible_ == True) / UPath(
                id_ == 'music_title_static')},

            'feed_tag_layout': {'type': Control, 'path': UPath(~id_ == 'feed_tag_layout|anchor_tag_icon_more', visible_ == True)},
            'follower_show': {'type': Control, 'path': UPath(id_ == 'follower_layout')},
            'iv_head_3': {'type': Control, "root": "feed_tag_layout",
                          'path': UPath(id_ == 'iv_head_3', visible_ == True)},
            "first_video": {
                "path": UPath(id_ == "feed_list", visible_ == True) / UPath(id_ == "container", index=0) / UPath(
                    id_ == "cover")},
            "tap_to_try_again": {"path": UPath(text_ == "Couldn't load. Tap to try again.")},
            "retry": {"path": UPath(id_ == "button", text_ == "Retry")},
            "try_again_later": {"path": UPath(text_ == "Try again later")},
            "second_video": {"path": UPath(id_ == 'feed_list', visible_ == True) / 1},
            "play_doc": {"path": UPath(id_ == 'play_list_empty_doc', visible_ == True)},
            "tv_text": {"path": UPath(id_ == 'tv_text')},
            "read_more": {"path": UPath(text_ == "See more", visible_ == True)},
            "more": {"path": UPath(text_ == "more", visible_ == True)},
            'comment_view_layout': {'type': Control, 'path': UPath(id_ == 'comment_view_layout', visible_ == True)},
            'comment_close_view': {'type': Control, 'path': UPath(id_ == 'comment_close_view')},
            'comment_count': {'type': Control, 'path': UPath(id_ == 'comment_count', visible_ == True)},
            'comment_panel': {'path': UPath(id_ == "rlt_fragment_container") / UPath(id_ == "llt_comment_list_container")},
            "user_title": {"path": UPath(text_ == '@juicy.ggg')},
            "comment_top_words": {"path": UPath(id_ == 'keyword')},
            "highlighted_word": {"path": UPath(text_ == "‎Pense que era Robin Sparkles​ de how i met your mother jajajaja")},
            'comment_list': {'type': CommentList, 'root': 'comment_panel', 'path': UPath(id_ == 'recyclerView')},
            'comment_edit': {'path': UPath(id_ == 'comment_edit_new', visible_ == True, index=0)},
            'first_comment': {'root': 'comment_panel',
                              'path': UPath(id_ == 'recyclerView') / 0 / UPath(id_ == 'content')},
            'first_comment_user': {'root': 'comment_panel',
                                   'path': UPath(id_ == 'recyclerView') / 0 / UPath(id_ == 'title')},
            "comment_edit_input": {
                "path": UPath(id_ == 'comment_edit_layout_new_input') / UPath(id_ == 'comment_edit_new', visible_ == True)},
            "comment_send_btn": {"path": UPath(id_ == 'comment_send_new_area', visible_ == True)},
            "detail_comment_input": {"path": UPath(id_ == 'container', type_ == 'android.widget.LinearLayout') / UPath(
                id_ == 'comment_edit_new', depth=6)},
            'at_search_loading': {'type': Control,
                                  'path': UPath(id_ == 'cal_search_at') / UPath(id_ == 'loading_view')},
            'at_list': {'type': FeedsList, 'path': UPath(id_ == 'rv_search')},
            'at_btn': {'type': Control, 'path': UPath(id_ == 'at_iv', visible_ == True)},
            'comment_publish': {'type': Control, 'path': UPath(id_ == 'comment_publish', visible_ == True, index=-1)},
            'comment_send': {'type': Control, 'path': UPath(id_ == 'comment_send_new', visible_ == True)},
            'comment_select_dialog': {'type': Control, 'path': UPath(id_ == 'select_dialog_listview')},
            'delete': {'root': 'comment_select_dialog', 'path': UPath(text_ == 'Delete')},
            'video_menu': {'path': UPath(id_ == 'share_dialog_options_container')},
            'add_to_favorites': {'path': UPath(text_ == 'Add to Favorites')},
            'save_video': {'path': UPath(text_ == 'Save video')},
            'report': {'path': UPath(text_ == 'Report')},
            'not_interest': {'path': UPath(text_ == 'Not interested')},
            'send_to_friends': {'root': 'comment_select_dialog', 'path': UPath(text_ == 'Send to friends')},

            'share_action_layout': {"type": Control, 'path': UPath(
                ~id_ == 'share_panel_action_container|im_action_container|sheet_content_container')},
            'action_list': {"type": ActionList, "root": "share_action_layout",
                            'path': UPath(~id_ == 'action_list|action_vertical_list')},
            'share_panel': {"type": ScrollView, "root": "share_action_layout",
                            'path': UPath(id_ == 'share_panel_action_bar')},

            "long_press_panel": {"type": ScrollView, 'path': UPath(~id_ == 'share_long_press_dialog_container|ll_bottom_area_container')},
            "long_press_send_btn": {
                'path': UPath(~id_ == 'recycle_view|rv_share_panel_avatar') / 0 / UPath(id_ == 'btn_send', visible_ == True)},
            "long_press_send_btn2": {
                'path': UPath(id_ == 'recycle_view|rv_share_panel_avatar') / 1 / UPath(id_ == 'btn_send', visible_ == True)},

            "share_button_panel": {'path': UPath(~id_ == 'share_panel_root|ll_bottom_area_container')},
            "share_panel_first_friend": {'path': UPath(id_ == 'name_tv', text_ == 'video_msg_receiver')},
            "rv_share_panel_avatar": {'path': UPath(id_ == "rv_share_panel_avatar")},
            "share_list_title": {'path': UPath(id_ == "share_list_title")},
            "share_panel_send": {'path': UPath(~id_ == 'send_container|send_layout', visible_ == True)},
            "share_panel_top_panel": {"type": ScrollView, 'path': UPath(~id_ == 'recycle_view|rv_share_panel_avatar')},
            "share_panel_second_friend": {"type": Control, 'path': UPath(id_ == 'recycle_view') / 1},
            "share_panel_third_friend": {"type": Control, 'path': UPath(id_ == 'recycle_view') / 2},
            "share_repost": {"type": Control, "path": UPath(id_ == "aiv_share_channel_icon")},
            "create_group_chat_checkbox": {"type": Control, 'path': UPath(id_ == 'group_chat_hint')},
            "more_btn": {'path': UPath(id_ == 'civ')},
            "search_bar": {"type": MTextEdit, 'path': UPath(id_ == 'search_et')},
            "clear_btn": {"type": MTextEdit, 'path': UPath(id_ == "btn_clear")},
            "select_user": {"type": ScrollView, 'path': UPath(id_ == 'contact_list_recyclerview')},
            "select_first_user": {'path': UPath(id_ == 'contact_list_recyclerview')/0},
            "send_btn": {'path': UPath(~id_ == 'tv_send|btn_send|send')},
            "live_btn": {'path': UPath(id_ == 'rl_tab_container') / UPath(type_ == 'android.widget.ImageView')},

            'share_iv': {'path': UPath(id_ == 'share_iv', visible_ == True)},
            "delete_video_btn": {"path": UPath(id_ == 'more_action_label', text_ == 'Delete', visible_ == True)},
            "feed_video_list": {"type": Feed_video_list, "path": UPath(id_ == 'feed_list', visible_ == True)},
            "feed_video_list1": {"type": VideoList, "path": UPath(id_ == 'feed_list', visible_ == True)},

            # 'comment_view_layout': {"type": Control, 'path': UPath(id_ == 'comment_view_layout', visible_ == True)},
            'comment_list_panel': {'path': UPath(id_ == "rlt_fragment_container") / UPath(id_ == 'fl_comment_bg')},
            'content_comment': {
                'path': UPath(id_ == 'rlt_fragment_container') / UPath(id_ == 'llt_commment_list_container')},
            "no_comments_tab": {'type': Control, 'path': UPath(text_ == 'Comments 0', visible_ == True)},
            'delete_btn': {"type": Control, 'path': UPath(id_ == 'button1', text_ == "Delete")},

            'digg_count': {'type': Control, 'path': UPath(id_ == 'digg_count', visible_ == True)},

            'tabs': {'type': Control,
                     'path': UPath(~id_ == 'main_bottom_tab_view|main_activity_bottom_tab_view', visible_ == True)},
            "Home": {'type': Control, "root": 'tabs', "path": UPath(index=0)},

            'Discover': {'type': Control, "root": 'tabs', "path": UPath(index=1)},

            "Friends": {"type": Control, "path": UPath(~text_ == 'Friends|Now', visible_ == True)},
            "Friends_tab": {"type": Control, "path": UPath(text_ == 'Friends', visible_ == True)},
            'Add': {'type': Control, "root": 'tabs', "path": UPath(index=2)},

            'Inbox': {'type': Control, "root": 'tabs', "path": UPath(index=-2)},

            'Me': {'type': Control, "root": 'tabs', "path": UPath(index=-1)},

            "friends_tab": {'type': Control, "path": UPath(text_ == 'Friends', visible_ == True)},

            'video_list_container': {'type': FeedsList,
                                     'path': UPath(id_ == 'loadmore_layout') / UPath(id_ == 'viewpager')},
            'iv_guide_root': {'path': UPath(id_ == "swipe_up_strengthen_layout", visible_ == True)},
            'iv_guide_test': {'path': UPath(text_ == "Swipe up for more", visible_ == True)},
            'iv_guide': {'type': Control, 'path': UPath(~id_ == 'iv_guide_(animation|image)', visible_ == True)},
            'not_allow': {'type': Control, 'path': UPath(type_ == 'com.bytedance.tux.widget.b') / 1},
            # 'sync': {'type': Control, 'path': UPath(~text_ == 'Not now|Skip', visible_ == True)},
            'follow_btn': {'type': Control, 'path': UPath(id_ == 'follow', visible_ == True)},
            'follow_btn1': {'type': Control, 'path': UPath(id_ == "follow_icon", visible_ == True)},
            'title': {'type': Control,
                      'path': UPath(~id_ == 'layout_author_info|common_feed_interact_area_tag') / UPath(id_ == 'title',
                                                                                                        visible_ == True)},
            'desc': {'type': TextView, 'path': UPath(id_ == 'desc', visible_ == True)},
            'see_more': {'type': Control, 'path': UPath(id_ == 'tv_toggle', visible_ == True)},
            'user_avatar': {'path': UPath(id_ == 'user_avatar', visible_ == True)},
            'user_avatar1': {'path': UPath(id_ == "user_avatar_layout_indicator", visible_ == True)},
            'user_avatar_live': {'path': UPath(id_ == 'user_avatar_live', visible_ == True)},
            "feed_debug_btn": {"path": UPath(id_ == "group_176")},
            "hide_feed_debug": {"path": UPath(id_ == "hide_this_tool_button")},
            "hide_this_tool": {"type": Control, "path": UPath(id_ == 'hide_this_tool', visible_ == True)},
            'digg_container': {'type': Control, 'path': UPath(id_ == "digg_container", visible_ == True)},
            'digg_view': {'type': Control, 'path': UPath(id_ == 'digg', visible_ == True)},

            'download_progress_bar': {'type': Control, 'path': UPath(id_ == 'root_download_progress_bar')},
            'download_success': {'type': Control, 'root': 'download_progress_bar',
                                 'path': UPath(text_ == 'Video saved')},
            "share_page": {'type': Control, 'path': UPath(id_ == 'radius_layout', visible_ == True)},
            "privacy_settings_entry": {"path": UPath(text_ == "Privacy settings", id_ == 'action_list')
                                               / 2 / UPath(id_ == 'share_action_label')},
            'region_selector_entrance': {'type': Control, 'path': UPath(id_ == 'region_button')},
            'country_only_switch': {'type': Button, 'path': UPath(id_ == 'country_only_switch')},
            'region_selector_list': {'type': FeedsList, 'path': UPath(id_ == 'recyclerview')},
            'region_selector_target': {'type': Control, 'root': 'region_selector_list', 'path': UPath(index=1)},
            'area_selector': {'type': Button, 'path': UPath(text_ == '*NONE*')},
            'music_full_screen': {'type': Control, 'path': UPath(id_ == 'full_screen', visible_ == True)},
            # exploring
            "favorites_icon": {'type': Control, 'path': UPath(id_ == 'favorite_view_layout', visible_ == True)},
            "trending_bar": {'type': Control, 'path': UPath(id_ == 'bottom_new', visible_ == True)},
            "feed_like_icon": {"path": UPath(id_ == 'digg_container', visible_ == True)},
            "feed_comment_icon": {"path": UPath(id_ == 'comment_inner_container', visible_ == True)},
            "feed_favourite_icon": {"path": UPath(id_ == 'favorite_container', visible_ == True)},
            "feed_share_icon": {"path": UPath(id_ == 'share_container', visible_ == True)},
            "remove_from_favourites": {"path": UPath(id_ == 'action_list')/4/UPath(id_ == 'share_action_label')},
            "input_collection_name": {"path": UPath(id_ == 'et_input', visible_ == True)},
            "save_collection_name": {"path": UPath(id_ == 'sumbit', visible_ == True)},
            "create_new_collection": {"path": UPath(id_ == 'collect_list', visible_ == True)/0},
            "collection_1": {"path": UPath(id_ == 'collect_list')/1},
            "public_title": {"path": UPath(id_ == 'tv_make_public', visible_ == True)},
            "public_settings": {"path": UPath(id_ == 'privacy_switch', visible_ == True)},
            "public_settings_text": {"path": UPath(id_ == 'tv_change_privacy_hint', visible_ == True)},
            "new_collection_back": {"path": UPath(id_ == 'back', visible_ == True)},
            "close_collection_panel": {"path": UPath(id_ == 'cancel', visible_ == True)},
            "collection_playlist_icon": {"path": UPath(id_ == 'tv_risk_warning_hint', visible_ == True)},
            # 推人卡片
            'suggest_friends': {'type': Control, 'path': UPath(id_ == 'layout_content', visible_ == True)},
            'close_suggest_friends': {'type': Control, 'path': UPath(id_ == 'close', visible_ == True)},
            # duet
            "process_loading": {
                "type": Control,
                "path": UPath(id_ == 'customProgressView') / UPath(id_ == 'iv_loading', visible_ == True)
            },
            # "dont_allow": {"path": UPath(type_ == 'com.bytedance.tux.widget.b')/1},
            "dont_allow": {"path": UPath(text_ == "Don't allow", visible_ == True)},
            "privacy_policy": {"path": UPath(text_ == "Privacy Policy", visible_ == True)},
            "ok_btn": {"path": UPath(text_ == "Ok", visible_ == True)},
            # Feed_Search
            "for_you_search": {"type": Control, "path": UPath(id_ == 'rl_tab_container') / UPath(
                type_ == 'com.bytedance.tux.icon.TuxIconView')},
            # Feed_report
            "feed_report": {"type": Control, "path": UPath(id_ == 'desc', text_ == 'Report')},
            "report_btn": {"type": Control, "path": UPath(text_ == 'Report', visible_ == True)},
            "comment_icon": {'type': Control, "path": UPath(id_ == "comment_view_layout", visible_ == True)},
            "follow_icon": {'type': Control, "path": UPath(id_ == 'follow_container', visible_ == True)},
            "back_button": {'type': Control, 'path': UPath(id_ == 'back_btn')},
            "person_page_back_button": {'type': Control, 'path': UPath(id_ == 'nav_start') / UPath(
                type_ == 'com.bytedance.tux.icon.TuxIconView', visible_ == True)},
            "like_icon": {'type': Control, 'path': UPath(id_ == 'digg', visible_ == True)},
            "no_comments": {'type': Control, 'path': UPath(id_ == 'rlt_fragment_container') / UPath(id_ == 'title')},
            "learn_more": {"path": UPath(text_ == 'Learn more', type_ == 'com.bytedance.tux.dialog.internal.l')},
            # popup
            "ad_guide": {'type': Control, 'path': UPath(id_ == "ad_guide_root", visible_ == True)},
            "close": {"type": Control, "path": UPath(id_ == 'close')},
            # "action_area": {'type': Control, "path": UPath(id_ == 'action_area', visible_==True)},
            # "pass": {'type': Control, 'path': UPath(id_ == 'action_area', visible_==True)/0/1/1},
            # Feed_Share
            "more_iv": {'type': Control, 'path': UPath(id_ == "more_iv", visible_ == True)},
            "close_share_iv": {'type': Control, 'path': UPath(id_ == 'iv_close', visible_ == True)},
            "cancel_share_iv": {'type': Control, 'path': UPath(id_ == "share_panel_cancel", visible_ == True)},
            'add_aweme_id': {'type': Control, 'path': UPath(id_ == 'button_plus')},
            'input_aweme_id': {'type': Control, 'path': UPath(id_ == 'input_id')},
            'import_videos': {'type': Control, 'path': UPath(id_ == 'button_search')},
            'manage_feed_done': {'type': Control, 'path': UPath(id_ == 'done')},
            'video_list': {'type': Control, 'path': UPath(id_ == 'manage_feed_recyclerView') / 0},
            'delete_video_list': {'type': Control, 'path': UPath(text_ == 'Delete', visible_ == True)},
            'remove_video': {'type': Control, 'path': UPath(id_ == 'button_delete')},
            'remove_video_done': {'type': Control, 'path': UPath(id_ == 'button_yes')},
            "share_to_1st_friend": {"type": Control, "path": UPath(id_ == 'avatar_iv', index=0)},
            "share_send_button": {"type": Control, "path": UPath(id_ == 'send')},
            "add_to_playlist_button": {"type": Control, "path": UPath(id_ == 'action_vertical_list') / 1 / UPath(
                id_ == 'more_action_label')},
            "video_add_to_playlist_button": {"type": Control,
                                             "path": UPath(id_ == 'share_dialog_options_container') / 1 / UPath(
                                                 id_ == 'desc')},
            "confirm_remove": {"type": Control, "path": UPath(id_ == 'positive_button')},
            "confirm_playlist_remove": {"type": Control, "path": UPath(text_ == "Remove", visible_ == True)},
            "feed_playlist_name": {"type": Control,
                                   "path": UPath(id_ == 'pw_list') / 1 / UPath(id_ == 'item_mix_name')},
            "feed_playlist_add_button": {"type": Control, "path": UPath(text_ == "Add", visible_ == True)},
            "create_btn": {"type": Control, "path": UPath(id_ == 'pw_list') / 0 / UPath(id_ == 'item_mix_name')},
            "video_add_to_playlist": {"type": Control,
                                      "path": UPath(id_ == 'share_dialog_options_container') / 1 / UPath(
                                          id_ == 'desc')},
            "select_to_add_playlist": {"type": Control,
                                       "path": UPath(id_ == 'pw_list') / 1 / UPath(id_ == 'item_mix_name')},
            "remove_from_playlist": {"type": Control,
                                     "path": UPath(id_ == 'share_dialog_options_container') / 1 / UPath(
                                         id_ == 'desc_layout')},
            "confirm_add": {"type": Control, "path": UPath(id_ == 'btn_add')},
            "login_or_signup_panel": {"type": Control, "path": UPath(id_ == 'visual_area') / 0},
            "close_login_popup": {"type": Control, "path": UPath(id_ == 'top_image_area') / UPath(
                type_ == 'androidx.constraintlayout.widget.ConstraintLayout') / 1},
            "add_to_playlist_icon": {"type": Control, "path": UPath(text_ == 'Add to playlist', visible_ == True)},
            "video_playlist_permission": {"type": Control,
                                          "path": UPath(id_ == 'share_dialog_options_container') / 1 / UPath(
                                              id_ == 'desc')},

            # Feed_debug
            'feed_debug_icon': {'type': Control,
                                'path': UPath(id_ == 'group_176') / UPath(type_ == 'android.view.View')},
            "debug_icon": {'type': Control,
                           "path": UPath(id_ == "feed_debug_icon")},
            'manage_feed_debug': {'type': Control, 'path': UPath(id_ == 'manage_feed_button')},

            'feed_nickname': {'type': Control, 'path': UPath(id_ == 'title', visible_ == True)},
            'comment_back_button': {'type': Control, 'path': UPath(id_ == 'back_btn',
                                                                   type_ == 'androidx.appcompat.widget.AppCompatlmageView')},
            'feed_back_button': {'type': Control, 'path': UPath(id_ == 'back_btn', visible_ == True)},
            'feed_options': {'type': Control, 'path': UPath(id_ == 'manage_feed_options')},
            'import_video': {'type': Control, 'path': UPath(id_ == 'actionsheet_actiongroups_container') / 0 / UPath(
                id_ == 'action_sheet_action_content')},

            # Event
            "discover": {"path": UPath(id_ == 'main_bottom_button_discover')},
            "search_textarea": {"path": UPath(id_ == 'tv_static_text', visible_ == True)},
            "search_textarea1": {"path": UPath(id_ == 'll_marquee_layout', visible_ == True)},
            "search_textarea2": {"path": UPath(id_ == 'll_search_bar', visible_ == True)},
            "search_btn": {"path": UPath(id_ == 'tv_search_textview', visible_ == True)},
            "search_text": {"path": UPath(id_ == 'et_search_kw', visible_ == True)},
            "search_text1": {"path": UPath(id_ == 'rl_content_container')},
            "suggested_searches": {"path": UPath(id_ == 'tv_suggest_words')},
            "suggested_options": {"path": UPath(id_ == 'bullet_container') / 0 / 0 / 1 / 0 / 1 / 0 / 0 / 0},
            "sugword_List": {"type": SugWord_List, "path": UPath(id_ == 'recycler_view')},
            "suggested_options_lynx": {
                "path": UPath(id_ == "bullet_container", visible_ == True) / 0 / 0 / 1 / 4 / UPath(
                    type_ == "com.lynx.FakeViews.FlattenView")},
            "bottom_add": {"path": UPath(id_ == "main_bottom_button_add")},
            "bottom_friends": {"path": UPath(id_ == "main_bottom_button_friend")},
            "first_history_word": {"path": UPath(id_ == 'listview') / 0},
            "bottom_inbox": {"path": UPath(id_ == 'main_bottom_button_inbox')},
            "massage_entrance_box": {"path": UPath(id_ == 'entrance_box')},
            "create_chat": {"path": UPath(id_ == "createChatView")},
            "session_massage": {"path": UPath(id_ == 'session_list_root_layout')},

            "suggested_searches_list": {"type": Suggested_searches_list,
                                        "path": UPath(id_ == 'bullet_container') / 0 / 0},
            "suggested_options_list": {"type": Suggested_searches_list, "path": UPath(id_ == 'bullet_container') / 0 / 0},
            "search_account_list": {"type": Search_account_list,
                                    "path": UPath(
                                        id_ == 'recycler_view', type_ == 'androidx.recyclerview.widget.RecyclerView')},
            "long_click_profile": {"type": Control, "path": UPath(id_ == "main_bottom_button_me")},
            "first_account": {"path": UPath(id_ == 'recycler_view') / 6},
            "skip_btn": {"path": UPath(text_ == "Skip", visible_ == True)},
            "homepage_search_btn": {
                "path": UPath(~id_ == 'rl_tab_container|iv_right_first') / UPath(type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "find_friends": {"path": UPath(id_ == "iv_left_first") / UPath(id_ == "icon_custom")},
            "friends_page_search_btn": {"path": UPath(id_ == 'iv_right_first', visible_ == True)},
            "first_history_word": {"type": Control, "path": UPath(id_ == 'bullet_container') / 0 / 0 / 0},
            "first_notice": {"path": UPath(id_ == 'rv_list') / 0},
            "text_area": {"path": UPath(id_ == "fl_intput_hint_container", visible_ == True)},
            "personal_profile": {"path": UPath(id_ == 'main_bottom_button_me')},
            # POI
            "poi_tag_root": {"path": UPath(id_ == 'anchor_tag_root', visible_ == True)},
            "poi_icon": {"path": UPath(id_ == 'anchor_tag_icon', visible_ == True)},
            "poi_title": {"path": UPath(id_ == 'anchor_tag_title', visible_ == True)},
            "poi_subtitle": {"path": UPath(~id_ == 'anchor_tag_subtitle|anchor_tag_root', visible_ == True)},
            "poi_area": {"path": UPath(id_ == "aweme_intro_ll", visible_ == True)},
            "poi_area_comment": {"path": UPath(id_ == 'header_anchor', visible_ == True)},
            "poi_bottom_icon": {"path": UPath(id_ == 'anchor_container') / 0 / UPath(id_ == 'common_anchor_icon')},
            "poi_name": {"path": UPath(id_ == 'poi_name')},
            "friends_post": {"path": UPath(id_ == 'friends_tab_intro_title', visible_ == True)},

            # 多锚点展开页
            "poi_icon_more": {"path": UPath(id_ == 'anchor_tag_icon_more', visible_ == True)},  # 多锚点下拉图标
            "poi_select_more": {"path": UPath(id_ == 'anchor_select_root', visible_ == True)},  # 多锚点展开区域
            "poi_anchor_icon": {
                "path": UPath(id_ == 'anchor_container') / 0 / UPath(~id_ == 'common_anchor_icon|poi_anchor_icon', visible_ == True)},
            "poi_anchor_title": {
                "path": UPath(id_ == 'anchor_container') / 0 / UPath(id_ == 'common_anchor_title', visible_ == True)},
            "poi_anchor_subtitle": {
                "path": UPath(id_ == 'anchor_container') / 0 / UPath(id_ == 'common_anchor_subtitle',
                                                                     visible_ == True)},
            "poi_anchor_cancel": {"path": UPath(id_ == 'anchor_select_cancel', visible_ == True)},
            "poi_anchor_icon_1": {
                "path": UPath(id_ == 'anchor_container') / 1 / UPath(id_ == 'common_anchor_icon', visible_ == True)},

            # 评论区锚点
            "poi_anchor_comment": {"path": UPath(id_ == 'header_anchor', visible_ == True)},
            "poi_anchor_icon_comment": {"path": UPath(id_ == 'poi_map_pin', visible_ == True)},
            "poi_anchor_title_comment": {"path": UPath(id_ == 'poi_name', visible_ == True)},
            "poi_anchor_subtitle_comment": {"path": UPath(id_ == 'comment_tag1_tv', visible_ == True)},
            "comment_top_edit": {"path": UPath(id_ == 'container', type_ == 'android.widget.LinearLayout') / UPath(
                id_ == 'comment_edit_layout_new_input')},
            "new_comments_tap_edit": {
                "path": UPath(id_ == 'container', type_ == 'android.widget.LinearLayout') / UPath(
                    id_ == 'comment_edit_layout_new_root')},

            # inbox锚点
            "all_activity": {"path": UPath(id_ == 'tv_group_title')},
            "following_btn": {"path": UPath(text_ == 'Following', visible_ == True)},
            "at_iv": {"type": Control, "path": UPath(id_ == 'iv_at', visible_ == True)},
            "at_icon": {"type": Control, "path": UPath(id_ == 'container', visible_ == True) / UPath(id_ == 'iv_at')},
            "cal_search_at": {"type": Control, "path": UPath(~id_ == 'rv_search|rv_mention_list', visible_ == True)},
            #   not interested
            "not_interested": {"type": Control,
                               "path": UPath(type_ == 'com.ss.android.ugc.aweme.feed.ui.masklayer2.layout.a') / UPath(
                                   id_ == 'desc')},
            "me_tab": {"path": UPath(id_ == "main_bottom_button_me")},
            "compare_test": {"type": Control, "path": UPath(id_ == 'rl_tab_container')},
            "feed_playlist_icon": {"type": Control, "path": UPath(id_ == 'feed_report_warn_ll', visible_ == True)},
            "playlist_icon": {"type": Control, "path": UPath(id_ == 'icon', visible_ == True)},
            "foryou_playlist_name": {"type": Control, "path": UPath(id_ == 'tv_risk_warning_hint', visible_ == True)},
            "region_brazil_popup_advertisement_close": {"type": Control,
                                                        "path": UPath(id_ == 'closeImg', visible_ == True)},
            "couldnotloadvideo": {"path": UPath(id_ == "tv_desc", visible_ == True)},
            "inbox_activity": {"type": Control,
                               "path": UPath(~text_ == 'Activity|Activities|New activity', visible_ == True)},
            "inbox_username": {"type": Control, "path": UPath(text_ == 'cduiauto002', visible_ == True)},
            # 上传视频
            "bg_cover": {"path": UPath(id_ == 'bg_cover')},
            # hy_dev
            "hy_dev_icon": {"type": Control, "path": UPath(id_ == 'icon', type_ == 'android.widget.ImageView')},
            "lynx": {"type": Control, "path": UPath(id_ == 'plugin_recycler_view') / 8},
            "module_content": {"type": Control, "path": UPath(id_ == 'module_content') / 0 / 0},
            "tt_shop": {"type": Control, "path": UPath(id_ == 'tab_container') / 1},
            "enter_tt_shop": {"type": Control, "path": UPath(desc_ == 'Enter')},
            "lynx_back_button": {"type": Control,
                                 "path": UPath(type_ == 'com.bytedance.ies.stark.framework.ui.TitleBar') / 0},
            "panel_close": {"type": Control, "path": UPath(id_ == 'iv_global_close', visible_ == True)},
            "later_button": {"type": Control, "path": UPath(text_ == "Later", visible_ == True)},
            "right_icon": {"type": Control, "path": UPath(id_ == "right_menu_ll", visible_ == True)},
            "panel_close": {"type": Control, "path": UPath(id_ == 'iv_global_close', visible_ == True)},
            # 权限弹窗
            "turn_on_location": {"path": UPath(text_ == "Turn on your location?")},
            "continue": {"path": UPath(text_ == 'Continue')},
            "View_all": {"path": UPath(text_ == 'View_all')},
            # 推人视频关注
            "follow_btn_under_video": {
                'type': Control,
                'path': UPath(id_ == 'btn_follow', text_ == 'Follow')},
            "following_title": {"path": UPath(id_ == 'tv_title', visible_ == True)},
            "following_desc": {"path": UPath(id_ == 'tv_desc', visible_ == True)},
            "card_part": {"path": UPath(id_ == 'super_recommend')},
            "card_head": {"path": UPath(id_ == 'avatar', visible_ == True)},
            "card_name": {"path": UPath(id_ == 'real_name', visible_ == True)},
            "card_nickname": {"path": UPath(id_ == 'user_name', visible_ == True)},
            "card_video": {"path": UPath(type_ == 'androidx.recyclerview.widget.RecyclerView') / 1},
            "card_close": {
                "path": UPath(type_ == 'androidx.recyclerview.widget.RecyclerView') / 1 / UPath(id_ == 'close')},
            # 儿童账号下控价元素
            "home_tab_icon_img": {"path": UPath(id_ == 'home_tab_icon_img')},
            "story_ring": {'type': Control, "path": UPath(id_ == 'user_story_ring', visible_ == True)},
            "story_tag": {'type': Control, "path": UPath(id_ == 'story_tag_layout', visible_ == True)},

            # 直播
            'live': {'type': Control,
                     'path': UPath(id_ == 'rl_tab_container') / UPath(type_ == 'android.widget.ImageView',
                                                                      visible_ == True)},
            "story_ring": {"type": Control, "path": UPath(id_ == 'user_story_ring', visible_ == True)},
            "story_tag": {"type": Control, "path": UPath(id_ == 'story_tag_layout', visible_ == True)},
            "super_recommend_following": {"path": UPath(id_ == 'super_recommend')},
            "for_you_btn": {"path": UPath(text_ == 'For You')},
            "search_button": {"path": UPath(id_ == 'favorite_button', visible_ == True)},
            "live_head": {"path": UPath(id_ == 'icon_tag')},
            # 多个直播天窗
            "live_head1": {
                "path": UPath(id_ == "recycler_view") / UPath(id_ == "iv_root", index=0) / UPath(id_ == "icon_tag")},
            "tv_upvote_repost": {
                "path": UPath(id_ == "upvote_bubble1") / UPath(id_ == "v_touch_area", visible_ == True)},
            "tb_recommend": {"path": UPath(id_ == "tb_recommend")},
            "tv_upvote_you_reposted": {
                "path": UPath(id_ == "upvote_bubble1", visible_ == True) / UPath(id_ == "tv_upvote")},
            "tv_upvote_add_comment": {"path": UPath(text_ == "Add comment", visible_ == True)},
            "reposted_avatar": {"type": Control,
                                "path": UPath(id_ == "upvote_bubble1", visible_ == True) / UPath(
                                    id_ == "iv_upvote_avatarStart")},
            "reposted_avatar_middle": {"type": Control,
                                       "path": UPath(id_ == "upvote_bubble1",
                                                     visible_ == True) / UPath(id_ == "iv_upvote_avatarMiddle")},
            "reposted_avatar_end": {"type": Control,
                                    "path": UPath(id_ == "upvote_bubble1",
                                                  visible_ == True) / UPath(id_ == "iv_upvote_avatarEnd")},
            "tv_upvote_three_reposted": {"type": Control,
                                         "path": UPath(id_ == "upvote_bubble1", visible_ == True) / UPath(
                                             id_ == "tv_upvote")},
            "upvote_entrance_img": {"type": Control, "path": UPath(~id_ == "upvote_entrance_img|aiv_share_channel_icon")},
            "nav_bar_title": {"type": Control, "path": UPath(id_ == "nav_bar_title", visible_ == True)},
            "add_account": {"type": Control, "path": UPath(text_ == "Add account")},
            "remove_repost": {"path": UPath(text_ == "Remove repost", visible_ == True)},
            "repost_guidance": {"type": Control, "path": UPath(id_ == "cl_upvote_demo")},
            "close_repost_guidance": {"type": Control, "path": UPath(id_ == "iv_close")},
            "confirm_repost_guidance": {"type": Control, "path": UPath(id_ == "tb_recommend")},
            "check_box_upvote": {"type": Control, "path": UPath(id_ == "check_box_upvote", visible_ == True)},
            "comment_edit_new_bottom": {"type": Control,
                                        "path": UPath(
                                            id_ == "comment_edit_new")},
            "comment_edit_new_up": {"type": Control,
                                    "path": UPath(
                                        id_ == "comment_edit_new")},
            "comment_send_new": {"type": Control, "path": UPath(id_ == "comment_send_new")},
            "comment_divider": {"type": Control,
                                "path": UPath(id_ == "recyclerView") / UPath(type_ == "LinearLayout", index=0)},
            "comment_back_btn": {"type": Control, "path": UPath(id_ == "back_btn")},
            "comment_top_item": {
                "path": UPath(id_ == "recyclerView") / UPath(id_ == "container_bg", index=0) / UPath(
                    id_ == "content")},
            "comment_add_comment_button": {"path": UPath(id_ == "comment_add_comment_button", visible_ == True)},
            "input_comment": {"type": MTextEdit, "path": UPath(id_ == 'comment_edit_new')},
            "comment_mention_list_panel": {"path": UPath(id_ == 'layout_comment_mention_list')},
            "no_comment_panel": {"path": UPath(id_ == "status_view_flex_layout", visible_ == True)},

            # Photo mode
            "photo_mode_indicator_style": {
                "path": UPath(type_ == "com.ss.android.ugc.aweme.ui.feed.photos.DotIndicatorView",
                              id_ == "photos_dot_indicator", visible_ == True)},
            "photo_mode_image_layout": {
                "path": UPath(type_ == "android.widget.FrameLayout",
                              id_ == "photomode_photos_dummy_layout", visible_ == True)},

            "inbox_text": {"path": UPath(text_ == "Inbox")},
            "home_root": {"path": UPath(id_ == "main_content_frame")},
            "more_friends": {"path": UPath(text_ == "More friends")},
            "inner_nick": {"path": UPath(id_ == "root_layout", visible_ == True)/UPath(id_ == "title", visible_ == True)},
            "send_to_friends": {"path": UPath(id_ == "share_list_title")},
            "see_more_btn": {"type": Control, "path": UPath(id_ == 'tv_toggle', visible_ == True)},
            "hide_btn": {"type": Control, "path": UPath(text_ == 'Hide', visible_ == True)},
            "pause_btn": {"type": Control, "path": UPath(id_ == 'play_iv', visible_ == True)},
            "screen_panel": {"type": Control, "path": UPath(id_ == 'widget_container', visible_ == True)},
            "feed_music_title": {"type": Control, "path": UPath(id_ == 'music_title', visible_ == True)},
            "no_interest_btn": {"type": Control, "path": UPath(id_ == 'action_list')/1/UPath(id_ == 'share_action_label')},
            "clear_mode_btn": {"type": Control, "path": UPath(~text_ == 'Clear mode|Clear display', visible_ == True)},
            "quit_clear_mode_btn": {"type": Control, "path": UPath(id_ == "ic_exit_clear_mode", visible_ == True)},
            "feed_hide_btn": {"type": Control, "path": UPath(id_ == 'interact_right_area', visible_ == True)},
            "feed_bar": {"type": Control, "path": UPath(id_ == 'search_label', visible_ == True)},
            "send_to_title": {"path": UPath(id_ == "send_to_friend_title")},
            "ad_bottom_btn": {"path": UPath(id_ == "fl_button", visible_ == True)},
            "ad_bottom_btn2": {"path": UPath(id_ == "mask_fl", visible_ == True)},
            "live_video": {"path": UPath(id_ == "tv_live_tips")},
            "languages_delete_btn": {"path": UPath(id_ == "tux_nav_bar") / UPath(type_ == "TuxIconView", visible_ == True)},
            "sponsored": {"path": UPath(id_ == "rich_desc", text_ =="Sponsored", visible_ == True)},
            "feed_effect_icon": {"type": Control, "path": UPath(id_ == 'anchor_tag_icon', visible_ == True)},
            "effect_title": {"type": Control, "path": UPath(id_ == 'anchor_tag_title', visible_ == True)},
            "feed_trending_bar": {"type": Control, "path": UPath(id_ == 'bottom_new')/UPath(type_ == 'android.widget.RelativeLayout')},
            "feed_trending_icon": {"type": Control, "path": UPath(id_ == 'icon_start', visible_ == True)},
            "feed_trending_text": {"type": Control, "path": UPath(id_ == 'content', type_ == 'com.bytedance.tux.input.TuxTextView')},
            "feed_trending_allow": {"type": Control, "path": UPath(~id_ == 'arrow_end|bq', visible_ == True)},
            "feed_trending_br": {"type": Control, "path": UPath(~id_ == 'br|rank_text|bj|bw', visible_ == True)},
            "playlist_bottom_bar": {"type": Control, "path": UPath(~id_ == 'playlist_bottom_bar|root_bottom_banner', visible_ == True)},
            "playlist_bottom_icon": {"type": Control, "path": UPath(id_ == "root_bottom_banner") / 0},
            "play_next_btn": {"type": Control, "path": UPath(id_ == 'button', visible_ == True)},
            "feed_playlist_text": {"type": Control, "path": UPath(id_ == "root_bottom_banner") / UPath(type_ == "TuxTextView")},
            'comments_icon': {'path': UPath(id_ == 'comment_image', visible_ == True)},
            "ad_nickname": {"type": Control, "path": UPath(id_ == 'cross_react_view')},
            "ad_guide_title": {"type": Control, "path": UPath(id_ == 'cross_title_bar')},
            "ad_card": {"type": Control, "path": UPath(id_ == 'spark_container')/0/1},
            "ad_card_panel": {"type": Control, "path": UPath(id_ == 'widget_container', visible_ == True)},
            "ad_download_icon": {"type": Control, "path": UPath(id_ == 'loading_percent_txt')},
            "feed_ad_download": {"type": Control, "path": UPath(id_ == 'feed_ad_download', visible_ == True)},
            "ad_pop_up_bar": {"type": Control, "path": UPath(id_ == 'pop_up_web_title_bar_in', visible_ == True)},
            "ad_popup_panel": {"type": Control, "path": UPath(id_ == 'container', type_ == 'android.widget.LinearLayout')},
            "fl_comment_bg": {"path":UPath(id_ == "nested_linearlayout") / 1},
            "comment_out_view": {"path": UPath(id_ == "comment_out_view")},
            "close_comment": {'path': UPath(id_ == 'back_btn')},

            "feed_poi_icon": {"type": Control, "path": UPath(id_ == "poi_icon", visible_ == True)},
            "bottom_user_list": {"type": Control, "path": UPath(id_ == "recycle_view")},
            "send": {"type": Control, "path": UPath(id_ == "send")},
            "tv_live_icon" : {"path": UPath(id_ == "tv_live_icon")},
            "delete_all_videos": {"type": Control, "path": UPath(id_ == 'actionsheet_actiongroups_container')/4},
            "confirm_delete_btn": {"type": Control, "path": UPath(text_ == 'Yes', visible_ == True)},
            # must show
            "warning_banner_text": {"type": Control, "path": UPath(id_ == 'tv_risk_warning_hint', visible_ == True)},
            "paid_collection": {"type": Control, "path": UPath(id_ == 'anchor_desc')/UPath(text_ == "TT_series_test1", visible_ == True)},
            "vertical_solution_game": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag', visible_ == True)/UPath(text_ == "Honkai: Star Rail")},
            "comment_anchor": {"type": Control, "path": UPath(id_ == 'ttcm_brand_name_tv', visible_ == True)},
            "personal_comment_anchor": {"path": UPath(id_ == 'anchor_tag_root', visible_ == True)},
            "resso_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(~text_ == "Resso | Play full song|Resso · Play full song", visible_ == True)},
            "ttm_playlist_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(~text_ == "Playlist | kpop|Playlist · kpop", visible_ == True)},
            "playlist_anchor_panel": {"type": Control, "path": UPath(id_ == 'engine_container', visible_ == True)},
            "ba_leadgen_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(text_ == "Get quote", visible_ == True)},
            "sg_poi_anchor": {"type": Control, "path": UPath(id_ == 'poi_title_anchor_layout', visible_ == True)},
            "live_anchor_panel": {"type": Control, "path": UPath(id_ == 'popup_inner_container', visible_ == True)},
            "minigame_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(text_ == "Play Love Tester on TikTok", visible_ == True)},
            "effect_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(text_ == "Super Cute Snail", visible_ == True)},
            "be_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(text_ == "The Babylon Effect", visible_ == True)},
            "movietok_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(text_ == "The Shawshank Redemption", visible_ == True)},
            "game_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(~text_ == "Link MLBB to get rewards|Click to get Elite skin", visible_ == True)},
            "donation_anchor": {"type": Control, "path": UPath(id_ == 'layout_anchor_tag') / UPath(text_ == "Act to Change", visible_ == True)},
            "quick_comment_button": {"type": Control, "path": UPath(id_ == 'common_feed_bottom_button_slot_id', visible_ == True)},
            "see_translation_btn": {"type": Control, "path": UPath(text_ == "See translation", visible_ == True)},
            "see_original_btn": {"type": Control, "path": UPath(~text_ == '查看原始內容|See original', visible_ == True)},
            "quick_comment_edit": {"type": Control, "path": UPath(id_ == 'tv_fake_edit', visible_ == True)},
            "comment_edit_panel": {"type": Control, "path": UPath(id_ == 'comment_edit_layout_new_root', visible_ == True)},
            "donation_enter": {"type": Control, "path": UPath(id_ == 'donation_enter', visible_ == True)},
            "anchor_list_btn": {"type": Control, "path": UPath(id_ == 'list_button', visible_ == True)},
            'user_avatar_layout_indicator': {'path': UPath(id_ == 'user_avatar_layout_indicator', visible_ == True)},
            'profile_btn_extra': {'path': UPath(id_ == 'profile_btn_extra')},
            'feed_list__2_tv_top': {'path': UPath(id_ == 'feed_list', visible_ == True) / 2 / UPath(id_ == 'tv_top')},
            'feed_list__5_cover': {'path': UPath(id_ == 'feed_list', visible_ == True) / 5 / UPath(id_ == 'cover')},

            "qna_banner": {"type": Control, "path": UPath(id_ == 'qna_feed_banner_tv', visible_ == True)},
            "qna_button": {"type": Control, "path": UPath(id_ == 'qa_invite_button', visible_ == True)},
            "ba_leadgen_btn": {"type": Control, "path": UPath(id_ == 'common_anchor_title', text_ == 'Get quote')},
            "live_anchor": {"type": Control, "path": UPath(id_ == 'anchor_container') / 1 / UPath(id_ == 'common_anchor_title')},
            "game_anchor_v1": {"type": Control, "path": UPath(id_ == 'anchor_container') / 2 / UPath(id_ == 'common_anchor_title')},
            "live_anchor_v1": {"type": Control, "path": UPath(id_ == 'anchor_container')/1/UPath(id_ == 'common_anchor_icon')},
            "favorite_bold_text": {"type": Control, "path": UPath(id_ == 'txt_favorite_bold', visible_ == True)},
            "favorite_ok_btn": {"type": Control, "path": UPath(id_ == "btn_ok_favorite", visible_ == True)},
            "auto_caption": {"type": Control, "path": UPath(~id_ == 'caption_text_view|collapse_caption_idle_view', visible_ == True)},
            "confirm_btn": {"path": UPath(id_ == 'confirm', visible_ == True)},
            "webview_icon": {"type": Control, "path": UPath(id_ == 'debug_info_tag', visible_ == True)},
            "comment_manage_btn_top": {"type": Control, "path": UPath(id_ == 'refactor_batch_management_btn_top', visible_ == True)},
            "comment_v0": {"type": Control, "path": UPath(id_ == "recyclerView", visible_ == True) / 0},
            "comment_delete_btn_top": {"type": Control, "path": UPath(id_ == 'refactor_batch_management_delete_btn', visible_ == True)},
            "comment_done_btn_top": {"type": Control, "path": UPath(id_ == 'refactor_batch_management_done_btn_top', visible_ == True)},
            "channel_list": {"type": ChannelList, "root": "share_action_layout", "path": UPath(id_ == "channel_list")},
            "repost_item": {"type": Control, "path": UPath(id_ == "rv_share_panel_avatar") / 0},
            "collection_anchor_title": {"type": Control, "path": UPath(id_ == 'anchor_title', visible_ == True)},
            "donation_number": {"type": Control, "path": UPath(id_ == 'donation_number', visible_ == True)},
            "feed_scm": {"type": Control, "path": UPath(id_ == 'scm_feed_label', visible_ == True)},
            "caption_area": {"type": Control, "path": UPath(id_ == 'caption_area', visible_ == True)},
            "reposted_icon": {"type": Control, "path": UPath(id_ == 'tv_upvote', visible_ == True)},
            "profile_page": {"type": Control, "path": UPath(id_ == 'profile_page', visible_ == True)},
            "report_icon": {"type": Control, "path": UPath(id_ == 'report', visible_ == True)},
            "share_panel_new": {"type": Control, "path": UPath(id_ == 'fl_main_bottom_panel', visible_ == True)},
            "photo_mode_tag": {"type": Control, "path": UPath(id_ == 'photo_mode_tag', visible_ == True)},
            "reposted_bubble": {"type": Control, "path": UPath(id_ == 'upvote_bubble1', visible_ == True)/0},
            "tako_icon": {"type": Control, "path": UPath(id_ == "tikBotIcon", visible_ == True)},
            "tako": {"type": Control, "path": UPath(id_ == "tikBotIcon", visible_ == True)},

            #RelationShip Label
            "maf_relation_label": {"type": Control, "path": UPath(id_ == "social_maf_tag_layout", visible_ == True)},
            "user_label_title":{"type": TextView, "path": UPath(id_ == "title", visible_ == True)},
            "nav_bar_title":{"type": TextView, "path": UPath(id_ == "nav_bar_title", visible_ == True)},
            "v1_pop_up_title": {"type": Control, "path": UPath(id_ == "sheet_container", visible_ == True)},

            "create_group_button": {"type": Control, "path": UPath(id_ == "create_group_button", visible_ == True)},
            "v1_pop_up_title": {"type": Control, "path": UPath(id_ == "sheet_container", visible_ == True)},
            "add_friends_btn": {"type": Control, "path": UPath(id_ == "feature_icon", visible_ == True)},
            "add_friends_btn1": {"type": Control, "path": UPath(id_ == "cta_feature_icon", visible_ == True)},
            "add_friends_btn2": {"type": Control, "path": UPath(text_ == "Add friends", visible_ == True)}

        }
    def ClickAddFriendsBtn(self):
        if self["add_friends_btn2"].wait_for_visible(timeout=5, raise_error=False):
            self["add_friends_btn2"].click()
            time.sleep(3)
        elif self["add_friends_btn"].wait_for_visible(timeout=5, raise_error=False):
            self["add_friends_btn"].click()
            time.sleep(3)
        elif self["add_friends_btn1"].wait_for_visible():
            self["add_friends_btn1"].click()
            time.sleep(3)

    def click_relation_label(self):
        self["maf_relation_label"].wait_for_visible(timeout=5, raise_error=False)
        self["maf_relation_label"].click()

    def check_v1_label_pop_up(self):
        return self["v1_pop_up_title"].wait_for_visible(timeout=5, raise_error=False)

    def get_user_label_text(self):
        self["user_label_title"].wait_for_visible(timeout=5, raise_error=False)
        return self["user_label_title"].text

    def get_nav_bar_title_text(self):
        self["nav_bar_title"].wait_for_visible(timeout=5, raise_error=False)
        return self["nav_bar_title"].text

    def click_user_avatar_layout_indicator(self):
        self['user_avatar_layout_indicator'].click()

    def click_playlist_bottom_icon(self):
        self["playlist_bottom_icon"].click()
        time.sleep(3)

    def check_user_avatar_layout_indicator(self):
        return self["user_avatar_layout_indicator"].wait_for_existing(timeout=5, raise_error=False)

    def check_feed_like_icon(self):
        return self["feed_like_icon"].wait_for_visible(timeout=3, raise_error=False)

    def check_share_panel_new(self):
        return self["share_panel_new"].wait_for_visible(timeout=3, raise_error=False)

    def check_photo_mode_tag(self):
        return self["photo_mode_tag"].wait_for_visible(timeout=3, raise_error=False)

    def check_reposted_bubble(self):
        return self["reposted_bubble"].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_profile_btn_extra_existing(self):
        self['profile_btn_extra'].wait_for_existing()

    def wait_for_feed_list_tv_top_existing(self):
        self['feed_list__2_tv_top'].wait_for_existing()

    def click_feed_list_cover(self):
        self['feed_list__5_cover'].click()

    def scroll_comment_panel(self, coefficient_x=0, coefficient_y=0):
        self["fl_comment_bg"].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)
        time.sleep(1)

    def check_game_anchor(self):
        return self["game_anchor"].wait_for_existing(timeout=5, raise_error=False)

    def check_anchor_list(self):
        if self["ba_leadgen_btn"].wait_for_existing(timeout=5, raise_error=False):
            if self["live_anchor"].text[0:10] == "LIVE Event":
                # if self["game_anchor_v1"].text == "Play Love Tester on TikTok":
                return True
        return False

    def click_ba_leadgen_btn(self):
        self["ba_leadgen_btn"].click()

    def click_game_anchor_v1(self):
        self["game_anchor_v1"].click()

    def click_live_anchor_v1(self):
        self["live_anchor_v1"].click()
        time.sleep(5)

    def check_webview_icon(self):
        return self["webview_icon"].wait_for_existing(timeout=5, raise_error=False)

    def check_auto_caption(self):
        return self["auto_caption"].wait_for_existing(timeout=5, raise_error=False)

    def click_confirm_btn(self):
        if self["confirm_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["confirm_btn"].click()
            time.sleep(2)
        self["close_btn"].click()
        self["confirm_btn"].click()
        time.sleep(3)

    def check_qna_button(self):
        return self["qna_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_qna_button(self):
        return self["qna_button"].click()

    def check_donation_anchor(self):
        return self["donation_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def click_donation_anchor(self):
        self["donation_anchor"].click()
        time.sleep(3)

    def check_donation_number(self):
        return self["donation_number"].wait_for_existing(timeout=10, raise_error=False)

    def check_qna_banner(self):
        return self["qna_banner"].wait_for_existing(timeout=5, raise_error=False)

    def click_qna_banner(self):
        self["qna_banner"].click()

    def check_poi_icon_more(self):
        return self["poi_icon_more"].wait_for_existing(timeout=5, raise_error=False)

    def click_anchor_list_btn(self):
        if self["anchor_list_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["anchor_list_btn"].click()
        time.sleep(3)

    def check_mini_game_anchor(self):
        return self["minigame_anchor"].wait_for_existing(timeout=5, raise_error=False)

    def check_donation_enter(self):
        return self["donation_enter"].wait_for_existing(timeout=5, raise_error=False)

    def check_comment_edit_panel(self):
        return self["comment_edit_panel"].wait_for_existing(timeout=5, raise_error=False)

    def click_quick_comment_edit(self):
        self["quick_comment_edit"].click()
        time.sleep(2)

    def check_quick_comment_button(self):
        return self["quick_comment_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_poi_subtitle(self):
        return self["feed_tag_layout"].wait_for_existing(timeout=5, raise_error=False)

    def check_see_translation_btn(self):
        return self["see_translation_btn"].wait_for_existing(timeout=5, raise_error=False)

    def check_feed_scm(self):
        return self["feed_scm"].wait_for_existing(timeout=5, raise_error=False)

    def click_feed_scm(self):
        self["feed_scm"].click()
        time.sleep(3)

    def check_caption_area(self):
        return self["caption_area"].wait_for_existing(timeout=5, raise_error=False)

    def check_reposted_icon(self):
        return self["reposted_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_reposted_icon(self):
        self["reposted_icon"].click()
        time.sleep(3)

    def check_comment_panel(self):
        return self["comment_panel"].wait_for_existing(timeout=5, raise_error=False)

    def check_nickname(self):
        return self["inner_nick"].wait_for_existing(timeout=5, raise_error=False)

    def click_nickname(self):
        self["inner_nick"].click()
        time.sleep(3)

    def check_profile_page(self):
        return self["profile_page"].wait_for_existing(timeout=5, raise_error=False)

    def check_report_icon(self):
        return self["report_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_report_icon(self):
        self["report_icon"].click()
        time.sleep(3)

    def click_see_translation_btn(self):
        self["see_translation_btn"].click()

    def check_see_original_btn(self):
        return self["see_original_btn"].wait_for_existing(timeout=5, raise_error=False)

    def click_see_original_btn(self):
        self["see_original_btn"].click()

    def click_game_anchor(self):
        self["game_anchor"].click()
        time.sleep(2)

    def find_capcut_name(self, content):
        for _ in Retry(timeout=30, raise_error=False):
            if self.return_capcut_text() == content:
                break
            self.swipe_up()
        time.sleep(2)

    def find_multiple_anchor(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self["poi_icon_more"].wait_for_existing(timeout=5, raise_error=False):
                break
            self.swipe_up()
        time.sleep(2)

    def check_movietok_anchor(self):
        return self["movietok_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def find_movietok_anchor(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["movietok_anchor"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def click_movietok_anchor(self):
        self["movietok_anchor"].click()
        time.sleep(2)

    def check_capcut_anchor(self):
        if self.return_capcut_text() == "CapCut":
            return True
        return False

    def check_be_anchor(self):
        return self["be_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def find_be_anchor(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["be_anchor"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def find_caption(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["auto_caption"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def click_be_anchor(self):
        self["be_anchor"].click()
        time.sleep(2)

    def check_effect_anchor(self):
        return self["effect_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def find_effect_anchor(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["desc"].existing and self["desc"].text == "Effect anchor":
                break
            self.swipe_up()
            time.sleep(2)

    def find_no_evade_video(self, content):
        for _ in Retry(timeout=30, raise_error=False):
            # print("11111111", self["desc"].text)
            if self["desc"].existing and self["desc"].text == content:
                break
            self.swipe_up()
            time.sleep(2)

    def check_desc(self, content):
        if self["desc"].text == content:
            return True
        return False

    def find_no_evade_video_v1(self, content):
        for _ in Retry(timeout=30, raise_error=False):
            # print("11111111", self["feed_nickname"].text)
            if self["feed_nickname"].existing and self["feed_nickname"].text == content:
                break
            self.swipe_up()
            time.sleep(2)

    def find_nickname_video(self, content):
        for _ in Retry(timeout=30, raise_error=False):
            if self["feed_nickname"].existing and self["feed_nickname"].text in content:
                break
            self.swipe_up()
            time.sleep(2)

    def click_effect_anchor(self):
        self["effect_anchor"].click()
        time.sleep(2)

    def check_minigame_anchor(self):
        return self["minigame_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def click_minigame_anchor(self):
        self["minigame_anchor"].click()
        time.sleep(2)

    def find_anchor_name(self, content):
        for _ in Retry(timeout=25, raise_error=False):
            if self.return_anchor_text() == content:
                break
            self.swipe_up()

    def check_live_anchor_panel(self):
        return self["live_anchor_panel"].wait_for_existing(timeout=5, raise_error=False)

    def return_anchor_text(self):
        return self["poi_title"].text[0:10]

    def return_capcut_text(self):
        return self["poi_title"].text[0:6]

    def click_live_anchor(self):
        self["poi_title"].click()
        time.sleep(2)

    def check_live_anchor(self):
        if self.return_anchor_text() == "LIVE Event":
            return True
        return False

    def check_comment_anchor(self):
        if self["personal_comment_anchor"].existing:
            return False
        return True

    def find_comment_anchor_video(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["desc"].existing and self["desc"].text == "Tcm comment anchor for test":
                break
            self.swipe_up()
            time.sleep(2)

    def click_comment_anchor(self):
        self["comment_anchor"].click()
        time.sleep(2)

    def check_sg_poi_anchor(self):
        return self["sg_poi_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def click_sg_poi_anchor(self):
        self["sg_poi_anchor"].click()
        time.sleep(2)

    def check_ba_leadgen_anchor(self):
        return self["ba_leadgen_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def find_ba_leadgen_anchor(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["desc"].existing and self["desc"].text == "BA leadgen anchor":
                break
            self.swipe_up()
            time.sleep(2)

    def click_ba_leadgen_anchor(self):
        self["ba_leadgen_anchor"].click()
        time.sleep(2)

    def check_playlist_anchor_panel(self):
        return self["playlist_anchor_panel"].wait_for_existing(timeout=10, raise_error=False)

    def check_ttm_playlist_anchor(self):
        return self["poi_tag_root"].wait_for_existing(timeout=10, raise_error=False)

    def find_ttm_playlist_anchor_video(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["desc"].existing and self["desc"].text == "ttm playlist anchor":
                break
            self.swipe_up()
            time.sleep(2)

    def click_ttm_playlist_anchor(self):
        self["poi_tag_root"].click()
        time.sleep(2)

    def check_resso_anchor(self):
        return self["poi_title"].wait_for_existing(timeout=10, raise_error=False)

    def find_resso_anchor(self):
        for _ in Retry(timeout=20, raise_error=False):
            if self["desc"].existing and self["desc"].text == "ug_pick_resso anchor":
                break
            self.swipe_up()
            time.sleep(2)

    def click_resso_anchor(self):
        self["poi_title"].click()
        time.sleep(2)

    def check_comment_panel_anchor(self):
        return self["comment_anchor"].wait_for_existing(timeout=10, raise_error=False)

    def check_warning_banner(self):
        return self["feed_playlist_icon"].wait_for_existing(timeout=10, raise_error=False)

    def check_vertical_solution_game(self):
        return self["vertical_solution_game"].wait_for_existing(timeout=10, raise_error=False)

    def find_vertical_solution_game(self, content):
        for _ in Retry(timeout=30, raise_error=False):
            # print("11111111", self["effect_title"].text)
            if self["effect_title"].existing and self["effect_title"].text == content:
                break
            self.swipe_up()
            time.sleep(2)

    def check_template_anchor(self):
        return self["effect_title"].wait_for_existing(timeout=10, raise_error=False)

    def find_quick_comment_video(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self["quick_comment_button"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def click_vertical_solution_game(self):
        return self["vertical_solution_game"].click()

    def check_paid_collection(self):
        return self["paid_collection"].wait_for_existing(timeout=10, raise_error=False)

    def find_collection_anchor_title(self, content):
        for _ in Retry(timeout=30, raise_error=False):
            # print("11111111", self["collection_anchor_title"].text)
            if self["collection_anchor_title"].existing and self["collection_anchor_title"].text == content:
                break
            self.swipe_up()
            time.sleep(2)

    def click_paid_collection(self):
        return self["paid_collection"].click()

    def check_warning_banner(self):
        return self["warning_banner_text"].wait_for_existing(timeout=3, raise_error=False)

    def click_warning_banner(self):
        return self["warning_banner_text"].click()

    def check_warning_banner_text(self, content):
        if self["collection_playlist_icon"].visible:
            if self["collection_playlist_icon"].text == content:
                return True
            return False

    def check_warning_banner_text_v1(self, content):
        if self["warning_banner_text"].visible:
            if self["warning_banner_text"].text == content:
                return True
            return False

    def find_covid_19(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self["warning_banner_text"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def find_feed_poi_icon(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self["feed_poi_icon"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def find_warning_banner_video(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self["feed_playlist_icon"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def check_warning_banner_clickable(self):
        self["feed_playlist_icon"].click()
        time.sleep(2)
        if self["feed_playlist_icon"].existing:
            return True
        return False

    def CheckFeedPageLiveVideoTitle(self):
        if self["live_video"].wait_for_existing(timeout=3, raise_error=False):
            return self["live_video"].wait_for_existing(timeout=3, raise_error=False)
        else:
            return False

    def ClickLanguagesDeleteBtn(self):
        if self["languages_delete_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["languages_delete_btn"].click()
            time.sleep(3)

    def check_ad_popup_panel(self):
        return self["ad_popup_panel"].wait_for_existing(timeout=5, raise_error=False)

    def check_ad_pop_up_bar(self):
        return self["ad_pop_up_bar"].wait_for_existing(timeout=5, raise_error=False)

    def click_ad_pop_up_bar(self):
        return self["ad_pop_up_bar"].click()

    def check_ad_guide_title(self):
        return self["ad_guide_title"].wait_for_existing(timeout=20, raise_error=False)

    def click_feed_ad_download(self):
        if self["feed_ad_download"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_ad_download"].click()
        time.sleep(2)

    def check_ad_guide(self):
        return self["ad_guide"].wait_for_existing(timeout=20, raise_error=False)

    def check_ad_nickname_jumping(self):
        return self["ad_nickname"].wait_for_existing(timeout=3, raise_error=False)

    def check_ad_download_icon(self):
        return self["ad_download_icon"].wait_for_existing(timeout=3, raise_error=False)

    def check_ad_card_panel(self):
        return self["ad_card_panel"].wait_for_existing(timeout=3, raise_error=False)

    def click_ad_card(self):
        if self["ad_card"].wait_for_existing(timeout=10, raise_error=False):
            self["ad_card"].click()
        time.sleep(2)

    def check_google_play(self):
        for _ in Retry(timeout=10, raise_error=False):
            logger.info("Current activity: %s" % self.app.get_device().get_current_activity())
            if "google" in self.app.get_device().get_current_activity() or \
                "chromium" in self.app.get_device().get_current_activity() or \
                "SplashActivity" in self.app.get_device().get_current_activity():
                return True
            logger.info("没有跳转到Google Play!")

    def find_ad_bottom_btn2(self):
        for _ in Retry(timeout=150, raise_error=False):
            if self["ad_bottom_btn2"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()

    def click_ad_bottom_btn2(self):
        return self["ad_bottom_btn2"].click()

    def find_sponsored_icon(self):
        for _ in Retry(timeout=150, raise_error=False):
            if self["sponsored"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()

    def check_user_avatar(self):
        return self["user_avatar"].wait_for_existing(timeout=3, raise_error=False)

    def click_user_avatar(self):
        return self["user_avatar"].click()

    def check_ad_title(self):
        return self['title'].wait_for_existing(timeout=3, raise_error=False)

    def click_ad_title(self):
        return self['title'].click()

    def check_ad_info(self):
        if self["feed_nickname"].wait_for_existing(timeout=3, raise_error=False):
            if self["desc"].wait_for_existing(timeout=3, raise_error=False):
                if self["ad_bottom_btn2"].wait_for_existing(timeout=3, raise_error=False):
                    return True
        return False

    def get_comment_num(self):
        self["comments_icon"].click()
        time.sleep(3)
        self["comment_back_btn"].click()
        time.sleep(2)

    def click_comment_back_btn(self):
        self["comment_back_btn"].click()
        time.sleep(3)

    def click_feed_back_button(self):
        self["feed_back_button"].click()
        time.sleep(3)

    def return_feed_playlist_text(self):
        return self["feed_playlist_text"].text

    def check_playlist_bottom_bar(self):
        return self["playlist_bottom_bar"].wait_for_existing(timeout=3, raise_error=False)

    def find_playlist_video(self):
        for _ in Retry(timeout=30, raise_error=False):
            if self["playlist_bottom_bar"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def click_playlist_bottom_bar(self):
        self["playlist_bottom_bar"].click()
        time.sleep(3)

    def check_play_next_btn(self):
        return self["play_next_btn"].wait_for_existing(timeout=3, raise_error=False)

    def click_play_next_btn(self):
        self["play_next_btn"].click()
        time.sleep(3)

    def return_feed_trending_br(self):
        if self["feed_trending_br"].text[0:3] == "No.":
            return True
        return False

    def check_feed_trending_allow(self):
        return self["feed_trending_allow"].wait_for_existing(timeout=3, raise_error=False)

    def check_feed_trending_text(self):
        return self["feed_trending_text"].wait_for_existing(timeout=3, raise_error=False)

    def check_feed_trending_icon(self):
        return self["feed_trending_icon"].wait_for_existing(timeout=3, raise_error=False)

    def click_feed_trending_icon(self):
        self["feed_trending_icon"].click()
        time.sleep(3)

    def check_feed_trending_bar(self):
        if self["playlist_bottom_bar"].existing or self["feed_bar"].existing:
            self.app.testcase.log_record("热点视频优先展示了playlist bar！")
            from shoots.exceptions import StopRunningCase
            raise StopRunningCase
        return self["feed_trending_bar"].wait_for_existing(timeout=3, raise_error=False)

    def click_feed_playlist_icon(self):
        self["feed_playlist_icon"].click()
        time.sleep(3)

    def return_effect_title(self):
        return self["effect_title"].text

    def click_effect_title(self):
        self["effect_title"].click()
        time.sleep(3)

    def check_feed_effect_icon(self):
        return self["feed_effect_icon"].wait_for_existing(timeout=3, raise_error=False)

    def click_feed_effect_icon(self):
        self["feed_effect_icon"].click()
        time.sleep(3)

    # Photo indicator
    def is_photo_mode_dot_indicator_exist(self):
        return self["photo_mode_indicator_style"].wait_for_existing(timeout=3, raise_error=False)

    # click like button
    def click_like_button(self):
        self["digg_container"].click()
        time.sleep(3)

    # 点击中间页搜索框
    def into_text_area(self):
        self["text_area"].refresh()
        self["text_area"].click()

    def click_suggested_options_text(self):
        if self["suggested_options_lynx"].wait_for_existing(timeout=5, raise_error=False):
            # suggested_options_text = self["suggested_options_lynx"].text
            self["suggested_options_lynx"].click()
        else:
            # suggested_options_text = self["suggested_options"].text
            self["suggested_options"].click()
        # return suggested_options_text
    # check repost button exist after watch twice

    def is_tv_upvote_visible(self):
        self["tv_upvote_repost"].wait_for_visible(timeout=15, interval=0.5, raise_error=True)

    def check_feed_bar(self):
        return self["feed_bar"].wait_for_existing(timeout=3, raise_error=False)

    def click_feed_bar(self):
        self["feed_bar"].click()
        time.sleep(2)

    def check_feed_hide_btn(self):
        return self["quit_clear_mode_btn"].wait_for_existing(timeout=3, raise_error=False)

    def check_see_more_btn(self):
        return self["see_more_btn"].wait_for_existing(timeout=3, raise_error=False)

    def check_no_interest_btn(self):
        return self["no_interest_btn"].wait_for_existing(timeout=3, raise_error=False)

    def click_no_interest_btn(self):
        if self["no_interest_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["no_interest_btn"].click()

    def check_clear_mode_btn(self):
        return self["clear_mode_btn"].wait_for_existing(timeout=3, raise_error=False)

    def click_clear_mode_btn(self):
        if self["clear_mode_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["clear_mode_btn"].click()
        time.sleep(5)

    def click_quit_clear_mode_btn(self):
        self["quit_clear_mode_btn"].click()

    def click_see_more_btn(self):
        if self["see_more_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["see_more_btn"].click()
        time.sleep(2)

    def click_screen_panel(self):
        if self["screen_panel"].wait_for_existing(timeout=3, raise_error=False):
            self["screen_panel"].click()
        time.sleep(2)

    def click_feed_music_title(self):
        if self["feed_music_title"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_music_title"].click()
        time.sleep(2)

    def check_hide_btn(self):
        return self["hide_btn"].wait_for_existing(timeout=3, raise_error=False)

    def click_hide_btn(self):
        if self["hide_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["hide_btn"].click()

    def check_pause_btn(self):
        return self["pause_btn"].wait_for_existing(timeout=3, raise_error=False)

    # click repost button
    def click_tv_upvote_repost(self):
        self["tv_upvote_repost"].wait_for_visible(timeout=5, interval=0.2, raise_error=False)
        self["tv_upvote_repost"].click()
        if self["tb_recommend"].wait_for_visible(timeout=5, interval=0.2, raise_error=False):
            self["tb_recommend"].click()
        self["tv_upvote_you_reposted"].wait_for_visible(timeout=5, interval=0.2, raise_error=True)
        self["tv_upvote_add_comment"].wait_for_visible(timeout=5, interval=0.2, raise_error=True)

    def click_tb_recommend(self):
        if self["tb_recommend"].wait_for_visible(timeout=5, interval=0.2, raise_error=False):
            self["tb_recommend"].click()

    def click_friends_tab(self):
        if self["bottom_friends"].wait_for_existing(timeout=5, raise_error=False):
            self["bottom_friends"].click()
            time.sleep(2)

    def check_collection_playlist_icon(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["collection_playlist_icon"].existing and self["feed_nickname"].text == "@user830gb":
                return
            self.swipe_up()
            time.sleep(3)

    def click_feed_share_icon(self):
        if self["feed_share_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_share_icon"].click()

    def remove_from_favourites(self):
        if self["remove_from_favourites"].text == "Remove from Favorites":
            self["remove_from_favourites"].click()

    def add_to_favourites(self):
        if self["remove_from_favourites"].text == "Add to Favorites":
            self["remove_from_favourites"].click()

    def create_collection_group(self, text):
        if self["create_new_collection"].wait_for_existing(timeout=3, raise_error=False):
            self["create_new_collection"].click()
        if self["input_collection_name"].wait_for_existing(timeout=3, raise_error=False):
            self["input_collection_name"].input(text)
            for _ in Retry(timeout=10):
                self.app.get_device().press_back()
                if self["save_collection_name"].wait_for_existing(timeout=3, raise_error=False):
                    return

    def check_settings(self):
        if self["create_new_collection"].wait_for_existing(timeout=5, raise_error=False):
            self["create_new_collection"].click()
            time.sleep(2)
            for _ in Retry(timeout=10):
                self.app.get_device().press_back()
                if self["save_collection_name"].wait_for_existing(timeout=3, raise_error=False):
                    return

    def check_public_title(self):
        return self["public_title"].wait_for_existing(timeout=3, raise_error=False)

    def check_public_settings(self):
        return self["public_settings"].wait_for_existing(timeout=3, raise_error=False)

    def return_public_settings_text(self):
        return self["public_settings_text"].text

    # 按控件位置截图
    def take_capture(self, area):
        time.sleep(5)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            return self[area].capture()

    def click_public_settings_twice(self):
        for _ in Retry(limit=2, raise_error=False):
            self["public_settings"].click()

    def back_feed_collection_icon(self):
        self["new_collection_back"].click()
        time.sleep(2)
        self["close_collection_panel"].click()
        self.video_add_to_favorites()

    def click_collection_1(self):
        if self["collection_1"].wait_for_existing(timeout=3, raise_error=False):
            self["collection_1"].click()

    # 儿童账号下控价元素
    def home_tab_icon_img(self):
        self["home_tab_icon_img"].click()
        time.sleep(2)

    # popup handle for ad_guide
    def feed_popup_handle(self):
        if self["ad_guide"].wait_for_existing(timeout=2, interval=0.2, raise_error=False):
            self.swipe(y_direction=1)

    def set_feed(self, feed_id_list: str):
        return self["feed_debug_icon"].set_feed(feed_id_list)

    def click_inbox_activity(self):
        if self["inbox_activity"].wait_for_visible(timeout=5, raise_error=False):
            self["inbox_activity"].click()
        time.sleep(2)

    def check_post_done(self):
        time.sleep(10)
        if not self["friends_post"].wait_for_existing(timeout=180, raise_error=False):
            logger.info("发布视频超时！")

    def check_favorites_icon(self):
        for _ in Retry(limit=5, raise_error=False):
            if self["favorites_icon"].wait_for_existing(timeout=3, raise_error=False):
                break
            else:
                self.swipe_up()

    def check_no_favorites_icon(self):
        if self["favorites_icon"].existing:
            return False
        return True

    def video_add_to_favorites(self):
        if self["favorites_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["favorites_icon"].click()
            if self["favorite_bold_text"].existing:
                self["favorite_ok_btn"].click()

    def click_collection_save_btn(self):
        if self["save_collection_name"].wait_for_existing(timeout=3, raise_error=False):
            self["save_collection_name"].click()

    def find_trending_bar(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["trending_bar"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def check_trending_bar(self):
        return self["trending_bar"].wait_for_existing(timeout=3, raise_error=False)

    def click_trending_bar(self):
        if self["trending_bar"].wait_for_existing(timeout=3, raise_error=False):
            self["trending_bar"].click()

    def click_bottom_inbox(self):
        for _ in Retry(timeout=15, interval=1, raise_error=False):
            if self["inbox_activity"].wait_for_visible(timeout=2, raise_error=False):
                break
            self["bottom_inbox"].wait_for_visible()
            self["bottom_inbox"].click()

    def click_hy_dev_icon(self):
        self["hy_dev_icon"].click()
        time.sleep(1)
        self["lynx"].click()
        time.sleep(1)

    def click_live_icon(self):
        if self["live_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["live_icon"].click()

    def change_dev_tool(self):
        items = self["module_content"].items()[0:-2]
        for item in items:
            if item.children[0].checked == False:
                item.click()
        time.sleep(1)
        self["lynx_back_button"].click()
        self["panel_close"].click()
        time.sleep(1)

    def check_right_icon(self):
        if self["right_icon"].wait_for_existing(timeout=5, raise_error=False):
            return True

    def check_music_full_screen(self):
        if self["music_full_screen"].wait_for_existing(timeout=5, raise_error=False):
            self["music_full_screen"].click()

    def click_tt_shop(self):
        time.sleep(5)
        self["tt_shop"].click()
        self["enter_tt_shop"].click()
        time.sleep(3)

    def enter_tt_shop(self):
        if self["enter_tt_shop"].wait_for_existing(timeout=3, raise_error=False):
            self["enter_tt_shop"].click()
            time.sleep(3)

    def share_feed_to_friend(self):
        self["share_iv"].refresh()
        self["share_iv"].click()
        self["share_to_1st_friend"].wait_for_existing(timeout=3, raise_error=False)
        self["share_to_1st_friend"].click()
        self["share_send_button"].wait_for_existing(timeout=3, raise_error=False)
        self["share_send_button"].click()
        time.sleep(3)

    def check_feed_search_bar(self):
        if self["search_bar_word"].existing:
            return True

    def check_feed_hashtags(self):
        if self["see_more_hashtags"].existing:
            return True

    def click_search_bar(self):
        if self["search_bar_word"].wait_for_existing(timeout=5, raise_error=False):
            self["search_bar_word"].click()
            time.sleep(3)

    def click_feed_hashtag(self):
        if self["see_more_hashtags"].wait_for_existing(timeout=5, raise_error=False):
            self["see_more_hashtags"].click()
            time.sleep(5)
            rect = self["see_more_hashtags"].rect
            x = rect.left / 2 + rect.width / 2
            y = rect.top + rect.height / 2
            print("点击feed话题坐标", x, y)
            self.app.get_device().click(x=x, y=y)

    def check_login_or_signup_popup(self):
        if self["close_login_popup"].wait_for_existing(timeout=5, raise_error=False):
            self["close_login_popup"].click()
            time.sleep(3)

    def check_feed_poi_anchor(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["poi_icon"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                return True
        return False

    def not_interested(self):
        self.long_click()
        self["not_interested"].wait_for_existing(timeout=2, raise_error=False)
        self["not_interested"].click()
        time.sleep(3)

    def enter_chat_click(self):
        self["massage_entrance_box"].click()

    def enter_share_collection_group(self):
        if self["massage_entrance_box"].wait_for_existing(timeout=2, raise_error=False):
            self["massage_entrance_box"].click()

    def click_poi_long_inbox(self, vdwindow):
        for _ in Retry(timeout=50):
            if self["user_avatar"].existing:
                self.app.get_device().press_back()
            notice_inbox_list = self["notice_inbox_list"].items()
            if len(notice_inbox_list) > 0:
                for item in notice_inbox_list:
                    if item.poi_entrance.existing:
                        item.poi_entrance.click()
                        if vdwindow.poi_anchor_comment.existing:
                            self.app.get_device().press_back()
                        if vdwindow.poi_subtitle.existing:
                            return item.poi_entrance
                        else:
                            self.app.get_device().press_back()

    def click_poi_more_inbox(self, vdwindow):
        self["notice_inbox_list"].wait_for_visible(timeout=60)
        for _ in Retry(limit=3):
            if self["user_avatar"].existing:
                self.app.get_device().press_back()
            notice_inbox_list = self["notice_inbox_list"].items()
            if len(notice_inbox_list) > 0:
                for item in notice_inbox_list:
                    if item.poi_entrance.existing:
                        item.poi_entrance.click()
                        if vdwindow.poi_anchor_comment.existing:
                            self.app.get_device().press_back()
                        if vdwindow.poi_icon_more.existing:
                            return item.poi_entrance
                        else:
                            self.app.get_device().press_back()

    def check_ui_element(self):
        count = 0
        while self["couldnotloadvideo"].wait_for_existing(timeout=5, raise_error=False) and count <= 3:
            self.open_tab("Home")
            time.sleep(5)
            count += 1
        self["user_avatar"].wait_for_existing(timeout=3)
        self["title"].wait_for_existing(timeout=3)
        self["digg_count"].wait_for_existing(timeout=3)
        return True

    def check_playlist_icon(self):
        return self["playlist_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_foryou_playlist_name(self):
        if self["foryou_playlist_name"].existing:
            self["foryou_playlist_name"].click()

    def click_feed_playlist_name(self):
        if self["feed_playlist_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_playlist_icon"].click()
        else:
            self.long_click()
            self["video_add_to_playlist"].click()
            self["select_to_add_playlist"].click()
            self["confirm_add"].click()
            time.sleep(3)
            self["feed_playlist_icon"].click()

    def remove_feed_playlist_icon(self):
        if self["feed_playlist_icon"].existing:
            self.long_click()
            self["remove_from_playlist"].click()
            self["confirm_remove"].click()
            time.sleep(3)

    def check_feed_playlist_icon(self):
        self["share_iv"].click()
        time.sleep(2)
        return self["add_to_playlist_icon"].wait_for_existing(timeout=3, interval=0.2, raise_error=False)

    def check_video_playlist_permission(self):
        self.long_click()
        return self["video_playlist_permission"].wait_for_existing(timeout=3, interval=0.2, raise_error=False)

    def check_playlist_feed_icon(self):
        return self["feed_playlist_icon"].wait_for_existing(timeout=5, raise_error=False)

    def check_add_to_playlist_button(self):
        return self["video_add_to_playlist_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_add_to_playlist_button(self):
        time.sleep(2)
        if self["video_add_to_playlist_button"].text == "Remove from playlist":
            self["video_add_to_playlist_button"].click()
            time.sleep(2)
            self["confirm_playlist_remove"].click()
            time.sleep(2)
            self.long_click()
            self["video_add_to_playlist_button"].click()
        else:
            self["video_add_to_playlist_button"].click()

    def return_add_to_playlist_text(self):
        text1 = self["video_add_to_playlist_button"].text
        if text1 == "Add to playlist":
            return True
        else:
            return False

    def click_create_button(self):
        self["create_btn"].click()

    def return_feed_playlist_name(self):
        time.sleep(3)
        item = self["feed_playlist_name"].text
        if item == "playlist02":
            return True
        else:
            return False

    def check_feed_playlist_name(self):
        return self["feed_playlist_name"].wait_for_existing(timeout=5, raise_error=False)

    def click_feed_playlist_add_button(self):
        if self["feed_playlist_add_button"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_playlist_add_button"].click()
            time.sleep(2)

    def check_foryou_playlist_name(self):
        return self["foryou_playlist_name"].wait_for_existing(timeout=5, raise_error=False)

    def return_foryou_playlist_name(self):
        if self["foryou_playlist_name"].existing:
            return self["foryou_playlist_name"].text

    def check_related_person_icon(self):
        if self["no_comments"].text == 'No comments':
            return self["at_icon"].wait_for_existing(timeout=5, raise_error=False)
        else:
            return self["at_iv"].wait_for_existing(timeout=5, raise_error=False)

    def click_related_person_icon(self):
        self["at_iv"].click()
        time.sleep(2)

    def check_at_icon(self):
        if self["no_comments"].text == 'No comments':
            self["at_icon"].click()
        else:
            self["at_iv"].click()

    def check_related_person_panel(self):
        return self["cal_search_at"].wait_for_existing(timeout=5, raise_error=False)

    # 点击第一个视频
    def click_first_video(self):
        self.wait_for_ui_stable()
        for _ in Retry(timeout=60, raise_error=False):
            if self["tap_to_try_again"].wait_for_visible(timeout=2, raise_error=False):
                self["tap_to_try_again"].click()
            elif self["retry"].wait_for_visible(timeout=2, raise_error=False):
                self["retry"].click()
            elif self["try_again_later"].wait_for_visible(timeout=2, raise_error=False):
                self.scroll(coefficient_y=0.8)
            elif self["first_video"].wait_for_visible(timeout=2, raise_error=False):
                break
        self["first_video"].wait_for_visible(timeout=60)
        if self["View_all"].wait_for_visible(timeout=2, raise_error=False):
            self.swipe_up()
        logger.info("first_video界面截图")
        self.app.testcase.take_screen_shot(self.app, "first_video")
        self["first_video"].refresh()
        logger.info("空间坐标:{}".format(self["first_video"].rect))
        self["first_video"].click()
        for _ in Retry(timeout=60, interval=1, raise_error=False):
            if not self["first_video"].wait_for_invisible(timeout=10, raise_error=False):
                self.app.testcase.take_screen_shot(self.app)
                self["first_video"].click()
                logger.info("再次点击视频")
            else:
                logger.info("video控件消失")
                break

    def judge_first_video(self):
        return self["first_video"].existing

    # 寻找长锚点
    def find_poi_long(self):
        for _ in Retry(timeout=90):
            if self.poi_subtitle.wait_for_existing(timeout=1, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    def find_no_poi_anchor(self):
        for _ in Retry(timeout=45, raise_error=False):
            if self.poi_subtitle.wait_for_existing(timeout=1, raise_error=False):
                break
            self.swipe_up()

    # 寻找多锚点
    def find_poi_more(self):
        for _ in Retry(timeout=90):
            if self.poi_icon_more.wait_for_existing(timeout=1, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)

    @property
    def get_poi_anchor_title_text(self):
        return self["poi_anchor_title"].text

    # 按控件位置截图
    def take_capture(self, area):
        # time.sleep(2)
        if self[area].wait_for_existing(timeout=5) and self[area].wait_for_visible(timeout=5):
            file_path = self[area].capture()
            logger.info(file_path)
            return file_path

    @property
    def get_home_text(self):
        return self["home"].text

    def check_following(self):
        for _ in Retry(timeout=20, raise_error=False):
            self.app.restart()
            time.sleep(5)
            if self["following_btn"].wait_for_existing(timeout=5, raise_error=False):
                self.following_btn_click()
                return

    def click_following(self):
        self["following"].click()

    def click_homepage_search_btn(self):
        return self["homepage_search_btn"].click()

    def get_homepage_search_btn_width(self):
        return self["homepage_search_btn"].rect.width

    def get_homepage_search_btn_height(self):
        return self["homepage_search_btn"].rect.height

    def get_homepage_top_tabs_height(self):
        return self["top_tabs"].rect.height

    def search_image(self, device, original_pic_name):
        # control = self["homepage_search_btn"]
        # self.BasePanel.element_compare_by_image(self, device, control, original_pic_name)
        import cv2
        import os
        capture = cv2.get_screenshot(device)
        self["homepage_search_btn"].ensure_visible()
        rect = self["homepage_search_btn"].rect
        target = capture[0][rect.top:(rect.top + rect.height), rect.left:(rect.left + rect.width)]
        basedir = os.path.abspath('.')
        pic_dir = os.path.join(basedir, device.serial)
        # os.mkdir(pic_dir)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        # original_pic_dir = os.path.join(os.path.abspath(os.path.join(os.getcwd(), "../../../../../..")), "resources")
        original_pic_dir = os.path.join(os.path.abspath(os.path.realpath(__file__) + os.path.sep + "../../../.."),
                                        "resources")
        print("路径：", original_pic_dir)
        original_pic_name = os.path.join(original_pic_dir, original_pic_name + ".png")
        # return ocr_helper.dhash_diff_pic(target_pic_name, original_pic_name)
        from shoots_cv.cv import CV
        cv_ = CV()
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']
        print(compare_result)
        return compare_result > 0.99999

    def put_in_search(self, text):
        self["search_text"].wait_for_visible(timeout=5)
        self["search_text"].input(text)

    def long_click_profile(self):
        self["long_click_profile"].long_click()

    def click_profile(self):
        """
        偶现 检测profile控件大小异常，可能是加载后控件不稳定，读取信息过早导致，添加对旁边控件inbox同时校验
        """
        self["long_click_profile"].wait_for_visible()
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            self["bottom_inbox"].refresh()
            self["long_click_profile"].refresh()
            if self["bottom_inbox"].rect.top == self["long_click_profile"].rect.top:
                self.app.testcase.log_record("Me控件和inbox控件高度一致")
                break
        self["long_click_profile"].wait_for_ui_stable()
        self["long_click_profile"].click()
        mine_panel = MinePanel(root=self.app)
        for _ in Retry(timeout=60, interval=1, raise_error=False):
            if not mine_panel.judge_mine_page():
                self.wait_for_loading()
                self["long_click_profile"].click()
                self.app.testcase.log_info("再次点击profile控件")
                self.refresh()
            else:
                break

    def click_live_btn(self):
        self["live_btn"].click()
        time.sleep(5)

    def for_you_search(self):
        return self["for_you_search"].wait_for_existing(timeout=5, raise_error=False)

    def click_close_btn(self):
        if self["for_you_search"].wait_for_existing(timeout=5, raise_error=False):
            self["for_you_search"].click()
        time.sleep(2)

    def wait_for_rv_share_panel_avatar(self):
        return self["rv_share_panel_avatar"].wait_for_invisible(timeout=2, raise_error=True)

    def check_share_list_title(self):
        return self["share_list_title"].wait_for_invisible(timeout=2, raise_error=True)

    def wait_for_long_press_panel(self):
        return self["long_press_panel"].wait_for_visible(timeout=5, raise_error=False)

    def click_send_long_press(self):
        if self["long_press_send_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["long_press_send_btn"].click()
            time.sleep(2)
            return ""
        else:
            self["long_press_panel"].swipe(y_direction=1, swipe_coefficient=5)
            self["long_press_send_btn2"].click()
            time.sleep(2)
            return "2"

    def wait_for_share_panel(self):
        return self["share_button_panel"].wait_for_visible(timeout=5, raise_error=False)


    def wait_for_save_photo(self):
        return self["save_photo_btn"].wait_for_visible(timeout=5, raise_error=False)

    def click_send_first_friend(self):
        while not self["share_panel_first_friend"].existing:
            self["share_panel_top_panel"].swipe(x_direction=1)
            time.sleep(2)
        self["share_panel_first_friend"].click()
        self["share_panel_send"].click()

    def create_group_chat(self):
        self["share_panel_second_friend"].click()
        self["share_panel_third_friend"].click()
        self["create_group_chat_checkbox"].click()
        self["share_send_button"].click()

    def click_for_you_search(self):
        # self["for_you_search"].wait_for_existing(timeout=2, interval=0.1, raise_error=False)
        self["for_you_search"].click()

    def check_report_button(self):
        return self["report_btn"].wait_for_existing(timeout=5, raise_error=False)

    def click_report_button(self):
        self["report_btn"].click()

    def check_comment_icon(self):
        return self["comment_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_comment_icon(self):
        time.sleep(5)
        for _ in Retry(limit=3, interval=1, raise_error=False):
            if self["poi_anchor_cancel"].wait_for_existing(timeout=5, raise_error=False):
                self["poi_anchor_cancel"].click()
                time.sleep(2)
            self["comment_icon"].click()
            if self["comment_edit"].wait_for_visible(timeout=3, raise_error=False):
                break
        if self["comment_top_edit"].wait_for_visible(timeout=2, raise_error=False):
            self.app.get_device().press_back()

    def find_follow_icon(self):
        for _ in Retry(timeout=50):
            if self["follow_icon"].wait_for_existing(timeout=5, raise_error=False):
                break
            else:
                self.swipe_up()

    def check_follow_icon(self):
        return self["follow_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_follow_icon(self):
        self["follow_icon"].click()

    def click_back_button(self):
        self["back_button"].wait_for_visible(raise_error=False)
        self["back_button"].click()

    def click_page_back_button(self):
        self["person_page_back_button"].click()

    def return_comment_title(self):
        return self["no_comments"].text

    def click_comment_title(self):
        self["no_comments"].click()

    def check_like_button(self):
        return self["like_icon"].wait_for_existing(timeout=5, raise_error=False)

    def click_like_button(self):
        self["like_icon"].wait_for_visible()
        self["like_icon"].refresh()
        self.app.testcase.log_info(self["like_icon"].rect)
        self["like_icon"].click()

    def check_share_button(self):
        return self["share_iv"].wait_for_existing(timeout=5, raise_error=False)

    def click_share_button(self):
        self["share_iv"].click()

    def close_share_button(self):
        if self["close_share_iv"].wait_for_existing(timeout=5, raise_error=False):
            self["close_share_iv"].click()
        elif self["cancel_share_iv"].wait_for_existing(timeout=5, raise_error=False):
            self["cancel_share_iv"].click()
        else:
            self.app.get_device().press_back()

    def check_more_button(self):
        return self["more_iv"].wait_for_existing(timeout=5, raise_error=False)

    def click_more_button(self):
        self["more_iv"].click()

    def add_debug_video(self, content):
        if self["feed_options"].wait_for_existing(timeout=5, raise_error=False):
            self["feed_options"].click()
            if self["import_video"].wait_for_existing(timeout=3, raise_error=False):
                self["import_video"].click()
            feed_debug_panel = FeedDebugPanel(root=self.app)
            feed_debug_panel.search_video(content)
        else:
            self["add_aweme_id"].click()
            for _ in Retry(timeout=10, raise_error=False):
                if self["input_aweme_id"].wait_for_visible(timeout=2, raise_error=False):
                    break
            self["input_aweme_id"].click()
            self["input_aweme_id"].input(content)
            self.app.testcase.take_screen_shot(self.app, "debug插入视频")
            self["import_videos"].click()
            time.sleep(2)
            self.app.testcase.take_screen_shot(self.app, "debug插入视频")
            self["manage_feed_done"].click()
            time.sleep(2)

    def remove_video_list(self):
        if self['video_list'].wait_for_existing(timeout=5, raise_error=False):
            if self["feed_options"].wait_for_existing(timeout=5, raise_error=False):
                self["feed_options"].click()
                time.sleep(2)
                self["delete_all_videos"].click()
                time.sleep(2)
                self["confirm_delete_btn"].click()

    def feed_debug_function(self, content):
        # if "google" in self.app.app_spec["channel"]:
        #     self.app.testcase.log_record("GooglePlay包无法使用debug工具")
        #     from shoots.exceptions import StopRunningCase
        #     raise StopRunningCase
        self["debug_icon"].wait_for_visible()
        self["debug_icon"].refresh()
        self["debug_icon"].wait_for_ui_stable()
        for _ in Retry(timeout=20, raise_error=False):
            self["debug_icon"].click()
            logger.info("点击debug_icon")
            if self["manage_feed_debug"].wait_for_visible(timeout=3, raise_error=False):
                break
        self["manage_feed_debug"].click()
        logger.info("点击debug_icon")
        for_you_panel = ForYouPanel(root=self.app)
        for_you_panel.remove_video_list()
        for_you_panel.add_debug_video(content)

    def add_video_to_feed_debug(self, content):
        # remove feed
        self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="removeAddedFeed")
        time.sleep(2)
        if isinstance(content, str):
            content = content.split()
        logger.info("content" + str(content))
        # Add content to feed
        result = self.app.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="insertSpecificFeed", args=content)
        time.sleep(5)


    def element_compare_by_image(self, device, original_pic_name):
        import cv2
        path = device.screenshot()
        capture = cv2.imread(path)
        self["for_you_search"].ensure_visible()
        rect1 = self["for_you_search"].rect
        self["feed_nickname"].ensure_visible()
        rect2 = self["feed_nickname"].rect
        target = capture[rect1.top:  rect2.top, :]
        basedir = os.path.abspath('.')
        pic_dir = os.path.join(basedir, device.serial)
        # os.mkdir(pic_dir)
        target_pic_name = os.path.join(pic_dir, 'target-%s.png' % int(time.time()))
        cv2.imwrite(target_pic_name, target)
        from shoots_cv.cv import CV
        cv_ = CV()
        original_pic_dir = os.path.join(os.path.abspath(os.path.join(os.getcwd(), "../../../../../..")), "resources")
        original_pic_name = os.path.join(original_pic_dir, original_pic_name + ".png")
        print(target_pic_name, original_pic_name)
        compare_result = cv_.sim(target_pic_name, original_pic_name, sim_type='ssim')['result']['ssim_score']
        print(compare_result)
        return compare_result > 0.9999

    def check_feed_desc(self):
        return self["desc"].wait_for_existing(timeout=5, raise_error=False)

    def click_desc(self):
        self["desc"].click()

    def return_coordinate(self):
        rect = self["desc"].rect
        x1 = rect.left + rect.width/3
        y1 = rect.top + rect.height/2
        return x1, y1

    def get_full_desc(self):
        if self["see_more"].wait_for_existing(timeout=2, raise_error=False):
            self["see_more"].click()
        return self["desc"].text

    def check_feed_nickname(self):
        return self["feed_nickname"].wait_for_existing(timeout=5, raise_error=False)

    def click_nickname(self):
        self["feed_nickname"].click()

    def click_comment_back_button(self):
        self["comment_back_button"].click()
        self["feed_back_button"].click()

    def return_nickname(self):
        return self["feed_nickname"].text

    def return_feed_comment_icon(self):
        self["desc"].ensure_visible()
        rect = self["desc"].rect
        x1 = x2 = rect.left + rect.width * 1 / 4
        y1 = rect.top + rect.height * 1 / 4
        y2 = rect.top + rect.height * 3 / 4
        return x1, y1, x2, y2

    def search_text_input(self, text):
        self["search_text"].input(text)

    def get_search_account_list(self):
        return self["search_account_list"].items()

    # 获取搜索结果account个数
    def get_search_account(self):
        sum = 0
        if len(self.get_search_account_list()) > 0:
            for i in self.get_search_account_list():
                if i.icon.existing:
                    sum += 1
            return sum

    # 点击第一个account
    def click_first_account(self):
        if len(self.get_search_account_list()) > 0:
            for i in self.get_search_account_list():
                if i.title.existing:
                    return i.click()

    def get_suggested_searches_list(self):
        return self["suggested_searches_list"].items()

    def click_bottom_add(self):
        self["bottom_add"].click()

    def get_sugword_List(self):
        return self["sugword_List"].items()

    # 普通sug
    def click_normal_sug_from_input(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if not i.icon.existing:
                    i.click()
                    return i.nickname

    def click_normal_sug(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if not i.icon.existing:
                    i.click()
                    return

    def click_rich_sug(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if i.icon.existing:
                    i.click()
                    return

    # 计算普通sug词数量
    def normal_sug_count(self):
        sum = 0
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if not i.icon.existing:
                    sum += 1
            return sum

    # 计算富sug词数量
    def rich_sug_count(self):
        sum = 0
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if i.icon.existing:
                    sum += 1
            return sum

    # rich sug
    def click_rich_sug_from_input(self):
        if len(self.get_sugword_List()) > 0:
            for i in self.get_sugword_List():
                if i.icon.existing:
                    i.click()
                    print(i.nickname)
                    return i.nickname

    def click_search_btn(self):
        self["search_btn"].wait_for_visible(timeout=8, interval=0.1)
        self["search_btn"].click()
        time.sleep(3)

    @property
    def search_area_text(self):
        return self["search_text"].text

    @property
    def suggested_options_text(self):
        return self["suggested_options"].text

    def click_suggested_options_text(self):
        suggested_options_text = self["suggested_options"].text
        self["suggested_options"].click()
        return suggested_options_text

    def click_suggested_options(self):
        self["suggested_options"].wait_for_existing(timeout=5, interval=0.1, raise_error=True)
        # suggested_options_text = self["suggested_options"].text
        self["suggested_options"].click()
        # return suggested_options_text

    @property
    def suggested_searches_text(self):
        print(self['suggested_searches'].text)
        return self['suggested_searches'].text

    # 搜索第一个历史词
    def click_first_history_word(self):
        return self["first_history_word"].click()

    # 进入猜你想搜页
    def into_suggested_searches(self):
        self["search_textarea1"].refresh()
        self["search_textarea1"].click()

    def search_something(self, text):
        self["search_textarea"].wait_for_existing(timeout=5, interval=0.1, raise_error=True)
        self["search_textarea"].click()
        self["search_text1"].wait_for_existing(timeout=5, interval=0.1)
        self["search_text1"].input(text)
        self["search_btn"].click()
        time.sleep(5)

    def click_search_box(self):
        if self["search_textarea2"].wait_for_existing(timeout=5, raise_error=False):
            self["search_textarea2"].click()
        else:
            self["homepage_search_btn"].click()

    # 点击find friends
    def click_find_friends_btn(self):
        if self["find_friends"].wait_for_existing(timeout=5, raise_error=False):
            self["find_friends"].click()

    def click_friends_page_search_btn(self):
        return self["friends_page_search_btn"].click()

    def is_search_visible(self):
        if self["search_textarea"].wait_for_existing(timeout=10):
            return True
        else:
            return False

    def click_discover(self):
        self['discover'].click()
        time.sleep(3)

    def click_friends(self):
        self["Friends"].click()
        time.sleep(3)

    def click_friends_tab_friends(self):
        self["Friends_tab"].click()
        time.sleep(3)

    def click_for_you(self):
        if self["for_you"].wait_for_existing(timeout=5, interval=1, raise_error=False):
            self["for_you"].click()
        elif self["for_you1"].wait_for_existing(timeout=5, interval=1, raise_error=True):
            self["for_you1"].click()

    def judge_comments_close(self):
        time.sleep(2)
        return self['comment_close_view'].visible == True

    def judge_region(self, region):
        region_now = self['region_selector_entrance'].text
        if region_now.split(" ")[-2] == region:
            return True
        else:
            return False

    def judge_region_feed(self, region):
        region_now = self['region_selector_entrance'].text
        if region_now.split(" ")[-2] != region:
            return False
        else:
            return True

    def dismiss_region_brazil_popup_advertisement(self):
        # Close the advertisement popup which appears after region change to Brazil
        if self["region_brazil_popup_advertisement_close"].wait_for_existing(timeout=5, interval=1, raise_error=False):
            self["region_brazil_popup_advertisement_close"].click()

    def judge_feeds_or_live(self):
        """ True : Feed
            False : Live
        """
        if self["suggest_friends"].wait_for_existing(timeout=1, interval=0.5, raise_error=False):
            self['close_suggest_friends'].click()
            self.swipe(y_direction=1)
            self.refresh()
        time.sleep(1)
        for item in self['video_list_container'].items():
            if item.visible:
                if item.children[0].elem_info['view_path'].split('/')[-1] == 'riv_cover':
                    return False
                else:
                    self["title"].wait_for_existing(timeout=5, interval=0.5, raise_error=False)
                    return True

    def switch_to_top_region(self):
        self['region_selector_entrance'].click()
        time.sleep(2)
        self['country_only_switch'].click()
        time.sleep(2)
        self["region_selector_list"].items()[1].click()
        time.sleep(2)
        self.app.restart()
        time.sleep(2)

    def wait_for_guide(self):
        self.open_tab("Home")
        return self.iv_guide.wait_for_visible()

    def wait_for_loading(self):
        """
            针对非new window的相关弹窗在此处处理
        """
        if not self["video_list_empty_hint"].wait_for_invisible(timeout=2, raise_error=False):
            self.open_tab("Home")
            time.sleep(1)
        self["loading_view"].wait_for_invisible(timeout=30, raise_error=False)
        if self["iv_guide_test"].wait_for_existing(timeout=5, interval=0.2, raise_error=False):
            for _ in Retry(timeout=100, raise_error=False):
                if not self["iv_guide_test"].wait_for_existing(timeout=2, raise_error=False):
                    return
                self.Home.click()
                self.swipe(y_direction=1, swipe_coefficient=7)
                time.sleep(5)
        if self["iv_guide"].wait_for_existing(timeout=5, interval=0.2, raise_error=False):
            self.open_tab("Home")
            self.swipe(y_direction=1, swipe_coefficient=7)

    def agree(self):
        if self["agree_btn"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
            self["agree_btn"].click()
            return True
        return False

    def open_tab(self, tab_name):
        """
        :param tab_name: target tab name
        :return:
        """
        pop = CommonPopup(root=self.app)
        closePnsUniversalPopup = ClosePnsUniversalPopup(root=self.app)
        closePnsUniversalPopup.handle()
        if pop["agree_and_continue"].wait_for_existing(timeout=3, raise_error=False):
            pop["agree_and_continue"].click()
        if pop["close_login_or_sign"].wait_for_existing(timeout=3, raise_error=False):
            pop["close_login_or_sign"].click()
        if self["iv_guide"].existing:
            self.swipe(y_direction=1)
        if tab_name not in self.tab_map.keys():
            raise Exception("tab name err")
        time.sleep(2)
        from business.ttlh.utils.main import ForYouPanel
        feed_panel = ForYouPanel(root=self.app)
        if self.app.wait_for_activity(feed_panel.activity, timeout=5, raise_error=False):
            self.swipe(y_direction=1)
        # self[tab_name].wait_for_visible(timeout=30)
        # self[tab_name].refresh()
        try:
            self[tab_name].click()
        except Exception as e:
            logger.info(f"click {tab_name} fail, fail reason{e}")
            self.click_bottom_tab(tab_name)
        if tab_name == "Home":
            time.sleep(3)
            self.swipe(y_direction=1)
        elif tab_name == "Me":
            from business.ttlh.utils.main import SignUpPanel, LoginPanel
            mine_panel = MinePanel(root=self.app)
            signup_panel = SignUpPanel(root=self.app)
            login_panel = LoginPanel(root=self.app)
            time.sleep(2)
            for _ in Retry(timeout=30, interval=1, raise_error=False):
                if self.app.current_activity not in mine_panel.activity or not mine_panel.judge_mine_page():
                    if self.app.wait_for_activity(login_panel.activity, timeout=2, raise_error=False):
                        self.app.back()
                    if self.app.wait_for_activity(signup_panel.activity, timeout=2, raise_error=False):
                        signup_panel.left_top_icon()
                        time.sleep(2)
                    if self["iv_guide"].existing:
                        self.swipe(y_direction=1, swipe_coefficient=7)
                    self["Me"].click()
                    self.app.testcase.log_info("点击profile控件")
                else:
                    break
        elif tab_name == "Inbox":
            pop = CommonPopup(root=self.app)
            if pop["close_activity_start"].wait_for_existing(timeout=5, raise_error=False):
                pop["close_activity_start"].click()
            elif pop["activity_status_save"].existing:
                pop["activity_status_save"].click()

    def open_debug_panel(self):
        """
        循环长按profile控件，进入debug界面失败后，通过device长按
        """
        if "google" in self.app.app_spec["channel"]:
            self.app.testcase.log_record("GooglePlay包无法使用debug工具")
            from shoots.exceptions import StopRunningCase
            raise StopRunningCase
        self["me_tab"].wait_for_visible(timeout=60)
        logger.info("profile控件存在")
        for _ in Retry(limit=5, raise_error=False):
            if self["me_tab"].wait_for_invisible(timeout=2, raise_error=False):
                logger.info("profile控件已消失")
                break
            else:
                if self["feed_report"].wait_for_visible(timeout=1, raise_error=False):
                    self.app.get_device().press_back()
                time.sleep(3)
                self["me_tab"].refresh()
                self["me_tab"].wait_for_ui_stable()
                logger.info("长按“profile”控件5秒进入debug界面")
                self["me_tab"].long_click(duration=5)
        else:
            if self["me_tab"].wait_for_visible(timeout=3, raise_error=False):
                me_tab_rect = self["me_tab"].rect.center
                self.app.get_device().long_click(me_tab_rect[0], me_tab_rect[1], duration=5)
                logger.info("通过device长按profile控件5秒进入debug界面")

    def click_me_tab(self):
        self["me_tab"].wait_for_visible(timeout=10, raise_error=False)
        self["me_tab"].click()

    def click_nav_bar_title(self):
        self["nav_bar_title"].click()

    def check_nav_bar_title(self, content):
        if self["nav_bar_title"].text == content:
            return True
        return False

    def click_add_account(self):
        self["add_account"].click()

    @property
    def video_profile_name(self):
        # if self.wait_for_loading:
        #     self.foryou.wait_for_loading()
        #     self.login()
        while not self["title"].wait_for_existing(timeout=5, raise_error=False):
            self.swipe(y_direction=1, swipe_coefficient=5)
        self["title"].refresh()
        time.sleep(1)
        return self["title"].text

    @property
    def video_profile_name_down(self):
        # if self.wait_for_loading:
        #     self.foryou.wait_for_loading()
        #     self.login()
        while not self["title"].wait_for_existing(timeout=5, raise_error=False):
            self.swipe(y_direction=-1, swipe_coefficient=5)
        self["title"].refresh()
        time.sleep(1)
        return self["title"].text

    @property
    def video_desc(self):
        return self["desc"].text

    def swipe_up(self, ratio=6):
        """上滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y * 2
        self["content"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)

    def swipe_up_comment(self, ratio=4):
        """上滑
        """
        rect = self["no_comment_panel"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] - offset_y
        self["no_comment_panel"].drag(to_x + 15, to_y, -15, +offset_y)
        time.sleep(1)

    def swipe_down(self, ratio=4):
        """下滑
        """
        rect = self["content"].rect
        offset_y = rect.height / ratio
        to_x = rect.center[0]
        to_y = rect.center[1] + offset_y
        self["content"].drag(to_x - 15, to_y, 15, -offset_y)
        time.sleep(1)

    def pause(self):
        # if self["not_allow"].wait_for_existing(timeout=5, interval=1, raise_error=False):
        #     self["not_allow"].click()
        self['content'].click()
        time.sleep(3)

    def swipe_video(self):
        for _ in Retry(limit=3, interval=1, raise_error=False):
            if self["content"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                self.swipe_up()
            time.sleep(3)

    def dismiss_contact_sync_popup(self):
        if self["not_allow"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["not_allow"].click()

    def play(self):
        self['play_icon'].click()
        time.sleep(3)

    def send_to_user(self, user):
        while not self["more_btn"].wait_for_existing(timeout=2, raise_error=False):
            self["share_panel_top_panel"].swipe(x_direction=1, swipe_coefficient=8)
        self["more_btn"].click()
        self["search_bar"].click()
        self["search_bar"].input(user)
        self["select_first_user"].click()
        self["send_btn"].click()

    def enter_user_profile(self):
        """进入用户主页
        """
        while self["user_avatar_live"].existing:
            self.swipe_up()
            time.sleep(1)
        if self['user_avatar'].wait_for_visible(timeout=60, raise_error=False):
            self['user_avatar'].click()
        elif self['user_avatar1'].wait_for_visible(timeout=60, raise_error=False):
            self["user_avatar1"].click()
        for _ in Retry(timeout=30, interval=1, raise_error=False):
            if self["hide_feed_debug"].wait_for_visible(timeout=1, raise_error=False):
                self["hide_feed_debug"].click()
                self['user_avatar'].refresh()
                self['user_avatar'].click()
            if self['user_avatar'].wait_for_invisible(timeout=4, raise_error=False):
                break
            else:
                self['user_avatar'].click()

    def enter_user_profile_v2(self):
        if self["feed_debug_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_debug_icon"].click()
            if self["hide_this_tool"].wait_for_existing(timeout=3, raise_error=False):
                self["hide_this_tool"].click()
        time.sleep(3)
        return self['user_avatar'].click()

    def wait_for_feed(self):
        return self["video_feed"].existing

    def wait_for_pause(self):
        self.play_icon.wait_for_visible()

    def check_pause_status(self):
        return self["play_icon"].wait_for_existing(timeout=3, raise_error=False)

    def wait_for_play(self):
        self.play_icon.wait_for_disappear()

    def click_music_tag(self):
        self["music_tag"].click()
        time.sleep(2)

    def find_caption_video(self):
        self.swipe_up()
        self["content"].swipe(y_direction=-1, swipe_coefficient=8)

    def check_music_tag(self):
        return self["music_tag"].wait_for_existing(timeout=3, raise_error=False)

    def find_music_tag(self):
        for _ in Retry(limit=3, raise_error=False):
            if self["music_tag"].wait_for_existing(timeout=3, raise_error=False):
                break
            self.swipe_up()
            time.sleep(2)


    def get_music_name(self):
        return self["music_desp"].text

    def find_hashtag(self):
        """查找hashtag
        """
        for _ in Retry(timeout=20):
            if self['desc'].existing and '#' in self['desc'].text:
                return self['desc']
            self.swipe_up()

    def find_sticker_prop(self):
        """查找道具icon
        """
        for _ in Retry(timeout=150, interval=1, raise_error=False):
            if self['iv_head_3'].existing:
                return self['iv_head_3']
            self.swipe_up()
            # self['iv_head_3'].refresh()

    # def swipe_up_for_more_comments(self):
    #     """查找评论数没有上万（Feed页面视频评论数较多）
    #     """
    #     while self["comment_count"].text.endswith("K"):
    #         self.swipe_up()
    #         self["comment_count"].refresh()

    def click_comment(self):
        time.sleep(5)
        if self["comment_view_layout"].wait_for_existing(timeout=3, raise_error=False):
            self["comment_view_layout"].refresh()
            self["comment_view_layout"].click()
        return self["comment_list_panel"].wait_for_visible(timeout=15)

    def wait_for_comment_panel(self):
        return self['comment_panel'].wait_for_visible(raise_error=False)

    def share_first_comment(self):
        self["first_comment"].long_click()
        self["comment_select_dialog"].wait_for_visible(raise_error=False)
        self["send_to_friends"].click()

    def click_comment_top_words(self):
        if self["comment_top_words"].wait_for_existing(timeout=5, raise_error=False):
            self["comment_top_words"].click()

    def find_highlighted_word_and_click(self):
        for _ in Retry(timeout=100, interval=1):
            if self["highlighted_word"].wait_for_visible(timeout=5, raise_error=False):
                self["highlighted_word"].click(offset_y=-50)
                return
            self.swipe_up_comment()

    def comments(self, full=False, index=0):
        """所有评论
        """
        time.sleep(1)
        items = self["comment_list"].items()
        if full:
            return items if items else None
        else:
            return items[index] if items else None

    def at_person(self, name):
        """at某人
        """
        self["comment_edit"].refresh()
        if name:
            self["comment_edit"].input("@" + name)
            self["at_search_loading"].wait_for_invisible(timeout=30)
            self["at_list"].items()[0].click()
        time.sleep(1)

    def comment(self, content):
        """评论
        :param content 内容
        """
        time.sleep(1)
        self["comment_edit"].refresh()
        self["comment_edit"].click()
        time.sleep(1)
        self["comment_edit_input"].input(content)
        time.sleep(2)
        if self["comment_publish"].wait_for_existing(timeout=3, raise_error=False):
            time.sleep(2)
            self["comment_publish"].click()
        if self["comment_send"].wait_for_existing(timeout=3, raise_error=False):
            time.sleep(2)
            self["comment_send"].click()
            time.sleep(2)

    def delete_comment(self, comment):
        comment.long_click(duration=2)
        time.sleep(1)
        self["delete"].click()

    def delete_first_comment(self):
        self["comment_manage_btn_top"].click()
        time.sleep(1)
        self["comment_v0"].click()
        self["comment_delete_btn_top"].click()
        self["delete_video_list"].click()
        self["comment_done_btn_top"].click()

    def find_normal_video_in_following(self):
        """
        在following页找到以一个有评论的正常视频
        """
        for _ in Retry(timeout=40, interval=1):
            if self["title"].wait_for_visible(timeout=2, raise_error=False) and self["digg_count"].text != "0":
                break
            else:
                self.scroll(coefficient_y=0.7)

    def follow(self):
        """查找一条未关注的视频再关注
        """
        for _ in Retry(timeout=20):
            time.sleep(5)
            if self['follow_btn'].existing:
                self['follow_btn'].refresh()
                self['follow_btn'].click()
                break
            self.swipe_up()

    def follow_if_available(self):
        if self['follow_btn'].existing:
            self['follow_btn'].click()
        time.sleep(2)

    def wait_for_follow_succeed(self):
        self.swipe_up()
        self.swipe_down()
        return self["follow_btn"].wait_for_not_existing()

    def enter_following(self):
        self["following"].click()

    def digg(self, ignore_digg_count=True):
        """双击视频页面点赞
        :param ignore_digg_count:
            当点赞数上千或上百万，无法通过点赞数判断是否点赞成功。当为False时，找到点赞数较少的视频，方便判断
        :return 点赞前点赞数，点赞后点赞数
        """
        if not ignore_digg_count:
            while self["digg_count"].text.endswith("K") or self["digg_count"].text.endswith("M"):
                self.swipe_up()
                for _ in Retry(timeout=5, raise_error=False):
                    if self["digg_count"].wait_for_visible(timeout=1, raise_error=False):
                        break
                    else:
                        self.swipe_up()
                self["digg_count"].refresh()
                time.sleep(1)
        self["digg_count"].refresh()
        old_digg_count = self["digg_count"].text
        self["content"].double_click()
        time.sleep(5)
        self["digg_count"].refresh()
        return old_digg_count, self["digg_count"].text

    def get_digg_count_with_refresh(self):
        self["digg_count"].refresh()
        return self["digg_count"].text

    def find_share_icon(self):
        for _ in Retry(limit=3, interval=1, raise_error=False):
            if self["share_iv"].wait_for_existing(timeout=5, raise_error=False):
                break
            else:
                self.swipe_up()

    def share(self):
        self['share_iv'].refresh()
        if self['share_iv'].wait_for_visible(timeout=3, raise_error=False):
            self['share_iv'].click()
        else:
            contrl = Control(root=self, path=UPath(id_ == "comment_layout", visible_ == True))
            rect = contrl.rect
            self.app.testcase.device.click(rect.center[0], rect.bottom+50)

    def click_share_repost(self):
        self["share_repost"].click()

    def judge_share_repost(self):
        return self["share_repost"].visible

    def click_upvote_entrance_img(self):
        self["upvote_entrance_img"].click()

    def judge_upvote_entrance_img(self):
        return self["upvote_entrance_img"].visible

    def get_share_action(self, action_name, index_flag=False):
        """返回一个指定action name的控件
        """
        self["action_list"].refresh()
        # save_button = Control(root=self, path=UPath(text_ == "Save video"))
        # if save_button.wait_for_visible(timeout=3, raise_error=False):
        #     return save_button
        for _ in Retry(limit=3, interval=1, raise_error=False):
            action_list = self["action_list"].items()
            if index_flag:
                for index, action in enumerate(action_list):
                    if action_name in action.action_name:
                        return action, index
            else:
                for action in action_list:
                    if action_name in action.action_name:
                        return action
            if self["share_panel"].wait_for_visible(timeout=3, raise_error=False):
                self["share_panel"].swipe(x_direction=1, swipe_coefficient=5)
            elif self["action_list"].existing:
                self["action_list"].swipe(x_direction=1, swipe_coefficient=5)
        return None

    def report(self):
        self.share()
        report_btn = self.get_share_action("Report")
        report_btn.click()

    def check_report_btn(self):
        return self["report_btn"].wait_for_existing(timeout=5, raise_error=False)

    def click_report_btn(self):
        if self["report_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["report_btn"].click()

    def add_to_favorites(self):
        self.share()
        favorite_action = self.get_share_action("Add to Favorites")
        favorite_action.click()

    def find_downloadable_video(self):
        self.share()
        download_action = self.get_share_action("Save video")
        while download_action is None:
            self._device_driver.send_key(4)
            self.swipe_up()
            time.sleep(1)
            self.share()
            download_action = self.get_share_action("Save video")
        return download_action

    def find_privacy_setting(self):
        self.share()
        privacy_action = self.get_share_action("Privacy settings")
        privacy_action.click()

    def wait_for_download_finish(self):
        if self["download_progress_bar"].wait_for_existing(timeout=10, raise_error=False):
            return self["download_success"].wait_for_visible(timeout=120, raise_error=False)
        else:
            return self["share_page"].wait_for_existing(timeout=20, raise_error=False)

    def download_video(self):
        self.find_downloadable_video()
        self.wait_for_download_finish()

    def delete_video(self, back=True):
        if self["more_iv"].wait_for_visible(timeout=2, raise_error=False):
            self["more_iv"].click()
            self["delete_video_btn"].click()
        else:
            self.share()
            delete_btn = self.get_share_action("Delete")
            delete_btn.click()
        self["delete_btn"].click()
        if back:
            self.app.get_device().press_back()

    def duet(self):
        from business.ttlh.utils.main import VideoRecordPanel
        while True:
            self.share()
            duet = self.get_share_action("Duet")
            while duet is None:
                self._device_driver.send_key(4)
                self.swipe_up()
                time.sleep(1)
                self.share()
                duet = self.get_share_action("Duet")
            duet.click()
            if self["process_loading"].wait_for_existing(timeout=2, raise_error=False):
                self["process_loading"].wait_for_invisible(timeout=60, raise_error=False)
            if self.app.wait_for_activity(VideoRecordPanel.window_spec["activity"], timeout=3, raise_error=False):
                return
            self._device_driver.send_key(4)
            self.swipe_up()
            time.sleep(1)

    def back_to_following(self):
        for i in Retry(limit=7):
            if self["share_iv"].wait_for_visible(timeout=2, interval=0.1, raise_error=False):
                self.app.get_device().press_back()
                break
            self.app.get_device().press_back()

    def paste_address(self):
        self["comment_edit"].wait_for_visible()
        self["comment_edit"].click()
        time.sleep(2)
        self.wait_for_ui_stable()
        if self["comment_top_edit"].wait_for_visible(timeout=4, raise_error=False):
            self["comment_top_edit"].long_click()
        elif self["comment_edit"].wait_for_visible(timeout=4, raise_error=False):
            self["comment_edit"].long_click()
        pop_panel = OtherWindow(root=self.app)
        if pop_panel["paste_btn"].wait_for_visible(timeout=5, raise_error=False):
            paste = pop_panel["paste_btn"]
        else:
            paste = pop_panel["paste"].wait_for_visible()
        for _ in Retry(limit=3, interval=1):
            if paste.existing:
                paste.click()
            else:
                break

    def add_comment_num(self, text):
        self["comment_edit_input"].wait_for_visible()
        self["comment_edit_input"].input(text)
        time.sleep(2)
        self["comment_send_btn"].click()
        time.sleep(2)
        self["comment_back_btn"].click()
        time.sleep(2)


    def comment_back(self):
        for _ in Retry(limit=3, interval=1):
            if self["new_comments_tap_edit"].wait_for_visible(timeout=2, raise_error=False):
                self.app.get_device().press_back()
            else:
                break

    def show_privacy_video(self):
        self["privacy_tab"].wait_for_visible()
        for _ in Retry(limit=6, interval=5):
            self["privacy_tab"].click()
            if self["first_video"].wait_for_visible(timeout=15, raise_error=False):
                break
            else:
                self["person_video"].click()

    def like_tab_click(self):
        self["like_tab"].click()
        if self["collect_tab_layout"].wait_for_visible(timeout=2, raise_error=False):
            like_tab = self["collect_tab"]
        else:
            like_tab = self["like_tab"]
        like_tab.click()
        for _ in Retry(timeout=120, interval=2):

            if self["first_video"].wait_for_visible(timeout=15, raise_error=False):
                break
            else:
                self["person_video"].click()
            like_tab.click()

    def ocr_click(self, device, text):
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)
        time.sleep(2)

    def ocr_click_grant_permission(self, device):
        cv_ = CV()
        pic_path = device.screenshot()
        device_os = int(device.os_version)
        if device_os >= 6 and device_os < 10:
            text = "始终允许"
        if device_os >= 10 and device_os < 11:
            text = "允许"
        if device_os >= 11:
            text = "仅限这一次"
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)

    def ocr_click_text(self, device, text, index=None):
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        if isinstance(index, int):
            x = result_pos["result"][index]["center_pos"][0]
            y = result_pos["result"][index]["center_pos"][1]
        else:
            x = result_pos["result"][1]["center_pos"][0]
            y = result_pos["result"][1]["center_pos"][1]
        device.click(x, y)

    def ocr_check(self, device, text):
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        if result_pos["msg"] == "Failed":
            return False
        else:
            return True

    def click_follow_btn_under_video(self):
        self["follow_btn_under_video"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["follow_btn_under_video"].click()

    def enter_live(self):
        if self.has_live_entrance():
            self['live'].click()

    def has_live_entrance(self):
        return self['live'].wait_for_existing(timeout=10, raise_error=False)

    def swipe_into_profile(self):
        self["content"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(2)

    def swipe_left_to_first_story(self):
        while not self.check_right_swipe_success():
            self["content"].swipe(x_direction=-1, swipe_coefficient=5)
        self.for_you_btn_click()

    def swipe_left_story(self, count):
        for i in range(count):
            self["content"].swipe(x_direction=-1, swipe_coefficient=5)
        time.sleep(1)

    def swipe_next_story(self, count):
        for i in range(count):
            self["content"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(1)

    def check_story_labels(self, count):
        for i in range(count):
            self["content"].swipe(x_direction=1, swipe_coefficient=5)
            if not self["story_tag"].existing:
                return False
        return True

    def swipe_next_feed(self):
        self.swipe(y_direction=1, swipe_coefficient=8)

    def swipe_previous_feed(self):
        self.swipe(y_direction=-1, swipe_coefficient=8)

    def swipe_right(self):
        """右滑
        """
        main_panel = ForYouPanel(root=self.app)
        rect = self["content"].rect
        x1 = rect.left + rect.width // 2
        x2 = rect.left + rect.width // 2 * 3
        y1 = y2 = rect.height // 2
        self.app._driver.drag(main_panel.id, x1, y1, x2, y2)
        time.sleep(1)
        # main_panel.wait_for_visible()
        self["loading_view"].wait_for_invisible(timeout=10, raise_error=False)

    def find_video_out_follow(self):
        while self['follow_btn1'].wait_for_invisible(timeout=5, raise_error=False):
            logger.info("Follow button is visible: " + str(self['follow_btn1'].wait_for_invisible(timeout=5, raise_error=False)))
            self.swipe_up()

    def swipe_left(self):
        """左滑
        """
        ratio = 2
        if (self['super_recommend_following'].wait_for_existing(timeout=5, raise_error=False)):
            ratio = 6
        main_panel = ForYouPanel(root=self.app)
        rect = self["content"].rect
        x1 = rect.left + rect.width // 2
        y1 = y2 = rect.height // ratio
        x2 = rect.left - rect.width // 2 * 3
        self.app._driver.drag(main_panel.id, x1, y1, x2, y2)

    def wait_for_following(self):
        return self['following'].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_for_you(self):
        if self['for_you'].wait_for_visible(timeout=3, raise_error=False):
            return self['for_you'].wait_for_visible(timeout=3, raise_error=True)
        elif self['for_you1'].wait_for_visible(timeout=3, raise_error=True):
            return self['for_you1'].wait_for_visible(timeout=3, raise_error=True)

    def following_btn_click(self):
        if self['following_btn'].wait_for_visible(timeout=3, raise_error=False):
            self["following_btn"].click()
            time.sleep(2)

    def CheckFollowingFeedTab(self):
        return self['following_btn'].wait_for_visible(timeout=5, raise_error=False)

    def for_you_btn_click(self):
        if self['for_you_btn'].wait_for_visible(timeout=3, raise_error=False):
            self["for_you_btn"].click()
            time.sleep(2)

    def check_right_swipe_success(self):
        return self['follow_btn1'].wait_for_invisible(timeout=3, raise_error=False)

    def click_search_button(self):
        self["search_button"].click()
        time.sleep(3)

    # 检查following feed中是否有直播天窗
    def check_live_head(self):
        if self["live_head"].wait_for_existing(timeout=5, raise_error=False):
            return self["live_head"].wait_for_existing(timeout=5, raise_error=False)
        else:
            self["live_head1"].wait_for_existing(timeout=5, raise_error=False)

    # 检查debug工具是否存在
    def check_feed_debug_btn(self):
        return self["feed_debug_btn"].wait_for_visible(timeout=5, raise_error=False)

    # 关闭debug工具
    def click_feed_debug_btn(self):
        self["feed_debug_btn"].click()
        time.sleep(3)
        self["hide_feed_debug"].click()
        time.sleep(1)

    def multiple_restart(self):
        for _ in range(2):
            self.app.restart()
            time.sleep(3)
        time.sleep(3)

    def judge_reposted_avatar(self):
        return self["reposted_avatar"].visible

    def judge_repost_guidance(self):
        return self["repost_guidance"].visible

    def confirm_repost_guidance(self):
        if self["confirm_repost_guidance"].wait_for_visible(timeout=5, raise_error=False):
            self["confirm_repost_guidance"].click()

    def judge_you_reposted(self):
        self["tv_upvote_you_reposted"].wait_for_visible(raise_error=False)
        return self["tv_upvote_you_reposted"].visible and ("You reposted*" in self["tv_upvote_you_reposted"].text)

    def judge_reposted_add_comment(self):
        self["tv_upvote_add_comment"].wait_for_visible(timeout=10, raise_error=False)
        return self["tv_upvote_add_comment"].visible

    def judge_three_reposted(self):
        self["tv_upvote_three_reposted"].wait_for_visible(timeout=10, raise_error=False)

        return (self["reposted_avatar"].wait_for_visible(raise_error=False)
                and self["reposted_avatar_middle"].wait_for_visible(raise_error=False)
                and self["reposted_avatar_end"].wait_for_visible(raise_error=False)
                and "3 reposted*" in self["tv_upvote_three_reposted"].text)

    def is_repost_exist(self, times: int):
        # if not self["comment_add_comment_button"].wait_for_visible(timeout=1, interval=0.2, raise_error=False):
        if times > 1:
            time.sleep(1)
            print("click the comment_edit_new_bottom")
            comment_edit_new_bottom = Control(root=self, path=UPath(id_ == "comment_edit_new"))
            comment_edit_new_bottom.wait_for_visible(timeout=5, interval=0.1, raise_error=False)
            comment_edit_new_bottom.click()

            # if not comment_edit_new_bottom.click():
            #     print("click the comment_edit_new_bottom first retry")
            #     time.sleep(1)
            #     comment_edit_new_bottom.click()

            # time.sleep(10)
            comment_edit_new_up = Control(root=self, path=UPath(id_ == "comment_edit_new"))
            comment_edit_new_up.click()
        self["check_box_upvote"].wait_for_visible(timeout=10, interval=0.1, raise_error=True)

    def test_add_comment(self, str_comment: str = "Nice video.", times: int = 1):
        # str_comment = "Nice video, keep Coming.!!"
        # if not self["comment_add_comment_button"].wait_for_visible(timeout=1, interval=0.2, raise_error=False):

        if times > 1:
            # self["comment_edit_new_bottom"].wait_for_visible(timeout=2, interval=0.2, raise_error=False)
            comment_edit_new_up = Control(root=self, path=UPath(id_ == "comment_edit_new"))
            comment_edit_new_up.wait_for_visible(timeout=5, interval=0.1, raise_error=False)
            comment_edit_new_up.text = str_comment
        else:
            self["comment_edit_new_up"].text = str_comment

        # if times == 4:
        #     import pdb;
        #     pdb.set_trace()
        self["check_box_upvote"].click()
        self["comment_send_new"].wait_for_visible(timeout=15, interval=0.5, raise_error=True)
        self["comment_send_new"].click()
        new_comment = Control(root=self, path=UPath(text_ == str_comment))
        new_comment.wait_for_visible(timeout=15, interval=0.5, raise_error=True)
        self["comment_back_btn"].click()

    def add_comment(self, content):
        self["input_comment"].input(content)

    def send_multiple_friend(self, multiple_friend, create_group=False, long_press_flag=False, search_flag=False, more_flag=True, btn_flag=False, undo_flag=False):
        """
        Send to multiple friends in share panel
        :param create_group:Create a group chat. Default is False
        :type: bool
        :param multiple_friend:select a few friends. Default is two,eg["a", "b"]
        :type: list
        :param long_press_flag:default is False
        :type:bool
        """
        result = self.select_user_by_share_panel(multiple_friend, long_press_flag, search_flag, more_flag, btn_flag, undo_flag)
        if long_press_flag and not search_flag:
            return result
        if create_group:
            if self["create_group_chat_checkbox"].wait_for_visible(timeout=3, raise_error=False):
                self["create_group_chat_checkbox"].click()
            elif self["create_group_button"].existing:
                self["create_group_button"].click()
        self["send_btn"].wait_for_existing(timeout=3)
        self["send_btn"].click()

    def select_user_by_share_panel(self, user_list, long_press_flag, search_flag=False, more_flag=True, btn_flag=False, undo_flag=False):
        """
        :param user_list:["a","b"]
        :type:list
        :param long_press_flag:long press panel case flag
        :type:bool
        :param search_flag:enter way, search_flag is True from bottom more friends enter
        :type:bool
        :param more_flag:Level list Find friends and enter
        :type:bool
        """
        device = self.app.get_device()
        if long_press_flag:
            # friend list invisible control
            time.sleep(1)
            self["long_press_panel"].swipe(y_direction=1, swipe_coefficient=2.5)
            self.app.testcase.take_screenshot(self.app.testcase.device, "swipe_share_panel.jpg")
        flag = False
        for _ in Retry(limit=30, interval=1):
            if self["share_panel_top_panel"].wait_for_visible(timeout=3, raise_error=False):
                if long_press_flag:
                    if search_flag:
                        self["long_press_panel"].swipe(y_direction=1, swipe_coefficient=5)
                    else:
                        for item in self["share_panel_top_panel"].children:
                            if not item.visible:
                                self["share_panel_top_panel"].swipe(y_direction=1, swipe_coefficient=2.3)
                                time.sleep(1)
                                self["share_panel_top_panel"].refresh()
                                time.sleep(1)
                            if "TextView" in item.children[2].type:
                                friend_name = item.children[2].text
                            else:
                                continue
                            logger.info(f"friend name:{friend_name}")
                            if friend_name == user_list[0]:
                                x, y = item.rect.center[0], item.rect.center[1]
                                logger.info(f"current item point:{(x, y)}")
                                self.app.testcase.device.click(x, y)
                                flag = True
                                if btn_flag:
                                    time.sleep(3)
                                    btn_text = item.children[4].text
                                if undo_flag:
                                    btn_text = item.children[4].text
                                    if "Undo" in btn_text:
                                        item.children[4].click()
                                break
                        if flag:
                            break
                        continue
                else:
                    if more_flag:
                        self["share_panel_top_panel"].swipe(x_direction=1, swipe_coefficient=8)
                    else:
                        for item in self["share_panel_top_panel"].children:
                            if "TextView" in item.children[1].type:
                                friend_name = item.children[1].text
                            else:
                                continue
                            logger.info(f"friend name:{friend_name}")
                            if friend_name == user_list[0]:
                                item.refresh()
                                if not item.visible:
                                    self["share_panel_top_panel"].swipe(x_direction=1, swipe_coefficient=2.1)
                                item.click()
                                flag = True
                                break
                            self["share_panel_top_panel"].swipe(x_direction=1, swipe_coefficient=2.1)
                        if flag:
                            break
                self["share_panel_top_panel"].wait_for_ui_stable()

            if self["more_btn"].wait_for_visible(timeout=2, raise_error=False):
                self["more_btn"].click()
                break
            elif self["more_friends"].wait_for_visible(timeout=1, raise_error=False):
                self["share_panel_top_panel"].swipe(y_direction=1, swipe_coefficient=3)
                self["more_friends"].click()
                break
        if long_press_flag and not search_flag:
            device.back()
            return btn_text if btn_flag else True
        if not more_flag:
            return
        self["search_bar"].wait_for_ui_stable()
        self["search_bar"].click()
        for user in user_list:
            if self["clear_btn"].wait_for_visible(timeout=3, raise_error=False):
                self["clear_btn"].click()
                self["search_bar"].wait_for_ui_stable()
            self["search_bar"].input(user)
            if len(self["select_user"].children) > 3:
                if not self["send_btn"].visible:
                    self.app.testcase.device.back()
                else:
                    self["select_user"].swipe(y_direction=1, swipe_coefficient=5)
                    self["select_user"].refresh()
                    self["select_user"].wait_for_ui_stable()
            for user_info in self["select_user"].children:
                if len(user_info.children) > 2:
                    # logger.info(f"search branch friend name:{user_info.children[2].children[2].text}")
                    if user_info.children[2].children[2].text == user:
                        user_info.click()
                        break
            else:
                self.ocr_click_text(device, "Friends", index=0)

    def click_bottom_tab(self, tab_name, long_flag=False):
        """
        click tab retry
        """
        rect = self.app.testcase.device.screen_rect

        current_acitvity = self.app.current_activity
        if "SplashActivity" not in current_acitvity:
            self.go_home()

        if self['tabs'].wait_for_existing(timeout=3, raise_error=False):
            tabs_rect = self["tabs"].rect
            y = tabs_rect.top + tabs_rect.height/2
        else:
            if self["home_root"].existing:
                y = self["home_root"].rect.height - 70
            else:
                y = rect.height - 100

        tab_wid = rect.width / 5
        x = rect.width / 2
        if tab_name == "Home":
            x = rect.left + tab_wid/2
        elif tab_name == "Inbox":
            x = tab_wid*4-tab_wid/2
        elif tab_name in ["Me", "Profile"]:
            x = rect.width-tab_wid/2
        logger.info(f"{tab_name} point:{x},{y}")
        time.sleep(1)
        if long_flag:
            self.app.testcase.device.long_click(x, y)
        else:
            self.app.testcase.device.click(x, y)

    def get_user_nick(self):
        if self["inner_nick"].wait_for_existing(timeout=5, raise_error=False):
            self["content"].refresh()
            self["content"].wait_for_ui_stable()
            nick = self["inner_nick"].text
        else:
            video_detail_panel = FeedDebugDetailPanel(root=self.app)
            nick = video_detail_panel.get_video_detail_nick()
        logger.info(f"nick name:{nick}")
        return nick

    def video_is_ad_control(self):
        """
        当前视频是否是广告视频判断【广告视频无分享成功toast】
        """
        try:
            for _ in Retry(limit=6, interval=1, raise_error=False):
                if self["sponsored"].wait_for_visible(timeout=5, raise_error=False):
                    self["content"].swipe(y_direction=1, swipe_coefficient=5)
                    continue
                if self["ad_bottom_btn"].wait_for_visible(timeout=1, raise_error=False):
                    self["content"].swipe(y_direction=1, swipe_coefficient=5)
                    continue
                elif self["ad_bottom_btn2"].wait_for_visible(timeout=1, raise_error=False):
                    self["content"].swipe(y_direction=1, swipe_coefficient=5)
                    continue
                elif self["live_video"].wait_for_visible(timeout=1, raise_error=False):
                    self["content"].swipe(y_direction=1, swipe_coefficient=5)
                    continue
                else:
                    logger.info("normal video")
                    break
            time.sleep(1)
        except BaseException as e:
            logger.info(e)

    def find_target_msg(self, msg_type):
        find_flag = False
        if "live_event" in msg_type:
            self.following.click()
        for _ in Retry(limit=8, interval=1, raise_error=False):
            for _ in Retry(limit=15, interval=1, raise_error=False):
                if "poi" in msg_type:
                    if self["feed_poi_icon"].wait_for_visible(timeout=3, raise_error=False):
                        self["feed_poi_icon"].click()
                        find_flag = True
                        break
                elif "effect" in msg_type:
                    if self["anchor_tag_icon"].wait_for_visible(timeout=3, raise_error=False):
                        find_flag = True
                        break
                elif "comment" in msg_type:
                    if int(self["comment_num"].text) > 0:
                        self["comment_icon"].click()
                        comment_list = Control(root=self, path=UPath(id_ == "recyclerView", visible_ == True))
                        x, y = comment_list.rect.center[0], comment_list.rect.top+20
                        self.app.get_device().long_click(x, y, duration=3)
                        find_flag = True
                        break
                elif "hashtag" in msg_type:
                    if self["desc"].wait_for_visible(timeout=3, raise_error=False):
                        text = self["desc"].text
                        logger.info(f"current desc:{text}")
                        if "#" in text:
                            start = text.find("#")
                            self["desc"].click_text_span(start=start, end=start+3)
                            if self["desc"].wait_for_visible(timeout=3, raise_error=False):
                                self.click_text_rect()
                            find_flag = True
                            break
                elif "live_event" in msg_type:
                    time.sleep(5)
                    if self.effect_title.wait_for_visible(timeout=3, raise_error=False):
                        if "LIVE Event" in self.effect_title.text:
                            self.effect_title.click()
                            time.sleep(8)
                            cv_tool = CV(self.app.get_device())
                            res = cv_tool.universal_ui_detection(self.app.get_device().screenshot())
                            logger.info(f"{res}")
                            share_position = res.get("result").get("Share", None)
                            if isinstance(share_position, list):
                                share_position = share_position[0]
                                x = (share_position[0]+share_position[2])/2
                                y = (share_position[1]+share_position[3])/2
                                self.app.testcase.device.click(x, y)
                                find_flag = True
                                break
                            else:
                                logger.error("landing page load failed")
                elif "Q&A" in msg_type:
                    self.collect_tab_layout.swipe(x_direction=1, swipe_coefficient=8)
                    self.ocr_click_text(self.app.testcase.device, "Questions")
                    self.create_new_collection.wait_for_visible(timeout=3)
                    self.create_new_collection.click()

                self.swipe_up()

            if find_flag:
                logger.info(f"find expect {msg_type}")
                break
            self.click_bottom_tab("Home")
        time.sleep(3)
        self.app.testcase.take_screenshot(self.app.testcase.device, "Msg_detail.jpg")

    def click_text_rect(self):
        for i in range(4):
            rect = self.desc.rect
            if i == 0:
                self.app.testcase.device.click(rect.left + 15, rect.top + 10)
            elif i == 1:
                self.app.testcase.device.click(rect.right - 15, rect.top + 10)
            elif i == 2:
                self.app.testcase.device.click(rect.left + 15, rect.bottom - 10)
            elif i == 3:
                self.app.testcase.device.click(rect.right - 15, rect.bottom - 10)
            if not self.desc.wait_for_visible(timeout=8, raise_error=False):
                break

    def check_repost_locate(self, ab_value):
        if ab_value == 0:
            for _ in Retry(limit=8, interval=1, raise_error=True):
                if self["repost_item"].wait_for_visible(timeout=5, raise_error=False):
                    repost_result = self["repost_item"].children[2]
                    logger.info(f"item text:{repost_result.text}")
                    self["repost_item"].click()
                    self.confirm_repost_guidance()
                    return True
                else:
                    self.check_repost_video()
        elif ab_value == 1 or ab_value == 2:
            for _ in Retry(limit=8, interval=1, raise_error=True):
                if self.get_share_channel("Repost") is not None:
                    repost_result = self.get_share_channel("Repost")
                    logger.info(f"Repost locate:{repost_result[1]}")
                    repost_result[0].click()
                    self.confirm_repost_guidance()
                    return True if ab_value == 2 else repost_result[1] == 0
                else:
                    self.check_repost_video()
        elif ab_value == 3 or ab_value == 4:
            for _ in Retry(limit=8, interval=1, raise_error=False):
                if self.get_share_action("Save video") is not None and self.get_share_action("Repost") is not None:
                    save_result = self.get_share_action("Save video", True)
                    repost_result = self.get_share_action("Repost", True)
                    logger.info(f"Repost locate:{repost_result[1]}, Save video locate:{save_result[1]}")
                    repost_result[0].click()
                    self.confirm_repost_guidance()
                    if ab_value == 3:
                        return repost_result[1] == 0
                    return repost_result[1] == save_result[1]-1
                else:
                    self.check_repost_video()
        return False

    def check_repost_video(self):
        logger.info("current video no repost")
        device = self.app.testcase.device
        screen_size = device.screen_rect
        device.click(screen_size.center[0], screen_size.center[1])
        time.sleep(1)
        self.swipe_up()
        self.video_is_ad_control()
        self.share()
        time.sleep(1)

    def check_repost_result(self):
        if self["tv_upvote_you_reposted"].wait_for_visible(timeout=15, raise_error=False):
            logger.info(self["tv_upvote_you_reposted"].text)
            return "repost" in self["tv_upvote_you_reposted"].text
        else:
            return False

    def get_share_channel(self, channel_name):
        """
        Returns the channel control and location
        """
        self["channel_list"].refresh()
        for _ in Retry(limit=3, interval=1, raise_error=False):
            channel_list = self["channel_list"].items()
            for index, channel in enumerate(channel_list):
                if channel_name in channel.channel_name:
                    return channel, index
            self["channel_list"].swipe(x_direction=1, swipe_coefficient=5)
        return None

    def click_tako_icon(self):
        time.sleep(10)
        self.app.testcase.take_screenshot(self.app.testcase.device, "feed_tikbot_icon.jpg")
        for _ in Retry(limit=15, interval=1):
            if self.tako_icon.wait_for_visible(timeout=5, raise_error=False):
                self.tako_icon.click()
                time.sleep(1)
                break
            self.app.restart()
            self.video_is_ad_control()


class VideoList(Control):
    """
    视频列表
    """
    elem_class = Feed_video
    elem_path = UPath(type_ == "com.ss.android.ugc.aweme.base.widget.FixedRatioFrameLayout")


class FollowingPushCardUpath(Control):
    def get_locators(self):
        return {
            "user_head": {
                "path": UPath(type_ == "com.ss.android.ugc.aweme.base.ui.SmartAvatarImageView", visible_ == True)},
            "user_name": {"path": UPath(id_ == "user_name", visible_ == True)},
            "real_name": {"path": UPath(id_ == "real_name", visible_ == True)},
            "follow_button": {"path": UPath(id_ == "follow", visible_ == True)},
        }

    def user_head_wait_for_existing(self):
        return self["user_head"].wait_for_existing(timeout=4, raise_error=False)

    def user_name_wait_for_existing(self):
        return self["user_name"].wait_for_existing(timeout=4, raise_error=False)

    def real_name_wait_for_existing(self):
        return self["real_name"].wait_for_existing(timeout=4, raise_error=False)

    def follow_button_wait_for_existing(self):
        return self["follow_button"].wait_for_existing(timeout=4, raise_error=False)


class FollowingPushCardList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout", visible_ == True)
    elem_class = FollowingPushCardUpath

class LiveVideoPanel(BasePanel):
    """直播"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LivePlayActivity"}

    def get_locators(self):
        return {
            "live_follow": {"path": UPath(id_ == "follow", visible_ == True)},
            'cross_close': {'path': UPath(id_ == 'cross_close')},
            'close_widget_container': {'path': UPath(id_ == 'close_widget_container')},
        }

    def ClickLiveVideoFollowBtn(self):
        self["live_follow"].click()
        time.sleep(3)

    def CheckLiveVideoFollowBtn(self):
        return self["live_follow"].wait_for_visible(timeout=3, raise_error=False)

class FeedCommentListUsernameUpath(Control):
    def get_locators(self):
        return {
            "user_name": {"path": UPath(id_ == "title", visible_ == True)},
        }


class FeedCommentListUsername(Control):
    elem_path = UPath(id_ == "container_bg", visible_ == True) / UPath(id_ == "layout_root", visible_ == True) / UPath(id_ == "title", visible_ == True)
    elem_class = FeedCommentListUsernameUpath

class FeedCommentListUserheadUpath(Control):
    def get_locators(self):
        return {
            "user_head": {"path": UPath(id_ == "avatar", visible_ == True)},
        }


class FeedCommentListUserhead(Control):
    elem_path = UPath(id_ == "container_bg", visible_ == True) / UPath(id_ == "layout_root", visible_ == True) / UPath(id_ == "avatar", visible_ == True)
    elem_class = FeedCommentListUserheadUpath


class FollowingPanel(ForYouPanel):
    """关注页面
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(FollowingPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "userhead": {"type": Control, "path": UPath(id_ == "user_avatar_layout_indicator", visible_ == True)},
            "username": {"type": Control, "path": UPath(id_ == "title", visible_ == True)},
            "video_desc": {"type": Control, "path": UPath(id_ == "desc", visible_ == True)},
            "music_title": {"type": Control, "path": UPath(id_ == "music_title", visible_ == True)},
            "music_title1": {"type": Control, "path": UPath(id_ == "music_title_static", visible_ == True)},
            "music_cover_animation": {"type": Control, "path": UPath(id_ == "music_cover_animation", visible_ == True)},
            "video_time": {"type": Control, "path": UPath(id_ == "tv_post_time", visible_ == True)},
            "video_progress_bar": {"type": Control, "path": UPath(id_ == "audio_view", visible_ == True)},

            "feed_comment_userhead": {"type": FeedCommentListUserhead, "path": UPath(id_ == 'recyclerView', visible_ == True)},
            "feed_comment_username": {"type": FeedCommentListUsername, "path": UPath(id_ == 'recyclerView', visible_ == True)},
            "publishing": {"type": Control, "path": UPath(id_ == 'stub_iv_publish') / UPath(id_ == 'iv_loading')},
            "live_btn": {"type": Control, "path": UPath(id_ == "rl_tab_container") / UPath(type_ == "ImageView", visible_ == True)},
            "recommend_list": {"type": FeedsList, "path": UPath(id_ == 'super_recommend') / 0},
            "living_list": {"type": FeedsList, "path": UPath(id_ == 'recycler_view', visible_ == True)},
            # 'follow_btn': {'type': Control, 'path': UPath(id_ == 'follow', visible_ == True)},
            'save_video_btn': {'path': UPath(text_ == 'Save video')},
            'save_photo_btn': {'path': UPath(text_ == "Save photo")},
            'report': {'path': UPath(text_ == 'Report')},
            'add_to_favourite': {'path': UPath(id_ == 'share_dialog_options_container') / 1 / UPath(id_ == 'desc')},
            'not_interest': {
                'path': UPath(type_ == 'com.ss.android.ugc.aweme.feed.ui.masklayer2.layout.a') / UPath(id_ == 'desc')},
            'content': {'type': Control, 'path': UPath(id_ == 'content', type_ == 'android.widget.FrameLayout')},
            # 'digg_count': {'type': Control, 'path': UPath(id_ == 'digg_count', visible_ == True)},
            'digg': {'path': UPath(id_ == 'digg', visible_ == True)},
            'favorites_ok': {'path': UPath(id_ == 'btn_ok_favorite')},
            'collect': {'path': UPath(id_ == 'favorite', visible_ == True)},
            'collect_count': {'path': UPath(id_ == 'favorite_count', visible_ == True)},
            'comment_icon': {'path': UPath(id_ == 'comment_image', visible_ == True)},
            'play_iv': {'path': UPath(id_ == 'play_iv', visible_ == True)},
            'gift_toast': {'path': UPath(id_ == 'ok_button')},
            'close_comment': {'path': UPath(id_ == 'back_btn')},
            'comment_box_userhead': {'path': UPath(id_ == "container", type_ == "LinearLayout") / UPath(id_ == "comment_avatar_new", depth=6)},
            'recommend_card_title': {'path': UPath(id_ == 'tv_title', visible_ == True)},
            'recommend_card_desc': {'path': UPath(id_ == 'tv_desc', visible_ == True)},
            'recommend_card_photo2': {"type": FollowingPushCardList,
                                      'path': UPath(type_ == "RecyclerView", visible_ == True)},
            'recommend_card_close_btn': {'path': UPath(id_ == "close", visible_ == True)},
            'share_icon': {'path': UPath(id_ == 'share_iv', visible_ == True)},
            'close_share': {'path': UPath(id_ == 'iv_close', visible_ == True)},
            "favorite_count": {'path': UPath(id_ == "favorite_count", visible_ == True)},
            "favorite_button": {'path': UPath(id_ == "favorite_container", visible_ == True)},
            "digg_count": {'path': UPath(id_ == "digg_count", visible_ == True)},
            "digg_button": {'path': UPath(id_ == "digg_container", visible_ == True)},
            "following_tab_red_dot": {'path': UPath(id_ == "tv_tab_dot", visible_ == True)},
            "following_tab_live": {'path': UPath(id_ == "living_badge_text", visible_ == True)},
            "comment_num": {"path": UPath(id_ == 'comment_count', visible_ == True)},
        })
        self.wait_for_loading()
    def CheckFollowingUserhead(self):
        return self["userhead"].wait_for_visible(timeout=3, raise_error=True)

    def CheckFollowingUsername(self):
        return self["username"].wait_for_visible(timeout=3, raise_error=True)

    def CheckFollowingVideoDesc(self):
        return self["video_desc"].wait_for_visible(timeout=3, raise_error=True)

    def CheckFollowingMusicTitle(self):
        if self["music_title"].wait_for_visible(timeout=3, raise_error=False):
            return self["music_title"].wait_for_visible(timeout=3, raise_error=True)
        elif self["music_title1"].wait_for_visible(timeout=3, raise_error=True):
            return self["music_title1"].wait_for_visible(timeout=3, raise_error=True)

    def CheckFollowingMusicCoverAnimation(self):
        return self["music_cover_animation"].wait_for_visible(timeout=3, raise_error=True)

    def CheckFollowingVideoTime(self):
        return self["video_time"].wait_for_visible(timeout=3, raise_error=True)

    def CheckFollowingVideoProgressBar(self):
        return self["video_progress_bar"].wait_for_visible(timeout=3, raise_error=True)

    def ClickLiveBtn(self):
        self["live_btn"].click()
        time.sleep(3)
    def ClickFirstCommentUsername(self, value=0):
        self["feed_comment_username"].items()[value]["user_name"].click()
        time.sleep(3)

    def ClickFirstCommentUserhead(self, value=0):
        self["feed_comment_userhead"].items()[value]["user_head"].click()
        time.sleep(3)

    def GetFirstCommentUsername(self):
        list = []
        for itme in self["feed_comment_username"].items():
            list.append(itme["user_name"].text)
        return list

    def CheckFollowingTabReddot(self):
        return self["following_tab_red_dot"].wait_for_visible(timeout=3, raise_error=False)

    def CheckFollowingTabLive(self):
        return self["following_tab_live"].wait_for_visible(timeout=3, raise_error=False)

    def check_following_first_head(self):
        return self["recommend_card_photo2"].items()[0].user_head_wait_for_existing()

    def check_following_first_follow_button(self):
        return self["recommend_card_photo2"].items()[0].follow_button_wait_for_existing()

    def check_following_first_user_name(self):
        return self["recommend_card_photo2"].items()[0].user_name_wait_for_existing()

    def check_following_first_real_name(self):
        return self["recommend_card_photo2"].items()[0].real_name_wait_for_existing()

    def click_comment_btn(self):
        self['comment_icon'].click()
        time.sleep(2)

    def return_comment_num(self):
        logger.info("comment_num: %s" % int(self["comment_num"].text))
        return int(self["comment_num"].text)

    def check_comment_num(self):
        if self["comment_num"].text == "1":
            return True
        return False

    def wait_for_loading(self):
        super(FollowingPanel, self).wait_for_loading()
        if self["living_list"].wait_for_visible(timeout=3, raise_error=False):
            self.swipe_up()

    def wait_for_publishing(self):
        return self["publishing"].wait_for_visible(raise_error=False)

    def wait_for_publish_done(self):
        return self["publishing"].wait_for_not_existing(timeout=60, raise_error=False)

    def wait_for_recommend_list(self):
        self.wait_for_loading()
        time.sleep(1)
        return self["recommend_list"].wait_for_visible(timeout=3, raise_error=False)

    def wait_for_save_video(self):
        return self['save_video_btn'].wait_for_existing(timeout=3, raise_error=False)

    def wait_for_save_photo(self):
        return self['save_photo_btn'].wait_for_existing(timeout=3, raise_error=False)

    def wait_for_add_to_favourite(self):
        return self['add_to_favourite'].wait_for_existing(timeout=3, raise_error=False)

    def wait_for_report(self):
        return self['report'].wait_for_existing(timeout=3, raise_error=False)

    def wait_for_not_interested(self):
        return not self['not_interest'].wait_for_existing(timeout=3, raise_error=False)

    def wait_for_digg_count_invisible(self, type="digg"):
        return not self[type + '_count'].wait_for_visible(timeout=3, raise_error=False)

    def swipe_video_for_add_fav(self, type="digg"):
        while self.wait_for_digg_count_invisible() or self[type + "_count"].text.endswith("K") or self[
            type + "_count"].text.endswith("M"):
            self.swipe_up()
            self[type + "_count"].refresh()
            time.sleep(3)

    def get_digg_count_text(self, type="digg"):
        digg_count_text = self[type + '_count'].text
        return digg_count_text

    def feed_add_fav(self, type="digg"):
        self[type].click()
        self[type + "_count"].refresh()
        time.sleep(3)

    # 点击收藏按钮后的确认弹窗
    def check_favorites_confirm(self):
        if self["favorites_ok"].wait_for_visible(timeout=3, raise_error=False):
            self['favorites_ok'].click()
            time.sleep(3)

    # 获取收藏数量
    def get_favorite_count(self):
        if self["favorite_count"].wait_for_visible(timeout=3, raise_error=False):
            return self['favorite_count'].text

    def click_favorite_button(self):
        if self["favorite_button"].wait_for_visible(timeout=3, raise_error=False):
            self['favorite_button'].click()
            time.sleep(3)

    def check_favorite_button(self):
        return self["favorite_button"].wait_for_visible(timeout=3, raise_error=False)

    def CheckFollowingFeedFavoriteBtn(self):
        if self["favorite_button"].wait_for_existing(timeout=3, raise_error=False):
            return self["favorite_button"].wait_for_visible(timeout=3, raise_error=False)

    def get_digg_count(self):
        if self["digg_count"].wait_for_visible(timeout=3, raise_error=False):
            return self['digg_count'].text

    def click_digg_button(self):
        if self["digg_button"].wait_for_visible(timeout=3, raise_error=False):
            self['digg_button'].click()
            time.sleep(3)

    def wait_for_comment_visible(self):
        return self['comment_icon'].wait_for_visible(timeout=2, raise_error=False)

    def wait_for_share_visible(self):
        return self['share_icon'].wait_for_visible(timeout=2, raise_error=False)

    def comment_click(self):
        self['comment_icon'].click()
        if (self['gift_toast'].wait_for_existing(timeout=3, raise_error=False)):
            self['gift_toast'].click()

    def ClickFeedComment(self):
        self['comment_icon'].click()
        time.sleep(5)

    def share_click(self):
        self['share_icon'].click()
        time.sleep(5)

    def wait_for_play_iv_visible(self):
        return self['play_iv'].wait_for_visible(timeout=2, raise_error=False)

    def close_comment(self):
        if self['comment_box_userhead'].wait_for_visible(timeout=2, raise_error=False):
            self.app.get_device().press_back()
            time.sleep(3)
            self['close_comment'].click()
        else:
            self['close_comment'].click()

    def close_share(self):
        self['close_share'].click()

    def check_rec_card_title(self):
        if (self['recommend_card_title'].wait_for_visible(timeout=3, raise_error=False)):
            return self['recommend_card_title'].text == 'Trending creators'
        return False

    def check_rec_card_desc(self):
        if (self['recommend_card_desc'].wait_for_visible(timeout=3, raise_error=False)):
            return self['recommend_card_desc'].text == 'Follow an account to see their latest videos here.'
        return False

    def wait_for_rec_card_close_btn(self):
        return self['recommend_card_close_btn'].wait_for_visible(timeout=3, raise_error=False)


class ChallengeDetailPanel(BasePanel):
    """话题页面
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.challenge.ui.ChallengeDetailActivity"}

    schema_url = "snssdk1233://challenge/detail/********"

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ChallengeDetailPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "hash_tag": {"type": Control, "path": UPath(id_ == 'avatar_container') / UPath(id_ == 'title')},
            "collect": {"type": Control, "path": UPath(id_ == 'tv_collect')},
            "share_btn": {"type": Control, "path": UPath(id_ == 'share_btn')},
            "share_panel_root": {"type": Control, "path": UPath(id_ == 'share_panel_root')},
            "share_panel_cancel": {"type": Control,
                                   "path": UPath(id_ == 'iv_close', type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "start_record": {"type": Control, "path": UPath(id_ == 'start_record')},
            "back_btn": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "hashtag_photo": {"type": Control, "path": UPath(id_ == 'avatar')},
            "tv_expand": {"type": Control, "path": UPath(id_ == "tv_expand")},
            "vv_count": {"type": TextView, "path": UPath(id_ == 'attrs_first')},
            "hashtag_desc": {"type": TextView,
                             "path": UPath(id_ == 'tv_desc', type_ == 'com.bytedance.tux.input.TuxTextView')},
            "feed_list": {"type": FeedsList, "path": UPath(id_ == 'feed_list')},
            "hashtag_video0": {"type": Control, "path": UPath(id_ == 'feed_list') / 0 / UPath(id_ == 'cover')},
            "hashtag_info": {"type": Control, "path": UPath(id_ == 'avatar_container')},
            "post_video_button": {"type": Control, "path": UPath(id_ == 'start_record_title')},
            "effect_icon": {"type": Control, "path": UPath(id_ == 'tv_nickname')},
            "send_to_panel": {"type": ScrollView, "path": UPath(id_ == 'recycle_view')},
            "more_btn": {"type": Control, "path": UPath(id_ == 'civ')},
            "search_user": {"type": Control, "path": UPath(id_ == 'search_et')},
            "first_user": {"type": Control, "path": UPath(id_ == 'contact_list_recyclerview') / 0},
            "send_btn": {"type": Control, "path": UPath(id_ == 'tv_send')},
            "pop_up": {"type": Control, "path": UPath(id_ == 'tv_notice')}
        })

    def wait_for_tag_visible(self):
        return self["hash_tag"].wait_for_visible()

    @property
    def challenge_title(self):
        return self["hash_tag"].text

    def check_hashtag_collect(self):
        return self["collect"].wait_for_existing(timeout=5, raise_error=True)

    def check_collect_status(self):
        return self["collect"].text

    def add_to_favorite(self):
        self["collect"].click()
        time.sleep(2)
        return self["collect"].text == "Added to Favorites"

    def remove_favorite(self):
        self["collect"].click()
        time.sleep(2)
        return self["collect"].text == "Add to Favorites"

    def share(self):
        self["share_btn"].click()

    def check_share_btn(self):
        return self["share_btn"].wait_for_existing(timeout=5, raise_error=True)

    def share_cancel(self):
        self["share_panel_cancel"].click()

    def check_hashtag_photo(self):
        return self["hashtag_photo"].wait_for_existing(timeout=5, raise_error=True)

    def click_hashtag_photo(self):
        self["hashtag_photo"].click()

    def check_hashtag_title(self):
        return self["hash_tag"].wait_for_existing(timeout=5, raise_error=True)

    def check_collect_btn(self):
        return self["collect"].wait_for_existing(timeout=5, raise_error=True)

    def check_hashtag_desc(self):
        if self["hashtag_desc"].visible == True:
            self["hashtag_desc"].ensure_visible()
            self["vv_count"].ensure_visible()
            rect1 = self["hashtag_desc"].rect
            rect2 = self["vv_count"].rect
            y1 = rect1.height
            y2 = rect2.height * 4
            if y2 <= y1:
                return self["tv_expand"].existing
            else:
                return self["hashtag_desc"].wait_for_existing(timeout=5, raise_error=True)

    def check_hashtag_list(self):
        return self["hashtag_video0"].wait_for_existing(timeout=5, raise_error=True)

    def check_video_size(self):
        self["hashtag_video0"].ensure_visible()
        self["hashtag_info"].ensure_visible()
        rect0 = self["hashtag_video0"].rect
        rect1 = self["hashtag_info"].rect
        if rect0.width * 3 <= rect1.width:
            return self["hashtag_video0"].wait_for_existing(timeout=5, raise_error=True)
        else:
            return False

    def check_post_video_button(self):
        return self["post_video_button"].wait_for_existing(timeout=5, raise_error=True)

    def post_video_button(self):
        if self["hash_tag"].existing and '#' in self["hash_tag"].text:
            return self["post_video_button"].text == "Join this hashtag"
        elif self["effect_icon"].existing:
            return self["post_video_button"].text == "Try this effect"
        else:
            return self["post_video_button"].text == "Use this sound"

    def check_video_num(self):
        return len(self["feed_list"].items())

    def wait_for_share_panel(self):
        return self["share_panel_root"].wait_for_visible()

    def record(self):
        self["start_record"].click()

    def click_back_button(self):
        self["back_btn"].click()

    def check_back_button(self):
        return self["back_btn"].wait_for_existing(timeout=5, raise_error=True)

    def click_video_by_index(self, swipe_cnt=0, index=0):
        for i in range(swipe_cnt):
            self.swipe(y_direction=1, swipe_coefficient=6)
        video = Control(root=self["feed_list"], path=UPath(id_ == 'cover', visible_ == True, index=index))
        video.click()

    def send_to_user(self, user):
        self["send_to_panel"].swipe(x_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["send_to_panel"].swipe(x_direction=1, swipe_coefficient=8)
        time.sleep(1)
        self["more_btn"].click()
        self["search_user"].click()
        self["search_user"].input(user)
        self["first_user"].click()
        self["send_btn"].click()


class EffectDetailPanel(BasePanel):
    """effect detail page
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.prop.activity.StickerPropDetailActicity"}

    def get_locators(self):
        return {
            'title': {'path': UPath(id_ == 'sticker_prop_title', visible_ == True)},
        }

    def return_title(self):
        return self["title"].text


class StickerPropPanel(BasePanel):
    """道具页面
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.prop.activity.StickerPropDetailActicity"}
    schema_url = "snssdk1233://stickers/detail/391043"

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(StickerPropPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "collect": {"path": UPath(id_ == 'tv_sticker_prop_collect')},
            "title": {"path": UPath(id_ == 'sticker_prop_title')},
            "share_btn": {"path": UPath(id_ == 'share_btn')},
            "share_panel_root": {"path": UPath(id_ == 'share_panel_root')},
            "start_record": {"path": UPath(id_ == 'start_record', visible_ == True)},
            "props_detail_list": {"path": UPath(id_ == "feed_list", visible_ == True)}
        })

    def get_props_detail_list(self):
        self["props_detail_list"].refresh()
        return self["props_detail_list"].items()

    def play_prop_detail_video(self):
        if len(self.get_props_detail_list()) > 0:
            return self.get_props_detail_list()[4].click()

    def sticker_title(self):
        return self["title"].text

    def add_to_favorite(self):
        self["collect"].click()
        time.sleep(2)
        return self["collect"].text == "Added to Favorites"

    def remove_favorite(self):
        self["collect"].click()
        time.sleep(2)
        return self["collect"].text == "Add to Favorites"

    def share(self):
        self["share_btn"].click()

    def wait_for_share_panel(self):
        return self["share_panel_root"].wait_for_visible()

    def record(self):
        self["start_record"].click()


class QuickCommentWindow(Window):
    """quick comment
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.ui.activity.PostModeDetailActivity"}  # 可填入任意字段, 但最好存放截取模版子图设备的任意屏幕原图以便获取原设别分辨率

    def get_locators(self):
        return {
            'add_comment': {'path': UPath(id_ == "post_mode_action_bar_edittext")},
        }


class AnchorPointWindow(CVWindow):
    """anchor cv window
    """
    window_spec = {"path": "image/poi/screen.jpg"}  # 可填入任意字段, 但最好存放截取模版子图设备的任意屏幕原图以便获取原设别分辨率

    def get_locators(self):
        return {'anchor': {'path': UPath(match_ == "image/poi/poi_icon.png")},
                }


class QnaStickerWindow(CVWindow):
    """qna sticker cv window
    """
    window_spec = {"path": "cv_qna.jpeg"}  # 可填入任意字段, 但最好存放截取模版子图设备的任意屏幕原图以便获取原设别分辨率

    def get_locators(self):
        return {
            'answer': {'path': UPath(ocr_ == "Answer")},
            'title': {'path': UPath(ocr_ == "How do you like!")},
                }

    def click_answer(self):
        self["answer"].click()
        time.sleep(3)

    def click_title(self):
        self["title"].click()
        time.sleep(3)

class NGOStickerWindow(CVWindow):

    window_spec = {"path": "donate_button_sticker.jpg"}

    def get_locators(self):
        return {
        'Donate': {'path': UPath(ocr_ == 'Donate')},
        }

    def click_donate(self):
        self['Donate'].click()

class LearnmoreDonorPanel(CVWindow):

    window_spec = {"path": "learn_more.jpg"}

    def get_locators(self):
        return {
        'learn_more': {'path': UPath(ocr_ == 'Learn more')},
        }

    def click_learn_more_btn(self):
        self['learn_more'].click()

class OtherWindow(BasePanel):
    """copy paste
    """
    window_spec = {"activity": "PopupWindow:.*"}

    def get_locators(self):
        return {
            'paste': {'path': UPath(type_ == 'android.widget.LinearLayout') / UPath(desc_ == 'Paste')},
            "paste_btn": {"path": UPath(text_ == 'Paste', visible_ == True)},
            "publish_success": {"path": UPath(id_ == 'rl_publish_success_bg')},
            "likes_btn": {"path": UPath(text_ == 'Likes', visible_ == True)},
            "share_send_success": {'path': UPath(id_ == 'message')},
            "posted": {"path": UPath(id_ == "tv_des")},
            "undo": {"path": UPath(text_ == "‎Undo*")},
        }

    def publish_done(self):
        if not self["publish_success"].wait_for_visible(timeout=180, raise_error=False):
            logger.info("视频上传超时未完成")

    def check_posted(self):
        return self["posted"].wait_for_visible(timeout=30, interval=1)

    def click_undo_toast(self):
        if self["undo"].wait_for_visible(timeout=15) and \
             ('Undo' in self["undo"].text or '撤销' in self["undo"].text):
             self["undo"].click()

    def click_toast(self, toast_text):
        """
        click toast
        :param toast_text:[Sent to, 发送] or Sent to
        :type:list or str
        """
        if self.share_send_success.wait_for_visible(timeout=20, raise_error=False):
            if isinstance(toast_text, list) and len(toast_text) > 1:
                toast_text_zh = toast_text[0]
                toast_text_us = toast_text[1]
                if toast_text_zh in self.share_send_success.text or toast_text_us in self.share_send_success.text:
                    self.share_send_success.click()
            else:
                if toast_text in self.share_send_success.text:
                    self.share_send_success.click()
            time.sleep(3)
        else:
            return False


class FeishuWindow(BasePanel):
    """
    飞书验证
    """
    window_spec = {"activity": "com.bytedance.feedbackerlib.activity.LarkSSOLaunchTransitionActivity"}


class NoticeInboxWindow(BasePanel):
    """
    inbox信息规范测试
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "notification_head": {"path": UPath(id_ == 'notification_head',
                                                type_ == 'com.ss.android.ugc.aweme.base.ui.AvatarImageView')},

        }


class CommodityPanel(BasePanel):
    """
    商品面板
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.bullet.BulletContainerActivity"}

    def get_locators(self):
        return {
            "shop_now": {"path": UPath(desc_ == 'Shop now')},

        }


class MiniGameAnchorDetail(BasePanel):
    """
    Mini game锚点详情页
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.i18n.mgl.PluginMglGameActivity"}

    def get_locators(self):
        return {
            "title": {"path": UPath(id_ == 'collection_detail_title')},
            "confirm_btn": {"path": UPath(id_ == 'confirm', visible_ == True)},
            "close_btn": {"path": UPath(~id_ == 'mgl_title_capsule_btn_close|m5f|m5m|mas', visible_ == True)}

        }

    def click_confirm_btn(self):
        if self["confirm_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["confirm_btn"].click()
            time.sleep(8)
        self["close_btn"].click()
        self["confirm_btn"].click()
        time.sleep(3)


class PaidContentCollectionDetail(BasePanel):
    """
    Paid Collection锚点详情页
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.paidcontent.activity.PaidContentCollectionDetailActivity"}

    def get_locators(self):
        return {
            "title": {"path": UPath(id_ == 'collection_detail_title')}
        }



class TranslationEditPage(BasePanel):

    window_spec = {"activity": "com.ss.android.ugc.aweme.feed.caption.edit.TranslatedCaptionEditActivity"}

    def get_locators(self):
        return {
            "cancel_edit": {"path": UPath(id_ == 'text_left')},
            "discard_btn": {"path": UPath(text_ == 'Discard')}
        }

    def click_cancel_edit(self):
        self["cancel_edit"].wait_for_visible(timeout=5, raise_error=True, interval=0.5)
        self["cancel_edit"].click()
        self["discard_btn"].wait_for_visible(timeout=5, raise_error=True, interval=0.5)
        self["discard_btn"].click()

class FeedSearchPanle(BasePanel):
    window_spec = {"activity": "com.ss.android.ugc.aweme.search.pages.core.ui.activity.SearchResultActivity"}

    def get_locators(self):
        return {
            "search_tab": {"path": UPath(desc_ == "Users") / UPath(type_ == "TuxTextView", visible_ == True)},
            "search_box": {"path": UPath(id_ == "et_search_middle", visible_ == True)},
            "search_back": {"path": UPath(id_ == "back_btn_left", visible_ == True)},
            "search_box_btn": {"path": UPath(id_ == "tv_search_textview", visible_ == True)},
            "feed_search_result": {"type": FeedSearchResult , "path":UPath(id_ == "list_view", visible_ == True)}
        }
    def ClickSearchBackBtn(self):
        self["search_back"].click()
        time.sleep(3)

    def GetFirstSearchResultUsername(self):
        if self["search_tab"].wait_for_visible(timeout=5, raise_error=False):
            self["search_tab"].click()
            time.sleep(3)
        list = []
        for item in self["feed_search_result"].items():
            list.append(item["username"].text)
        return list

    def ClickFirstSearchResultUsername(self):
        if self["search_tab"].wait_for_visible(timeout=5, raise_error=False):
            self["search_tab"].click()
            time.sleep(3)
        if "xiaonickname" in self.GetFirstSearchResultUsername():
            index_username = self.GetFirstSearchResultUsername().index("xiaonickname")
            logger.info("xiaonickname的下标为:{}".format(index_username))
            self["feed_search_result"].items()[index_username]["username"].click()
        elif "12341833170" in self.GetFirstSearchResultUsername():
            index_username = self.GetFirstSearchResultUsername().index("12341833170")
            logger.info("12341833170:{}".format(index_username))
            self["feed_search_result"].items()[index_username]["username"].click()
        elif "uixiao003" in self.GetFirstSearchResultUsername():
            index_username = self.GetFirstSearchResultUsername().index("uixiao003")
            logger.info("uixiao003:{}".format(index_username))
            self["feed_search_result"].items()[index_username]["username"].click()
        elif "user8634350870561" in self.GetFirstSearchResultUsername():
            index_username = self.GetFirstSearchResultUsername().index("user8634350870561")
            logger.info("user8634350870561:{}".format(index_username))
            self["feed_search_result"].items()[index_username]["username"].click()
        elif "strive1018__" in self.GetFirstSearchResultUsername():
            index_username = self.GetFirstSearchResultUsername().index("strive1018__")
            logger.info("strive1018__:{}".format(index_username))
            self["feed_search_result"].items()[index_username]["username"].click()
        elif "Strive1018__" in self.GetFirstSearchResultUsername():
            index_username = self.GetFirstSearchResultUsername().index("Strive1018__")
            logger.info("Strive1018__:{}".format(index_username))
            self["feed_search_result"].items()[index_username]["username"].click()
        time.sleep(3)

    def InputFeedSearchBoxText(self, value):
        self["search_box"].text = value
        time.sleep(5)
        self["search_box_btn"].click()
        time.sleep(8)

class FeedSearchResultUpatn(Control):
    def get_locators(self):
        return {
            "userhead": {"path": UPath(type_ == "X.6bh", visible_ == True)},
            "username": {"path": UPath(id_ == "tv_aweme_id", visible_ == True)}
        }

class FeedSearchResult(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout", visible_ == True) / UPath(id_ == "search_common_user_rl", visible_ == True) / UPath(id_ == "layout_desc", visible_ == True)
    elem_class = FeedSearchResultUpatn

class FeedPageFollwBtnUpath(Control):
    def get_locators(self):
        return {
            "title": {"path": UPath(id_ == "title")},
            "follow_btn": {"path": UPath(id_ == "follow")}
        }

class FeedPageFollwBtn(Control):
    elem_path = UPath(id_ == "view_rootview") / UPath(id_ == "widget_container") / UPath(id_ == "top_widget_container")
    elem_class = FeedPageFollwBtnUpath

class FeedFollow(BasePanel):
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "swipe_up_to_skip": {"path": UPath(id_ == "tv_swipe_up", visible_ == True)},
            "follow_button": {"path": UPath(id_ == "follow_icon", visible_ == True)},
            "follow_button1": {"type": FeedPageFollwBtn ,"path": UPath(id_ == "feed_root_layout", visible_ == True)},
            "user_name": {"path": UPath(id_ == 'title', visible_ == True)},
            "nick_name": {"path": UPath(id_ == 'title', visible_ == True)},
            "user_head": {"path": UPath(id_ == 'user_avatar', visible_ == True)},
            "user_head_story": {"path": UPath(id_ == "user_story_ring", visible_ == True)},
            "live_title": {"path": UPath(id_ == 'tv_live_tips')},
            "head_live_text": {"path": UPath(text_ == 'LIVE')},
            "post_to_view_btn": {"path": UPath(id_ == "now_overlay_button")},
            "go_to_now_tab_btn": {"path": UPath(id_ == "tv_check_now")},
            "learn_more": {"path": UPath(id_ == "loading_percent_txt", visible_ == True)},
            "search_btn": {"path": UPath(id_ == "rl_tab_container") / UPath(type_ == "TuxIconView", visible_ == True)},
            # 推人相关
            "push_people_label": {"path": UPath(id_ == 'tv_label', visible_ == True)},
            "Anchor_point": {"path": UPath(id_ == 'anchor_tag_title', visible_ == True)},
            "caption": {"path": UPath(id_ == 'desc', visible_ == True)},
            "music_horse_race_lamp": {"path": UPath(id_ == 'music_cover', visible_ == True)},
            "Danger_bar": {"path": UPath(id_ == 'tv_risk_warning_hint')},
            "not_interested": {"path": UPath(id_ == 'btn_not_interested', visible_ == True)},
            "follow_back": {"path": UPath(id_ == 'btn_follow', visible_ == True)},
            "region_button": {"path": UPath(id_ == "region_button", visible_ == True)},
        }
    def ClickFeedSearchBtn(self):
        if self["search_btn"].wait_for_existing(timeout=5, raise_error=True, interval=0.5):
            self["search_btn"].click()
            time.sleep(3)

    def CheckFeedSwipeUpToSkip(self):
        if self["swipe_up_to_skip"].wait_for_existing(timeout=5, raise_error=False, interval=0.5):
            return self["swipe_up_to_skip"].wait_for_existing(timeout=5, raise_error=False, interval=0.5)
        else:
            return False

    def GetFeedUsername(self):
        userlist = []
        for item in self["follow_button1"].items():
            userlist.append(item["title"].text)
        return userlist

    def CheckFeedFollowBtn(self,index):
        return self["follow_button1"].items()[index]["follow_btn"].visible

    def CheckFeedPageLearnMore(self):
        if self["learn_more"].wait_for_existing(timeout=5, raise_error=False, interval=0.5):
            return self["learn_more"].wait_for_existing(timeout=5, raise_error=False, interval=0.5)
        else:
            return False

    def CheckFeedPageGoToNowTabBtn(self):
        if self["go_to_now_tab_btn"].wait_for_existing(timeout=5, raise_error=False, interval=0.5):
            return self["go_to_now_tab_btn"].wait_for_existing(timeout=5, raise_error=False, interval=0.5)
        else:
            return False

    def CheckFeedPagePostToViewBtn(self):
        if self["post_to_view_btn"].wait_for_existing(timeout=5, raise_error=False, interval=0.5):
            return self["post_to_view_btn"].wait_for_existing(timeout=5, raise_error=False, interval=0.5)
        else:
            return False

    def CheckFeedPageRegionButton(self):
        return self["region_button"].wait_for_visible(timeout=5, raise_error=False, interval=0.5)

    # 检查story头像
    def check_user_head_story(self):
        return self["user_head_story"].wait_for_existing(timeout=5, raise_error=False)

    # 获取not_interested按钮文案
    def get_not_interested(self):
        if self["not_interested"].wait_for_existing(timeout=5, raise_error=False):
            return self["not_interested"].text

    # 获取follow按钮文案
    def get_follow_back(self):
        if self["follow_back"].wait_for_existing(timeout=5, raise_error=False):
            return self["follow_back"].text

    # 获取推人标签文案
    def get_push_people_label(self):
        if self["push_people_label"].wait_for_existing(timeout=5, raise_error=False):
            return self["push_people_label"].text

    # 检查锚点
    def check_Anchor_point(self):
        return self["Anchor_point"].wait_for_existing(timeout=5, raise_error=False)

    # 检查跑马灯
    def check_music_horse_race_lamp(self):
        return self["music_horse_race_lamp"].wait_for_existing(timeout=5, raise_error=False)

    # 获取caption文案
    def get_caption(self):
        if self["caption"].wait_for_existing(timeout=5, raise_error=False):
            return self["caption"].text

    # 获取危险提示条文案
    def get_Danger_bar(self):
        if self["Danger_bar"].wait_for_existing(timeout=5, raise_error=False):
            return self["Danger_bar"].text

    def check_Danger_bar(self):
        return self["Danger_bar"].wait_for_existing(timeout=5, raise_error=False)

    def check_head_live_text(self):
        return self["head_live_text"].wait_for_existing(timeout=5, raise_error=False)

    def check_live_title1(self):
        if self["live_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["live_title"].wait_for_existing(timeout=5, raise_error=False)
        else:
            return False

    def click_follow_button(self):
        if self["follow_button"].wait_for_visible(timeout=5, raise_error=False, interval=0.5):
            self["follow_button"].click()
            time.sleep(3)

    def click_user_head(self):
        if self["user_head"].wait_for_visible(timeout=5, raise_error=False, interval=0.5):
            self["user_head"].click()
            time.sleep(3)

    def check_follow_butoon(self):
        if self["follow_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["follow_button"].wait_for_existing(timeout=5, raise_error=False)
        else:
            return False

    def get_user_name(self):
        self["user_name"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        user_name = self["user_name"].text
        time.sleep(3)
        if user_name[0] == "@":
            return user_name[1:]
        else:
            return user_name

    def ClickFeedUsername(self):
        self["user_name"].click()
        time.sleep(3)

    def get_nickname(self):
        self["nick_name"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        nick_name = self["nick_name"].text
        time.sleep(3)
        if nick_name[0] == "@":
            return nick_name[1:]
        else:
            return nick_name

    def get_nickname1(self):
        self["nick_name"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        return self["nick_name"].text
