import time
from shoots_android.control import *
from uibase.upath import tag_
from business.ttlh.utils.main import *

from business.ttlh.utils.account import get_account_by_tag


# 点击屏幕，关闭弹窗
def close_panel(case, device):
    device.press_back()


# 主播侧切换榜单开关
def switch_rank_setting(case, rank_list, device):
    close_panel(case, device)
    exist(rank_list["toolbar_icon"])
    rank_list["toolbar_icon"].click()
    rank_list["scroll_content"].scroll(distance_x=0, distance_y=800)
    exist(rank_list["settings"])
    rank_list["settings"].click()
    exist(rank_list["rankings_setting"])
    rank_list["rankings_setting"].click()
    exist(rank_list["gift_rank_switch"])
    rank_list["gift_rank_switch"].click()
    time.sleep(2)
    close_panel(case, device)
    time.sleep(10)
    exist(rank_list["entrance"])
    rank_list["entrance"].click()
    time.sleep(10)


# 等待控件出现
def exist(view):
    count = 10
    while (not view.wait_for_visible(timeout=10, raise_error=False)) and count > 0:
        time.sleep(5)
        count = count - 1
        print("重新获取控件")


# 校验榜单入口
def check_entrance_text(case, rank_list, rank_type: str = None):
    text_status = False
    exist(rank_list["entrance_text"])
    ranking_text = rank_list["entrance_text"].text

    print(ranking_text)

    if rank_type in ("Weekly", "stars", "last", "Daily"):

        if "Daily Ranking" in ranking_text:
            print("日榜排名99+文案校验通过：" + ranking_text)
            text_status = True

        if "Daily No." in ranking_text:
            print("日榜排名99内文案校验通过：" + ranking_text)
            text_status = True

        if "Weekly Ranking" in ranking_text:
            print("周榜排名99+文案校验通过：" + ranking_text)
            text_status = True

        if "Weekly No." in ranking_text:
            print("周榜排名99内文案校验通过：" + ranking_text)
            text_status = True

        if "Rising Star No." in ranking_text:
            print("新星榜文案校验通过：" + ranking_text)
            text_status = True

    if rank_type == "hourly":

        if "Hourly" in ranking_text:
            text_status = True
            print("小时榜排名99内文案校验通过：" + ranking_text)

    return text_status


# 校验周榜标题
def check_weekly_tab(case, rank_list):
    exist(rank_list.weekly_title)
    weekly_title = rank_list.weekly_title.text
    status = False
    if weekly_title == "Weekly Ranking":
        status = True
    return status


# 校验新星榜标题
def check_stars_tab(case, rank_list):
    exist(rank_list.stars_title)
    stars_title = rank_list.stars_title.text
    status = False
    if stars_title == "Rising Stars":
        status = True
    return status


# 校验日榜标题
def check_daily_tab(case, rank_list):
    exist(rank_list.daily_title)
    stars_title = rank_list.daily_title.text
    status = False
    if stars_title == "Daily Ranking":
        status = True
    return status


# 检验倒计时文案
def check_countdown_text(case, rank_list):
    exist(rank_list.countdown_text)
    countdown_text = rank_list.countdown_text.text
    status = False
    if countdown_text == "Refresh in":
        status = True

    return status


# 检验倒计时
def check_countdown(case, rank_list):
    return rank_list.countdown.wait_for_visible(timeout=240, raise_error=False)


# 校验底部bar文案
def check_bar_text(case, rank_list, rank_type: str = None):
    bar_text = ""
    rank_num = ""
    check_text = ""
    status = False

    if rank_type in ("Weekly", "hourly", "Daily"):
        count = 10
        while (("+" in rank_list["bar_num"].text) and (rank_list["bar_num"].text != "99+") and count != 0 and (
            rank_list["bar_num"].text == '')):
            time.sleep(5)
            count = count - 1
        exist(rank_list["bar_num"])
        rank_num = rank_list["bar_num"].text
        exist(rank_list["bar_text"])
        bar_text = rank_list["bar_text"].text
        print(bar_text)
        print(rank_num)

    if rank_type == "stars":
        exist(rank_list["bar_num"])
        rank_num = rank_list["bar_num"].text
        print(rank_num)

        if rank_list.bar_text2.wait_for_existing(raise_error=False):
            check_text = "This ranking shows the hosts who received fewer than"
            exist(rank_list["bar_text2"])
            bar_text = rank_list["bar_text2"].text
            print(bar_text)
        else:
            exist(rank_list["bar_text"])
            bar_text = rank_list["bar_text"].text
            print(bar_text)

    if rank_num == "-":
        if rank_type == "stars":
            if check_text in bar_text:
                status = True
        if rank_type == "Weekly" or rank_type == "Daily":
            if "Ranking turned off by host" or "Ranking off" in bar_text:
                status = True
    elif rank_num == "99+":
        if rank_type == "Weekly" or rank_type == "Daily":
            if "Diamonds to reach" in bar_text:
                status = True
                print("当前主播排名99+,文案校验通过：", bar_text)
        if rank_type == "hourly":
            if "Diamonds needed to be an hourly top host" in bar_text:
                status = True
                print("当前主播排名99+,小时榜文案校验通过：", bar_text)
        if rank_type == "stars":
            if "Diamonds to reach" in bar_text or check_text in bar_text:
                status = True
                print("当前主播排名99+,文案校验通过：", bar_text)


    elif int(rank_num) in range(2, 99 + 1):
        if "Diamonds to reach" in bar_text:
            status = True
            print("当前主播排名2~99,文案校验通过：", bar_text)

    elif int(rank_num) == 1:
        if "Diamonds ahead of No. 2" in bar_text:
            status = True
            print("当前主播排名1,文案校验通过：", bar_text)
    else:
        status = False

    return status


# 校验吸底bar送礼按钮
def check_bar_gift(case, rank_list, rank_type: str = None):
    if rank_type == "stars":
        exist(rank_list["stars_bar_num"])
        rank_num = rank_list["stars_bar_num"].text
        if rank_num == "-":
            pass
        else:
            return rank_list.bar_gift.wait_for_existing(raise_error=False)
    else:
        return rank_list.bar_gift.wait_for_existing(raise_error=False)


# mock用户到榜单第一名
def mock_rank_No1(case, app2, audience_rank_list, anchor_rank_list, rank_type):
    if (rank_type == "Weekly"):
        title = "Weekly No. 1"
    if (rank_type == "Daily"):
        title = "Daily No. 1"
    exist(audience_rank_list["entrance_text"])
    while (not (title in audience_rank_list["entrance_text"].text)):
        if ("K" in anchor_rank_list["bar_text"].text or "M" in anchor_rank_list["bar_text"].text or "B" in
            anchor_rank_list["bar_text"].text):
            send_gift_by_name(case, "Sam the Whale", audience_rank_list, False, 1)
        else:
            send_gift_by_name(case, "Stars Snap", audience_rank_list, False, 1)
        time.sleep(3)


# 打开上榜开关
def open_rank_setting(case, anchor_rank_list, device):
    if anchor_rank_list["bar_text_close_rank"].wait_for_existing(timeout=240, raise_error=False):
        if anchor_rank_list[
            "bar_text_close_rank"].text == "You’ve turned off LIVE creator ranking. To participate in the ranking, open settings":
            switch_rank_setting(case, anchor_rank_list, device)

        # 校验在线观众人数是否大于99


def audience_count(case, text):
    status = False

    if ("K" in text or "M" in text or "B" in text or int(text) > 99):
        status = True

    return status


# 周榜日榜case
def rank_case(case, app1, app2, app3, rank_type, rank_name):
    device1 = app1.device
    device2 = app2.device
    device3 = app3.device

    case.start_step("anchor check rank list entrance")
    anchor_rank_list = AnchorRankListWindow(root=app1)
    anchor_rank_entrance = check_entrance_text(case, anchor_rank_list, rank_type=rank_type) or check_entrance_text(
        case, anchor_rank_list, rank_type="stars")
    case.assert_("检验榜单入口是否符合预期", anchor_rank_entrance)
    case.start_step("anchor click rank list entrance")
    rank_type_tab = ""
    if (rank_type in anchor_rank_list["entrance_text"].text):
        if (rank_type == "Weekly"):
            rank_type_tab = "Weekly Ranking"
        if (rank_type == "Daily"):
            rank_type_tab = "Daily Ranking"
        print(rank_type_tab)
    if ("Rising" in anchor_rank_list["entrance_text"].text):
        rank_type_tab = "Rising Stars"
    exist(anchor_rank_list["entrance"])
    anchor_rank_list["entrance"].click()
    case.take_screenshot(device1)
    exist(anchor_rank_list["first_rank_type"])
    case.assert_("检验展示榜单与入口是否一致", rank_type_tab == anchor_rank_list["first_rank_type"].text)
    anchor_rank_list[rank_name].wait_for_existing(timeout=120)
    anchor_rank_list["stars_title"].wait_for_existing(timeout=120)
    if (rank_type == "Weekly"):
        case.assert_("校验周榜tab标题", check_weekly_tab(case, anchor_rank_list))
    if (rank_type == "Daily"):
        case.assert_("校验日榜tab标题", check_daily_tab(case, anchor_rank_list))
    case.assert_("校验新星榜tab标题", check_stars_tab(case, anchor_rank_list))
    case.assert_("校验倒计时文案", check_countdown_text(case, anchor_rank_list))
    case.assert_("校验倒计时", check_countdown(case, anchor_rank_list))
    # 检测榜单开关是否打开
    open_rank_setting(case, anchor_rank_list, device1)
    case.assert_("校验吸底bar排名文案", check_bar_text(case, anchor_rank_list, rank_type=rank_type))
    case.start_step("检验有无送礼按钮")
    anchor_rank_list["bar_gift"].wait_for_invisible(timeout=20)
    anchor_rank_list["stars_title"].click()
    anchor_rank_list["last_text"].wait_for_invisible(timeout=20)
    anchor_rank_list[rank_name].click()
    anchor_rank_list["last_text"].wait_for_existing(timeout=240)
    case.take_screenshot(device1)

    case.start_step("观众进入直播间")
    rank02 = getUID("rank02")
    goToLIVERoom(case, app2, rank02, app1)
    case.start_step("audience check rank list entrance")
    case.take_screenshot(device2)
    audience_rank_list = AudienceRankListWindow(root=app2)
    audience_rank_entrance = check_entrance_text(case, audience_rank_list, rank_type=rank_type) or check_entrance_text(
        case, audience_rank_list, rank_type="stars")
    case.assert_("检验榜单入口是否符合预期", audience_rank_entrance)
    case.start_step("audience click rank list entrance")
    exist(audience_rank_list["entrance"])
    audience_rank_list["entrance"].click()
    time.sleep(10)
    case.take_screenshot(device2)
    exist(audience_rank_list["bar_num"])
    case.assert_("检验上榜状态是否与主播侧一致", audience_rank_list["bar_num"].text == anchor_rank_list["bar_num"].text)
    if (rank_type == "Weekly"):
        case.assert_("校验周榜tab标题", check_weekly_tab(case, audience_rank_list))
    if (rank_type == "Daily"):
        case.assert_("校验日榜tab标题", check_daily_tab(case, audience_rank_list))
    case.assert_("校验新星榜tab标题", check_stars_tab(case, audience_rank_list))
    case.assert_("校验倒计时文案", check_countdown_text(case, audience_rank_list))
    case.assert_("校验倒计时", check_countdown(case, audience_rank_list))
    case.assert_("校验吸底bar排名文案", check_bar_text(case, audience_rank_list, rank_type=rank_type))
    case.assert_("校验吸底bar送礼按钮", check_bar_gift(case, audience_rank_list, rank_type=rank_type))
    exist(audience_rank_list["stars_title"])
    audience_rank_list["stars_title"].click()
    case.assert_("校验倒计时文案", check_countdown_text(case, audience_rank_list))
    case.assert_("校验倒计时", check_countdown(case, audience_rank_list))
    close_panel(case, device2)
    case.take_screenshot(device2)
    print("bar_num" + anchor_rank_list["bar_num"].text)
    anchor_rank_list = AnchorRankListWindow(root=app1)
    print("bar_num" + anchor_rank_list["bar_num"].text)
    exist(anchor_rank_list["bar_num"])
    while (anchor_rank_list["bar_num"].text == "99+"):
        send_gift_by_name(case, "Rose", audience_rank_list, False, 1)
    exist(anchor_rank_list["bar_container"])
    anchor_rank_list["bar_container"].click()
    exist(anchor_rank_list["bar_nickname"])
    if (anchor_rank_list["bar_nickname"].text == "test01"):
        ticket_count = "rank01_ticket_count2"
        ticket_count2 = "rank03_ticket_count"
    if (anchor_rank_list["bar_nickname"].text == "testvvmnlzuxoq"):
        ticket_count = "rank01_ticket_count"
        ticket_count2 = "rank03_ticket_count2"
    time.sleep(10)
    exist(anchor_rank_list[ticket_count])
    count = int(anchor_rank_list[ticket_count].text) + 1
    if (count < 1000):
        case.take_screenshot(device2)
        send_gift_by_name(case, "Rose", audience_rank_list, False, 1)
        time.sleep(10)
        case.take_screenshot(device2)
        case.take_screenshot(device1)
        case.start_step("Check whether bonus points are consistent after gifting")
        exist(anchor_rank_list["bar_container"])
        anchor_rank_list["bar_container"].click()
        exist(anchor_rank_list[ticket_count])
        print(anchor_rank_list[ticket_count].text)
        case.assert_("检验送礼后加分是否一致", count == int(anchor_rank_list[ticket_count].text))
    case.start_step("观众账号B进行送礼,使A上榜第一名")
    mock_rank_No1(case, app2, audience_rank_list, anchor_rank_list, rank_type=rank_type)
    exist(anchor_rank_list["bar_container"])
    anchor_rank_list["bar_container"].click()
    exist(audience_rank_list["entrance"])
    audience_rank_list["entrance"].click()
    case.take_screenshot(device2)
    exist(audience_rank_list["entrance_text"])
    exist(anchor_rank_list["entrance_text"])
    case.assert_("观众侧榜单入口展示", "No. 1" in audience_rank_list["entrance_text"].text)
    case.assert_("主播侧的榜单入口展示", "No. 1" in anchor_rank_list["entrance_text"].text)
    case.assert_("检验主播侧和观众侧入口是否一致", anchor_rank_list["entrance_text"].text == audience_rank_list["entrance_text"].text)
    exist(anchor_rank_list["bar_text"])
    exist(audience_rank_list["bar_text"])
    # device1.click(anchor_rank_list["bar_text"].rect.center[0], anchor_rank_list["bar_text"].rect.center[1])
    case.assert_("主播侧吸底文案展示", "Diamonds ahead of No. 2" in anchor_rank_list["bar_text"].text)
    case.assert_("观众侧吸底文案展示", "Diamonds ahead of No. 2" in audience_rank_list["bar_text"].text)

    case.start_step("主播点击自己，检验是否打开资料卡")
    exist(anchor_rank_list[ticket_count])
    anchor_rank_list[ticket_count].click()
    case.take_screenshot(device1)
    anchor_rank_list["user_card"].wait_for_existing(timeout=240)
    close_panel(case, device1)
    case.take_screenshot(device1)
    anchor_rank_list1 = AnchorRankListWindow(root=app3)
    case.start_step("观众进入直播间")
    # 检验主播侧是否打开了上榜开关
    exist(anchor_rank_list1["entrance"])
    anchor_rank_list1["entrance"].click()
    open_rank_setting(case, anchor_rank_list1, device3)
    close_panel(case, device2)
    goToLIVERoom(case, app2, rank02, app3)
    audience_rank_list = AudienceRankListWindow(root=app2)
    mock_rank_No1(case, app2, audience_rank_list, anchor_rank_list, rank_type=rank_type)
    exist(anchor_rank_list[ticket_count2])
    anchor_rank_list[ticket_count2].click()
    close_panel(case, device3)
    case.take_screenshot(device1)
    anchor_rank_list["user_card"].wait_for_existing(timeout=240)

    close_panel(case, device1)
    goToLIVERoom(case, app2, rank02, app1)
    audience_rank_list = AudienceRankListWindow(root=app2)
    exist(audience_rank_list["entrance"])
    audience_rank_list["entrance"].click()
    case.take_screenshot(device1)
    case.start_step("主播关闭榜单开关")
    switch_rank_setting(case, anchor_rank_list, device1)
    case.take_screenshot(device1)
    exist(anchor_rank_list["bar_num"])
    exist(audience_rank_list["bar_num"])
    exist(anchor_rank_list["bar_text_close_rank"])
    case.assert_("主播关闭榜单校验排名", anchor_rank_list["bar_num"].text == "-" and audience_rank_list["bar_num"].text == "-")
    case.assert_("校验看播侧底部文案", audience_rank_list["bar_text"].text == "Ranking turned off by creator")
    case.assert_("检验主播侧底部文案", anchor_rank_list[
        "bar_text_close_rank"].text == "You’ve turned off LIVE creator ranking. To participate in the ranking, open settings")
    case.start_step("主播开启榜单开关")
    switch_rank_setting(case, anchor_rank_list, device1)
    case.take_screenshot(device1)
    exist(anchor_rank_list["bar_num"])
    exist(audience_rank_list["bar_num"])
    case.assert_("检验上榜状态是否与主播侧一致", audience_rank_list["bar_num"].text == anchor_rank_list["bar_num"].text)
    case.assert_("检验观众侧是否上榜", audience_rank_list["bar_num"].text != "-")
    case.assert_("检验主播侧是否上榜", anchor_rank_list["bar_num"].text != "-")
    close_panel(case, device1)
    case.take_screenshot(device1)
