import time

from shoots_android.control import Window, Button
from uibase.upath import visible_
from uibase.web import Webview, WebElement

from business.ttlh.utils.main.base import BasePanel, Control, UPath, text_, id_


class Common(BasePanel):
    """
    测试页面公共button & 公共api
    """

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(Common, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'Log Switch': {'type': Control, 'path': UPath(text_ == 'Log Switch')},
            'UseInitConf': {'type': Control, 'path': UPath(text_ == 'UseInitConf')},
            'UseReflect': {'type': Control, 'path': UPath(text_ == 'UseReflect')},
            'ClearLog': {'type': Control, 'path': UPath(text_ == 'ClearLog')},
            'ShowConfig': {'type': Control, 'path': UPath(text_ == '展示当前的配置内容')},
            'ActiveManualRule': {'type': Control, 'path': UPath(text_ == '激活默认的Manual配置')},
            'Crash': {'type': Control, 'path': UPath(text_ == 'Crash')},
            'ExcuteCoverApi': {'type': Button, 'path': UPath(text_ == '执行覆盖API')},
            'ExcuteUnCoveringApi': {'type': Control, 'path': UPath(text_ == '执行未覆盖API')},
            "location_edit": {'type': Control, 'path': UPath(id_ == 'et_input')},
            "click_location": {'type': Control, 'path': UPath(id_ == 'power_list') / 0}
        })

    '''
    点击add location后的继续按钮
    '''

    # class FooPopup(Window):
    #     window_spec = {"path": UPath(id_ == 'visual_area')}

    #     def get_locators(self):
    #         return {"close_button": {
    #             "path": UPath(text_ == 'Continue', visible_ == True)}}

    #     def handle(self):
    #         return self.app.get_device().click(x=self.close_button.rect.center[0], y=self.close_button.rect.center[1])

    def excute_cover_api(self):
        self['ExcuteCoverApi'].click()

    def input_info_and_location(self, location):
        self["location_edit"].input(location)
        time.sleep(1)
        self["click_location"].click()
        time.sleep(1)


class PrivacySecurityDetectionScenario(BasePanel):
    """
    隐私安全检测场景
    """
    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(PrivacySecurityDetectionScenario, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'SkipLogin': {'type': Control, 'path': UPath(id_ == 'top_right_icon')}
        })

    def skip_login(self):
        self['SkipLogin'].long_click()
        time.sleep(1)


class PrivturstSensitive(Common):
    """
    敏感api测试页面'
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.SensitiveAPIMainActivity'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(PrivturstSensitive, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'TelephoneInfo': {'type': Control, 'path': UPath(text_ == 'TelephoneInfo')},
            'HardwareInfo': {'type': Control, 'path': UPath(text_ == 'HardwareInfo')},
            'AppInfo': {'type': Control, 'path': UPath(text_ == 'AppInfo')},
            'AppInfoJava': {'type': Control, 'path': UPath(text_ == 'AppInfoJava')},
            'CameraFlash': {'type': Control, 'path': UPath(text_ == 'CameraFlash')},
            'Camera2': {'type': Control, 'path': UPath(text_ == 'Camera2')},
            'MicrophoneTest': {'type': Control, 'path': UPath(text_ == 'MicrophoneTest')},
            'Location': {'type': Control, 'path': UPath(text_ == 'Location')},
            'ScreenRecording': {'type': Control, 'path': UPath(text_ == 'ScreenRecording')},
            'ApiEvent': {'type': Control, 'path': UPath(text_ == 'Api Event')},
            'ApiConfig': {'type': Control, 'path': UPath(text_ == 'Api Config')},
            'MultiProcessRuleTest ': {'type': Control, 'path': UPath(text_ == 'MultiProcessRuleTest')},
            'Anr': {'type': Control, 'path': UPath(text_ == 'Anr')}
        })

    def open_telephoneInfo_page(self):
        self['TelephoneInfo'].click()

    def open_hardware_info_page(self):
        self['HardwareInfo'].click()

    def open_app_info_page(self):
        self['AppInfo'].click()

    def open_app_info_java_page(self):
        self['AppInfoJava'].click()

    def open_camera_flash_page(self):
        self['CameraFlash'].click()

    def open_camera2_page(self):
        self['Camera2'].click()

    def open_microphone_test_page(self):
        self['MicrophoneTest'].click()

    def open_loaction_page(self):
        self['Location'].click()

    def open_screen_recording_page(self):
        self['ScreenRecording'].click()

    def open_api_event_page(self):
        self['ApiEvent'].click()

    def open_api_config_page(self):
        self['ApiConfig'].clcik()

    def open_multi_process_rule_test_page(self):
        self['MultiProcessRuleTest'].click()

    def trigger_crash(self):
        self['Crash'].click()

    def trigger_anr(self):
        self['Anr'].click()


class SkyEye(Window):
    """
    Sky Eye 页面
    """
    window_spec = {'activity': 'com.bytedance.helios.tools.skyeye.ui.view.SkyEyeEntranceActivity'}

    def get_locators(self):
        return {
            "MockApi": {'type': Control, 'path': UPath(id_ == 'mock')}
        }

    def open_mock_api_page(self, device):
        self.scroll(coefficient_y=0.3)
        x = self['MockApi'].rect.center[0]
        y = self['MockApi'].rect.center[1]
        time.sleep(2)
        device.click(x, y)


class TelephoneInfo(Common):
    """
    TelephoneInfo page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.telephonyInfo.TelephonyInfoTesting'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(TelephoneInfo, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({

            'ShowCellLocation': {'type': Control, 'path': UPath(text_ == 'ShowCellLocation')},
            'ShowCmdaBId': {'type': Control, 'path': UPath(text_ == 'ShowCmdaBId')},

        })


class HandwareInfo(Common):
    """
    TelephoneInfo page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.hardware_information.HardwareInfoTesting'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(HandwareInfo, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'ShowCellLocation': {'type': Control, 'path': UPath(text_ == 'ShowCellLocation')},
            'ShowCmdaBId': {'type': Control, 'path': UPath(text_ == 'ShowCmdaBId')},
            'ShowCmdaNetworkId': {'type': Control, 'path': UPath(text_ == 'ShowCmdaNetworkId')},
            'ShowCmdaSystemId': {'type': Control, 'path': UPath(text_ == 'ShowCmdaSystemId')},
            'ShowCmdaBLatitude': {'type': Control, 'path': UPath(text_ == 'ShowCmdaBLatitude')},
            'ShowCmdaBLongitude': {'type': Control, 'path': UPath(text_ == 'ShowCmdaBLongitude')},
            'ShowGsmCid': {'type': Control, 'path': UPath(text_ == 'ShowGsmCid')},
            'ShowGsmLac': {'type': Control, 'path': UPath(text_ == 'ShowGsmLac')}
        })


class AppInfo(Common):
    """
    AppInfo page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.app_information.AppListInfoTesting'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(AppInfo, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'ShowRecentTasks': {'type': Control, 'path': UPath(text_ == 'ShowRecentTasks')},
            'ShowRunningTasks': {'type': Control, 'path': UPath(text_ == 'ShowRunningTasks')},
            'ShowInstalledApps': {'type': Control, 'path': UPath(text_ == 'ShowInstalledApps')},
            'ShowInstalledPackages': {'type': Control, 'path': UPath(text_ == 'ShowInstalledPackages')},
            'ShowCmdaBLatitude': {'type': Control, 'path': UPath(text_ == 'ShowCmdaBLatitude')},
            'ShowAccessibilityListOld': {'type': Control, 'path': UPath(text_ == 'ShowAccessibilityListOld')},
            'ShowAccessibilityListNew': {'type': Control, 'path': UPath(text_ == 'ShowAccessibilityListNew')}
        })


class AppInfoJava(Common):
    """
    AppInfoJava page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.app_information.AppListInfoTestingJava'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(AppInfoJava, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'ShowRecentTasks': {'type': Control, 'path': UPath(text_ == 'ShowRecentTasks')},
            'ShowRunningTasks': {'type': Control, 'path': UPath(text_ == 'ShowRunningTasks')},
            'ShowInstalledApps': {'type': Control, 'path': UPath(text_ == 'ShowInstalledApps')},
            'ShowInstalledPackages': {'type': Control, 'path': UPath(text_ == 'ShowInstalledPackages')},
            'ShowCmdaBLatitude': {'type': Control, 'path': UPath(text_ == 'ShowCmdaBLatitude')},
            'ShowAccessibilityListOld': {'type': Control, 'path': UPath(text_ == 'ShowAccessibilityListOld')},
            'ShowAccessibilityListNew': {'type': Control, 'path': UPath(text_ == 'ShowAccessibilityListNew')}
        })


class CameraFlash(Common):
    """
    CameraFlash page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.camera.CameraTesting1'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(CameraFlash, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'Cam1FlashlightRepeat': {'type': Control, 'path': UPath(text_ == 'Cam1FlashlightRepeat')},
            'Cam1Open': {'type': Control, 'path': UPath(text_ == 'Cam1Open')},
            'Cam1Close': {'type': Control, 'path': UPath(text_ == 'Cam1Close')},
            'Cam1StartPreview': {'type': Control, 'path': UPath(text_ == 'Cam1StartPreview')},
            'Cam1StopPreview': {'type': Control, 'path': UPath(text_ == 'Cam1StopPreview')},
            'Cam1Lock': {'type': Control, 'path': UPath(text_ == 'Cam1Lock')},
            'Cam1Unlock': {'type': Control, 'path': UPath(text_ == 'Cam1Unlock')},
            'Cam1TorchOn': {'type': Control, 'path': UPath(text_ == 'Cam1TorchOn')},
            'Cam1TorchOff': {'type': Control, 'path': UPath(text_ == 'Cam1TorchOff')}
        })


class Camera2(Common):
    """
    Camera2 page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.camera.CameraTesting2'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(Camera2, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'OpenCam': {'type': Control, 'path': UPath(text_ == 'OpenCam')},
            'StartPreview1': {'type': Control, 'path': UPath(text_ == 'StartPreview1')},
            'StartPreview2': {'type': Control, 'path': UPath(text_ == 'StartPreview2')},
            'CloseCam': {'type': Control, 'path': UPath(text_ == 'CloseCam')},
            'PreviewTorchOn': {'type': Control, 'path': UPath(text_ == 'PreviewTorchOn')},
            'PreviewTorchOff': {'type': Control, 'path': UPath(text_ == 'PreviewTorchOff')},
            'TopPreview': {'type': Control, 'path': UPath(text_ == 'TopPreview')},
            'TorchOn': {'type': Control, 'path': UPath(text_ == 'TorchOn')},
            'TorchOff': {'type': Control, 'path': UPath(text_ == 'TorchOff')}
        })


class Clipboard(Common):
    """
    Clipboard page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.clipboard.ClipboardTesting'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(Clipboard, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'ClearClipboard': {'type': Control, 'path': UPath(text_ == 'ClearClipboard')},
            'HasPrimaryClip': {'type': Control, 'path': UPath(text_ == 'HasPrimaryClip')},
            'GetPrimaryClipDescreiption': {'type': Control, 'path': UPath(text_ == 'GetPrimaryClipDescreiption')},
            'GetPrimaryClip': {'type': Control, 'path': UPath(text_ == 'GetPrimaryClip')},
            'SetPrimaryClip': {'type': Control, 'path': UPath(text_ == 'SetPrimaryClip')},
            'HasText': {'type': Control, 'path': UPath(text_ == 'HasText')},
            'GetText': {'type': Control, 'path': UPath(text_ == 'GetText')},
            'SetText': {'type': Control, 'path': UPath(text_ == 'SetText')},
            'AddListener': {'type': Control, 'path': UPath(text_ == 'AddListener')},
            'DelListener': {'type': Control, 'path': UPath(text_ == 'DelListener')}
        })


class MicrophoneTest(Common):
    """
    MicrophoneTest page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.microphone.MicrophoneTesting'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(MicrophoneTest, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'OpenAudioRecord': {'type': Control, 'path': UPath(text_ == 'OpenAudioRecord')},
            'ReleaseAudioRecord': {'type': Control, 'path': UPath(text_ == 'ReleaseAudioRecord')},
            'StartAudioRecord': {'type': Control, 'path': UPath(text_ == 'StartAudioRecord')},
            'StopAudioRecord': {'type': Control, 'path': UPath(text_ == 'StopAudioRecord')},
            'Open_M_MediaRecord': {'type': Control, 'path': UPath(text_ == 'Open_M_MediaRecord')},
            'Release_M_MediaRecord': {'type': Control, 'path': UPath(text_ == 'Release_M_MediaRecord')},
            'Start_M_MediaRecord': {'type': Control, 'path': UPath(text_ == 'Start_M_MediaRecord')},
            'Stop_M_MediaRecord': {'type': Control, 'path': UPath(text_ == 'Stop_M_MediaRecord')}
        })


class Location(Common):
    """
    Location page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.location.LocationTestActivity'}

    # TODO:update element path
    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(Location, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "set_monitor_config_info": {'type': Control, 'path': UPath(id_ == 'btnGetLocation')},
            "method_choose": {'type': Control, 'path': UPath(id_ == '0x3041') / UPath(id_ == 'text1')},
            "webview": {"type": DetailWebview, 'path': UPath(id_ == 'win_content', visible_ == True) / UPath(id_ == '0x1e240')},
            "init": {'type': Control, 'path': UPath(id_ == '0x27')},
            "grant_permission_yes": {'type': Control, 'path': UPath(text_ == 'Yes')},
            "grant_permission_no": {'type': Control, 'path': UPath(text_ == 'No')}

        })

    def choose_method(self):
        self["method_choose"].click()

    def click_initLoc(self):
        self["init"].click()

    def click_permisiion_yes(self):
        self["grant_permission_yes"].click()

    def click_permission_no(self):
        self["grant_permission_no"].click()


class MethodPopup(Common, Window):

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(MethodPopup, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "addGpsStatusListener": {'type': Control, 'path': UPath(text_ == 'addGpsStatusListener')},
            "addProximityAlert": {'type': Control, 'path': UPath(text_ == 'addProximityAlert')},
            "getLastKnownLocation": {'type': Control, 'path': UPath(text_ == 'getLastKnownLocation')},
            "requestLocationUpdates": {'type': Control, 'path': UPath(text_ == 'requestLocationUpdates')},
            "requestSingleUpdate": {'type': Control, 'path': UPath(text_ == 'requestSingleUpdate')},
            "webViewLocation": {'type': Control, 'path': UPath(text_ == 'webViewLocation')},
            "addNmeaListener": {'type': Control, 'path': UPath(text_ == 'addNmeaListener')},
            "registerGnssMeasurementsCallback": {'type': Control,
                                                 'path': UPath(text_ == 'registerGnssMeasurementsCallback')},
            "registerAntennaInfoListener": {'type': Control,
                                            'path': UPath(text_ == 'registerAntennaInfoListener')},
            "registerGnssNavigationMessageCallback": {'type': Control,
                                                      'path': UPath(text_ == 'registerGnssNavigationMessageCallback')},
            "registerGnssStatusCallback": {'type': Control,
                                           'path': UPath(text_ == 'registerGnssStatusCallback')},
            "getCurrentLocation": {'type': Control,
                                   'path': UPath(text_ == 'getCurrentLocation')},
        })

    def click_addGpsStatusListener(self):
        self["addGpsStatusListener"].click()

    def click_addProximityAlert(self):
        self["addProximityAlert"].click()

    def click_getLastKnownLocation(self):
        self["getLastKnownLocation"].click()

    def click_requestLocationUpdates(self):
        self["requestLocationUpdates"].click()

    def click_requestSingleUpdate(self):
        self["requestSingleUpdate"].click()

    def click_webViewLocation(self):
        self["webViewLocation"].click()

    def click_addNmeaListener(self):
        self["addNmeaListener"].click()

    def click_registerGnssMeasurementsCallback(self):
        self["registerGnssMeasurementsCallback"].click()

    def click_registerAntennaInfoListener(self):
        self["registerAntennaInfoListener"].click()

    def click_registerGnssNavigationMessageCallback(self):
        self["registerGnssNavigationMessageCallback"].click()

    def click_registerGnssStatusCallback(self):
        self["registerGnssStatusCallback"].click()

    def click_getCurrentLocation(self):
        self["getCurrentLocation"].click()


class DetailWebview(Webview):
    view_spec = {"description": "\"attached\":true"}

    def get_locators(self):
        return {
            "click_me": {'type': WebElement, 'path': UPath(id_ == 'btnGetLocation')}
        }

    def click_me(self):
        self["click_me"].click()


class ScreenRecording(Common):
    """
    ScreenRecording page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.screen_recording.ScreenRecordingTesting'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ScreenRecording, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'StartRecordingMediaRecorder': {'type': Control, 'path': UPath(text_ == 'StartRecordingMediaRecorder')},
            'StopRecording': {'type': Control, 'path': UPath(text_ == 'StopRecording')},
            'ScreenshotMediaProjector': {'type': Control, 'path': UPath(text_ == 'ScreenshotMediaProjector')},
            'ScreenshotViewGetDrawingCache': {'type': Control,
                                              'path': UPath(text_ == 'ScreenshotViewGetDrawingCache')},
            'ScreenshotViewDraw': {'type': Control, 'path': UPath(text_ == 'ScreenshotViewDraw')},
            'ScreenshotViewPixelCopy': {'type': Control, 'path': UPath(text_ == 'ScreenshotViewPixelCopy')}
        })


class ApiEvent(Common):
    """
    ApiEvent page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.test_tool.ui.ApiEventActivity'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ApiEvent, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            '方法筛选': {'type': Control, 'path': UPath(text_ == '方法筛选')},
            '类型筛选': {'type': Control, 'path': UPath(text_ == '类型筛选')},
            '快捷筛选': {'type': Control, 'path': UPath(text_ == '快捷筛选')},
            '时间顺序': {'type': Control, 'path': UPath(text_ == '时间顺序')},
            '展开堆栈': {'type': Control, 'path': UPath(text_ == '展开堆栈')},
            '复制数据': {'type': Control, 'path': UPath(text_ == '复制数据')}
        })


class ApiConfig(Common):
    """
    ApiConfig page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.test_tool.ui.ApiConfigActivity'}

    # TODO: UPDATE ELEMENT UPATH
    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ApiConfig, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
        })


class MultiProcessRuleTest(Common):
    """
    MultiProcessRuleTest page
    """
    window_spec = {'activity': 'com.bytedance.privtrust.sensitive.api.multi_process.MultiProcessTest'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(MultiProcessRuleTest, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'ToMultiProcessActivity': {'type': Control, 'path': UPath(text_ == 'ToMultiProcessActivity')},
            'RunAPI': {'type': Control, 'path': UPath(text_ == 'RunAPI')}
        })
