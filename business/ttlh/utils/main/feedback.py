# -*- coding:utf-8 _*-
from shoots_android.control import UPath, Control, Window, type_, time, text_, visible_
from uibase.controls import ControlList
from uibase.upath import id_, class_
from uibase.web import WebElement, Webview, WebTextEdit


class FeedbackPanel(Window):
    """feedback
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.crossplatform.activity.CrossPlatformActivity|com.bytedance.hybrid.spark.page.SparkActivity"}

    def get_locators(self):
        return {
            "webview": {"type": FeedbackWebview, "path": UPath(id_ == "webview_root")},
            "webview_message_panel": {"type": FeedbackMessageWebview, "path": UPath(id_ == "webview_root")},
            "webview_message_input": {"type": FeedbackInputWebview, "path": UPath(id_ == "webview_root")},
            "delete_account_confirm": {"type": DeleteAccountConfirmWebview, "path": UPath(id_ == "webview_root")},
            "delete_account_continue": {"type": DeleteAccountContinueWebview, "path": UPath(id_ == "webview_root")},
            "delete_account_password": {"type": DeleteAccountPasswordWebview, "path": UPath(id_ == "webview_root")},
            "delete_account_code": {"type": DeleteAccountCodeWebview, "path": UPath(id_ == "webview_root")},
            "delete_account_confirm_2": {"type": DeleteAccountConfirm2Webview, "path": UPath(id_ == "webview_root")},
            "delete_account_confirm_3": {"type": DeleteAccountConfirm3Webview, "path": UPath(id_ == "webview_root")},
            "INS_Webview": {"type": FeedbackWebview, "path": UPath(type_ == "SECTION")},
            "ins_webview": {"type": FeedbackWebview, "path": UPath(type_ == "SECTION")},
            "ins_username_Webview": {"type": INSFeedbackWebview, "path": UPath(id_ == "webview_root")},
            "ins_recover_go": {"type": Control, "path": UPath(id_ == 'button_go') / 0},
            "find_account_page": {"type": INSFindaccountWebview, "path": UPath(id_ == "webview_root")},
            "report_problem_page": {"type": ReportProblemWebview, "path": UPath(id_ == "webview_root")},
            "webview_translation_center": {"type": TranslationWebview, "path": UPath(id_ == "webview_root")},
        }

    def click_left_back_btn(self):
        self["ins_username_Webview"].click_back_btn()

    def find_translation_webview(self):
        self["webview_translation_center"].wait_for_visible(timeout=10, interval=0.5, raise_error=False)
        return self["webview_translation_center"].click_edit_translation()

    @property
    def text_head_judge(self):
        self["report_problem_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["report_problem_page"].head_text()

    @property
    def find_account_page_title(self):
        self["find_account_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["find_account_page"].page_title()

    @property
    def ins_title(self):
        """用来接收verify_title_msg的值
        """
        self["ins_username_Webview"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["ins_username_Webview"].verify_title_msg()

    @property
    def popup_title_text(self):
        self["ins_username_Webview"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["ins_username_Webview"].verify_Popup_title()

    def cancel_click(self):
        self["ins_username_Webview"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["ins_username_Webview"].cancel_btn_click()

    @property
    def Popup_text_verify(self):
        self["ins_username_Webview"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        return self["ins_username_Webview"].verify_Popup_text()

    def click_confirm_btn(self):
        self["ins_username_Webview"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["ins_username_Webview"].click_ok_btn()

    def back_white_click(self):
        self["report_problem_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["report_problem_page"].click_back_white()

    def submit_click(self):
        self["find_account_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["find_account_page"].click_submit()

    def phone_click(self):
        self["find_account_page"].click_phone()
        self["find_account_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["find_account_page"].click_email()

    def ins_input_username(self, username):
        self.ins_username_Webview.name_input(username)

    def ins_next_btn(self):
        self["ins_username_Webview"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self.ins_username_Webview.next_btn_click()

    def ins_close_debug(self):
        self["ins_recover_go"].long_click()
        return not self["ins_recover_go"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)

    def click_look_up_username(self):
        self["ins_username_Webview"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["ins_username_Webview"].look_up_username_click()

    def delete_account_skip_click(self):
        self.refresh()
        self.delete_account_confirm.skip_click()

    def delete_account_continue_click(self):
        self.refresh()
        self.delete_account_continue.continue_click()

    def next_page_click_continue(self):
        self.refresh()
        self.delete_account_confirm.next_page_continue_click()

    def delete_account_code_wrong(self, code):
        return self.delete_account_code.input_wrong_code(code)

    def delete_account_code_right(self, code, delete=False):
        self.delete_account_code.input_right_code(code)
        if delete:
            return self.delete_account_confirm_3.delete_account_confirm()
        else:
            return self.delete_account_confirm_3.delete_account_cancel()

    def delete_account_password_wrong(self, password):
        return self.delete_account_password.input_wrong_password(password)

    def delete_account_password_right(self, password, delete=False):
        self.delete_account_password.input_right_password(password)
        if delete:
            return self.delete_account_confirm_2.delete_account_confirm()
        else:
            return self.delete_account_confirm_2.delete_account_cancel()

    def click_content(self):
        self["ins_webview"].wait_for_visible(timeout=20, interval=0.5, raise_error=False)
        self["webview"].ins_click()
        return self["ins_webview"].wait_for_visible(raise_error=False)

    def report_feedback(self, content):
        self.webview.report_feedback()
        self.webview_message_panel.to_feedback_input()
        self.webview_message_input.feedback_input(content)
        time.sleep(5)

    def wait_for_tickets_title_visible(self):
        return self.webview.wait_for_tickets_title_visible()

    def wait_for_tickets_content_visible(self):
        time.sleep(5)
        return self.webview.wait_for_content_visible()

    def wait_for_webview_visible(self):
        return self.webview.wait_for_webview_visible()

    def close_webview(self):
        self.webview.close_webview()
        time.sleep(3)

    def get_last_feedback(self):
        self.webview_message_panel.refresh()
        return self.webview_message_panel.get_last_feedback()


class TranslationWebview(Webview):
    view_spec = {
        "title": "https://www.tiktok.com/web-inapp/cla/crowdsourcing/translation-center"
    }

    def get_locators(self):
        return {
            "edit_button": {"type": WebElement, "path": UPath(class_ ==
                                                              'tux-button box-border flex select-none appearance-none '
                                                              'outline-none cursor-pointer border-solid rounded '
                                                              'items-center justify-center px-0 relative w-full '
                                                              'P1-SemiBold text-color-ConstTextInverse '
                                                              'background-color-Primary border-0',
                                                              index=0)},
        }

    def click_edit_translation(self):
        self["edit_button"].click()



class MsgList(ControlList):
    elem_class = WebElement
    elem_path = UPath(~class_ == "p1 content.*")


class FeedbackWebview(Webview):
    view_spec = {
        "description": "\"attached\":true.*\"visible\":true"
    }

    def get_locators(self):
        return {
            "record": {"type": WebElement, "path": UPath(id_ == "goRecord")},
            "title": {"type": WebElement, "path": UPath(class_ == 'navbar-main')},
            "content": {"type": WebElement, "path": UPath(class_ == 'csp-dropdown-item', text_ == 'Other')},
            "ins_content": {"type": WebElement, "path": UPath(id_ == 'all-faq-list-ul') / 4 / UPath(type_ == 'SPAN')},
            "close": {"type": WebElement, "path": UPath(id_ == 'goBack')},
            "page_tile": {"type": WebElement, "path": UPath(class_ == 'navbar-main') / UPath(type_ == 'SPAN')}
        }

    def ins_click(self):
        self["ins_content"].wait_for_visible(timeout=2, interval=0.5, raise_error=False)
        self["ins_content"].click()

    # return self["inscontent"].wait_for_visible(raise_error=False)

    def wait_for_tickets_title_visible(self):
        return self["title"].wait_for_visible(raise_error=False)

    def wait_for_webview_visible(self):
        return self["record"].wait_for_visible()

    def wait_for_content_visible(self):
        return self["content"].wait_for_visible(timeout=40, interval=0.5, raise_error=False)

    def close_webview(self):
        self["close"].click()

    def report_feedback(self):
        self["record"].click()
        self.refresh()
        self.refresh_container()

    @property
    def msg_content_list(self):
        msg_list = self["msg_list"].items()
        return [msg.text for msg in msg_list]


class FeedbackMessageWebview(Webview):
    view_spec = {
        "description": "\"attached\":true.*\"visible\":true"
    }

    def get_locators(self):
        return {
            "feedback_input": {"type": WebElement, "path": UPath(id_ == "toFeedbackInput")},
            "support_tickets_title": {"type": WebElement, "path": UPath(text_ == "Your support tickets", index=0)},
            "msg_list": {"type": MsgList, "path": UPath(type_ == "ul")},
            "last_message": {"type": WebElement, "path": UPath(type_ == 'UL')/ -1},
        }

    def to_feedback_input(self):
        self["feedback_input"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["feedback_input"].wait_for_ui_stable()
        self["feedback_input"].click()

    def get_last_feedback(self):
        self["last_message"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        return self["last_message"].text


class FeedbackInputWebview(Webview):
    view_spec = {
        "description": "\"attached\":true.*\"visible\":true"
    }

    def get_locators(self):
        return {
            "textarea": {"type": WebTextEdit, "path": UPath(type_ == "textarea")},
            "report": {"type": WebElement, "path": UPath(text_ == "Report", index=0)},

        }

    def feedback_input(self, content):
        self["textarea"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["textarea"].wait_for_ui_stable()
        self["textarea"].input(content)
        self["report"].click()


class FeedbackWindow(Window):
    """反馈弹窗
    """
    window_spec = {"activity": 'PopupWindow:.*',
                   "process_name": None}

    def get_locators(self):
        return {
            'content': {'type': Control, 'path': UPath(id_ == "tv_content", text_ == "Thank you for your feedback")},
        }

    def wait_for_thanks_msg(self):
        return self["content"].wait_for_visible(raise_error=False)


class DeleteAccountConfirmWebview(Webview):
    view_spec = {
        "url": "https://www.tiktok.com/web-inapp/account/delete/confirm?.*"
    }

    def get_locators(self):
        return {
            "continue": {"type": WebElement, "path": UPath(type_ == 'SPAN')},
            "continue_checkbox": {"type": WebElement, "path": UPath(class_ == 'TUXCheckbox__icon') / UPath(
                class_ == 'text-color-LineSecondary'
            )},
            "continue_btn": {"type": WebElement, "path": UPath(
                class_ == 'flex items-center justify-center w-full px-8'
            )},
            "skip_btn": {"type": WebElement, "path": UPath(type_ == 'BUTTON', text_ == "Skip")}
        }

    def skip_click(self):
        self["skip_btn"].wait_for_existing(timeout=10, interval=0.5, raise_error=True)
        self["skip_btn"].click()

    def next_page_continue_click(self):
        self["continue_btn"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["continue_btn"].click()


class DeleteAccountContinueWebview(Webview):
    view_spec = {
        "url": "account\/delete\/status"
    }

    def get_locators(self):
        return {
            "continue": {"type": WebElement, "path": UPath(type_ == 'SPAN')},
            "continue_checkbox": {"type": WebElement, "path": UPath(class_ == 'TUXCheckbox__icon')},
        }

    def continue_click(self):
        self["continue_checkbox"].wait_for_existing(timeout=10, interval=0.5, raise_error=True)
        self["continue_checkbox"].click()
        self["continue"].click()


class DeleteAccountPasswordWebview(Webview):
    view_spec = {
        "url": "account\/delete\/passport"
    }

    def get_locators(self):
        return {
            "password_input": {"type": WebTextEdit, "path": UPath(type_ == 'INPUT')},
            "password_input_clear": {"type": WebElement, "path": UPath(class_ == 'text-color-TextQuaternary mx-8')/UPath(type_ == 'path')},
            "delete_button": {"type": WebElement, "path": UPath(type_ == 'SPAN')},
            "wrong_password": {"type": WebElement, "path": UPath(class_ == 'mx-8')},
            "delete_confirm": {"type": WebElement, "path": UPath(class_ == 'tux-dialog-text-action h-48 w-full block text-center px-8 text-color-Negative H4-SemiBold truncate')},
        }

    def input_wrong_password(self, password):
        self.refresh()
        self["password_input"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["password_input"].click()
        time.sleep(2)
        self.refresh()
        # if self["password_input_clear"].existing:
        #     self["password_input_clear"].click()
        self["password_input"].wait_for_ui_stable()
        self["password_input"].input(password)
        self["delete_button"].click()
        self.refresh()
        return self["wrong_password"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def input_right_password(self, password):
        self.refresh()
        self["password_input"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["password_input"].click()
        time.sleep(2)
        self.refresh()
        # if self["password_input_clear"].existing:
        #     self["password_input_clear"].click()
        self["password_input"].wait_for_ui_stable()
        self["password_input"].input(password)
        self["delete_button"].click()


class DeleteAccountConfirm2Webview(Webview):
    view_spec = {
        "url": "account\/delete\/passport"
    }

    def get_locators(self):
        return {
            "delete_confirm": {"type": WebElement, "path": UPath(text_ == 'Delete', type_ == "BUTTON")},
            "delete_cancel": {"type": WebElement, "path": UPath(text_ == 'Cancel', type_ == "BUTTON",visible_ == True)},

        }

    def delete_account_confirm(self):
        if self["delete_confirm"].wait_for_visible():
            time.sleep(2)
            self["delete_confirm"].click()
            return True
        else:
            return False

    def delete_account_cancel(self):
        return self["delete_cancel"].wait_for_existing()


class DeleteAccountCodeWebview(Webview):
    view_spec = {
        "url": "account\/delete\/mobile"
    }

    def get_locators(self):
        return {
            "code_input_title": {"type": WebElement, "path": UPath(class_ == 'mb-8 H2-Bold text-color-TextPrimary')},
            "code_input": {"type": WebTextEdit, "path": UPath(type_ == 'INPUT')},
            "code_input_clear": {"type": WebElement, "path": UPath(class_ == 'text-color-TextQuaternary mx-8')/UPath(type_ == 'path')},
            "delete_button": {"type": WebElement, "path": UPath(class_ == 'truncate', visible_ == True, text_ == 'Delete account')},
            "wrong_code_notice": {"type": WebElement, "path": UPath(class_ == 'mx-8')},
            "delete_confirm": {"type": WebElement, "path": UPath(class_ == 'tux-dialog-text-action h-48 w-full block text-center px-8 text-color-Negative H4-SemiBold truncate')},
        }

    def input_wrong_code(self, code):
        self.refresh()
        self["code_input"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["code_input"].click()
        time.sleep(2)
        self.refresh()
        self["code_input"].wait_for_ui_stable()
        self["code_input"].input(code)
        self.refresh()
        self["delete_button"].click()
        self.refresh()
        return self["wrong_code_notice"].wait_for_existing(timeout=3, interval=0.5, raise_error=False)

    def input_right_code(self, code):
        self.refresh()
        self["code_input"].wait_for_existing(timeout=10, interval=0.5, raise_error=False)
        self["code_input"].click()
        # if self["password_input_clear"].existing:
        #     self["password_input_clear"].click()
        self["code_input"].wait_for_ui_stable()
        if self["code_input_title"].text == "Enter 6-digit code":
            self["code_input"].input("00"+code)
        else:
            self["code_input"].input(code)
        self["delete_button"].click()


class DeleteAccountConfirm3Webview(Webview):
    view_spec = {
        "url": "account\/delete\/mobile"
    }

    def get_locators(self):
        return {
            "delete_confirm": {"type": WebElement, "path": UPath(text_ == 'Delete', type_ == "BUTTON")},
            "delete_cancel": {"type": WebElement, "path": UPath(text_ == 'Cancel', type_ == "BUTTON",visible_ == True)},
        }

    def delete_account_confirm(self):
        if self["delete_confirm"].wait_for_visible():
            time.sleep(2)
            self["delete_confirm"].click()
            return True
        else:
            return False

    def delete_account_cancel(self):
        return self["delete_cancel"].wait_for_existing()


class INSFeedbackWebview(Webview):
    """通过INS找回账号 INS账号username输入页面
    """
    view_spec = {
        "title": "Instagram Rebind"
    }

    def get_locators(self):
        return {
            "ins_title": {"type": WebElement, "path": UPath(type_ == "H1")},
            "username_input": {"type": WebTextEdit, "path": UPath(type_ == "INPUT")},
            "next_btn": {"type": WebElement, "path": UPath(class_ == 'long-button main-btn')},
            "look_up_username": {"type": WebElement, "path": UPath(class_ == 'custom-dialog-footer') / 0},
            "Popup_title": {"type": WebTextEdit, "path": UPath(class_ == 'custom-dialog-title')},
            "cancel_btn": {"type": WebElement, "path": UPath(text_ == 'Cancel')},
            "Popup_text": {"type": WebElement, "path": UPath(class_ == 'custom-dialog-desc')},
            "Popup_text_ok": {"type": WebElement, "path": UPath(class_ == 'custom-dialog-confirm')},
            "left_back_btn": {"type": WebElement,
                              "path": UPath(class_ == 'page-bar-icon page-bar-icon-back page-bar-icon-back-white')}
        }

    def verify_title_msg(self):
        return self["ins_title"].text

    def name_input(self, username):
        self["username_input"].click()
        self["username_input"].input(username)

    def next_btn_click(self):
        self["next_btn"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["next_btn"].click()

    def look_up_username_click(self):
        self["look_up_username"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["look_up_username"].click()

    def verify_Popup_title(self):
        return self["Popup_title"].text

    def cancel_btn_click(self):
        self["cancel_btn"].click()

    def verify_Popup_text(self):
        return self["Popup_text"].text

    def click_ok_btn(self):
        self["Popup_text_ok"].click()

    def click_back_btn(self):
        self["left_back_btn"].click()


class INSFindaccountWebview(Webview):
    """在INSFeedbackWebview   ins username输入页面输入正确的username，进入找回账号页面

    """
    view_spec = {
        "title": "Instagram Rebind"
    }

    def get_locators(self):
        return {
            "use_phone_find": {"type": WebElement, "path": UPath(class_ == 'page-body') / 3},
            "use_email_find": {"type": WebElement, "path": UPath(class_ == 'page-body') / 4},
            "submit_verification_form": {"type": WebElement, "path": UPath(type_ == 'EM')},
        }

    def page_title(self):
        return self["submit_verification_form"].text

    def click_phone(self):
        self["use_phone_find"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["use_phone_find"].click()

    def click_email(self):
        self["use_email_find"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["use_email_find"].click()

    def click_submit(self):
        self["submit_verification_form"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["submit_verification_form"].click()


class ReportProblemWebview(Webview):
    """在找回账号页面点击submit_verification_form，进入report problem 页面

       """
    view_spec = {
        "title": "https://www.tiktok.com/falcon/fe_tiktok_common/faq/feedback/?hide_nav_bar=1"
    }

    def get_locators(self):
        return {
            "left_back_btn": {"type": WebElement, "path": UPath(type_ == 'I')},
            "head_text": {"type": WebElement, "path": UPath(type_ == 'HEADER')},
        }

    def head_text(self):
        return self["head_text"].text

    def click_back_white(self):
        self["left_back_btn"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["left_back_btn"].click()
