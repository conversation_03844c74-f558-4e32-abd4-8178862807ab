# -*- coding:utf-8 _*-
import time
from shoots_android.control import *
from uibase.web import *
from uibase.controls import Control
from uibase.upath import UPath, type_, desc_, text_, class_, lang_
from uibase.web import Webview, WebElement
from .base import <PERSON>Panel, FeedsList
from business.ttlh.utils.main.base import Control
from uibase.base import UIScene


class Video(Control):
    def get_locators(self):
        return {
            'drafts': {'type': Control, 'path': UPath(id_ == 'tv_draft')},
            'posted': {'type': Control, 'path': UPath(id_ == 'tv_play_count')},
        }

    def is_draft(self):
        return self["drafts"].visible

    def is_posted(self):
        return self["posted"].visible


class PrivacyPopup(Window):
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity#1|com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.setting.ui.I18nSettingNewVersionActivity|com.ss.android.ugc.aweme.setting.ui.SettingContainerActivity.*|com.ss.android.ugc.aweme.journey.NewUserJourneyActivity.*"}

    def get_locators(self):
        return {
            "tv_msg": {"type": Control, "path": UPath(id_ == 'tv_msg')},
            "agree_and_continue": {"type": Control, "path": UPath(id_ == 'btn_agree')},
            # "tv_msg": {"type": Control, "path": UPath(id_ == "hr5")},
        }

    def privacy_policy(self):
        text = self["tv_msg"].text
        print(text)
        start = text.find("隐", 0)
        end = text.find("策", 0)
        # 英文状态下点击（点击不准确）
        if start == -1:
            start = text.find("Privacy", 0)
            end = text.find("Policy", 0) + len('Policy')
        self["tv_msg"].click_text_span(start, end)
        time.sleep(1)

    def service_clause(self):
        text = self["tv_msg"].text
        print(text)
        start = text.find("服", 0)
        end = text.find("款", 0)
        # 英文状态下点击（点击不准确）
        if start == -1:
            start = text.find("Privacy", 0)
            end = text.find("Policy", 0) + len('Policy')
        self["tv_msg"].click_text_span(start, end)
        time.sleep(1)

    def agree_and_continue(self):
        if self['agree_and_continue'].existing:
            self['agree_and_continue'].click()

    def verify_agree_and_continue(self):
        return self['agree_and_continue'].text


class MyVideoList(FeedsList):
    elem_class = Video


class MinePanel(BasePanel):
    """Mine Tab
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(MinePanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'mine_page': {'type': Control, 'path': UPath(id_ == 'profile_page', visible_ == True)},
            'sign_up': {'type': Control,
                        'path': UPath(~id_ == 'btn_signin|unlogin_btn_login_signup|btn_show_next_time_item', visible_ == True)},
            'user_id': {'type': Control, 'path': UPath(id_ == 'user_id')},
            'edit_profile_btn': {'type': Control, 'path': UPath(id_ == 'my_profile_edit', visible_ == True)},
            'edit_profile_btn1': {'type': Control, 'path': UPath(~text_ == "Set up profile|Edit profile")},
            'my_favourite': {'type': Control, 'path': UPath(id_ == 'my_favourite')},
            # 'more_btn': {'type': Control, 'path': UPath(id_ == 'nav_end')},
            'feeds': {'type': MyVideoList, 'path': UPath(id_ == 'feed_list', visible_ == True)},
            'tab_container': {'type': Control, 'path': UPath(id_ == 'tab_container')},
            'liked': {'type': Control, "root": 'tab_container', 'path': UPath(index=1)},
            'add_account_entrance': {'type': Control, "path": UPath(id_ == "nav_bar_title", visible_ == True)},
            # 'add_account_btn': {'type': Control, 'path': UPath(id_ == 'account_list')/-1/UPath(id_ == 'line0')},
            'add_account_btn': {'type': Control,
                                'path': UPath(type_ == 'androidx.recyclerview.widget.RecyclerView') / 1},
            'add_account_text': {'type': Control, 'path': UPath(text_ == "Add account")},
            'switch_account_2': {'type': Control, 'path': UPath(id_ == 'sheet_content_container') / 0 / 1},
            "bind_phone_or_email_yes": {"type": Control, "path": UPath(id_ == 'positive_button')},
            "bind_phone_or_email_no": {"type": Control, "path": UPath(id_ == 'negative_button')},
            "bind_phone_or_email_title": {"type": Control, "path": UPath(id_ == 'tv_title', visible_ == True)},
            "bind_phone_or_email_text": {"type": Control, "path": UPath(id_ == 'tv_content')},
            "follow_your_friends_page": {"type": TextView, "path": UPath(id_ == 'title_recommend_user')},
            "right_top_cancel": {"type": Control, "path": UPath(id_ == 'close')},
            "avatar": {"type": Control, "path": UPath(id_ == 'header_image', visible_ == True)},
            "privacy_and_settings_entry": {"type": Control, "path": UPath(id_ == 'more_btn_src')},
            "follow_friends_panel": {"type": Control, "path": UPath(id_ == "layout_content")},
            "close_follow_panel": {"type": Control, "path": UPath(id_ == "close")},
            "private_account_text": {"type": TextView,
                                     "path": UPath(id_ == 'profile_head') / 0 / 0 / UPath
                                     (type_ == 'com.bytedance.tux.input.TuxTextView')},
            "settings_pop_up": {"path": UPath(id_ == 'actionsheet_actiongroups_container')},
            "settings_enter": {"path": UPath(id_ == 'action_sheet_action_content',
                                             ~text_ == 'Settings and privacy|隱私設定|设置与隐私')},
            "playlist_add_button": {'path': UPath(id_ == 'power_list', visible_ == True) / 0},
            # Settings 入口（未登陆）
            "settings_btn": {"type": Control, "path": UPath(id_ == 'setting_btn', visible_ == True)},
            # Settings 入口（登陆）
            "settings_btn_logged": {"type": Control, "path": UPath(id_ == 'nav_end') / 2},
            "close_sort_video_playlist_x_button": {"type": Control, "path": UPath(id_ == 'ic_close')},
            "playlist_items": {"type": Control, "path": UPath(id_ == 'power_list', visible_ == True)},
            "playlist_card": {"type": Control, "path": UPath(id_ == 'item_guide_user_card',
                                                             type_ == 'com.bytedance.tux.button.TuxButton')},
            "playlist_first_card": {"type": Control, "path": UPath(id_ == 'power_list', visible_ == True) / 1},
            "playlist_empty_content": {"type": Control, "path": UPath(id_ == 'play_list_empty_content')},
            "playlist_empty_doc": {"type": Control, "path": UPath(id_ == 'play_list_empty_doc')},
            "content_container": {"path": UPath(id_ == 'content_container', visible_ == True)},
            "first_video": {"type": Control,
                            "path": UPath(id_ == 'feed_list', visible_ == True) / 0 / UPath(id_ == 'cover')},
            "first_draft_video": {"type": Control, "path": UPath(id_ == 'feed_list', visible_ == True) / UPath(
                type_ == 'android.view.View', visible_ == True)},
            "private_first_video": {"type": Control,
                                    "path": UPath(id_ == 'feed_list', visible_ == True) / 0 / UPath(id_ == 'cover')},
            "privacy_videos": {"type": Control, "path": UPath(desc_ == "Private videos") / UPath(id_ == "icon",
                                                                                                 visible_ == True)},
            "following": {"type": Control, "path": UPath(id_ == 'following_layout')},
            "find_friends": {"type": Control, "path": UPath(id_ == "nav_start", visible_ == True) / 1},
            "draft_video": {"type": Control, "path": UPath(id_ == 'tv_draft', visible_ == True)},
            # Exploring
            "tab_lists": {"type": Control, "path": UPath(id_ == 'tab_layout', visible_ == True)},
            "tab_list": {"type": Control, "path": UPath(~type_ == 'com.bytedance.ies.dmt.ui.widget.tablayout.DmtTabLayout$f|X.XWn')},
            "collections_tab": {"type": Control, "path": UPath(~type_ == 'com.bytedance.ies.dmt.ui.widget.tablayout.DmtTabLayout$f|X.XWn')/1},
            "videos_tab": {"type": Control, "path": UPath(~type_ == 'com.bytedance.ies.dmt.ui.widget.tablayout.DmtTabLayout$f|X.XWn')/0},
            "sounds_tab": {"type": Control, "path": UPath(~type_ == 'com.bytedance.ies.dmt.ui.widget.tablayout.DmtTabLayout$f|X.XWn') / 2},
            "collect_list": {"type": Control, "path": UPath(id_ == 'collect_list', visible_ == True)},
            "feed_list": {"type": Control, "path": UPath(id_ == 'feed_list', visible_ == True)},
            "video_1": {"type": Control, "path": UPath(id_ == 'feed_list', visible_ == True)/0},
            "video_text": {"type": Control, "path": UPath(type_ == 'X.Ug3')/0/UPath(id_ == 'text1')},
            "collection_group_1": {"type": Control, "path": UPath(id_ == 'collect_list')/0},
            "collections_private_icon": {"type": Control, "path": UPath(id_ == 'collect_list', visible_ == True)/1/UPath(id_ == 'icon_privacy_marker')},
            "collection_public_group_name": {"type": Control, "path": UPath(id_ == 'collect_list', visible_ == True)/1/UPath(id_ == 'name')},
            "find_friends_icon": {"type": Control, "path": UPath(id_ == 'cta_container')/1/UPath(id_ == 'feature_text')},
            "add_friends_icon": {"type": Control, "path": UPath(id_ == 'nav_start', visible_ == True)},
            "add_friends_icon_v2": {"type": Control, "path": UPath(text_ == "Add friends", visible_ == True)},
            # "未同意隐私安全文字提示"
            "tv_msg": {"type": Control, "path": UPath(id_ == 'tv_msg')},
            "translation": {"type": Control, "path": UPath(id_ == 'advanced_content') / UPath(type_ == 'com.bytedance'
                                                                                                       '.tux.button'
                                                                                                       '.TuxButton',
                                                                                              visible_ == True)},
            "create_story_icon": {"type": Control, "path": UPath(id_ == 'cover_story_icon', visible_ == True)},
            "story_ring": {"type": Control, "path": UPath(id_ == 'iv_story_ring', visible_ == True)},
            "story_collection": {"type": Control, "path": UPath(id_ == 'tv_top', visible_ == True)},
            "story_archive": {"type": Control, "path": UPath(text_ == 'Stories archive')},
            "refresh_layout": {"type": Control, "path": UPath(id_ == 'refresh_layout', visible_ == True)},
            "video_post": {"type": Control,
                           "path": UPath(id_ == 'feed_list', visible_ == True) / UPath(id_ == 'cover')},
            "feed_debug": {"type": Control,
                           "path": UPath(id_ == 'group_176', visible_ == True) / UPath(type_ == 'android.view.View')},
            "collection_tab": {"type": Control, 'path': UPath(desc_ == 'Favorites')/UPath(id_ == 'icon')},
            "video_tab": {"type": Control, 'path': UPath(id_ == 'tab_layout') / 0 / 0},
            "favorite_video_title": {"type": Control,
                                     'path': UPath(id_ == 'status_view_flex_layout', visible_ == True)},
            "collection_video_name": {"type": Control, 'path': UPath(id_ == 'title', visible_ == True)},
            "video_guide": {"type": Control, "path": UPath(id_ == 'guide_user_video', visible_ == True)},
            "private_video_guide": {"type": Control,
                                    "path": UPath(id_ == 'status_view_flex_layout', visible_ == True)},
            "post_tab": {"type": Control, "path": UPath(id_ == 'tab_container') / 0},
            "private_tab": {"type": Control, "path": UPath(id_ == 'tab_container') / 1},
            "video_post": {"type": Control,
                           "path": UPath(id_ == "feed_list", visible_ == True) / UPath(id_ == "container",
                                                                                       index=0) / UPath(
                               id_ == "cover")},
            "saved_panel": {"type": Control, "path": UPath(id_ == "tab_container") / 3 },
            "ngo_link": {'path': UPath(id_ == 'advanced_feature_text', visible_ == True)},
            "just_watched": {"type": Control, "path": UPath(id_ == 'just_watched_text', visible_ == True)},
            "suggest_account": {"type": Control, "path": UPath(text_ == "Suggested accounts", visible_ == True)},
            "hide_btn": {"type": Control, "path": UPath(text_ == "Hide", visible_ == True)},
            "creator_tools_enter":{"type": Control, "path": UPath(id_ == 'action_sheet_action_content',
                                            text_ == 'Creator tools')},
            'cover': {'path': UPath(id_ == 'cover')},
            "sound_list": {"type": Control, "path": UPath(type_ == "SparkView") / 0 / 0}
        })

    def check_ngo_link_visible(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['ngo_link'].wait_for_visible(timeout=3, interval=0.5, raise_error=True):
                return True
        return False

    def hide_suggest_account(self):
        if self["suggest_account"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["hide_btn"].click()
            time.sleep(3)

    def find_friends_icon(self):
        if self["add_friends_icon"].wait_for_existing(timeout=5, raise_error=False):
            self["add_friends_icon"].click()
        else:
            self["add_friends_icon_v2"].click()
        time.sleep(2)

    def find_watched_video(self):
        for _ in Retry(timeout=20, raise_error=False):
            if not self["just_watched"].existing:
                self["feed_list"].swipe(y_direction=1, swipe_coefficient=5)
        self["just_watched"].click()
        time.sleep(3)

    def click_ngo_link(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['ngo_link'].wait_for_visible(timeout=3, interval=0.5, raise_error=True):
                return self['ngo_link'].click()

    def click_nth_video_in_my_profile(self, n: int) -> Control:
        self["first_video"].wait_for_visible(timeout=20, interval=1, raise_error=False)
        Control(root=self["content_container"],
                path=UPath(id_ == 'feed_list', visible_ == True) / (n - 1) / UPath(id_ == 'cover')).click()

    # 点击find friends
    def click_find_friends_btn(self):
        if self["find_friends"].wait_for_existing(timeout=5, raise_error=False):
            self["find_friends"].click()

    def into_setting_page(self):
        self["settings_btn"].wait_for_visible()
        self["settings_btn"].click()
        time.sleep(2)

    # "未同意隐私安全文字提示"
    def tv_msg(self, start, end):
        self["tv_msg"].click_text_span(start, end)
        time.sleep(1)

    def click_privacy_videos(self):
        if self["privacy_videos"].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["privacy_videos"].click()
            time.sleep(3)

    def check_videos_panel(self):
        return self["feed_list"].wait_for_existing(timeout=3, raise_error=False)

    def check_collections_panel(self):
        return self["collect_list"].wait_for_existing(timeout=3, raise_error=False)

    def check_video_tab(self):
        if self["video_text"].existing and self["video_text"].text == "Videos":
            return False
        return True

    def click_video_1(self):
        if self["video_1"].wait_for_existing(timeout=3, raise_error=False):
            self["video_1"].click()

    def check_collections_private_icon(self):
        if self["collections_private_icon"].visible:
            return False
        return True

    def return_collection_public_group_name(self):
        return self["collection_public_group_name"].text

    def swipe_collect_list(self):
        self["mine_page"].swipe(y_direction=1, swipe_coefficient=3)

    def click_collection_public_group(self, content):
        item_list = self["collect_list"].items()[1:]
        for item in item_list:
            ch_items = item.children
            for ch_item in ch_items:
                sh_item = ch_item.children
                for th_item in sh_item:
                    if th_item.children[4].text[0:6] == content:
                        return th_item.children[4].click()

    def click_collections_group(self):
        item_list = self["collect_list"].items()[1:]
        for item in item_list:
            item.click()
            if CollectionGroup(root=self.app).check_collection_more_icon():
                break
            self.app.get_device().press_back()
            time.sleep(2)

    def check_tab_arrangement_sequence(self):
        tab_list = self["tab_lists"].items()[0]
        tab_name_list_v2 = ['Posts ', 'Collec', 'Sounds', 'Effect', 'Hashta', 'Produc']
        for item in tab_list.children:
            for children_list in item.children:
                tab_name_list_v1 = children_list.children[0].text[0:6]
                if tab_name_list_v1 in tab_name_list_v2:
                    return True
                return False

    def click_tab_name(self):
        if self["tab_lists"].wait_for_existing(timeout=3, raise_error=False):
            for _ in Retry(limit=2, raise_error=False):
                self.return_sounds_tab().click()
                time.sleep(2)
                self.return_collections_tab().click()
                return self["collect_list"].wait_for_existing(timeout=3, raise_error=False)

    def swipe_tab_name(self):
        if self["tab_lists"].wait_for_existing(timeout=3, raise_error=False):
            self.return_collections_tab().click()
            self["collect_list"].scroll(coefficient_x=0.8)
            return self["sound_list"].wait_for_existing(timeout=3, raise_error=False)

    def user_collection_group(self):
        if self["private_tab"].wait_for_visible(timeout=5, raise_error=False):
            self["private_tab"].click()
            time.sleep(2)
        if self["collection_group_1"].wait_for_visible(timeout=3, raise_error=False):
            self["collection_group_1"].click()
            time.sleep(2)

    def return_videos_tab(self):
        if self["tab_lists"].wait_for_visible(timeout=3, raise_error=False):
            time.sleep(2)
            item = self["tab_lists"].items()[0]
            return item.children[0]

    def return_collections_tab(self):
        if self["tab_lists"].wait_for_visible(timeout=3, raise_error=False):
            time.sleep(2)
            item = self["tab_lists"].items()[0]
            return item.children[1]

    def return_sounds_tab(self):
        if self["tab_lists"].wait_for_visible(timeout=3, raise_error=False):
            time.sleep(2)
            item = self["tab_lists"].items()[0]
            return item.children[2]

    def video_collection(self):
        if self["collection_tab"].wait_for_visible(timeout=3, raise_error=False):
            self["collection_tab"].click()
            time.sleep(2)
            if self["tab_lists"].wait_for_visible(timeout=3, raise_error=False):
                time.sleep(2)
                item = self["tab_lists"].items()[0]
                item.children[1].click()

    def click_profile_video(self):
        if self["collection_tab"].wait_for_visible(timeout=3, raise_error=False):
            self["collection_tab"].click()
            time.sleep(2)
            if self["tab_lists"].wait_for_visible(timeout=3, raise_error=False):
                time.sleep(2)
                item = self["tab_lists"].items()[0]
                item.children[0].click()

    def collection_group(self):
        if self["collection_tab"].wait_for_visible(timeout=3, raise_error=False):
            self["collection_tab"].click()
            time.sleep(2)
        if self["video_tab"].wait_for_visible(timeout=3, raise_error=False):
            self["video_tab"].click()
            time.sleep(2)

    def check_collection_no_video(self):
        if self["favorite_video_title"].wait_for_existing(timeout=3, raise_error=False):
            logger.info("视频收藏列表拉空！")
            return False
        return True

    def click_collection_video(self):
        self.refresh()
        if self["private_first_video"].wait_for_existing(timeout=3, raise_error=False):
            self["private_first_video"].click()

    def check_post_no_video(self):
        if self["video_guide"].wait_for_existing(timeout=3, raise_error=False):
            logger.info("个人页投稿列表拉空！")
            return False
        return True

    def check_private_no_video(self):
        if self["private_video_guide"].wait_for_existing(timeout=3, raise_error=False):
            logger.info("私密列表拉空！")
            return False
        return True

    def check_feeds(self):
        return self["feeds"].wait_for_existing(timeout=5, raise_error=False)

    def click_private_first_video(self):
        self["private_first_video"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["private_first_video"].click()
        time.sleep(3)

    def check_follow_friends(self):
        if self["follow_friends_panel"].wait_for_existing(timeout=5, raise_error=False):
            self["close_follow_panel"].click()
        time.sleep(3)

    def click_first_video(self):
        if self["draft_video"].wait_for_existing(timeout=3, raise_error=False):
            self["first_video"].click()
            DraftBoxPanel(root=self.app).delete_drafts()
        self["first_video"].click()
        time.sleep(2)

    def click_second_video(self):
        if self["draft_video"].wait_for_existing(timeout=3, raise_error=False):
            self["second_video"].click()
            DraftBoxPanel(root=self.app).delete_drafts()
        self["second_video"].click()
        time.sleep(2)

    def click_third_video(self):
        if self["draft_video"].wait_for_existing(timeout=3, raise_error=False):
            self["third_video"].click()
            DraftBoxPanel(root=self.app).delete_drafts()
        self["third_video"].click()
        time.sleep(2)

    def video_add_to_feed(self):
        if self["feed_debug"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_debug"].click()

    def judge_first_video(self):
        return self["first_video"].existing and self["first_video"].visible

    def click_first_draft_video(self):
        if self["first_draft_video"].wait_for_existing(timeout=5, raise_error=False):
            self["first_draft_video"].click()

    def check_pl_post_video_permission(self):
        if self["playlist_items"].wait_for_existing(timeout=5, raise_error=False):
            return True
        else:
            self.app.restart()

    def click_playlist(self):
        if self["playlist_items"].existing:
            if len(self["playlist_items"].children) < 3:
                self["playlist_card"].click()
            else:
                self["playlist_first_card"].click()

    def return_playlist_name(self):
        if self["playlist_items"].existing:
            if len(self["playlist_items"].children) < 3:
                return self["playlist_card"].text
            else:
                return self["playlist_first_card"].text

    def check_playlist_permission(self):
        if self["playlist_empty_content"].existing:
            if self["playlist_empty_doc"].text == "Sort videos into playlists":
                return True

    def check_playlist_name(self, content):
        item_list = self["playlist_items"].items()
        if item_list[1].text == content:
            return True

    def check_playlist_card(self):
        return self["playlist_first_card"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def refresh_playlist(self):
        playlist = self["playlist_items"]
        playlist.refresh()
        time.sleep(2)

    def check_delete_playlist_name(self):
        playlist_item = self["playlist_items"]
        item_list = playlist_item.items()
        text1 = item_list[1].text
        return text1

    def check_playlist_item(self):
        return self["playlist_items"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def swipe_playlist_card(self):
        if self["playlist_items"].existing:
            if len(self["playlist_items"].children) > 3:
                self["playlist_items"].swipe(x_direction=1, swipe_coefficient=3)

    def check_bind_phone_or_email_popup(self):
        return self["bind_phone_or_email_yes"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)

    def bind_phone_or_email_yes(self):
        self["bind_phone_or_email_yes"].click()

    def bind_phone_or_email_no(self):
        self["bind_phone_or_email_no"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["bind_phone_or_email_no"].click()

    def bind_phone_or_email_title_check(self):
        print(self["bind_phone_or_email_title"].text)
        return self["bind_phone_or_email_title"].text == "Your account needs to be updated"

    def bind_phone_or_email_text_check(self):
        print(self["bind_phone_or_email_text"].text)
        return self["bind_phone_or_email_text"].text == "To keep your account secure, link your phone number or " \
                                                        "email address before you switch accounts."

    def judge_mine_page(self):
        if self.app.current_activity not in self.window_spec["activity"]:
            return False
        self.refresh()
        for _ in Retry(timeout=5, interval=0.1, raise_error=False):
            if self["settings_btn"].existing:
                return True
            elif self["mine_page"].existing:
                return True
            elif self["sign_up"].existing:
                return True
        self.app.testcase.log_info("judge mine page failed")
        return False

    def get_user_id(self, timeout=20):
        count_num = int(timeout / 2)
        while self["user_id"].text == "" and count_num > 0:
            self["user_id"].refresh()
            time.sleep(3)
            count_num -= 1
        return self["user_id"].text

    def switch_account(self):
        if self["follow_your_friends_page"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["right_top_cancel"].click()
        self["add_account_entrance"].click()
        self.refresh()
        self['switch_account_2'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self['switch_account_2'].click()

    def signup(self):
        if self["sign_up"].wait_for_existing(timeout=3, raise_error=False):  # 有时候不会出现 sigup 按钮
            self["sign_up"].click()

    def add_account(self):
        if self["follow_your_friends_page"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            self["right_top_cancel"].click()
        self["add_account_entrance"].click()
        self.refresh()
        self['add_account_btn'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self['add_account_btn'].click()

    def click_add_account(self):
        self["add_account_entrance"].wait_for_existing(raise_error=False)
        self["add_account_entrance"].click()
        self.refresh()
        self["add_account_text"].click()

    def private_account_text_exist(self):
        if self["private_account_text"].wait_for_existing(timeout=2, interval=0.5, raise_error=False):
            return True
        else:
            return False

    def privacy_and_settings_entry_click(self):
        self['privacy_and_settings_entry'].click()

    @property
    def my_video_list(self):
        return self["feeds"].items()

    def open_my_video(self, index=0):
        video_list = self["feeds"].items()
        video_list[index].click()

    def edit_profile(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["edit_profile_btn"].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return self["edit_profile_btn"].click()
        return self["edit_profile_btn1"].click()

    def avatar_click(self):
        self["avatar"].click()
        time.sleep(1)

    def enter_favourite_list(self):
        self["my_favourite"].click()

    def open_liked_video(self, index=0):
        self["liked"].click()
        video_list = self["feeds"].items()
        video_list[index].click()

    def enter_settings_page(self):
        if self["follow_your_friends_page"].wait_for_existing(timeout=10, interval=1, raise_error=False):
            self["right_top_cancel"].click()
        # self["more_btn"].click()
        if self["settings_btn"].existing:
            self["settings_btn"].click()
        elif self["settings_btn_logged"].existing:
            self["settings_btn_logged"].click()
        if self["settings_pop_up"].wait_for_existing(timeout=10, interval=1, raise_error=False):
            self["settings_enter"].click()

    def enter_creator_tools(self):
        if self["follow_your_friends_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["right_top_cancel"].click()
        if self["settings_btn"].existing:
            self["settings_btn"].click()
        elif self["settings_btn_logged"].existing:
            self["settings_btn_logged"].click()
        if self["settings_pop_up"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["creator_tools_enter"].click()

    def wait_for_drafts_visible(self):
        first_video = self.my_video_list[0]
        return first_video.is_draft()

    def playlist_add_button_is_visible(self):
        return self["playlist_add_button"].visible

    def click_add_playlist_button(self):
        self["playlist_add_button"].click()

    def close_sort_video_playlist_banner(self):
        if self['close_sort_video_playlist_x_button'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self['close_sort_video_playlist_x_button'].click()

    def click_following(self):
        self["following"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["following"].click()

    def into_translation_page(self):
        if self["follow_your_friends_page"].wait_for_visible(timeout=2, interval=0.5, raise_error=False):
            self["right_top_cancel"].click()
        time.sleep(2)
        self["translation"].click()

    def click_create_story_icon(self):
        if self["create_story_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["create_story_icon"].click()

    def click_story_avatar(self):
        self["story_ring"].click()
        time.sleep(6)

    def enter_story_collection(self):
        self["story_collection"].click()
        time.sleep(8)

    def enter_story_archive(self):
        self["story_archive"].click()
        time.sleep(1)

    def get_story_count(self):
        string = self["story_collection"].text
        start = string.find('(') + 1
        end = string.find(')')
        count = int(string[start:end])
        return count

    def refresh_profile(self):
        self["refresh_layout"].swipe(y_direction=-1, swipe_coefficient=8)

    def click_video_post(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["video_post"].wait_for_visible(timeout=10, interval=1, raise_error=False):
                return self["video_post"].click()

    def enter_saved_panel(self):
        if self['saved_panel'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self['saved_panel'].click()

    def click_if_cover_exist(self):
        if self['cover'].wait_for_visible(timeout=5, raise_error=False):
            self['cover'].click()


class VideoList(FeedsList):
    elem_path = UPath(id_ == "container")


class ChallengeList(FeedsList):
    elem_path = UPath(id_ == "challenge_item_ll")


class Music(Control):
    def get_locators(self):
        return {
            "music_detail_iv": {"path": UPath(id_ == "music_detail_iv")}
        }

    def click(self, offset_x=0, offset_y=0):
        self["music_detail_iv"].click()


class MusicList(FeedsList):
    elem_path = UPath(id_ == "music_item_ll")
    elem_class = Music


class UserFavoritesPanel(BasePanel):
    window_spec = {"activity": "com.ss.android.ugc.aweme.favorites.ui.UserFavoritesActivity"}
    favorite_type_map = {"VIDEOS": 0, "HASHTAGS": 1, "SOUNDS": 2, "EFFECTS": 3}
    favorite_list_map = {
        "VIDEOS": "video_list",
        "HASHTAGS": "challenge_list",
        "SOUNDS": "sound_list",
        "EFFECTS": "effect_list"
    }

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(UserFavoritesPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "favorite_tab": {"type": Control, "path": UPath(id_ == 'tab_layout') / 0},
            "loading_view": {"type": Control, "path": UPath(id_ == 'progressBarLayout', visible_ == True)},
            "video_list": {"type": VideoList, "path": UPath(id_ == "feed_list", visible_ == True)},
            "challenge_list": {"type": ChallengeList, "path": UPath(id_ == "collect_list", visible_ == True)},
            "sound_list": {"type": MusicList, "path": UPath(id_ == "collect_list", visible_ == True)},
            "effect_list": {"type": FeedsList, "path": UPath(id_ == "collect_list", visible_ == True)},
        })

    def get_favorite_list(self, favorite_type):
        """返回指定收藏类型的列表
        """
        if favorite_type not in self.favorite_type_map:
            raise RuntimeError("收藏类型不正确")
        # 先进入对应收藏列表
        favorite_tab = Control(root=self["favorite_tab"], path=UPath(index=self.favorite_type_map[favorite_type]))
        favorite_tab.click()
        self["loading_view"].wait_for_invisible(timeout=10, raise_error=False)
        lst = self[self.favorite_list_map[favorite_type]]
        lst.refresh()
        return lst.items()


class ProfileEditPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.profile.ui.ProfileEditActivity.*"}

    def get_locators(self):
        return {
            "bio": {"type": Control, "path": UPath(id_ == 'bio_tux_cell') / UPath(id_ == 'label_tv')},
            "bio_text": {"type": Control, "path": UPath(id_ == 'et_input')},
            "save": {"type": Control, "path": UPath(id_ == 'nav_end', visible_ == True)},
            "change_image": {"type": Control, "path": UPath(id_ == 'image')},
            "take_photo": {"type": Control, "path": UPath(id_ == 'actionsheet_actiongroups_container') / 0},
            'nonprofit': {"type": Control, "path": UPath(id_ == 'nonprofit_tux_cell') / UPath(id_ == 'label_tv')},
            "remove": {"type": Control, "path": UPath(id_ == "actionsheet_actiongroups_container") / 2}
        }

    @property
    def bio(self):
        return self["bio"].text

    def modify_bio(self, new_bio):
        self["bio"].click()
        self["bio_text"].text = new_bio
        time.sleep(3)
        self["save"].click()
        time.sleep(3)

    def change_image(self):
        self["change_image"].click()
        time.sleep(1)

    def take_photo(self):
        self["take_photo"].click()
        time.sleep(1)

    def click_select_nonprofit(self):
        for _ in Retry(timeout=5, interval=0.5, raise_error=False):
            if self['nonprofit'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self['nonprofit'].click()
                return True
        return False

    def check_nonprofit_added(self):
        for _ in Retry(timeout=5, interval=0.5, raise_error=False):
            if self['nonprofit'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                if self['nonprofit'].text != "Add nonprofit to your profile":
                    return True
        return False

    def remove_nonprofit(self):
        for _ in Retry(timeout=5, interval=0.5, raise_error=False):
            if self['remove'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self['remove'].click()
                return True
        return False

class I18nSettingPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.setting.ui.I18nSettingNewVersionActivity"}

    def get_locators(self):
        return {
            "content": {"type": ScrollView, "path": UPath(id_ == 'root') / 2},
            "feedback": {"type": Control, "root": "content", "path": UPath(id_ == 'feedback_and_help')},
        }

    def report_problem(self):
        self["feedback"].click()


class DraftList(FeedsList):
    elem_path = UPath(id_ == "draft_cell")


class DraftListTableViewCell(Control):
    elem_class = Control
    elem_path = UPath(id_ == "container")


class DraftListCollectionViewCell(Control):
    elem_class = Control
    elem_path = UPath(id_ == "draft_cell")


class DraftBoxPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.tools.draft.DraftBoxActivity"}

    def get_locators(self):
        return {
            "draft_list": {"type": DraftList, "path": UPath(id_ == "recycler_view")},
            # Rendering of draft videos is currently under A/B testing hence required different selectors
            "draft_video_list_table_cell": {"type": DraftListTableViewCell, "path": UPath(id_ == "recycler_view")},
            "draft_video_list_collection_cell": {"type": DraftListCollectionViewCell,
                                                 "path": UPath(id_ == "recycler_view")},
            "select_btn": {"type": Control, "path": UPath(id_ == "right_btn")},
            "delete_btn": {"type": Control, "path": UPath(id_ == "tv_delete")},
            "confirm_delete_btn": {"type": Control, "path": UPath(text_ == "Delete", visible_ == True)},
            "draft_list_cell": {"type": Control, "path": UPath(id_ == 'cover', visible_ == True)}
        }

    def edit_draft(self, index=0):
        drafts = self["draft_list"].items()
        drafts[index].click()

    def get_draft_video_list_table_cell(self):
        return self.draft_video_list_table_cell.items()

    def get_draft_video_list_collection_cell(self):
        return self.draft_video_list_collection_cell.items()

    def click_video_in_draft_list(self, index=0):
        # Rendering of draft videos is currently under A/B testing hence required different selectors
        self["draft_video_list_table_cell"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        if len(self["draft_video_list_table_cell"].items()) > 0:
            list_of_draft_videos = self.get_draft_video_list_table_cell()
        elif len(self["draft_video_list_collection_cell"].items()) > 0:
            list_of_draft_videos = self.get_draft_video_list_collection_cell()
        # Ensure the index to click is within the range
        if index < len(list_of_draft_videos):
            list_of_draft_videos[index].wait_for_visible(timeout=5, interval=1, raise_error=False)
            list_of_draft_videos[index].click()
        else:
            raise Exception("Index provided to open the draft video is out of bounds - ", index)

    def delete_drafts(self, index=None):
        drafts = self["draft_list"].items()
        self["select_btn"].click()
        if isinstance(index, int) and index < len(drafts):
            drafts[index].click()
        else:
            for draft in drafts:
                draft.click()
        self["delete_btn"].wait_for_visible()
        self["delete_btn"].click()
        self["confirm_delete_btn"].click()

    def click_draft_list_cell(self):
        self["draft_list_cell"].click()
        time.sleep(1)


class AvatarPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "change_frame": {"type": Control, "path": UPath(id_ == 'profile_widget_cta')},
            "frame_title": {"type": TextView, "path": UPath(id_ == 'profile_widget_title')},
            "frame_subtitle": {"type": TextView, "path": UPath(id_ == 'profile_widget_subtitle')},
            "bio_text": {"type": Control, "path": UPath(id_ == 'et_input')},
            "save": {"type": Control, "path": UPath(text_ == "Save")},
        }
    def modify_bio(self, new_bio):
        self["bio_text"].text = new_bio
        time.sleep(3)
        self["save"].click()
        time.sleep(3)

    @property
    def frame_subtitle(self):
        return self["frame_subtitle"].text

    def frame_title(self):
        return self["frame_title"].text

    def change_frame(self):
        self["change_frame"].click()
        time.sleep(2)

    def cancel(self, new_bio):
        self["cancel"].click()
        time.sleep(2)


class ChangeFramePanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "change_frame_title": {"type": TextView, "path": UPath(id_ == 'edit_badge_title')},
            "event_title": {"type": TextView, "path": UPath(id_ == 'edit_badge_event_title')},
            "event_date": {"type": TextView, "path": UPath(id_ == 'edit_badge_event_date')},
            "item": {"type": Control, "path": UPath(id_ == 'edit_badge_item_layout')},
            "save": {"type": Control, "path": UPath(id_ == 'text_right')},
            "x_button": {"type": Control, "path": UPath(id_ == 'edit_badge_x_button')},
        }

    @property
    def cancel(self):
        return ""

    def change_frame_title(self):
        return self["change_frame_title"].text

    def event_title(self):
        return self["event_title"].text

    def event_date(self):
        return self["event_date"].text

    def click_x_button(self):
        self["x_button"].click()
        time.sleep(2)


class CreatePlaylistPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.mix.createmix.CreatePlaylistActivity"}

    def get_locators(self):
        return {
            "name_Playlist_title": {"path": UPath(id_ == 'doc_title', visible_ == True)},
            "enter_playlist_name_texbox": {"path": UPath(id_ == 'et_input', visible_ == True)},
            "next_button": {"path": UPath(id_ == 'button', visible_ == True)},
            "first_video_unavailable": {
                "path": UPath(id_ == 'pw_list_video') / 1 / UPath(id_ == 'candidate_unavailable')},
            "video_list": {"type": Control, "path": UPath(id_ == 'pw_list_video')},
            "first_video": {
                "path": UPath(id_ == 'pw_list_video') / 1 / UPath(id_ == 'candidate_checkbox', visible_ == True)},
            "second_video_unavailable": {
                "path": UPath(id_ == 'pw_list_video') / 2 / UPath(id_ == 'candidate_unavailable')},
            "second_video": {
                "path": UPath(id_ == 'pw_list_video') / 2 / UPath(id_ == 'candidate_checkbox', visible_ == True)},
            "third_video": {
                "path": UPath(id_ == 'pw_list_video') / 3 / UPath(id_ == 'candidate_checkbox', visible_ == True)},
            "fourth_video": {
                "path": UPath(id_ == 'pw_list_video') / 4 / UPath(id_ == 'candidate_checkbox', visible_ == True)},
            "third_video_unavailable": {
                "path": UPath(id_ == 'pw_list_video') / 3 / UPath(id_ == 'candidate_unavailable')},
            "next": {"path": UPath(id_ == 'next', visible_ == True)},
            "create_playlist": {"path": UPath(id_ == 'mix_btn_done', visible_ == True)},
            "playlist_title": {"path": UPath(id_ == 'tv_mix_detail_title', visible_ == True)},
            "playlist_video_list": {"type": Control, "path": UPath(id_ == 'pw_list_video')},
            "select_playlist_video": {"type": Control,
                                      "path": UPath(id_ == 'pw_list_video') / 1 / UPath(id_ == 'candidate_checkbox')},
            "name_playlist": {"type": Control, "path": UPath(id_ == 'doc_title')},
            "playlist_btn_add": {"type": Control, "path": UPath(id_ == 'btn_add', visible_ == True)}
        }

    def check_playlist_title_exists(self):
        return self["name_Playlist_title"].visible

    def enter_playlist_name(self, desc):
        self["enter_playlist_name_texbox"].input(desc)

    def click_next_button(self):
        time.sleep(3)
        self["next_button"].click()
        if self["playlist_btn_add"].wait_for_existing(timeout=5, raise_error=False):
            self["playlist_btn_add"].click()

    def select_video(self):
        time.sleep(2)
        for item in self["video_list"].children[1:]:
            item_mask = Control(root=item, path=UPath(id_ == 'candidate_unavailable'))
            if not item_mask.visible:
                item_checkbox = Control(root=item, path=UPath(id_ == 'candidate_checkbox'))
                item_checkbox.click()
                break
        time.sleep(2)
        self["next"].click()

    def get_playlist_video(self):
        video_list = self["playlist_video_list"]
        if self["first_video_unavailable"].visible == False:
            self["select_playlist_video"].click()
        else:
            swipe_cnt = 0
            chosen_video_cnt = 0
            while chosen_video_cnt < 1 and swipe_cnt < 5:
                playlist_item = video_list.items()
                for item in playlist_item[1:]:
                    check_box = Control(root=item, path=UPath(id_ == 'candidate_checkbox'))
                    for children in item.children:
                        playlist_path = children.elem_info['view_path']
                        playlist_path_len = playlist_path[-21:-1]
                        if playlist_path_len == "candidate_unavailabl":
                            if children.visible == False:
                                check_box.click()
                                chosen_video_cnt += 1
                    if chosen_video_cnt >= 1:
                        break
                self.swipe(y_direction=1, swipe_coefficient=3)
                video_list.refresh()
                video_list.wait_for_ui_stable()
                swipe_cnt += 1

    def click_next(self):
        self["next"].click()

    def check_name_playlist(self):
        return self["name_playlist"].wait_for_existing(timeout=5, raise_error=False)

    def create_playlist(self):
        if self["create_playlist"].wait_for_visible(timeout=5, interval=0.1, raise_error=False):
            self["create_playlist"].click()


class PlaylistPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.mix.videodetail.MixVideoDetailActivity|com.ss.android.ugc.aweme.mix.videodetail.MixVideoDetailActivity#1"}

    def get_locators(self):
        return {
            "playlist_title": {"path": UPath(id_ == 'nav_bar_title', visible_ == True)},
            "playlist_settings": {"path": UPath(id_ == 'nav_layout') / UPath(id_ == 'nav_end', visible_ == True)},
            "delete_playlist": {"path": UPath(~text_ == 'Delete playlist.*', visible_ == True)},
            "delete_button": {"path": UPath(text_ == 'Delete', visible_ == True)},
            "playlist_icon": {"type": Control, "path": UPath(id_ == 'feed_report_warn_ll')},
            "back_button": {"type": Control, "path": UPath(id_ == 'nav_start', visible_ == True)},
            "playlist_manage": {"type": Control, "path": UPath(id_ == 'nav_end', visible_ == True) / UPath(
                type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "playlist_management_panel": {"type": Control, "path": UPath(id_ == 'actionsheet_actiongroups_container')},
            "edit_playlist": {"type": Control, "path": UPath(id_ == 'actionsheet_actiongroups_container') / 0 / UPath(
                id_ == 'action_sheet_action_content')},
            "change_playlist_name": {"type": Control,
                                     "path": UPath(id_ == 'actionsheet_actiongroups_container') / 2 / UPath(
                                         id_ == 'action_sheet_action_content')},
            "delete_playlist_icon": {"type": Control, "path": UPath(text_ == 'Delete playlist', visible_ == True)},
            "return_btn": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "playlist_video_icon": {"type": Control, "path": UPath(id_ == 'cover_iv')},
            "mix_detail_dialog": {"type": Control, "path": UPath(id_ == 'mix_detail_dialog')},
            "playlist_no_videos": {"type": Control, "path": UPath(id_ == 'visual_area') / 0},
            "confirm_no_video": {"type": Control, "path": UPath(text_ == 'OK')},
            "playlist_text": {"type": Control, "path": UPath(id_ == "root_bottom_banner", visible_ == True) / UPath(type_ == "TuxTextView")},
            "ba_leadgen_btn": {"type": Control, "path": UPath(id_ == 'common_anchor_title', text_ == 'Get quote')},
            "live_anchor": {"type": Control, "path": UPath(id_ == 'anchor_container') / 1 / UPath(id_ == 'common_anchor_title')},
            "game_anchor_v1": {"type": Control, "path": UPath(id_ == 'anchor_container') / 2 / UPath(id_ == 'common_anchor_title')},
            "webview_icon": {"type": Control, "path": UPath(id_ == 'debug_info_tag', visible_ == True)},
            "see_translation_btn": {"type": Control, "path": UPath(id_ == 'interact_info_area')/10/UPath(type_ == 'com.bytedance.tux.input.TuxTextView', visible_ == True)},
        }

    def getPlaylistTitle(self):
        time.sleep(3)
        return self["playlist_title"].text

    def check_see_translation_btn(self):
        return self["see_translation_btn"].wait_for_existing(timeout=5, raise_error=False)

    def check_webview_icon(self):
        return self["webview_icon"].wait_for_existing(timeout=5, raise_error=False)

    def check_anchor_list(self):
        if self["ba_leadgen_btn"].wait_for_existing(timeout=5, raise_error=False):
            if self["live_anchor"].text[0:10] == "LIVE Event":
                if self["game_anchor_v1"].text == "Play Love Tester on TikTok":
                    return True
        return False

    def click_ba_leadgen_btn(self):
        self["ba_leadgen_btn"].click()

    def click_live_anchor(self):
        self["live_anchor"].click()

    def click_game_anchor_v1(self):
        self["game_anchor_v1"].click()

    def return_playlist_text(self):
        return self["playlist_text"].text

    def click_return_btn(self):
        self["return_btn"].click()
        time.sleep(3)

    def delete_playlist(self):
        if self["playlist_manage"].wait_for_existing(timeout=5, raise_error=False):
            self["playlist_manage"].click()
        self["delete_playlist_icon"].click()
        if self["delete_button"].wait_for_existing(timeout=5, raise_error=False):
            self["delete_button"].click()

    def click_playlist_icon(self):
        self["playlist_icon"].click()
        time.sleep(3)

    def click_back_button(self):
        self["back_button"].click()

    def click_playlist_manage_icon(self):
        if self["playlist_no_videos"].wait_for_existing(timeout=5, raise_error=False):
            self["confirm_no_video"].click()
        else:
            self["playlist_manage"].click()

    def check_playlist_no_videos(self):
        if self["playlist_no_videos"].wait_for_existing(timeout=5, raise_error=False):
            return True

    def check_icon(self):
        return self["playlist_manage"].wait_for_existing(timeout=5, raise_error=False)

    def check_playlist_panel(self):
        return self["playlist_management_panel"].wait_for_existing(timeout=5, raise_error=False)

    def check_panel_text(self):
        text1 = self["edit_playlist"].text
        text2 = self["change_playlist_name"].text
        text3 = self["delete_playlist_icon"].text
        # if self["playlist_management_panel"].existing:
        if text1 == "Edit playlist" and text2 == "Change playlist name" and text3 == "Delete playlist":
            return True

    def return_profile_page(self):
        self["back_button"].click()
        time.sleep(2)
        self["return_btn"].click()

    def check_back_button(self):
        return self["back_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_playlist_video_icon(self):
        return self["playlist_video_icon"].wait_for_existing(timeout=5, raise_error=False)

    def close_playlist_panel(self):
        time.sleep(3)
        self["mix_detail_dialog"].swipe(y_direction=-1, swipe_coefficient=8)

    def swipe_mix_detail_dialog(self):
        time.sleep(3)
        self["mix_detail_dialog"].swipe(y_direction=1, swipe_coefficient=3)


class FollowingFriendsPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.following.ui.FollowRelationTabActivity"}

    def get_locators(self):
        return {
            "friend_text": {"path": UPath(text_ == 'Friends')},
            "cduiauto002_username": {"type": Control, "path": UPath(text_ == 'cduiauto002', id_ == 'txt_user_name')},
            "cduiauto001_username": {"type": Control, "path": UPath(text_ == 'cduiauto001', id_ == 'txt_user_name')},
            "following_friend": {
                "type": Control,
                "path": UPath(id_ == 'rv_list') / 0},
            "close_suggest_user_btn": {
                "type": Control,
                "path": UPath(id_ == 'deleteIconView')},
            "follow_btn": {
                "type": Control,
                "path": UPath(text_ == 'Follow')},
            "following_btn": {
                "type": Control,
                "path": UPath(id_ == 'relationBtn', text_ == 'Following')},
            "chentest1678_username": {
                "type": Control,
                "path": UPath(text_ == 'chentest1678')},
            "chentest001_username": {
                "type": Control,
                "path": UPath(text_ == 'ChenTest001')},
            "username_desc": {
                "type": Control,
                "path": UPath(id_ == 'txt_desc')},
            "check_caption_account_follower": {"type": TextView, "path": UPath(id_ == 'txt_user_name',
                                                                               text_ == 'cla_automation_creator01')},
            "see_translation_account_follower": {"type": TextView, "path": UPath(id_ == 'txt_user_name',
                                                                                 text_ == 'cla_automation_creator02')}
        }

    def click_friend(self):
        self["following_friend"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["following_friend"].click()

    def choose_account_follower(self, test_case_name):
        if test_case_name == "check_caption":
            self["check_caption_account_follower"].wait_for_visible(timeout=5, interval=1, raise_error=False)
            self["check_caption_account_follower"].click()
        elif test_case_name == "check_see_translation":
            self["see_translation_account_follower"].wait_for_visible(timeout=5, interval=1, raise_error=False)
            self["see_translation_account_follower"].click()
        else:
            self.log_record("Account Follower Element NOT found!")

    def close_user(self):
        self["close_suggest_user_btn"] \
            .wait_for_visible(timeout=5, interval=1, raise_error=True)
        self["close_suggest_user_btn"].click()

    def unfollow_following_user(self):
        value = self["following_btn"] \
            .wait_for_existing(timeout=3, interval=0.5, raise_error=False)
        if value is True:
            logger.info("Found following user")
            self["following_btn"].click()
        else:
            logger.info("Didn't find following user")

    def click_chentest001_username(self):
        self["chentest001_username"] \
            .wait_for_visible(timeout=3, interval=0.5, raise_error=False)
        self["chentest001_username"].click()

    def click_chentest1678_username(self):
        self["chentest1678_username"] \
            .wait_for_visible(timeout=3, interval=0.5, raise_error=True)
        self["chentest1678_username"].click()

    def click_following_btn(self):
        self["following_btn"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["following_btn"].click()

    def click_follow_btn(self):
        self["follow_btn"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["follow_btn"].click()

    def is_following_user(self):
        '''
        防止有未取关的用户影响埋点结果
        '''
        # 如果有未取关的好友，会存在desc
        value = self["username_desc"] \
            .wait_for_existing(timeout=5, interval=0.5, raise_error=False)
        if value is True:
            logger.info("Found Following user")
            self["username_desc"].click()
            return value
        else:
            logger.info("Didn't find following user")
        return value


class VideoProfilePage(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "content_container": {"type": Control, "path": UPath(id_ == 'content_container', visible_ == True)},
            "first_video": {"path": UPath(id_ == 'feed_list') / 0 / UPath(id_ == 'cover', visible_ == True)},
            "show_suggestion_btn": {"path": UPath(id_ == 'recommend_users')},
            "follow_btn": {"path": UPath(id_ == 'profile_btn_extra')},
            "unfollow_btn": {
                "type": Control,
                "path": UPath(id_ == 'follow_iv')},
            "msg_btn": {
                "type": Control,
                "path": UPath(id_ == 'send_message_btn')},
            "follow_btn_under_suggestion": {
                "type": Control,
                "path": UPath(text_ == 'Follow')},
            "following_btn_under_suggestion": {
                "type": Control,
                "path": UPath(id_ == 'relationBtn', text_ == 'Following')},
            "first_permission_close_btn": {
                "type": Control,
                "path": UPath(
                    type_ == 'com.ss.android.ugc.aweme.social.widget.card.view.LegacyPermissionLayout') / 0 / UPath(
                    id_ == 'permission_delete_btn')},
            "only_permission_close_btn": {"path": UPath(id_ == 'permission_delete_btn')},
            "view_video": {"path": UPath(id_ == 'cover')},
            "title": {"path": UPath(id_ == 'nav_bar_title')},
            "story_list": {"type": StoryList, "path": UPath(id_ == 'story_list')},
            "view_video": {"type": Control, "path": UPath(id_ == 'cover')},
            "back_button": {"path": UPath(id_ == 'nav_start', visible_ == True)},
            "video_icon": {"path": UPath(desc_ == "Videos") / UPath(id_ == "icon")},

        }

    def click_nth_video_in_others_profile(self, n: int):
        self["first_video"].wait_for_visible(timeout=40)
        video = Control(root=self['content_container'],
                        path=UPath(id_ == 'feed_list', visible_ == True) / (n - 1) / UPath(id_ == 'cover'))
        video.click()

    def view_video(self):
        self["view_video"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["view_video"].click()

    def click_first_video(self):
        """
        点击第一个视频后，跳转没有成功，则循环点击，先点video列表，再点视频
        """
        self["first_video"].wait_for_visible(timeout=40)
        self["first_video"].click()
        for _ in Retry(timeout=60, interval=1, raise_error=False):
            if not self["video_icon"].wait_for_invisible(timeout=4, raise_error=False):
                self["video_icon"].click()
                self["first_video"].wait_for_visible()
                self["first_video"].click()
            else:
                break
        time.sleep(2)

    def click_follow_btn(self):
        self["follow_btn"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["follow_btn"].click()

    def click_unfollow_btn(self):
        self["unfollow_btn"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["unfollow_btn"].click()

    def click_msg_btn(self):
        self["msg_btn"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["msg_btn"].click()

    def click_show_suggestion_btn(self):
        self["show_suggestion_btn"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        self["show_suggestion_btn"].click()

    def click_follow_btn_under_suggestion(self):
        self["follow_btn_under_suggestion"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["follow_btn_under_suggestion"].click()

    def click_follwoing_btn_under_suggestion(self):
        self["following_btn_under_suggestion"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["following_btn_under_suggestion"].click()

    def click_permission_close_btn(self):
        time.sleep(2)
        value = self["first_permission_close_btn"] \
            .wait_for_existing(timeout=3, interval=1, raise_error=False)
        if value:
            self["first_permission_close_btn"].click()

        time.sleep(2)
        value = self["only_permission_close_btn"] \
            .wait_for_existing(timeout=3, interval=1, raise_error=False)
        if value:
            self["only_permission_close_btn"].click()

    def play_archive_story(self, index):
        self["story_list"].items()[index].click()

    def click_back_button(self):
        self["back_button"].click()


class StoryList(Control):
    elem_class = Control
    elem_path = UPath(id_ == 'iv_cover', visible_ == True)


class VideoProfileList(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity"}

    def get_locators(self):
        return {
            "show_video": {"type": Control, "path": UPath(id_ == 'feed_list', visible_ == True) / 0}
        }

    def show_video(self):
        self["show_video"].wait_for_visible(timeout=5, interval=1, raise_error=False)
        self["show_video"].click()

class DonationsPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.detail.ui.DetailActivity|com.ss.android.ugc.aweme.splash.SplashActivity.*"}

    def get_locators(self):
        return {
            "donate_button": {"type": Control, "path": UPath(id_ == 'donate', visible_ == True)},
            "donation_summary": {"type": Control, "path": UPath(id_ == 'donation_number')},
            "donation_title": {"type": Control, "path": UPath(text_ == 'Donations', visible_ == True)},
            "close_button": {"type": Control,
                             "path": UPath(id_ == "header") / UPath(id_ == "back_btn")},
            "powered_by_footer": {'type': Control, "path": UPath(id_ == "powered_by")},
            "ngo_avatar": {"type": Control, "path": UPath(id_ == "ngo_avatar")},
            "ngo_title": {"type": Control, "path": UPath(id_ == "ngo_info")},
            "ngo_description": {'type': Control, "path": UPath(id_ == "ngo_desc")}
        }

    def donation_summary_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["donation_summary"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return True
        return False

    def donor_panel_title_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["donation_title"].wait_for_visible(timeout=5, interval=1, raise_error=False):
                return True
        return False

    def donate_button_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['donate_button'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return True
        return False

    def powered_by_footer_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['powered_by_footer'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['powered_by_footer'].text
        return False

    def ngo_avatar_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['ngo_avatar'].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
                return True
        return False

    def ngo_title_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['ngo_title'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return True
        return False

    def ngo_description_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['ngo_description'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return True
        return False

    def donate_button_click(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['donate_button'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self['donate_button'].click()

    def click_close_button(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["close_button"].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                return self["close_button"].click()

class TiltifyPageWebview(Webview):
    """webview use inject
    """
    view_spec = {
        "description": "\"attached\":true.*\"visible\":true"
    }

    def get_locators(self):
        return {
            'learn_more': {'type': WebElement, 'path': UPath(type_ == 'DIV', text_ == 'Learn more')},
            'tiltify_footer': {'type': WebElement, 'path': UPath(type_ == 'DIV', text_ == 'Tiltify')},
            'back_to_donation': {'type': WebElement, 'path': UPath(type_ == "DIV", text_ == "Back to donation")}
        }

    def click_learn_more(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['learn_more'].wait_for_existing(timeout=3, interval=0.5, raise_error=True):
                return self['learn_more'].click()

    def click_tiltify_footer(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['tiltify_footer'].wait_for_existing(timeout=3, interval=0.5, raise_error=True):
                return self['tiltify_footer'].click()

    def click_back_to_donation_btn(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['back_to_donation'].wait_for_existing(timeout=3, interval=0.5, raise_error=True):
                return self['back_to_donation'].click()

class TiltifyPage(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.donation.webpage.DonationWebPageDialogActivity"}

    def get_locators(self):
        return {
            "close_button": {"type": Control, "path": UPath(id_ == 'btn_share')},
            "donation_form_title": {"type": Control, "path": UPath(id_ == 'title', text_ == 'Donations')},
            'tiltify_donation_form': {'type': TiltifyPageWebview, "path": UPath(id_ == "webview_root")},
            'close_custom': {'path': UPath(id_ == 'close_custom')},
        }

    def get_tiltify_page_scene(self):
        return UIScene(self).dump()

    def donation_form_title_exists(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["donation_form_title"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return True
        return False

    def close_tiltify_page(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["close_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return self["close_button"].click()

    def get_tiltify_donation_form(self) ->TiltifyPageWebview:
        return self['tiltify_donation_form']

    def click_close_custom(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["close_custom"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return self['close_custom'].click()

class SavedPanel(Window):
    """A class for the Saved Video panel and its elements"""
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.detail.ui.DetailActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity|com.ss.android.ugc.aweme.wiki.AddWikiActivity"}

    def get_locators(self):
        return {
            "collections": {"type": TextView, "path": UPath(id_ == "tab_layout") / 0 / 1 / UPath(id_ == "text1")},
            "first_collection": {"type": Control,
                                 "path": UPath(id_ == "name", text_ == "Shoots automation collection")},
            "collection_name": {"type": TextView, "path": UPath(id_ == "collection_name")},
            "v30_video_in_collection": {"type": Control, "path": UPath(id_ == "collect_detail") / 1 / 0 / 1},
            "v31_video_in_collection": {"type": Control, "path": UPath(id_ == "collect_detail") / 2 / 0 / 1},
            "insights_more_data": {"type": TextView, "path": UPath(id_ == "tv_insights_more_data")},
            "title": {"type": TextView, "path": UPath(id_ == "title")},
            "user_id": {"type": TextView, "path": UPath(id_ == "user_id")},
            "back_button": {"type": Control, "path": UPath(id_ == "back_btn")},
            'tagged_label': {'path': UPath(text_ == "You're tagged")},
            'mentioned_comment': {'path': UPath(id_ == "content", text_ == '\u200e那是纳斯达克的呢😅')},
            'mentioned_video': {'path': UPath(id_ == "content", text_ == "\u200e被大喊大叫的")},
            'favorite_like_video': {'path': UPath(id_ == 'favorite_count')},
            'digg_like_story': {'path': UPath(id_ == "digg_count")},
            'duet_tag': {'path': UPath(id_ == 'desc')},
            'close_story_tianchuang': {'path': UPath(id_ == 'close_iv')},
            'add_widget_btn': {'path': UPath(id_ == 'add_widget_btn')},
            'widget_btn_end': {'path': UPath(id_ == 'nav_end', visible_ == True) / 0},
            'comment_image': {'path': UPath(id_ == 'comment_image')},
            'fl_avatar_fl_now_avatar': {'path': UPath(id_ == 'fl_avatar') / UPath(id_ == 'fl_now_avatar')},
            'tab_layout_LinearLayout_1': {'path': UPath(id_ == 'tab_layout') / UPath(type_ == 'LinearLayout') / 1},
            'list_0_iv_avatar': {'path': UPath(id_ == 'list') / 0 / UPath(id_ == 'iv_avatar')},
            '观看次数 3': {'path': UPath(text_ == '观看次数 3')},
            'iv_avatar': {'path': UPath(id_ == 'iv_avatar')},
            'fl_avatar_avatar': {'path': UPath(id_ == 'fl_avatar') / UPath(id_ == 'avatar')},
            'list_0_fl_now_avatar': {'path': UPath(id_ == 'list') / 0 / UPath(id_ == 'fl_now_avatar')},

        }

    def click_collections(self):
        if self["collections"].wait_for_existing(timeout=5, raise_error=False):
            self["collections"].click()

    def enter_first_collection(self):
        self.swipe(y_direction=1)
        if self["first_collection"].wait_for_existing(timeout=5, raise_error=False):
            self["first_collection"].click()

    def check_collection(self):
        if self["collection_name"].wait_for_existing(timeout=5, raise_error=False):
            return self["collection_name"].text

    def enter_v30_video_in_collection(self):
        if self["v30_video_in_collection"].wait_for_existing(timeout=5, raise_error=False):
            self["v30_video_in_collection"].click()

    def enter_v31_video_in_collection(self):
        if self["v31_video_in_collection"].wait_for_existing(timeout=5, raise_error=False):
            self["v31_video_in_collection"].click()

    def check_insights_field_in_collection(self):
        if self["insights_more_data"].wait_for_existing(timeout=5, raise_error=False):
            return self["insights_more_data"].text

    def enter_analysis_page(self):
        self["insights_more_data"].click()

    def click_back_button(self):
        if self["back_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["back_button"].click()

    def wait_for_tagged_label_existing(self):
        self['tagged_label'].wait_for_existing()

    def wait_for_tagged_label_visible(self):
        self['tagged_label'].wait_for_visible()

    def wait_for_tagged_label_text(self):
        self['tagged_label'].wait_for_text("^You're tagged$")

    def wait_for_nasdaq_of_that_text(self):
        self['mentioned_comment'].wait_for_text('^\u200e那是纳斯达克的呢😅$')

    def wait_for_mentioned_video_text(self):
        self['mentioned_video'].wait_for_text('^\u200e被大喊大叫的$')

    def wait_for_favorite_like_video_text(self):
        self['favorite_like_video'].wait_for_text('^1$')

    def wait_for_digg_like_story_text(self):
        self['digg_like_story'].wait_for_text('^1$')

    def wait_for_duet_tag_text(self):
        self['duet_tag'].wait_for_text('^#与 @史迪仔很酷 合拍 $')

    def click_close_story_tianchuang(self):
        self['close_story_tianchuang'].click()

    def click_if_add_widget_btn_exist(self):
        if self['add_widget_btn'].wait_for_visible(timeout=5, raise_error=False):
            self['add_widget_btn'].click()

    def click_if_widget_btn_end_exist(self):
        if self['widget_btn_end'].wait_for_visible(timeout=5, raise_error=False):
            self['widget_btn_end'].click()

    def click_comment_image(self):
        self['comment_image'].click()

    def wait_for_fl_avatar_fl_now_avatar_visible(self):
        self['fl_avatar_fl_now_avatar'].wait_for_visible()

    def click_tab_layout_linear_layout_1(self):
        self['tab_layout_LinearLayout_1'].click()

    def wait_for_list_iv_avatar_visible(self):
        self['list_0_iv_avatar'].wait_for_visible()

    def click_views_3(self):
        self['观看次数 3'].click()

    def wait_for_iv_avatar_visible(self):
        self['iv_avatar'].wait_for_visible()

    def wait_for_fl_avatar_avatar_visible(self):
        self['fl_avatar_avatar'].wait_for_visible()

    def wait_for_list_fl_now_avatar_visible(self):
        self['list_0_fl_now_avatar'].wait_for_visible()


class VideoAnalysisWebView(Webview):
    """A class for the Video Analysis Webview for three versions: v3.0, v3.1 and v3.2"""
    view_spec = {
        # "title": "https://www.tiktok.com/web-inapp/analytics/video/7083256079206960385?hide_nav_bar=1&amp;__status_bar=true&amp;should_full_screen=1&amp;status_bar_height=32&amp;status_bar_height=32",
        "title": "https://inapp.tiktokv.com/web-inapp/analytics/video/*",
        # "url": "https://www.tiktok.com/web-inapp/analytics/video/7083256079206960385?hide_nav_bar=1&__status_bar=true&should_full_screen=1&status_bar_height=32&status_bar_height=32",
        "url": "https://inapp.tiktokv.com/web-inapp/analytics/video/*",
    }

    def get_locators(self):
        return {
            "back_button": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / 0 / 0 / 0 / 1 / 0 / 0 / 1 / 0 / 0 / 0},
            "performance_tab": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Performance")},
            "viewers_tab": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Viewers")},

            # elements for v3.0 videos
            "published_time_3.0": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / 0 / 0 / 2 / 0 / 0 / 1 / 0},
            "total_play_time_3.0": {"type": WebElement, "path": UPath(text_ == "Total Play Time")},
            "average_watch_time_3.0": {"type": WebElement, "path":
                UPath(text_ == "Average Watch Time")},
            "watched_full_video_3.0": {"type": WebElement, "path":
               UPath(text_ == "Watched full video")},
            "reached_audience_3.0": {"type": WebElement, "path":
               UPath(text_ == "Reached Audience")},
            "video_views_by_section_3.0": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / UPath(type_ == "MAIN", visible_ == True) / 2},
            "video_views_by_region_3.0": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / UPath(type_ == "MAIN", visible_ == True) / 5},

            # elements for v3.1 videos

            "published_time_3.1": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / 0 / 0 / 2 / 1 / 0 / 1 / 0},
            "total_play_time_3.1": {"type": WebElement, "path":
                UPath(text_ == "Total Play Time")},
            "average_watch_time_3.1": {"type": WebElement, "path":
                UPath(text_ == "Average Watch Time")},
            "watched_full_video_3.1": {"type": WebElement, "path":
                UPath(text_ == "Watched full video")},
            "reached_audience_3.1": {"type": WebElement, "path":
                UPath(text_ == "Reached Audience")},
            "retention_rate_3.1": {"type": WebElement, "path":
                UPath(type_ == "SPAN", text_ == "Retention rate")},
            "understand_key_moments_3.1": {"type": WebElement, "path":
                UPath(class_ == "pb-8", text_ == "Understand key moments")},
            "understand_key_moments_text_3.1": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / 0 / 0 / 2 / 3 / 2 / 0 / 2 / 0 / 1},
            "video_views_by_section_3.1": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / 0 / 0 / 2 / 3 / 3},
            "video_views_by_region_3.1": {"type": WebElement, "path":
                UPath(id_ == "root") / UPath(lang_ == "en") / 0 / 0 / 2 / 3 / 6},

        }


class VideoAnalysisPanel(Window):
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.wiki.AddWikiActivity|com.ss.android.ugc.aweme.crossplatform.activity.CrossPlatformActivity"
    }

    def get_locators(self):
        return {
            "video_analysis_webview": {"type": VideoAnalysisWebView,
                                       "path": UPath(~type_ ==
                                                     "com.ss.android.ugc.aweme.crossplatform.view.WebViewFrameLayout",
                                                     visible_ == True)},
        }

    def click_back_button(self):
        if self["video_analysis_webview"]["back_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["video_analysis_webview"]["back_button"].click()

    def check_performance_tab(self):
        return self["video_analysis_webview"]["performance_tab"].wait_for_existing(timeout=5, raise_error=False)

    def check_viewers_tab(self):
        return self["video_analysis_webview"]["viewers_tab"].wait_for_existing(timeout=5, raise_error=False)

    # v3.0 tests

    def check_published_time_30(self):
        self["video_analysis_webview"]["published_time_3.0"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["published_time_3.0"].text

    def check_total_play_time_30(self):
        self["video_analysis_webview"]["total_play_time_3.0"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["total_play_time_3.0"].text

    def check_average_watch_time_30(self):
        self["video_analysis_webview"]["average_watch_time_3.0"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["average_watch_time_3.0"].text

    def check_watched_full_video_30(self):
        self["video_analysis_webview"]["watched_full_video_3.0"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["watched_full_video_3.0"].text

    def check_reached_audience_30(self):
        if self["video_analysis_webview"]["reached_audience_3.0"].wait_for_existing(timeout=10, raise_error=False):
            return self["video_analysis_webview"]["reached_audience_3.0"].text

    def check_video_views_by_section_30(self):
        return self["video_analysis_webview"]["video_views_by_section_3.0"].wait_for_existing(timeout=10,
                                                                                          raise_error=False)


    def check_video_views_by_region_30(self):
        self["video_analysis_webview"]["video_views_by_region_3.0"].wait_for_existing(timeout=10, raise_error=False)
        region_text =  self["video_analysis_webview"]["video_views_by_region_3.0"].text
        return region_text.strip()
    # v3.1 tests

    def check_published_time_31(self):
        self["video_analysis_webview"]["published_time_3.1"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["published_time_3.1"].text

    def check_total_play_time_31(self):
        self["video_analysis_webview"]["total_play_time_3.1"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["total_play_time_3.1"].text

    def check_average_watch_time_31(self):
        self["video_analysis_webview"]["average_watch_time_3.1"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["average_watch_time_3.1"].text

    def check_watched_full_video_31(self):
        self["video_analysis_webview"]["watched_full_video_3.1"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["watched_full_video_3.1"].text

    def check_reached_audience_31(self):
        self["video_analysis_webview"]["reached_audience_3.1"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["reached_audience_3.1"].text

    def check_retention_rate_31(self):
        self["video_analysis_webview"]["retention_rate_3.1"].wait_for_existing(timeout=10, raise_error=False)
        return self["video_analysis_webview"]["retention_rate_3.1"].text

    def check_understand_key_moments_31(self):
        self["video_analysis_webview"]["understand_key_moments_3.1"].wait_for_existing(timeout=10,
                                                                                          raise_error=False)
        return self["video_analysis_webview"]["understand_key_moments_3.1"].text

    def check_understand_key_moments_text_31(self):
        self["video_analysis_webview"]["understand_key_moments_text_3.1"].wait_for_existing(timeout=10,
                                                                                               raise_error=False)
        return self["video_analysis_webview"]["understand_key_moments_text_3.1"].text

    def check_video_views_by_section_31(self):
        self["video_analysis_webview"]["video_views_by_section_3.1"].wait_for_existing(timeout=10,
                                                                                          raise_error=False)
        section_text =  self["video_analysis_webview"]["video_views_by_section_3.1"].text
        return section_text.strip()

    def check_video_views_by_region_31(self):
        self["video_analysis_webview"]["video_views_by_region_3.1"].wait_for_existing(timeout=10, raise_error=False)
        region_text =  self["video_analysis_webview"]["video_views_by_region_3.1"].text
        return region_text.strip()
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["close_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return self["close_button"].click()

    def get_tiltify_donation_form(self) ->TiltifyPageWebview:
        return self['tiltify_donation_form']

    def click_close_custom(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self["close_custom"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return self['close_custom'].click()

class InboxSharedGroup(Window):

    window_spec = {"activity": "com.ss.android.ugc.aweme.im.sdk.chatlist.ui.fragment.SessionListFragment$$Activity"}

    def get_locators(self):
        return {
            "shared_public_group": {"path": UPath(id_ == 'session_list_root_layout', visible_ == True)},
        }

    def click_shared_messages(self):
        if self["shared_public_group"].wait_for_existing(timeout=3, raise_error=False):
            self["shared_public_group"].click()


class CollectionGroup(Window):

    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "collection_group_video": {"type": Control, "path": UPath(id_ == 'image_frame', visible_ == True)},
            "collection_group_video_1": {"type": Control, "path": UPath(id_ == 'collect_detail')/1/UPath(id_ == 'image_frame')},
            "more_icon": {"type": Control, "path": UPath(id_ == 'icon_more', visible_ == True)},
            "more_btn_panel": {"type": Control, "path": UPath(~id_ == 'sheet_container|share_panel_root', visible_ == True)},
            "change_btn": {"type": Control, "path": UPath(id_ == 'action_list')/1/UPath(id_ == 'share_action_label')},
            "delete_btn": {"type": Control, "path": UPath(id_ == 'action_list')/2/UPath(id_ == 'share_action_label')},
            "rename_btn": {"type": Control, "path": UPath(text_ == 'Change collection name', visible_ == True)},
            "delete_btn_v2": {"type": Control, "path": UPath(text_ == 'Delete collection', visible_ == True)},
            "video_list": {"type": Control, "path": UPath(id_ == 'collect_detail', visible_ == True)},
            "video_item": {"type": Control, "path": UPath(id_ == 'collect_detail')/1/0},
            "share_panel": {"type": Control, "path": UPath(~id_ == 'share_panel_root|fl_main_bottom_panel', visible_ == True)},
            "manage_videos": {"type": Control, "path": UPath(text_ == 'Manage videos', visible_ == True)},
            "add_videos": {"type": Control, "path": UPath(id_ == 'btn_manage_videos', visible_ == True)},
            "new_group_video_1": {"type": Control, "path": UPath(id_ == 'collect_list')/0/UPath(id_ == 'selected')},
            "video_save_btn": {"type": Control, "path": UPath(id_ == 'tv_submit', visible_ == True)},
            "back_btn": {"type": Control, "path": UPath(~id_ == 'back|cancel', visible_ == True)},
            "send_name": {"type": Control, "path": UPath(~id_ == 'recycle_view|rv_share_panel_avatar')/0/UPath(id_ == 'avatar_iv')},
            "send_btn": {"type": Control, "path": UPath(id_ == 'send', visible_ == True)},
            "send_notice": {"type": Control, "path": UPath(id_ == 'layout_notice', visible_ == True)},
            "share_group_1": {"type": Control, "path": UPath(id_ == 'recycle_view')/1},
            "no_video_info": {"type": Control, "path": UPath(id_ == 'title_tv', visible_ == True)},
            "ok": {"type": Control, "path": UPath(text_ == 'OK', visible_ == True)},
            "other_user_collection_tab": {"type": Control, "path": UPath(id_ == 'tab_container') / 1},
            "other_user_liked_tab": {"type": Control, "path": UPath(id_ == 'tab_container')/2},
            "group_1": {"type": Control, "path": UPath(id_ == 'collect_list')/0/UPath(id_ == 'cover')},
            "share_icon": {"type": Control, "path": UPath(id_ == 'icon_more', visible_ == True)},
            "sent_notice": {"type": Control, "path": UPath(id_ == 'layout_notice', visible_ == True)},
            "user_profile_video": {"type": Control, 'path': UPath(id_ == 'feed_list', visible_ == True)/1/UPath(id_ == 'cover')},
            "feed_debug_icon": {"type": Control, "path": UPath(id_ == 'feed_debug_icon', visible_ == True)},
            "add_to_feed_icon": {"type": Control, "path": UPath(id_ == 'add_to_feed', visible_ == True)},
            "add_videos_btn": {"type": Control, "path": UPath(id_ == 'btn_text', visible_ == True)},
            # "no_favourites_video": {"type": Control, "path": UPath(id_ == 'title_tv', visible_ == True)},
            "cancel_btn": {"type": Control, "path": UPath(id_ == 'cancel', visible_ == True)},
            "manage_videos_btn": {"type": Control, "path": UPath(id_ == 'btn_manage_videos') / UPath(text_ == 'Manage videos', visible_ == True)},
            "qna_back_btn": {"type": Control, "path": UPath(id_ == 'qna_back_btn', visible_ == True)},
        }

    def check_collection_group_video(self):
        return self["collection_group_video"].wait_for_existing(timeout=3, raise_error=False)

    def click_collection_group_video(self):
        if self["collection_group_video"].wait_for_existing(timeout=3, raise_error=False):
            self["collection_group_video"].click()
        time.sleep(3)

    def click_qna_back_btn(self):
        self["qna_back_btn"].click()
        time.sleep(2)

    def check_no_collection_group_video(self):
        items = self["video_list"].items()
        if len(items[2:]) > 0 and self["manage_videos_btn"].existing:
            items[1].click()
        else:
            self.collections_group_add_videos()
            if self["collection_group_video_1"].wait_for_existing(timeout=3, raise_error=False):
                self["collection_group_video_1"].click()
            time.sleep(2)

    def check_video_activity(self):
        if self["no_video_info"].wait_for_existing(timeout=3, raise_error=False):
            return True
        elif self.app.wait_for_activity("com.ss.android.ugc.aweme.detail.ui.DetailActivity"):
            return True
        return False

    def check_add_videos_btn(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self["no_video_info"].wait_for_existing(timeout=5, raise_error=False):
                return True
            return self["video_list"].wait_for_existing(timeout=5, raise_error=False)

    def video_add_to_feed(self):
        if self["feed_debug_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["feed_debug_icon"].click()
            time.sleep(2)
            self["add_to_feed_icon"].click()

    def check_sent_notice(self):
        if self["no_video_info"].existing:
            return True
        else:
            return self["sent_notice"].wait_for_existing(timeout=3, raise_error=False)

    def check_collection_tab(self):
        return self["other_user_collection_tab"].wait_for_existing(timeout=3, raise_error=False)

    def share_user_twice(self):
        if self["share_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["share_icon"].click()
            time.sleep(2)
            self["send_name"].click()
            time.sleep(2)
            self["send_btn"].click()

    def click_collection_group(self):
        if self["other_user_collection_tab"].wait_for_existing(timeout=3, raise_error=False):
            self["other_user_collection_tab"].click()
            if self["group_1"].wait_for_existing(timeout=3, raise_error=False):
                self["group_1"].click()

    def group_add_video(self):
        if self["no_video_info"].text == "No videos in this collection":
            self["add_videos"].click()
            self.add_new_group_video()

    def send_collection_group(self):
        for _ in Retry(limit=2, raise_error=False):
            if self["send_name"].wait_for_existing(timeout=3, raise_error=False):
                self["send_name"].click()
                if self["send_btn"].wait_for_existing(timeout=3, raise_error=False):
                    self["send_btn"].click()
                elif self["ok"].wait_for_existing(timeout=3, raise_error=False):
                    self["ok"].click()

    def send_collection_group_twice(self):
        for _ in Retry(limit=2, raise_error=False):
            # self.click_more_icon()
            if self["send_name"].wait_for_existing(timeout=3, raise_error=False):
                self["send_name"].click()
                if self["send_btn"].wait_for_existing(timeout=3, raise_error=False):
                    self["send_btn"].click()
                elif self["ok"].wait_for_existing(timeout=3, raise_error=False):
                    self["ok"].click()

    def check_send_notice(self):
        return self["send_notice"].wait_for_existing(timeout=3, raise_error=False)

    def click_share_group_1(self):
        if self["share_group_1"].wait_for_existing(timeout=3, raise_error=False):
            self["share_group_1"].click()
        else:
            self.app.testcase.log_record("分享的收藏夹中无收藏视频")
            from shoots.exceptions import StopRunningCase
            raise StopRunningCase

    def check_share_icon(self):
        return self["more_icon"].wait_for_existing(timeout=3, raise_error=False)

    def check_collection_more_icon(self):
        return self["more_icon"].wait_for_existing(timeout=3, raise_error=False)

    def click_more_icon(self):
        if self["more_icon"].wait_for_existing(timeout=3, raise_error=False):
            self["more_icon"].click()
            time.sleep(2)

    def check_more_btn_panel(self):
        return self["more_btn_panel"].wait_for_existing(timeout=3, raise_error=False)

    def click_back_btn(self):
        return self["back_btn"].click()

    def collections_group_add_videos(self):
        if self["manage_videos"].wait_for_existing(timeout=3, raise_error=False):
            self["manage_videos"].click()
            time.sleep(2)
        self["add_videos"].click()
        if self["no_video_info"].wait_for_existing(timeout=3, raise_error=False):
            self["cancel_btn"].click()
            return
        time.sleep(2)
        self.add_new_group_video()

    def add_new_group_video(self):
        if self["new_group_video_1"].wait_for_existing(timeout=3, raise_error=False):
            self["new_group_video_1"].click()
            time.sleep(2)
            self["video_save_btn"].click()
        time.sleep(2)

    def check_share_panel(self):
        if self["no_video_info"].existing and self["no_video_info"].text == "No favorite videos to add":
            self.app.testcase.log_record("No favorite videos to add")
            from shoots.exceptions import StopRunningCase
            raise StopRunningCase
        return self["share_panel"].wait_for_existing(timeout=3, raise_error=False)

    def check_btn_text(self):
        if self["change_btn"].existing and self["change_btn"].text == "Change name":
            if self["delete_btn"].text == "Delete collection":
                return True
        elif self["rename_btn"].existing and self["rename_btn"].text == "Change collection name":
            if self["delete_btn_v2"].text == "Delete collection":
                return True
        return False

    def back_video_list(self):
        if self["more_btn_panel"].wait_for_existing(timeout=3, raise_error=False):
            self.app.get_device().press_back()
            time.sleep(2)

    def check_swipe_video_list(self):
        for _ in Retry(limit=2, raise_error=False):
            self.swipe(y_direction=2)
            self.collections_group_add_videos()
            time.sleep(3)
            for _ in Retry(timeout=20, raise_error=False):
                if self["video_item"].visible:
                    self["video_item"].click()
                video_page = DonationsPanel(root=self.app)
                if self["no_video_info"].wait_for_existing(timeout=3, raise_error=False):
                    return True
                elif self.app.wait_for_activity(video_page.activity) == True:
                    return True
                return False



