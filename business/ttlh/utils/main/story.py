# -*- coding: utf8 -*-
import json
import time

import jsonpath
import requests
import settings
from business.ttlh.utils.main.main import *




class StoriesArchiveUpath(Window):
    def get_locators(self):
        return {
            "iv_cover": {'path': UPath(id_ == "iv_cover")}
        }

    def ClickIvCover(self):
        if self["iv_cover"].wait_for_visible(timeout=5, raise_error=False):
            self["iv_cover"].click()

class StoriesArchiveIv(Control):
    elem_path = UPath(id_ == "iv_cover")
    elem_class = Control


class StoryInboxConsumeUpath(Window):
    def get_locators(self):
        return {
            'content': {"path": UPath(id_ == "last_session_content")},
        }

    def click_conent(self):
        if self["content"].wait_for_existing(timeout=5, interval=0.5):
            self["content"].click()

class StoryInboxConsume(Control):
    elem_path = UPath(id_ == "session_list_root_layout") / UPath(id_ == "session_content_view") / UPath(id_ == "content_layout")
    elem_class = StoryInboxConsumeUpath


class StoryDetailPanel(ForYouPanel):
    """
        Story详情页
    """
    window_spec = {"activity":"com.ss.android.ugc.aweme.(detail.ui.Detail.*|splash.Splash.*|music.ui.MusicDetailActivity)"}

    def get_locators(self):
        return {
            'user_avatar_layout_indicator': {'path': UPath(id_ == 'user_avatar_layout_indicator', visible_ == True)},
            'digg_container': {'path': UPath(id_ == 'digg_container', visible_ == True)},
            'comment_image': {'path': UPath(id_ == 'comment_image', visible_ == True)},
            'comment_edit_new': {'type': MTextEdit, 'path': UPath(id_ == "comment_edit_new", visible_ == True)},
            'recyclerView_content': {
                'path': UPath(id_ == "recyclerView", visible_ == True) / 1 / UPath(id_ == "content")},
            'comment_send_new': {'path': UPath(id_ == "comment_send_new", visible_ == True)},
            'comment_panel_view': {'path': UPath(id_ == "llt_comment_list_container", visible_ == True, index=0)},
            'Creator': {'path': UPath(id_ == "comment_style", text_ == "Creator", index=-1)},
            'story_share_iv': {'path': UPath(id_ == 'share_iv', visible_ == True)},
            'tiv_story_camera': {'path': UPath(id_ == 'tiv_story_camera', visible_ == True)},
            'interact_right_area__2_digg_view_layout': {
                'path': UPath(id_ == 'interact_right_area', visible_ == True) / 2 / UPath(id_ == 'digg_view_layout')},
            'tux_status_view': {'path': UPath(id_ == 'tux_status_view')},
            'digg_count': {'path': UPath(id_ == 'digg_count', visible_ == True)},
            'comment_count': {'path': UPath(id_ == 'comment_count', visible_ == True)},
            'iv_close': {'path': UPath(id_ == 'iv_close')},
            'comment_back_btn': {"path": UPath(id_ == "comment_back_layout") / UPath(id_ == "back_btn")},
            'story_privacy_btn': {'path': UPath(id_ == 'story_privacy_btn', visible_ == True)},
            'set_nav_bar_title': {'path': UPath(id_ == 'nav_bar_title', visible_ == True)},
            'nav_start_TuxIconView': {
                'path': UPath(id_ == 'nav_start') / UPath(type_ == 'TuxIconView', visible_ == True)},
            'user_nav_bar_title': {'path': UPath(id_ == 'nav_bar_title', text_ == "user2066703", visible_ == True)},
            'share_to_title': {'path': UPath(id_ == "nav_bar_title", text_ == "Share to", visible_ == True)},
            'story_nick_name': {'path': UPath(id_ == 'nameTv', index=0)},
            'bubble_time': {'path': UPath(id_ == "timeTv")},
            'story_comment_bubble_list': {'path': UPath(id_ == "comment_bubble_list") / 1 and UPath(index=0)},
            'story_progress_bar': {'path': UPath(id_ == "progress_bar", visible_ == True) / 0},
            'comment_send_new_background': {'path': UPath(id_ == 'comment_send_new_background', visible_ == True)},
            'digg_and_hate_view_0': {'path': UPath(id_ == 'digg_and_hate_view') / 0},
            'Delete': {'path': UPath(text_ == 'Delete', visible_ == True)},
            'action_list': {'path': UPath(id_ == "action_list")},
            'share_dialog_options_container': {'path': UPath(id_ == "share_dialog_options_container")},
            'downloading_status_txt': {'path': UPath(id_ == 'downloading_status_txt')},
            'download_success_txt': {'path': UPath(id_ == 'download_success_txt', text_ == "Video saved")},
            'share_save_icon_1': {'path': UPath(id_ == "action_list") / 0 / UPath(id_ == "share_action_icon")},
            'share_save_icon_2': {'path': UPath(id_ == "share_dialog_options_container") / 0 / UPath(id_ == "icon")},
            'swipe_share_to': {'type': ScrollView, 'path': UPath(id_ == "sheet_handle_vector")},
            'origin_music_cover': {'path': UPath(id_ == "origin_music_cover", visible_ == True)},
            'view_entrance_text': {'path': UPath(id_ == 'view_entrance_text', visible_ == True)},
            'view_entrance_play_vv_icon': {'path': UPath(id_ == "view_entrance_play_vv_icon", visible_ == True)},
            'tv_title': {'path': UPath(id_ == 'tv_title')},
            'share_friends_list': {'type': ScrollView, 'path': UPath(id_ == 'rv_share_panel_avatar')},
            'channel_list': {'path': UPath(id_ == 'channel_list', visible_ == True)},
            'll_secondary_container_1_0': {'path': UPath(id_ == 'll_secondary_container') / 1 / 0},
            'more': {'path': UPath(id_ == 'civ')},
            'copy_link': {'path': UPath(text_ == 'Copy link')},
            'share_download_icon': {
                'path': UPath(id_ == 'action_list') / 0 / UPath(id_ == 'share_action_icon')},
            'send_to_title': {'path': UPath(id_ == "title", visible_ == True)},
            'contact_list_recyclerview_0': {
                'path': UPath(id_ == "contact_list_recyclerview") / 0 / UPath(id_ == "checkbox")},
            'edit_msg': {'type': TextEdit, 'path': UPath(id_ == 'edit_msg')},
            'tv_send': {'path': UPath(id_ == "tv_send", text_ == "Send", visible_ == True)},
            'search_et': {'path': UPath(id_ == 'search_et')},
            'checkbox_container': {'path': UPath(id_ == "checkbox_container") / UPath(id_ == "checkbox")},
            'more_dialog_close': {'path': UPath(id_ == 'more_dialog_close')},
            'save_video_icon': {'path': UPath(id_ == "action_list") / 0 / UPath(id_ == "share_action_icon")},
            'share_iv': {'path': UPath(id_ == 'share_iv', visible_ == True)},
            'share_story_del_icon': {'path': UPath(id_ == "action_list") / 2 / UPath(id_ == "share_action_icon")},
            'delete_message': {'path': UPath(id_ == "message")},
            'button2': {'path': UPath(id_ == 'button2')},
            'button1': {'path': UPath(id_ == 'button1')},
            'list_0_title_tv': {'path': UPath(id_ == 'list') / 0 / UPath(id_ == 'title_tv')},
            'Everyone': {'path': UPath(text_ == 'Everyone')},
            'Followers': {'path': UPath(text_ == "Followers")},
            'list_1_subtitle_tv': {'path': UPath(id_ == "list") / 1 / UPath(id_ == "subtitle_tv")},
            'Friends': {'path': UPath(text_ == 'Friends')},
            'subtitle_tv': {'path': UPath(id_ == 'subtitle_tv', visible_ == True)},
            'list_2_subtitle_tv': {'path': UPath(id_ == "list") / 2 / UPath(id_ == "subtitle_tv")},
            'only_me': {'path': UPath(text_ == 'Only me')},
            'list_1_tux_textCell_accessory_0': {
                'path': UPath(id_ == 'list') / 1 / UPath(id_ == 'tux_textCell_accessory') / 0},
            'list_2_tux_textCell_accessory_0': {
                'path': UPath(id_ == "list") / 2 / UPath(id_ == "tux_textCell_accessory") / 0},
            'list_3_tux_textCell_accessory_0': {
                'path': UPath(id_ == "list") / 3 / UPath(id_ == "tux_textCell_accessory") / 0},
            'nav_end': {'path': UPath(id_ == "nav_end", visible_ == True)},
            'story_desc_view': {'path': UPath(id_ == "descView")},
            'detail_close_iv': {'path': UPath(id_ == "close_iv", visible_ == True)},
            'bottom_container': {'path': UPath(id_ == "ll_bottom_area_container")},
            "story_detail_back_btn": {"path": UPath(id_ == "back_btn", visible_ == True)},
            "story_detail": {"path": UPath(id_ == "story_root", visible_ == True)},
            "progress_bar_list": {"path": UPath(id_ == "progress_bar") / UPath(type_ == "android.view.View", visible_ == True)},
        }

    def wait_for_share_list_existing(self):
        for _ in Retry(timeout=5, raise_error=False):
            if self["action_list"].existing and self["action_list"].visible:
                return True
            if self["share_dialog_options_container"].existing and self["share_dialog_options_container"].visible:
                return True

    def check_downloading_status_txt(self):
        return self['downloading_status_txt'].wait_for_visible(timeout=5, raise_error=False)

    def check_download_success_txt(self):
        self['downloading_status_txt'].wait_for_invisible(timeout=20, raise_error=False)
        return self['download_success_txt'].wait_for_visible(timeout=5, interval=0.2, raise_error=True)

    def click_story_share_iv(self):
        if self["story_share_iv"].wait_for_visible(timeout=5, interval=0.5):
            self['story_share_iv'].click()

    def click_iv_close(self):
        self['iv_close'].click()

    def click_comment_back_btn(self):
        if self["comment_back_btn"].wait_for_visible(timeout=5, interval=1):
            self['comment_back_btn'].click()

    def check_share_download_icon(self):
        return self["share_download_icon"].wait_for_visible(timeout=5)

    def check_digg_container(self):
        return self["digg_container"].wait_for_visible(timeout=5)

    def check_story_comment_bubble_list(self):
        return self["story_comment_bubble_list"].wait_for_visible(timeout=5, raise_error=False)

    def click_digg_container(self):
        if self["digg_container"].wait_for_visible(timeout=5,raise_error=False):
            return self["digg_container"].click()

    def get_digg_count_text(self):
        if self["digg_count"].wait_for_visible(timeout=5, interval=0.5):
            self["digg_count"].refresh()
            time.sleep(2)
            return int(self["digg_count"].text)

    def click_story_privacy_btn(self):
        if self["story_privacy_btn"].wait_for_visible(timeout=5, interval=0.5):
            self['story_privacy_btn'].click()

    def get_set_nav_bar_title_text(self):
        if self["set_nav_bar_title"].wait_for_visible(timeout=5, interval=0.5, raise_error=True):
            return self['set_nav_bar_title'].text

    def check_tiv_story_camera(self):
        return self['tiv_story_camera'].wait_for_visible(timeout=5, raise_error=False)

    def click_tiv_story_camera(self):
        if self["tiv_story_camera"].wait_for_visible(timeout=5):
            self['tiv_story_camera'].click()

    def click_story_detail_back_btn(self):
        if self["story_detail_back_btn"].wait_for_visible(timeout=5):
            self["story_detail_back_btn"].click()

    def check_story_detail(self):
        return self["story_detail"].wait_for_disappear(timeout=5, interval=0.5, raise_error=False)

    def check_user_avatar_layout(self):
        return self["user_avatar_layout_indicator"].wait_for_visible(timeout=5)

    def click_user_avatar_layout_indicator(self):
        if self["user_avatar_layout_indicator"].wait_for_visible(timeout=5):
            self['user_avatar_layout_indicator'].click()

    def click_nav_start_tux_icon_view(self):
        self['nav_start_TuxIconView'].click()

    def check_comment_image(self):
        return self["comment_image"].wait_for_visible(timeout=5)

    def click_comment_image(self):
        if self["comment_image"].wait_for_visible(timeout=5, interval=1):
            self['comment_image'].click()

    def check_comment_panel_view(self):
        return self["comment_panel_view"].wait_for_visible(timeout=5, interval=1, raise_error=False)

    def check_story_progress_bar(self):
        return self["story_progress_bar"].wait_for_visible(timeout=5)

    def click_comment_send_new(self):
        if self["comment_send_new"].wait_for_visible(timeout=5, raise_error=False):
            self["comment_send_new"].click()

    def send_comment_edit_new(self, text):
        if self["comment_edit_new"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['comment_edit_new'].click()
        self['comment_edit_new'].input(text)
        time.sleep(3)
        # self["Creator"].click()        # 有些设备输入状态不会退出，所以点击空白区域
        self.click_comment_send_new()

    def get_recyclerView_content_text(self):
        self["recyclerView_content"].refresh()
        if self["recyclerView_content"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self["recyclerView_content"].text[1:]

    def get_comment_count_text(self):
        if self["comment_count"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return int(self["comment_count"].text)

    def click_delete(self):
        self['Delete'].click()

    def check_tux_status_view(self):
        return self["tux_status_view"].wait_for_visible(timeout=5, raise_error=False)

    def click_tux_status_view(self):
        if self["tux_status_view"].wait_for_visible():
            self['tux_status_view'].click()

    def long_click_tux_status_view(self):
        if self["tux_status_view"].wait_for_visible():
            self['tux_status_view'].click()
            self['tux_status_view'].long_click(duration=2)

    def double_click_tux_status_view(self):
        if self["tux_status_view"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['tux_status_view'].double_click()

    def click_share_save_icon(self):
        for _ in Retry(timeout=5, raise_error=False):
            if self["share_save_icon_1"].existing and self["share_save_icon_1"].visible:
                self['share_save_icon_1'].click()
                break
            if self["share_save_icon_2"].existing and self["share_save_icon_2"].visible:
                self['share_save_icon_2'].click()
                break

    def swipe_share_to(self):
        try:
            if self["swipe_share_to"].wait_for_existing(timeout=40, interval=2, raise_error=False):
                self['swipe_share_to'].swipe(y_direction= -1, swipe_coefficient= 6)
        except UINotFoundError:
            pass

    def click_music_cover(self):
        for _ in Retry(timeout=15, raise_error=False):
            if self["origin_music_cover"].existing and self["origin_music_cover"].visible:
                self["origin_music_cover"].click()
                time.sleep(2)
                if self.app.wait_for_activity("com.ss.android.ugc.aweme.music.ui.MusicDetailActivity"):
                    break

    def story_view_entrance(self):
        for _ in Retry(timeout=10, raise_error=False):
            if self['view_entrance_text'].existing and self['view_entrance_text'].visible:
                return True
            if self['view_entrance_play_vv_icon'].existing and self['view_entrance_play_vv_icon'].visible:
                return True

    def get_story_nick_name(self):
        for _ in Retry(timeout=10, interval=0.2, raise_error=False):
            if self["story_nick_name"].existing:
                text = self["story_nick_name"].text
                return text.replace("\u200e", "")

    def get_bubble_time(self):
        if self["bubble_time"].wait_for_visible(timeout=5, raise_error=False):
            import re
            from string import printable
            ttime = re.sub("[^{}]+".format(printable), "", self["bubble_time"].text)
            return ttime.replace(" ","")

    def check_story_video_status(self):
        num = 1
        while num <= 5:
            bubble_time = self.get_bubble_time()
            if bubble_time == "1970-01-01":
                logger.info(f"触发视频兜底展示逻辑，时间：{bubble_time}，第{num}次滑动切换视频")
                self.swipe(x_direction=1)
            num += 1
        # self.click_tux_status_view()


    def get_tv_title_text(self):
        if self["tv_title"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['tv_title'].text

    def swipe_share_friends_list(self):
        return self['share_friends_list'].swipe(x_direction=1, swipe_coefficient=6)

    def wait_for_share_friends_list_visible(self):
        return self['share_friends_list'].wait_for_visible(timeout=5, interval=0.2, raise_error=False)

    def wait_for_share_channel_list_visible(self):
        return self['channel_list'].wait_for_visible(timeout=5, interval=0.2, raise_error=False)

    def wait_for_ll_secondary_container_visible(self):
        return self['ll_secondary_container_1_0'].wait_for_visible(timeout=5, interval=0.2, raise_error=False)

    def get_send_to_title_text(self):
        if self['send_to_title'].wait_for_visible(timeout=5, raise_error=False):
            return self['send_to_title'].text

    def check_more(self):
        return self['more'].wait_for_visible(timeout=5, raise_error=False)

    def click_more(self):
        if self['more'].wait_for_visible(timeout=5, raise_error=False):
            self['more'].click()

    def click_contact_list_recyclerview_0(self):
        if self['contact_list_recyclerview_0'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self['contact_list_recyclerview_0'].click()

    def send_edit_msg(self, text):
        if self['edit_msg'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self['edit_msg'].click()
            self['edit_msg'].send_keys(text)
        time.sleep(1)
        if self['tv_send'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self['tv_send'].click()

    def send_search_et_text(self,text):
        if self['search_et'].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['search_et'].click()
            self['search_et'].send_keys(text)
        time.sleep(1)

    def click_more_dialog_close(self):
        self['more_dialog_close'].click()

    def click_save_video_icon(self):
        self['save_video_icon'].click()

    def click_share_iv(self):
        if self['share_iv'].wait_for_visible(timeout=5, raise_error=False):
            self['share_iv'].click()

    def click_share_story_del_icon(self):
        if self['share_story_del_icon'].wait_for_visible(timeout=5, raise_error=False):
            self['share_story_del_icon'].click()

    def click_button1(self):
        if self['button1'].wait_for_visible(timeout=5, raise_error=False):
            self['button1'].click()

    def click_stroies_del_icon(self):
        if self['stroies_del_icon'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
            self['stroies_del_icon'].click()

    def get_delete_message(self):
        if self['delete_message'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
            return self['delete_message'].text

    def disappear_button2(self):
        return self['button2'].wait_for_disappear(timeout=5, interval=0.5, raise_error=False)

    def click_button2(self):
        if self['button2'].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['button2'].click()

    def get_list_title_tv_text(self):
        if self["list_0_title_tv"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['list_0_title_tv'].text

    def get_everyone_text(self):
        if self["Everyone"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['Everyone'].text

    def get_followers_text(self):
        if self["Followers"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['Followers'].text

    def get_list_1_subtitle_tv_text(self):
        if self["list_1_subtitle_tv"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['list_1_subtitle_tv'].text

    def get_list_2_subtitle_tv_text(self):
        if self["list_2_subtitle_tv"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['list_2_subtitle_tv'].text

    def get_friends_text(self):
        if self["Friends"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['Friends'].text

    def get_subtitle_tv_text(self):
        if self["subtitle_tv"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['subtitle_tv'].text

    def get_only_me_text(self):
        if self["list_0_title_tv"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self['only_me'].text

    def click_list_1_tux_textCell_accessory(self):
        if self["list_1_tux_textCell_accessory_0"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['list_1_tux_textCell_accessory_0'].click()

    def click_list_2_tux_textCell_accessory(self):
        if self["list_2_tux_textCell_accessory_0"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['list_2_tux_textCell_accessory_0'].click()

    def click_list_3_tux_textCell_accessory(self):
        if self["list_3_tux_textCell_accessory_0"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['list_3_tux_textCell_accessory_0'].click()

    def click_nav_end(self):
        if self["nav_end"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['nav_end'].click()

    def get_story_desc_view_text(self):
        import re
        from string import printable
        for _ in Retry(timeout=10, interval=0.2, raise_error=False):
            self["story_desc_view"].refresh()
            if self["story_desc_view"].existing and self["story_desc_view"].visible:
                text = re.sub("[^·{}]+".format(printable), " ", self["story_desc_view"].text)
                return text

    def click_detail_close_iv(self):
        if self['detail_close_iv'].wait_for_visible(timeout=5, interval=1, raise_error=False):
            self["detail_close_iv"].click()



class StoryDetailFuncPanel(Window):
    """
        关联功能
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.(music.ui.MusicDetailActivity|splash.SplashActivity|shortvideo.ui.VideoRecordNew.*|shortvideo.edit.VEVideoPublishEdit.*)"}

    def get_locators(self):
        return {

            # 音乐播放器
            'music_back_btn_0': {'path': UPath(id_ == 'back_btn')},
            'music_back_btn_1': {'path': UPath(id_ == "nav_bar") / 1 / UPath(id_ == "nav_start") / 0},
            'music_details_root': {'path': UPath(id_ == "music_details_root")},


            # 拍摄页
            "15s": {'type': Control, 'path': UPath(~text_ == "15s|15 秒")},
            "story_btn_next": {"path": UPath(id_ == "btn_next")},
            "circle_progress_segment_view": {'type': Control, 'path': UPath(id_ == "circle_progress_segment_view")},
            "tv_progress": {"path": UPath(id_ == "tv_progress")},
            'img_close_record': {'path': UPath(id_ == 'img_close_record', visible_ == True)},
            'video_record_scene': {'path': UPath(id_ == "video_record_new_scene_root")},

        }

    def click_music_back_btn(self):
        for _ in Retry(timeout=15, interval=0.5, raise_error=False):
            if self["music_back_btn_0"].existing and self["music_back_btn_0"].visible:
                self['music_back_btn_0'].click()
                break
            if self["music_back_btn_1"].existing and self["music_back_btn_1"].visible:
                self['music_back_btn_1'].click()
                break


    def get_tv_progress_text(self):
        return int(self["tv_progress"].text[-2:])

    def check_video_record_scene(self):
        return self["video_record_scene"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def click_camera_close_btn(self):
        if self["img_close_record"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['img_close_record'].click()

    def check_music_details(self):
        return self["music_details_root"].wait_for_visible(timeout=5, raise_error=False)

    def scroll_biz_top_container(self):
        if self["biz_top_container"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["biz_top_container"].scroll(coefficient_x= -1)



class StoryEntranceGatherPanel(Window):
    """
        Story消费入口
    """
    window_spec = {"activity": "com.ss.android.ugc.aweme.(host.TikTokHostActivity|splash.SplashActivity|shortvideo.ui.VideoRecordNew.*|shortvideo.edit.VEVideoPublishEdit.*|adaptation.saa.SAAActivit.*)"}

    def get_locators(self):
        return {
            'tabs': {'type': Control,'path': UPath(~id_ == 'main_bottom_tab_view|main_activity_bottom_tab_view', visible_ == True)},
            "Home": {'type': Control, "root": 'tabs', "path": UPath(index=0)},
            'Friends': {'type': Control, "root": 'tabs', "path": UPath(index=1)},
            'Add': {'type': Control, "root": 'tabs', "path": UPath(index=2)},
            'Inbox': {'type': Control, "root": 'tabs', "path": UPath(index=-2)},
            'Me': {'type': Control, "root": 'tabs', "path": UPath(index=-1)},

            'iv_upload': {'path': UPath(id_ == 'iv_upload')},
            'guidance_confirm_button': {'path': UPath(id_ == 'guidance_confirm_button')},
            'file_story_video': {'path': UPath(id_ == "grid") / 0 / UPath(id_ == "media_view")},
            'close_button': {'path': UPath(id_ == "close_button")},

            'tv_story': {'path': UPath(id_ == 'tv_story')},
            'profile_ll': {'path': UPath(id_ == "profile_ll")},
            'hide': {'path': UPath(id_ == "see_all_view", text_ == "Hide", visible_ == True)},
            'add_btn': {"type": Control, "path": UPath(id_ == "main_bottom_button_add") / UPath(type_ == "ImageView")},
            'tv_top_story': {'path': UPath(id_ == 'tv_top', ~text_ == "Story.*|Stories.*", visible_ == True)},
            'tv_quick_publish': {'path': UPath(id_ == "tv_quick_publish", text_ == "Your Story")},
            'bg_cover_mask': {'path': UPath(id_ == 'bg_cover_mask')},
            'profile_icon': {'path': UPath(id_ == "tab_container") / 0},
            'private_icon': {'path': UPath(id_ == "tab_container") / 1 / UPath(id_ == "icon")},
            'ic_story': {'path': UPath(id_ == "ic_story", visible_ == True)},
            'share_iv': {'path': UPath(id_ == 'share_iv', visible_ == True)},
            'share_story_del_icon': {
                'path': UPath(id_ == "action_list") / 2 / UPath(id_ == "share_action_icon", visible_ == True)},
            'button1': {'path': UPath(id_ == 'button1')},
            'biz_top_container': {"type": ScrollView, 'path': UPath(id_ == "biz_top_container", visible_ == True)},

            "stories_list": {"type": StoriesArchiveIv,
                "path": UPath(id_ == "story_list") / UPath(type_ == "androidx.constraintlayout.widget.ConstraintLayout")},
            'story_tag_layout': {'path': UPath(id_ == 'story_tag_layout', visible_ == True)},
            'tv_post_time': {'path': UPath(id_ == 'tv_post_time', visible_ == True)},
            'friends_feed': {'path': UPath(id_ == "tab_title", text_ == "Friends")},
            'iv_cover': {'path': UPath(id_ == 'iv_cover', visible_ == True)},
            'tv_play_count': {'path': UPath(id_ == 'tv_play_count', index=0)},
            'inboxRecyclerView': {"type": StoryInboxConsume, 'path': UPath(id_ == "inboxRecyclerView")},

            # 私信页
            'back_left_fl': {'type': Control, 'path': UPath(id_ == 'left_fl')},
            'inbox_msg_story_iv': {'path': UPath(id_ == "recycle_view") / 1 / UPath(id_ == "content_cover_iv")},

        }

    def check_inbox_msg_story_iv(self):
        return self["inbox_msg_story_iv"].wait_for_visible(timeout=5, raise_error=False)


    def click_inbox_msg_story_iv(self):
        if self["inbox_msg_story_iv"].wait_for_visible(timeout=5, raise_error=False):
            self["inbox_msg_story_iv"].click()

    def check_story_private_back(self):
        return self["back_left_fl"].wait_for_visible(timeout=5, raise_error=False)

    def click_story_private_back(self):
        if self["back_left_fl"].wait_for_visible(timeout=5, raise_error=False):
            self["back_left_fl"].click()

    def del_story_video(self):
        time.sleep(3)
        self.refresh_driver()
        if self['share_iv'].wait_for_visible(timeout=5, raise_error=False):
            self['share_iv'].click()
        if self['share_story_del_icon'].wait_for_existing(timeout=5, raise_error=False):
            self['share_story_del_icon'].click()
        if self['button1'].wait_for_existing(timeout=5, raise_error=False):
            self['button1'].click()
        logger.info("测试完成，删除story视频")

    def GetConentText(self):
        textlist = []
        for item in self["inboxRecyclerView"].items():
            textlist.append(item["text_content"].text[1:])
        return textlist

    def ClickConentText(self):
        return self["inboxRecyclerView"].items()[0].click_conent()

    def count_iv_cover(self):
        iv_list = []
        for item in self["stories_list"].items():
            iv_list.append(item.items())
        return iv_list

    def click_iv_cover(self, index):
        return self["stories_list"].items()[index].ClickIvCover()

    def exit_hide_visible(self):
        if self["hide"].wait_for_existing(timeout=5, raise_error=False):
            self["hide"].click()

    def click_private_icon(self):
        foryou = ForYouPanel(root=self.app)
        foryou.open_tab("Me")
        self.exit_hide_visible()
        if self['private_icon'].wait_for_visible(timeout=5, raise_error=False):
            self["private_icon"].click()

    def click_ic_story_icon(self):
        if self["ic_story"].wait_for_visible(timeout=5, raise_error=False):
            self["ic_story"].click()
        self.app.wait_for_activity("com.ss.android.ugc.aweme.host.TikTokHostActivity")

    def check_tv_play_count(self):
        return self['tv_play_count'].wait_for_visible(timeout=5, raise_error=False)

    def check_iv_cover(self):
        return self['iv_cover'].wait_for_visible(timeout=5, raise_error=False)

    def invisible_tv_quick_publish(self):
        return self['tv_quick_publish'].wait_for_invisible(timeout=5, raise_error=False)

    def check_story_tag_layout(self):
        return self['story_tag_layout'].wait_for_visible(timeout=5, raise_error=False)

    def check_tv_post_time(self):
        return self['tv_post_time'].wait_for_visible(timeout=5, raise_error=False)

    def click_tv_quick_publish(self):
        if self['tv_quick_publish'].wait_for_existing(timeout=5, raise_error=True):
            self['tv_quick_publish'].click()

    def click_close_button(self):
        try:
            if self["close_button"].wait_for_existing(timeout=5, raise_error=True):
                self["close_button"].click()
        except UINotFoundError:
            pass

    def get_tv_story_text(self):
        if self["tv_story"].wait_for_visible(timeout=5, raise_error=False):
            return self["tv_story"].text

    def wait_for_story_tv_visible(self):
        return self['tv_top_story'].wait_for_existing(timeout=10, raise_error=True)

    def check_bg_cover_mask(self):
        return self['bg_cover_mask'].wait_for_existing(timeout=5, interval=0.2, raise_error=False)

    def check_friends_feed(self):
        return self['friends_feed'].wait_for_visible(timeout=10, raise_error=False)

    def invisible_bg_cover_mask(self):
        return self['bg_cover_mask'].wait_for_invisible(timeout=15, raise_error=False)

    def click_iv_upload(self):
        if self["iv_upload"].wait_for_visible(timeout=5, interval=0.5):
            self['iv_upload'].click()

    def click_guidance_confirm_button(self):
        if self["guidance_confirm_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self['guidance_confirm_button'].click()

    def click_file_story_video(self):
        if self["file_story_video"].wait_for_visible(timeout=5, raise_error=False):
            self["file_story_video"].click()
            return self["file_story_video"].wait_for_disappear(timeout=10, raise_error=False)

    def story_entrance_exist(self, filename, room, sessionid):
        """
            未过期Story入口存在则点击，不存在就重新发布
        """
        foryou = ForYouPanel(root=self.app)
        try:
            foryou.open_tab("Me")
            self.exit_hide_visible()
            if self['tv_top_story'].wait_for_existing(timeout=10, raise_error=True):
                self['tv_top_story'].click()
        except:
            pub_story = PublishStoryApi(
                filename= filename,
                room= room,
                sessionid= sessionid
            )
            pub_story.post_story_video()
            foryou.open_tab("Home")
            count = 1
            while count <= 5:
                foryou.swipe(y_direction=1, swipe_coefficient=6)
                time.sleep(3)
                count += 1
            foryou.open_tab("Me")
            self.exit_hide_visible()
            if self['tv_top_story'].wait_for_existing(timeout=10, raise_error=True):
                self['tv_top_story'].click()
        story_detail = StoryDetailPanel(root=self.app)
        story_detail.check_story_video_status()
        time.sleep(5)

    def publish_shoot_story(self):
        '''
            story视频发布
        '''
        foryou = ForYouPanel(root=self.app)
        foryou.open_tab("Add")
        logger.info("打开拍摄页，发布Story")
        StoryDetailFunc = StoryDetailFuncPanel(root=self.app)
        if StoryDetailFunc['15s'].wait_for_visible(raise_error=False):
            StoryDetailFunc['15s'].click()
        if StoryDetailFunc['circle_progress_segment_view'].wait_for_visible(raise_error=False):
            StoryDetailFunc['circle_progress_segment_view'].click()
        logger.info("正在拍摄story视频")
        time.sleep(8)  # 等待story拍摄完成
        try:
            for _ in Retry():
                if StoryDetailFunc.get_tv_progress_text() == 10:
                    StoryDetailFunc["story_btn_next"].click()
                    break
            self.click_close_button()
            self.click_tv_quick_publish()
            self["tv_quick_publish"].wait_for_disappear()
            logger.info("Story发布成功")
        except:
            raise Exception("Story发布失败")


class StoryDetailToastPopup(Window):
    """
        Toast
    """
    window_spec = {"activity": "PopupWindow:.*"}

    def get_locators(self):
        return {
            "press_mask": {"path": UPath(id_ == "press_mask")},
            "toast_message": {"path": UPath(id_ == "message")},
            # "toast_message": {"path": UPath(ocr_ == "Sent to .*")},
            "right_icon": {"path": UPath(id_ == "right_icon")},
            "delete": {"path": UPath(id_ == "message", text_ == "Deleted")},
        }

    def get_toast_message_text(self):
        if self["toast_message"].wait_for_visible(timeout=5, interval=0.2, raise_error=True):
            return self["toast_message"].text

    def check_toast(self):
        return self['toast_message'].wait_for_existing(timeout=5, interval=0.2, raise_error=False)

    def check_toast_delete(self):
        return self['delete'].wait_for_existing(timeout=5, interval=0.2, raise_error=False)

    def click_toast_message(self):
        for _ in Retry(timeout=10, interval=0.2, raise_error=True):
            if self['toast_message'].existing:
                self['toast_message'].click()
                break


class PublishStoryApi():
    """
        Story发布API
    """
    def __init__(self, filename, room, sessionid):
        path = settings.PROJECT_ROOT + "/resources/story"
        self.filename = filename
        self.filepath = os.path.join(path, self.filename)
        logger.info("获取需上传的视频")
        self.room = room
        self.sessionid = sessionid

    def upload_video(self):
        """
            根据测试账号所属机房选择对应的host
            sg_host = "https://ttugqa-sg.byted.org/"
            va_host = "https://ttugqa-va.byted.org/"
        """
        url = "https://ttugqa-{}.byted.org/ttqa/video/upload".format(self.room)
        param = {
            'file': (self.filename, open(self.filepath, 'rb'), 'application/octet-stream')
        }
        params = {
            **param,
            "user_token": "eWlrYWkueGlhby8yMDIxLTEyLTMxIDA4OjEyOjA1"
        }
        try:
            logger.info("上传一个视频")
            resp = requests.request("POST", url, files=params)
            resp_json = json.loads(resp.text)
            video_id = jsonpath.jsonpath(resp_json, "$..vid")[0]
            logger.info("视频上传成功")
        except:
            raise Exception("上传视频失败")
        return video_id

    def post_story_video(self):
        """
            发布story视频
        """
        video_id = self.upload_video()
        url = "https://api22-core-c-alisg.tiktokv.com/aweme/v1/create/aweme/"
        headers = {
            'x-tt-env': 'prod',
            'Cookie': 'sessionid={}'.format(self.sessionid),
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        params = {
            'version_code': '290300',
            'aid': '1233',
            'device_id': '7163889306460964394',
            'channel': 'inhouse',
            'device_platform': 'iphone',
            'app_name': 'musical_ly'
        }
        data = {
            'new_sdk': 1,
            'is_private': 0,
            'video_id': video_id,
            'original': 0,
            'aweme_type': 40,
            'video_type': 40
        }
        try:
            logger.info("发布story视频")
            resp = requests.post(url=url, data=data, params=params, headers=headers)
            # logger.info("resp_text=%s", resp.headers)
            # logger.info("logid=%s", resp.headers.get('X-Tt-Logid'))
            # logger.info("resp_text=%s", resp.text)
            resp_json = json.loads(resp.text)
            aweme_id = jsonpath.jsonpath(resp_json, "$..aweme_id")[0]
            logger.info("story发布成功")
        except:
            raise Exception("story视频发布失败")
        return aweme_id






