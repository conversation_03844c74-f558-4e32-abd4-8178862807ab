# -*- coding:utf-8 _*-
from shoots_android.control import *
from uibase.upath import id_
from shoots_android.control import Control as BaseControl


class FeedsList(ScrollView):
    elem_class = Control

    @on_expired
    def items(self):
        time.sleep(1)
        items = super(FeedsList, self).items()
        for _ in Retry(limit=20, interval=1, raise_error=False):
            if len(items) != 0:
                break
        return items


class BasePanel(Window):

    def get_locators(self):
        return {
            "content": {"type": Control, "path": UPath(id_ == "content")},
            "feeds": {"type": FeedsList, "path": UPath(id_ == "feed_list", visible_ == True)},
            "viewpager": {"type": Control, "path": UPath(id_ == "scrollable_viewpager")},
            "progress_bar": {"type": Control, "root": "viewpager", "path": UPath(id_ == "progressBarLayout", index=0)},
        }

    def wait_for_loading(self):
        self["progress_bar"].wait_for_invisible(raise_error=False)
        self["feeds"].wait_for_ui_stable()

    @property
    def first_video(self):
        return self["feeds"].items()[-1]

    def swipe_up(self):
        """上滑
        """
        self["content"].swipe(y_direction=1, swipe_coefficient=6)
        time.sleep(1)
        self.wait_for_loading()

    def quick_swipe(self, x_direction=0, y_direction=0, swipe_coefficient=3, duration=None):
        """快速滑动

        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        # 后续会支持斜着滑动
        :param swipe_coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        :param duration: 滑动操作周期，数值配置越小，滑动越快(s)
        """

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(x_direction, y_direction))

        self._driver.drag(self.id, x1, y1, x2, y2, duration)
        time.sleep(1)

    def swipe(self, x_direction=0, y_direction=0, swipe_coefficient=3):
        '''滑动

        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        后续会支持斜着滑动

        :param coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        '''

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * \
                 (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * \
                 (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(
                x_direction, y_direction))

        # self._driver.drag(self.id, x1, y1, x2, y2)
        self.drag(x2, y2, x1 - rect.width / 2, y1 - rect.height / 2)
        time.sleep(1)

    def go_home(self):
        HOME_ACTIVITY = "com.ss.android.ugc.aweme.splash.SplashActivity"
        if not self.check_is_expect_activity(HOME_ACTIVITY.split(".")[-1]):
            for _ in range(5):
                self.app.testcase.device.back()
                time.sleep(1)
                if self.check_is_expect_activity(HOME_ACTIVITY.split(".")[-1]):
                    break

    def check_is_expect_activity(self, activity_name):
        return True if activity_name in self.app.current_activity else False


class Control(BaseControl):

    def swipe(self, x_direction=0, y_direction=0, swipe_coefficient=3):
        '''滑动

        :param x_direction: 大于0向左，小于0向右
        :param y_direction: 大于0向上，小于0向下
        后续会支持斜着滑动

        :param coefficient:滑动系数 ,决定滑动距离，系数允许范围（2，8]
        '''

        if swipe_coefficient <= 2 or swipe_coefficient > 8:
            raise ValueError("coefficient range is （2，8]")

        self.wait_for_ui_stable()
        self.wait_for_visible(raise_error=False)

        rect = self.ensure_visible()
        if y_direction > 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
            y2 = rect.top + rect.height // swipe_coefficient
        elif y_direction < 0 and x_direction == 0:
            x1 = x2 = rect.left + rect.width // 2
            y1 = rect.top + rect.height // swipe_coefficient
            y2 = rect.top + rect.height * (swipe_coefficient - 1) // swipe_coefficient
        elif x_direction > 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
            x2 = rect.left + rect.width // swipe_coefficient
        elif x_direction < 0 and y_direction == 0:
            y1 = y2 = rect.top + rect.height // 2
            x1 = rect.left + rect.width // swipe_coefficient
            x2 = rect.left + rect.width * (swipe_coefficient - 1) // swipe_coefficient
        else:
            raise ValueError("not support this direction x {}  y{}".format(x_direction, y_direction))
        self.scroll(x1 - x2, y1 - y2)
        self.app.testcase.log_info(("x1-x2:%s,y1-y2:%s" % (x1 - x2, y1 - y2)))
        time.sleep(1)
