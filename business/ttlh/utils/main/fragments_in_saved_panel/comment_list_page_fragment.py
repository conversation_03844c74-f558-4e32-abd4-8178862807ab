# -*- coding: utf-8 -*-

from uibase.upath import *
from uibase.controls import Window


class CommentListPageFragmentInSavedPanel(Window):
    """activity=com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.detail.ui.DetailActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity|com.ss.android.ugc.aweme.wiki.AddWikiActivity
    """
    window_spec = {
        "path": UPath(fragment_ == "CommentListPageFragment")
    }

    def get_locators(self):
        return {
        'recyclerView_1_avatar': {'path': UPath(id_ == 'recyclerView') / 1 / UPath(id_ == 'avatar')},
        'Likes_3': {'path': UPath(~text_ == 'Likes.*')},
        'list_1_iv_avatar': {'path': UPath(id_ == 'list') / 1 / UPath(id_ == 'iv_avatar')},
        }

    def click_recycler_view_avatar(self):
        self['recyclerView_1_avatar'].click()

    def click_like_3(self):
        if self['Likes_3'].wait_for_visible():
            self['Likes_3'].click()

    def wait_for_list_iv_avatar_visible(self):
        self['list_1_iv_avatar'].wait_for_visible()

    def click_list_iv_avatar(self):
        self['list_1_iv_avatar'].click()


