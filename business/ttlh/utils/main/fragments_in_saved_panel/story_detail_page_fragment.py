# -*- coding: utf-8 -*-

from uibase.upath import *
from uibase.controls import Window


class StoryDetailPageFragmentInSavedPanel(Window):
    """activity=com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.detail.ui.DetailActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity|com.ss.android.ugc.aweme.wiki.AddWikiActivity
    """
    window_spec = {
        "path": UPath(fragment_ == "StoryDetailPageFragment")
    }

    def get_locators(self):
        return {
        'close_iv': {'path': UPath(id_ == 'close_iv')},
        'drag_scale_layout_0_19_top_widget_container': {'path': UPath(id_ == 'drag_scale_layout') / 0 / 19 / UPath(id_ == 'top_widget_container', depth=5)},
        'top_widget_container': {'path': UPath(id_ == 'top_widget_container', visible_ == True)},
        }

    def click_close_iv(self):
        self['close_iv'].click()

    def scroll_drag_scale_layout_top_widget_container(self, coefficient_x=0, coefficient_y=0):
        self['drag_scale_layout_0_19_top_widget_container'].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)

    def scroll_top_widget_container(self, coefficient_x=0, coefficient_y=0):
        self['top_widget_container'].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)


