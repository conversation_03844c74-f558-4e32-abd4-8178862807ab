# -*- coding:utf-8 _*-
import os
from shoots_android.control import *
from re import search
from uibase.web import Webview, WebElement
from shoots_android.control import *
from business.ttlh.utils.main.main import *
from uibase.upath import id_, text_, UPath, visible_, type_, href_, class_
import time


class PhotoModeFullPage(ForYouPanel):
    window_spec = {
        'activity': 'com.ss.android.ugc.aweme.ui.activity.PostModeDetailActivity'}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ForYouPanel, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            'avatar_icon': {'type': Control, 'path': UPath(id_ == "avatar_iv", visible_ == True)},
            'username': {'type': Control,'path': UPath(id_ == "nickname_txt", visible_ == True)},
            'photo_mode_full_page_desc': {'type': Control,'path': UPath(id_ == "post_mode_description", visible_ == True)},
            'photo_mode_full_page_dot_indicator': {'type': Control, 'path': UPath(~id_ == "photos_dot_indicator|photos_dot_indicator_v2", visible_ == True)},
            'photo_mode_full_page_image_view_exist': {'type': Control, 'path': UPath( ~id_ == "smart_image_view|photos_img_view", visible_ == True)},
            'photo_mode_full_page_volume_icon': {'type': Control, 'path': UPath( id_ == "post_mode_volume_icon", visible_ == True)},
            'photo_mode_full_page_anchor_view': {'type': Control, 'path': UPath( id_ == "post_mode_anchors_recyclerview", visible_ == True)},
            'photo_mode_full_page_title': {'type': Control, 'path': UPath( id_ == "post_mode_title",visible_ == True)},
            'photo_mode_full_page_add_comment': {'type': Control, 'path': UPath(id_ == "post_mode_action_bar_edittext",visible_ == True)},
            'photo_mode_full_page_add_comment_edit_new': {'type': Control, 'path': UPath(id_ == "comment_edit_new", visible_ == True)},
            'photo_mode_full_page_add_comment_send': {'type': Control, 'path': UPath(id_ == "comment_send_new_background", visible_ == True)},
            'photo_mode_full_page_top_comment': {'type': Control, 'path': UPath(id_ == "content", visible_ == True, index=0)},
            'comment_select_dialog': {'type': Control, 'path': UPath(id_ == 'select_dialog_listview')},
            'delete_comment': {'root': 'comment_select_dialog', 'path': UPath(text_ == 'Delete')},
            'photo_mode_full_page_user_icon': {(id_ == "avatar_iv", visible_ == True)},
            'photo_mode_full_page_like_btn': {'type': Control, 'path': UPath(id_ == "post_mode_action_bar_digg_container", visible_ == True)},
            'photo_mode_full_page_like_count': {'type': Control, 'path': UPath(id_ == "post_mode_action_bar_digg_text", visible_ == True)},
            'photo_mode_full_page_comment_btn': {'type': Control, 'path': UPath(id_ == "post_mode_action_bar_comment_container", visible_ == True)},
            'photo_mode_full_page_favorite_btn': {'type': Control, 'path': UPath(id_ == "post_mode_action_bar_favorite_container", visible_ == True)},
            'photo_mode_full_page_share_btn': {'type': Control, 'path': UPath(id_ == "post_mode_action_bar_share_container", visible_ == True)},

            'content': {'type': Control, 'path': UPath(id_ == 'content', type_ == 'android.widget.FrameLayout')},
            'photo_mode_full_page_to_profile': {'type': Control, 'path': UPath(id_ == "profile_ll", visible_ == True)},
            'photo_mode_full_page_back_to_post': {'type': Control,
                                                  'path': UPath(id_ == "nav_start", visible_ == True)},
            'back_to_post_in_feed': {'type': Control,'path': UPath(id_ == "back_iv", visible_ == True)},
            'back_to_post_in_profile': {'type': Control, 'path': UPath(id_ == "back_btn", visible_ == True)},
            'photo_mode_full_page_comment_panel_emoji_icon': {'type': Control, 'path': UPath(id_ == "iv_emoji", visible_ == True)},
            'photo_mode_full_page_comment_panel_emoji_panel': {'type': Control,'path': UPath(id_ == "emoji_panel", visible_ == True)},
            'photo_mode_full_page_comment_panel_emoji_panel_indicator': {'type': Control, 'path': UPath(id_ == "emoji_indicator",visible_ == True)},

        })

    # **Start Photo mode**
    def is_photo_mode_full_page_avatar_icon_exist(self):
        return self["avatar_icon"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_username_exist(self):
        return self["username"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_description_exist(self):
        return self["photo_mode_full_page_desc"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_dot_indicator_exist(self):
        return self["photo_mode_full_page_dot_indicator"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_image_view_exist(self):
        return self["photo_mode_full_page_image_view_exist"].wait_for_visible(timeout=10, interval=1,
                                                                              raise_error=False)

    def is_photo_mode_full_page_volume_icon_exist(self):
        return self["photo_mode_full_page_volume_icon"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_anchor_view_exist(self):
        return self["photo_mode_full_page_anchor_view"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_title_exist(self):
        return self["photo_mode_full_page_title"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_add_comment_exist(self):
        return self["photo_mode_full_page_add_comment"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_like_icon_exist(self):
        return self["photo_mode_full_page_like_btn"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_comment_icon_exist(self):
        return self["photo_mode_full_page_comment_btn"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_favorite_icon_exist(self):
        return self["photo_mode_full_page_favorite_btn"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_share_icon_exist(self):
        return self["photo_mode_full_page_share_btn"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_user_icon_exist(self):
        return self["photo_mode_full_page_user_icon"].wait_for_visible(timeout=10, interval=1, raise_error=False)

    def is_photo_mode_full_page_emoji_panel_exist(self):
        return self["photo_mode_full_page_comment_panel_emoji_panel"].wait_for_visible(timeout=10, interval=1,
                                                                                       raise_error=False)

    def is_photo_mode_full_page_emoji_panel_indicator_exist(self):
        return self["photo_mode_full_page_comment_panel_emoji_panel_indicator"].wait_for_visible(timeout=10,
                                                                                                 interval=1,
                                                                                                 raise_error=False)

    def photo_mode_full_navigate_to_emoji_keypad(self):
        self["photo_mode_full_page_comment_btn"].click()
        time.sleep(5)
        self["photo_mode_full_page_comment_btn"].click()
        self["photo_mode_full_page_comment_panel_emoji_icon"].click()

    def photo_mode_full_page_add_comment(self, comment):
        self["photo_mode_full_page_add_comment"].click()
        self["photo_mode_full_page_add_comment_edit_new"].input(comment)
        self["photo_mode_full_page_add_comment_send"].click()
        self["photo_mode_full_page_comment_btn"].click()
        if comment in self["photo_mode_full_page_top_comment"].text:
            return True
        else:
            return False

    def delete_comment(self):
        self["photo_mode_full_page_top_comment"].long_click(duration=2)
        self["delete_comment"].click()

    def swipe_left(self):
        self["content"].swipe(x_direction=1, swipe_coefficient=5)
        time.sleep(1)

    def swipe_right(self):
        self["content"].swipe(x_direction=-1, swipe_coefficient=5)
        time.sleep(1)

    def is_profile_page_visible(self):
        return self["photo_mode_full_page_to_profile"].wait_for_visible(timeout=5, interval=1, raise_error=False)
