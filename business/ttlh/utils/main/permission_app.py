from shoots_android.androidapp import AccessibilityApp
from uibase.controls import Window, Control
from uibase.upath import text_, UPath, visible_, id_


class PermissionApp(AccessibilityApp):
    """
    系统权限弹窗app
    """

    app_spec = {
        "package_name": '',  # 被测app的包名    允许为空
        "init_device": False,
        "process_name": "",
        "grant_all_permissions": False,
        "clear_data": False,
        "kill_process": False
    }

    def __init__(self, device, start_activity, package_name, **kwargs):
        self.app_spec["start_activity"] = start_activity
        self.app_spec["process_name"] = package_name
        super(PermissionApp, self).__init__(device, **kwargs)


class PermissionWindow(Window):
    window_spec = {"activity": "com.*"}

    def get_locators(self):
        return {
            "允许": {'path': UPath(~text_ == '^(允许|始终允许|总是允许|使用时允许|仅使用期间允许|仅使用时允许|仅在使用中允许|仅在使用该应用时允许|使用App时允许|仅在使用此应用时允许|仅在前台使用应用时允许|确定)', visible_ == True)},
            "允许一次": {"path": UPath(~text_ == '^(允许一次|仅本次使用时允许|仅限这一次|本次运行允许)', visible_ == True)},
            "拒绝": {'type': Control, 'path': UPath(~text_ == '^(拒绝|不允许|禁止)')},
            "拒绝且不再询问": {'type': Control, 'path': UPath(text_ == '拒绝且不再询问')},
            "禁止后不再提示": {'type': Control, 'path': UPath(text_ == '禁止后不再提示')},
            "禁止": {'type': Control, 'path': UPath(text_ == '禁止')},
            "取消": {'type': Control, 'path': UPath(text_ == '取消')},
            "GPS": {'type': Control, 'path': UPath(~text_ == '.*(定位|位置)+.*')},
            "通讯录": {'type': Control, 'path': UPath(~text_ == '.*(通讯录|联系人|信息).*')},
            "相机": {'type': Control, 'path': UPath(~text_ == '.*(相机|录制|拍摄|录音|照片|摄像头|麦克风)+.*')},
            "通知": {'type': Control, 'path': UPath(~text_ == '.*通知.*')},
            "拍摄": {'type': Control, 'path': UPath(~text_ == '.*拍摄+.*')},
            "录制": {'type': Control, 'path': UPath(~text_ == '.*录制+.*')},
            "存储": {'type': Control, 'path': UPath(~text_ == '.*(照片|媒体|文件|内容|录音|麦克风|存储)+.*')},
        }
