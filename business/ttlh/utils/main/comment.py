# -*- coding:utf-8 _*-
import os
from shoots_android.control import *
from re import search
from uibase.web import Webview, WebElement
from shoots_android.control import *
from business.ttlh.utils.main.main import *
from uibase.upath import id_, text_, UPath, visible_, type_, href_, class_
import time
import json

from business.ttlh.utils.main import VideoDetailPanel


class CommentPanel(VideoDetailPanel):
    window_spec = {"activity": "com.ss.android.ugc.aweme.detail.ui.DetailActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(VideoDetailPanel, self).__init__(path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "comment_send_new": {"path": UPath(id_ == "comment_send_new", visible_ == True)},
            "comment_send_new_area": {"path":UPath(id_ == "comment_send_new_area", visible_ == True)},
            "first_comment": {"path": UPath(id_ == "recyclerView", visible_ == True) / 0 / UPath(id_ == "content")},
            'close_comment': {'path': UPath(id_ == 'back_btn')},
            'batch_management_button': {'path': UPath(id_ == "refactor_batch_management_btn_top")},
            'comment_tab': {'path': UPath(id_ == "tab_layout") / 0 / 0 / 1},
            'like_tab': {'path': UPath(id_ == "tab_layout") / 0 / 1 / 1},
            'comment_out_wrapper': {"path": UPath(id_ == "out_wrapper")},
            'comment_avatar_new': {"path": UPath(id_ == "comment_avatar_new", visible_ == True)},
            'iv_emoji': {"path": UPath(id_ == "iv_emoji", visible_ == True)},
            'iv_at': {"path": UPath(id_ == "iv_at", visible_ == True)},
            'comment_edit_new': {"path": UPath(id_ == "comment_edit_new", visible_ == True)},
            'view_entrance_text': {"path": UPath(id_ == "view_entrance_text", visible_ == True)},
            'view_entrance_play_vv_icon': {"path": UPath(id_ == "view_entrance_play_vv_icon", visible_ == True)},
            'privacy_btn': {"path": UPath(id_ == "privacy_btn", visible_ == True)},
            'shoot_button_root': {"path": UPath(id_ == "shoot_button_root", visible_ == True)},
            'comment_publish': {'type': Control, 'path': UPath(id_ == 'comment_publish', visible_ == True, index=-1)},
            'comment_out_view': {"path": UPath(id_ == "comment_out_view", visible_ == True)},
            'comment_list': {"type": CommentList, "path": UPath(id_ == "recyclerView", visible_ == True)},
            'content_comment': {'path': UPath(id_ == 'fl_comment_container')},
            "comment_delete": {"path": UPath(id_ == 'text1', text_ == 'Delete')}

        })

    def comment_input(self, content):
        self["comment_edit_new"].refresh()
        self["comment_edit_new"].wait_for_visible()
        self.app.get_device().click(self['comment_edit_new'].rect.center[0], self['comment_edit_new'].rect.center[1])
        time.sleep(1)
        self["comment_edit_new"].text = content
        time.sleep(2)
        self.swipe_up()
        self["comment_send_new_area"].click()
        time.sleep(1)
        return
    
    def get_comment_list(self):
        return self["comment_list"].items()

    def get_comment(self,index):
        return self["comment_list"].items()[index]

    def get_comment_text(self, index):
        return self["comment_list"].items()[index].get_comment_text()

    def delete_comment(self, comment):
        if comment is not None:
            comment.long_click(duration=2)
            self["comment_delete"].click()


class Comment(Control):
    def get_locators(self):
        return {
            'content': {"path": UPath(id_ == "content")},


        }

    def get_comment_text(self):
        return self["content"].text



class CommentList(Control):
    elem_class = Comment
    elem_path = UPath(id_ == "layout_root")
