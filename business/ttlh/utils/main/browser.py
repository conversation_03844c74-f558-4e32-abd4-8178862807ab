# -*- coding:utf-8 _*-
from shoots_android.control import *
from uibase.upath import id_

class Browser_panel(Window):

    window_spec = {"activity": "com.android.quicksearchbox.SearchActivity"}

    def get_locators(self):
        return {
            'input_edit': {'path': UPath(id_ == 'search_src_text')},
            'search_btn': {'path': UPath(id_ == 'search_go_btn')},
            'descargar_tiktok': {'path': UPath(text_ == "Descargar TikTok")},
        }

    def input_and_search(self, text):
        self.input_edit.send_keys(text)
        self.search_btn.click()
        self.descargar_tiktok.click()
