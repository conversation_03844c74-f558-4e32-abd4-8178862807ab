# -*- coding: utf-8 -*-

from uibase.upath import *
from uibase.controls import Window


class AddYoursTopicDetailFragmentInChatRoomPanel(Window):
    """activity=com.ss.android.ugc.aweme.im.sdk.chat.ui.activity.ChatRoomActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity
    """
    window_spec = {
        "path": UPath(fragment_ == "AddYoursTopicDetailFragment")
    }

    def get_locators(self):
        return {
        'title': {'path': UPath(id_ == 'title', index=0)},
        'video_count': {'path': UPath(id_ == 'video_count')},
        'link_invite': {'path': UPath(id_ == 'link_invite')},
        'New prom': {'path': UPath(text_ == 'New prompt')},
        'btn_post_new': {'path': UPath(id_ == 'btn_post_new')},
        'btn_new_prompt': {'path': UPath(id_ == 'btn_new_prompt')},
        }

    def wait_for_title_text(self):
        self['title'].wait_for_text('^哈哈哈刚刚好$')

    def wait_for_video_count_text(self):
        self['video_count'].wait_for_text('^1 video$')

    def wait_for_link_invite_text(self):
        self['link_invite'].wait_for_text('^+ Invite friends$')

    def click_link_invite(self):
        self['link_invite'].click()

    def wait_for_new_prom_visible(self):
        self['New prom'].wait_for_visible()

    def wait_for_btn_post_new_text(self):
        self['btn_post_new'].wait_for_text('.*')

    def click_btn_new_prompt(self):
        self['btn_new_prompt'].click()


