# -*- coding: utf-8 -*-

from uibase.upath import *
from uibase.controls import Window


class AddYoursHubFragmentInChatRoomPanel(Window):
    """activity=com.ss.android.ugc.aweme.im.sdk.chat.ui.activity.ChatRoomActivity|com.ss.android.ugc.aweme.host.TikTokHostActivity
    """
    window_spec = {
        "path": UPath(fragment_ == "AddYoursHubFragment")
    }

    def get_locators(self):
        return {
        'TuxIconView': {'path': UPath(type_ == 'TuxIconView', visible_ == True)},
        'nav_bar_title': {'path': UPath(id_ == 'nav_bar_title')},
        'Added 2': {'path': UPath(~text_ == 'Added .*')},
        'To add 4': {'path': UPath(~text_ == 'To add .*')},
        'power_list__0_video_cover': {'path': UPath(id_ == 'power_list', visible_ == True) / 0 / UPath(id_ == 'video_cover')},
        'profile你那边步步高广告哈哈哈宝宝VVVW个VW斑斑驳驳Vw斑斑驳驳': {'path': UPath(text_ == 'profile你那边步步高广告哈哈哈宝宝VVVW个VW斑斑驳驳Vw斑斑驳驳')},
        'power_list__0_video_view': {'path': UPath(id_ == 'power_list', visible_ == True) / 0 / UPath(id_ == 'video_view')},
        'power_list__0_invite_button': {'path': UPath(id_ == 'power_list', visible_ == True) / 0 / UPath(id_ == 'invite_button')},
        'power_list__0_topic_info_container': {'path': UPath(id_ == 'power_list', visible_ == True) / 0 / UPath(id_ == 'topic_info_container')},
        }

    def click_tux_icon_view(self):
        self['TuxIconView'].click()

    def wait_for_nav_bar_title_text(self):
        self['nav_bar_title'].wait_for_text('^Add Yours$')

    def wait_for_added_text(self):
        self['Added 2'].wait_for_text('^Added 2$')

    def wait_for_to_add_text(self):
        self['To add 4'].wait_for_text('^To add 4$')

    def wait_for_power_list_video_cover_visible(self):
        self['power_list__0_video_cover'].wait_for_visible()

    # def wait_for_ha_ha_ha_just_right_text(self):
    #     self['profile你那边步步高广告哈哈哈宝宝VVVW个VW斑斑驳驳Vw斑斑驳驳'].wait_for_text('^profile你那边步步高广告哈哈哈宝宝VVVW个VW斑斑驳驳Vw斑斑驳驳$')

    def wait_for_power_list_video_view_text(self):
        self['power_list__0_video_view'].wait_for_text('^1 video$')

    def wait_for_power_list_invite_button_text(self):
        self['power_list__0_invite_button'].wait_for_text('^Invite$')

    def click_power_list_topic_info_container(self):
        self['power_list__0_topic_info_container'].click()

    def wait_for_added_visible(self):
        self['Added 2'].wait_for_visible()


