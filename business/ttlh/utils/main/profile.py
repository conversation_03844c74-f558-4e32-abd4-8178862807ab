# -*- coding:utf-8 _*-
import time

from shoots_android.control import *
from shoots_cv.cv import CV
from uibase.upath import id_

from .base import BasePanel, FeedsList
from business.ttlh.utils.tt_popup import InboxFollowFriendPopup
from uibase.upath import UPath, class_
from uibase.web import WebElementList
from uibase.web import Webview, WebElement


class Action(Control):
    def get_locators(self):
        return {
            'share_action_label': {"type": Control, 'path': UPath(id_ == 'share_action_label')},
        }

    @property
    def action_name(self):
        return self["share_action_label"].text


class ActionList(FeedsList):
    elem_class = Action


class ProfilePanel(BasePanel):
    """个人主页
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.(splash.SplashActivity|profile.ui.UserProfileActivity|profile.ui.UserProfileActivity|host.TikTokHostActivity)"}

    report_schema_url = "snssdk1233://user/profile/6833646635634574341" \
                        "?gd_label=click_push_live_steam&type=123&source_aid=111&profile_type=1"

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(ProfilePanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            "title": {"type": Control, "path": UPath(id_ == 'title', visible_ == True)},
            "user_id": {"type": Control, "path": UPath(id_ == 'user_id')},
            "profile_head": {"type": Control, "path": UPath(id_ == "profile_head")},
            "header": {"path": UPath(id_ == 'header_image')},
            "profile_btn_report": {"type": Control, "path": UPath(id_ == "profile_btn_report")},
            'share_action_layout': {"type": Control, 'path': UPath(~id_ == 'share_panel_action_container|im_action_container')},
            'action_list': {"type": ActionList, "root": "share_action_layout", 'path': UPath(~id_ == 'action_list|action_vertical_list')},
            'follow_iv': {"type": Button, "path": UPath(id_ == 'follow_iv')},
            'profile_btn_extra': {"type": Button, "path": UPath(id_ == "profile_btn_extra")},
            "back_btn": {"path": UPath(id_ == 'nav_start')/UPath(type_ == "com.bytedance.tux.icon.TuxIconView")},
            "music_icon": {"type": Control,
                           "path": UPath(id_ == 'tab_container') / 0 / UPath(id_ == 'icon')},
            "video_icon": {"type": Control,
                           "path": UPath(desc_ == 'Videos', visible_ == True) / UPath(id_ == 'icon')},
            "message_icon": {"path:": UPath(id_ == 'follow_iv', text_ == 'Message')},
            "story_label": {"path": UPath(id_ == 'tv_top')},
            "profile_banner": {"type": Control, "path": UPath(id_ == 'tv_text')},
            "add_story_btn_avatar": {"type": Control, "path": UPath(id_ == 'ic_story', visible_ == True)},
            "profile_videos_list": {"path": UPath(id_ == "feed_list", visible_ == True)},
            "text_area": {"path": UPath(id_ == "edit_input_layout")},
            "send": {"path": UPath(id_ == "send_btn")},
            "tako_videos_list": {"path": UPath(id_ == "video_list", visible_ == True, index=0)}
        })

    def get_tako_videos_list(self):
        self["tako_videos_list"].refresh()
        return self["tako_videos_list"].items()

    def watch_the_video_from_tako_replay(self):
        for _ in Retry(timeout=50, interval=1):
            if self["tako_videos_list"].wait_for_existing(timeout=5,raise_error=False) and len(self.get_tako_videos_list()) > 0:
                self.swipe(y_direction=1, swipe_coefficient=3)
                self.get_tako_videos_list()[1].click()
                break
            else:
                self.swipe(y_direction=1, swipe_coefficient=5)

    def ask_tako_bot_question(self, text):
        inboxFollowFriendPopup = InboxFollowFriendPopup(root=self.app)
        inboxFollowFriendPopup.handle()
        self["text_area"].input(text)
        time.sleep(5)
        self["send"].click()

    def get_profile_videos_list(self):
        self["profile_videos_list"].refresh()
        return self["profile_videos_list"].items()

    def play_normal_user_profile_video(self):
        if len(self.get_profile_videos_list()) > 0:
            return self.get_profile_videos_list()[1].click()

    def click_video_icon(self):
        if self["music_icon"].wait_for_existing(timeout=5,raise_error=False):
            print("video的坐标",self["video_icon"].rect)
            self["video_icon"].click()
        self.wait_for_loading()

    def click_back_btn(self):
        if self["back_btn"].wait_for_existing(timeout=5,raise_error=False):
            self["back_btn"].click()

    def click_add_story_from_avatar(self):
        if self["add_story_btn_avatar"].wait_for_existing(timeout=5, raise_error=False):
            self["add_story_btn_avatar"].click()

    @property
    def title(self):
        time.sleep(1)
        return self["title"].text

    @property
    def user_name(self):
        time.sleep(1)
        return self["user_id"].text

    def check_profile(self):
        return self["profile_head"].wait_for_visible()

    def swipe_up_to_bottom(self, count_default=3):
        self["feeds"].wait_for_existing()
        self.swipe(y_direction=1)
        time.sleep(5)
        last_feed = self["feeds"].items()[-1]  # 获取一开始可见的最后一个视频控件
        count = count_default
        while True and count >= 0:
            self.swipe(y_direction=1)
            feed = self["feeds"].items()[-1]  # 获取当前可见的最后一个视频控件
            if feed.id.id == last_feed.id.id:
                return
            last_feed = feed  # 最后一个视频变动了，说明还没滑倒底部，继续上滑
            count -= 1

    def get_video(self, index):
        return self["feeds"].items()[index]

    def report(self):
        self["profile_btn_report"].click()
        report_btn = self.get_share_action("Report")
        report_btn.click()

    def get_share_action(self, action_name):
        action_list = self["action_list"].items()
        for action in action_list:
            if action_name in action.action_name:
                return action
        return None

    def click_story_feed(self):
        if self["story_label"].wait_for_visible(timeout=2, raise_error=False):
            self["story_label"].click()


class ReportPanel(Window):
    window_spec = {"activity": "com.ss.android.ugc.aweme.compliance.business.report.ReportWebPageDialogActivity"}

    def get_locators(self):
        return {
            "webview": {"type": ReportWebview, "path": UPath(id_ == "ame_rn_web_container")},
        }

    def report_user(self):
        self.webview.report_user()

    def report_video(self):
        self.webview.report_video()

    def wait_for_thanks_report(self):
        return self.webview.wait_for_thanks_report()

    def close(self):
        self.webview.close()


class ReportWebview(Webview):
    view_spec = {
        "description": "\"attached\":true.*\"visible\":true"
    }

    def get_locators(self):
        return {
            "report_account": {"type": WebElement, "path": UPath(text_ == "Report account")},
            "Other": {"type": WebElement, "path": UPath(text_ == "Other")},
            "animal_cruelty": {"type": WebElement, "path": UPath(text_ == "Animal cruelty")},
            "submit": {"type": WebElement, "path": UPath(id_ == "submitBtn")},
            "thanks_msg": {"type": WebElement, "path": UPath(text_ == "Thanks for reporting")},
            "close_btn": {"type": WebElement, "path": UPath(class_ == "navbar-btn")},
        }

    def report_user(self):
        self["report_account"].click()
        self["Other"].click()
        self["submit"].click()

    def report_video(self):
        self["animal_cruelty"].click()
        self["submit"].click()

    def wait_for_thanks_report(self):
        return self["thanks_msg"].wait_for_visible(raise_error=False)

    def close(self):
        return self["close_btn"].click()

class ProfileRelationBottom(Window):

    window_spec = {"activity": "com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            'my_QNA':{'type': Control, 'path': UPath(id_ == "qna_header_title")},
            'my_QNA_back':{'type': Control, 'path': UPath(id_ == "qna_back_btn")},
            'suggest_account_othersprofile_title':{'type': Control, 'path': UPath(id_ == 'nav_bar_title')},
            'suggest_account_othersprofile_follow':{'type': Control, 'path': UPath(id_ == 'profile_btn_extra')},
            'suggest_account_othersprofile_unfollow':{'type': Control, 'path': UPath(id_ == 'follow_iv', visible_ == True)},
            "other_profile_follower": {'type': Control, 'path': UPath(id_ == 'follower_count_desc')},
            "other_recommend_button": {'type': Control, 'path': UPath(id_ == 'recommend_users')},
            "suggest_account_text": {'type': Control, 'path': UPath(id_ == "title_view")},
            "suggest_account_text1": {'type': Control, 'path': UPath(id_ == "tv_recommend")},
            "bio_text": {'type': Control, 'path': UPath(id_ == 'user_signature')},
            "bell_icon": {"path": UPath(id_ == 'bell_icon')},
            "half_size_username": {"path": UPath(id_ == 'follow_title')},
            "more_button": {"path": UPath(id_ == 'nav_end')/1},
            "block_button": {"type": Control, "path": UPath(id_ == 'action_list')/2/UPath(id_ == 'share_action_icon')},
            "block_button1": {"type": Control, "path": UPath(text_ == "Block", visible_ == True)},
            "block_large_text": {"type": Control, "path": UPath(id_ == 'title_tv')},
            "block_small_text": {"type": Control, "path": UPath(id_ == 'content_tv')},
            "block_confirm_button": {"type": Control, "path": UPath(text_ == 'Block')},
            "back_button": {"type": Control, "path":UPath(id_ == 'nav_start', visible_ == True)/UPath(type_ == 'com.bytedance.tux.icon.TuxIconView',visible_ == True)},
            "back_button1": {"type": Control, "path":UPath(id_ == "nav_start",visible_==True) / 0},
            "other_page_nickname": {"type": Control, "path": UPath(id_ == 'nav_bar_title')},
            "other_page_username": {"type": Control, "path": UPath(id_ == 'user_id', visible_ == True)},
            "sns_button": {"type": Control, "path": UPath(id_ == 'bind_account', visible_ == True)},
            "h5_link": {"type": Control, "path": UPath(id_ == 'user_web_site')},
            "other_page_contact_find": {"type": Control, "path": UPath(type_ == "LegacyPermissionLayout") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "permission_find_btn")},
            "other_page_facebook_find": {"type": Control, "path": UPath(type_ == "LegacyPermissionLayout") / UPath(type_ == "RelativeLayout", index=1) / UPath(id_ == "permission_find_btn")},
            "permissios_ok": {"type": Control, "path": UPath(text_ == "OK")},
            "unblock_btn": {"type": Control, "path": UPath(id_ == "profile_btn_extra", visible_ == True)},
            "other_page_editprofile": {"type": Control, "path": UPath(id_ == "cta_container") / 0 / UPath(type_ == "TuxTextView", visible_ == True)},
            "other_page_editprofile1": {"type": Control, "path": UPath(text_ == "Edit profile", visible_ == True)},
        }
    def GetMyQAtext(self):
        return self["my_QNA"].text

    def ClickMyQNABackBtn(self):
        self["my_QNA_back"].click()
        time.sleep(3)

    def check_other_page_editprofile(self):
        if self["other_page_editprofile1"].wait_for_existing(timeout=5, raise_error=False):
            return self["other_page_editprofile1"].wait_for_existing(timeout=5, raise_error=True)
        elif self["other_page_editprofile"].wait_for_existing(timeout=5, raise_error=True):
            return self["other_page_editprofile"].wait_for_existing(timeout=5, raise_error=True)

    def check_unblock_btn(self):
        return self["unblock_btn"].wait_for_existing(timeout=5, raise_error=False)

    #点击other页面contact find按钮
    def click_other_page_contact_find(self):
        if self["other_page_contact_find"].wait_for_existing(timeout=5, raise_error=False):
            self["other_page_contact_find"].click()
            time.sleep(3)

    #点击other页面facebook find按钮
    def click_other_page_facebook_find(self):
        if self["other_page_facebook_find"].wait_for_existing(timeout=5, raise_error=False):
            self["other_page_facebook_find"].click()
            time.sleep(3)

    #permission Ok按钮
    def check_permission_ok(self):
        return self["permissios_ok"].wait_for_existing(timeout=5, raise_error=False)

    #点击他人页面h5链接
    def click_h5_link(self):
        if self["h5_link"].wait_for_existing(timeout=5, raise_error=False):
            self["h5_link"].click()
            time.sleep(3)

    def get_other_page_nickname(self):
        time.sleep(3)
        return self["other_page_nickname"].text

    def get_other_page_username(self):
        if self["other_page_username"].wait_for_existing(timeout=5, raise_error=False):
            username = self["other_page_username"].text
            return username[1:]

    def get_nickname(self):
        self["suggest_account_othersprofile_title"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggest_account_othersprofile_title"].text

    def check_sns_button(self):
        return self["sns_button"].wait_for_existing(timeout=5, raise_error=False)

    def get_block_large_text(self):
        return self["block_large_text"].text

    def get_block_small_text(self):
        return self["block_small_text"].text

    def click_block_button(self):
        if self["back_button1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["block_button1"].click()
            time.sleep(3)
        else:
            self["block_button"].click()
            time.sleep(3)

    def click_more_button(self):
        self["more_button"].click()
        time.sleep(3)

    def click_bell_icon(self):
        self["bell_icon"].click()
        time.sleep(3)

    def check_half_size_username(self):
        return self["half_size_username"].text

    def click_block_confirm_button(self):
        self["block_confirm_button"].click()
        time.sleep(3)

    def click_back_button(self):
        if self["back_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["back_button"].click()
            time.sleep(3)
        elif self["back_button1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["back_button1"].click()
            time.sleep(3)

    def check_bio_text(self):
        return self["bio_text"].wait_for_existing(timeout=5, raise_error=True)

    def get_suggest_account_text(self):
        if self["suggest_account_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self["suggest_account_text"].text
        elif self["suggest_account_text1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
                return self["suggest_account_text1"].text

    def CheckOtherSuggestAccount(self):
        if self["suggest_account_text"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            return self["suggest_account_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        elif self["suggest_account_text1"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            return self["suggest_account_text1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def click_other_recommend_button(self):
        self["other_recommend_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["other_recommend_button"].click()
        time.sleep(3)

    def click_other_profile_follower(self):
        self["other_profile_follower"].click()
        time.sleep(3)

    def click_otherprofile_follow(self):
        self['suggest_account_othersprofile_follow'].click()

    def check_otherprofile_follow(self):
        return self["suggest_account_othersprofile_follow"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)


    def click_otherprofile_unfollow(self):
        self['suggest_account_othersprofile_unfollow'].click()


class SelfProfileSuggestedPageRecommendUserhead(Control):
    def get_locators(self):
        return {
            "userhead": {"path": UPath(id_ == "avatar_view", visible_ == True)},
        }

class SelfProfileSuggestedPageRecommendUserheadList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout", visible_ == True) / UPath(id_ == "start_area_layout", visible_ == True)
    elem_class = SelfProfileSuggestedPageRecommendUserhead

class SelfProfileSuggestedPageRecommendUsername(Control):
    def get_locators(self):
        return {
            "username": {"path": UPath(id_ == "nickNameView")},
            "userlabel": {"path": UPath(id_ == "relation_label")},
        }

class SelfProfileSuggestedPageRecommendUsernameList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "middle_area_layout")
    elem_class = SelfProfileSuggestedPageRecommendUsername

class SelfProfileSuggestedPageRecommendRelation(Control):
    def get_locators(self):
        return {
            "relationBtn": {"path": UPath(id_ == "relationBtn")},
        }

class SelfProfileSuggestedPageRecommendRelationList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "relationBtn")
    elem_class = SelfProfileSuggestedPageRecommendRelation

class SelfProfileSuggestedPageRecommendDelete(Control):
    def get_locators(self):
        return {
            "deleteIconView": {"path": UPath(id_ == "deleteIconView")},
        }

class SelfProfileSuggestedPageRecommendDeleteList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "deleteIconView")
    elem_class = SelfProfileSuggestedPageRecommendDelete


class SelfProfileSuggestedRecommendPanel(BasePanel):
    window_spec = {"activity": "com.ss.android.ugc.aweme.following.ui.FollowRelationTabActivity"}

    def get_locators(self):
        return {
            #friends tab
            "friends_btn": {"type": Control,"path": UPath(text_ == "Friends 0")},
            "empty_friends_title": {"type": Control,"path": UPath(id_ == "empty_title")},
            "empty_friends_desc": {"type": Control,"path": UPath(id_ == "empty_desc")},
            "find_friends_title": {"type": Control,"path": UPath(id_ == "recommend_user_title", visible_ == True)},
            "find_friends_title_i": {"type": Control,"path": UPath(id_ == "info_icon_view", visible_ == True)},
            "find_friends_title_i_info": {"type": Control,"path": UPath(id_ == "tv_suggest")},
            #suggested tab
            "suggested_btn": {"type": Control,"path": UPath(text_ == "Suggested", visible_ == True)},
            "suggested_tab_userhead": {"type": SelfProfileSuggestedPageRecommendUserheadList,"path": UPath(id_ == "rv_list", visible_ == True)},
            "suggested_tab_userinfo": {"type": SelfProfileSuggestedPageRecommendUsernameList,"path": UPath(id_ == "rv_list", visible_ == True)},
            "suggested_tab_relation": {"type": SelfProfileSuggestedPageRecommendRelationList,"path": UPath(id_ == "rv_list", visible_ == True)},
            "suggested_tab_deleteIconView": {"type": SelfProfileSuggestedPageRecommendDeleteList,"path": UPath(id_ == "rv_list", visible_ == True)},
        }
    def CheckSuggestedPageRecommendUserhead(self):
        list = []
        for item in self["suggested_tab_userhead"].items():
            list.append(item["userhead"].visible)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendUserhead(self, value=0):
        self["suggested_tab_userhead"].items()[value]["userhead"].click()
        time.sleep(3)

    def GetSuggestedPageRecommendUsername(self):
        list = []
        for item in self["suggested_tab_userinfo"].items():
            list.append(item["username"].text)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendUsername(self, value=0):
        self["suggested_tab_userinfo"].items()[value]["username"].click()
        time.sleep(3)

    def GetSuggestedPageRecommendUserLabel(self):
        list = []
        for item in self["suggested_tab_userinfo"].items():
            list.append(item["userlabel"].text)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendUserLable(self, value=0):
        self["suggested_tab_userinfo"].items()[value]["userlabel"].click()
        time.sleep(3)

    def GetSuggestedPageRecommendRelationBtn(self):
        list = []
        for item in self["suggested_tab_relation"].items():
            list.append(item["relationBtn"].text)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendRelationBtn(self, value=0):
        self["suggested_tab_relation"].items()[value]["relationBtn"].click()
        time.sleep(3)

    def CheckSuggestedPageRecommendDeleteBtn(self):
        list = []
        for item in self["suggested_tab_deleteIconView"].items():
            list.append(item["deleteIconView"].visible)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendDeleteBtn(self, value=0):
        self["suggested_tab_deleteIconView"].items()[value]["deleteIconView"].click()
        time.sleep(3)

    def GetProfileSuggestedTitle(self):
        if self["suggested_btn"].wait_for_visible():
            return self["suggested_btn"].text

    def ClickProfileSuggestedBtn(self):
        if self["suggested_btn"].wait_for_visible():
            self["suggested_btn"].click()
            time.sleep(3)

    def GetProfileFriendsTitle(self):
        if self["friends_btn"].wait_for_visible():
            return self["friends_btn"].text

    def GetProfileFindFriendsTitle(self):
        if self["find_friends_title"].wait_for_visible():
            return self["find_friends_title"].text

    def ClickProfileFindFriendsIBtn(self):
        if self["find_friends_title_i"].wait_for_visible():
            self["find_friends_title_i"].click()
            time.sleep(3)

    def GetProfileEmptyFriendsTitle(self):
        if self["empty_friends_title"].wait_for_visible():
            return self["empty_friends_title"].text

    def GetProfileFindFriendsInfo(self):
        if self["find_friends_title_i_info"].wait_for_visible():
            return self["find_friends_title_i_info"].text

    def GetProfileEmptyFriendsDesc(self):
        if self["empty_friends_desc"].wait_for_visible():
            return self["empty_friends_desc"].text

    def ClickProfileFriendsBtn(self):
        if self["friends_btn"].wait_for_visible():
            self["friends_btn"].click()
            time.sleep(3)

class SelfProfileFriendsTabRecommendPanel(BasePanel):
    window_spec = {"activity": "com.ss.android.ugc.aweme.following.ui.FollowRelationTabActivity"}
    def get_locators(self):
        return {
        #friends tab
            "suggested_tab_userhead": {"type": SelfProfileSuggestedPageRecommendUserheadList,"path": UPath(id_ == "power_list", visible_ == True)},
            "suggested_tab_userinfo": {"type": SelfProfileSuggestedPageRecommendUsernameList,"path": UPath(id_ == "power_list", visible_ == True)},
            "suggested_tab_relation": {"type": SelfProfileSuggestedPageRecommendRelationList,"path": UPath(id_ == "power_list", visible_ == True)},
            "suggested_tab_deleteIconView": {"type": SelfProfileSuggestedPageRecommendDeleteList,"path": UPath(id_ == "power_list", visible_ == True)},
        }

    def CheckSuggestedPageRecommendUserhead(self):
        list = []
        for item in self["suggested_tab_userhead"].items():
            list.append(item["userhead"].visible)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendUserhead(self, value=0):
        self["suggested_tab_userhead"].items()[value]["userhead"].click()
        time.sleep(3)

    def GetSuggestedPageRecommendUsername(self):
        list = []
        for item in self["suggested_tab_userinfo"].items():
            list.append(item["username"].text)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendUsername(self, value=0):
        self["suggested_tab_userinfo"].items()[value]["username"].click()
        time.sleep(3)

    def GetSuggestedPageRecommendUserLabel(self):
        list = []
        for item in self["suggested_tab_userinfo"].items():
            list.append(item["userlabel"].text)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendUserLable(self, value=0):
        self["suggested_tab_userinfo"].items()[value]["userlabel"].click()
        time.sleep(3)

    def GetSuggestedPageRecommendRelationBtn(self):
        list = []
        for item in self["suggested_tab_relation"].items():
            list.append(item["relationBtn"].text)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendRelationBtn(self, value=0):
        self["suggested_tab_relation"].items()[value]["relationBtn"].click()
        time.sleep(3)

    def CheckSuggestedPageRecommendDeleteBtn(self):
        list = []
        for item in self["suggested_tab_deleteIconView"].items():
            list.append(item["deleteIconView"].visible)
        self.app.testcase.log_info(list)
        return list

    def CheckSuggestedPageRecommendDeleteBtn(self):
        list = []
        for item in self["suggested_tab_deleteIconView"].items():
            list.append(item["deleteIconView"].visible)
        self.app.testcase.log_info(list)
        return list

    def ClickSuggestedPageRecommendDeleteBtn(self, value=0):
        self["suggested_tab_deleteIconView"].items()[value]["deleteIconView"].click()
        time.sleep(3)

class OthersProfilePanel(BasePanel):

    window_spec = {
        "activity":
        'com.ss.android.ugc.aweme.splash.SplashActivity|com.ss.android.ugc.aweme.splash.SplashActivity#1|com.ss.android.ugc.aweme.host.TikTokHostActivity'}
    def get_locators(self):
        return {
            "update_nickname_inputbox": {"type": Control, "path": UPath(id_ == "et_input")},
            "update_nickname_inputbox_save": {"type": Control, "path": UPath(text_ == "Save")},
            "update_nickname_inputbox_confirm": {"type": Control, "path": UPath(text_ == "Confirm")},
            "suggested_account_hide": {"type": Control, "path": UPath(id_ == "see_all_view", visible_ == True)},
            "add_photo": {"type": Control, "path": UPath(text_ == "Add", visible_ == True)},
            "add_bio": {"type": Control, "path": UPath(id_ == "user_signature", visible_ == True)},
            "no_works_text": {"type": Control, "path": UPath(id_ == "share_your_first_video", visible_ == True)},
            "upload_btn": {"type": Control, "path": UPath(id_ == "upload_work", visible_ == True)},
            "live_event": {"type": Control, "path": UPath(id_ == "advanced_content") / 2 / UPath(id_ == "advanced_feature_text")},
            "tips": {"type": Control, "path": UPath(text_ == "Tips")},
            "SupportingRED": {"type": Control, "path": UPath(text_ == "Supporting: RED")},
            "SupportingREDDelete": {"type": Control, "path": UPath(id_ == "back_btn")},
            "Q&A": {"type": Control, "path": UPath(text_ == "Q&A")},
            "getquote": {"type": Control, "path": UPath(text_ == "Get quote")},
            "selfprofile_userhead": {"type": Control, "path": UPath(id_ == "avatar_border_view", visible_ == True)},
            "other_page_nickname": {"type": Control, "path": UPath(id_ == 'nav_bar_title', visible_ == True)},
            "find_friend": {"type": Control, "path": UPath(id_ == 'nav_start', visible_ == True)/1},
            "find_friend1": {"type": Control, "path": UPath(text_ == "Add friends", visible_ == True)},
            "find_friend2": {"type": Control, "path": UPath(id_ == "cta_container") / 1 / UPath(id_ == "feature_text_and_alert", visible_ == True)},
            "live_event_button": {"type": Control, "path": UPath(id_ == 'nav_end')/1},
            "profile_header_image": {"type": Control, "path": UPath(id_ == 'header_image')},
            "profile_header_image1": {"type": Control, "path": UPath(id_ == "avatar_layout_view") / 0},
            "update_header": {"type": Control, "path": UPath(id_ == 'profile_share_edit')},
            "edit_profile": {"type": Control, "path": UPath(text_ == "Edit profile", visible_ == True)},
            "edit_profile1": {"type": Control, "path": UPath(text_ == "编辑个人主页", visible_ == True)},
            "bio_text": {"type": Control, "path": UPath(id_ == 'user_signature')},
            "url_path": {"type": Control, "path": UPath(id_ == 'user_web_site')},
            "follow_btn": {"type": Control, "path": UPath(id_ == 'profile_btn_extra')},
            "follow_btn1": {"type": Control, "path": UPath(id_ == "follow", visible_ == True)},
            "unfollow_btn": {"type": Control, "path": UPath(id_ == 'follow_iv')},
            "following_button": {"type": Control, "path": UPath(id_ == 'following_count')},
            "back_button": {"type": Control, "path":UPath(id_ == 'nav_start', visible_ == True)/UPath(type_ == 'com.bytedance.tux.icon.TuxIconView', visible_ == True)},
            "back_button1": {"type": Control, "path":UPath(id_ == "nav_start", visible_ == True) / 0},
            "message_text": {"type": Control, "path": UPath(id_ == 'send_message_btn')},
            "profilepage_following_button": {"type": Control, "path": UPath(id_ == 'following_count_desc', visible_ == True)},
            "profilepage_following_button1": {"type": Control, "path": UPath(id_ == "fragment_detail_volume_button_stub", visible_ == True)},
            "profilepage_following_button2": {"type": Control, "path": UPath(text_ == "Following", visible_ == True)},
            "profilepage_following_button3": {"type": Control, "path": UPath(id_ == 'following_count_desc')},
            "profilepage_following_button4": {"type": Control, "path": UPath(id_ == "fragment_detail_volume_button_stub")},
            "profilepage_follower_button": {"type": Control, "path": UPath(id_ == 'follower_count_desc')},
            "more_button": {"path": UPath(id_ == 'nav_end')/1},
            "block_button": {"type": Control, "path": UPath(id_ == 'action_list')/2/UPath(id_ == 'share_action_icon')},
            "block_button1": {"type": Control, "path": UPath(text_ == "Block", visible_ == True)},
            "block_large_text": {"type": Control, "path": UPath(id_ == 'title_tv')},
            "block_small_text": {"type": Control, "path": UPath(id_ == 'content_tv')},
            "block_confirm_button": {"type": Control, "path": UPath(text_ == 'Block')},
            "settings_button": {"type": Control, "path": UPath(id_ == "nav_end", visible_ == True) / UPath(type_ == "TuxIconView", index=1)},
            "settings_button1": {"type": Control, "path": UPath(id_ == "nav_end") / UPath(type_ == "TuxIconView", index=1)},
            "settings_button2": {"type": Control, "path": UPath(id_ == "nav_end") / 2},
            "settings_and_privacy": {"type": Control, "path": UPath(text_ == "Settings and privacy")},
            "nickname": {"type": Control, "path": UPath(id_ == 'nav_bar_title', visible_ == True)},
            "other_page_username": {"type": Control, "path": UPath(id_ == 'user_id', visible_ == True)},
            "switch_account": {"type": Control, "path": UPath(id_ == 'sheet_nav_bar_container')/UPath(id_ == 'nav_bar_title', visible_ == True)},
            "visitor_button": {"type": Control, "path": UPath(id_ == 'iv_avatar')},
            "sns_button": {"type": Control, "path": UPath(id_ == 'bind_account', visible_ == True)},
            "like_tab": {"type": Control, "path": UPath(id_ == 'tab_container')/3},
            "like_tab_video": {"type": Control, "path": UPath(id_ == 'feed_list', visible_ == True)/1/UPath(id_ == 'cover')},
            "bell_icon": {"path": UPath(id_ == 'bell_icon')},
            "half_size_username": {"path": UPath(id_ == 'follow_title')},
            "profilepage_user_name":{"path": UPath(id_ == "user_id")},
            "profile_qr_code_button": {"path": UPath(id_ == "profile_qrcode")},
            "unblock_btn": {"type": Control, "path": UPath(id_ == "profile_btn_extra", visible_ == True)},
            "liek_btn": {"type": Control, "path": UPath(id_ == "diag_count_desc", visible_ == True)},
            "liek_popup_text": {"type": Control, "path": UPath(id_ == "title_tv", visible_ == True)},
            "profile_email_btn": {"type": Control, "path": UPath(id_ == "transform_first_tv", visible_ == True)},
            "profile_email_btn1": {"type": Control, "path": UPath(id_ == "enterprise_text", visible_ == True)},
            "profile_bio_btn": {"type": Control, "path": UPath(id_ == "user_signature", visible_ == True)},
            "profile_bio_platform_btn": {"type": Control, "path": UPath(id_ == "mine_signature", visible_ == True)},
            "profile_bio_platform_btn1": {"type": Control, "path": UPath(id_ == "profile_platform_head_view", visible_ == True) / 3 / 0},
        }
    def InputSelfProfileNickname(self, value):
        self["update_nickname_inputbox"].text = value
        time.sleep(3)
        self["update_nickname_inputbox_save"].click()
        time.sleep(3)
        self["update_nickname_inputbox_confirm"].click()
        time.sleep(3)

    def CheckSelfProfileSuggestedAccountHideBtn(self):
        if self["suggested_account_hide"].wait_for_visible(timeout=5, raise_error=False):
            self["suggested_account_hide"].click()
            time.sleep(3)

    def CheckSelfProfileAddNameBtn(self):
        return self["nickname"].wait_for_visible(timeout=5, raise_error=True)

    def CheckSelfProfileAddPhotoBtn(self):
        return self["add_photo"].wait_for_visible(timeout=5, raise_error=True)

    def CheckSelfProfileAddBioBtn(self):
        return self["add_bio"].wait_for_visible(timeout=5, raise_error=False)

    def ClickSelfProfileAddBioBtn(self):
        if self["add_bio"].wait_for_visible(timeout=5, raise_error=True):
            self["add_bio"].click()
            time.sleep(3)

    def GetSelfProfileNoWorksText(self):
        return self["no_works_text"].text

    def CheckSelfProfileNoWorksUploadBtn(self):
        return self["upload_btn"].wait_for_visible(timeout=5, raise_error=True)

    def CheckSelfProfileNonprofitBtn(self):
        return self["SupportingRED"].wait_for_visible(timeout=5, raise_error=True)

    def CheckSelfProfileTipsBtn(self):
        return self["tips"].wait_for_visible(timeout=5, raise_error=True)

    def CheckSelfProfileLiveEventBtn(self):
        return self["live_event"].wait_for_visible(timeout=5, raise_error=True)

    def ClickSelfProfileNonprofitBtn(self):
        if self["SupportingRED"].wait_for_visible(timeout=5, raise_error=True):
            self["SupportingRED"].click()
            time.sleep(3)
    def ClickSelfProfileNonprofitDeleteBtn(self):
        if self["SupportingREDDelete"].wait_for_visible(timeout=5, raise_error=True):
            self["SupportingREDDelete"].click()
            time.sleep(3)

    def ClickSelfProfileTipsBtn(self):
        if self["tips"].wait_for_visible(timeout=5, raise_error=True):
            self["tips"].click()
            time.sleep(3)

    def ClickSelfProfileLiveEventBtn(self):
        if self["live_event"].wait_for_visible(timeout=5, raise_error=True):
            self["live_event"].click()
            time.sleep(3)

    def CheckSelfProfileQABtn(self):
        return self["Q&A"].wait_for_visible(timeout=5, raise_error=True)

    def ClickSelfProfileQABtn(self):
        if self["Q&A"].wait_for_visible(timeout=5, raise_error=True):
            self["Q&A"].click()
            time.sleep(3)

    def ClickSelfProfileGetquoteBtn(self):
        if self["getquote"].wait_for_visible(timeout=5, raise_error=True):
            self["getquote"].click()
            time.sleep(3)

    def CheckSelfProfilegetquoteBtn(self):
        return self["getquote"].wait_for_visible(timeout=5, raise_error=True)

    def ClickSelfProfileUserhead(self):
        if self["selfprofile_userhead"].wait_for_visible(timeout=5, raise_error=True):
            self["selfprofile_userhead"].click()
            time.sleep(3)

    def GetProfileBioText(self):
        if self["profile_bio_btn"].wait_for_visible(timeout=5, raise_error=False):
            return self["profile_bio_btn"].text
        elif self["profile_bio_platform_btn"].wait_for_visible(timeout=5, raise_error=False):
            return self["profile_bio_platform_btn"].text
        else:
            self["profile_bio_platform_btn1"].wait_for_visible(timeout=5)
            return self["profile_bio_platform_btn1"].text

    def ClickProfileEmailBtn(self):
        if self["profile_email_btn"].wait_for_visible(timeout=5, raise_error=False):
            self["profile_email_btn"].click()
            # time.sleep(1)
        elif self["profile_email_btn1"].wait_for_visible(timeout=5, raise_error=True):
            self["profile_email_btn1"].click()
            # time.sleep(1)

    def CheckProfileEmailBtn(self):
        if self["profile_email_btn"].wait_for_visible(timeout=5, raise_error=False):
            return self["profile_email_btn"].wait_for_visible(timeout=5, raise_error=False)
        elif self["profile_email_btn1"].wait_for_visible(timeout=5, raise_error=True):
            return self["profile_email_btn1"].wait_for_visible(timeout=5, raise_error=False)

    def ClickProfileLikeBtn(self):
        self["liek_btn"].click()
        time.sleep(3)

    def GetProfileLikePopupText(self):
        return self["liek_popup_text"].text

    def CheckunBlockBtn(self):
        return self["unblock_btn"].wait_for_visible(timeout=5, raise_error=False)

    def CheckPrfoleQrCodeButton(self):
        return self["profile_qr_code_button"].wait_for_visible(timeout=5, raise_error=False)

    def ClickPrfoleQrCodeButton(self):
        self["profile_qr_code_button"].click()
        time.sleep(3)

    def get_profilepage_user_name(self):
        value = self["profilepage_user_name"].text
        return value[1:]

    def check_half_size_username(self):
        return self["half_size_username"].text

    def click_bell_icon(self):
        self["bell_icon"].click()
        time.sleep(3)

    def get_other_page_nickname(self):
        return self["other_page_nickname"].text

    def click_like_tab_video(self):
        self["like_tab_video"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["like_tab_video"].click()
        time.sleep(3)

    def click_like_tab(self):
        self["like_tab"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["like_tab"].click()
        time.sleep(3)

    def check_sns_button(self):
        return self["sns_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_live_event_button(self):
        return self["live_event_button"].wait_for_existing(timeout=5, raise_error=False)

    def get_block_large_text(self):
        return self["block_large_text"].text

    def get_block_small_text(self):
        return self["block_small_text"].text

    def click_block_button(self):
        if self["block_button1"].wait_for_existing(timeout=5, raise_error=False):
            self["block_button1"].click()
            time.sleep(3)
        elif self["block_button"].wait_for_existing(timeout=5, raise_error=True):
            self["block_button"].click()
            time.sleep(3)

    def click_more_button(self):
        self["more_button"].click()
        time.sleep(3)

    def check_visitor_button(self):
        return self["visitor_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_settings_and_privacy(self):
        self["settings_and_privacy"].click()
        time.sleep(3)

    def click_settings_button(self):
        if self["settings_button"].wait_for_existing(timeout=5, raise_error=False):
            self["settings_button"].click()
            time.sleep(3)
        elif self["settings_button1"].wait_for_existing(timeout=5, raise_error=False):
            self["settings_button1"].click()
            time.sleep(3)
        elif self["settings_button2"].wait_for_existing(timeout=5, raise_error=False):
            self["settings_button2"].click()
            time.sleep(3)

    def check_settings_button(self):
        if self["settings_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["settings_button"].wait_for_existing(timeout=5, raise_error=False)
        elif self["settings_button1"].wait_for_existing(timeout=5, raise_error=False):
            return self["settings_button1"].wait_for_existing(timeout=5, raise_error=False)
        elif self["settings_button2"].wait_for_existing(timeout=5, raise_error=False):
            return self["settings_button2"].wait_for_existing(timeout=5, raise_error=False)

    def click_block_confirm_button(self):
        self["block_confirm_button"].click()
        time.sleep(3)

    def click_profilepage_follower_button(self):
        self["profilepage_follower_button"].click()
        time.sleep(3)

    def click_profilepage_following_button(self):
        if self["profilepage_following_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["profilepage_following_button"].click()
            time.sleep(3)
        elif self["profilepage_following_button1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["profilepage_following_button1"].click()
            time.sleep(3)
        elif self["profilepage_following_button2"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["profilepage_following_button2"].click()
            time.sleep(3)
        elif self["profilepage_following_button3"].wait_for_visible(timeout=5, interval=0.5, raise_error=True):
            self["profilepage_following_button3"].click()
            time.sleep(3)

    def check_profilepage_following_button(self):
        if self["profilepage_following_button"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            return self["profilepage_following_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        elif self["profilepage_following_button1"].wait_for_existing(timeout=5, interval=0.5, raise_error=True):
            return self["profilepage_following_button1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def check_profilepage_following_button1(self):
        if self["profilepage_following_button3"].wait_for_existing(timeout=5, interval=0.5, raise_error=False):
            return self["profilepage_following_button3"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)
        elif self["profilepage_following_button4"].wait_for_existing(timeout=5, interval=0.5, raise_error=True):
            return self["profilepage_following_button4"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def get_message_text(self):
        return self["message_text"].text

    def click_back_button(self):
        if self["back_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["back_button"].click()
            time.sleep(3)
        elif self["back_button1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["back_button1"].click()
            time.sleep(3)

    def click_follow_btn(self):
        if self["follow_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["follow_btn"].click()
            time.sleep(3)
        else:
            self["follow_btn1"].click()
            time.sleep(3)


    def click_unfollow_btn(self):
        self["unfollow_btn"]\
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["unfollow_btn"].click()
        time.sleep(3)

    def click_following_button(self):
        self["following_button"] \
            .wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["following_button"].click()
        time.sleep(3)

    def get_following_count(self):
        return self["following_button"].text

    def get_follow_text(self):
        return self["follow_btn"].text

    def click_nickname(self):
        self["nickname"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["nickname"].click()
        time.sleep(3)

    def get_nickname_text(self):
        return self["nickname"].text

    def get_other_page_username(self):
        user_name = self["other_page_username"].text
        time.sleep(3)
        if user_name[0] == "@":
            return user_name[1:]
        else:
            return user_name

    def get_switch_account(self):
        return self["switch_account"].text

    def click_find_friend(self):
        if self["find_friend"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["find_friend"].click()
            time.sleep(3)

    def click_find_friend1(self):
        if self["find_friend1"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["find_friend1"].click()
            time.sleep(3)

    def click_profile_header(self):
        if self["profile_header_image"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["profile_header_image"].click()
            time.sleep(3)
        else:
            self["profile_header_image1"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
            self["profile_header_image1"].click()
            time.sleep(3)

    def click_url_path(self):
        self["url_path"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["url_path"].click()
        time.sleep(3)

    def click_edit_profile(self):
        if self["edit_profile"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["edit_profile"].click()
            time.sleep(3)
        else:
            self["edit_profile1"].click()
            time.sleep(3)

    def check_edit_profile(self):
        return self["edit_profile"].wait_for_existing(timeout=5, raise_error=False)

    def check_update_header(self):
        return self["update_header"].wait_for_existing(timeout=5, raise_error=False)

    def click_update_header(self):
        if self["update_header"].wait_for_existing(timeout=5, raise_error=True):
            self["update_header"].click()

    def get_bio_text(self):
        self["bio_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        return self["bio_text"].text

    def check_bio(self):
        return self["bio_text"].wait_for_existing(timeout=5, raise_error=False)

class FollowingUser(Control):
    def get_locators(self):
        return {
            "nickname": {"path":  UPath(id_ == "txt_user_name")}
        }

class FollowingUserList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout", visible_ == True)/1/0
    elem_class = FollowingUser

class FollowingPermissionUpath(Control):
    def get_locators(self):
        return {
            "delete_btn": {"path": UPath(id_ == "permission_delete_btn", visible_ == True)}
        }

class FollowingPermission(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout", visible_ == True) / UPath(id_ == "permission_delete_btn", visible_ == True)
    elem_class = FollowingPermissionUpath

class Following_list(BasePanel):
    window_spec = {
        "activity":"com.ss.android.ugc.aweme.following.ui.FollowRelationTabActivity"}

    def get_locators(self):
        return {
            "following_title": {"type": Control, "path": UPath(id_ == "title_tv", visible_ == True)},
            "back": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "search_box": {"type": Control, "path": UPath(id_ == 'et_search_kw')},
            "following_user_head": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/4/UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarImageView')},
            "following_nickname": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/4/UPath(id_ == 'txt_user_name')},
            "following_username": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/4/UPath(id_ == 'txt_desc')},
            "following_user_head0": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 4 / UPath(id_ == "iv_avatar") / 0},
            #关注列表无授权模块第一个用户
            "following_user_head_no": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / UPath(type_ == "LinearLayout", index=0) / UPath(type_ == "SmartAvatarImageView")},
            "following_user_head_no1": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 1 / UPath(type_ == "SmartAvatarImageView", depth=6)},
            #关注列表无授权模块第二个用户
            "following_user_head_two": {"type": Control, "path": UPath(text_ == "uixiao001410")},
            "user_head0": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/4/UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarImageView')},
            "user_head1": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/5/UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarImageView')},
            "user_head2": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/6/UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarImageView')},
            "user_name_no": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 1 / UPath(id_ == "txt_desc")},
            "nick_name_no": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 1 / UPath(id_ == "txt_user_name")},
            "follower_page_head1": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / UPath(type_ == "LinearLayout", index=1) / UPath(type_ == "SmartAvatarImageView", depth=6)},
            "following_list_nick_name": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 1 / 1 / UPath(type_ == "LinearLayout") / UPath(id_ == "txt_user_name")},
            "profilepage_following_button": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/0/UPath(id_ == 'relationBtn')},
            "following_list_following_button": {"type": Control, "path": UPath(id_ == "relationBtn", text_ == "Following")},
            "cancel_following_list_following_button": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 3 / UPath(id_ == "relationBtn", visible_ == True)},
            "following_page_friends_button": {"type": Control, "path": UPath(text_ == "Friends")},
            "live_button": {"type": Control, "path": UPath(type_ == "RecyclerView", visible_ == True) / UPath(type_ == "LinearLayout", index=0) / UPath(type_ == "TuxIconView", visible_ == True)},
            "following_tab": {"type": Control, "path": UPath(type_ == 'com.bytedance.ies.dmt.ui.widget.tablayout.DmtTabLayout$f')/0/UPath(id_ == 'text1')},
            "following_tab1": {"type": Control, "path": UPath(type_ == "T1t") / UPath(type_ == "T1u", index=0) / UPath(id_ == "text1")},
            "privacy_large_title": {"type": Control, "path": UPath(id_ == 'status_view_flex_layout')/UPath(id_ == 'title_tv')},
            "privacy_Small_title": {"type": Control, "path": UPath(id_ == 'message_tv')},
            "follower_user_head": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/1/UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarImageView')},
            "follower_user_head1": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 0 / UPath(type_ == "SmartAvatarImageView", depth=6)},
            "follower_user_head0": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 0 / UPath(id_ == "iv_avatar") / 0},
            "follower_username": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/0/UPath(id_ == 'txt_user_name')},
            "follower_nickname": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/0/UPath(id_ == 'txt_desc')},
            "follower_follow_back_button": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / UPath(type_ == "LinearLayout", index=0) / UPath(id_ == "relationBtn")},
            "follower_friends_back_button": {"type": Control, "path": UPath(text_ == 'Friends')},
            "follower_more_button": {"type": Control, "path": UPath(id_ == 'rv_list', visible_ == True)/0/UPath(id_ == 'more_iv')},
            "following_list_contact_delete": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / UPath(type_ == "LinearLayout", index=0) / UPath(id_ == "permission_delete_btn")},
            "no_new": {"type": Control, "path": UPath(id_ == "button", visible_ == True)},
            "following_user_list": {"type": FollowingUserList, "path": UPath(id_ == "rv_list", visible_ == True)},
            "following_username_one": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 1 / 1 / UPath(id_ == "txt_desc")},
            "following_page_contact_find": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / UPath(type_ == "LinearLayout", index=0) / UPath(id_ == "find_text_view")},
            "following_page_facebook_find": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / UPath(type_ == "LinearLayout", index=1) / UPath(id_ == "find_text_view")},
            "permissios_alone": {"type": Control, "path": UPath(id_ == "permission_delete_btn", visible_ == True)},
            "contact_popup_ok": {"type": Control, "path": UPath(text_ == "OK")},
            "check_permission_delete_btn": {"type": FollowingPermission, "path": UPath(id_ == "rv_list", visible_ == True)},
            #粉丝列表
            "follower_list_5_relationBtn": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 4 / UPath(id_ == "relationBtn")},
            "follower_list_4_relationBtn": {"type": Control, "path": UPath(id_ == "rv_list", visible_ == True) / 3 / UPath(id_ == "relationBtn")},
            "requested_ok": {"type": Control, "path": UPath(id_ == "button1", visible_ == True)},
        }
    def ClickFollowerListRequestedOKBtn(self):
        if self["requested_ok"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["requested_ok"].click()
            time.sleep(3)

    def ClickFollwersListRelationBtn5(self):
        self["follower_list_5_relationBtn"].click()
        time.sleep(3)

    def ClickFollwersListRelationBtn4(self):
        self["follower_list_4_relationBtn"].click()
        time.sleep(3)

    def GetFollwersListRelationBtn5(self):
        return self["follower_list_5_relationBtn"].text

    def GetFollwersListRelationBtn4(self):
        return self["follower_list_4_relationBtn"].text

    def get_first_nickname(self):
        list = []
        for item in self["following_user_list"].items():
            list.append(item["nickname"].text)
        return list

    def get_first_username(self):
        return self["following_username_one"].text

    def get_first_permission_delete(self):
        list = []
        for item in self["check_permission_delete_btn"].items():
            list.append(str(item["delete_btn"].existing))
        return list

    def ClickContactsDeleteButton(self):
        for item in self["check_permission_delete_btn"].items():
            item["delete_btn"].click()
            time.sleep(3)
            break
    #contact和facebook Ok按钮
    def check_popup_ok(self):
        return self["contact_popup_ok"].wait_for_existing(timeout=5, raise_error=False)

    #点击following页面contact find按钮
    def click_following_page_contact_find(self):
        if self["following_page_contact_find"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["following_page_contact_find"].click()

    #点击following页面facebook find按钮
    def click_following_page_facebook_find(self):
        if self["following_page_facebook_find"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["following_page_facebook_find"].click()

    #获取关注列表的标题
    def get_following_title(self):
        return self["following_title"].text

    #点击关注列表页面第一个用户
    def click_following_user_head0(self):
        if self["following_user_head_no"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["following_user_head_no"].click()
            time.sleep(6)
        elif self["following_user_head0"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["following_user_head0"].click()
            time.sleep(6)
        else:
            self["following_user_head_no1"].click()
            time.sleep(6)

    #点击关注列表页面无授权cell第二个用户
    def ClickFollowingListNoPermissionTwouser(self):
        self["following_user_head_two"].click()
        time.sleep(3)

    #检查关注列表无授权卡片第一个用户
    def check_following_user_head0_no(self):
        if self["following_user_head_no"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            return self["following_user_head_no"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        else:
            return self["following_user_head_no1"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)

    #检查关注列表页面第一个用户存在
    def check_following_user_head0(self):
        return self["following_user_head0"].wait_for_existing(timeout=5, raise_error=False)

    #关注列表无网络时
    def check_no_new(self):
        print(str(self["no_new"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)))
        if self["no_new"].wait_for_visible(timeout=5, interval=0.5, raise_error=False):
            self["no_new"].click()
            time.sleep(3)

    #删除关注列表contact授权
    def click_following_list_contact_delete(self):
        if self["following_list_contact_delete"].wait_for_existing(timeout=5, raise_error=False):
            self["following_list_contact_delete"].click()
            time.sleep(3)
    #检查是否存在两个授权cell
    def check_permission_cell(self):
        return self["following_list_contact_delete"].wait_for_existing(timeout=5, raise_error=False)

    #检查是否存在一个授权cell
    def check_permissios_alone(self):
        return self["permissios_alone"].wait_for_existing(timeout=5, raise_error=False)

    #删除关注列表contact授权
    def click_permissios_alone(self):
        if self["permissios_alone"].wait_for_existing(timeout=5, raise_error=False):
            self["permissios_alone"].click()
            time.sleep(3)

    def click_back(self):
        self["back"].click()
        time.sleep(2)

    def checK_follower_more_button(self):
        return self["follower_more_button"].wait_for_existing(timeout=5, raise_error=False)

    def get_follower_friends_back_button(self):
        return self["follower_friends_back_button"].text

    def get_follower_follow_back_button(self):
        return self["follower_follow_back_button"].text

    def get_follower_nickname(self):
        return self["follower_nickname"].text

    def CheckFollowerNickname(self):
        return self["follower_nickname"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def get_follower_username(self):
        return self["follower_username"].text

    def CheckFollowerUsername(self):
        return self["follower_username"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def check_follower_user_head(self):
        if self["follower_user_head"].wait_for_existing(timeout=5, raise_error=False):
            return self["follower_user_head"].wait_for_existing(timeout=5, raise_error=False)
        else:
            return self["follower_user_head1"].wait_for_existing(timeout=5, raise_error=False)

    def get_following_list_nickname(self):
        return self["following_list_nick_name"].text

    def click_following_button(self):
        self["profilepage_following_button"].click()
        time.sleep(3)

    def check_search_box(self):
        return self["search_box"].wait_for_existing(timeout=5, raise_error=False)

    def check_following_user_head(self):
        return self["user_head0"].wait_for_existing(timeout=5, raise_error=False)

    def check_following1_user_head(self):
        return self["following_user_head"].wait_for_existing(timeout=5, raise_error=False)

    def click_follower_user_head(self):
        """粉丝列表点击chentest001头像"""
        if self["follower_user_head"].wait_for_existing(timeout=5, raise_error=False):
            self["follower_user_head"].click()
            time.sleep(3)
        else:
            self["follower_user_head1"].click()
            time.sleep(3)

    def click_follower_user_head0(self):
        """粉丝列表点击第一个头像"""
        self["follower_user_head0"].click()
        time.sleep(3)

    def check_following_nickneme(self):
        return self["nick_name_no"].wait_for_existing(timeout=5, raise_error=False)

    def check_following_userneme(self):
        return self["user_name_no"].wait_for_existing(timeout=5, raise_error=False)

    # def click_following_user_head0(self):
    #     self["user_head0"].click()
    #     time.sleep(3)

    def click_following_user_head01(self):
        self["user_head1"].click()
        time.sleep(3)

    def click_following_user_head1(self):
        self["follower_page_head1"].click()
        time.sleep(3)

    def click_following_user_head2(self):
        """关注列表点击12341833171a0"""
        self["user_head2"].click()
        time.sleep(3)

    def check_following_user_head2(self):
        return self["user_head2"].wait_for_existing(timeout=5, raise_error=False)

    def click_follower_user_head1(self):
        self["follower_page_head1"].click()
        time.sleep(3)

    def check_user_name(self):
        return self["user_name"].wait_for_existing(timeout=5, raise_error=False)

    def check_nick_name(self):
        return self["nick_name"].wait_for_existing(timeout=5, raise_error=False)

    def check_following_list_following_button(self):
        return self["following_list_following_button"].wait_for_existing(timeout=5, raise_error=False)

    def CheckCancelFollowingListFollowingBtn(self):
        return self["cancel_following_list_following_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_cancel_following_list_following_button(self):
        self["cancel_following_list_following_button"].click()
        time.sleep(3)

    def check_friends_button(self):
        return self["following_page_friends_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_live_button(self):
        return self["live_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_following_tab(self):
        if self["following_tab"].wait_for_existing(timeout=5, raise_error=True):
            self["following_tab"].click()
            time.sleep(3)
        elif self["following_tab1"].wait_for_existing(timeout=5, raise_error=True):
            self["following_tab1"].click()
            time.sleep(3)

    def get_privacy_large_title(self):
        return self["privacy_large_title"].text

    def get_privacy_Small_title(self):
        return self["privacy_Small_title"].text

class AddFriendsPageRecommendRelation(Control):
    def get_locators(self):
        return {
            "relationbtn": {"path": UPath(id_ == "relationBtn")},
        }

class AddFriendsPageRecommendRelationBtnList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "relationBtn")
    elem_class = AddFriendsPageRecommendRelation

class AddFriendsPageRecommendDelete(Control):
    def get_locators(self):
        return {
            "delete": {"path": UPath(id_ == "deleteIconView")},
        }

class AddFriendsPageRecommendDeleteBtnList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "deleteIconView")
    elem_class = AddFriendsPageRecommendDelete

class AddFriendsPageRecommendUserInfo(Control):
    def get_locators(self):
        return {
            "user_head": {"path": UPath(id_ == "avatar_view")},
        }

class AddFriendsPageRecommendUserInfoList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "start_area_layout")
    elem_class = AddFriendsPageRecommendUserInfo

class AddFriendsPageRecommendUsername(Control):
    def get_locators(self):
        return {
            "username": {"path": UPath(id_ == "nickNameView")},
            "relation_label": {"path": UPath(id_ == "relation_label")},
            "video_label": {"path": UPath(id_ == "video_reason")},
        }

class AddFriendsPageRecommendUsernameList(Control):
    elem_path = UPath(type_ == "android.widget.LinearLayout") / UPath(id_ == "middle_area_layout")
    elem_class = AddFriendsPageRecommendUsername

class New_find_friends(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.relation.ffp.ui.FindFriendsPageActivity'}

    def get_locators(self):
        return{
            "find_friend_title": {"type": Control, "path": UPath(id_ == "nav_bar_title")},
            "go_back_button": {"type": Control, "path": UPath(id_ == "nav_start") / UPath(type_ == "TuxIconView")},
            "code_button": {"type": Control, "path": UPath(id_ == "nav_end") / UPath(type_ == "TuxIconView")},
            "seach_box": {"type": Control, "path": UPath(id_ == "edit_text")},
            "search_edit_text": {"type": Control, "path": UPath(id_ == 'search_edit_text', visible_ == True)},
            "seach_box_cancel": {"type": Control, "path": UPath(id_ == "cancel_tv")},
            "seach_box_user_list": {"type": Control, "path": UPath(id_ == "search_list") / UPath(type_ == "LinearLayout", index=0) / UPath(type_ == "SmartAvatarBorderView")},
            "null_facebook": {"type": Control, "path": UPath(id_ == "fb_auth_btn")},
            "null_contact": {"type": Control, "path": UPath(id_ == "contact_auth_btn")},
            "invite_text": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "RelativeLayout", index=2) / UPath(id_ == "title_tv")},
            "contact_find_text": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "connect_btn")},
            "facebook_find_text": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "RelativeLayout", index=1) / UPath(id_ == "connect_btn")},
            "facebook_popup_ok_button": {"type": Control, "path": UPath(text_ == "OK")},
            "suggested_account_text": {"type": Control, "path": UPath(id_ == "recommend_user_title")},
            "suggested_account_i_button": {"type": Control, "path": UPath(id_ == "info_icon_view")},
            "suggested_account_i_content": {"type": Control, "path": UPath(id_ == "tv_suggest")},
            "suggested_account_head": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "ConstraintLayout", index=0) / UPath(id_ == "avatar_view")},
            "suggested_account_delete_button": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "ConstraintLayout", index=0) / UPath(id_ == "deleteIconView")},
            "suggested_account_nickname": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "ConstraintLayout", index=0) / UPath(id_ == "nickNameView")},
            "suggested_account_reason": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "ConstraintLayout", index=0) / UPath(id_ == "reasonView")},
            "suggested_account_follow_buttoon": {"type": Control, "path": UPath(id_ == "power_list") / UPath(type_ == "ConstraintLayout", index=0) / UPath(id_ == "relationBtn")},
            "suggested_account_follow_back_buttoon": {"type": Control, "path": UPath(text_ == "Follow back")},
            "bottom_contact_find_text": {"type": Control, "path": UPath(id_ == 'empty_title')},
            "collection_user": {"type": Control, "path": UPath(id_ == 'search_list')/1/UPath(id_ == 'search_common_user_rl')},
            "collection_user_v0": {"type": Control, "path": UPath(id_ == 'search_list')/0/UPath(id_ == 'search_common_user_rl')},
            "recommend_relation_list": {"type": AddFriendsPageRecommendRelationBtnList, "path": UPath(id_ == 'power_list')},
            "recommend_delete_list": {"type": AddFriendsPageRecommendDeleteBtnList, "path": UPath(id_ == 'power_list')},
            "recommend_userinfo_list": {"type": AddFriendsPageRecommendUserInfoList, "path": UPath(id_ == 'power_list')},
            "recommend_username_list": {"type": AddFriendsPageRecommendUsernameList, "path": UPath(id_ == 'power_list')},
        }
    def GetRecommendInfoContent(self):
        return self["suggested_account_i_content"].text

    def GetRecommendUserVideoLabel(self):
        list = []
        for item in self["recommend_username_list"].items():
            list.append(str(item["video_label"].visible))
        self.app.testcase.log_info(list)
        return list

    def ClickRecommendUserVideoLabel(self,value=0):
        self["recommend_username_list"].items()[value].click()
        time.sleep(3)

    def ClickRecommendUserhead(self):
        self["recommend_userinfo_list"].items()[0].click()
        time.sleep(3)

    def ClickRecommendUsername(self):
        self["recommend_username_list"].items()[0].click()
        time.sleep(3)

    def ClickRecommendUserLabel(self):
        self["recommend_username_list"].items()[0].click()
        time.sleep(3)

    def ClickRecommendRelationBtn(self, value=0):
        self["recommend_relation_list"].items()[value].click()
        time.sleep(3)

    def ClickRecommendDeleteBtn(self,value=0):
        self["recommend_delete_list"].items()[value].click()
        time.sleep(3)

    def GetRecommendRelationBtnText(self):
        list = []
        for item in self["recommend_relation_list"].items():
            list.append(str(item["relationbtn"].text))
        self.app.testcase.log_info(list)
        return list

    def GetRecommendDelete(self):
        list = []
        for item in self["recommend_delete_list"].items():
            list.append(str(item["delete"].visible))
        self.app.testcase.log_info(list)
        return list

    def GetRecommendUserInfo(self):
        list = []
        for item in self["recommend_userinfo_list"].items():
            list.append(str(item["user_head"].visible))
        self.app.testcase.log_info(list)
        return list

    def GetRecommendUsername(self):
        list = []
        for item in self["recommend_username_list"].items():
            list.append(str(item["username"].text))
        self.app.testcase.log_info(list)
        return list

    def GetRecommendUserLabel(self):
        list = []
        for item in self["recommend_username_list"].items():
            list.append(str(item["relation_label"].text))
        self.app.testcase.log_info(list)
        return list

    def check_null_contact(self):
        return self["null_contact"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def check_null_facebook(self):
        return self["null_facebook"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def check_contact_find_button(self):
        return self["contact_find_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def check_facebook_find_button(self):
        return self["facebook_find_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def ocr_click(self, device, text):
        time.sleep(5)
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)

    def get_title_text(self):
        if self["find_friend_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["find_friend_title"].text

    def check_go_back_button(self):
        return self["go_back_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_go_back_button(self):
        if self["go_back_button"].wait_for_existing(timeout=5, raise_error=False):
            self["go_back_button"].click()
            time.sleep(3)

    def check_code_button(self):
        return self["code_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_seach_box(self):
        return self["seach_box"].wait_for_existing(timeout=5, raise_error=False)

    def click_seach_box(self):
        self["seach_box"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["seach_box"].click()
        time.sleep(3)

    def input_friends_name(self, content):
        if self["search_edit_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=True):
            self["search_edit_text"].input(content)
        time.sleep(5)

    def check_seach_box_cancel(self):
        return self["seach_box_cancel"].wait_for_existing(timeout=5, raise_error=False)

    def check_seach_box_user_list(self):
        return self["seach_box_user_list"].wait_for_existing(timeout=5, raise_error=False)

    def check_invite_text(self):
        self["invite_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["invite_text"].text

    def click_invite_button(self):
        self["invite_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["invite_text"].click()
        time.sleep(3)

    def check_contact_find_text(self):
        self["contact_find_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["contact_find_text"].text

    def click_contact_button(self):
        self["contact_find_text"].click()
        time.sleep(3)

    def check_facebook_find_text(self):
        self["facebook_find_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["facebook_find_text"].text

    def click_facebook_button(self):
        self["facebook_find_text"].click()
        time.sleep(3)

    def check_facebook_popup_ok_button(self):
        return self["facebook_popup_ok_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_facebook_popup_ok_button(self):
        self["facebook_popup_ok_button"].click()
        time.sleep(6)

    def check_suggested_account_text(self):
        self["suggested_account_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggested_account_text"].text

    def check_suggested_account_i_button(self):
        return self["suggested_account_i_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_suggested_account_i_button(self):
        if self["suggested_account_i_button"].wait_for_existing(timeout=5):
            self["suggested_account_i_button"].click()
            time.sleep(3)

    def check_suggested_account_head(self):
        return self["suggested_account_head"].wait_for_existing(timeout=5, raise_error=False)

    def click_suggested_account_head(self):
        self["suggested_account_head"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["suggested_account_head"].click()
        time.sleep(3)

    def check_suggested_account_nickname(self):
        return self["suggested_account_nickname"].wait_for_existing(timeout=5, raise_error=False)

    def get_suggested_account_reason(self):
        self["suggested_account_reason"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggested_account_reason"].text

    def get_suggested_account_follow_buttoon(self):
        self["suggested_account_follow_buttoon"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggested_account_follow_buttoon"].text

    def get_suggested_account_follow_back_buttoon(self):
        if self["suggested_account_follow_back_buttoon"].wait_for_existing(timeout=5, raise_error=False):
            return self["suggested_account_follow_back_buttoon"].text

    def check_suggested_account_delete_button(self):
        return self["suggested_account_delete_button"].wait_for_existing(timeout=5, raise_error=False)

    def get_bottom_contact_find_text(self):
        self["bottom_contact_find_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["bottom_contact_find_text"].text

    def check_bottom_contact_find_text(self):
        return self["bottom_contact_find_text"].wait_for_existing(timeout=5, raise_error=False)


class Find_friends(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.find.ui.FindFriendsActivity'}

    def get_locators(self):
        return{
            "find_friend_title": {"type": Control, "path": UPath(text_ == "Find friends")},
            "go_back_button": {"type": Control, "path": UPath(id_ == 'nav_start')/UPath(type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "code_button": {"type": Control, "path": UPath(id_ == 'nav_end')/UPath(type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "seach_box": {"type": Control, "path": UPath(id_ == 'et_search_box')},
            "seach_box_cancel": {"type": Control, "path": UPath(id_ == 'tv_search_cancel')},
            "seach_box_user_list": {"type": Control, "path": UPath(id_ == 'search_list')/0/UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarBorderView')},
            "invite_text": {"type": Control, "path": UPath(text_ == 'Invite')},
            "contact_find_text": {"type": Control, "path": UPath(id_ == 'channel_list')/1/UPath(id_ == 'button')},
            "facebook_find_text": {"type": Control, "path": UPath(id_ == 'channel_list')/2/UPath(id_ == 'button')},
            "facebook_popup_ok_button": {"type": Control, "path": UPath(type_ == 'com.bytedance.tux.widget.b')/0},
            "suggested_account_text": {"type": Control, "path": UPath(id_ == 'recommend_list')/1/UPath(id_ == 'title')},
            "suggested_account_i_button": {"type": Control, "path": UPath(id_ == 'tip')},
            "suggested_account_head": {"type": Control, "path": UPath(id_ == 'recommend_list')/2/UPath(id_ == 'avatarIv')},
            "suggested_account_delete_button": {"type": Control, "path": UPath(id_ == 'recommend_list')/2/UPath(id_ == 'blockIv')},
            "suggested_account_nickname": {"type": Control, "path": UPath(id_ == 'recommend_list')/2/UPath(id_ == 'mainTv')},
            "suggested_account_reason": {"type": Control, "path": UPath(id_ == 'recommend_list')/2/UPath(id_ == 'reasonTv')},
            "suggested_account_follow_buttoon": {"type": Control, "path": UPath(id_ == 'recommend_list')/2/UPath(id_ == 'relationBtn')},
            "suggested_account_follow_back_buttoon": {"type": Control, "path": UPath(id_ == 'recommend_list')/4/UPath(id_ == 'relationBtn')},
            "bottom_contact_find_text": {"type": Control, "path": UPath(id_ == 'empty_title')},

        }
    def ocr_click(self, device, text):
        time.sleep(5)
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)

    def get_title_text(self):
        if self["find_friend_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["find_friend_title"].text

    def check_go_back_button(self):
        return self["go_back_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_go_back_button(self):
        if self["go_back_button"].wait_for_existing(timeout=5, raise_error=False):
            self["go_back_button"].click()
            time.sleep(3)

    def check_code_button(self):
        return self["code_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_seach_box(self):
        return self["seach_box"].wait_for_existing(timeout=5, raise_error=False)

    def click_seach_box(self):
        self["seach_box"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["seach_box"].click()
        time.sleep(3)

    def check_seach_box_cancel(self):
        return self["seach_box_cancel"].wait_for_existing(timeout=5, raise_error=False)

    def check_seach_box_user_list(self):
        return self["seach_box_user_list"].wait_for_existing(timeout=5, raise_error=False)

    def check_invite_text(self):
        self["invite_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["invite_text"].text

    def click_invite_button(self):
        self["invite_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["invite_text"].click()
        time.sleep(3)

    def check_contact_find_text(self):
        self["contact_find_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["contact_find_text"].text

    def click_contact_button(self):
        self["contact_find_text"].click()
        time.sleep(3)

    def check_facebook_find_text(self):
        self["facebook_find_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["facebook_find_text"].text

    def click_facebook_button(self):
        self["facebook_find_text"].click()
        time.sleep(3)

    def check_facebook_popup_ok_button(self):
        return self["facebook_popup_ok_button"].wait_for_existing(timeout=5, raise_error=False)

    def click_facebook_popup_ok_button(self):
        self["facebook_popup_ok_button"].click()
        time.sleep(3)

    def check_suggested_account_text(self):
        self["suggested_account_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggested_account_text"].text

    def check_suggested_account_i_button(self):
        return self["suggested_account_i_button"].wait_for_existing(timeout=5, raise_error=False)

    def check_suggested_account_head(self):
        return self["suggested_account_head"].wait_for_existing(timeout=5, raise_error=False)

    def click_suggested_account_head(self):
        self["suggested_account_head"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["suggested_account_head"].click()
        time.sleep(3)

    def check_suggested_account_nickname(self):
        return self["suggested_account_nickname"].wait_for_existing(timeout=5, raise_error=False)

    def get_suggested_account_reason(self):
        self["suggested_account_reason"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggested_account_reason"].text

    def get_suggested_account_follow_buttoon(self):
        self["suggested_account_follow_buttoon"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggested_account_follow_buttoon"].text

    def get_suggested_account_follow_back_buttoon(self):
        self["suggested_account_follow_back_buttoon"].wait_for_existing(timeout=5, raise_error=False)
        return self["suggested_account_follow_back_buttoon"].text

    def check_suggested_account_delete_button(self):
        return self["suggested_account_delete_button"].wait_for_existing(timeout=5, raise_error=False)

    def get_bottom_contact_find_text(self):
        self["bottom_contact_find_text"].wait_for_existing(timeout=5, raise_error=False)
        return self["bottom_contact_find_text"].text

    def check_bottom_contact_find_text(self):
        return self["bottom_contact_find_text"].wait_for_existing(timeout=5, raise_error=False)

class New_invite_page(BasePanel):
    window_spec = {
        "activity":"com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "invite_page_title": {"type": Control, "path": UPath(id_ == "nav_bar_title")},
            "back_button": {"type": Control, "path": UPath(id_ == "nav_start") / UPath(type_ == "ImageView")},
            "ok": {"type": Control, "path": UPath(text_ == "OK")},
            "invite_via_title": {"type": Control, "path": UPath(id_ == "invite_channel_title")},
            "sms_button": {"type": Control, "path": UPath(text_ == "SMS")},
            "copy_link_button": {"type": Control, "path": UPath(text_ == "Copy link")},
            "all_friends_title": {"type": Control, "path": UPath(id_ == 'header_recycler_view')/UPath(type_ == 'com.bytedance.tux.input.TuxTextView')},
            "name": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "primary_area_tv")},
            "phone_number": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "second_area_tv")},
            "head": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "avatar_view")},
            "invite_button": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "invite_button")},
        }
    def get_invite_via(self):
        if self["invite_via_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_via_title"].text

    def get_sms_text(self):
        if self["sms_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["sms_button"].text

    def get_copy_link_button_text(self):
        if self["copy_link_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["copy_link_button"].text

    def get_all_friends_title(self):
        if self["all_friends_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["all_friends_title"].text

    def get_name_text(self):
        if self["name"].wait_for_existing(timeout=5, raise_error=False):
            return self["name"].text

    def get_phone_number_text(self):
        if self["phone_number"].wait_for_existing(timeout=5, raise_error=False):
            return self["phone_number"].text

    def check_head(self):
        return self["head"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def get_invite_button_text(self):
        if self["invite_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_button"].text

    def check_pop_ok(self):
        return self["ok"].wait_for_existing(timeout=5, raise_error=False)

    def click_ok(self):
        if self["ok"].wait_for_existing(timeout=5, raise_error=False):
            self["ok"].click()
            time.sleep(3)

    def get_invite_page_title(self):
        return self["invite_page_title"].text

    def click_back_button(self):
        if self["back_button"].wait_for_existing(timeout=5, raise_error=False):
            self["back_button"].click()
            time.sleep(3)


class Invite_page(BasePanel):
    window_spec = {
        "activity":"com.ss.android.ugc.aweme.friends.ui.InviteFriendsActivity"}

    def get_locators(self):
        return {
            "invite_page_title": {"type": Control, "path": UPath(id_ == 'title')},
            "back_button": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "ok": {"type": Control, "path": UPath(type_ == 'com.bytedance.tux.widget.b')/0},
            "invite_via_title": {"type": Control, "path": UPath(id_ == 'invite_via')},
            "sms_button": {"type": Control, "path": UPath(text_ == 'SMS')},
            "copy_link_button": {"type": Control, "path": UPath(text_ == 'Copy link')},
            "all_friends_title": {"type": Control, "path": UPath(id_ == 'header_recycler_view')/UPath(type_ == 'com.bytedance.tux.input.TuxTextView')},
            "name": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'txt_user_name')},
            "phone_number": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'txt_follow_info')},
            "head": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(type_ == 'com.ss.android.ugc.aweme.base.ui.SmartAvatarImageView')},
            "invite_button": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'btn_follow_user')},
        }
    def get_invite_via(self):
        if self["invite_via_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_via_title"].text

    def get_sms_text(self):
        if self["sms_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["sms_button"].text

    def get_copy_link_button_text(self):
        if self["copy_link_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["copy_link_button"].text

    def get_all_friends_title(self):
        if self["all_friends_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["all_friends_title"].text

    def get_name_text(self):
        if self["name"].wait_for_existing(timeout=5, raise_error=False):
            return self["name"].text

    def get_phone_number_text(self):
        if self["phone_number"].wait_for_existing(timeout=5, raise_error=False):
            return self["phone_number"].text

    def check_head(self):
        return self["head"].wait_for_existing(timeout=5, raise_error=False)

    def get_invite_button_text(self):
        if self["invite_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_button"].text

    def check_pop_ok(self):
        return self["ok"].wait_for_existing(timeout=5, raise_error=False)

    def click_ok(self):
        if self["ok"].wait_for_existing(timeout=5, raise_error=False):
            self["ok"].click()
            time.sleep(3)

    def get_invite_page_title(self):
        return self["invite_page_title"].text

    def click_back_button(self):
        if self["back_button"].wait_for_existing(timeout=5, raise_error=False):
            self["back_button"].click()
            time.sleep(3)

class New_contact_page(BasePanel):
    window_spec = {
        "activity":"com.ss.android.ugc.aweme.host.TikTokHostActivity"}


    def get_locators(self):
        return {
            "contact_page_title": {"type": Control, "path": UPath(id_ == "nav_bar_title")},
            "back_button": {"type": Control, "path": UPath(type_ == "TuxIconView")},
            "from_your_contacts_title": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(id_ == "root", index=0) / UPath(type_ == "TuxTextView")},
            "from_your_contacts_userhead": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "avatar_view")},
            "from_your_contacts_nickname": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "primary_area_tv")},
            "from_your_contacts_username": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "second_area_tv")},
            "from_your_contacts_follow_button": {"type": Control, "path": UPath(id_ == "follow_button")},
            "invite_to_tiktok_title": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(id_ == "root", index=1) / UPath(type_ == "TuxTextView")},
            "invite_to_tiktok_head": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=1) / UPath(id_ == "avatar_view")},
            "invite_to_tiktok_name": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=1) / UPath(id_ == "primary_area_tv")},
            "invite_to_tiktok_phone_number": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=1) / UPath(id_ == "second_area_tv")},
            "invite_to_tiktok_invite_button": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=1) / UPath(id_ == "invite_button")},
        }
    def get_from_your_contacts_title(self):
        if self["from_your_contacts_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_title"].text

    def check_from_your_contacts_userhead(self):
        return self["from_your_contacts_userhead"].wait_for_existing(timeout=5, raise_error=False)

    def get_from_your_contacts_nickname(self):
        if self["from_your_contacts_nickname"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_nickname"].text

    def get_from_your_contacts_username(self):
        if self["from_your_contacts_username"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_username"].text

    def get_from_your_contacts_follow_button(self):
        if self["from_your_contacts_follow_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_follow_button"].text

    def get_invite_to_tiktok_title(self):
        if self["invite_to_tiktok_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_title"].text

    def check_invite_to_tiktok_head(self):
        return self["invite_to_tiktok_head"].wait_for_existing(timeout=5, raise_error=False)

    def get_invite_to_tiktok_name(self):
        if self["invite_to_tiktok_name"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_name"].text

    def get_invite_to_tiktok_phone_number(self):
        if self["invite_to_tiktok_phone_number"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_phone_number"].text

    def get_invite_to_tiktok_invite_button(self):
        if self["invite_to_tiktok_invite_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_invite_button"].text

    def get_contact_page_title(self):
        return self["contact_page_title"].text

    def click_back_button(self):
        if self["back_button"].wait_for_existing(timeout=5, raise_error=False):
            self["back_button"].click()
            time.sleep(3)

class Contact_page(BasePanel):
    window_spec = {
        "activity":"com.ss.android.ugc.aweme.friends.ui.ContactsActivity"}

    def get_locators(self):
        return {
            "contact_page_title": {"type": Control, "path": UPath(id_ == 'title')},
            "back_button": {"type": Control, "path": UPath(id_ == 'back_btn')},
            "from_your_contacts_title": {"type": Control, "path": UPath(id_ == 'list_view')/0/UPath(id_ == 'tv_title')},
            "from_your_contacts_userhead": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'iv_avatar')},
            "from_your_contacts_nickname": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'txt_user_name')},
            "from_your_contacts_username": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'txt_second_name')},
            "from_your_contacts_follow_button": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'btn_follow_user')},
            "invite_to_tiktok_title": {"type": Control, "path": UPath(id_ == 'list_view')/2/UPath(id_ == 'tv_title')},
            "invite_to_tiktok_head": {"type": Control, "path": UPath(id_ == 'list_view')/3/UPath(id_ == 'iv_avatar')},
            "invite_to_tiktok_name": {"type": Control, "path": UPath(id_ == 'list_view')/3/UPath(id_ == 'txt_user_name')},
            "invite_to_tiktok_phone_number": {"type": Control, "path": UPath(id_ == 'list_view')/3/UPath(id_ == 'txt_second_name')},
            "invite_to_tiktok_invite_button": {"type": Control, "path": UPath(id_ == 'list_view')/3/UPath(id_ == 'btn_follow_user')},
        }
    def get_from_your_contacts_title(self):
        if self["from_your_contacts_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_title"].text

    def check_from_your_contacts_userhead(self):
        return self["from_your_contacts_userhead"].wait_for_existing(timeout=5, raise_error=False)

    def get_from_your_contacts_nickname(self):
        if self["from_your_contacts_nickname"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_nickname"].text

    def get_from_your_contacts_username(self):
        if self["from_your_contacts_username"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_username"].text

    def get_from_your_contacts_follow_button(self):
        if self["from_your_contacts_follow_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["from_your_contacts_follow_button"].text

    def get_invite_to_tiktok_title(self):
        if self["invite_to_tiktok_title"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_title"].text

    def check_invite_to_tiktok_head(self):
        return self["invite_to_tiktok_head"].wait_for_existing(timeout=5, raise_error=False)

    def get_invite_to_tiktok_name(self):
        if self["invite_to_tiktok_name"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_name"].text

    def get_invite_to_tiktok_phone_number(self):
        if self["invite_to_tiktok_phone_number"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_phone_number"].text

    def get_invite_to_tiktok_invite_button(self):
        if self["invite_to_tiktok_invite_button"].wait_for_existing(timeout=5, raise_error=False):
            return self["invite_to_tiktok_invite_button"].text

    def get_contact_page_title(self):
        return self["contact_page_title"].text

    def click_back_button(self):
        if self["back_button"].wait_for_existing(timeout=5, raise_error=False):
            self["back_button"].click()
            time.sleep(3)

class New_facebook_page(BasePanel):

    window_spec = {
        "activity":"com.ss.android.ugc.aweme.host.TikTokHostActivity"}

    def get_locators(self):
        return {
            "back_button": {"type": Control, "path": UPath(type_ == "TuxIconView")},
            "facebook_page_title": {"type": Control, "path": UPath(id_ == "nav_bar_title")},
            "facebook_friends_text": {"type": Control, "path": UPath(text_ == "Facebook friends")},
            "user_head0": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "avatar_view")},
            "nickname": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "primary_area_tv")},
            "username": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "second_area_tv")},
            "follow_button": {"type": Control, "path": UPath(type_ == "PowerList") / UPath(type_ == "RelativeLayout", index=0) / UPath(id_ == "follow_button")},
        }
    def click_back_button(self):
        self["back_button"].click()
        time.sleep(3)

    def get_facebook_page_title(self):
        return self["facebook_page_title"].text

    def get_facebook_friend_text(self):
        return self["facebook_friends_text"].text

    def check_user_head0(self):
        return self["user_head0"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def get_facebook_nickname(self):
        return self["nickname"].text

    def get_facebook_username(self):
        return self["username"].text

    def get_facebook_follow_button_text(self):
        return self["follow_button"].text


class Facebook_page(BasePanel):

    window_spec = {
        "activity":"com.ss.android.ugc.aweme.friends.ui.InviteUserListActivity"}

    def get_locators(self):
        return {
            "back_button": {"type": Control, "path": UPath(id_ == 'nav_start')/UPath(type_ == 'com.bytedance.tux.icon.TuxIconView')},
            "facebook_page_title": {"type": Control, "path": UPath(text_ == 'Facebook')},
            "facebook_friends_text": {"type": Control, "path": UPath(id_ == 'tv_title')},
            "user_head0": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'iv_avatar')},
            "nickname": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'txt_user_name')},
            "username": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'txt_second_name')},
            "follow_button": {"type": Control, "path": UPath(id_ == 'list_view')/1/UPath(id_ == 'btn_follow_user')},
        }
    def click_back_button(self):
        self["back_button"].click()
        time.sleep(3)

    def get_facebook_page_title(self):
        return self["facebook_page_title"].text

    def get_facebook_friend_text(self):
        return self["facebook_friends_text"].text

    def check_user_head0(self):
        return self["user_head0"].wait_for_existing(timeout=5, raise_error=False)

    def get_facebook_nickname(self):
        return self["nickname"].text

    def get_facebook_username(self):
        return self["username"].text

    def get_facebook_follow_button_text(self):
        return self["follow_button"].text

class invite_popup(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.friends.ui.InviteFriendsActivity'}

    def get_locators(self):
        return {
            "ok": {"type": Control, "path": UPath(type_ == 'com.bytedance.tux.widget.b')/0},
            "page_title": {"type": Control, "path": UPath(id_ == 'title')},
            "back_button": {"type": Control, "path": UPath(id_ == 'back_btn')},

        }
    def click_back(self):
        if self["back_button"].wait_for_existing(timeout=5, raise_error=False):
            self["back_button"].click()
            time.sleep(3)

    def click_ok(self):
        self["ok"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["ok"].click()
        time.sleep(3)

    def check_page_title(self):
        self["page_title"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        return self["page_title"].text






class Profile_h5_page(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.crossplatform.activity.CrossPlatformActivity|com.bytedance.hybrid.spark.page.SparkActivity'}

    def get_locators(self):
        return {
            "profile_h5_page_title": {"type": Control, "path": UPath(text_ == "Web Browser")},
            "cancel_btn": {"type": Control, "path": UPath(type_ == 'com.bytedance.hybrid.spark.page.SparkView')/0/1/UPath(type_ == 'com.lynx.component.svg.b')},
        }

    def get_title_text(self):
        return self["profile_h5_page_title"].text

    def click_cancel_btn(self):
        self["cancel_btn"].click()
        time.sleep(2)

    def ocr_click(self, device, text):
        cv_ = CV()
        pic_path = device.screenshot()
        result_pos = cv_.ocr_location(text=text, target_pic=pic_path)
        logger.info(result_pos)
        if result_pos["msg"] == "Failed":
            return False
        x = result_pos["result"][-1]["center_pos"][0]
        y = result_pos["result"][-1]["center_pos"][1]
        device.click(x, y)
        time.sleep(2)


class Balance(BasePanel):
    window_spec = {
        "activity": 'com.bytedance.hybrid.spark.page.SparkActivity'}

    def get_locators(self):
        return {
            "balance_v1": {"type": BalanceWebview, "path": UPath(type_ == "WebKitView")},
            "balance_v2": {"type": BalanceWebviewV2, "path": UPath(type_ == "WebKitView")},
            "balance_v3": {"type": BalanceWebviewV3, "path": UPath(type_ == "WebKitView")},
            "balance_v4": {"type": BalanceWebviewV4, "path": UPath(type_ == "WebKitView")},

        }


class BalanceWebview(Webview):
    view_spec = {"title": "Balance",
                 "use_inject": True,
                 }

    def get_locators(self):
        return {
            "get_coins": {"type": WebElement, "path": UPath(class_ == "text-color-ConstTextInverse4", visible_ == True)},
            "next_btn": {"type": WebElement, "path": UPath(class_ == "px-6", text_ == "Next")},
            "get_start_btn": {"type": WebElement, "path": UPath(class_ == "px-6", text_ == "Get started")},
        }

    def click_get_coins(self):
        self["get_coins"].click()
        time.sleep(5)

    def click_next_btn(self):
        for _ in Retry(limit=6, raise_error=False):
            if self["next_btn"].existing and self["next_btn"].visible:
                self["next_btn"].click()
                if self["get_start_btn"].existing and self["get_start_btn"].visible:
                    self["get_start_btn"].click()
            time.sleep(3)


class BalanceWebviewV2(Webview):
    view_spec = {
                 "url": "https://inapp.tiktokv.com/falcon/main/wallet/recharge"
                 }

    def get_locators(self):
        return {
            "top_up_currency": {"type": WebElement, "path": UPath(type_ == "DIV", text_ == "£0.07")},
            "contact_us": {"type": WebElement, "path": UPath(text_ == "Contact us", visible_ == True)},
        }

    def top_up_currency(self):
        self["top_up_currency"].click()
        time.sleep(5)

    def click_contact_us(self):
        self["contact_us"].click()
        time.sleep(5)


class BalanceWebviewV3(Webview):
    view_spec = {
                 "url": "https://feedback.tiktokv.com/feedback/im/chat?hide_nav_bar=1&entrance=TIKTOK_APP_LIVE_WALLET&enter_from=live_recharge&use_spark=1&error_code=4005192&hit_entry=balance_page_recharge&entry_type=recharge&pop_up_type=recharge_limit&enter_from=balance_page_recharge"
                 }

    def get_locators(self):
        return {
            "chat_with_us": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Chat with us")},
            "queued_state": {"type": WebElement, "path": UPath(class_ == "title-CCMFXz")},
            "top_right_icon": {"type": WebElement, "path": UPath(class_ == "more-xOAEvI") / UPath(type_ == "svg")},
            "end_chat": {"type": WebElement, "path": UPath(class_ == "text-sgZNBA", text_ == "End chat session")},
            "end_status": {"type": WebElement, "path": UPath(class_ == "adm-pull-to-refresh-content") / 3},
            "message_icon": {"type": WebElement, "path": UPath(class_ == "adm-text-area-element-hidden")},
            "send_btn":  {"type": WebElement, "path": UPath(id_ == "send_btn") / UPath(type_ == "path")},
            "message_text": {"type": WebElement, "path": UPath(class_ == "right-JAfnm9") / UPath(type_ == "SPAN")},
            "test_received": {"type": WebElement, "path": UPath(class_ == "left-H4cnDw")},
            "rate_experience": {"type": WebElement, "path": UPath(class_ == "btns-IMZDAY") / 2 / UPath(type_ == "path")},
            "select_reason": {"type": WebElement, "path": UPath(text_ == "Delayed response")},
            "submit_reason_status": {"type": WebElement, "path": UPath(class_ == "option-JreCbg", class_ == "selected-wxmYUT")},
            "support_tickets": {"type": WebElement, "path": UPath(class_ == "adm-popup-body") / 0 / UPath(class_ == "text-sgZNBA")},
            "im_box_title": {"type": WebElement, "path": UPath(class_ == "adm-nav-bar-title")},
        }

    def check_chat_with_us(self):
        return self["chat_with_us"].wait_for_existing(timeout=5, raise_error=False)

    def click_chat_with_us(self):
        self["chat_with_us"].click()
        time.sleep(5)

    def check_queued_state(self):
        return self["queued_state"].wait_for_existing(timeout=5, raise_error=False)

    def click_top_right_icon(self):
        self["top_right_icon"].click()
        time.sleep(3)
        self["end_chat"].click()
        time.sleep(3)

    def check_im_box_title(self):
        return self["im_box_title"].wait_for_existing(timeout=5, raise_error=False)

    def enter_to_feedback(self):
        self["top_right_icon"].click()
        time.sleep(3)
        self["support_tickets"].click()
        time.sleep(3)

    def check_end_status(self):
        return self["end_status"].wait_for_existing(timeout=5, raise_error=False)

    def rate_experience(self):
        self["rate_experience"].click()
        time.sleep(5)

    def check_rate_experience(self):
        return self["rate_experience"].wait_for_existing(timeout=5, raise_error=False)

    def select_reason(self):
        self["select_reason"].click()
        time.sleep(5)

    def check_submit_reason_status(self):
        return self["submit_reason_status"].wait_for_existing(timeout=5, raise_error=False)

    def check_test_received(self):
        return self["test_received"].wait_for_existing(timeout=5, raise_error=False)

    def click_message_icon(self):
        self["message_icon"].click()
        time.sleep(3)

    def send_message(self, text):
        self["message_icon"].input(text)
        time.sleep(2)
        self["send_btn"].click()
        time.sleep(3)

    def check_message_test(self):
        return self["message_text"].wait_for_existing(timeout=5, raise_error=False)


class BalanceWebviewV4(Webview):
    view_spec = {
                 "url": "https://feedback.tiktokv.com/feedback/im/chat?hide_nav_bar=1&entrance=TIKTOK_APP_INBOX&enter_from=inbox&use_spark=1"
                 }

    def get_locators(self):
        return {
            "share_icon": {"type": WebElement, "path": UPath(type_ == "BUTTON") / UPath(type_ == "SPAN")},
            "edit_box": {"type": WebElement, "path": UPath(class_ == "title-pxJHWQ")},
        }

    def click_share_icon(self):
        self["share_icon"].click()
        time.sleep(5)

    def check_edit_box(self):
        return self["edit_box"].wait_for_existing(timeout=5, raise_error=False)



class Feedback(BasePanel):
    window_spec = {
        "activity": 'com.bytedance.hybrid.spark.page.SparkActivity'}

    def get_locators(self):
        return {
            "feedback_webview": {"type": FeedbackWebview, "path": UPath(id_ == 'activity_container')},
            "feedback_webview_v2": {"type": FeedbackWebviewV2, "path": UPath(id_ == 'activity_container')},
            "feedback_webview_v3": {"type": FeedbackWebviewV3, "path": UPath(id_ == 'activity_container')},
            "feedback_webview_v4": {"type": FeedbackWebviewV4, "path": UPath(id_ == 'activity_container')},
            "feedback_webview_v5": {"type": FeedbackWebviewV5, "path": UPath(id_ == 'activity_container')},
            "feedback_webview_v6": {"type": FeedbackWebviewV6, "path": UPath(id_ == 'activity_container')},
        }


class FeedbackWebview(Webview):
    view_spec = {
        "url": "https://feedback.tiktokv.com/falcon/tiktok/feedback/main/index.html#/?hide_nav_bar=1"
    }

    def get_locators(self):
        return {
            "account_and_profile_label": {"type": WebElement, "path": UPath(text_ == "Account and profile", index=0)},
            "right_icon": {"type": WebElement, "path": UPath(class_ == "csp-header__right")},
            "submit_btn": {"type": WebElement, "path": UPath(class_ == "button_text-PHT0q")},
            "search_icon": {"type": WebElement, "path": UPath(type_ == "INPUT")},
        }

    def click_account_and_profile_label(self):
        self["account_and_profile_label"].click()
        time.sleep(2)

    def click_right_icon(self):
        self["right_icon"].click()
        time.sleep(5)

    def swipe_up(self):
        self.scroll(coefficient_y=0.8)
        time.sleep(2)

    def click_submit_btn(self):
        self["submit_btn"].click()
        time.sleep(2)

    def click_search_icon(self):
        self["search_icon"].click()
        time.sleep(2)


class FeedbackWebviewV2(Webview):
    view_spec = {"title": "TikTok",
                 "use_inject": True
    }

    def get_locators(self):
        return {
            "create_account_label": {"type": WebElement, "path": UPath(id_ == "7025833139687660038")},
            "feedback_title": {"type": WebElement, "path": UPath(class_ == "csp-header__title__text")},
            "return_btn": {"type": WebElement, "path": UPath(class_ == "csp-header__icon") / UPath(type_ == "path")},
        }

    def click_create_account_label(self):
        self["create_account_label"].click()
        time.sleep(5)

    def click_return_btn(self):
        self["return_btn"].click()
        time.sleep(3)

    def check_feedback_title(self):
        if self["feedback_title"].wait_for_existing(timeout=8, raise_error=False):
            return True


class FeedbackWebviewV3(Webview):
    view_spec = {
        "title": "TikTok",
        "use_inject": True
    }

    def get_locators(self):
        return {
            "no_btn": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "No")},
            "need_help_btn": {"type": WebElement, "path": UPath(class_ == "csp-cell__title")},
        }

    def click_no_btn(self):
        self.scroll(coefficient_y=0.8)
        if self["no_btn"].wait_for_existing(timeout=10, raise_error=False):
            self["no_btn"].click()
            time.sleep(2)
        self["need_help_btn"].click()
        time.sleep(5)


class FeedbackWebviewV4(Webview):
    view_spec = {
        "description": "\"attached\":true,\"empty\":false,\"height\":.*,\"visible\":true,\"width\":.*"
    }

    def get_locators(self):
        return {
            "content": {"type": WebElement, "path": UPath(type_ == "TEXTAREA", visible_ == True)},
            "submit_btn": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Submit")},
            "select_photo_icon": {"type": WebElement, "path": UPath(class_ == "upload-placeholder-3oiPv", text_ == "0/4")},
            "already_feedback_panel": {"type": WebElement, "path": UPath(class_ == "title-1Xlj7")},
            "search_icon": {"type": WebElement, "path": UPath(type_ == "INPUT")},
            "content_v1": {"type": WebElement, "path": UPath(class_ == "search-result-container-AjVVH") / 0 / 0},
            "report_title": {"type": WebElement, "path": UPath(class_ == "csp-header__title__text")},
        }

    def input_content(self, content):
        if self["already_feedback_panel"].wait_for_existing(timeout=3, raise_error=False):
            self["report_title"].click()
        if self["content"].wait_for_existing(timeout=3, raise_error=False):
            self["content"].input(content)
        time.sleep(2)
        for _ in Retry(limit=2, raise_error=False):
            if self["submit_btn"].wait_for_existing(timeout=3, raise_error=False):
                self["submit_btn"].click()

    def search_content(self, content):
        self["search_icon"].input(content)
        time.sleep(3)
        self["content_v1"].click()
        time.sleep(3)

    def input_content_with_attachment(self, content):
        if self["content"].wait_for_existing(timeout=3, raise_error=False):
            self["content"].input(content)
        time.sleep(2)
        # for _ in Retry(limit=2, raise_error=False):
        #     if self["submit_btn"].wait_for_existing(timeout=3, raise_error=False):
        #         self["submit_btn"].click()

    def add_photo(self):
        self["select_photo_icon"].click()
        time.sleep(2)



class FeedbackWebviewV5(Webview):

    view_spec = {
        "description": "\"attached\":true,\"empty\":false,\"height\":.*,\"visible\":true,\"width\":.*"
    }

    def get_locators(self):
        return {
            "feedback_title": {"type": WebElement, "path": UPath(class_ == "csp-header__title__text")},
            "reply_btn": {"type": WebElement, "path": UPath(type_ == "BUTTON", text_ == "Reply")},
            "feedback_list_v1": {"type": WebElement, "path": UPath(class_ == "session-item__title-KvE1m") / UPath(text_ == "Feedback test")},
            "select_photo_icon": {"type": WebElement, "path": UPath(class_ == "upload-placeholder-3oiPv", text_ == "0/4")},
            "no_btn": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "No")},
            "need_help_btn": {"type": WebElement, "path": UPath(class_ == "csp-cell__title")},
        }

    def check_feedback_title(self):
        if self["feedback_title"].wait_for_existing(timeout=8, raise_error=False):
            return True

    def click_reply_button(self):
        if self["reply_btn"].wait_for_existing(timeout=5, raise_error=False):
            self["reply_btn"].click()
        time.sleep(2)

    def click_feedback_list_v1(self):
        self["feedback_list_v1"].click()
        time.sleep(2)

    def add_photo(self):
        self["select_photo_icon"].click()
        time.sleep(2)

    def click_no_btn(self):
        self.scroll(coefficient_y=0.8)
        if self["no_btn"].wait_for_existing(timeout=10, raise_error=False):
            self["no_btn"].click()
            time.sleep(2)
        self["need_help_btn"].click()
        time.sleep(5)


class FeedbackWebviewV6(Webview):
    view_spec = {
        "description": "\"attached\":true,\"empty\":false,\"height\":.*,\"visible\":true,\"width\":.*"
    }

    def get_locators(self):
        return {
            "content": {"type": WebElement, "path": UPath(type_ == "TEXTAREA", visible_ == True)},
            "submit_btn": {"type": WebElement, "path": UPath(type_ == "SPAN", text_ == "Submit")},
            "select_photo_icon": {"type": WebElement, "path": UPath(class_ == "upload-placeholder-3oiPv", text_ == "0/4")},
            "already_feedback_panel": {"type": WebElement, "path": UPath(class_ == "title-1Xlj7")},
            "report_title": {"type": WebElement, "path": UPath(class_ == "csp-header__title__text")},
        }

    def input_content(self, content):
        if self["content"].wait_for_existing(timeout=3, raise_error=False):
            self["content"].input(content)
        time.sleep(2)
        for _ in Retry(limit=2, raise_error=False):
            if self["submit_btn"].wait_for_existing(timeout=3, raise_error=False):
                self["submit_btn"].click()

    def input_content_with_attachment(self, content):
        if self["content"].wait_for_existing(timeout=3, raise_error=False):
            self["content"].input(content)
        time.sleep(2)
        # for _ in Retry(limit=2, raise_error=False):
        #     if self["submit_btn"].wait_for_existing(timeout=3, raise_error=False):
        #         self["submit_btn"].click()

    def add_photo(self):
        if self["already_feedback_panel"].wait_for_existing(timeout=3, raise_error=False):
            self["report_title"].click()
        time.sleep(2)
        self["select_photo_icon"].click()
        time.sleep(2)


class AttachmentPanel(BasePanel):
    window_spec = {
        "activity": 'com.ss.android.ugc.aweme.fe.method.upload.ImageChooseUploadActivity'}

    def get_locators(self):
        return {
            "select_photo": {"type": Control, "path": UPath(id_ == 'rv_images')/0/UPath(id_ == 'image_select_indicator')},
            "ok_btn": {"type": Control, "path": UPath(id_ == 'tv_sure')},
        }

    def select_photo(self):
        self["select_photo"].click()
        time.sleep(2)

    def click_ok_btn(self):
        if self["ok_btn"].wait_for_existing(timeout=3, raise_error=False):
            self["ok_btn"].click()
        time.sleep(2)


class All_video(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.i18n.musically.cut.AvatarChooseActivity|com.ss.android.ugc.aweme.i18n.musically.cut.AvatarCutActivity'}

    def get_locators(self):
        return {
            "all_video_title": {"type": Control, "path": UPath(id_ == 'nav_bar_title')},
            "all_video_photo_1": {"type": Control, "path": UPath(id_ == "grid") / 0 / UPath(id_ == "media_view", visible_ == True)},
            "all_video_photo": {"type": Control, "path": UPath(id_ == "media_view", visible_ == True)},
            "all_video_save": {"type": Control, "path": UPath(id_ == "notification_fans_root", visible_ == True)},
        }
    def ClickAllVideoPhoto(self):
        if self["all_video_photo_1"].wait_for_visible(timeout=5, raise_error=False):
            self["all_video_photo_1"].click()
            self["all_video_save"].click()
        elif self["all_video_photo"].wait_for_visible(timeout=5, raise_error=True):
            self["all_video_photo"].click()
            self["all_video_save"].click()

    def get_all_video_text(self):
        return self["all_video_title"].text

class Save_All_media1(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.profile.ui.CropActivity'}

    def get_locators(self):
        return {
            "all_media_save": {"type": Control, "path": UPath(id_ == "tv_confirm")},
        }
    def ClickAllMediaSaveBtn(self):
        self["all_media_save"].click()

class Save_All_media(BasePanel):

    window_spec = {
        "activity":'com.zhihu.matisse.ui.MatisseActivity'}

    def get_locators(self):
        return {
            "all_media_title": {"type": Control, "path": UPath(id_ == "selected_album", visible_ == True)},
            "all_media_photo": {"type": Control, "path": UPath(id_ == "check_view", visible_ == True)},
            "all_media_photo1": {"type": Control, "path": UPath(id_ == "recyclerview") / 0 / UPath(id_ == "check_view", visible_ == True)},
            "confirm_btn": {"type": Control, "path": UPath(id_ == "button_apply")}
        }
    def ClickAllMediaPhoto(self):
        if self["all_media_photo1"].wait_for_visible(timeout=5, raise_error=False):
            self["all_media_photo1"].click()
            self["confirm_btn"].click()
        elif self["all_media_photo"].wait_for_visible(timeout=5, raise_error=True):
            self["all_media_photo"].click()
            self["confirm_btn"].click()

    def ClickSaveBtn(self):
        if self["save_btn"].wait_for_visible(timeout=5, raise_error=True):
            self["save_btn"].click()
            time.sleep(8)

    def GetAllmediaTitle(self):
        return self["all_media_title"].text

class Save_All_video(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.i18n.musically.cut.AvatarCutActivity'}

    def get_locators(self):
        return {
            "save_btn": {"type": Control, "path": UPath(id_ == "next", visible_ == True)}
        }
    def ClickSaveBtn(self):
        if self["save_btn"].wait_for_visible(timeout=5, raise_error=True):
            self["save_btn"].click()
            time.sleep(8)

class ActionButtons(BasePanel):
    window_spec = {
        "activity": 'com.bytedance.hybrid.spark.page.SparkActivity'}

    def get_locators(self):
        return {
            "actionbuttons_webview": {"type": ActionButtonsWebview, "path": UPath(id_ == 'activity_container')},
        }


class ActionButtonsWebview(Webview):
    view_spec = {
        "url": "https://lf77-gecko-source.tiktokcdn.com/obj/byte-gurd-source-sg/10/gecko/resource/tiktok_ba_pia/ba_profile.html?_pia_=1&page_action_type=1&enter_from=profile#/info"
    }

    def get_locators(self):
        return {
            "account_and_profile_label": {"type": WebElement, "path": UPath(class_ == "text-color-TextPrimary", class_ == "flex-row") / UPath(type_ == "P")},
        }

    def ClickEmailCell(self):
        self["account_and_profile_label"].click()
        time.sleep(3)

    def GetEmailText(self):
        return self["account_and_profile_label"].text

class ActionButtonsEmailPage(BasePanel):
    window_spec = {"activity":'com.bytedance.hybrid.spark.page.SparkActivity#1|com.bytedance.hybrid.spark.page.SparkActivity'}

    def get_locators(self):
        return {
            "email_delete": {"path": UPath(id_ == 'iv_clear_all', visible_ == True)},
            "email_input": {"type": Control, "path": UPath(id_ == "et_input")},
            "email_save": {"type": Control, "path": UPath(text_ == "Save")},
        }
    def UpdateEmailInfo(self,value):
        self["email_delete"].click()
        time.sleep(2)
        self["email_input"].text = value
        time.sleep(2)
        self["email_save"].click()
        time.sleep(3)

class Edit_profile_page(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.profile.ui.ProfileEditActivity'}

    def get_locators(self):
        return {
            "name": {"type": Control, "path": UPath(text_ == 'Name')},
            "update_name_page_7_days_text": {"type": Control, "path": UPath(id_ == "tv_edit_hint")},
            "confirm_update_name_button": {"type": Control, "path": UPath(text_ == "Confirm")},
            "email": {"type": Control, "path": UPath(text_ == "Action buttons", visible_ == True)},
            "get_email_text": {"type": Control, "path": UPath(id_ == 'mail_tux_cell')/UPath(id_ == 'label_tv')},
            "update_name": {"type": Control, "path": UPath(id_ == 'et_input')},
            "clear_name": {"type": Control, "path": UPath(id_ == 'iv_clear_all')},
            "change_video": {"type": Control, "path": UPath(id_ == 'header_image_video_icon')},
            "save": {"type": Control, "path": UPath(text_ == 'Save')},
            "back": {"type": Control, "path": UPath(id_ == 'nav_start')/UPath(~type_ == 'com.bytedance.tux.icon.TuxIconView|TuxIconView')},
            "name_text": {"type": Control, "path": UPath(id_ == 'nickname_tux_cell')/UPath(id_ == 'label_tv')},
            "bio_text": {"type": Control, "path": UPath(id_ == 'bio_tux_cell')/UPath(id_ == 'label_tv')},
            "nonprofit": {"type": Control, "path":  UPath(id_ == 'nonprofit_tux_cell')},
            "pronouns_button": {"type": Control, "path":  UPath(text_ == 'Pronouns')},
            "pronouns_title": {"type": Control, "path":UPath(id_ == 'nav_bar_title', text_ == 'Pronouns')},
            "select_organization_title": {"type": Button, "path":  UPath(id_ == 'tv_title')},
        }
    def click_email_button(self):
        self["email"].click()
        time.sleep(3)

    def get_email_text(self):
        return self["get_email_text"].text

    def get_pronouns_title(self):
        return self["pronouns_title"].text

    def click_pronouns_button(self):
        self["pronouns_button"].click()
        time.sleep(3)

    def get_select_organization_title(self):
        return self["select_organization_title"].text

    def click_nonprofit_button(self):
        self["nonprofit"].click()
        time.sleep(3)

    def get_bio_text(self):
        return self["bio_text"].text

    def click_bio_button(self):
        self["bio_text"].click()
        time.sleep(3)

    def click_name(self):
        self["name"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["name"].click()
        time.sleep(3)

    def click_update_name(self,value):
        self["update_name"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["update_name"].text = value
        time.sleep(3)

    def click_change_video(self):
        self["change_video"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["change_video"].click()
        time.sleep(3)

    def click_back(self):
        self["back"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["back"].click()
        time.sleep(3)

    def click_clear_name(self):
        self["clear_name"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["clear_name"].click()
        time.sleep(3)

    def click_save_button(self):
        self["save"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        self["save"].click()
        time.sleep(3)

    def get_update_name_page_7_days_text(self):
        return self["update_name_page_7_days_text"].text

    def click_confirm_update_name_button(self):
        if self["confirm_update_name_button"].wait_for_visible(timeout=5, interval=0.5, raise_error=True):
            self["confirm_update_name_button"].click()
            time.sleep(3)

    def check_save_button(self):
        return self["save"].wait_for_visible(timeout=5, interval=0.5, raise_error=False)

    def get_name_text(self):
        self["name_text"].wait_for_visible(timeout=5, interval=0.5, raise_error=True)
        return self["name_text"].text


class ProfilePageSuggestedPopup(BasePanel):

    window_spec = {
        "activity":'com.ss.android.ugc.aweme.splash.SplashActivity#1|com.ss.android.ugc.aweme.splash.SplashActivity'}

    def get_locators(self):
        return {
            "close_btn": {"type": Control, "path": UPath(id_ == "close_icon_view")},
        }

    def CheckProfilePageSuggestedPopup(self):
        if self["close_btn"].wait_for_visible(timeout=5, interval=0.5, raise_error=False) == True:
            self["close_btn"].click()
            time.sleep(3)
        else:
            logger.info("未发现推人弹窗")

class SelectNonprofitPage(Window):
    """Donation Organization Selection page"""

    window_spec = {'activity': 'com.ss.android.ugc.aweme.profile.ui.ProfileEditActivity.*'}

    def get_locators(self):
        return {
            'ngo_search_box': {'type': Control, 'path': UPath(id_ == "et_search_kw")},
            'search_box': {'type': TextEdit, 'path': UPath(id_ == "et_search_kw")},
            'black_girls_code': {'type': Control, 'path': UPath(id_ == "tv_org_name", text_ == "Black Girls Code")}
        }

    def click_ngo_search_box(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['ngo_search_box'].wait_for_visible(timeout=3, interval=0.5, raise_error=False):
                return self['ngo_search_box'].click()

    def input_ngo(self, ngo):
        # self['search_box'].text = ""
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['search_box'].wait_for_existing(timeout=3, interval=0.5, raise_error=False):
                self['search_box'].input(ngo)
                return True
        return False

    def click_black_girls_code(self):
        for _ in Retry(timeout=10, interval=1, raise_error=False):
            if self['black_girls_code'].wait_for_visible(timeout=3, interval=0.5, raise_error=True):
                return self['black_girls_code'].click()
