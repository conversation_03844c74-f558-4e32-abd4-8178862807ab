# -*- coding: utf8 -*-
import time

from shoots_android.control import *
from uibase.upath import id_, visible_, UPath

from uibase.web import Webview, WebElement
from business.ttlh.utils.main.login import LoginPanel


class DatePickerInSignUp(Control):
    """生日日期选择器
    产品代码：https://code.byted.org/ugc-android/TikTokAccount/blob/develop/awemeaccount/src/main/java/com/ss/android/ugc/aweme/account/common/widget/datepicker/DatePicker.java
    """

    def set_date(self, num):
        self._driver.call_object_method(self.id, '', 'setStartYear', 'void', num)

    def get_date(self):
        return self._driver.call_object_method(self.id, '', 'getYear', 'int')


class NumberPicker(ScrollView):
    """年份选择器
    产品代码：https://code.byted.org/ugc-android/TikTokAccount/blob/develop/awemeaccount/src/main/java/com/ss/android/ugc/aweme/account/common/widget/datepicker/NumberPicker.java
    """

    def set_date(self, num):
        self._driver.call_object_method(self.id, '', 'setCurrentNumber', '', num)


class JourneyPanel(Window):
    """App启动后默认在该页面
    """

    window_spec = {
        "activity": "com.ss.android.ugc.aweme.journey.NewUserJourneyActivity|com.ss.android.ugc.aweme.journey.NewUserJourneyActivity#1|com.ss.android.ugc.aweme.compliance.business.termspp.TermsConsentCombineDialogV2"}

    def get_locators(self):
        return {
            "skip_btn": {"type": Control, "path": UPath(~id_ == "slogan_skip|skip|top_skip")},
            "swipe_gesture": {"type": Control, "path": UPath(id_ == "video_container")},
            "start_watching": {"type": Control, "path": UPath(id_ == "start_watching")},
            "slogan": {"type": TextView, "path": UPath(id_ == "slogan")},
            "interest_selection_header_title": {"type": TextView, "path": UPath(id_ == 'header_title')},
            "second_title": {"type": TextView, "path": UPath(id_ == 'second_title')},
            "select_nfl_interest": {"type": TextView, "path": UPath(id_ == 'text', text_ == 'NFL')},
            "select_motherhood_interest": {"type": TextView, "path": UPath(id_ == 'text', text_ == 'Motherhood')},
            "interest_selection_next_button": {"type": TextView, "path": UPath(id_ == 'center_next')},
            "username_skip": {"type": TextView, "path": UPath(text_ == "Skip", visible_ == True)},
            "swipe_up_title": {"type": TextView, "path": UPath(id_ == 'title', )},

            # Consent box
            "consent_box": {"path": UPath(id_ == "root", type_ == "LinearLayout")},
            "consent_box_text": {"path": UPath(id_ == "tv_msg", visible_ == True)},
            "consent_box_agree_continue_btn": {"path": UPath(id_ == "btn_agree")},
            "next_button": {"path": UPath(id_ == "done")},
            # select video language screen
            "header_title_ae": {"path": UPath(id_ == "header_title")},
            "header_title_my": {"path":  UPath(id_ == "header_title")},
            "second_title": {"path": UPath(id_ == "second_title")},
            'anchor_layout_1': {'path': UPath(id_ == 'anchor_layout') / 1},
            'العربية': {'path': UPath(text_ == 'العربية')},
            'Arabic': {'path': UPath(text_ == 'Arabic')},
            'anchor_layout_2': {'path': UPath(id_ == 'anchor_layout') / 2},
            'local_language_English': {'path': UPath(id_ == 'local_language', text_ == 'English')},
            'english_English': {'path': UPath(id_ == 'english', text_ == 'English')},
            'anchor_layout': {"path": UPath(id_ == "rv")},
            'fake_lynx_view': {"path": UPath(id_ == "container") / 0 / 0 / 1 / 0},
            'fake_lynx_view_english_only': {"path": UPath(id_ == "container")},
            'fake_lynx_view_tagalog_only': {"path": UPath(id_ == "container") / 0 / 0 / 1 / 0 },

            # KR region consent box
            'terms_and_condition_title': {"path": UPath(id_ == "terms_title")},
            'terms_text': {"path": UPath(text_ == "To continue, please agree to TikTok’s Terms of Service and Privacy Policy.")},
            'tv_terms': {"path": UPath(id_ == "tv_terms")},
            'tv_privacy': {"path": UPath(id_ == "tv_privacy")},
            'select_both_button': {"path": UPath(id_ == "btn_continue")},
            'got_it': {"path": UPath(text_ == "Got it")},
            'interest_category_name': {"path": UPath(id_ == "rv") / 1 / UPath(id_ == "interest_category_name")},
            'jp_interest_name': {"path" : UPath(text_ == "スポーツ")},
            'interest_name': {"path": UPath(id_ == "rv") / 1}

        }

    def interest_name(self):
        return self['interest_name'].visible

    def jp_interest_name(self):
        return self['jp_interest_name'].visible


    def interest_category_name(self):
        return self['interest_category_name'].visible

    def got_it_text(self):
        return self['got_it'].visible

    def click_got_it(self):
        return self['got_it'].click()

    @property
    def terms_title_kr(self):
        return self['terms_and_condition_title'].text

    def terms_title_kr_is_displayed(self):
        return self['terms_and_condition_title'].wait_for_existing(raise_error=False)

    @property
    def terms_text_kr(self):
        return self['terms_text'].text

    @property
    def terms_of_service_kr(self):
        return self['tv_terms'].text

    @property
    def privacy_policy_kr(self):
        return self['tv_privacy'].text

    def select_both_button_and_continue(self):
        self['select_both_button'].click()
        return self['select_both_button'].click()

    def language_exist_and_click_ae(self, language):
        if language == "Arabic":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 1).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view,path=UPath(desc_ == "Arabic"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 1)

        if language == "English":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 2).wait_for_existing(raise_error=False)
            if lang == True:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 2)
            else:
                lang = Control(container=self.fake_lynx_view_english_only, path=UPath(id_ == "container") / 0 / 0 / 1 / 0 / 1 / 0 / 1)

        if language == "Hindi":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 3).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Hindi"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 3)
        if language == "Urdu":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 4).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Urdu"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 4)
        if language == "Malay":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 5).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Malay"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 5)
        if language == "Tagalog":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 6).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view_tagalog_only)
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 6)
        if language == "Bengali":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 7).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Bengali"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 7)
        if language == "Telugu":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 8).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view,path=UPath(desc_ == "Telugu"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 8)
        if language == "Tamil":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 9).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Tamil"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 9)
        if language == "French":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 10).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "French"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 10)
        if language == "Malayalam":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 11).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Malayalam"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 11)
        if language == "Kannada":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 12).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Kannada"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 12)
        if language == "Marathi":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 13).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Marathi"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 13)
        if language == "Punjabi":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 14).wait_for_existing(raise_error=False)
            if lang == False:
                lang = Control(root=self.fake_lynx_view, path=UPath(desc_ == "Punjabi"))
            else:
                lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 14)
        return lang.click()

    def language_exist_and_click_MY(self, language):
        if language == "Bahasa Melayu (Malaysia)":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 1)
        if language == "中文（繁體）":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 2)
        if language == "English":
            lang = Control(root=self.anchor_layout, path=UPath(id_ == "rv") / 3)
        return lang.visible and lang.click()

    def scroll_root(self, coefficient_x=0, coefficient_y=0):
        self['anchor_layout'].scroll(coefficient_x=coefficient_x, coefficient_y=coefficient_y)

    @property
    def choose_app_languages_title_MY(self):
        return self['header_title_my'].text

    @property
    def select_video_language_AE(self):
        return self['header_title_ae'].text

    @property
    def select_video_language_second_title_AE(self):
        return self['second_title'].text

    @property
    def next_button(self):
        return self['next_button']

    @property
    def next_button_click(self):
        return self['next_button'].click()

    def next_button_text(self):
        return self['next_button'].text

    @property
    def consent_box_agree_continue_btn(self):
        return self['consent_box_agree_continue_btn'].click()

    @property
    def consent_box_agree_continue_txt(self):
        return self['consent_box_agree_continue_btn'].text


    def consent_box(self):
        return self['consent_box_text'].wait_for_existing(raise_error=False)

    @property
    def consent_box_text(self):
        return self['consent_box_text'].text

    @property
    def swipe_up_title(self):
        return self['swipe_up_title'].text

    @property
    def swipe_up_second_title(self):
        return self['second_title'].text

    def username_skip(self):
        return self['username_skip'].click()

    def skip(self):
        self["skip_btn"].click()

    def different_region_skip(self, language):
        if language == "JP":
            assert self["skip_btn"].text == "スキップ"
            self["skip_btn"].click()
        if language == "FR":
            assert self["skip_btn"].text == "Ignorer"
            self["skip_btn"].click()
        if language == "ID":
            assert self["skip_btn"].text == "Lewati"
            self["skip_btn"].click()

    def select_nfl_interest(self):
        return self['select_nfl_interest'].click()

    def select_motherhood_interest(self):
        return self['select_motherhood_interest'].click()

    def interest_selection_next_button(self):
        self['interest_selection_next_button'].click()

    @property
    def interest_selection_second_title(self):
        return self['second_title'].text

    @property
    def interest_selection_header_title(self):
        return self['interest_selection_header_title'].text

    def start_watching(self):
        if self["start_watching"].wait_for_existing(timeout=3, raise_error=False):
            self["start_watching"].click()

    def start_watching_text(self):
        if self["start_watching"].wait_for_existing(timeout=3, raise_error=False):
            return self["start_watching"].text

    def different_region_start_watching(self):
        if self["start_watching"].wait_for_existing(timeout=3, raise_error=False) and self["start_watching"].text == "動画を見る":
            self["start_watching"].click()

    @property
    def slogan_text(self):
        return self['slogan'].text


class I18nSignUpPanel(Window):
    """App启动后默认在该页面
    """

    window_spec = {
        "activity": "com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivityWithNoAnimation|com.ss.android.ugc.aweme.account.login.I18nSignUpActivityWithNoAnimation|com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivity|com.bytedance.lobby.internal.LobbyInvisibleActivity|com.ss.android.ugc.aweme.account.login.v2.ui.SignUpOrLoginActivity"}

    def get_locators(self):
        return {
            "right_question_info": {"type": Control, "path": UPath(id_ == "top_right_icon")},
            "left_question_info": {"type": Control, "path": UPath(id_ == 'top_left_icon')},
            "privacy_text": {"type": TextView, "path": UPath(id_ == "tv_bottom", visible_ == True)},
            "log_in_text": {"type": TextView, "path": UPath(~id_ == "ll_login_group|tv_footer", visible_ == True,
                                                            ~text_ == "Already have an account\\? Log in|已經有帳號\\？登入|已经有账号\\？登录")},
            "sign_up_text": {"path": UPath(id_ == "tv_footer", visible_ == True,
                                                             text_ == "Don’t have an account? Sign up")},
            "log_in_select": {"type": TextView,
                              "path": UPath(~text_ == "Use phone / .*|使用電話 / .*|使用手机/.*", visible_ == True)},
            "Continue_with_ins": {"type": TextView, "path": UPath(id_ == 'channel_list', visible_ == True) / 5 / UPath(
                id_ == 'channel_name')},
            "webview": {"type": PrivacyWebview, "path": UPath(id_ == "webview")},
            "page_title": {"type": TextView, "path": UPath(id_ == 'tv_title', visible_ == True)},
            "recover_account": {"type": TextView, "path": UPath(id_ == 'positive_button')},
            "page_text": {"type": TextView, "path": UPath(id_ == 'tv_content')},
            "go_back_btn": {"type": Control, "path": UPath(id_ == "negative_button")},
            "login_page_title": {"type": TextView,
                                 "path": UPath(id_ == "channel_list", visible_ == True) / UPath(id_ == 'title')},
            "Continue_with_twitter": {"type": TextView,
                                      "path": UPath(id_ == 'channel_list', visible_ == True) / 4},

            "Use_phone/email/username": {"type": TextView,
                                         "path": UPath(id_ == 'channel_list', visible_ == True) / 1},
            "Continue_with_facebook": {"type": TextView,
                                       "path": UPath(id_ == 'channel_list', visible_ == True) / 2},
            "Continue_with_Google": {"type": TextView,
                                     "path": UPath(id_ == 'channel_list', visible_ == True) / 3},
            "Continue_with_Instagram": {"type": TextView,
                                        "path": UPath(id_ == 'channel_list', visible_ == True) / 5},
            "login_page_second_title": {"type": TextView, "path": UPath(id_ == 'second_title', visible_ == True)},
            "Use_phone_or_email": {"type": TextView, "path": UPath(id_ == 'channel_name', visible_ == True,
                                                                   text_ == "Use phone or email")},
            "Agree_to_all" : {"type": Control, "path": UPath(id_ == "cb_agree_all", visible_ == True)}

        }

    def click_agree_to_all(self):
        if self["Agree_to_all"].wait_for_visible(timeout=10, raise_error=False):
            self["Agree_to_all"].click()
        else:
            self["Agree_to_all"].click()

    def click_question_info_language(self):
        if self["right_question_info"].wait_for_visible(timeout=10, raise_error=False):
            self["right_question_info"].long_click()
        else:
            self["left_question_info"].long_click()

    def click_question_info(self, duration=None):
        if duration:
            if self["right_question_info"].wait_for_visible(timeout=10, raise_error=False):
                self["right_question_info"].long_click(duration=duration)
            else:
                self["left_question_info"].long_click(duration=duration)
        else:
            if self["right_question_info"].wait_for_visible(timeout=2, raise_error=False):
                self["right_question_info"].click()
            else:
                self["left_question_info"].wait_for_visible(timeout=2, raise_error=False)
                self["left_question_info"].click()

    def click_right_icon(self):
        if self["right_question_info"].wait_for_existing(timeout=5, raise_error=False):
            self["right_question_info"].long_click()

    def click_span(self, text):
        span_list = self["privacy_text"].get_clickable_span()
        for span in span_list:
            if span["text"] == text:
                self["privacy_text"].click_char_rect(span["Start"], span["End"])
                return

    def get_clickable_spanlist(self):
        span_list = self["privacy_text"].get_clickable_span()
        clickable_list=[]
        for span in span_list:
            clickable_list.append(span["text"])
        return clickable_list



    def log_in(self):
        for _ in Retry(timeout=15, interval=1, raise_error=False):
            if self["log_in_text"].wait_for_existing(timeout=2, raise_error=False):
                self["log_in_text"].click()
            elif self["log_in_select"].existing:
                break
        self["log_in_select"].click()

    def log_in1(self):
        self["log_in_text"].click()

    def log_in_by_use_phone_email_username(self):
        self["Use_phone/email/username"].click()

    def log_in2(self):
        if self["login_page_title"].text == 'Sign up for TikTok':
            self["log_in_text"].click()
        if self["login_page_title"].text == "Log in to TikTok":
            return

    def signup_by_Use_phone_or_email(self):
        self["Use_phone_or_email"].wait_for_visible(timeout=2, raise_error=False)
        return self["Use_phone_or_email"].click()

    def wait_for_terms_visible(self):
        time.sleep(1)
        self.webview.refresh()
        self.webview.refresh_container()
        return self.webview["terms"].wait_for_existing(raise_error=False)

    def wait_for_policy_visible(self):
        time.sleep(1)
        self.webview.refresh()
        self.webview.refresh_container()
        return self.webview["policy"].wait_for_existing(raise_error=False)

    @property
    def privacy_text_us_region(self):
        return self["privacy_text"].text
    @property
    def privacy_text_gb_region(self):
        return self["privacy_text"].text

    @property
    def verify_login_page_second_title(self):
        return self["login_page_second_title"].text

    def switch_to_signup_panel(self):
        if self["sign_up_text"].wait_for_visible(timeout=5, interval = 1,raise_error=False):
            self["sign_up_text"].click()


    @property
    def verify_signup_page_second_title(self):
        return self["login_page_second_title"].text


class SignUpPanel(I18nSignUpPanel):
    """从Me Tab页进入的SignUp界面
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivity|com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivityWithNoAnimation|com.ss.android.ugc.aweme.account.login.I18nSignUpActivity"}

    def __init__(self, path=None, root=None, driver=None, timeout=10):
        super(SignUpPanel, self).__init__(
            path=path, root=root, driver=driver, timeout=timeout)
        self.locators.update({
            # "phone_num": {"type": Control, "path": UPath(id_ == 'channel_list', visible_ == True) / 1},
            "back_btn": {"type": Control, "path": UPath(id_ == 'back_btn', visible_ == True)},
            "more_btn": {"type": Control, "path": UPath(id_ == 'more', visible_ == True)},
            "sign_up_title": {"type": TextView,
                              "path": UPath(id_ == 'title', visible_ == True, text_ == 'Sign up for TikTok')},
            "log_in_title": {"type": TextView,
                             "path": UPath(id_ == 'title', visible_ == True, text_ == 'Log in to TikTok')},
            "top_left_icon": {"type": Control, "path": UPath(id_ == 'top_left_icon')},
            "me_page_text": {"type": TextView, "path": UPath(id_ == "hint")},
            "use_phone_num": {"path": UPath(id_ == 'channel_list', visible_ == True) / 1},
            "sign_up_second_title": {"type": TextView,
                                     "path": UPath(id_ == 'second_title', visible_ == True)}
        })

    def me_page_text_verify(self):
        return self["me_page_text"].text

    def left_top_icon(self):
        self["top_left_icon"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self['top_left_icon'].click()

    def return_to_profile_page(self):
        if self["sign_up_title"].wait_for_existing(timeout=3, raise_error=False):
            self.top_left_icon.click()
        time.sleep(2)

    def use_phone_to_signup(self):
        self.refresh()
        self["use_phone_num"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["use_phone_num"].click()

    def top_left_button_click(self):
        self["left_question_info"].wait_for_visible(timeout=2, raise_error=False)
        self["left_question_info"].click()

    def top_right_button_click(self, duration):
        self["right_question_info"].wait_for_visible(timeout=2, raise_error=False)
        if duration:
            self["right_question_info"].long_click(duration=duration)
        else:
            self["right_question_info"].click()

    def top_right_icon_validate(self):
        if not self["right_question_info"].wait_for_visible(timeout=2, raise_error=False):
            return False
        return self["right_question_info"].clickable


    def continue_with_ins_click(self):
        self["Continue_with_ins"].wait_for_visible(timeout=2, raise_error=False)
        self["Continue_with_ins"].click()

    def switch_to_login_panel(self):
        self["log_in_text"].click()
        return self["log_in_title"].wait_for_visible(timeout=2, raise_error=False)

    def switch_to_signup_panel(self):
        self["sign_up_text"].click()
        return self["sign_up_title"].wait_for_visible(timeout=2, raise_error=False)

    def back_from_terms_page(self):
        self["back_btn"].wait_for_existing(timeout=2, raise_error=False)
        self["back_btn"].click()
        time.sleep(5)

    def show_more_login_ways(self):
        self["more_btn"].wait_for_visible(timeout=2, raise_error=False)
        self["more_btn"].click()
        return self["Continue_with_twitter"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)


    @property
    def verify_title_msg(self):
        return self["page_title"].text

    def recover_account_click(self):
        self["recover_account"].wait_for_visible(timeout=2, raise_error=False)
        self["recover_account"].click()

    @property
    def verify_text_msg(self):
        return self["page_text"].text

    def go_back_click(self):
        self["go_back_btn"].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self["go_back_btn"].click()

    @property
    def verify_login_page_title(self):
        if self["login_page_title"].wait_for_existing(timeout=3, raise_error=False):
            return self["login_page_title"].text


    @property
    def verify_signup_page_title(self):
        if self["sign_up_title"].wait_for_existing(timeout=3, raise_error=False):
            return self["sign_up_title"].text

    @property
    def sign_up_second_title_text(self):
        return self["sign_up_second_title"].text

    def click_use_phone_num(self):
        self['use_phone_num'].wait_for_existing(timeout=2, interval=0.5, raise_error=False)
        self['use_phone_num'].click()


class AgeGatePanel(Window):
    """For the Age gate screen panel
    """

    window_spec = {
        "activity": "com.ss.android.ugc.aweme.compliance.business.agegate.AgeGateActivity|com.ss.android.ugc.aweme.compliance.business.agegate.AgeGateActivity#1|com.ss.android.ugc.aweme.pns.agegate.view.PNSAgeGateActivity|com.ss.android.ugc.aweme.pns.agegate.ui.view.PNSAgeGateActivity"}

    def get_locators(self):
        return {
            "age_gate_nav_bar_title": {"type": TextView, "path": UPath(id_ == "nav_bar_title", visible_ == True)},
            "age_gate_content_title": {"type": TextView, "path": UPath(id_ == "ageGateContentTitle")},
            "age_gate_content_desc": {"type": TextView, "path": UPath(id_ == "ageGateContentDesc")},
            "age_gate_end_image": {"type": ImageView, "path": UPath(id_ == "ageGateEndImage")},
            "age_gate_edit_text": {"path": UPath(id_ == "ageGateDobInput")},
            "age_gate_next_button": {"type": Button, "path": UPath(id_ == "ageGateNextButton",visible_ == True)},
            "year_picker": {"type": NumberPicker, "path": UPath(id_ == 'year_picker',visible_ == True)},
            "birthday_selector": {"type": DatePickerInSignUp, "path": UPath(id_ == 'ageGateDatePicker')},
            "birthday_input_edit": {"path" : UPath(id_ == "inputWithIndicatorEditText")},
            "kids_mode_pop_up":{"path": UPath(id_ == "content_tv")},
            "kids_mode_pop_up_close": {"path": UPath(text_ == "Let's go")},
            "review_birth_date_ok_btn":{"path" : UPath(text_ == "OK",visible_ == True)},
            "edit_btn":{"path" : UPath(text_ == "Edit")},
            "confirm_btn":{"path" : UPath(text_ == "Confirm")},
            "title_tv": {"path": UPath(id_ == "title_tv")},
            "content_tv": {"path": UPath(id_ == "content_tv")}
        }

    @property
    def age_gate_nav_bar_title_text(self):
        return self['age_gate_nav_bar_title'].text

    @property
    def age_gate_content_title_text(self):
        return self['age_gate_content_title'].text

    @property
    def age_gate_content_desc_text(self):
        return self['age_gate_content_desc'].text

    def age_gate_end_image(self):
        self['age_gate_end_image']

    @property
    def age_gate_edit_text(self):
        return self['age_gate_edit_text'].text

    @property
    def age_gate_next_button_enabled(self):
        return self['age_gate_next_button'].enabled

    def age_gate_next_button(self):
        self['age_gate_next_button'].click()

    def review_birth_date_ok_btn(self):
        self["review_birth_date_ok_btn"].click()

    def edit_popup_click(self):
        self["edit_btn"].click()

    def confirm_popup_click(self):
        self["confirm_btn"].click()

    @property
    def popup_title_text(self):
        return self['title_tv'].text

    @property
    def popup_desc_text(self):
        return self['content_tv'].text

    def set_birthday_on_Age_gate_screen(self, year):
        #self["year_picker"].set_date(year + 1)
        x = 1
        # added a hack for selecting age for now
        rect_height = self["year_picker"].ensure_visible().height
        while x != year - 10:
            self["year_picker"].scroll(0, -int(rect_height))
            x = x +1
        self.refresh()
        current_year = self["birthday_selector"].get_date()
        return current_year == year

    def set_birthday_age_gate_screen(self, year):
        self["year_picker"].set_date(year + 1)
        rect_height = self["year_picker"].ensure_visible().height
        self["year_picker"].scroll_by_offset(0, -int(rect_height) / 3)
        self.refresh()
        current_year = self["birthday_selector"].get_date()
        return current_year == year

    @property
    def check_kids_mode_popup(self):
        return self["kids_mode_pop_up"].wait_for_visible(timeout=5, interval=1,raise_error=True)

    def close_kids_popup(self):
        self["kids_mode_pop_up_close"].wait_for_visible(timeout=5, interval=1,raise_error=True)
        self["kids_mode_pop_up_close"].click()

class PrivacyWebview(Webview):
    view_spec = {
        "description": "\"attached\":true.*"
    }

    def get_locators(self):
        return {
            "article": {"type": WebElement, "path": UPath(type_ == "h1", id_ == "terms-us")},
            "terms": {"type": WebElement, "path": UPath(text_ == "Terms of Service", index=0)},
            "privacy": {"type": WebElement, "path": UPath(type_ == "h1", id_ == "privacy-us")},
            "policy": {"type": WebElement, "path": UPath(text_ == "Privacy Policy", index=0)},
        }

