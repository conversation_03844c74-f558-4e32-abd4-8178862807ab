# -*- coding:utf-8 _*-
import time

from shoots_android.control import *
from uibase.upath import id_, class_
from business.ttlh.utils.main.video import *
from uibase.upath import *
from uibase.controls import Window


class FollowRelationTab(Window):
    """Follow Relation Tab
    """

    window_spec = {"activity": "com.ss.android.ugc.aweme.following.ui.FollowRelationTabActivity"}

    def get_locators(self):
        return {
        "tab_title": {"type": Control, "path":UPath(id_ == "title_tv", visible_ == True)},
        "mutual_tab_text": {"type": Control, "path": UPath(text_ == "Mutual", visible_ == True)}
        }
    
    def check_tab_title_text(self):
        self["tab_title"].wait_for_visible(timeout=5, raise_error=False)
        return self["tab_title"].text
    
    def check_mutual_tab(self):
        return self["tab_title"].wait_for_visible(timeout=5, raise_error=False)