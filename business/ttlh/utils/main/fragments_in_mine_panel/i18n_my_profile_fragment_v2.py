# -*- coding: utf-8 -*-

from uibase.upath import *
from uibase.controls import Window


class I18nMyProfileFragmentV2InMinePanel(Window):
    """activity=com.ss.android.ugc.aweme.splash.SplashActivity
    """
    window_spec = {
        "path": UPath(fragment_ == 'I18nMyProfileFragmentV2')
    }

    def get_locators(self):
        return {
        'advanced_feature_icon': {'path': UPath(id_ == 'advanced_feature_icon', visible_ == True)},
        'advanced_feature_text': {'path': UPath(id_ == 'advanced_feature_text', visible_ == True)},
        'cover': {'path': UPath(id_ == 'cover', visible_ == True)},
        }

    def wait_for_advanced_feature_icon_visible(self):
        self['advanced_feature_icon'].wait_for_visible()

    def wait_for_advanced_feature_text_text(self):
        self['advanced_feature_text'].wait_for_text('^Add Yours$')

    def wait_for_cover_visible(self):
        self['cover'].wait_for_visible()

    def click_if_cover_exist(self):
        if self['cover'].wait_for_visible(timeout=5, raise_error=False):
            self['cover'].click()

    def click_advanced_feature_icon(self):
        self['advanced_feature_icon'].click()

    def click_advanced_feature_text(self):
        self['advanced_feature_text'].click()


