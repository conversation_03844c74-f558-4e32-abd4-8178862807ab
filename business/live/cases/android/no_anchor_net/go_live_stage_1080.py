# -*- coding: utf-8 -*-

"""
Author: gengdongya
Date: 2025-02-20
Description: 非主播网秀场直播-1080P
"""
from business.ttlh.utils.android.Login import LoginPanel
from business.ttlh.utils.android.M2LAndroidLiveUI import HostLivePanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import PlatformType


class GoLiveStage1080(TTCrossPlatformPerfAppTestBase):
    """
    非主播网秀场直播-1080P
    """
    owner = "gengdongya"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "非主播网秀场直播-1080P"
    description = ""
    tags = []

    def run_test(self):
        self.start_step('登录测试账号')
        loginpage = LoginPanel(root=self.perf_app)
        loginpage.login(account="***********", code="006815")

        self.start_step("主播开启直播")
        page = HostLivePanel(root=self.perf_app)
        page.start_live(choose_quality=True, quality='1080p', wait_time=5)

        self.start_step("开始采集性能数据")
        perf_data = self.collect_perf_data(app=self.perf_app)
        self.assert_("性能采集失败", perf_data)

        self.start_step("关闭直播")
        page.close_live()


if __name__ == '__main__':
    GoLiveStage1080().debug_run()
