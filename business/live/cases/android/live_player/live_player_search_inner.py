# -*- coding: utf-8 -*-
"""
Author: gengdongya
Date: 2025-04-27
Description: 搜索直播内流看播性能测试
"""

import time

from business.live.utils.android.live_panel import LivePanel
from business.live.utils.android.search_page import SearchUtils
from business.ttlh.utils.android.Login import LoginPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import PlatformType


class LivePlayerSearchInner(TTCrossPlatformPerfAppTestBase):
    """
    搜索直播内流持续看播
    """
    owner = "gengdongya"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "搜索直播内流持续看播20分钟"
    description = ""
    tags = []

    def run_test(self):
        self.start_step('登录测试账号')
        loginpage = LoginPanel(root=self.perf_app)
        loginpage.login(account="***********", code="006815")

        self.start_step('进入搜索页，搜索关键字live，切换直播Tab点击第一个卡片进入直播间')
        time.sleep(5)
        search_utils = SearchUtils(app=self.perf_app)
        search_utils.go_to_live_room_from_search(search_word="live")
        live_page = LivePanel(root=self.perf_app)

        self.start_step("直播播放20分钟，开始采集性能数据")
        perf_data = self.collect_perf_data(app=self.perf_app)
        # time.sleep(20 * 60)
        self.assert_("性能采集失败", perf_data)

        self.start_step("退出直播间")
        live_page.close()
        time.sleep(5)


if __name__ == '__main__':
    LivePlayerSearchInner().debug_run()
