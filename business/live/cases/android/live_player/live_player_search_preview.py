# -*- coding: utf-8 -*-
"""
Author: gengdongya
Date: 2025-04-27
Description: 搜索直播预览流看播性能测试
"""

import time

from business.live.utils.android.live_panel import LivePanel
from business.live.utils.android.search_page import SearchUtils
from business.ttlh.utils.android.Login import LoginPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import PlatformType


class LivePlayerSearchPreview(TTCrossPlatformPerfAppTestBase):
    """
    搜索直播内流持续看播
    """
    owner = "gengdongya"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "搜索直播预览流持续看播20分钟"
    description = ""
    tags = []

    def run_test(self):
        self.start_step('登录测试账号')
        loginpage = LoginPanel(root=self.perf_app)
        loginpage.login(account="***********", code="006815")

        self.start_step('进入搜索页，搜索关键字live，切换直播Tab，直播预览卡看播20分钟')
        time.sleep(5)
        SearchUtils(app=self.perf_app).go_to_live_preview_from_search(search_word="live")

        self.start_step("直播预览流播放20分钟，开始采集性能数据")
        perf_data = self.collect_perf_data(app=self.perf_app)
        # time.sleep(10)
        self.assert_("性能采集失败", perf_data)

        self.start_step("测试结束")
        time.sleep(5)


if __name__ == '__main__':
    LivePlayerSearchPreview().debug_run()
