# -*- coding: utf-8 -*-
"""
Author: gengdongya
Date: 2025-04-29
Description: 直播广场看播性能测试
"""

import time

from business.live.utils.android.live_panel import LiveSquareUtils, LivePanel
from business.ttlh.utils.android.Login import LoginPanel
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import PlatformType


class LivePlayerLiveSquare(TTCrossPlatformPerfAppTestBase):
    """
    直播广场 - 看播20mins
    """
    owner = "gengdongya"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "直播广场持续看播20分钟"
    description = ""
    tags = []

    def run_test(self):
        self.start_step('登录测试账号')
        loginpage = LoginPanel(root=self.perf_app)
        loginpage.login(account="***********", code="006815")

        self.start_step('进入直播广场观看直播')
        live_square = LiveSquareUtils(self.perf_app)
        live_square.go_to_live_room_from_square()
        time.sleep(5)
        live_page = LivePanel(root=self.perf_app)

        self.start_step("直播播放20分钟，开始采集性能数据")
        perf_data = self.collect_perf_data(app=self.perf_app)
        self.assert_("性能采集失败", perf_data)
        time.sleep(2)

        self.start_step("退出直播间")
        live_page.close()
        time.sleep(5)


class LivePlayerLiveSquareSwipe(TTCrossPlatformPerfAppTestBase):
    """
    直播广场 - 持续上滑20分钟
    """
    owner = "gengdongya"
    timeout = 3600
    platform = [PlatformType.ANDROID]
    device_count = 1
    title = "直播广场看播30后上滑一次，持续20分钟"
    description = ""
    tags = []

    def play_and_swipe(self, times, interval):
        LiveSquareUtils(self.perf_app).swipe_up_times(times=times, interval=interval)

    def run_test(self):
        self.start_step('登录测试账号')
        loginpage = LoginPanel(root=self.perf_app)
        loginpage.login(account="***********", code="006815")

        self.start_step('进入直播广场观看直播')
        live_square = LiveSquareUtils(self.perf_app)
        live_square.go_to_live_room_from_square()
        time.sleep(5)
        live_page = LivePanel(root=self.perf_app)

        self.start_step("每看播30s后上滑一次，同时采集性能数据")
        # 20分钟内：每个直播间看30s后上滑，共计上滑40次
        perf_data = self.collect_perf_data(
            app=self.perf_app,
            operations=self.play_and_swipe,
            operations_args=(40, 30)
        )
        self.assert_("性能采集失败", perf_data)
        # self.play_and_swipe(times=5, interval=10)
        time.sleep(2)

        self.start_step("退出直播间")
        live_page.close()
        time.sleep(5)


if __name__ == '__main__':
    LivePlayerLiveSquareSwipe().debug_run()
