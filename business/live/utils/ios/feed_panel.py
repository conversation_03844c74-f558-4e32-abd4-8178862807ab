# -*- coding: utf-8 -*-

from uibase.controls import *
from uibase.upath import *


class FeedPanel(Window):
    """
        iOS Feed Panel
    """
    window_spec = {"path": UPath(id_ == "AWEMaskWindow")}

    def get_locators(self):
        return {
            "live_square": {"path": UPath(type_ == "AWELiveFeedEntranceView")},
            "search_btn": {"path": UPath(type_ == "TTKSearchEntranceButton")},
            "homepage_tab": {"path": UPath(id_ == "首页")}
        }

    def enter_search_page(self):
        self.click_homepage_tab()
        if self['search_btn'].wait_for_visible():
            self["search_btn"].click()

    def enter_live_square(self):
        self.click_homepage_tab()
        if self['live_square'].wait_for_visible():
            self["live_square"].click()

    def click_homepage_tab(self):
        if self['homepage_tab'].wait_for_visible():
            self['homepage_tab'].click()
