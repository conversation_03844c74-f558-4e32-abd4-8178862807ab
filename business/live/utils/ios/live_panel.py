# -*- coding: utf-8 -*-

"""
Author: gengdongya
Date: 2025-05-08
Description: 直播间看播页
"""
import time

from uibase.controls import Window
from uibase.upath import *

from business.live.utils.ios.feed_panel import FeedPanel
from utils.common.log_utils import logger


class LivePanel(Window):
    """
    直播间看播页
    """

    window_spec = {"path": UPath(controller_ == "IESLiveRoomSlideViewController")}

    def get_locators(self):
        return {
            "live_play": {"path": UPath(type_ == "MTKView")},
            "close_btn": {"path": UPath(label_ == "TopClose", visible_ == True)}
        }

    def close(self):
        if self['close_btn'].wait_for_visible():
            self['close_btn'].click()

    def is_live_card(self):
        logger.info("是否为直播间：{}".format(self["live_play"].existing))
        return self['live_play'].wait_for_visible(timeout=10, raise_error=False)

    def swipe_up_to_next_live(self):
        # self.scroll(coefficient_y=0.5)
        self.scroll(coefficient_y=0.8)


class LiveSquareUtils:

    def __init__(self, app):
        self.app = app
        self.feed = None
        self.live_page = None

    def go_to_live_room_from_square(self):
        logger.info("[Start] 点击首页左上角LIVE按钮进入直播广场")
        self.feed = FeedPanel(root=self.app)
        self.feed.enter_live_square()
        time.sleep(3)

        logger.info("[Start] 上滑切换到下个直播间")
        self.live_page = LivePanel(root=self.app)
        self.live_page.swipe_up_to_next_live()
        return self.live_page.is_live_card()

    def swipe_up_times(self, times, interval):
        self.live_page = LivePanel(root=self.app)
        for t in range(times):
            self.live_page.swipe_up_to_next_live()
            time.sleep(interval)
