# -*- coding: utf-8 -*-

from shoots_android.control import *


class FeedPanel(Window):
    """
    Android Feed Panel
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity",
        "process_name": "com.zhiliaoapp.musically"
    }

    def get_locators(self):
        return {
            "live_square": {"path": UPath(id_ == "rl_tab_container") / 1},
            "search_btn": {"path": UPath(id_ == "rl_tab_container") / 2},
        }

    def is_current_window(self):
        activity = self.window_spec['activity']
        return activity == self.root.get_device().get_current_activity()

    def enter_search_page(self):
        if self['search_btn'].wait_for_visible():
            self["search_btn"].click()

    def enter_live_square(self):
        if self['live_square'].wait_for_visible():
            self["live_square"].click()
