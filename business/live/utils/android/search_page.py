# -*- coding: utf-8 -*-

from shoots_android.control import *

from business.live.utils.android.feed_panel import FeedPanel
from business.live.utils.android.live_panel import LivePanel


class SearchMiddlePanel(Window):
    """
    搜索中间页
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity",
        "process_name": "com.zhiliaoapp.musically"
    }

    def get_locators(self):
        return {
            "search_input": {"path": UPath(id_ == "et_search_middle")},
            "search_btn": {"path": UPath(id_ == "tv_search_textview")},
        }

    def search_by_keyword(self, search_word):
        self['search_input'].text = search_word
        self.click_search_btn()

    def click_search_btn(self):
        if self['search_btn'].wait_for_visible():
            self["search_btn"].click()


class SearchResultPanel(Window):
    """
    搜索结果页
    """
    window_spec = {
        "activity": "com.ss.android.ugc.aweme.splash.SplashActivity",
        "process_name": "com.zhiliaoapp.musically"
    }

    def get_locators(self):
        return {
            "直播Tab": {"path": UPath(type_ == "TuxTabBar$f") / UPath(text_ == "直播")},
            "fist_card": {"path": UPath(type_ == "SparkView") / 0 / 0 / 1},
        }

    def click_live_tab(self):
        if self['直播Tab'].wait_for_visible():
            self['直播Tab'].click()

    def is_live_preview_exist(self):
        return self['fist_card'].wait_for_visible()

    def click_fist_card(self):
        if self['fist_card'].wait_for_visible():
            self['fist_card'].click()


class SearchUtils:
    """
    从搜索进入直播间相关UI操作的封装
    """

    def __init__(self, app):
        self.app = app
        self.feed = None
        self.search_page = None
        self.search_res_page = None
        self.live_page = None

    def _go_to_search_res_page(self, search_word):
        logger.info("[Start] 点击首页右上角搜索按钮进入搜索中间页")
        self.feed = FeedPanel(root=self.app)
        self.feed.enter_search_page()
        time.sleep(5)

        logger.info("[Start] 搜索中间页输入并搜索关键字")
        self.search_page = SearchMiddlePanel(root=self.app)
        self.search_page.search_by_keyword(search_word=search_word)
        time.sleep(5)

        logger.info("[Start] 搜索结果页切换到直播Tab")
        self.search_res_page = SearchResultPanel(root=self.app)
        self.search_res_page.click_live_tab()

    def go_to_live_preview_from_search(self, search_word):
        self._go_to_search_res_page(search_word=search_word)
        time.sleep(3)

        logger.info("[Start] 校验搜索结果页-直播Tab是否存在直播预览卡")
        return self.search_res_page.is_live_preview_exist()

    def go_to_live_room_from_search(self, search_word):
        self._go_to_search_res_page(search_word=search_word)
        time.sleep(3)
        self.search_res_page.click_fist_card()
        time.sleep(3)

        logger.info("[Start] 校验当前是否为直播间")
        self.live_page = LivePanel(root=self.app)
        return self.live_page.is_live_card()
