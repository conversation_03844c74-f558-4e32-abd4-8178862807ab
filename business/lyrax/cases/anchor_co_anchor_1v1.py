"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 11:36:48
FilePath: /global_business_perf/business/global_rtc/cases/anchor_co_anchor_1v1.py
Description: 主播与主播1V1连麦场景测试用例
"""
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from utils.common.log_utils import logger, log_exception
from defines import *


class AnchorCoAnchor_1V1(TTCrossPlatformPerfAppTestBase):
    """
    AnchorCoAnchor_1V1
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 2
    title = "主播与主播1V1连麦"
    description = "主播与主播1V1连麦场景性能测试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("获取辅助设备应用")
        auxil_app = self.auxil_app_list[0]

        logger.info("登录账号")
        self.login_all_devices()

        logger.info("主播A开启直播")
        self.start_live_broadcast(self.perf_app)
        self.assert_anchor_live_broadcast(self.perf_app)

        logger.info("主播B开启直播")
        self.start_live_broadcast(auxil_app)
        self.assert_anchor_live_broadcast(auxil_app)

        logger.info("主播A与主播B连麦")
        self.invite_anchor_connect(auxil_app, self.perf_account["account_username"])
        self.accept_anchor_connect(self.perf_app)
        self.assert_anchor_connect(self.perf_app, assert_type="1v1")

        logger.info("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)


if __name__ == '__main__':
    AnchorCoAnchor_1V1().debug_run()
