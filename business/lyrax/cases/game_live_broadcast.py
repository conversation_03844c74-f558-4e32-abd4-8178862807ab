'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 14:09:34
FilePath: /global_business_perf/business/global_rtc/cases/game_live_broadcast.py
Description: 
'''
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from utils.common.log_utils import logger, log_exception
from defines import *

class GameLiveBroadCast(TTCrossPlatformPerfAppTestBase):
    """
    GameLiveBroadCast
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 1
    title = "游戏直播"
    description = "游戏直播场景性能测试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("登录账号")
        self.login_all_devices()

        logger.info("主播开启游戏直播")
        self.start_live_broadcast(self.perf_app, live_type="game")
        self.assert_anchor_live_broadcast(self.perf_app)

        logger.info("开始采集性能数据")
        self.collect_perf_data(app=self.perf_app)


if __name__ == '__main__':
    GameLiveBroadCast().debug_run()
