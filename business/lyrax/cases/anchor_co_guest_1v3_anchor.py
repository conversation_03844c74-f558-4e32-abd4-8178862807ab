'''
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-18 14:08:27
FilePath: /global_business_perf/business/global_rtc/cases/anchor_co_guest_1v3_anchor.py
Description: 
'''
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from utils.common.log_utils import logger, log_exception
from defines import *

class AnchorCoGuest_1V3_Anchor(TTCrossPlatformPerfAppTestBase):
    """
    AnchorCoGuest_1V3
    """
    owner = HEJIABEI
    timeout = 3600
    platform = [PlatformType.ANDROID, PlatformType.IOS]
    device_count = 4
    title = "主播与嘉宾1V3连麦"
    description = "主播与嘉宾1V3连麦场景性能测试用例"
    tags = []

    @log_exception
    def run_test(self):
        logger.info("获取辅助设备应用")
        auxil_apps = self.auxil_app_list[:self.device_count - 1]

        logger.info("登录账号")
        self.login_all_devices()
        
        logger.info("主播开启直播")
        self.start_live_broadcast(self.perf_app)
        self.assert_anchor_live_broadcast(self.perf_app)

        for index, auxil_app in enumerate(auxil_apps):
            logger.info(f"第{index+1}个嘉宾通过UI进入直播间")
            self.search_and_enter_live_room(auxil_app, self.perf_account["account_username"])
            self.assert_guest_enter_anchor_room(auxil_app)
            logger.info(f"第{index+1}个嘉宾申请连麦")
            self.guest_apply_connect(auxil_app)
            self.anchor_agree_guest_connect(self.perf_app)
            logger.info("嘉宾打开摄像头")
            self.guest_open_camera(auxil_app)

        self.assert_guest_connect(self.perf_app, assert_type="1v3")

        logger.info("开始采集基础性能数据")
        self.collect_perf_data(app=self.perf_app)

if __name__ == '__main__':
    AnchorCoGuest_1V3_Anchor().debug_run()
