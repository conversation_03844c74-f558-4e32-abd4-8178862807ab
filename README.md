# 全球业务性能测试框架

一个全面的跨平台业务性能测试框架，专注于字节跳动全球业务的移动应用性能监控、分析和优化。

## 🚀 核心特性

### 📱 支持平台
- **Android** - 完整支持所有性能指标采集
- **iOS** - 完整支持所有性能指标采集
- **跨平台** - 统一的测试接口和数据处理流程

### 🔧 性能采集工具
- **DS性能采集工具** - 字节跳动内部专业性能监控工具
- **GamePerf性能采集工具** - 游戏和高性能应用专用监控工具
- **统一接口** - 支持工具间无缝切换和对比分析

### 🌐 采集方式
- **有线连接采集** - 稳定可靠的USB连接方式
- **无线连接采集** - 灵活便捷的WiFi连接方式
- **自动切换** - 根据设备状态智能选择最佳连接方式

### 📊 数据处理能力
- **实时数据采集** - 毫秒级精度的性能数据收集
- **智能数据处理** - 自动数据清洗、去重和异常值处理
- **多维度分析** - 支持时间序列、统计分析和趋势预测
- **数据可视化** - 丰富的图表和报告生成

## 🎯 业务覆盖

本框架支持字节跳动全球业务的性能测试，包括：

### 📺 直播业务 (Live)
- 主播开播性能测试
- 连麦场景性能监控
- 多档位推流性能分析
- 美颜特效性能评估

### 🎵 TikTok/抖音海外版 (TTLH)
- 视频播放性能测试
- 短视频录制性能监控
- 社交功能性能分析
- 推荐算法性能评估

### 📞 实时通讯 (Global RTC)
- 音视频通话性能测试
- 多人会议性能监控
- 网络适应性测试
- 延迟和质量分析

### 🎬 点播业务 (VOD)
- 视频播放性能测试
- 缓存策略性能分析
- 多码率切换性能监控

### 🎮 游戏业务 (CLA)
- 游戏运行性能测试
- 渲染性能监控
- 交互延迟测试

### 📱 企业应用 (GSM Performance)
- 企业级应用性能测试
- 办公场景性能监控

## 📈 性能指标

框架收集全面的性能指标，支持深度性能分析：

### 🤖 Android 性能指标
- **CPU指标**: 标准化总使用率、进程使用率
- **GPU指标**: GPU使用率、GPU负载
- **内存指标**: PSS内存、USS内存、Dalvik堆内存、Dalvik其他内存
- **电池指标**: 电流、电压、功耗
- **网络指标**: 总接收/发送流量、进程接收/发送流量
- **系统指标**: 文件描述符数量、线程数量
- **存储指标**: 磁盘读取/写入速度
- **显示指标**: 帧率(FPS)

### 🍎 iOS 性能指标
- **CPU指标**: 总使用率、进程使用率
- **GPU指标**: 设备GPU、渲染GPU、平铺GPU
- **内存指标**: 总内存占用、常驻内存、内存足迹
- **电池指标**: 电池温度、电压、电流、功耗
- **网络指标**: 进程接收/发送流量
- **系统指标**: 上下文切换、中断唤醒
- **存储指标**: 磁盘读取/写入速度
- **能耗指标**: 总能耗、CPU能耗、GPU能耗
- **显示指标**: 帧率(FPS)

### 📊 扩展数据采集
- **CPU Profile数据** - 详细的CPU性能分析数据
- **ByteIO数据** - 用户行为和事件数据
- **Trace数据** - 应用执行轨迹和性能追踪数据

## 🏗️ 项目架构

```
global_business_perf/
├── business/                    # 🏢 业务测试实现
│   ├── live/                   # 📺 直播业务测试
│   ├── ttlh/                   # 🎵 TikTok海外版测试
│   ├── global_rtc/             # 📞 实时通讯测试
│   ├── vod/                    # 🎬 点播业务测试
│   ├── cla/                    # 🎮 游戏业务测试
│   ├── gsm_performance/        # 📱 企业应用测试
│   └── lyrax/                  # 🔧 其他业务测试
├── common/                      # 🛠️ 通用组件和工具
│   ├── tiktok/                 # TikTok通用组件
│   └── feilian/                # 飞连相关工具
├── utils/                       # 🔧 核心工具模块
│   ├── perf/                   # 性能测量和数据处理
│   │   ├── ds_perf_monitor.py          # DS性能监控器
│   │   ├── gameperf_monitor.py         # GamePerf性能监控器
│   │   ├── ds_data_process.py          # DS数据处理器
│   │   ├── gameperf_data_process.py    # GamePerf数据处理器
│   │   ├── profile_collector.py        # CPU Profile收集器
│   │   ├── byteio_data_manager.py      # ByteIO数据管理器
│   │   └── trace_data_manager.py       # Trace数据管理器
│   ├── apis/                   # API接口模块
│   ├── control/                # 测试控制和管理
│   └── common/                 # 通用工具函数
├── 📋 配置和脚本文件
├── requirements.txt            # Python依赖包
├── run.sh                     # 🚀 主执行脚本
├── manage.py                  # 📊 项目管理脚本
├── defines.py                 # 📝 常量和枚举定义
├── settings.py                # ⚙️ 项目配置
├── task.json                  # 📋 任务配置文件
└── cleans.py                  # 🧹 清理工具
```

## 📁 数据文件结构

测试执行后会生成以下文件结构：

```
测试用例根目录/
├── case.log                    # 测试用例执行日志
├── cpu_profile.data           # CPU性能分析数据
└── data/                     # 性能工具专用数据目录
    └── {device_id}/
        ├── trace_raw_data.json           # Trace原始数据
        ├── trace_processed_data.json     # Trace处理后数据
        ├── trace_avg_data.json           # Trace平均值数据
        ├── byteio_raw_data.json          # ByteIO原始数据
        ├── byteio_processed_data.json    # ByteIO处理后数据
        ├── byteio_avg_data.json          # ByteIO平均值数据
        └── {package_name}/
            ├── ds_raw_perf_data.json         # DS原始性能数据
            ├── ds_processed_perf_data.json   # DS处理后性能数据
            ├── ds_avg_perf_data.json         # DS平均性能数据
            ├── gameperf_raw_perf_data.json   # GamePerf原始性能数据
            ├── gameperf_processed_perf_data.json # GamePerf处理后性能数据
            └── gameperf_avg_perf_data.json   # GamePerf平均性能数据
```

## 🛠️ 环境要求

### 基础环境
- **Python 3.9** - 主要开发语言
- **Homebrew** - macOS包管理器（macOS用户）
- **虚拟环境** - 隔离的Python运行环境
- **字节跳动内部网络** - 访问内部包仓库和服务

### 设备要求
- **Android设备** - 支持USB调试和无线调试
- **iOS设备** - 支持开发者模式和无线调试
- **macOS开发机** - 用于iOS设备连接和调试

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone [仓库地址]
cd global_business_perf
```

### 2. 一键安装
```bash
chmod +x run.sh
./run.sh
```

安装脚本会自动完成：
- ✅ 检查并安装Homebrew（macOS）
- ✅ 安装Python 3.9
- ✅ 创建虚拟环境
- ✅ 安装所有依赖包
- ✅ 配置项目环境

### 3. 选择业务和用例
脚本会自动列出可用的业务模块和测试用例：
```
选择要运行的业务:
1. live          # 直播业务
2. ttlh          # TikTok海外版
3. global_rtc    # 实时通讯
4. vod           # 点播业务
5. cla           # 游戏业务
6. gsm_performance # 企业应用
```

### 4. 执行测试
选择对应的业务和用例后，框架会自动执行性能测试。

## ⚙️ 配置说明

### 核心配置文件
- **`settings.py`** - 项目主配置，包含路径、库依赖等
- **`defines.py`** - 性能指标、平台类型、工具类型等枚举定义
- **`task.json`** - 任务配置，包含设备信息、采集参数等
- **`requirements.txt`** - Python依赖包列表

### 关键配置项
```python
# 性能采集工具选择
PerfToolType.DS = 1         # DS性能采集工具
PerfToolType.GAMEPERF = 2   # GamePerf性能采集工具

# 采集方式选择
PerfCollectMode.WIRE_COLLECT = 1      # 有线连接
PerfCollectMode.WIRELESS_COLLECT = 2  # 无线连接

# 平台类型
PlatformType.ANDROID = 1    # Android平台
PlatformType.IOS = 2        # iOS平台
```

## 📖 使用指南

### 🎯 基础使用

#### 1. 交互式运行（推荐）
```bash
./run.sh
```
脚本会引导你选择业务模块和具体测试用例。

#### 2. 直接运行指定用例
```bash
# 激活虚拟环境
source .venv/bin/activate

# 运行指定测试用例
python business/ttlh/cases/android/anchor_game_golive.py
```

#### 3. 项目管理
```bash
python manage.py [命令]    # 项目管理和维护
```

#### 4. 清理测试产物
```bash
python cleans.py --pattern "*.log" "*.png" "*.jpg"
```

### 🔧 高级配置

#### 性能采集工具切换
在`task.json`中配置：
```json
{
  "task_info": {
    "perf_tool_type": 1,  // 1=DS工具, 2=GamePerf工具
    "perf_collect_mode": 2  // 1=有线连接, 2=无线连接
  }
}
```

#### 设备配置
```json
{
  "devices": [
    {
      "udid": "R5CWB30CMWK",           // Android设备ID
      "platform": "android",
      "ip": "*************"            // 无线连接IP
    },
    {
      "udid": "00008101-001865592650001E",  // iOS设备ID
      "platform": "ios",
      "ip": "*************"
    }
  ]
}
```

### 📊 数据分析

#### 性能数据文件说明
- **原始数据** (`*_raw_perf_data.json`) - 采集的原始性能数据
- **平均数据** (`*_avg_perf_data.json`) - 统计分析后的平均值数据
- **CPU Profile** (`cpu_profile.data`) - 详细的CPU性能分析
- **ByteIO数据** (`byteio_data.json`) - 用户行为事件数据
- **Trace数据** (`trace_data.json`) - 应用执行轨迹数据

#### 数据处理流程
1. **数据采集** - 使用DS或GamePerf工具实时采集
2. **数据清洗** - 自动去除异常值和无效数据
3. **数据处理** - 计算平均值、趋势分析
4. **数据保存** - 分类保存到对应目录

## 👨‍💻 开发指南

### 🆕 添加新测试用例

#### 1. 选择业务目录
根据测试内容选择对应的业务目录：
```
business/
├── live/           # 直播相关测试
├── ttlh/           # TikTok海外版测试
├── global_rtc/     # 实时通讯测试
├── vod/            # 点播测试
├── cla/            # 游戏测试
└── gsm_performance/ # 企业应用测试
```

#### 2. 创建测试文件
```python
from common.tiktok.base import TTCrossPlatformPerfAppTestBase
from defines import *

class YourTestCase(TTCrossPlatformPerfAppTestBase):
    """测试用例描述"""
    owner = HEJIABEI                    # 负责人
    timeout = 3600                      # 超时时间(秒)
    platform = [PlatformType.ANDROID]  # 支持平台
    device_count = 1                    # 设备数量
    title = "测试用例标题"
    description = "详细描述"
    tags = ["performance", "live"]      # 标签

    def run_test(self):
        """测试执行逻辑"""
        # 实现具体的测试步骤
        pass
```

#### 3. 性能数据采集集成
```python
# 在测试用例中集成性能采集
def run_test(self):
    # 开始性能采集
    self.start_perf_collection()

    # 执行测试步骤
    self.execute_test_steps()

    # 停止性能采集并处理数据
    self.stop_perf_collection()
```

### 🔧 核心API使用

#### 性能监控器使用
```python
from utils.perf.ds_perf_monitor import ds_perf_monitor
from utils.perf.gameperf_monitor import gameperf_monitor
from defines import PerfToolType, PerfCollectMode

# DS工具使用示例
if perf_tool_type == PerfToolType.DS:
    perf_client = ds_perf_monitor.start_collection_for_app(
        app=app,
        perf_collect_mode=PerfCollectMode.WIRELESS_COLLECT,
        perf_device_ip="*************",
        collect_duration=60
    )

# GamePerf工具使用示例
if perf_tool_type == PerfToolType.GAMEPERF:
    gameperf_monitor.start_collection_for_app(
        app=app,
        perf_collect_mode=PerfCollectMode.WIRELESS_COLLECT,
        collect_duration=60
    )
```

#### 数据处理器使用
```python
from utils.perf.ds_data_process import ds_perf_data_processor
from utils.perf.gameperf_data_process import gameperf_perf_data_processor

# 处理DS数据
perf_data, avg_data = ds_perf_data_processor.process_ds_perf_data(
    data_source=raw_data,
    save_local_path="./data"
)

# 处理GamePerf数据
perf_data, avg_data = gameperf_perf_data_processor.handle_perf_data(
    basic_perf_data=raw_data,
    save_local_path="./data"
)
```

### 🏗️ 架构扩展

#### 添加新的性能工具
1. 在`utils/perf/`目录下创建新的监控器和数据处理器
2. 在`defines.py`中添加新的工具类型枚举
3. 在统一接口中集成新工具

#### 添加新的业务模块
1. 在`business/`目录下创建新的业务目录
2. 实现业务特定的工具类和测试基类
3. 添加对应的测试用例

## 📦 依赖管理

### 字节跳动内部依赖
```bash
# 核心测试框架
shoots                      # 基础测试框架
shoots-android             # Android测试支持
shoots-ios                 # iOS测试支持
shoots-cv                  # 计算机视觉支持
ui_ext_byted              # UI扩展组件

# 性能监控工具
bytedance.ds              # DS性能监控
bytedance.profile_toolkit # CPU性能分析

# 数据处理和存储
bytedbdc                  # 数据库连接
bytedtos                  # 对象存储
```

### 安装命令
```bash
# 使用内部PyPI镜像安装
pip install -r requirements.txt \
  -i https://bytedpypi.byted.org/simple/ \
  -i https://shoots-pypi.bytedance.net/simple/
```

## ⚠️ 注意事项

### 🔒 安全和权限
- 需要字节跳动内部网络访问权限
- 设备需要开启开发者模式和USB调试
- iOS设备需要信任开发证书

### 📁 数据管理
- 测试数据自动保存到指定目录结构
- 使用`cleans.py`定期清理临时文件
- 重要数据会上传到TOS存储桶备份

### 🔧 设备连接
- 支持USB有线连接和WiFi无线连接
- 无线连接需要设备和测试机在同一网络
- 建议优先使用有线连接确保稳定性

### 📊 性能数据
- 数据采集精度可达毫秒级
- 支持多设备并发采集
- 自动处理数据异常和丢失情况

## 🤝 贡献指南

### 代码规范
- 遵循PEP 8 Python代码规范
- 使用中文注释和文档字符串
- 保持代码简洁和可读性
- 添加必要的错误处理和日志

### 提交规范
```bash
# 提交消息格式
git commit -m "功能描述

- 具体修改点1
- 具体修改点2
- 修复的问题或新增的功能"
```

### 测试要求
- 新功能必须包含对应的测试用例
- 确保现有测试用例通过
- 性能相关修改需要进行回归测试

## 📞 支持和联系

### 团队成员
- **贺加贝** (hejiabei.oxep) - 项目负责人

### 问题反馈
- 🐛 **Bug报告**: 通过内部工单系统提交
- 💡 **功能建议**: 联系项目负责人讨论
- 📖 **文档问题**: 直接提交PR修改

### 相关资源
- 📚 **内部文档**: [字节跳动性能测试平台文档]
- 🔧 **工具文档**: [DS工具使用指南] | [GamePerf工具使用指南]
- 📊 **数据平台**: [性能数据分析平台]

## 📈 版本历史

### 最新更新
- ✅ **数据保存重构** - 统一CPU Profile、ByteIO、Trace数据保存逻辑
- ✅ **工具集成优化** - DS和GamePerf工具接口统一
- ✅ **跨平台支持** - 完善Android和iOS平台支持
- ✅ **数据处理增强** - 提升数据处理精度和稳定性

### 发展路线
- 🔄 **实时监控** - 支持实时性能数据展示
- 🤖 **智能分析** - 集成AI性能异常检测
- 🌐 **云端部署** - 支持云端设备和远程测试
- 📱 **更多平台** - 扩展支持Web、小程序等平台

---

<div align="center">

**🚀 让性能测试更简单，让产品体验更优秀！**

*Built with ❤️ by ByteDance Global Business Performance Team*

</div>