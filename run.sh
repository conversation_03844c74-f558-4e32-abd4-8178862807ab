#!/bin/bash

# 获取项目根目录
PROJECT_ROOT=$(pwd)

# 设置 PYTHONPATH
export PYTHONPATH=$PROJECT_ROOT

# 检查并安装 Homebrew
if ! command -v brew &> /dev/null; then
    echo "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://gitee.com/ineo6/homebrew-install/raw/master/install.sh)"
else
    echo "Updating Homebrew..."
    brew update
    echo "Homebrew already installed"
fi

# 检查并安装 Python 3.9
if ! command -v python3.9 &> /dev/null; then
    echo "Installing Python 3.9..."
    brew install python@3.9
else
    echo "Python 3.9 already installed"
fi

# 检查并创建虚拟环境
if [ ! -d "$PROJECT_ROOT/.venv" ]; then
    echo "Creating virtual environment..."
    python3.9 -m venv .venv
fi

# 激活虚拟环境
echo "Activating virtual environment..."
source .venv/bin/activate

# 确保使用虚拟环境中的 pip
VENV_PIP="$PROJECT_ROOT/.venv/bin/pip3.9"
VENV_PYTHON="$PROJECT_ROOT/.venv/bin/python3.9"

# 更新 pip
echo "Updating pip..."
"$VENV_PYTHON" -m pip install --upgrade pip

# 安装依赖包
echo "Installing requirements..."
"$VENV_PIP" install -r requirements.txt -i https://bytedpypi.byted.org/simple/ -i https://shoots-pypi.bytedance.net/simple/ -U --progress-bar on --no-cache-dir 2>&1 | grep -v "Requirement already satisfied"

echo "Setup completed successfully!"

# 获取所有业务目录
echo -e "\n选择要运行的业务:"
business_dirs=($(ls -d $PROJECT_ROOT/business/*/ | xargs -n 1 basename))
for i in "${!business_dirs[@]}"; do
    echo "$((i+1)). ${business_dirs[$i]}"
done

# 选择业务
read -p "请输入业务编号 (1-${#business_dirs[@]}): " business_choice
if [[ ! "$business_choice" =~ ^[0-9]+$ ]] || [ "$business_choice" -lt 1 ] || [ "$business_choice" -gt "${#business_dirs[@]}" ]; then
    echo "无效的选择"
    exit 1
fi

selected_business="${business_dirs[$((business_choice-1))]}"
cases_dir="$PROJECT_ROOT/business/$selected_business/cases"

# 检查 cases 目录是否存在
if [ ! -d "$cases_dir" ]; then
    echo "错误: $cases_dir 目录不存在"
    exit 1
fi

# 获取所有 Python 用例文件
echo -e "\n选择要运行的用例:"
case_files=($(ls "$cases_dir"/*.py | grep -v "__init__" | xargs -n 1 basename))
for i in "${!case_files[@]}"; do
    echo "$((i+1)). ${case_files[$i]}"
done

# 选择用例文件
read -p "请输入用例编号 (1-${#case_files[@]}): " case_choice
if [[ ! "$case_choice" =~ ^[0-9]+$ ]] || [ "$case_choice" -lt 1 ] || [ "$case_choice" -gt "${#case_files[@]}" ]; then
    echo "无效的选择"
    exit 1
fi

selected_case="${case_files[$((case_choice-1))]}"
case_path="$cases_dir/$selected_case"

# 执行选定的用例
echo -e "\n执行用例: $selected_case"
"$VENV_PYTHON" "$case_path"