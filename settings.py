import os

PROJECT_NAME = "global_business_perf"
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
INSTALLED_LIBS = ["uibase", "shoots_android", "shoots_ios", "ui_ext_byted"]
RESOURCE_PATH = os.path.join(PROJECT_ROOT, "resources")

# 当前实验类型，1: 实验组 2: 对照组
GLOBAL_BUSINESS_PERF_TASK_CURRENT_EXPERIMENT_TYPE = 1
# 当前是否采集性能数据，True: 采集 False: 不采集
GLOBAL_BUSINESS_PERF_TASK_CURRENT_COLLECT_PERF_DATA = True