"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/common/tiktok/perf.py
Description: 性能测试相关功能模块 - 性能监控和分析功能
"""

import threading
import time
import os
import json
import glob
import ctypes
from typing import Union, Optional, Callable, Tuple
from dataclasses import dataclass

from common.tiktok.android.app import AndroidTikTokApp
from common.tiktok.ios.app import iOSTikTokApp
from defines import *
from utils.common.log_utils import logger
from utils.control.serial_control import serial_control
from utils.control.android_control import android_control
from utils.control.ios_control import ios_control
from utils.perf.ds_perf_monitor import ds_perf_monitor
from utils.perf.ds_data_process import ds_perf_data_processor
from utils.perf.gameperf_data_process import gameperf_data_processor
from utils.perf.profile_collector import profile_collector
from utils.perf.gameperf_monitor import gameperf_manager
from bytedance.profile_toolkit.tool.profiler.cpu_profiler import Platform
from utils.apis.byteio_api import byteio_api
from shoots.config import settings


@dataclass
class PerfCollectResult:
    """性能采集结果数据类"""
    perf_data: Optional[dict] = None  # 基础性能数据
    trace_path: Optional[str] = None  # CPU Profile路径
    byteio_path: Optional[str] = None  # ByteIO数据路径
    start_time: Optional[float] = None  # 采集开始时间
    end_time: Optional[float] = None  # 采集结束时间
    status: str = "pending"  # 状态：pending/running/completed/failed/skipped
    error: Optional[str] = None  # 错误信息


class TTCrossPlatformPerfMixin:
    """跨平台性能测试混入类"""

    def assert_perf_data(self, device_id: str = None, package_name: str = None):
        """断言性能数据文件存在性和完整性

        验证性能数据采集后生成的文件是否完整，包括：
        1. 验证性能数据文件是否存在
        2. 验证原始数据和平均数据字段完整性
        3. 验证关键性能指标的有效性

        Args:
            device_id: 设备ID，如果为None则使用self.perf_udid
            package_name: 包名，如果为None则从perf_app获取
        """
        # 获取设备ID和包名
        if not device_id:
            device_id = getattr(self, 'perf_udid', None)
        if not package_name:
            if hasattr(self, 'perf_app') and self.perf_app:
                if hasattr(self.perf_app, 'package_name'):  # Android
                    package_name = self.perf_app.package_name
                else:  # iOS
                    package_name = self.perf_app.bundle_id

        if not device_id or not package_name:
            raise AssertionError("无法获取设备ID或包名信息")

        # 构建数据路径
        data_path = os.path.join("data", device_id, package_name)

        # 根据性能工具类型验证对应的数据文件
        if self.perf_tool_type == PerfToolType.DS:
            self._assert_ds_perf_data(data_path, device_id)
        elif self.perf_tool_type == PerfToolType.GAMEPERF:
            self._assert_gameperf_perf_data(data_path, device_id)
        else:
            logger.warning(f"[性能数据验证] 未知的性能工具类型: {self.perf_tool_type}，跳过验证")

    def _assert_ds_perf_data(self, data_path: str, device_id: str):
        """验证DS性能数据文件"""
        logger.info(f"[性能数据验证] 开始验证DS性能数据: {data_path} (设备: {device_id})")

        # 定义文件映射
        files = {
            "原始数据": "ds_raw_perf_data.json",
            "处理后数据": "ds_processed_perf_data.json",
            "平均数据": "ds_avg_perf_data.json"
        }

        # 验证文件存在性和内容
        for file_type, filename in files.items():
            file_path = os.path.join(data_path, filename)
            self.assert_(f"[断言] DS{file_type}文件不存在: {file_path}", os.path.exists(file_path))

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                if file_type == "平均数据":
                    self.assert_(f"[断言] DS{file_type}格式错误，应为字典", isinstance(data, dict))
                    self.assert_(f"[断言] DS{file_type}为空", len(data) > 0)
                    # 验证关键指标
                    key_metrics = ["fps", "cpuTotalUsage", "memPSS"]
                    for metric in key_metrics:
                        if metric in data and data[metric] is not None:
                            self.assert_(f"[断言] DS关键指标{metric}值无效: {data[metric]}",
                                       isinstance(data[metric], (int, float)) and data[metric] >= 0)
                else:
                    self.assert_(f"[断言] DS{file_type}格式错误，应为列表", isinstance(data, list))
                    self.assert_(f"[断言] DS{file_type}为空", len(data) > 0)
                    if file_type == "原始数据" and data:
                        # 验证必需字段
                        required_fields = ["time", "pcTime"]
                        for field in required_fields:
                            self.assert_(f"[断言] DS原始数据缺少必需字段: {field}", field in data[0])

            except json.JSONDecodeError as e:
                self.assert_(f"[断言] DS{file_type}JSON格式错误: {str(e)}", False)

        logger.info(f"[性能数据验证] DS性能数据验证通过")

    def _assert_gameperf_perf_data(self, data_path: str, device_id: str):
        """验证GamePerf性能数据文件"""
        logger.info(f"[性能数据验证] 开始验证GamePerf性能数据: {data_path} (设备: {device_id})")

        # 定义文件映射
        files = {
            "原始数据": "gameperf_raw_perf_data.json",
            "处理后数据": "gameperf_processed_perf_data.json",
            "平均数据": "gameperf_avg_perf_data.json"
        }

        # 查找文件路径（支持多进程目录结构）
        file_paths = {}
        for file_type, filename in files.items():
            file_path = os.path.join(data_path, filename)
            if not os.path.exists(file_path):
                # 查找子目录
                pattern = os.path.join(data_path, "*", filename)
                matching_files = glob.glob(pattern)
                if matching_files:
                    file_path = matching_files[0]
            file_paths[file_type] = file_path
            self.assert_(f"[断言] GamePerf{file_type}文件不存在: {file_path}", os.path.exists(file_path))

        # 验证文件内容
        try:
            # 验证原始数据
            with open(file_paths["原始数据"], 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            self.assert_(f"[断言] GamePerf原始数据格式错误，应为字典", isinstance(raw_data, dict))
            self.assert_(f"[断言] GamePerf原始数据缺少data字段", "data" in raw_data)
            data_list = raw_data.get("data", [])
            self.assert_(f"[断言] GamePerf原始数据为空", len(data_list) > 0)
            if data_list:
                self.assert_(f"[断言] GamePerf原始数据缺少必需字段: time", "time" in data_list[0])

            # 验证处理后数据
            with open(file_paths["处理后数据"], 'r', encoding='utf-8') as f:
                processed_data = json.load(f)
            self.assert_(f"[断言] GamePerf处理后数据格式错误，应为列表", isinstance(processed_data, list))
            self.assert_(f"[断言] GamePerf处理后数据为空", len(processed_data) > 0)

            # 验证平均数据
            with open(file_paths["平均数据"], 'r', encoding='utf-8') as f:
                avg_data = json.load(f)
            self.assert_(f"[断言] GamePerf平均数据格式错误，应为字典", isinstance(avg_data, dict))
            self.assert_(f"[断言] GamePerf平均数据为空", len(avg_data) > 0)

            # 验证关键指标
            key_metrics = ["fps", "cpu", "mem"]
            for metric in key_metrics:
                if metric in avg_data and avg_data[metric] is not None:
                    value = avg_data[metric]
                    self.assert_(f"[断言] GamePerf关键指标{metric}值无效: {value}",
                               isinstance(value, (int, float, list)) and
                               (isinstance(value, list) or value >= 0))

            logger.info(f"[性能数据验证] GamePerf性能数据验证通过")

        except json.JSONDecodeError as e:
            self.assert_(f"[断言] GamePerf数据文件JSON格式错误: {str(e)}", False)

    def wireless_connect(self, udid: str, device_ip: Optional[str] = None) -> str:
        """无线连接
        
        Args:
            udid: 设备ID
            device_ip: 设备IP，可选
            
        Returns:
            str: 无线连接后的设备ID
            
        Raises:
            ValueError: 如果平台不支持则抛出异常
        """
        if self.perf_platform == PlatformType.ANDROID:
            wireless_udid = android_control.wireless_connect(udid, device_ip)
            self.assert_(self._format_error_message(udid, "无线连接", "Android"), wireless_udid)
            return wireless_udid
        elif self.perf_platform == PlatformType.IOS:
            wireless_udid = ios_control.wireless_connect(udid)
            self.assert_(self._format_error_message(udid, "无线连接", "iOS"), wireless_udid)
            return wireless_udid

        platform_name = PlatformType.get_name(self.perf_platform)
        raise ValueError(f"暂不支持 {platform_name} 平台的无线连接")

    def wireless_disconnect(self, udid: str) -> bool:
        """断开无线连接
        
        Args:
            udid: 设备ID
            
        Returns:
            bool: 是否成功断开连接
            
        Raises:
            ValueError: 如果平台不支持则抛出异常
        """
        is_disconnect = False
        if self.perf_platform == PlatformType.ANDROID:
            is_disconnect = android_control.wireless_disconnect(udid)
        elif self.perf_platform == PlatformType.IOS:
            is_disconnect = ios_control.wireless_disconnect(udid)
            ios_control.ui_test_init(udid)
        return is_disconnect

    def start_profile_collect(self, app: Union[AndroidTikTokApp, iOSTikTokApp]) -> Optional[str]:
        """开始CPU Profile采集"""
        if isinstance(app, iOSTikTokApp):
            logger.info("iOS平台暂不支持CPU Profile采集")
            return None

        try:
            # 直接调用profile_collector方法
            profile_collector.init_profiler(Platform.ANDROID)
            device_id = self.perf_device_ip if self.perf_collect_mode == PerfCollectMode.WIRELESS_COLLECT else app.get_device().udid
            profile_collector.use_device(device_id=device_id)
            return profile_collector.sync_record(duration_sec=self.collect_time, target=app.package_name)
        except Exception as e:
            logger.warning(f"[性能] CPU Profile采集失败，将跳过此功能: {str(e)}")
            # 降级策略：返回None，让性能采集继续进行，只是没有CPU Profile数据
            return None

    def collect_perf_data(self, app: Union[AndroidTikTokApp, iOSTikTokApp],
                          operations: Optional[Callable[..., None]] = None,
                          operations_args: Optional[Tuple] = None,
                          operations_kwargs: Optional[dict] = None) -> PerfCollectResult:
        """采集性能数据

        Args:
            app: Android或iOS TikTok应用实例
            operations: 性能采集过程中需要执行的操作函数
            operations_args: 操作函数的位置参数
            operations_kwargs: 操作函数的关键字参数

        Returns:
            PerfCollectResult: 包含性能数据采集结果的对象
        """

        def _handle_perf_collection():
            """处理性能数据采集"""
            try:
                # 根据工具类型选择采集方法
                if self.perf_tool_type == PerfToolType.DS:
                    # 调用DS模块的采集方法
                    self.perf_client = ds_perf_monitor.start_collection_for_app(
                        app, self.perf_collect_mode, self.perf_device_ip,
                        self.collect_metrics, self.collect_interval, self.collect_time
                    )
                    if self.perf_client:
                        self.collect_result.status = "completed"
                    else:
                        self.collect_result.status = "failed"
                elif self.perf_tool_type == PerfToolType.GAMEPERF:
                    # 调用GamePerf模块的采集方法
                    title = getattr(self, 'title', self.__class__.__name__)
                    success = gameperf_manager.start_collection_for_app(
                        app, self.perf_collect_mode, self.perf_device_ip,
                        self.collect_time, title
                    )
                    if success:
                        self.collect_result.status = "completed"
                    else:
                        self.collect_result.status = "failed"
                else:
                    # 无效的性能工具类型，跳过性能采集
                    logger.warning(f"[性能] 无效的性能工具类型: {self.perf_tool_type}，跳过性能采集")
                    self.collect_result.status = "skipped"
            except Exception as e:
                self.collect_result.error = f"[性能] 性能采集失败: {e}"
                logger.error(self.collect_result.error)

        def _handle_profile_collection():
            """处理CPU Profile采集"""
            try:
                trace_path = self.start_profile_collect(app)
                if trace_path:
                    self.collect_result.trace_path = trace_path
            except Exception as e:
                logger.error(f"[性能] CPU Profile采集失败: {e}")

        def _execute_operations():
            """执行操作函数"""
            nonlocal operations_kwargs
            if not operations:
                return

            if operations_kwargs is None:
                operations_kwargs = {}

            try:
                operations(*(operations_args or ()), **operations_kwargs)
            except Exception as e:
                logger.error(f"操作执行异常: {e}")

        def _force_terminate_thread(thread: threading.Thread):
            """强制终止线程"""
            try:
                if thread and thread.is_alive():
                    tid = thread.ident
                    exc = ctypes.py_object(SystemExit)
                    res = ctypes.pythonapi.PyThreadState_SetAsyncExc(ctypes.c_long(tid), exc)
                    if res > 1:
                        ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
                    logger.info("操作线程已被强制终止")
            except Exception as e:
                logger.error(f"终止操作线程失败: {e}")

        try:
            # 初始化采集环境
            self.perf_client = None
            self.stop_event = threading.Event()

            logger.info("[性能] 性能采集前对所有设备进行截图...")
            self.screenshot_all_devices()

            # 检查是否需要采集性能数据
            if not settings.GLOBAL_BUSINESS_PERF_TASK_CURRENT_COLLECT_PERF_DATA:
                logger.info("[性能] 根据配置，跳过性能数据采集")
                return PerfCollectResult(start_time=time.time(), end_time=time.time(), status="skipped")

            # 创建采集结果对象
            self.collect_result = PerfCollectResult(start_time=time.time(), status="running")

            # 验证 ByteIO 会话
            if hasattr(self, 'byteio') and isinstance(self.byteio, list):
                try:
                    byteio_api.verify_session(
                        app_id=APP_ID,
                        device_ids=[self.perf_app_did]
                    )
                except Exception as e:
                    logger.error(f"[性能] ByteIO会话验证失败: {e}")

            # 处理无线连接和串口操作
            if self.perf_collect_mode == PerfCollectMode.WIRE_COLLECT:
                self.wireless_disconnect(self.perf_udid)
            elif self.perf_collect_mode == PerfCollectMode.WIRELESS_COLLECT:
                self.wireless_connect(self.perf_udid, self.perf_device_ip)
                serial_control.relay_switch(self.perf_device_serial_port, False)

            # 等待设备稳定
            time.sleep(30)

            # 启动性能采集线程
            perf_thread = threading.Thread(target=_handle_perf_collection)
            profile_thread = threading.Thread(target=_handle_profile_collection)
            perf_thread.start()
            profile_thread.start()

            # 启动操作线程（如果有）
            operations_thread = None
            if operations:
                operations_thread = threading.Thread(target=_execute_operations, daemon=True)
                operations_thread.start()

            # 等待采集时间
            start_time = time.time()
            while time.time() - start_time < self.collect_time:
                time.sleep(0.1)

            # 处理超时的操作线程
            if operations_thread and operations_thread.is_alive():
                logger.warning(f"操作未能在 {self.collect_time} 秒内完成，将被强制终止。")
                self.stop_event.set()
                operations_thread.join(timeout=0.5)
                if operations_thread.is_alive():
                    _force_terminate_thread(operations_thread)

            # 等待性能采集线程完成
            perf_thread.join()
            profile_thread.join()

            # 获取采集数据
            if self.perf_tool_type == PerfToolType.DS:
                    self.collect_result.perf_data = ds_perf_monitor.get_perf_data(self.perf_client)
            elif self.perf_tool_type == PerfToolType.GAMEPERF:
                self.collect_result.perf_data = gameperf_data_processor.load_data_from_files(
                    self.perf_device, self.perf_app
                )
                self.collect_result.status = "completed"
        except Exception as e:
            self.collect_result.status = "failed"
            self.collect_result.error = f"性能数据采集失败: {str(e)}"
            logger.error(self.collect_result.error)

        finally:
            self.collect_result.end_time = time.time()
            # 清理资源
            try:
                if self.perf_client:
                    if self.perf_tool_type == PerfToolType.DS:
                        ds_perf_monitor.close_ds_server(self.perf_client)
                    elif self.perf_tool_type == PerfToolType.GAMEPERF:
                        pass
            except Exception as e:
                logger.error(f"[性能] 停止性能采集失败: {e}")

        return self.collect_result

    def handle_collect_result(self) -> bool:
        """处理采集结果

        Returns:
            bool: 处理成功返回True，失败返回False
        """
        if not self.collect_result:
            logger.warning("[性能数据处理] 没有采集结果")
            return False

        if self.collect_result.status == "skipped":
            logger.info("[性能数据处理] 根据配置跳过了性能数据采集")
            return True

        if self.collect_result.status != "completed":
            logger.warning(f"[性能数据处理] 采集未完成，当前状态: {self.collect_result.status}")
            return False

        try:
            basic_perf_data = self.collect_result.perf_data
            cpu_profile_path = self.collect_result.trace_path

            # 根据性能工具类型调用相应的数据处理方法
            if self.perf_tool_type == PerfToolType.DS:
                logger.info("[性能数据处理] 使用DS数据处理方法")
                result = ds_perf_data_processor.handle_ds_perf_data_with_context(
                    basic_perf_data=basic_perf_data,
                    cpu_profile_path=cpu_profile_path,
                    perf_account=self.perf_account,
                    perf_device=self.perf_device,
                    perf_app=self.perf_app,
                    vqos_trace_configs=self.trace_metrics,
                    byteio_configs=self.byteio_metrics
                )
                # 检查处理结果
                if result and len(result) >= 2:
                    perf_data, perf_avg_data = result[0], result[1]
                    if perf_data or perf_avg_data:
                        logger.info("[性能数据处理] DS数据处理成功")
                        return True
                    else:
                        logger.warning("[性能数据处理] DS数据处理结果为空")
                        return False
                else:
                    logger.warning("[性能数据处理] DS数据处理失败")
                    return False

            elif self.perf_tool_type == PerfToolType.GAMEPERF:
                logger.info("[性能数据处理] 使用GamePerf数据处理方法")
                result = gameperf_data_processor.handle_gameperf_perf_data_with_context(
                    basic_perf_data=basic_perf_data,
                    cpu_profile_path=cpu_profile_path,
                    perf_account=self.perf_account,
                    perf_device=self.perf_device,
                    perf_app=self.perf_app,
                    vqos_trace_configs=self.trace_metrics,
                    byteio_configs=self.byteio_metrics
                )
                # 检查处理结果
                if result and len(result) >= 2:
                    perf_data, perf_avg_data = result[0], result[1]
                    if perf_data or perf_avg_data:
                        logger.info("[性能数据处理] GamePerf数据处理成功")
                        return True
                    else:
                        logger.warning("[性能数据处理] GamePerf数据处理结果为空")
                        return False
                else:
                    logger.warning("[性能数据处理] GamePerf数据处理失败")
                    return False
            else:
                logger.warning(f"[性能数据处理] 未知的性能工具类型: {self.perf_tool_type}，跳过数据处理")
                return False

        except Exception as e:
            logger.error(f"[性能数据处理] 采集结果处理失败: {str(e)}")
            return False


