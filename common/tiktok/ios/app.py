import time
from typing import List, Optional, Dict, Any, Union, Tuple

from shoots.retry import Retry
from shoots_cv.app import CVAppMixin
from api_test.app import APITestAppMixin
from shoots_ios.app import iOSApp, AccessibilityApp
from shoots_libra.app import LibraAppMixin
from ui_ext_byted.app import iOSEventAppMixin
from common.tiktok.ios.popups import *
from utils.common.log_utils import logger
from common.tiktok.rpc.link_mic_instance.link_mic_ios import LinkMiciOS


class iOSTikTokApp(iOSApp, iOSEventAppMixin, CVAppMixin, APITestAppMixin, LibraAppMixin):
    app_spec = {
        "bundle_ids": ["com.zhiliaoapp.musically", "com.zhiliaoapp.musically.ep"],
        "kill_process": True,
        "timeout": 10,
        "libra_global_reverse": True,  # 是否开启libra全局反向代理
        "libra_appid": 22, # AB后台内部id
        "libra_region": "sg"  # 默认是None，表示使用native region
    }
    app_popup_classes = [
        
        IntellectualPropertyPolicyUpdatePopup,
        StartScreenRecordingToStartYourLiveStreamPopup,
        SaveYourLoginInformationPopup,
        TVStrengthenSwipeUpGuidePopup,
        CloseSecurityCheckPopup,
        LiveStudioIsNowAvailablePopup,
        PrivacyPolicyAgreeAndContinuePopup,
        SelectTheContentYouAreInterestedInPopup,
        LiveContentCompliancePopup,
        LiveContentCompliancePopup2,
        PrivacyAgreementPopup,
        BlockUnpopularCommentsPopup,
        SearchFriendsAddressBookPopup,
        VisitorRecordPopup,
        EventTrackPopup,
        IllustrationMReportPopup
    ]
    system_popup_rules = [
        {
            "match": {"label": "在“飞书”中打开？"},
            "popup_params": {"button_texts": ["打开"]}
        },
        # TikTok 位置权限
        {
            "match": {"label": "允许“TikTok”使用您的大致位置？"},
            "popup_params": {"button_texts": ["使用App时允许"]}
        },
        {
            "match": {"label": "允许“TikTok”使用你的大致位置？"},
            "popup_params": {"button_texts": ["使用App时允许"]}
        },
        {
            "match": {"label": "允许“TikTok M”使用您的大致位置？"},
            "popup_params": {"button_texts": ["使用App时允许"]}
        },
        {
            "match": {"label": "允许“TikTok M”使用你的大致位置？"},
            "popup_params": {"button_texts": ["使用App时允许"]}
        },
        # TikTok 通知权限
        {
            "match": {"label": "“TikTok”想给您发送通知"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "“TikTok”想给你发送通知"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "允许“TikTok M”想给您发送通知"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "允许“TikTok M”想给你发送通知"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        # TikTok 跟踪权限
        {
            "match": {"label": "允许“TikTok”跟踪您在其他公司的App和网站上的活动吗？"},
            "popup_params": {"button_texts": ["允许"]}
        },
        {
            "match": {"label": "允许“TikTok”跟踪你在其他公司的App和网站上的活动吗？"},
            "popup_params": {"button_texts": ["允许"]}
        },
        {
            "match": {"label": "允许“TikTok M”跟踪您在其他公司的App和网站上的活动吗？"},
            "popup_params": {"button_texts": ["允许"]}
        },
        {
            "match": {"label": "允许“TikTok M”跟踪你在其他公司的App和网站上的活动吗？"},
            "popup_params": {"button_texts": ["允许"]}
        },
        # TikTok 通讯录权限
        {
            "match": {"label": "“TikTok”想访问您的通讯录"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "“TikTok”想访问你的通讯录"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "“TikTok M”想访问您的通讯录"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "“TikTok M”想访问你的通讯录"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        # TikTok 相机权限
        {
            "match": {"label": "“TikTok”想访问您的相机"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        {
            "match": {"label": "“TikTok”想访问你的相机"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        {
            "match": {"label": "“TikTok M”想访问您的相机"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        {
            "match": {"label": "“TikTok M”想访问你的相机"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        # TikTok 麦克风权限
        {
            "match": {"label": "“TikTok”想访问您的麦克风"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        {
            "match": {"label": "“TikTok”想访问你的麦克风"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        {
            "match": {"label": "“TikTok M”想访问您的麦克风"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        {
            "match": {"label": "“TikTok M”想访问你的麦克风"},
            "popup_params": {"button_texts": ["好", "允许"]}
        },
        # 其他系统弹窗
        {
            "match": {"label": "同步 Facebook 好友和电子邮件地址"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "保存登录信息以供下次使用？"},
            "popup_params": {"button_texts": ["暂时不要"]}
        },
        {
            "match": {"label": "直播屏幕"},
            "popup_params": {"button_texts": ["好"]}
        },
        {
            "match": {"label": ".*用于 iMessage 和 FaceTime"},
            "popup_params": {"button_texts": ["是", "否"]}
        },
        {
            "match": {"label": "提醒事项"},
            "popup_params": {"button_texts": ["忽略"]}
        },
        {
            "match": {"label": "私有地址"},
            "popup_params": {"button_texts": ["重新加入"]}
        },
        {
            "match": {"label": "允许“BDCBot”删除.*"},
            "popup_params": {"button_texts": ["删除"]}
        },
        {
            "match": {"label": "未受信任的企业级开发者"},
            "popup_params": {"button_texts": ["取消"]}
        },
        {
            "match": {"label": "App Update"},
            "popup_params": {"button_texts": ["Cancel"]}
        },
        {
            "match": {"label": "查找通讯录好友"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        {
            "match": {"label": "查找通讯录好友"},
            "popup_params": {"button_texts": ["确定"]}
        },
        {
            "match": {"label": "查找 Facebook 好友"},
            "popup_params": {"button_texts": ["不允许"]}
        },
        # TikTok 无线数据权限
        {
            "match": {"label": "允许“TikTok”使用无线数据？"},
            "popup_params": {"button_texts": ["无线局域网与蜂窝网络"]}
        },
        {
            "match": {"label": "允许“TikTok M”使用无线数据？"},
            "popup_params": {"button_texts": ["无线局域网与蜂窝网络"]}
        },
    ]


    def _init_linc_mic_client(self):
        return LinkMiciOS(self)
    
    def restart(self, delay=10):
        """
        重启应用, 重置 link_mic_client
        """
        super().restart()
        logger.debug(f"Restarting app, waiting {delay}s...")
        time.sleep(delay)
        # logger.debug("init link_mic_client...")
        # self.link_mic_client = self._init_linc_mic_client()

    def get_uid(self) -> str:
        """
        获取用户uid
        """
        ret: Dict = self.call_method(class_name="TTKUserLoginStub", method="userId")
        logger.debug(f"Get udid result: {ret}")
        return ret.get("value", "")

    def get_did(self):
        """获取设备did
        :return:
        """
        return self.get_device_did()

    def on_app_popup(self):
        """
        处理应用中的弹窗，以避免影响自动化流程的稳定性。

        该方法最多尝试处理 4 次弹窗：
        - 如果某次尝试中未检测到可处理的弹窗，则提前终止；
        - 如果检测到无法自动处理的弹窗（抛出 AppPopupNotHandled 异常），也会立即终止。

        注意事项：
        1. Android 平台上，大多数弹窗会被控件的 `wait_for_visible` 自动识别并触发处理逻辑；
        2. iOS 平台存在差异：即使有弹窗遮挡，控件的 `wait_for_visible` 仍可能返回 True,
        导致控件可见性判断失效，因此需要额外调用 `on_app_popup` 主动处理弹窗。
        """

        logger.debug("操作前先主动处理一波弹窗")
        
        for attempt in range(4):
            try:
                handled_popups = super().on_app_popup()
                if not handled_popups:
                    logger.debug(f"No pop-up handled on attempt {attempt + 1}")
                    break
                logger.debug(f"handled popups: {handled_popups}")
            except Exception as e:
                break
    
    def open_scheme(self, scheme: str):
        """
        打开指定的scheme，并在打开前处理可能弹出的干扰弹窗。

        Args:
            scheme (str): Scheme URL

        Returns:
            None
        """
        # Try to handle any interfering popups up to 3 times before opening the scheme
        for _ in range(3):
            handled_popups = self.on_app_popup()
            if not handled_popups:
                break  # No popup handled, proceed to open scheme

        logger.info(f"Open scheme URL: {scheme}")

        super().open_scheme(scheme)
        time.sleep(2)

    def login(
            self,
            phone: str,
            sm_code: str,
            region: int = 86,
            retries: int = 3,
        ) -> str:
        """
        使用ttmock进行登录，支持登录态检测与自动重试。 

        为了避免账号重复登录，该方法会先判断是否已处于登录状态，若未登录则执行登录流程。
        登录过程中会自动进行失败重试，最多尝试指定次数（默认为 3 次）。
        登录成功后返回当前登录用户的 UID，失败则抛出异常。

        Tips:
            1. 自动登录后,需要重启应用,否则登录态无法生效

        Args:
            phone (str): 登录手机号
            sm_code (str): 短信验证码
            region (int): 区号，默认 86（中国）
            retries (int): 登录最大重试次数（外层 Retry 循环）

        Returns:
            bool: 登录成功返回 True，否则返回 False
        """

        logger.info(f"login... {phone}/{sm_code}")
        attempt_count = 0

        for _ in Retry(limit=retries, raise_error=False):

            # Call login method
            self.call_method(
                class_name="TTKUserLoginStub",
                method="loginWithPhoneNumber:SMSCode:",
                args=[phone, sm_code]
            )

            # Notify login broadcast after login call
            self.call_method(
                class_name="TTKUserLoginStub",
                method="notifyLoginSuccessResult"
            )
            logger.info(f"等待登录是否完成, 15s")
            time.sleep(15)  # Wait for login to take effect

            # Restart for login to take effect
            self.restart()

            # Final check after retries, raise error if still not logged in
            if self.is_login():
                return True

            logger.warning(f'Attempt #{attempt_count + 1} to login: {phone}/{sm_code}')
            attempt_count += 1

        logger.error(f"Failed to auto login after {retries} attempts")
        return False

    def logout(self, retries=3, interval=5):
        """
        使用ttmock退出登录
        """
        logger.info("logout...")
        for _ in Retry(limit=retries, raise_error=False):
            self.call_method(class_name="TTKUserLoginStub", method="logout")

            time.sleep(interval)  # important 等待登出

            if not self.is_login():
                return True

            logger.warning("logout failed, try again...")

        return False

    def is_login(self) -> bool:
        """
        使用ttmock判断是否登录
        """
        result: Dict = self.call_method(class_name="TTKUserLoginStub", method="isLogin")
        logger.debug(f"is_login result: {result}")
        return result.get("value", 0) == 1


class SpringboardAccessibilityApp(AccessibilityApp):
    app_spec = {
        'bundle_ids': ['com.apple.springboard'],
        'kill_process': False
    }


class iOSSettingApp(CVAppMixin, iOSApp):
    app_spec = {
        "bundle_ids": ["com.apple.Preferences"],
        "kill_process": True,
    }
    system_popup_rules = [
        {
            "match": {"label": "“信任”将允许在iPhone上使用任何来自此企业级开发者的App并可能允许其访问您的数据。"},
            "popup_params": {"button_texts": ["信任"]}
        },
        {
            "match": {"label": "未受信任的企业级开发者"},
            "popup_params": {"button_texts": ["取消"]}
        },
    ]


class iOSFeiShuApp(CVAppMixin, iOSApp):
    app_spec = {
        "bundle_id": "com.bytedance.ee.lark",
        "kill_process": False,
    }
    system_popup_rules = [
        {
            "match": {"label": "“TikTok M”想给您发送通知"},
            "popup_params": {"button_texts": ["允许"]}
        },
        {
            "match": {"label": "“TikTok”想给您发送通知"},
            "popup_params": {"button_texts": ["允许"]}
        },
    ]
