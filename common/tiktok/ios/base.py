"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/common/tiktok/ios/base.py
Description: iOS 测试基类
"""

from typing import Dict
from shoots_ios.testcase import iOSTestBase
from utils.common.log_utils import logger

from common.tiktok.ios.app import iOSTikTokApp
from utils.control.ios_control import iOSControl


class TTiOSPerfAppTestBase(iOSTestBase):
    """iOS 测试基类"""

    def __init__(self, *args, **kwargs):
        super(TTiOSPerfAppTestBase, self).__init__(*args, **kwargs)

        self.ios_device = None
        self.ios_app = None
        self.ios_control = iOSControl()


    def _ios_init_device(self, device_conditions=None):
        """初始化 iOS 设备"""
        self.ios_device = self.acquire_device(device_conditions)
        return self.ios_device

    def _ios_init_app(self,
            device,
            version_ids=None,
            uid=None,
            env: Dict={},
            app_args=["-skipForceLogin", "-skipUserJourney", "-disableCodeCoverageUploader"]):
        """初始化 iOS 应用
        
        Args:
            device: 设备实例
            version_ids: 实验ids
            uid: 用户id
            env: APP启动环境变量
            app_args: APP启动参数
            
        """  
        logger.debug(f"launch app env: {env}")
        logger.debug(f"launch app args: {app_args}")

        if version_ids:
            self.ios_app = iOSTikTokApp(device, app_args=app_args, version_ids=version_ids, uid=uid)
            logger.debug(f"[{device.udid}] 开始配置Libra实验，实验: {version_ids}")
            self.ios_app.config_libra()
        else:
            self.ios_app = iOSTikTokApp(device, env=env, app_args=app_args)
            
        return self.ios_app

    def init_ios_env(self, udid=None, version_ids=None, uid=None):
        """初始化 iOS 环境"""

        logger.debug(f"[{udid}] 开始初始化uitest...")
        if not self.ios_control.ui_test_init(udid):
            raise Exception(f"[{udid}] iOS uitest环境初始化失败")

        logger.debug(f"[{udid}] 开始初始化设备...")
        device = self._ios_init_device({"udid": udid, "type": "iOS"})

        logger.debug(f"[{udid}] 开始初始化app...")
        app = self._ios_init_app(device, version_ids, uid)

        return device, app