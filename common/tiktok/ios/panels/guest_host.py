"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/ios/panels/guest_host.py
Description:
"""
import time

from uibase.controls import Window
from uibase.upath import UPath, id_, type_, text_, label_, visible_
from utils.common.log_utils import logger


class iOSGuestHostPanel(Window):
    """
    iOS嘉宾连麦面板
    """

    window_spec = {"path":UPath(~type_ == "AWEMaskWindow|AWELoginWindow|AWEZoomWindow|XCUIElementTypeApplication")}

    def get_locators(self):
        return {
            "申请连麦": {"path": UPath(label_ == "嘉宾")},
            "连接开关": {"path": UPath(id_ == "请求")},
            "嘉宾列表": {"path": UPath(id_ == "null", label_ == "邀请嘉宾")},
            "接受嘉宾": {"path": UPath(id_ == "接受", visible_ == True, index=0)},
            "相机_1": {"path": UPath(label_ == "相机")},
            "相机_2": {"path": UPath(text_ == "相机")},
            "保存": {"path": UPath(id_ == "保存")},
            "单个嘉宾标签": {"path": UPath(id_ == "optionView", visible_ == True)},
            "3 位嘉宾": {"path": UPath(id_ == "3 位嘉宾")},
            "结束直播": {
                "path": UPath(id_ == "live_mt_tool_close_new_new", type_ == "UIImageView")},
            "断开连麦按钮": {"path": UPath(text_ == "断开连线")},
        }

    def request_to_host(self):
        """
        嘉宾请求上麦
        """
        logger.info(f"[{self.device.udid}][连麦] 嘉宾开始申请连麦")

        # 点击申请连麦按钮
        if self["申请连麦"].wait_for_visible(timeout=5, raise_error=False):
            self["申请连麦"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击申请连麦按钮")
            time.sleep(2)

        # 点击连接开关
        if self["连接开关"].wait_for_visible(timeout=20, raise_error=False):
            self["连接开关"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击连接开关")

        logger.info(f"[{self.device.udid}][连麦] 嘉宾已发送连麦申请")

    def accept_guest_connect(self):
        """
        主播同意嘉宾上麦
        """
        logger.info(f"[{self.device.udid}][连麦] 主播开始同意嘉宾上麦")

        # 点击嘉宾列表
        if self["嘉宾列表"].wait_for_visible(timeout=5, raise_error=False):
            self["嘉宾列表"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击嘉宾列表")

        # 接受嘉宾连麦
        if self["接受嘉宾"].wait_for_visible(timeout=5, raise_error=False):
            self["接受嘉宾"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击接受嘉宾")

        logger.info(f"[{self.device.udid}][连麦] 主播已同意嘉宾上麦")

    def open_guest_camera(self):
        """
        嘉宾打开摄像头确认上麦
        """
        logger.info(f"[{self.device.udid}][连麦] 嘉宾开始确认上麦")
        time.sleep(10)

        # 点击相机按钮
        elements = [self["相机_1"], self["相机_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][连麦] 点击相机按钮")
                time.sleep(2)
                break

        # 点击保存按钮
        if self["保存"].wait_for_visible(timeout=5, raise_error=False):
            self["保存"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击保存按钮")

        logger.info(f"[{self.device.udid}][连麦] 嘉宾已确认上麦")

    def reject_guest_connect(self):
        """
        拒绝嘉宾连麦
        """
        logger.info(f"[{self.device.udid}][连麦] 开始拒绝嘉宾连麦")
        # TODO: 实现拒绝连麦逻辑
        logger.info(f"[{self.device.udid}][连麦] 已拒绝嘉宾连麦")

    def disconnect_guest_connect(self):
        """
        断开嘉宾连麦
        """
        logger.info(f"[{self.device.udid}][连麦] 开始断开嘉宾连麦")

        # 点击断开连麦按钮
        if self["断开连麦按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["断开连麦按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击断开连麦按钮")

        logger.info(f"[{self.device.udid}][连麦] 已断开嘉宾连麦")

    def assert_guest_connect_1v1(self):
        """
        断言嘉宾1V1连麦成功
        """
        time.sleep(5)
        success = self["单个嘉宾标签"].wait_for_visible(timeout=20, raise_error=False)
        logger.info(f"[{self.device.udid}][连麦] 断言嘉宾1V1连麦: {success}")
        return success

    def assert_guest_connect_1v3(self):
        """
        断言嘉宾1V3连麦成功
        """
        time.sleep(5)

        # 检查嘉宾列表按钮是否可见
        if not self["嘉宾列表"].wait_for_visible(timeout=5, raise_error=False):
            logger.info(f"[{self.device.udid}][连麦] 未检测到嘉宾列表按钮")
            return False

        # 点击嘉宾列表按钮
        self["嘉宾列表"].click()
        logger.info(f"[{self.device.udid}][连麦] 点击嘉宾列表")

        # 检查是否有3位嘉宾
        has_three_guests = self["3 位嘉宾"].wait_for_visible(timeout=5, raise_error=False)
        if has_three_guests:
            logger.info(f"[{self.device.udid}][连麦] 检测到3位嘉宾")
        else:
            logger.info(f"[{self.device.udid}][连麦] 未检测到3位嘉宾")

        # 尝试退出嘉宾列表
        for attempt in range(3):
            if self["结束直播"].wait_for_visible(timeout=5, raise_error=False):
                self.device.click(*self["结束直播"].rect.center)
                if self["结束直播"].clickable:
                    logger.info(f"[{self.device.udid}][连麦] 成功退出嘉宾列表")
                    break
            logger.info(f"[{self.device.udid}][连麦] 退出嘉宾列表尝试 {attempt+1} 失败")

        logger.info(f"[{self.device.udid}][连麦] 断言嘉宾1V3连麦: {has_three_guests}")
        return has_three_guests

    def assert_guest_disconnect(self):
        """
        断言嘉宾连麦断开
        """
        success = not self["单个嘉宾标签"].wait_for_visible(timeout=5, raise_error=False)
        logger.info(f"[{self.device.udid}][连麦] 断言嘉宾连麦断开: {success}")
        return success



