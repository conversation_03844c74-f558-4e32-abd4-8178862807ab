"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/ios/panels/login.py
Description: 登录相关页面操作
"""
import time

from uibase.controls import Window
from uibase.upath import UPath, id_, type_, visible_, text_
from shoots_cv.controls import CVWindow
from shoots_cv.upath import ocr_
from common.tiktok.ios.popups import handle_sys_popups
from utils.common.log_utils import logger


class iOSLoginPanel(Window, CVWindow):
    """
    iOS登录面板
    """
    window_spec = {"path":UPath(~type_ == "AWEMaskWindow|AWELoginWindow|AWEZoomWindow|XCUIElementTypeApplication")}

    def get_locators(self):
        return {
            # AWELoginWindow 相关元素
            "底部已有账号？登录": {"path": UPath(id_ == "loginView")},
            "登录TikTok": {"path": UPath(id_ == "登录 TikTok")},
            "手机号保存登录视图": {"path": UPath(id_ == "phoneSaveLoginView")},
            "关闭面板按钮": {"path": UPath(id_ == "icon_close_panel", type_ == "UIImageView")},
            "添加其它账号": {"path": UPath(text_ == "添加其它账号")},
            "使用手机邮箱用户名1": {"path": UPath(text_ == "使用手机 / 邮箱 / 用户名")},
            "使用手机邮箱用户名2": {"path": UPath(id_ == "phoneEmailButton") / 2},
            "使用手机邮箱用户名3": {"path": UPath(id_ == "iconPhoneEmail")},
            "手机号输入框1": {"path": UPath(id_ == "placeholderLabel", visible_ == True)},
            "手机号输入框2": {"path": UPath(id_ == "输入手机号码")},
            "发送验证码按钮1": {"path": UPath(id_ == "phoneLoginButton")},
            "发送验证码按钮2": {"path": UPath(type_ == "TMLoginButton")},
            "验证码输入框": {"path": UPath(id_ == "verificationCodeInputView")},
            "code_input": {"path": UPath(type_ == "TMVerificationCodeInputView")},
            "登录错误提示": {"path": UPath(id_ == "errorHintLabel")},

            # AWEZoomWindow 相关元素
            "btn_id_skip": {"path": UPath(id_ == "跳过", type_ == "TUXButton")},
            "btn_start_watching": {"path": UPath(text_ == "开始观看")},

            # ShootsCvWindow 相关元素
            '飞书内部员工验证': {'path': UPath(ocr_ == "重试")},
            '飞书确认授权': {'path': UPath(ocr_ == "确认授权")},
            '选择感兴趣内容': {'path': UPath(ocr_ == "选择你感兴趣的内容")},
            '添加其他账号': {'path': UPath(ocr_ == "添加其他账号")},
            '登录TikTok_OCR': {'path': UPath(ocr_ == "登录 TikTok")},
            '主页': {'path': UPath(ocr_ == "主页")},
            '登录': {'path': UPath(ocr_ == "已经有账号?登录")},
            '同意并继续': {'path': UPath(ocr_ == "同意并继续")},
            '以后再说': {'path': UPath(ocr_ == "以后再说")},
            '关闭': {'path': UPath(ocr_ == "关")},
            '打开': {'path': UPath(ocr_ == "开")},
            '网络设置': {'path': UPath(ocr_ == "网络设置")},
            '当前服务器': {'path': UPath(ocr_ == "当前服务器")},
            '服务器1': {'path': UPath(ocr_ == "US-MAL-VA1")},
            '服务器2': {'path': UPath(ocr_ == "US-MAL-VA2")},
            '确认': {'path': UPath(ocr_ == "确认")},
            '通用': {'path': UPath(ocr_ == "通用")},
            'VPN管理': {'path': UPath(ocr_ == "VPN与设备管理")},
            '不受信任': {'path': UPath(~ocr_ == "不受信任|未受信任")},
            '信任抖音': {'path': UPath(ocr_ == '信任"TikTok Inc."')},
            '信任': {'path': UPath(ocr_ == "信任")},

            # AWEMaskWindow 相关元素（登录用）
            "主页_MASK": {"path": UPath(id_ == "主页")},
            "头像": {"path": UPath(id_ == "avatar_normal")},
            "上滑查看更多视频": {"path": UPath(id_ == "mainLabel")},
            "账号名称_1": {"path": UPath(id_ == "user_info_nickname") / UPath(id_ == "titleLabel", depth=5)},
            "账号名称_2": {"path": UPath(id_ == "titleLabel", type_ == "TUXLabel")},
            "添加账号": {"path": UPath(text_ == "添加账号")},
        }

    # AWELoginWindow 相关方法
    def click_bottom_has_account_login(self):
        """点击底部已有账号登录按钮"""
        if self["底部已有账号？登录"].wait_for_visible(timeout=10, raise_error=False):
            self["底部已有账号？登录"].click()
            logger.info(f"[{self.device.udid}][登录] 点击底部已有账号登录按钮")

    def is_login_tiktok_page(self):
        """检测是否在登录TikTok页面"""
        if self["登录TikTok"].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 检测到登录TikTok页面")
            return True
        return False

    def click_btn_phone_save_login_view(self):
        """点击手机号保存登录视图"""
        if self["手机号保存登录视图"].wait_for_visible(timeout=10, raise_error=False):
            self["手机号保存登录视图"].click()
            logger.info(f"[{self.device.udid}][登录] 点击手机号保存登录视图")

    def click_btn_icon_close_panel(self):
        """点击关闭面板按钮"""
        if self["关闭面板按钮"].wait_for_visible(timeout=10, raise_error=False):
            self["关闭面板按钮"].click()
            logger.info(f"[{self.device.udid}][登录] 点击关闭面板按钮")

    def click_btn_use_phone_email_username(self):
        """点击使用手机/邮箱/用户名按钮"""
        elements = [self["使用手机邮箱用户名1"], self["使用手机邮箱用户名2"], self["使用手机邮箱用户名3"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][登录] 点击使用手机/邮箱/用户名按钮")
                break

    def click_btn_add_other_account(self):
        """点击添加其它账号按钮"""
        if self["添加其它账号"].wait_for_visible(timeout=10, raise_error=False):
            self["添加其它账号"].click()
            logger.info(f"[{self.device.udid}][登录] 点击添加其它账号按钮")

    def input_iphone_number(self, iphone_number):
        """输入手机号"""
        elements = [self["手机号输入框1"], self["手机号输入框2"]]
        for element in elements:
            if element.wait_for_visible(timeout=10, raise_error=False):
                element.send_keys(iphone_number)
                logger.info(f"[{self.device.udid}][登录] 输入手机号: {iphone_number}")
                break

    def click_btn_send_captcha(self):
        """点击发送验证码按钮"""
        elements = [self["发送验证码按钮1"], self["发送验证码按钮2"]]

        def try_click_captcha():
            for element in elements:
                if element.wait_for_visible(timeout=10, raise_error=False):
                    element.click()
                    logger.info(f"[{self.device.udid}][登录] 点击发送验证码按钮")
                    return True
            return False

        # 第一次尝试点击
        if not try_click_captcha():
            return

        # 如果出现错误提示,重试一次
        if self["登录错误提示"].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 登录错误提示已出现，再次点击发送验证码按钮")
            try_click_captcha()

    def input_captcha(self, captcha):
        """输入验证码"""
        codes = [self["验证码输入框"], self["code_input"]]
        for code in codes:
            if code.wait_for_visible(timeout=10, raise_error=False):
                code.send_keys(captcha)
                logger.info(f"[{self.device.udid}][登录] 输入验证码: {captcha}")
                break

    # AWEZoomWindow 相关方法
    def click_btn_id_skip(self):
        """点击跳过按钮"""
        if self["btn_id_skip"].wait_for_visible(timeout=10, raise_error=False):
            self["btn_id_skip"].click()
            logger.info(f"[{self.device.udid}][登录] 点击跳过按钮")

    def click_btn_start_watching(self):
        """点击开始观看按钮"""
        if self["btn_start_watching"].wait_for_visible(timeout=10, raise_error=False):
            self["btn_start_watching"].click()
            logger.info(f"[{self.device.udid}][登录] 点击开始观看按钮")

    # ShootsCvWindow 相关方法
    def click_feishu_internal_staff_verification(self):
        """点击飞书内部员工验证"""
        if self['飞书内部员工验证'].wait_for_visible(timeout=10, raise_error=False):
            self['飞书内部员工验证'].click()
            logger.info(f"[{self.device.udid}][登录] 点击飞书内部员工验证")
            return True
        return False

    def click_feishu_confirm_the_authorization(self):
        """点击飞书确认授权"""
        if self['飞书确认授权'].wait_for_visible(timeout=10, raise_error=False):
            self['飞书确认授权'].click()
            logger.info(f"[{self.device.udid}][登录] 点击飞书确认授权")

    def is_exist_tt_ocr_select_what_interests_you(self):
        """检测是否存在选择感兴趣内容"""
        if self['选择感兴趣内容'].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 检测到选择感兴趣内容")
            return True
        return False

    def is_exist_tt_ocr_add_another_account(self):
        """检测是否存在添加其他账号"""
        if self['添加其他账号'].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 检测到添加其他账号")
            return True
        return False

    def is_exist_tt_ocr_login_tiktok(self):
        """检测是否存在登录TikTok"""
        if self['登录TikTok_OCR'].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 检测到登录TikTok")
            return True
        return False

    def is_exist_tt_ocr_home_page(self):
        """检测是否存在主页"""
        if self['主页'].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 检测到主页")
            return True
        return False

    def click_tt_ocr_login(self):
        """点击登录"""
        if self['登录'].wait_for_visible(timeout=10, raise_error=False):
            self['登录'].click()
            logger.info(f"[{self.device.udid}][登录] 点击登录")

    def click_feilian_ocr_more_on_that_later(self):
        """点击以后再说"""
        if self['以后再说'].wait_for_visible(timeout=10, raise_error=False):
            self['以后再说'].click()
            logger.info(f"[{self.device.udid}][登录] 点击以后再说")

    def is_exist_feilian_ocr_close(self):
        """检测是否存在关闭"""
        if self['关闭'].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 检测到关闭")
            return True
        return False

    def click_feilian_ocr_close(self):
        """点击关闭"""
        if self['关闭'].wait_for_visible(timeout=10, raise_error=False):
            self['关闭'].click()
            logger.info(f"[{self.device.udid}][登录] 点击关闭")

    def is_exist_feilian_ocr_open(self):
        """检测是否存在打开"""
        if self['打开'].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][登录] 检测到打开")
            return True
        return False

    def click_feilian_ocr_open(self):
        """点击打开"""
        if self['打开'].wait_for_visible(timeout=10, raise_error=False):
            self['打开'].click()
            logger.info(f"[{self.device.udid}][登录] 点击打开")

    def click_feilian_ocr_network_settingss(self):
        """点击网络设置"""
        if self['网络设置'].wait_for_visible(timeout=10, raise_error=False):
            self['网络设置'].click()
            logger.info(f"[{self.device.udid}][登录] 点击网络设置")

    def click_feilian_ocr_the_current_server(self):
        """点击当前服务器"""
        if self['当前服务器'].wait_for_visible(timeout=10, raise_error=False):
            self['当前服务器'].click()
            logger.info(f"[{self.device.udid}][登录] 点击当前服务器")

    def click_feilian_ocr_server(self):
        """点击服务器"""
        servers = ['服务器1', '服务器2']
        for _ in range(5):
            for server in servers:
                if self[server].wait_for_visible(timeout=10, raise_error=False):
                    self[server].click()
                    logger.info(f"[{self.device.udid}][登录] 点击{server}")
                    return
            self.device.drag(100.0, 600.0, 100.0, 100.0, 2.0, 0.5)

    def click_feilian_ocr_confirm(self):
        """点击确认"""
        if self['确认'].wait_for_visible(timeout=10, raise_error=False):
            self['确认'].click()
            logger.info(f"[{self.device.udid}][登录] 点击确认")

    def click_setting_ocr_general(self):
        """点击通用"""
        for _ in range(3):
            self.device.drag(100.0, 600.0, 100.0, 100.0, 2.0, 0.5)
            if self['通用'].wait_for_visible(timeout=10, raise_error=False):
                self['通用'].click()
                logger.info(f"[{self.device.udid}][登录] 点击通用")
                return

    def click_setting_ocr_vpn_device_management(self):
        """点击VPN管理"""
        [self.device.drag(100.0, 600.0, 100.0, 100.0, 2.0, 0.5) for _ in range(2)]
        if self['VPN管理'].wait_for_visible(timeout=10, raise_error=False):
            self['VPN管理'].click()
            logger.info(f"[{self.device.udid}][登录] 点击VPN管理")
            return

    def click_setting_ocr_not_trusted(self):
        """点击不受信任"""
        if self['不受信任'].wait_for_visible(timeout=10, raise_error=False):
            self['不受信任'].click()
            logger.info(f"[{self.device.udid}][登录] 点击不受信任")
            return True
        return False

    def click_setting_ocr_trust_tiktok_inc(self):
        """点击信任抖音"""
        if self['信任抖音'].wait_for_visible(timeout=10, raise_error=False):
            self['信任抖音'].click()
            logger.info(f"[{self.device.udid}][登录] 点击信任抖音")

    def click_setting_ocr_trust(self):
        """点击信任"""
        if self['信任'].wait_for_visible(timeout=10, raise_error=False):
            self['信任'].click()
            logger.info(f"[{self.device.udid}][登录] 点击信任")
            return True
        return False

    # AWEMaskWindow 相关方法（登录用）
    def swipe_up_to_view_more_videos(self):
        """上滑查看更多视频"""
        if self["上滑查看更多视频"].wait_for_visible(timeout=5, raise_error=False):
            [self.device.drag(100.0, 600.0, 100.0, 100.0, 2.0, 0.5) for _ in range(2)]
            logger.info(f"[{self.device.udid}][登录] 上滑查看更多视频")

    def click_btn_main_bottom_button_me(self):
        """点击主页按钮"""
        if self["主页_MASK"].wait_for_visible(timeout=5, raise_error=False):
            self["主页_MASK"].click()
            logger.info(f"[{self.device.udid}][登录] 点击主页按钮")

    def is_true_btn_nav_bar_title(self, nick):
        """检查账号名称是否正确"""
        nick_names = []
        elements = [self["账号名称_1"], self["账号名称_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                nick_name = element.text
                nick_names.append(nick_name)
                logger.info(f"[{self.device.udid}][登录] 获取账号名称: {nick_name}")
        if nick in nick_names or "@" + nick.lower() in nick_names:
            logger.info(f"[{self.device.udid}][登录] 检测到账号名称")
            return True
        return False

    def click_btn_nav_bar_title(self):
        """点击账号名称"""
        elements = [self["账号名称_1"], self["账号名称_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][登录] 点击账号名称")
                break

    def click_btn_add_account(self):
        """点击添加账号"""
        if self["添加账号"].wait_for_visible(timeout=5, raise_error=False):
            self["添加账号"].click()
            logger.info(f"[{self.device.udid}][登录] 点击添加账号")



    def auto_login(self, account, code):
        """
        自动登录方法
        Args:
            account: 手机号
            code: 验证码
        Returns:
            bool: 登录是否成功
        """
        try:
            logger.info(f"[{self.device.udid}][登录] 开始自动登录")

            # 调用登录接口
            self.root.call_method(class_name="TTKUserLoginStub", method="loginWithUserName:password:",args=[account, code])
            time.sleep(5)

            # 验证登录结果
            return self.root.call_method(class_name="TTKUserLoginStub", method="isLogin")["value"]

        except Exception as e:
            logger.error(f"[{self.device.udid}][登录] 登录异常:{str(e)}")
            return False

    def is_account_login_correct(self, nick):
        """
        判断登录的账号是否正确
        Args:
            nick: 用户昵称
        Returns:
            bool: 账号是否正确
        """
        try:
            if self.is_exist_tt_ocr_home_page():
                self.swipe_up_to_view_more_videos()
                self.click_btn_main_bottom_button_me()
                if self.is_true_btn_nav_bar_title(nick):
                    logger.info(f"[{self.device.udid}][登录] 用户[{nick}]已登录")
                    return True
                logger.info(f"[{self.device.udid}][登录] 当前登录账号与目标账号[{nick}]不匹配")
                return False
            logger.info(f"[{self.device.udid}][登录] 没有检测到首页")
            return False
        except Exception as e:
            logger.error(f"[{self.device.udid}][登录] 检查账号是否正确异常:{str(e)}")
            return False

    def add_account(self):
        """
        添加账号
        """
        try:
            self.click_btn_nav_bar_title()
            self.click_btn_add_account()
            logger.info(f"[{self.device.udid}][登录] 点击添加账号")
        except Exception as e:
            logger.error(f"[{self.device.udid}][登录] 添加账号异常:{str(e)}")

    def login_by_phone_code(self, account, code, nick):
        """iOS 用户验证码登录
        Args:
            account: 账号
            code: 验证码
            nick: 昵称
        """
        logger.info(f"[{self.device.udid}][登录] 开始验证码登录")
        time.sleep(5)
        handle_sys_popups(self.root)

        # 检查是否已经登录正确账号
        if self.is_account_login_correct(nick):
            logger.info(f"[{self.device.udid}][登录] 用户[{nick}]已登录")
            return

        # 处理飞书内部员工验证
        if self.click_feishu_internal_staff_verification():
            self.click_feishu_confirm_the_authorization()

        # 处理新用户引导
        if self.is_exist_tt_ocr_select_what_interests_you():
            logger.info(f"[{self.device.udid}][登录] 处理新用户引导流程")
            self.click_btn_id_skip()
            self.click_btn_start_watching()
            self.swipe_up_to_view_more_videos()

        # 处理添加其他账号
        if self.is_exist_tt_ocr_add_another_account():
            self.add_account()

        # 处理登录TikTok页面
        if self.is_exist_tt_ocr_login_tiktok():
            self.click_tt_ocr_login()

        # 处理登录流程
        if self.is_login_tiktok_page():
            self.click_btn_use_phone_email_username()
            self.input_iphone_number(account)
            self.click_btn_send_captcha()
            self.input_captcha(code)

        logger.info(f"[{self.device.udid}][登录] 验证码登录完成")

    def is_login_success(self) -> bool:
        """
        判断登录是否成功
        """
        self.app.open_scheme("snssdk1180://feed")
        if self.is_exist_tt_ocr_home_page():
            logger.info(f"[{self.device.udid}][登录] 登录成功")
            return True
        logger.warning(f"[{self.device.udid}][登录] 登录失败")
        return False

    @staticmethod
    def trust_tiktok_app(device):
        """
        信任TikTok应用
        """
        from common.tiktok.ios.app import iOSSettingApp

        logger.info(f"[{device.udid}][设置] 开始信任TikTok应用")
        setting_app = iOSSettingApp(device)
        login_panel = iOSLoginPanel(root=setting_app)
        login_panel.click_setting_ocr_general()
        login_panel.click_setting_ocr_vpn_device_management()
        login_panel.click_setting_ocr_not_trusted()
        login_panel.click_setting_ocr_trust_tiktok_inc()
        login_panel.click_setting_ocr_trust()
        time.sleep(3)
        setting_app.stop()
        logger.info(f"[{device.udid}][设置] 完成TikTok应用信任设置")

