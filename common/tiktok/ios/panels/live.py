"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/ios/panels/live.py
Description: 直播相关页面操作
"""
import time
from uibase.controls import Window
from uibase.upath import UPath, id_, type_, visible_, text_, label_
from utils.common.log_utils import logger


class iOSLivePanel(Window):
    """
    iOS直播面板
    """
    window_spec = {"path":UPath(~type_ == "AWEMaskWindow|AWELoginWindow|AWEZoomWindow|XCUIElementTypeApplication")}

    def get_locators(self):
        return {
            "首页": {"path": UPath(id_ == "首页")},
            "底部添加按钮": {"path": UPath(id_ == "create_inverse", type_ == "UIImageView")},
            "我知道并同意": {"path": UPath(text_ == "I Know & Agree")},
            "继续按钮": {"path": UPath(label_ == "继续")},
            "模板标签": {"path": UPath(text_ == "模板")},
            "直播工作室指南": {"path": UPath(text_ == "直播工作室指南")},
            "取消直播工作室指南": {"path": UPath(type_ == "ACCStickerContainerView", visible_ == True) / 1},
            "直播标签": {"path": UPath(text_ == "直播")},
            "直播内容确认": {"path": UPath(text_ == "OK")},
            "取消直播": {"path": UPath(text_ == "取消")},
            "开启直播": {"path": UPath(label_ == "开启直播")},
            "视频标签": {"path": UPath(id_ == "videoButton")},
            "手机游戏标签": {"path": UPath(id_ == "手机游戏")},
            "游戏列表第一个游戏": {"path": UPath(id_ == "tableView", visible_ == True) / 1 / UPath(id_ == "null", visible_ == True, depth=5)},
            "游戏直播界面": {"path": UPath(id_ == "descriptionLabel")},
            "结束直播": {
                "path": UPath(id_ == "live_mt_tool_close_new_new", type_ == "UIImageView")},
            "立即结束": {"path": UPath(text_ == "立即结束")},
            "主页搜索": {
                "path": UPath(type_ == "TTKSearchEntranceButton") / UPath(type_ == "UIImageView", depth=5)},
            "搜索文本输入": {"path": UPath(type_ == "UISearchBarTextFieldLabel")},
            "搜索按钮": {"path": UPath(type_ == "UIButtonLabel", visible_ == True)},
            "用户标签": {"path": UPath(id_ == "contentView", text_ == "用户")},
            "头像进入直播间": {"path": UPath(id_ == "直播")},
            "关闭连接": {"path": UPath(id_ == "IconXMark") / UPath(type_ == "UIImageView", depth=5)},
            "退出确认": {"path": UPath(text_ == "确认")},
            "开始直播按钮": {"path": UPath(label_ == "开始直播")},
            "直播预览页美颜按钮": {"path": UPath(text_ == "美颜", visible_ == True)},
            "直播预览页增强按钮": {"path": UPath(text_ == "增强")},
            "直播预览页增强美颜状态": {"path": UPath(id_ == "beautyTUXSwitch")},
            "直播预览页增强美颜开关": {"path": UPath(id_ == "switchLabel")},
        }

    def click_start_live_broadcast(self):
        """
        点击开始直播按钮
        """
        if self["开始直播按钮"].wait_for_visible(timeout=10, raise_error=False):
            self["开始直播按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击开始直播按钮")

    def enter_prepare_live_page(self):
        """
        进入直播准备页面
        """
        logger.info(f"[{self.device.udid}][直播] 进入直播准备页面")

        # 点击底部添加按钮
        if self["底部添加按钮"].wait_for_visible(timeout=10, raise_error=False):
            self["底部添加按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击底部添加按钮")

        # 点击我知道并同意
        if self["我知道并同意"].wait_for_visible(timeout=5, raise_error=False):
            self["我知道并同意"].click()
            logger.info(f"[{self.device.udid}][直播] 点击我知道并同意")

        # 点击继续按钮
        if self["继续按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["继续按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击继续按钮")

        # 处理直播工作室指南
        if self["直播工作室指南"].wait_for_visible(timeout=5, raise_error=False):
            if self["取消直播工作室指南"].wait_for_visible(timeout=5, raise_error=False):
                self["取消直播工作室指南"].click()
                logger.info(f"[{self.device.udid}][直播] 取消直播工作室指南")

        if self["模板标签"].wait_for_visible(timeout=5, raise_error=False):
            self["模板标签"].click()
            logger.info(f"[{self.device.udid}][直播] 点击模板标签")

        if self["直播标签"].wait_for_visible(timeout=5, raise_error=False):
            self["直播标签"].click()
            logger.info(f"[{self.device.udid}][直播] 点击直播标签")

        if self["取消直播"].wait_for_visible(timeout=5, raise_error=False):
            self["取消直播"].click()
            logger.info(f"[{self.device.udid}][游戏直播] 取消直播")

    def open_beaty(self):
        """
        开启美颜
        Args:
            enable (bool): 是否开启美颜
        """
        if self["直播预览页美颜按钮"].wait_for_visible(timeout=5, raise_error=False):
            logger.info(f"[{self.device.udid}][直播] 美颜已开启")
            return
        if self["直播预览页增强按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["直播预览页增强按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击增强按钮")
        if self["直播预览页增强美颜状态"].wait_for_visible(timeout=5, raise_error=False):
            if self["直播预览页增强美颜状态"].text == "关闭":
                self["直播预览页增强美颜开关"].click()
                logger.info(f"[{self.device.udid}][直播] 开启增强美颜")

        self.app.get_device().back()
        logger.info(f"[{self.device.udid}][直播] 返回到直播预览页")

    def _click_start_live_button(self):
        """
        点击开始直播按钮的通用方法
        """
        if self["取消直播"].wait_for_visible(timeout=5, raise_error=False):
            self["取消直播"].click()
            logger.info(f"[{self.device.udid}][直播] 取消直播")
        # 开启直播
        if self["开启直播"].wait_for_visible(timeout=5, raise_error=False):
            self["开启直播"].click()
            logger.info(f"[{self.device.udid}][直播] 点击开启直播")

    def _handle_game_live_flow(self):
        """
        处理游戏直播特殊流程
        """
        time.sleep(3)

        # 处理系统权限
        device = self.root.get_device()
        if device.get_current_process() in ['com.zhiliaoapp.musically', 'com.zhiliaoapp.musically.ep']:
            logger.info(f"[{self.device.udid}][游戏直播] 选择游戏列表第一个游戏")
            if self["游戏列表第一个"].wait_for_visible(timeout=5, raise_error=False):
                self["游戏列表第一个"].click()
                logger.info(f"[{self.device.udid}][游戏直播] 点击游戏列表第一个")
            time.sleep(3)
        elif device.get_current_process() == 'com.apple.springboard':
            logger.info(f"[{self.device.udid}][游戏直播] 处理系统权限")
            self.click_start_live_broadcast()

    def camera_live(self, enable_beauty: bool = False):
        """
        相机直播
        Args:
            enable_beauty (bool): 是否开启美颜
        """
        logger.info(f"[{self.device.udid}][直播] 开始相机直播， 美颜: {enable_beauty}")

        # 进入直播准备页面
        self.enter_prepare_live_page()

        # 确认直播内容
        if self["直播内容确认"].wait_for_visible(timeout=5, raise_error=False):
            self["直播内容确认"].click()
            logger.info(f"[{self.device.udid}][直播] 确认直播内容")

        # 处理取消直播和视频标签
        if self["取消直播"].wait_for_visible(timeout=5, raise_error=False):
            self["取消直播"].click()
            logger.info(f"[{self.device.udid}][直播] 取消直播")
        if self["视频标签"].wait_for_visible(timeout=5, raise_error=False):
            self["视频标签"].click()
            logger.info(f"[{self.device.udid}][直播] 点击视频标签")

        # 设置美颜
        if enable_beauty:
            self.open_beaty()

        # 开始直播
        self._click_start_live_button()

        logger.info(f"[{self.device.udid}][直播] 相机直播启动完成")

    def game_live(self):
        """
        游戏直播
        """
        logger.info(f"[{self.device.udid}][游戏直播] 开始游戏直播")

        # 进入直播准备页面
        self.enter_prepare_live_page()

        # 切换到游戏直播
        if self["手机游戏标签"].wait_for_visible(timeout=5, raise_error=False):
            self["手机游戏标签"].click()
            logger.info(f"[{self.device.udid}][游戏直播] 点击手机游戏标签")

        # 选择游戏
        if self["游戏列表第一个游戏"].wait_for_visible(timeout=5, raise_error=False):
            self["游戏列表第一个游戏"].click()
            logger.info(f"[{self.device.udid}][游戏直播] 选择游戏列表第一个游戏")

        # 开始直播
        self._click_start_live_button()

        # 处理游戏直播特殊流程
        self._handle_game_live_flow()
        logger.info(f"[{self.device.udid}][游戏直播] 游戏直播启动完成")

    def close_live_stream(self):
        """
        关闭直播
        """
        logger.info(f"[{self.device.udid}][直播] 开始关闭直播")

        # 点击结束直播
        if self["结束直播"].wait_for_visible(timeout=5, raise_error=False):
            self["结束直播"].click()
            logger.info(f"[{self.device.udid}][直播] 点击结束直播")

        # 立即结束
        if self["立即结束"].wait_for_visible(timeout=5, raise_error=False):
            self["立即结束"].click()
            logger.info(f"[{self.device.udid}][直播] 点击立即结束")

        logger.info(f"[{self.device.udid}][直播] 直播已关闭")

    def search_and_enter_live_room(self, anchor_name: str):
        """
        搜索并进入直播间
        """
        logger.info(f"[{self.device.udid}][直播] 搜索并进入 {anchor_name} 的直播间")

        # 点击主页搜索
        if self["主页搜索"].wait_for_visible(timeout=5, raise_error=False):
            self["主页搜索"].click()
            logger.info(f"[{self.device.udid}][直播] 点击主页搜索")

        # 输入搜索文本
        if self["搜索文本输入"].wait_for_visible(timeout=5, raise_error=False):
            self["搜索文本输入"].input(anchor_name)
            logger.info(f"[{self.device.udid}][直播] 输入搜索文本: {anchor_name}")

        # 搜索并进入直播间
        for _ in range(5):
            if self["搜索按钮"].wait_for_visible(timeout=5, raise_error=False):
                self["搜索按钮"].click()
                logger.info(f"[{self.device.udid}][直播] 点击搜索按钮")
            if self["用户标签"].wait_for_visible(timeout=5, raise_error=False):
                self["用户标签"].click()
                logger.info(f"[{self.device.udid}][直播] 点击用户标签")
            if self["头像进入直播间"].wait_for_visible(timeout=5, raise_error=False):
                self["头像进入直播间"].click()
                logger.info(f"[{self.device.udid}][直播] 点击头像进入直播间")
                return
            if self["搜索文本输入"].wait_for_visible(timeout=5, raise_error=False):
                self["搜索文本输入"].click()
                logger.info(f"[{self.device.udid}][直播] 点击搜索文本输入")

    def exit_live_room(self):
        """
        退出直播间
        """
        logger.info(f"[{self.device.udid}][直播] 开始退出直播间")

        # 点击关闭连接
        if self["关闭连接"].wait_for_visible(timeout=5, raise_error=False):
            self["关闭连接"].click()
            logger.info(f"[{self.device.udid}][直播] 点击关闭连接")

        # 确认退出
        if self["退出确认"].wait_for_visible(timeout=5, raise_error=False):
            self["退出确认"].click()
            logger.info(f"[{self.device.udid}][直播] 确认退出")

        logger.info(f"[{self.device.udid}][直播] 已退出直播间")

    def assert_live_broadcast_success(self):
        """
        断言直播成功
        """
        time.sleep(5)
        success = self["结束直播"].wait_for_visible(timeout=15, raise_error=False)
        logger.info(f"[{self.device.udid}][直播] 断言直播成功: {success}")
        return success

    def assert_game_live_broadcast_success(self):
        """
        断言游戏直播成功
        """
        time.sleep(5)
        success = self["游戏直播界面"].wait_for_visible(timeout=20, raise_error=False)
        logger.info(f"[{self.device.udid}][游戏直播] 断言游戏直播成功: {success}")
        return success

    def assert_audience_enter_anchor_room(self):
        """
        断言观众进入主播直播间
        """
        # TODO: 实现观众进入主播直播间断言
        return True


