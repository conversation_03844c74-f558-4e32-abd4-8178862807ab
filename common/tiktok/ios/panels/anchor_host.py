"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/ios/panels/anchor_host.py
Description:
"""
import time

from uibase.controls import Window
from uibase.upath import UPath, id_, type_, text_, label_
from utils.common.log_utils import logger


class iOSAnchorHostPanel(Window):
    """
    iOS主播连麦面板
    """

    window_spec = {"path":UPath(~type_ == "AWEMaskWindow|AWELoginWindow|AWEZoomWindow|XCUIElementTypeApplication")}

    def get_locators(self):
        return {
            "连接主播_1": {"path": UPath(text_ == "+主播")},
            "连接主播_2": {"path": UPath(id_ == "null", label_ == "直播连麦")},
            "连麦更多按钮": {"path": UPath(id_ == "IconEllipsisHorizontalFill") / UPath(type_ == "UIImageView", depth=5)},
            "搜索主播_1": {"path": UPath(id_ == "rightBarButtonItem") / 0},
            "搜索主播_2": {"path": UPath(id_ == "搜索")},
            "搜索主播名输入框": {"path": UPath(id_ == "placeholderLabel")},
            "搜索指定主播": {"path": UPath(id_ == "searchLabel")},
            "邀请指定主播_1": {
                "path": UPath(id_ == "collectionView") / 0 / UPath(id_ == "邀请", depth=5)},
            "邀请指定主播_2": {"path": UPath(id_ == "searchResultView") / UPath(id_ == "collectionView", depth=5) / 0 / UPath(id_ == "邀请", depth=5)},
            "接受邀请按钮_1": {"path": UPath(id_ == "acceptButton")},
            "接受邀请按钮_2": {"path": UPath(id_ == "接受", type_ == "UIButton")},
            "主播显示ID": {"path": UPath(type_ == "GBLCoHostProfileView")},
            "断开主播连麦按钮": {"path": UPath(text_ == "断开连线")},
        }

    def invite_anchor_connect(self, anchor_name):
        """
        邀请主播连麦
        """
        logger.info(f"[{self.device.udid}][连麦] 开始邀请主播 {anchor_name} 连麦")

        # 点击连接主播按钮
        elements = [self["连接主播_1"], self["连接主播_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][连麦] 点击连接主播按钮")
                time.sleep(2)
                break

        # 点击连麦更多按钮
        if self["连麦更多按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["连麦更多按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击连麦更多按钮")

        # 点击搜索主播按钮
        elements = [self["搜索主播_1"], self["搜索主播_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][连麦] 点击搜索主播")
                break

        # 输入主播名称
        if self["搜索主播名输入框"].wait_for_visible(timeout=5, raise_error=False):
            self["搜索主播名输入框"].input(anchor_name)
            logger.info(f"[{self.device.udid}][连麦] 输入主播名称: {anchor_name}")

        # 搜索并邀请指定主播
        time.sleep(6)
        for _ in range(5):
            if self["搜索指定主播"].wait_for_visible(timeout=5, raise_error=False):
                self["搜索指定主播"].click()
                logger.info(f"[{self.device.udid}][连麦] 点击搜索指定主播")
            elements = [self["邀请指定主播_1"], self["邀请指定主播_2"]]
            for element in elements:
                if element.wait_for_visible(timeout=8, raise_error=False):
                    element.click()
                    logger.info(f"[{self.device.udid}][连麦] 点击邀请指定主播")
                    return

    def accept_anchor_connect(self):
        """
        接受主播连麦
        """
        logger.info(f"[{self.device.udid}][连麦] 开始接受主播连麦请求")
        elements = [self["接受邀请按钮_1"], self["接受邀请按钮_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=10, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][连麦] 点击接受邀请按钮")
                break

    def reject_anchor_connect(self):
        """
        拒绝主播连麦
        """
        # TODO: 实现拒绝连麦逻辑
        pass

    def disconnect_anchor_host(self):
        """
        断开直播连麦
        """
        logger.info(f"[{self.device.udid}][连麦] 开始断开主播连麦")

        # 点击连接主播按钮
        elements = [self["连接主播_1"], self["连接主播_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][连麦] 点击连接主播按钮")
                time.sleep(2)
                break

        # 点击断开连麦按钮
        if self["断开主播连麦按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["断开主播连麦按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击断开连麦按钮")

    def assert_anchor_host_connect_1v1(self):
        """
        断言主播1V1连麦成功
        """
        time.sleep(5)
        if self["主播显示ID"].wait_for_visible(timeout=20, raise_error=False):
            logger.info(f"[{self.device.udid}][连麦] 断言主播1V1连麦成功")
            return True
        logger.info(f"[{self.device.udid}][连麦] 断言主播1V1连麦失败")
        return False

    def assert_anchor_host_connect_1v3(self):
        """
        断言主播1V3连麦成功
        """
        time.sleep(5)
        if self["主播显示ID"].wait_for_visible(timeout=20, raise_error=False):
            logger.info(f"[{self.device.udid}][连麦] 断言主播1V3连麦成功")
            return True
        logger.info(f"[{self.device.udid}][连麦] 断言主播1V3连麦失败")
        return False

    def assert_anchor_host_disconnected(self):
        """
        断言主播连麦断开
        """
        success = not self["主播显示ID"].wait_for_visible(timeout=5, raise_error=False)
        logger.info(f"[{self.device.udid}][连麦] 断言主播连麦断开: {success}")
        return success

