from uibase.controls import PopupBase
from uibase.upath import UPath, id_, type_, visible_, text_, controller_, label_


def handle_sys_popups(app):
    for i in range(5):
        if app.get_device().device_driver.get_system_popups():
            app.on_system_popup()
        else:
            break


class CommonPopup(PopupBase):
    window_spec = {}

    def handle(self):
        if self.existing:
            return self.click()
        return False
    
    def handler(self):
        return self.handle()
        
class IntellectualPropertyPolicyUpdatePopup(CommonPopup):
    """
    知识产权政策更新弹窗
    """
    spec = {"path": UPath(controller_ == "PNSCommsDialogTUXViewController")}
    window_spec = {"path": UPath(type_ == "TUXDialogHighlightBackgroundButton")}
    
    
class PrivacyPolicyAgreeAndContinuePopup(CommonPopup):
    """
    隐私政策-同意并继续弹窗
    """
    spec = {"path": UPath(controller_ == "PNSCommsDialogTUXViewController")}
    window_spec = {"path": UPath(type_ == "TUXDialogHighlightBackgroundButton")}

class StartScreenRecordingToStartYourLiveStreamPopup(CommonPopup):
    """
    开始屏幕录制以开启直播
    """
    window_spec = {"path": UPath(id_ == "btn")}

class SaveYourLoginInformationPopup(CommonPopup):
    """
    保存登录信息以供下次使用？
    """
    window_spec = {"path": UPath(text_ == "保存登录信息")}


class TVStrengthenSwipeUpGuidePopup(CommonPopup):
    """
    向上滑动
    """
    spec = {"path": UPath(controller_ == "AWESwipeUpGuideFullScreenViewController")}
    window_spec = {"path": UPath(id_ == "mainLabel")}

    def handle(self):
        if self.existing:
            return [self.device.drag(100.0, 600.0, 100.0, 100.0, 2.0, 0.5) for i in range(2)]
        return False

class CloseSecurityCheckPopup(CommonPopup):
    """
    让我们快速做个安全检查-关闭
    """
    spec = {"path": UPath(controller_ == "CustomSheetForPopsuiteImpl")}
    window_spec = {"path": UPath(id_ == "IconXMarkSmall") / UPath(type_ == "UIImageView", depth=5)}

    
class LiveStudioIsNowAvailablePopup(CommonPopup):
    """
    直播工作室现已上线！-暂时不要
    """
    spec = {"path": UPath(controller_ == "IESLiveMTActionSheetPresentViewController")}
    window_spec = {"path": UPath(id_ == "暂时不要")}
    
class SelectTheContentYouAreInterestedInPopup(CommonPopup):
    """
    选择感兴趣的内容-跳过
    """
    spec = {"path": UPath(controller_ == "TTKUserJourneyContainerViewController")}
    window_spec = {"path": UPath(~text_ == "跳过|开始观看")}

class LiveContentCompliancePopup(CommonPopup):
    '''
    please make sure youre live dos NOT Contain any Company confidential infomation
    中文: 请确保您的直播内容不包含任何公司机密信息
    '''
    
    window_spec = {"path": UPath(text_ == "I Know & Agree")}

class LiveContentCompliancePopup2(CommonPopup):
    '''
    please make sure youre live dos NOT Contain any Company confidential infomation
    中文: 请确保您的直播内容不包含任何公司机密信息
    '''
    
    window_spec = {"path": UPath(text_ == "OK")}


class PrivacyAgreementPopup(CommonPopup):
    """同意隐私协议弹窗"""

    window_spec = {"path": UPath(text_ == "同意并继续")}

class BlockUnpopularCommentsPopup(CommonPopup):
    """屏蔽不受欢迎的评论"""

    window_spec = {"path": UPath(text_ == "关闭")}


class SearchFriendsAddressBookPopup(CommonPopup):
    """查找通讯录好友"""

    window_spec = {"path": UPath(text_ == "不允许")}


class VisitorRecordPopup(CommonPopup):
    """访客记录"""

    window_spec = {"path": UPath(label_ == "确认")}


class EventTrackPopup(CommonPopup):
    """"⚠️EventTrack: Error Detection"""

    window_spec = {"path": UPath(label_ == "Ignore")}

class IllustrationMReportPopup(CommonPopup):
    """屏蔽不受欢迎的评论变得更加轻松"""
    
    window_spec = {"path": UPath(text_ == "关闭")}
    