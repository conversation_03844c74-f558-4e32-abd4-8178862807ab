"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/common/tiktok/panel.py
Description: 界面操作和场景相关功能模块
"""

import time
import json
from pathlib import Path
from typing import Union, Optional

from common.tiktok.android.panels.debug import AndroidDebugPanel
from common.tiktok.android.panels.login import *
from common.tiktok.android.panels.live import AndroidLivePanel
from common.tiktok.android.panels.guest_host import AndroidGuestHostPanel
from common.tiktok.android.panels.anchor_host import AndroidAnchorHostPanel
from common.tiktok.android.panels.region import AndroidRegionPanel
from common.tiktok.ios.panels.login import *
from common.tiktok.ios.panels.live import iOSLivePanel
from common.tiktok.ios.panels.guest_host import iOSGuestHostPanel
from common.tiktok.ios.panels.anchor_host import iOSAnchorHostPanel
from common.tiktok.ios.panels.region import iOSRegionPanel
from common.tiktok.android.app import AndroidTikTokApp
from common.tiktok.ios.app import iOSTikTokApp
from utils.common.log_utils import logger


class TTCrossPlatformPanelMixin:
    """跨平台界面操作混入类"""

    def _execute_platform_action(
            self, 
            app: Union[AndroidTikTokApp, iOSTikTokApp],
            method_name: str,
            android_action,
            ios_action,
            *args,
            **kwargs
    ):
        """执行平台相关操作的通用方法
        
        Args:
            app: 应用实例
            method_name: 方法名称
            android_action: Android平台执行的操作
            ios_action: iOS平台执行的操作
            *args: 传递给具体平台操作的位置参数
            **kwargs: 传递给具体平台操作的关键字参数
            
        Returns:
            Any: 平台操作的返回结果
            
        Raises:
            ValueError: 如果平台不支持则抛出异常
        """
        self._check_app_platform(app, method_name)
        
        if isinstance(app, AndroidTikTokApp):
            return android_action(*args, **kwargs)
        elif isinstance(app, iOSTikTokApp):
            return ios_action(*args, **kwargs)

    def _check_app_platform(self, app, method_name):
        """检查应用平台类型是否支持
        Args:
            app: 应用实例
            method_name: 方法名称，用于错误提示
        Raises:
            ValueError: 如果平台不支持则抛出异常
        """
        if not isinstance(app, (AndroidTikTokApp, iOSTikTokApp)):
            raise ValueError(f"{method_name} 方法暂不支持该平台的应用")

    def _format_error_message(self, udid: str, scene: str, platform: str = "") -> str:
        """格式化错误信息
        
        Args:
            udid: 设备ID
            scene: 场景描述
            platform: 平台类型,可选
            
        Returns:
            str: 格式化后的错误信息
        """
        error_msg = f"[断言] 断言失败: {udid} {platform} {scene}"
        return error_msg

    def switch_region(self, app, region):
        """
        切换区域

        Args:
            app: 应用实例
            region: 区域, 例如SG
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 切换区域: {region}")
        def android_action():
            android_region_panel = AndroidRegionPanel(root=app)
            android_region_panel.switch_region(region)

        def ios_action():
            ios_region_panel = iOSRegionPanel(root=app)
            ios_region_panel.switch_region(region)

        self._execute_platform_action(app, "switch_region", android_action, ios_action)
        app.restart()

    def start_live_broadcast(
            self,
            app: Union[AndroidTikTokApp, iOSTikTokApp],
            live_type: str = "camera",
            video_quality: Optional[str] = None,
            enable_beauty: bool = False
    ):
        """
        开启直播
        
        Args:
            app: 应用实例
            live_type: 直播类型，支持 "audio"/"camera"/"game"
            video_quality (str): 视频质量, 可选值: 1080p, 720p, 540p, 480p, 为None默认1080p
            enable_beauty (bool): 是否启用美颜, 只有 camera 直播类型支持
        Raises:
            ValueError: 如果直播类型不支持则抛出异常
        """
        valid_types = ["audio", "camera", "game"]
        if live_type not in valid_types:
            raise ValueError(f"不支持的直播类型: {live_type}, 支持的类型为: {valid_types}")

        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 开始开启直播, 直播类型: {live_type}, 视频质量: {video_quality}")

        def android_action():
            """
            通过客户端UI 开启直播
            """
            android_live_panel = AndroidLivePanel(root=app)
            if live_type == "audio":
                android_live_panel.audio_live()
            elif live_type == "camera":
                android_live_panel.camera_live(video_quality, enable_beauty)
            elif live_type == "game":
                android_live_panel.game_live(video_quality)
            time.sleep(10)

        def ios_action():
            ios_live_panel = iOSLivePanel(root=app)
            if live_type == "audio":
                logger.info("iOS 不支持音频直播")
            elif live_type == "camera":
                ios_live_panel.camera_live(enable_beauty)
            elif live_type == "game":
                ios_live_panel.game_live()
            time.sleep(10)

        return self._execute_platform_action(app, "start_live_broadcast", android_action, ios_action)

    def enter_live_room_with_api(self, app: Union[AndroidTikTokApp], room_id: str):
        """
        嘉宾进入直播间, 暂时只支持Android平台

        Args:
            app: 应用实例
            room_id: 直播间ID
        """
        if not isinstance(app, AndroidTikTokApp):
            raise ValueError("嘉宾通过API的方式进房间只支持Android平台")

        app.on_app_popup()
        cur_room_id = app.link_mic_client.enter_room_with_check(room_id)
        time.sleep(5)  # 等待房间进入
        if cur_room_id != room_id:
            logger.error(f"嘉宾进房间ID不匹配, 期望: {room_id}, 实际: {cur_room_id}")

    def invite_guest_connect_with_api(self, app: Union[AndroidTikTokApp], auxil_app: Union[AndroidTikTokApp], room_id: str):
        """
        邀请嘉宾连麦, 暂时只支持Android平台

        Args:
            app: 应用实例
            room_id: 直播间ID
        """
        app.on_app_popup()
        resp = app.link_mic_client.invite_for_multi(auxil_app.get_uid(), room_id)
        logger.debug(f"invite guest resp: {resp}")
        time.sleep(5)

    def reply_invitation_with_api(self, app: Union[AndroidTikTokApp, iOSTikTokApp], room_id: str, host_uid: str):
        """
        嘉宾接受连麦, 暂时只支持Android平台
        NOTE: 通过UI操作回复邀请, Android端无法通过API来实现, 出现UI不同步问题

        Args:
            app: 应用实例
            room_id: 直播间ID
            host_uid: 主播UID
        """
        udid = app.get_device().udid

        from common.tiktok.android.panels.guest_host import AndroidGuestHostPanel
        window = AndroidGuestHostPanel(root=app)
        if window["接受连麦"].wait_for_visible(timeout=5):
            window["接受连麦"].click()
            logger.info(f"[{udid}][连麦] 点击接受连麦按钮")
        time.sleep(6)

    def assert_anchor_live_broadcast(self, app: Union[AndroidTikTokApp, iOSTikTokApp]):
        """断言主播开启直播成功
        
        Args:
            app: 应用实例
        """
        def android_action():
            android_live_panel = AndroidLivePanel(root=app)
            result = android_live_panel.assert_anchor_live_broadcast()
            if result:
                self.app_lived_list.append(app)
            return result

        def ios_action():
            ios_live_panel = iOSLivePanel(root=app)
            result = ios_live_panel.assert_live_broadcast_success()
            if result:
                self.app_lived_list.append(app)
            return result

        success = self._execute_platform_action(
            app, 
            "assert_anchor_live_broadcast",
            android_action,
            ios_action
        )
        self.assert_(self._format_error_message(app._device.udid, "主播开启直播场景", app._device.type), success)

    def close_live_broadcast(self, app: Union[AndroidTikTokApp, iOSTikTokApp]):
        """
        关闭直播
        根据平台类型选择不同的操作, Android平台优先使用客户端API, iOS平台使用UI操作
        
        Args:
            app: 应用实例
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 关闭直播...")

        def android_action():
            """
            warning: 目前发现长时间连麦时, 一端关闭直播, 另一端会导致直播间crash, 退出到首页, 导致app报crash
            """
            android_live_panel = AndroidLivePanel(root=app)
            android_live_panel.close_live()

        def android_action_with_api():
            """
            TIPS: 执行时间过长后, TT内嵌的socket服务容易挂掉,导致shoots-api调用失败 
            """
            logger.info(f"[{udid}] 通过客户端API 实现主播退出直播间")
            app.on_app_popup()  
            app.link_mic_client.leave_room()

        def ios_action():
            ios_live_panel = iOSLivePanel(root=app)
            ios_live_panel.close_live_stream()

        self._execute_platform_action(
            app,
            "close_live_broadcast",
            android_action,
            ios_action
        )

    def invite_anchor_connect(self, app: Union[AndroidTikTokApp, iOSTikTokApp], anchor_name: str):
        """邀请主播连麦, 基于UI的方式

        Args:
            app: 应用实例
            anchor_name: 主播昵称
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 邀请主播连麦...")

        def android_action():
            android_anchor_host_panel = AndroidAnchorHostPanel(root=app)
            android_anchor_host_panel.invite_anchor_connect(anchor_name)

        def ios_action():
            ios_anchor_host_panel = iOSAnchorHostPanel(root=app)
            ios_anchor_host_panel.invite_anchor_connect(anchor_name)

        self._execute_platform_action(
            app,
            "invite_anchor_connect",
            android_action,
            ios_action
        )

    def accept_anchor_connect(self, app: Union[AndroidTikTokApp, iOSTikTokApp]):
        """接受主播连麦, 基于UI的方式

        Args:
            app: 应用实例
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 接受主播连麦...")

        def android_action():
            android_anchor_host_panel = AndroidAnchorHostPanel(root=app)
            android_anchor_host_panel.accept_anchor_connect()

        def ios_action():
            ios_anchor_host_panel = iOSAnchorHostPanel(root=app)
            ios_anchor_host_panel.accept_anchor_connect()

        self._execute_platform_action(
            app,
            "accept_anchor_connect",
            android_action,
            ios_action
        )

    def assert_anchor_connect(self, app: Union[AndroidTikTokApp, iOSTikTokApp], assert_type: str = "1v1"):
        """断言主播连麦是否成功

        Args:
            app: 应用实例
            assert_type: 断言类型，支持 "1v1"/"1v3"

        Raises:
            ValueError: 如果断言类型不支持则抛出异常
        """
        if assert_type not in ["1v1", "1v3"]:
            raise ValueError(f"不支持的断言类型: {assert_type}, 支持的类型为: ['1v1', '1v3']")

        def android_action():
            android_anchor_host_panel = AndroidAnchorHostPanel(root=app)
            if assert_type == "1v1":
                return android_anchor_host_panel.assert_anchor_host_connect_1v1()
            return android_anchor_host_panel.assert_anchor_host_connect_1v3()

        def ios_action():
            ios_anchor_host_panel = iOSAnchorHostPanel(root=app)
            if assert_type == "1v1":
                return ios_anchor_host_panel.assert_anchor_host_connect_1v1()
            return ios_anchor_host_panel.assert_anchor_host_connect_1v3()

        success = self._execute_platform_action(
            app,
            "assert_anchor_connect",
            android_action,
            ios_action
        )
        self.assert_(self._format_error_message(app._device.udid, f"主播{assert_type}连麦场景", app._device.type), success)

    def search_and_enter_live_room(self, app: Union[AndroidTikTokApp, iOSTikTokApp], anchor_name: str):
        """搜索并进入直播间

        Args:
            app: 应用实例
            anchor_name: 主播昵称
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 通过搜索进入直播间...")

        def android_action():
            android_live_panel = AndroidLivePanel(root=app)
            android_live_panel.search_anchor_name_enter_anchor_room(anchor_name)

        def ios_action():
            ios_live_panel = iOSLivePanel(root=app)
            ios_live_panel.search_anchor_name_enter_anchor_room(anchor_name)

        self._execute_platform_action(
            app,
            "search_and_enter_live_room",
            android_action,
            ios_action
        )

    def guest_apply_connect(self, app: Union[AndroidTikTokApp, iOSTikTokApp]):
        """嘉宾申请连麦, 基于UI的方式

        Args:
            app: 应用实例
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 嘉宾申请连麦...")

        def android_action():
            android_guest_host_panel = AndroidGuestHostPanel(root=app)
            android_guest_host_panel.request_to_host()

        def ios_action():
            ios_guest_host_panel = iOSGuestHostPanel(root=app)
            ios_guest_host_panel.request_to_host()

        self._execute_platform_action(
            app,
            "guest_apply_connect",
            android_action,
            ios_action
        )

    def anchor_agree_guest_connect(self, app: Union[AndroidTikTokApp, iOSTikTokApp]):
        """主播同意嘉宾连麦, 基于UI的方式

        Args:
            app: 应用实例
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 主播同意嘉宾连麦...")

        def android_action():
            android_guest_host_panel = AndroidGuestHostPanel(root=app)
            android_guest_host_panel.accept_guest_connect()

        def ios_action():
            ios_guest_host_panel = iOSGuestHostPanel(root=app)
            ios_guest_host_panel.accept_guest_connect()

        self._execute_platform_action(
            app,
            "anchor_agree_guest_connect",
            android_action,
            ios_action
        )

    def assert_guest_enter_anchor_room(self, app: Union[AndroidTikTokApp, iOSTikTokApp]):
        """断言嘉宾进入主播直播间

        Args:
            app: 应用实例
        """
        def android_action():
            android_live_panel = AndroidLivePanel(root=app)
            return android_live_panel.assert_audience_enter_anchor_room()

        def ios_action():
            ios_live_panel = iOSLivePanel(root=app)
            return ios_live_panel.assert_audience_enter_anchor_room()

        success = self._execute_platform_action(
            app,
            "assert_guest_enter_anchor_room",
            android_action,
            ios_action
        )
        self.assert_(self._format_error_message(app._device.udid, "嘉宾进入主播直播间场景", app._device.type), success)

    def guest_open_camera(self, app: Union[AndroidTikTokApp, iOSTikTokApp]):
        """嘉宾打开摄像头

        Args:
            app: 应用实例
        """
        udid = app.get_device().udid
        logger.info(f"==>[{udid}] 嘉宾准备打开摄像头...")

        def android_action():
            android_guest_host_panel = AndroidGuestHostPanel(root=app)
            android_guest_host_panel.open_guest_camera()

        def ios_action():
            ios_guest_host_panel = iOSGuestHostPanel(root=app)
            ios_guest_host_panel.open_guest_camera()

        self._execute_platform_action(
            app,
            "guest_open_camera",
            android_action,
            ios_action
        )

    def assert_guest_connect(self, app: Union[AndroidTikTokApp, iOSTikTokApp], assert_type: str = "1v1"):
        """断言嘉宾连麦是否成功

        Args:
            app: 应用实例
            assert_type: 断言类型，支持 "1v1"/"1v3"

        Raises:
            ValueError: 如果断言类型不支持则抛出异常
        """
        if assert_type not in ["1v1", "1v3"]:
            raise ValueError(f"不支持的断言类型: {assert_type}, 支持的类型为: ['1v1', '1v3']")
        time.sleep(3)

        def android_action():
            android_guest_host_panel = AndroidGuestHostPanel(root=app)
            if assert_type == "1v1":
                return android_guest_host_panel.assert_guest_connect_1v1()
            android_guest_host_panel.guest_name_a = self.auxil_accounts[0]["account_username"]
            android_guest_host_panel.guest_name_b = self.auxil_accounts[1]["account_username"]
            android_guest_host_panel.guest_name_c = self.auxil_accounts[2]["account_username"]
            return android_guest_host_panel.assert_guest_connect_1v3()

        def ios_action():
            ios_guest_host_panel = iOSGuestHostPanel(root=app)
            if assert_type == "1v1":
                return ios_guest_host_panel.assert_guest_connect_1v1()
            return ios_guest_host_panel.assert_guest_connect_1v3()

        success = self._execute_platform_action(
            app,
            "assert_guest_connect",
            android_action,
            ios_action
        )
        self.assert_(self._format_error_message(app._device.udid, f"嘉宾{assert_type}连麦场景", app._device.type), success)

    def enable_ttmock(self, app: Union[AndroidTikTokApp]):
        """
        启用ttmock开关, 只要推送一个空的配置就可以启动ttmock
        """
        self._push_ttmock_json(app, {})

    def _push_ttmock_json(self, app: Union[AndroidTikTokApp], config: dict):
        json_path = Path("ttmock.json")
        try:
            with json_path.open("w", encoding="utf-8") as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            raise RuntimeError(f"Failed to write ttmock.json: {e}")

        logger.debug("push ttmock.json to device")
        app.get_device().push_file(str(json_path), "/data/local/tmp/ttmock.json")

    def switch_ppe_environment(self, lane: str):
        """
        切换主设备的PPE环境并设置泳道配置
        注意: 这个方法会默认设置Region为SG, 先这样吧!!

        功能说明：
        1. Android设备: 通过推送TTMock配置设置区域和PPE泳道
        2. iOS设备: 通过初始化新环境配置设置网络类型和泳道和设置区域
        3. 最后重启应用使配置生效

        Args:
            lane: 泳道名称 (例如：'ppe_wuzhihao_feedmock')
        """
        logger.info(f"开始切换PPE环境, 目标泳道: {lane}")

        if isinstance(self.perf_app, AndroidTikTokApp):
            config = {
                "region": {
                    "carrier_region" : "SG",
                    "mcc_region" : "529",
                    "sys_region" : "SG"
                },
                "boe": {
                    "enable": True,
                    "enablePPE": True,
                    "ppeLane": lane
                }
            }
            self._push_ttmock_json(self.perf_app, config)

        elif isinstance(self.perf_app, iOSTikTokApp):
            ios_env = {
                "net_env": json.dumps({
                    "net_type": 1,  # 网络类型1表示PPE环境
                    "lane": lane
                }),
                "kTTKRegionLaunchStubsEnvKey": "SG",
                "kTTKCarrierCountryCodeStubsEnvKey": "SG",
                "kTTKPriorityRegionLaunchStubsEnvKey": "SG",
                "kTTKCarrierRegionLaunchStubsEnvKey": "SG",
                "kTTKCountryCodeStubsEnvKey": "SG"
            }
            self.perf_app = self._ios_init_app(self.perf_device, env=ios_env)

        # 重启应用使配置生效
        self.perf_app.restart()
        logger.info(f"PPE环境切换成功, 当前泳道: {lane}")

    def enable_link_to_ET(self):
        """
        开启主设备的Link to ET功能
        1. 重启应用等待一段时间, 等待埋点上报生效
        1. Android平台通过debug panel开启
        2. iOS平台默认是开启的, 因此无需重复开启
        """
        if isinstance(self.perf_app, iOSTikTokApp):
            logger.info("iOS平台默认开启Link to ET, 无需额外操作")
            return

        logger.info("先重启应用, 等待一段时间, 等待埋点上报生效")   # TODO
        self.perf_app.restart(delay=20)

        android_debug_panel = AndroidDebugPanel(root=self.perf_app)
        android_debug_panel.enable_link_to_ET()

        time.sleep(20)
        self.perf_app.restart(delay=20)
        android_debug_panel = AndroidDebugPanel(root=self.perf_app)
        android_debug_panel.enable_link_to_ET()
