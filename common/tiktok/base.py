"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/common/tiktok/base.py
Description: 基础功能模块 - 数据驱动、设备管理、账号管理相关功能
"""

import threading
import random
import time
import os
import traceback
from typing import Dict, Union, List

from common.tiktok.android.app import AndroidTikTokApp
from common.tiktok.ios.app import iOSTikTokApp
from common.tiktok.android.base import TTAndroidPerfAppTestBase
from common.tiktok.ios.base import TTiOSPerfAppTestBase
from common.tiktok.panel import TTCrossPlatformPanelMixin
from common.tiktok.perf import TTCrossPlatformPerfMixin
from defines import *
from utils.apis.wltc_token_api import wltc_token_api
from utils.common.file_utils import get_work_dir, get_task_json, write_bytest_config
from utils.common.log_utils import logger, log_exception
from utils.control.serial_control import serial_control
from utils.control.android_control import android_control
from utils.control.ios_control import ios_control
from shoots.config import settings



class TTCrossPlatformPerfAppTestBase(TTAndroidPerfAppTestBase, TTiOSPerfAppTestBase, TTCrossPlatformPanelMixin, TTCrossPlatformPerfMixin):
    """跨平台测试基类 - 整合基础功能、界面操作和性能测试功能"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.subtask_data: Dict = get_task_json()

        # 性能测试设备相关
        self.perf_udid = None  # 设备ID
        self.perf_account_uid = None  # UID

        # 任务相关
        self.task_info = None  # 任务信息

        # 设备相关
        self.perf_device = None  # 性能测试设备实例
        self.auxil_device_list = []  # 辅助设备实例列表

        # 应用相关
        self.perf_app = None  # 性能测试设备应用实例
        self.perf_app_did = None
        self.auxil_app_list = []  # 辅助设备应用实例列表
        
        # 账号相关
        self.auxil_accounts = []  # 辅助设备账号数据列表

        # 数据采集相关
        self.collect_result = None  # 采集结果

        # 业务功能相关
        self.app_lived_list = []  # 正在直播的应用列表

        # 实验相关
        self.experiment_configs = None

        # 优化：缓存设备数量，避免重复getattr调用
        self.required_device_count = getattr(self, 'device_count', 1)

    def _format_error_message(self, udid: str, scene: str, platform: str = "") -> str:
        """格式化错误信息
        
        Args:
            udid: 设备ID
            scene: 场景描述
            platform: 平台类型,可选
            
        Returns:
            str: 格式化后的错误信息
        """
        error_msg = f"[断言] 断言失败: {udid} {platform} {scene}"
        return error_msg

    @log_exception
    def pre_test(self) -> None:
        """初始化测试环境
        
        执行测试前的准备工作:
        1. 获取数据驱动数据
        2. 初始化性能测试设备
        3. 初始化辅助设备
        4. 重启应用
        """
        logger.info("初始化本地配置")
        self._init_local_config()

        # 获取数据驱动数据
        logger.info("设置数据驱动相关的基础数据")
        self.setup_driven_data()

        # 初始化设备
        logger.info("初始化性能测试设备") 
        self._init_perf_device()

        logger.info("初始化辅助设备")
        self._init_auxil_devices()

        # 重启应用
        logger.info("并行重启性能设备APP和辅助设备APP")
        self._restart_apps_parallel()

    def _restart_apps_parallel(self):
        """
        并行重启性能设备APP和辅助设备APP
        """
        threads = []
        
        def __restart_perf():
            perf_udid = self.perf_app.get_device().udid
            logger.info(f"[{perf_udid}] 重启性能设备APP")
            self.perf_app.restart()
        
        def __restart_auxil(app):
            auxil_udid = app.get_device().udid
            logger.info(f"[{auxil_udid}] 重启辅助设备APP")
            app.restart()
        
        perf_thread = threading.Thread(target=__restart_perf)
        perf_thread.start()
        threads.append(perf_thread)
        
        for app in self.auxil_app_list:
            auxil_thread = threading.Thread(target=__restart_auxil, args=(app,))
            auxil_thread.start()
            threads.append(auxil_thread)
        
        for thread in threads:
            thread.join()

    def _init_local_config(self):
        """初始化本地配置"""
        
        # 初始化shoots-libra配置
        logger.debug("初始化shoots-libra配置")
        write_bytest_config()

    def setup_driven_data(self):
        """设置数据驱动相关的基础数据"""
        logger.debug("[数据] 开始设置数据驱动基础数据")

        # 基础配置数据
        self.battery_level = self.subtask_data.get("perf_device_power_level", 0)
        self.collect_time = self.subtask_data.get("perf_collect_duration", 0)
        self.collect_metrics = self.subtask_data.get("perf_collect_type_list", [])
        self.collect_interval = self.subtask_data.get("perf_collect_interval", 0)
        self.perf_collect_mode = self.subtask_data.get("perf_collect_mode", 0)
        logger.debug(f"[数据] 数据驱动基础数据设置完成")

        # 任务信息
        self.task_info = self.subtask_data.get("task_info", {})
        self._setup_task_info()

        # 账号组数据
        self.account_group = self.subtask_data.get("account_group", {})
        self._setup_account_data()

        # 设备组数据
        self.device_group = self.subtask_data.get("device_group", {})
        self._setup_device_data()

        # 配置组数据
        self.config_group = self.subtask_data.get("config_group", {})
        self._setup_config_data()

        # 实验组数据
        self.experiment_configs = self.subtask_data.get("experiment_configs", {})
        self._setup_experiment_configs_data()

    def _setup_task_info(self):
        """设置任务信息"""
        logger.debug("[数据] 开始设置任务信息")
        self.task_name = self.task_info.get("name")
        self.task_type = self.task_info.get("type")
        self.task_status = self.task_info.get("status")
        self.perf_tool_type = self.task_info.get("perf_tool_type")
        logger.debug(f"[数据] 任务信息设置完成: {self.task_info}")


    def _setup_account_data(self):
        """设置账号相关数据"""
        logger.debug("[数据] 开始设置账号数据")

        # 性能测试设备账号
        perf_account = self.account_group.get("perf_account", {})
        self.perf_account_uid = perf_account.get("uid")
        self.perf_account_iphone = perf_account.get("iphone")
        self.perf_account_username = perf_account.get("username")
        self.perf_account_captcha = perf_account.get("captcha")
        self.perf_account_email = perf_account.get("email")
        self.perf_account_pwd = perf_account.get("pwd")
        self.perf_account_country = perf_account.get("country")
        self.perf_account_country_code_alpha2 = perf_account.get("country_code_alpha2")
        self.perf_account_phone_area_code = perf_account.get("phone_area_code")
        if perf_account:
            self.perf_account_uid = perf_account.get("uid")
            self.perf_account = {
                "account_uid": self.perf_account_uid,
                "account_iphone": self.perf_account_iphone,
                "account_username": self.perf_account_username,
                "account_captcha": self.perf_account_captcha,
                "account_email": self.perf_account_email,
                "account_pwd": self.perf_account_pwd,
                "account_country": self.perf_account_country,
                "account_country_code_alpha2": self.perf_account_country_code_alpha2,
                "account_phone_area_code": self.perf_account_phone_area_code
            }
            logger.info(f"[数据] 性能测试设备账号: {self.perf_account['account_username']}")

        # 辅助设备账号
        self.auxil_accounts = [
            {
                "account_uid": account.get("uid"),
                "account_iphone": account.get("iphone"),
                "account_username": account.get("username"),
                "account_captcha": account.get("captcha"),
                "account_email": account.get("email"),
                "account_pwd": account.get("pwd"),
                "account_country": account.get("country", "中国大陆"),
                "account_country_code_alpha2": account.get("country_code_alpha2", "CN"),
                "account_phone_area_code": account.get("phone_area_code", 86)
            }
            for account in self.account_group.get("auxil_accounts", [])
        ]
        for account_data in self.auxil_accounts:
            logger.info(f"[数据] 辅助设备账号: {account_data['account_username']}")

        logger.debug(f"[数据] 账号数据设置完成 {self.account_group}")

    def _setup_device_data(self):
        """设置设备相关数据"""
        logger.debug("[数据] 开始设置设备数据")

        # 性能测试设备
        perf_device = self.device_group.get("perf_device", {})
        self.perf_name = perf_device.get("name")
        self.perf_udid = perf_device.get("udid")
        self.perf_model = perf_device.get("model")
        self.perf_platform = perf_device.get("sys_type")
        self.perf_sys_version = perf_device.get("sys_version")
        self.perf_brand = perf_device.get("brand")
        self.perf_resolution = perf_device.get("resolution")
        self.perf_device_ip = perf_device.get("ip")
        self.perf_device_serial_port = perf_device.get("serial_port")

        logger.info(f"[数据] 性能测试设备: {self.perf_udid}")

        # 辅助设备列表
        self.auxil_devices = self.device_group.get("auxil_devices", [])
        for device in self.auxil_devices:
            logger.info(f"[数据] 辅助设备: {device.get('udid')}")

        logger.debug(f"[数据] 设备数据设置完成 {self.device_group}")

    def _setup_config_data(self):
        """设置配置相关数据"""
        logger.debug("[数据] 开始设置配置数据")

        self.metrics = self.config_group.get("metrics", [])

        # trace指标配置
        self.trace_metrics = [metric for metric in self.metrics if metric.get("metric_category") == MetricCategory.TRACE]
        logger.debug(f"[数据] trace指标配置: {self.trace_metrics}")
        self.byteio_metrics = [metric for metric in self.metrics if metric.get("metric_category") == MetricCategory.BYTEIO]
        logger.debug(f"[数据] byteio指标配置: {self.byteio_metrics}")

    def _setup_experiment_configs_data(self):
        """设置实验相关数据"""
        logger.debug("[数据] 开始设置实验数据")

        # 实验组数据
        self.experiment_configs = self.subtask_data.get("experiment_configs", {})
        self.hit_type = self.experiment_configs.get("hit_type")
        self.experiment_group_version_ids = self.experiment_configs.get("experiment_group_version_ids")
        self.control_group_version_ids = self.experiment_configs.get("control_group_version_ids")

        logger.debug(f"[数据] 实验数据设置完成: {self.experiment_configs}")

    def _init_perf_device(self):
        """初始化性能测试设备"""
        logger.info("开始初始化性能测试设备")
        
        # 只在无线采集模式下操作串口
        if self.perf_collect_mode == PerfCollectMode.WIRELESS_COLLECT:
            serial_control.relay_switch(self.perf_device_serial_port, True)

        self.perf_platform = self.device_group["perf_device"].get("sys_type")

        # 验证平台类型
        if self.perf_platform not in [PlatformType.ANDROID, PlatformType.IOS]:
            raise ValueError(f"不支持的平台类型: {self.perf_platform}")

        # 初始化环境参数
        init_params = [self.perf_udid]

        if self.task_type == TaskType.LIBRA_EXPERIMENT:
            if settings.GLOBAL_BUSINESS_PERF_TASK_CURRENT_EXPERIMENT_TYPE == LibraGroupType.EXPERIMENT:
                init_params.append(self.experiment_group_version_ids)
            elif settings.GLOBAL_BUSINESS_PERF_TASK_CURRENT_EXPERIMENT_TYPE == LibraGroupType.CONTROL:
                init_params.append(self.control_group_version_ids)
            else:
                raise ValueError(f"不支持的实验类型: {settings.GLOBAL_BUSINESS_PERF_TASK_CURRENT_EXPERIMENT_TYPE}")

            if self.hit_type == LibraHitType.UID:
                init_params.append(self.perf_account_uid)

        # 根据平台类型初始化环境
        init_env = self.init_android_env if self.perf_platform == PlatformType.ANDROID else self.init_ios_env
        self.perf_device, self.perf_app = init_env(*init_params)

        # Android平台额外启动ttmock开关
        if self.perf_platform == PlatformType.ANDROID:
            logger.info(f"[{self.perf_device.udid}] 启动ttmock开关")
            self.enable_ttmock(self.perf_app)

        self.perf_device_ip = self.perf_device.ip
        logger.info(f"[{self.perf_device.udid}] 性能测试设备IP: {self.perf_device_ip}")
        logger.info(f"[{self.perf_device.udid}] 性能测试设备初始化完成")

    def _init_auxil_devices(self):
        """初始化辅助设备"""
        # 优化：使用缓存的设备数量，避免重复getattr调用
        # 如果只需要1台设备，说明不需要辅助设备
        if self.required_device_count <= 1:
            logger.info("用例只需要1台设备，无需初始化辅助设备")
            return

        init_device_count = self.required_device_count - 1

        if init_device_count <= 0:
            logger.info("没有辅助设备需要初始化")
            return
        
        online_devices: List[Dict] = []

        for device in self.auxil_devices:
            udid = device.get("udid")
            platform = device.get("sys_type")

            if platform == PlatformType.ANDROID and android_control.is_android_device_online(udid):
                online_devices.append(device)
            elif platform == PlatformType.IOS and ios_control.is_ios_device_online(udid):
                online_devices.append(device)

        online_udids = [d.get("udid") for d in online_devices]
        if init_device_count > len(online_udids):
            raise ValueError(
                f"用例要求的辅助设备数为 {init_device_count}，但当前实际在线的辅助设备仅有 {len(online_udids)} 台: {online_udids}"
            )

        selected_devices = random.sample(online_devices, init_device_count)
        logger.info(f"开始初始化{init_device_count}台辅助设备")
    
        for device_data in selected_devices:
            platform_type = device_data.get("sys_type")
            udid = device_data.get("udid")

            if platform_type == PlatformType.ANDROID:
                device, app = self.init_android_env(udid)
                logger.info(f"[{udid}] 启动ttmock开关")
                self.enable_ttmock(app)
            elif platform_type == PlatformType.IOS:
                device, app = self.init_ios_env(udid)
            else:
                raise ValueError(f"不支持的平台类型: {platform_type}")

            self.auxil_device_list.append(device)
            self.auxil_app_list.append(app)
            logger.info(f"[{device.udid}] 辅助设备初始化完成")

    def login_all_devices(self):
        """
        登录所有设备
        获取任务的所有设备,并分别登录, 账号信息是从平台传入的
        1. 登录性能测试设备账号
        2. 登录辅助设备账号
        
        Note:
        1. Android设备是通过自动登录实现
        2. iOS设备是通过UI登录实现

        Raises:
            RuntimeError: 登录失败时抛出异常
        """
        # 登录性能测试设备账号
        self._login_perf_account()
        # 登录辅助设备账号
        self._login_auxil_accounts()

    def _login_device(self, app: Union[AndroidTikTokApp, iOSTikTokApp], 
                    account_info: dict, 
                    device_type: str = "性能测试设备") -> bool:
        """登录设备
        
        Args:
            app: 应用实例
            account_info: 账号信息字典，包含以下字段:
                - account_iphone: 账号
                - account_captcha: 验证码
                - account_username: 昵称
            device_type: 设备类型描述
            
        Returns:
            bool: 登录成功返回True

        Raises:
            RuntimeError: 登录失败时抛出异常
        """

        account_iphone = account_info.get("account_iphone")
        account_captcha = account_info.get("account_captcha") 
        account_username = account_info.get("account_username")
        account_uid = account_info.get("account_uid")
        country = account_info.get("account_country", "中国大陆")
        phone_area_code = account_info.get("account_phone_area_code", 86)
        udid = app.get_device().udid

        # 豁免风控
        self._bypass_risk_control(app=app)

        logger.info(f"[{udid}] 开始登录{device_type} 账号:{account_iphone} 验证码: {account_captcha} 昵称:{account_username} 国家:{country} 区号:{phone_area_code}")

        # if isinstance(app, AndroidTikTokApp):
        logger.info(f"[{device_type}][{udid}]判断是否登录")
        if app.is_login():
            logger.info(f"[{device_type}][{udid}]判断是否已登录正确账号")
            if app.get_uid() == account_uid:
                logger.info(f"[{device_type}][{udid}]已登录正确账号")
                return True
            else:
                logger.info(f"[{device_type}][{udid}]登录的不是预期账号")

        logger.info(f"[{device_type}][{udid}]进行自动登录")
        if app.login(account_iphone, account_captcha, phone_area_code):
            logger.info(f"[{device_type}][{udid}]自动登录成功") 
        else:
            raise RuntimeError(f"[{device_type}][{udid}]自动登录失败")  

    def _login_perf_account(self):
        """登录性能测试设备账号"""
        self._login_device(self.perf_app, self.perf_account)

    def _login_auxil_accounts(self):
        """登录辅助设备账号"""
        # 优化：使用缓存的设备数量，避免重复getattr调用
        if self.required_device_count <= 1:
            logger.info("用例只需要1台设备，无需登录辅助设备")
            return

        login_device_count = min(len(self.auxil_devices), self.required_device_count - 1)
        if login_device_count <= 0:
            logger.info("没有辅助设备需要登录")
            return

        logger.info(f"开始登录{login_device_count}台辅助设备")
        for idx in range(login_device_count):
            app = self.auxil_app_list[idx]
            account = self.auxil_accounts[idx]
            self._login_device(app, account, "辅助设备")

    def login_fixed_account(self, fixed_account: Dict):
        """
        使用固定账号登录
        1. Android设备是通过自动登录实现
        2. iOS设备是通过UI登录实现
        
        Args:
            fixed_account: 固定账号信息字典
        Example:
            fixed_account = {
                "account_iphone": "***********",  # 固定手机号
                "account_captcha": "XXXXX",  # 固定验证码
                "account_username": "GlobalRTC9069",  # 固定昵称
                "account_uid": "7327132535639884818",  # 固定用户ID
                "account_country": "中国大陆",
                "account_phone_area_code": "86"
            }

        Raises:
            RuntimeError: 登录失败时抛出异常
        """
        self._login_device(self.perf_app, fixed_account, "固定账号") 

    def _bypass_risk_control(self, did: str = None, app: Union[AndroidTikTokApp, iOSTikTokApp] = None) -> str:
        """豁免风控并获取token
        
        Args:   
            did (str, optional): 设备ID. 如果为None则尝试从app获取
            app: 应用实例

        Returns:
            str: 风控豁免token

        Raises:
            RuntimeError: 获取token失败时抛出
        """
        try:
            # 如果没有传入did则获取设备信息
            if not did:
                did = app.get_did()
                if app == self.perf_app:
                    self.perf_app_did = did
                if not did:
                    return None
            
            # 先尝试获取已存在的豁免信息
            did_result = wltc_token_api.get_did_risk_control_exemption_info(did).get('data', "")
            logger.debug(f"[豁免] 获取豁免信息: {did_result}")
            if did_result:
                token = did_result[0]['user_define']['token']
                return token

            # 如果不存在则申请新的token
            wltc_token = wltc_token_api.get_wltc_token(did)
            logger.debug(f"[豁免] 申请新的WLTC token: {wltc_token}")
            if not wltc_token:
                logger.error("申请新的WLTC token失败")
                return None
            
            # 添加豁免信息
            wltc_token_api.add_did_risk_control_exemption_info(did, wltc_token)
            logger.info(f"[豁免] 添加豁免信息: {wltc_token}")
            return wltc_token

        except Exception as e:
            logger.error(f"风控豁免失败: {str(e)}\n{traceback.format_exc()}")
            return None

    def screenshot_all_devices(self):
        """
        对当前性能设备及所有辅助设备进行截图并保存到本地路径。

        截图文件将保存在当前工作目录下
        截图文件名格式为: custom_screenshot_{udid}_{timestamp}.jpg
        示例:
            custom_screenshot_5bdc046_1747222097095.jpg

        """

        BASE_DIR = get_work_dir()

        def _build_path(udid, prefix="custom_screenshot") -> str:
            timestamp = int(time.time() * 1000)
            filename = f"{prefix}_{udid}_{timestamp}.jpg"
            return os.path.join(BASE_DIR, filename)

        udid = self.perf_device.udid

        logger.info(f"[{udid}] 对性能设备进行截图...")
        path = _build_path(udid)
        self.perf_device.screenshot(path)
        logger.debug(f"截图保存路径: {path}")

        # 所有辅助设备截图
        for device in self.auxil_device_list:
            auxil_udid = device.udid
            _path = _build_path(auxil_udid)
            logger.info(f"[{auxil_udid}] 对辅助设备进行截图...")
            device.screenshot(_path)
            logger.debug(f"截图保存路径: {_path}")

    @log_exception
    def post_test(self):
        """测试结束处理"""
        try:
            # 无线采集模式处理
            if self.perf_collect_mode == PerfCollectMode.WIRELESS_COLLECT:
                serial_control.relay_switch(self.perf_device_serial_port, True)
                time.sleep(3)
                self.wireless_disconnect(self.perf_udid)

            # 关闭直播
            if hasattr(self, 'app_lived_list') and len(self.app_lived_list) > 0:
                for app in self.app_lived_list:
                    try:
                        self.close_live_broadcast(app)
                    except Exception as e:
                        # 目前发现长时间连麦时, 一端关闭直播, 另一端会导致直播间crash, 退出到首页, 导致ProcessNotFoundError
                        udid = app.get_device().udid
                        logger.warning(f"[{udid}] 关闭直播失败: {str(e)}")
                    # 关闭一个连麦的主播直播后,其他的主播端页面会有变化,需要等待
                    time.sleep(5)

            # 后台处理
            if self.perf_app:
                self.perf_app.background()
            for app in self.auxil_app_list:
                app.background()

            # 处理采集结果
            if hasattr(self, 'collect_result') and self.collect_result:
                self.assert_("[性能数据处理] 结果", self.handle_collect_result())
        except Exception as e:
            logger.error(str(e))










