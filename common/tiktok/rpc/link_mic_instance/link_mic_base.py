# -*- coding: utf8 -*-
import time
from typing import Dict, Any

from common.tiktok.rpc.link_mic_instance.base_role import BaseRoleInterface


class LinkMicInstance(BaseRoleInterface):
    """
    嘉宾连麦 & 主播 连麦LinkMicSession类
    """

    _channel_id = None
    _inner_channel_id = None
    _cohost_link_mic_id = None
    _multi_link_mic_id = None

    def get_inner_channel_id(self):
        """
        获取CoHost的频道ID
        
        Returns:
            内部频道ID
        """
        resp = self.rpc.request("getInnerChannelId",{})
        return resp["data"]

    def get_channel_id(self):
        """
        获取MultiGuest的频道ID
        
        Returns:
            频道ID
        """
        resp = self.rpc.request("channelId",{})

        return resp["data"]

    def get_link_mic_id_for_multi(self):
        """
        获取多人连麦ID
        
        Returns:
            多人连麦ID
        """
        resp = self.rpc.request("selfLinkMicId",{})

        return resp["data"]

    def get_link_mic_id_for_cohost(self):
        """
        获取CoHost连麦ID
        
        Returns:
            CoHost连麦ID
        """
        resp = self.rpc.request("getCoHostLinkMicId",{})
        return resp["data"]

    def get_first_frame_list_for_cohost(self, link_mic_id):
        """
        获取CoHost首帧列表
        
        Args:
            link_mic_id: 连麦ID
            
        Returns:
            首帧列表响应
        """
        resp = self.rpc.request("getCoHostFirstFrameGuestList", {
            "link_mid_id": link_mic_id
        })

        return resp

    def get_user_list_for_cohost(self):
        """
        获取CoHost用户列表
        
        Returns:
            用户列表响应
        """
        resp = self.rpc.request("getCoHostUserList",{})
        return resp

    def get_cohost_linked_list_from_session(self):
        """
        从会话中获取CoHost已连接列表
        
        Returns:
            已连接列表响应
        """
        resp = self.rpc.request("getCoHostLinkedListFromSession",{})
        return resp

    def get_cohost_unlinked_list_from_session(self):
        """
        从会话中获取CoHost未连接列表
        
        Returns:
            未连接列表响应
        """
        resp = self.rpc.request("getCoHostUnLinkedListFromSession",{})
        return resp
        
    def get_cohost_unlinked_list_from_biz(self):
        """
        从业务中获取CoHost未连接列表
        
        Returns:
            未连接列表响应
        """
        resp = self.rpc.request("getCoHostUnLinkedListFromBiz",{})
        return resp

    def get_cohost_linked_list_from_biz(self):
        """
        从业务中获取CoHost已连接列表
        
        Returns:
            已连接列表响应
        """
        resp = self.rpc.request("getCoHostLinkedListFromBiz",{})
        return resp

    def get_cohost_link_mic_state_from_session(self):
        """
        从会话中获取CoHost连麦状态
        const val IDLE = 0
        const val INITIALIZED = 1
        const val WAITING = 2
        const val JOINING_CHANNEL = 3
        const val JOINED_CHANNEL = 4
        const val FRAME_LINKED = 5
        const val FINISHED = 6 // multi-host
        
        Returns:
            连麦状态
        """
        resp = self.rpc.request("getCoHostLinkMicStateFromSession",{})

        return resp["data"]

    def get_cohost_link_mic_state_from_biz(self):
        """
        从业务中获取CoHost连麦状态
        
        Returns:
            连麦状态
        """
        resp = self.rpc.request("getCoHostLinkMicStateFromBiz",{})

        return resp["data"]

    def enter_room(self, room_id: str, max_retries: int = 5) -> Dict[str, Any]:
        """
        尝试进入指定房间，支持重试机制

        Args:
            room_id (str): 要进入的房间ID
            max_retries (int): 最大重试次数（默认为5次）

        Returns:
            Dict[str, Any]: 服务器响应数据
        """
        attempt = 0
        while attempt <= max_retries:
            data = {"roomId": str(room_id)}
            response = self.rpc.request("enterRoom", data)

            # 检查请求是否成功
            if response.get("code") == "0" or response.get("data") is None:
                break

            attempt += 1
            if attempt <= max_retries:
                time.sleep(4)  # 重试前等待

        return response

    def get_usr_info(self):
        """
        获取用户信息
        
        Returns:
            用户信息响应
        """
        response = self.rpc.request("getUserInfo", {})
        uid = response["data"]["uid"]
        self.set_uid(uid)
        return response

    def get_list_by_type(self, room_id, type_list):
        """
        根据类型获取列表
        
        Args:
            room_id: 房间ID
            type_list: 类型列表
            
        Returns:
            列表响应
        """
        params = {
            "roomId": room_id,
            "typeList": type_list
        }
        response = self.rpc.request("getListByType", params)
        return response

    def set_uid(self, uid):
        """
        设置用户ID
        
        Args:
            uid: 用户ID
        """
        self._uid = uid

    def set_hardware_channel(self, channel):
        """
        设置硬件通道
        
        Args:
            channel: 硬件通道
        """
        self.hardware_channel = channel

    def set_input_path(self, path):
        """
        设置输入路径
        
        Args:
            path: 输入路径
        """
        self.input_path = path

    def set_output_path(self, path):
        """
        设置输出路径
        
        Args:
            path: 输出路径
        """
        self.output_path = path

    def set_role(self, role):
        """
        设置角色
        
        Args:
            role: 角色
        """
        self.role = role

    def set_device_name(self, name):
        """
        设置设备名称
        
        Args:
            name: 设备名称
        """
        self.devices_name = name

    def do_play_audio(self, input_path=None):
        """
        播放音频
        
        Args:
            input_path: 输入路径
        """
        return

    def do_rec_audio(self, save_tag=None, len=20):
        """
        录制音频
        
        Args:
            save_tag: 保存标签
            len: 录制时长
        """
        return

    def do_play_and_rec(self):
        """
        播放并录制音频
        """
        pass

    def is_mute(self, detection):
        """
        判断是否静音
        
        Args:
            detection: 检测数据
            
        Returns:
            bool: 是否静音
        """
        right = detection["left"].get("is_mute", True)
        left = detection["right"].get("is_mute", True)

        return right and left

    def do_restart(self):
        """
        执行重启操作
        """
        self.app.restart()

    @property
    def room_id(self):
        """
        获取房间ID
        
        Returns:
            房间ID
        """
        return self._room_id

    def set_room_id(self,value):
        """
        设置房间ID
        
        Args:
            value: 房间ID值
        """
        self._room_id = value
        
    @property
    def uid(self):
        """
        获取用户ID
        
        Returns:
            用户ID
        """
        return self._uid

    @property
    def device(self):
        """
        获取设备实例
        
        Returns:
            设备实例
        """
        return self.app.get_device()

    @property
    def channel_id(self):
        """
        获取频道ID
        
        Returns:
            频道ID
        """
        if not self._channel_id:
            self._channel_id = self.get_channel_id()
            return self._channel_id
        else:
            return self._channel_id

    @channel_id.setter
    def channel_id(self,value):
        """
        设置频道ID
        
        Args:
            value: 频道ID值
        """
        self._channel_id = value

    def set_inner_channel_id(self,value):
        """
        设置内部频道ID
        
        Args:
            value: 内部频道ID值
        """
        self._inner_channel_id = value
        
    @property
    def inner_channel_id(self):
        """
        获取内部频道ID
        
        Returns:
            内部频道ID
        """
        if not self._inner_channel_id:
            inner = self.get_inner_channel_id()
            self.set_inner_channel_id(inner)
            return inner
        return self._inner_channel_id

    @property
    def cohost_link_mic_id(self):
        """
        获取CoHost连麦ID
        
        Returns:
            CoHost连麦ID
        """
        if not self._cohost_link_mic_id:
            value = self.get_link_mic_id_for_cohost()
            self._cohost_link_mic_id = value
        return self._cohost_link_mic_id

    @property
    def multi_link_mic_id(self):
        """
        获取多人连麦ID
        
        Returns:
            多人连麦ID
        """
        if not self._multi_link_mic_id:
            value = self.get_link_mic_id_for_multi()
            self._multi_link_mic_id = value
        return self._multi_link_mic_id

    # def __str__(self):
    #     return "device name: %s; input_path:%s; output_path:%s; role:%s; hardware_channel:%s" % (
    #         self.devices_name, self.input_path, self.output_path, self.role, self.hardware_channel)

    def __str__(self):
        """
        返回对象字符串表示
        
        Returns:
            str: 对象字符串表示
        """
        return f"[linkMicBaseRole] device_name: {self.serial}"
        # return ("[linkMicBaseRole] device_name: %s, uid: %s, channel_id: %s, room_id %s" %
        #         (self.serial, self.uid,self.channel_id,self.room_id) )