# -*- coding: utf8 -*-
import time

from shoots import logger


class BaseRoleInterface:
    """
    社交互动基础连麦角色构造
    """

    def __init__(self, app):
        """
        初始化基础角色接口
        
        Args:
            app: 应用实例
        """
        self.app = app
        self.rpc = app.rpc
        self._serial = None
        self.default_audio = None
        self.default_video = None
        # self._room_id = None
        self._uid = None

    def get_version(self):
        """
        获取版本信息
        
        Returns:
            版本响应信息
        """
        params = {}
        response = self.rpc.request("get_version", params)
        return response

    def set_audio_is_mute(self, is_mute):
        """
        设置音频是否静音
        
        Args:
            is_mute (bool): 是否静音
        """
        self.default_audio = is_mute
        logger.info(f"set role's audio state: {self.default_video}")

    def set_video_is_mute(self, is_mute):
        """
        设置视频是否静音
        
        Args:
            is_mute (bool): 是否静音
        """
        self.default_video = is_mute
        logger.info(f"set role's video state: {self.default_video}")

    def get_app(self):
        """
        获取应用实例
        
        Returns:
            应用实例
        """
        return self.app

    def restart(self):
        """
        重启应用
        """
        self.app.restart()

    @property
    def serial(self):
        """
        获取设备序列号
        
        Returns:
            设备序列号
        """
        return self.app.get_device().serial
        # return self._serial


    # @serial.setter
    # def serial(self,value):
    #     self._serial = value