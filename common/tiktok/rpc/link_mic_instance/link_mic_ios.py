# -*- coding: utf8 -*-
import json
import time
import warnings

from utils.common.log_utils import logger

from common.tiktok.rpc.link_mic_instance.link_mic_base import LinkMicInstance


class LinkMiciOS(LinkMicInstance):
    """
    iOS平台连麦实例类，处理嘉宾/主播在iOS设备上的连麦相关操作
    """

    # -------------------------------------- live base functions ------------------------------------------ #

    def login(self, phone, sm_code, region=86):
        """
        使用手机号和短信验证码登录TikTok账号
        
        :param phone: 手机号码（不带国家码）
        :param sm_code: 短信验证码
        :param region: 国家代码，默认86（中国）
        :return: 登录请求的响应结果
        """
        params = {
            "phone": "+" + str(region) + str(phone),
            "smCode": sm_code,
        }
        response = self.rpc.request("login", params)
        self.restart()
        logger.info(f"[login] device {self.devices_name} login with phone:{phone}, sm_code: {sm_code}")
        return response

    def start_live(self, retry=3):
        """
        开始直播并返回直播间ID
        
        :param retry: 启动失败时的重试次数，默认3次
        :return: 包含直播间信息的响应字典，其中data.room_id为直播间ID
        """
        """
        iOS will return room_id directly.
        :return:

        """

        params = {}
        response = self.rpc.request("startLive", params)
        time.sleep(12)
        room_id = response.get("data").get("roomId")
        response["data"]["room_id"] =  room_id
        return response


    def enter_room_with_check(self,room_id):
        self.enter_room(room_id)

    def leave_room(self):
        """
        离开当前直播间
        
        调用此方法会使当前用户退出直播间，如果是主播则会结束直播
        :return: 退出请求的响应结果
        """
        response = self.rpc.request("leaveRoom", {})
        return response

    # -------------------------------------- link mic core functions ------------------------------------------ #

    def invite_for_multi(self, guest_uid, room_id, position):
        """
        iOS-邀请嘉宾连麦
        :param guest_uid:
        :param room_id:
        :param position:
        :return:
        """
        params = {
            "inviteParam": {
                "uid": guest_uid,
                "roomId": room_id,
                "layoutName": position,
            }
        }
        response = self.rpc.request("invite", params)
        return response

    def apply_for_multi(self, position):
        """
        申请上麦连麦
        
        :param position: 申请的麦位位置，-1表示由客户端自动排序
        :return: 申请请求的响应结果
        """
        params = {
            "ApplyParam": {
                "position": position
            }
        }
        response = self.rpc.request("apply", params)
        return response

    def kick_out_for_multi(self, room_id, guest_uid):
        """
        主播将嘉宾踢出连麦
        
        被踢出的嘉宾不会退出直播间，只是结束连麦状态
        :param room_id: 当前直播间ID
        :param guest_uid: 要踢出的嘉宾用户ID
        :return: 踢人请求的响应结果
        """
        params = {
            "KickOutParams": {
                "uid": guest_uid,
                "roomId": room_id,
            }
        }
        response = self.rpc.request("kickout", params)
        return response

    def reply_invite_for_multi(self, agree, room_id, host_uid):
        """
        :param host_uid:
        :param agree: status = "1" if true, else status = "2"
        :param room_id:
        :return:
        """
        status = "1" if agree else "2"
        params = {
            "replyInviteParams": {
                "status": status,
                "roomId": room_id,
                "uid": host_uid
            }
        }
        response = self.rpc.request("replyInvite", params)
        return response

    def permit_apply_for_multi(self, status, room_id, guest_uid):
        """
        处理嘉宾的上麦申请
        
        :param status: 处理状态，1表示同意，2表示拒绝
        :param room_id: 当前直播间ID
        :param guest_uid: 申请上麦的嘉宾用户ID
        :return: 处理请求的响应结果
        """
        params = {
            "permitApplyParam": {
                "status": status,
                "roomId": room_id,
                "uid": guest_uid
            }
        }
        response = self.rpc.request("permitApply", params)

        return response

    def leave_channel_for_multi(self, leave_source):
        """
        退出连麦频道但不退出直播间
        
        :param leave_source: 退出原因标识
        :return: 退出请求的响应结果
        """
        param = {
            "leaveChannelParam": {
                "leaveSource": leave_source
            }
        }
        response = self.rpc.request("leaveChannel", param)
        return response

    def create_channel_for_multi(self):
        """
        创建连麦频道
        
        用于初始化连麦所需的频道环境
        :return: 创建频道请求的响应结果
        """
        response = self.rpc.request("createChannel", {})
        return response

    def destroy_channel_for_multi(self):
        """
        销毁连麦频道
        
        用于清理连麦频道资源
        :return: 销毁频道请求的响应结果
        """
        response = self.rpc.request("destroyChannel", {})
        return response

    def change_layout_for_multi(self, layout_id, guest_cnt):
        """
        切换连麦布局
        
        :param layout_id: 布局ID，具体布局信息参考文档：https://bytedance.feishu.cn/wiki/wikcnZx6egGl2U6LzdDx6acJRDh
        :param guest_cnt: 嘉宾数量
        :return: 布局切换请求的响应结果
        """
        params = {"layoutId": layout_id}
        response = self.rpc.request("switchLayout", params)
        return response

    def get_current_layout_for_multi(self):
        params = {}
        response = self.rpc.request("getCurrentLayout", params)
        return response

    def get_mic_view_by_pos_for_multi(self, position):
        params = {"position": position}
        response = self.rpc.request("getMicViewByPos", params)
        return response

    def get_all_layout_window_for_multi(self):
        params = {}
        response = self.rpc.request("getAllLayoutWindow", params)
        return response

    def change_max_position_for_multi(self, max_position):
        params = {"maxPosition": max_position}
        response = self.rpc.request("changeMaxPosition", params)
        return response

    def moderator_cancel_invite_for_multi(self):
        params = {}
        resp = self.rpc.request("moderatorKickOut", params)
        return params

    def invite_for_cohost(self, to_uid, to_room_id):
        params = {
            "toUid": to_uid,
            "toRoomId": to_room_id
        }
        resp = self.rpc.request("inviteCohost", params)
        return resp

    def reply_for_cohost(self, agree, to_uid, to_room_id, to_channel_id):
        status = "1" if agree else "2"
        params = {
            "replyStatus": status,
            "toUid" : to_uid,
            "toRoomId": to_room_id,
            "channelId": to_channel_id
        }
        resp = self.rpc.request("replyCohost", params)
        return resp

    def apply_for_cohost(self, to_uid, to_room_id):
        params = {
            "toUid": to_uid,
            "toRoomId": to_room_id
        }
        resp = self.rpc.request("applyCohost", params)
        return resp

    def permit_for_cohost(self, agree, to_uid, to_room_id, to_channel_id):
        status = "1" if agree else "2"
        params = {
            "permitStatus": status,
            "toUid": to_uid,
            "toRoomId": to_room_id,
            "channelId": to_channel_id
        }
        resp = self.rpc.request("permitCohost", params)
        return resp

    def leave_for_cohost(self):
        params = {}
        resp = self.rpc.request("leaveCohost", params)
        return resp

    # -------------------------------------- video & audio ------------------------------------------ #
    def mute_local_audio_for_multi(self, mute):
        """
        控制本地音频静音状态
        
        :param mute: 是否静音，True为静音，False为取消静音
        :return: 静音操作的响应结果
        """
        params = {"audioMute": mute}
        response = self.rpc.request("muteLocalAudio", params)
        return response

    def mute_local_video_for_multi(self, mute):
        """
        控制本地视频静音状态
        
        :param mute: 是否静音，True为关闭视频，False为开启视频
        :return: 视频控制操作的响应结果
        """
        params = {"videoMute": mute}
        response = self.rpc.request("muteLocalVideo", params)
        return response

    def mute_remote_audio_for_multi(self, uid, mute):
        params = {"mute": mute,
                  "uid": uid}
        response = self.rpc.request("muteRemoteAudio", params)
        return response

    def mute_all_remote_audio(self, audio_mute):
        """
        business is no longer use this api
        :param audio_mute:
        :return:
        """
        params = {"mute": audio_mute}
        response = self.rpc.request("muteAllRemoteAudio", params)
        return response

    def get_rtc_version(self):
        return {"data": "1110", "code": "0"}