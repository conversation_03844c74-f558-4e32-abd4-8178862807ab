# -*- coding: utf8 -*-
import json
import os
import re
import time
from typing import *

from utils.common.log_utils import logger
from common.tiktok.rpc.link_mic_instance.link_mic_base import LinkMicInstance



class LinkMicAndroid(LinkMicInstance):


    def leave_room(self):
        """
        leave room, if it's Host then LIVE end
        :return:
        """
        self.rpc.request("leaveRoom",{})
        return True


    def start_live(self):
        response = self.rpc.request("startLive", {})
        time.sleep(15)
        if str(response["code"]) == "0":
            response = self.get_room_info()
        return response

    def enter_room_with_check(self, room_id: str, max_retries: int = 5) -> Optional[str]:
        """
        Attempts to enter the room and verifies whether the entry is successful.

        Args:
            room_id (str): ID of the room to enter.
            max_retries (int): Maximum number of retry attempts if entry fails.

        Returns:
            Optional[str]: Successfully entered room ID, or None if all retries fail.
        """
        retries_left = max_retries

        while retries_left > 0:
            # Try to enter the room
            logger.debug(f"Attempting to enter room: {room_id}, type: {type(room_id)}")
            self.enter_room(room_id)
            time.sleep(5)  # Give some time for the server to update room info

            # Fetch current room ID
            room_detail = self.get_room_detail()
            cur_room_id = (
                room_detail.get("data", {})
                .get("nameValuePairs", {})
                .get("roomId")
            )

            if cur_room_id is not None:
                logger.info(f"Successfully entered room: {cur_room_id}")
                return str(cur_room_id)

            # If not successful, prepare to retry
            retries_left -= 1
            logger.warning(f"Failed to enter room, retries left: {retries_left}")
            time.sleep(10)  # Longer wait before retrying

        # All retries exhausted
        logger.error("Failed to enter room after maximum retries.")
        return None

    def get_room_info(self):
        params = {}
        response = self.rpc.request("getRoomInfo", params)
        return response

    def get_room_detail(self):
        """
        more details from live room.
        ("roomId")
        ("hashtag")
        ("audio_mute")
        ("room_auth_status")
        ("videoOrientation")
        ("roomOrientation")
        ("qualityList")
        ("userAttr")
        ("originRoomInfo")
        :return:
        """
        params = {}
        response = self.rpc.request("getRoomInfoV2", params)
        return response

    # def end_live(self):
    #     """
    #     for host end live, implemented by UI
    #     :return:
    #     """
    #     from testlib.android_lib.UI.live import LiveBaseBroadcastPanel
    #
    #     live_broadcast_panel = LiveBaseBroadcastPanel(root=self.get_app())
    #     live_broadcast_panel.close()



    # -------------------------------------- link mic core functions ------------------------------------------ #
    def invite_for_multi(self, guest_uid, room_id, position="-1", expire_time_in_sec="120"):
        """
        Android-邀请嘉宾连麦
        :param guest_uid:
        :param room_id:
        :param position:
        :param expire_time_in_sec:
        :return:
        """
        param = {
            "inviteParam": {
                "roomId": room_id,
                "uid": guest_uid,
                "layoutName": "FLOAT_FIX",
                "position": position,
                "expireTimeInSeconds": expire_time_in_sec
            }
        }
        response = self.rpc.request("invite", param)
        return response

    def apply_for_multi(self, position):
        """
        申请上麦
        :param position: -1: 客户端顺次排序的位置
        :return:
        """
        params = {
            "ApplyParam": {
                "position": position
            }
        }
        response = self.rpc.request("apply", params)
        return response

    def reply_invitation_with_api(self):
        from cls_uilib.android.panels.guest_accepted_nvitation import GuestAcceptInvitationPanel
        window = GuestAcceptInvitationPanel(root=self.app)
        window.agree_invitation()
        time.sleep(5) # wait for preview window

    def reply_invite_for_multi_by_sdk(self, status, room_id, host_uid):
        """
        回复邀请-嘉宾连麦; UI不同步，如果需要UI变化使用：reply_invitation_by_ui
        :param host_uid:
        :param status: 1=agree;2=reject
        :param room_id:
        :return:
        """
        params = {
            "replyInviteParams": {
                "status": status,
                "roomId": room_id,
                "uid": host_uid
            }
        }
        response = self.rpc.request("replyInvite", params)
        return response

    def change_layout_for_multi(self, layout_id, max_pos="9"):
        """
        :param layout_id:
        :param max_pos: ensure input as a String. For more layout info: https://bytedance.feishu.cn/wiki/wikcnZx6egGl2U6LzdDx6acJRDh
        :return:
        """
        params = {
            "layoutId": layout_id,
            "maxPos": max_pos,
        }
        response = self.rpc.request("changeLayout", params)
        return response

    def cancel_invite_for_multi(self, room_id, guest_uid):
        param = {
            "cancelInviteParam": {
                "roomId": room_id,
                "uid": guest_uid
            }
        }
        response = self.rpc.request("cancelInvite", param)
        return response

    def cancel_apply_for_multi(self):
        """
        guest cancel apply
        :return:
        """
        response = self.rpc.request("cancelApply", {})
        return response

    def kick_out_for_multi(self, room_id, guest_uid, guest_link_mic_id):
        """
        主播踢嘉宾离开连麦，嘉宾并不退出直播间
        :param room_id:
        :param guest_uid:
        :param guest_link_mic_id:
        :return:
        """
        param = {
            "kickOutParam": {
                "roomId": room_id,
                "uid": guest_uid,
                "linkMicId": guest_link_mic_id,
                "reason": 1,  # no meaning for test
            }
        }

        response = self.rpc.request("kickOut", param)
        return response

    def permit_apply_for_multi(self, status, room_id, guest_uid):
        params = {
            "permitApplyParam": {
                "status": status,
                "roomId": room_id,
                "uid": guest_uid
            }
        }
        response = self.rpc.request("permitApply", params)

        return response

    def reply_for_cohost(self,agree,to_room_id, to_uid, to_sec_uid,channel_id):
        status = "agree" if agree else "reject"
        param = {
            "toRoomId": to_room_id,
            "toUid": to_uid,
            "toSecUid": to_sec_uid,
            "innerChannelId": channel_id,
            "replyStatus": status
        }
        response = self.rpc.request("replyCohost", param)
        return response

    def invite_for_cohost(self, to_room_id, to_uid, to_inner_channel_id, to_sec_uid="test_uid"):
        """
        主播邀请；1v1 或者 2v1
        :param to_room_id:
        :param to_uid:
        :param to_sec_uid:
        :param to_inner_channel_id:
        :return: response[data] will include groupChannelId,
        """
        param = {
            "toRoomId": to_room_id,
            "toUid": to_uid,
            "toSecUid": to_sec_uid,
            "innerChannelId": to_inner_channel_id
        }
        response = self.rpc.request("inviteCohost", param)

        return response

    def apply_for_cohost(self, to_room_id, to_uid, inner_channel_id, group_channel_id):
        """
        主播连麦申请加入别的房间
        :param to_room_id:
        :param to_uid:
        :param inner_channel_id:
        :param group_channel_id:
        :return:
        """
        param = {
            "toRoomId": to_room_id,
            "toUid": to_uid,
            "innerChannelId": inner_channel_id,
            "groupChannelId": group_channel_id
        }
        response = self.rpc.request("applyCohost",param)
        return response

    def permit_for_cohost_by_sdk(self, from_room_id, from_uid, channel_id, agree, from_link_mic_id):
        """
        :param from_room_id:
        :param from_uid:
        :param channel_id:
        :param agree:
        :param from_link_mic_id:
        :return:
        """
        status = "agree" if agree else "reject"
        param = {
            "roomId": from_room_id,
            "fromUserId": from_uid,
            "channelId": channel_id,
            "permitStatus": status,
            "fromLinkMicStr": from_link_mic_id
        }

        response = self.rpc.request("permitCohost",param)
        return response

    def permit_for_cohost(self,agree):
        """
        有一个groupChannelId拿不到，先用UI的，回复邀请
        :param agree:
        :return:
        """
        self.permit_for_cohost_by_ui(agree)

    def permit_for_cohost_by_ui(self,agree):
        window = BeInvitedPanelCoHost(root=self.app)
        if agree:
            window.agree_invitation()
            time.sleep(5) # wait for preview window
        else:
            window.reject_invitation()
        return

    def auto_match_for_cohost(self):
        return

    def leave_for_cohost(self,group_channel_id):
        param = {
            "groupChannelId": group_channel_id
        }
        response = self.rpc.request("leaveCohost", param)

        return response


    def leave_channel_for_multi(self, leave_source="layer auto test"):
        """
        only for guest!
        嘉宾离开rtc房间
        :param leave_source:
        :return:
        """
        param = {
            "leaveChannelParam": {
                "leaveSource": leave_source
            }
        }
        response = self.rpc.request("leaveChannel", param)
        return response

    def destroy_channel_for_multi(self):
        """
        only for host!
        主播关闭连麦房间
        :return:
        """

        response = self.rpc.request("leaveChannel", {})
        return response

    def get_first_frame_guest_list_for_multi(self, link_mic_id):
        """
        获取已经拉取到首帧的嘉宾列表
        :param link_mic_id:
        :return:
        """
        param = {
            "link_mic_id": link_mic_id
        }

        response = self.rpc.request("getFirstFrameGuestList", param)
        return response


    # def get_channelid_for_multi(self):
    #     """
    #     获取连麦房间中的channelid
    #     :return:
    #     """
    #     param = {}
    #
    #     response = self.rpc.request("channelId", param)
    #     return response

    # def get_link_info_for_multi(self):
    #     """
    #     获取连麦房间中的连麦详情信息
    #     :return:
    #     """
    #     param = {}
    #
    #     response = self.rpc.request("selfLinkInfo", param)
    #     return response
    # -------------------------------------- layout ------------------------------------------ #

    def get_float_position_by_link_mic_id(self,link_mic_id):
        param = {
            "link_mic_id": link_mic_id
        }
        response = self.rpc.request("getFloatPositionByLinkMicId", param)
        return response

    def get_fixed_position_by_link_mic_id(self, link_mic_id):
        param = {
            "link_mic_id": link_mic_id
        }
        response = self.rpc.request("getFixedPositionByLinkMicId", param)
        return response


    def start_to_show_for_multi(self):
        """
        开启show
        :return:
        """
        param = {}

        response = self.rpc.request("startToShow", param)
        return response

    def switch_to_normal_layout_for_multi(self):
        """
        恢复默认布局
        :return:
        """
        param = {}

        response = self.rpc.request("switchToNormalLayout", param)
        return response

    def get_layout_max_mic_count_for_multi(self, layout_id):
        """
        获取layout最大连麦位数
        :return:
        """
        param = {
            "layoutId": layout_id
        }

        response = self.rpc.request("getLayoutMaxMicCountById", param)
        return response

    def get_version_by_layout_id_for_multi(self, layout_id):
        """
        获取version
        :return:
        """
        param = {
            "layoutId": layout_id
        }

        response = self.rpc.request("getVersionByLayoutId", param)
        return response

    def get_window_by_pos_for_multi(self, pos):
        """
        按麦位获取窗口
        :return:
        """
        param = {
            "pos": pos
        }

        response = self.rpc.request("getWindowByPos", param)
        return response

    def get_current_layout_config_for_multi(self):
        """
        获取当前layout配置
        :return:
        """
        param = {}

        response = self.rpc.request("getCurrentLayoutConfig", param)
        return response

    def get_current_ui_layout_config_for_multi(self):
        """
        获取当前UI layout配置
        :return:
        """
        param = {}

        response = self.rpc.request("getCurrentUILayoutConfig", param)
        return response

    def is_valid_layout_id_for_multi(self, layout_id):
        """
        获取当前layout_id是否有效
        :return:
        """
        param = {
            "layoutId": layout_id
        }

        response = self.rpc.request("isValidLayoutId", param)
        return response

    def get_live_users_position_map_for_multi(self):
        """
        获取直播间用户麦位
        :return:
        """
        param = {}

        response = self.rpc.request("getLiveUsersPositionMap", param)
        return response

    # -------------------------------------- UserManager ------------------------------------------ #
    def get_link_user_by_link_mic_id(self, link_mic_id):
        """
        获取直播间用户信息
        :return:
        """
        param = {
            "link_mic_id": link_mic_id
        }

        response = self.rpc.request("getLinkUserByLinkMicId", param)
        return response

    def get_link_user_by_uid(self, uid):
        """
        获取直播间用户信息
        :return:
        """
        param = {
            "uid": uid
        }

        response = self.rpc.request("getLinkUserByUid", param)
        return response

    def get_linked_list(self):
        """
        获取直播间用户列表
        :return:
        """
        param = {}

        response = self.rpc.request("getLinkedList", param)
        return response

    def get_has_invited_uid_set(self):
        """
        获取已邀请列表
        :return:
        """
        param = {}

        response = self.rpc.request("getHasInvitedUidSet", param)
        return response

    def get_has_invited_uid_set_for_moderator(self):
        """
        获取已邀请列表
        :return:
        """
        param = {}

        response = self.rpc.request("getHasInvitedUidSetForModerator", param)
        return response

    def get_fixed_position(self, link_mic_id):
        """
        获取已邀请列表
        :return:
        """
        param = {
            "link_mic_id": link_mic_id
        }

        response = self.rpc.request("getFixedPosition", param)
        return response

    def get_float_position(self, link_mic_id):
        """
        获取已邀请列表
        :return:
        """
        param = {
            "link_mic_id": link_mic_id
        }

        response = self.rpc.request("getFloatPosition", param)
        return response

    def fetch_latest_states(self, link_mic_id):
        """
        获取已邀请列表
        :return:
        """
        param = {}

        response = self.rpc.request("fetchLatestStates", param)
        return response

    def chane_state(self, state_type, state):
        """
        获取已邀请列表
        :return:
        """
        param = {
            "state_type": state_type,
            "state": state
        }

        response = self.rpc.request("chaneState", param)
        return response

    # -------------------------------------- moderator ------------------------------------------ #
    def moderator_invite_for_multi(self, guest_uid, room_id, position, expire_time_in_sec="120"):
        params = {
            "inviteParam":{
                "roomId": room_id,
                "uid": guest_uid,
                "layoutName": "FLOAT_FIX",
                "expireTimeSeconds": expire_time_in_sec,
                "position": position
            }
        }
        resp = self.rpc.request("moderatorInvite", params)
        return resp

    def moderator_permit_apply_for_multi(self, agree, room_id, uid):
        status = "1" if agree else "2"
        params = {
            "permitApplyParam": {
                "status": status,
                "roomId": room_id,
                "uid": uid
            }
        }
        resp = self.rpc.request("moderatorInvite", params)
        return resp

    def moderator_kick_out_for_multi(self, room_id, uid, link_mic_id, reason = "test client"):
        params = {
            "kickOutParam": {
                "roomId": room_id,
                "uid": uid,
                "linkMicId": link_mic_id,
                "reason": reason
            }
        }
        resp = self.rpc.request("moderatorKickOut", params)
        return resp

    def enable_moderator(self,enable=True):
        resp = self.rpc.request("enableModerator",{"enable": enable})
        return resp

    # -------------------------------------- video & audio ------------------------------------------ #
    def mute_local_video_for_multi(self, mute, is_guest=False):
        """
        开关本地摄像头
        :param mute: true = mute/turn off camera; false = unmute/turn on camera
        :param is_guest:
        :return:
        """
        # should be in local test channel, otherwise mute will not work
        params = {
            "videoMute": mute,
            "isGuest": is_guest,
        }
        response = self.rpc.request("muteLocalVideo", params)
        return response

    def mute_local_audio_for_multi(self, mute):
        """
        嘉宾mute自己，主播不可用
        :param mute: true = mute; false = unmute
        :return:
        """
        param = {
            "audioMute": mute,
        }
        response = self.rpc.request("muteLocalAudio", param)
        return response

    def mute_remote_audio_for_multi(self, mute, guest_link_mic_id):
        """
        关闭远端音屏，RD开发未完成，只做到了从RTC层面mute，业务状态并不能同步
        :param mute:
        :param guest_link_mic_id:
        :return:
        """
        param = {
            "videoMute": mute,
            "interactId": guest_link_mic_id
        }

        response = self.rpc.request("muteRemoteAudio", param)
        return response

    def get_rtc_version(self):

        # response = self.rpc.request("getRTCVersion",{})
        response = {"data":"rust demo"}
        return response

    def get_cur_rtc_uid(self):

        response = self.rpc.request("getCurrentLinkMicID",{})
        return response

    def get_usr_map(self):
        """
        主播获取当前连麦中包含嘉宾的所有信息
        :return:
        """
        response = self.rpc.request("getUserMap",{})
        return response
    # -------------------------------------- Android OS log info ------------------------------------------ #

    def get_audio_state(self):
        content = self._update_audio_log()
        states = self._parse_audio_content(content)
        state = self._get_cur_audio_state(states)
        return state

    def _update_audio_log(self):
        s = 'Audio event log: recording activity received by AudioService'
        e = 'AudioDeviceBroker:'
        pattern = f""" | grep -A100000 "{s}"  | sed -n '/{s}/,/{e}/p' | grep -v "{s}" | grep -v "{e}"
                """
        cmd = "audio" + pattern
        ret = self.device.adb.dumpsys(cmd)
        return ret

    def test_audio_log(self):
        return self._update_audio_log()

    def _parse_audio_content(self, content):
        res = []
        for line in content.split('\n'):
            match = re.search(r'rec\s(\w+)\sriid:\d+\suid:\d+\ssession:\d+\ssrc:\w+\spack:(\S+)', line)
            if match:
                cur_state = (match.group(1), match.group(2))
                res.append(cur_state)
        return res[::-1]

    def get_video_state(self):
        content = self._update_video_log()
        states = self._parse_video_content(content)
        state = self._get_cur_camera_state(states)
        return state

    def _get_cur_audio_state(self, states):
        for cur_state in states:
            if cur_state[1] == self.app.package_name:
                return False if cur_state[0] == 'stop' else True
        logger.info(f"did not find package : {self.app.package_name} used device audio recording record")
        return False

    def _update_video_log(self):
        s = '== Camera service events log (most recent at top): =='
        e = '== Camera device 0 dynamic info: =='
        pattern = f""" | grep -A100000 "{s}"  | sed -n '/{s}/,/{e}/p' | grep -v "{s}" | grep -v "{e}"
        """
        cmd = "media.camera" + pattern
        ret = self.device.adb.dumpsys(cmd)
        return ret

    def _parse_video_content(self, content):
        pattern = r'(\d\d-\d\d \d\d:\d\d:\d\d) : (CONNECT|DISCONNECT) device \d+ client for package (\S+) \(PID \d+\)'
        matches = re.findall(pattern, content)
        return matches

    def _get_cur_camera_state(self, states):
        for cur_state in states:
            if cur_state[2] == self.app.package_name:
                return True if cur_state[1] == 'CONNECT' else False
        logger.info(f"did not find package : {self.app.package_name} used device camera record")
        return False

    # -------------------------------------- HTTP Test (Debug Only) ------------------------------------------ #
    def reply_invite_only_server(self, status, room_id, host_uid):
        param = {
            "replyInviteParam": {
                "roomId": room_id,
                "uid": host_uid,
                "status": status
            }
        }
        response = self.rpc.request("replyInviteOnlyServer", param)
        return None

    def apply_only_server(self, position):
        param = {
            "applyParam": {
                "position": position
            }
        }
        response = self.rpc.request("applyOnlyServer", param)
        return response

    def cancel_apply_only_server(self):
        response = self.rpc.request("cancelApplyOnlyServer", {})
        return response

    def create_channel_only_server(self):
        response = self.rpc.request("createChannelOnlyServer", {})
        return response

    def permit_apply_only_server(self, status, room_id, guest_uid):

        params = {
            "permitApplyParam": {
                "status": status,
                "roomId": room_id,
                "uid": guest_uid
            }
        }
        response = self.rpc.request("permitApplyOnlyServer", params)

        return response

    def invite_only_server(self, guest_uid, room_id, position):
        params = {
            "inviteParam": {
                "uid": guest_uid,
                "roomId": room_id,
                "layoutName": "FLOAT_FIX",
                "position": position
            }
        }
        response = self.rpc.request("inviteOnlyServer", params)
        return response

    def cancel_invite_only_server(self):
        """
        sdk层目前可以做到重入
        :return:
        """
        return NotImplementedError

    def change_layout_only_server(self, layout_id, uid, max_position):
        param = {
            "layoutId": layout_id,
            "uid": uid,
            "maxPosition": max_position
        }
        response = self.rpc.request("changeLayoutOnlyServer", param)
        return response

    def recharge(self, recharge_type):
        """
        :param recharge_type:
        invite = only for guest, who miss invite Msg
        reply = only for host, who miss reply Msg
        :return:
        """
        params = {
            "rechargeType": recharge_type
        }
        response = self.rpc.request("recharge", params)
        return response
