"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/common/tiktok/android/popups.py
Description: 
"""
from uibase.controls import PopupBase
from uibase.upath import UPath, text_, id_, visible_, desc_, class_
from utils.common.log_utils import logger


class CommonPopup(PopupBase):
    def handle(self):
        for elem_name in self.get_locators().keys():
            if self[elem_name].existing and self[elem_name].visible:
                logger.info("[弹窗] %s 已处理" % self.__class__.__name__)
                return self[elem_name].click()
        return False
    
    def handler(self):
        self.handle()


class IntellectualPropertyPolicyUpdatePopup(CommonPopup):
    """知识产权政策更新弹窗"""

    def get_locators(self):
        return {
            "知道了-知识产权政策更新弹窗": {"path": UPath(text_ == "知道了")}
        }

class SwipeToViewMorePopup(CommonPopup):
    """上滑查看更多视频弹窗"""

    def get_locators(self):
        return {
            "上滑查看更多视频": {"path": UPath(text_ == "上滑查看更多视频")}
        }

class SecurityCheckPopup(CommonPopup):
    """安全检查弹窗"""

    def get_locators(self):
        return {
            "关闭-让我们快速做个安全检查1": {"path": UPath(desc_ == "关闭") / 0},   # 一个界面有多个关闭按钮
            "关闭-让我们快速做个安全检查2": {"path": UPath(id_ == "nav_end", visible_ == True) / 0}
        }

class BindEmailPopup(CommonPopup):
    """绑定电子邮箱地址弹窗"""    

    def get_locators(self):
        return {
            "暂时不要-绑定电子邮箱地址弹窗": {"path": UPath(text_ == "暂时不要")}
        }
    
class FacebookFriendsPopup(CommonPopup):
    """允许 TikTok 访问你的 Facebook 好友列表和电子邮件弹窗"""

    def get_locators(self):
        return {
            "不允许-允许 TikTok 访问你的 Facebook 好友列表和电子邮件弹窗": {"path": UPath(text_ == "不允许")}
        }

class FollowFriendsPopup(CommonPopup):
    """关注你的好友弹窗"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.splash.SplashActivity#1"}

    def get_locators(self):
        return {
            "关闭-关注你的好友弹窗": {"path": UPath(id_ == "close_icon_view")}
        }
    
class CloseLiveStudioPopup(CommonPopup):
    """关闭-直播工作室现已上线弹窗"""

    def get_locators(self):
        return {
            "关闭-直播工作室现已上线1": {"path": UPath(text_ == "暂时不要")},
            "关闭-直播工作室现已上线2": {"path": UPath(id_ == "ttlive_dialog_game_content_close")}
        }
    
class NearbyDevicesPopup(CommonPopup):
    """允许-允许 TikTok 访问附近的设备弹窗"""

    def get_locators(self):
        return {
            "允许-允许 TikTok 访问附近的设备": {"path": UPath(text_ == "允许")}
        }
    
class PrivacyAgreementPopup(CommonPopup):
    """同意隐私协议弹窗"""

    def get_locators(self):
        return {
            "同意并继续-同意隐私协议弹窗": {"path": UPath(text_ == "同意并继续")}
        }
    
# useless
class SendGiftPopup(CommonPopup):
    """送出价值 1 枚金币的礼物-送出第一份礼物弹窗"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.LivePlayActivity#1"}

    def get_locators(self):
        return {
            "送出价值 1 枚金币的礼物-送出第一份礼物1": {"path": UPath(id_ == "gift_price")},
            "送出价值 1 枚金币的礼物-送出第一份礼物2": {"path": UPath(id_ == "giftPriceLayout")}
        }
    
class SelectTheContentYouAreInterestedInPopup(CommonPopup):
    """选择你感兴趣的内容弹窗"""

    def get_locators(self):
        return {
            "跳过-选择你感兴趣的内容": {"path": UPath(text_ == "跳过")}
        }
    
class DogfoodUpdateAvailablePopup(CommonPopup):
    """Dogfood update available!"""
    window_spec = {"activity": (
        "com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivityWithNoAnimation#1|"
        "com.ss.android.ugc.aweme.account.login.auth.I18nSignUpActivity#1"
    )}

    def get_locators(self):
        return {
            "No-Dogfood update available!": {"path": UPath(text_ == "No")}
        }

    
class EndAnotherDeviceLivePopup(CommonPopup):
    """结束另一台设备上的直播弹窗"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.adaptation.saa.*"}

    def get_locators(self):
        return {
            "结束-结束另一台设备上的直播": {"path": UPath(text_ == "立即结束")}
        }
    

class ContinueLivePopup(CommonPopup):
    """继续直播弹窗"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.adaptation.saa.*"}

    def get_locators(self):
        return {
            "继续-在其他设备在直播,是否继续直播": {"path": UPath(text_ == "继续")}
        }
    

class ResumeLivePopup(CommonPopup):
    """恢复直播弹窗"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.adaptation.saa.*"}

    def get_locators(self):
        return {
            "恢复-直播已暂停是否恢复": {"path": UPath(text_ == "恢复")}
        }


class DeviceLiveHasEndedPopup(CommonPopup):
    """此设备上的直播已结束"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.*"}

    def get_locators(self):
        return {
            "确定-此设备上的直播已结束": {"path": UPath(text_ == "确定")}
        }
    
class LiveAccessAgeVerifirPopup(CommonPopup):
    """必须年满18周岁才能访问直播"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.live.*"}

    def get_locators(self):
        return {
            "继续-必须年满18周岁才能访问直播": {"path": UPath(text_ == "继续")}
        }
    
class BlockUnpopularCommentsPopup(CommonPopup):
    """屏蔽不受欢迎的评论"""
    window_spec = {"activity": "com.ss.android.ugc.aweme.adaptation.saa.*"}

    def get_locators(self):
        return {
            "关闭-屏蔽不受欢迎的评论": {"path": UPath(text_ == "关闭")}
        }
    

class ErrCodeSandboxPopup(CommonPopup):
    '''
    错误码沙盒弹窗页面
    '''
    def get_locators(self):
        return {
            "NEVER_SHOW_THIS": {'path': UPath(text_ == "NEVER SHOW THIS")}
        }

    
class DisableSaveLoginInfoPopup(CommonPopup):
    '''
    保存登录信息以供下次使用弹窗页面  - 暂时不要
    '''
    def get_locators(self):
        return {
            "暂时不要-保存登录信息": {'path': UPath(text_ == "暂时不要")}
        }

class LiveContentCompliancePopup(CommonPopup):
    '''
    please make sure youre live dos NOT Contain any Company confidential infomation
    中文: 请确保您的直播内容不包含任何公司机密信息
    '''
    def get_locators(self):
        return {
            "确认-直播内容不包含任何公司机密信息": {'path': UPath(text_ == "确认")}
        }
    
class PuzzleVerifyGopup(CommonPopup):
    '''
    拼图验证弹窗, 一般是在登录后在首页出现的 (对抗风控)
    '''
    def get_locators(self):
        return {
            "关闭拼图弹窗": {'path': UPath(class_ == "verify-bar-close--icon")}
        }