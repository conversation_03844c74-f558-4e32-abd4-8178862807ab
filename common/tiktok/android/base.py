"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/common/tiktok/android/base.py
Description: 
"""
from typing import Tuple

from shoots_android.androidtestbase import AndroidTestBase
from common.tiktok.android.app import AndroidTikTokApp
from uibase.device import Device
from utils.common.log_utils import logger


class TTAndroidPerfAppTestBase(AndroidTestBase):
    """Android 测试基类"""

    def __init__(self, *args, **kwargs):
        super(TTAndroidPerfAppTestBase, self).__init__(*args, **kwargs)
        self.android_device = None
        self.android_app = None

    def _android_init_device(self, device_conditions=None):
        """初始化 Android 设备"""
        self.android_device = self.acquire_device(device_conditions)
        return self.android_device

    def _android_init_app(self, device, version_ids=None, uid=None):
        """初始化 Android 应用"""
        if version_ids:
            self.android_app = AndroidTikTokApp(device, version_ids=version_ids, uid=uid)
            logger.debug(f"[{device.udid}] 开始配置Libra实验，实验: {version_ids}")
            self.android_app.config_libra()
        else:
            self.android_app = AndroidTikTokApp(device)
        return self.android_app

    def init_android_env(self, udid=None, version_ids=None, uid=None) -> Tuple[Device, AndroidTikTokApp]:
        """初始化 Android 环境"""
        logger.debug(f"[{udid}] 开始初始化设备...")
        device = self._android_init_device({"udid": udid, "type": "Android"})
        logger.debug(f"[{udid}] 开始初始化app...")
        app = self._android_init_app(device, version_ids, uid)

        return device, app