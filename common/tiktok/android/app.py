"""
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-11-16 13:58:26
FilePath: /global_business_perf/common/tiktok/android/app.py
Description: 
"""
import time

from shoots.retry import Retry
from shoots_libra.app import LibraAppMixin
from shoots_android_byted.app import AndroidAppMixin
from api_test.app import APITestAppMixin
from shoots_android.androidapp import AndroidApp, AccessibilityApp
from common.tiktok.android.popups import *
from utils.common.log_utils import logger
from common.tiktok.rpc.link_mic_instance.link_mic_android import LinkMicAndroid

class AndroidTikTokApp(AndroidApp, AndroidAppMixin, APITestAppMixin, LibraAppMixin):
    app_spec = {
        "package_name": "com.zhiliaoapp.musically",  # app多个包名，从左到右查找已安装的包
        "android_init_device": True,  # 是否唤醒设备
        "process_name": "",  # 应用的主进程名称
        "start_activity": "",  # 将其留空以便自动检测
        "grant_all_permissions": True,  # 在启动应用程序之前授予所有权限
        "clear_data": False,  # 清除应用数据
        "kill_process": True,  # 是否终止以前启动的应用
        "libra_global_reverse": True,  # 是否开启libra全局反向代理
        "libra_appid": 22, # AB后台内部id
        "libra_region": "sg"  # 默认是None，表示使用native region
    }
    app_popup_classes = [
        DogfoodUpdateAvailablePopup,
        IntellectualPropertyPolicyUpdatePopup,
        SecurityCheckPopup,
        BindEmailPopup,
        FacebookFriendsPopup,
        FollowFriendsPopup,
        CloseLiveStudioPopup,
        NearbyDevicesPopup,
        PrivacyAgreementPopup,
        SelectTheContentYouAreInterestedInPopup,
        EndAnotherDeviceLivePopup,
        ContinueLivePopup,
        DeviceLiveHasEndedPopup,
        LiveAccessAgeVerifirPopup,
        BlockUnpopularCommentsPopup,
        ErrCodeSandboxPopup,
        DisableSaveLoginInfoPopup,
        LiveContentCompliancePopup
    ]
    system_popup_rules = [
        {
            "match": {"label": "一步小窗"},
            "popup_params": {"button_texts": ["稍后提示"]}
        },
        {
            "match": {"label": "是否允许访问手机数据？"},
            "popup_params": {"button_texts": ["允许"]}
        },
        {
            "match": {"label": "允许"},
            "popup_params": {"button_texts": ["允许"]}
        }
    ]

    def __init__(self, *args, **kwargs):
        super(AndroidTikTokApp, self).__init__(*args, **kwargs)
        self.link_mic_client: LinkMicAndroid = None
        self.huawei_risk_alert_flag = False

    def _init_linc_mic_client(self):
        return LinkMicAndroid(self)
    
    def restart(self, delay=15):
        """
        重启应用并进行初始化处理：
        1. 重置 link_mic_client (已注释逻辑)
        2. 检查并处理华为手机的风险提示弹窗（仅处理一次）
        """
        super().restart()
        logger.debug(f"Restarting app... waiting {delay}s")
        time.sleep(delay)
        # logger.debug("init link_mic_client...")
        # self.link_mic_client = self._init_linc_mic_client()

        self._handle_huawei_risk_alert_if_needed()


    def _handle_huawei_risk_alert_if_needed(self):
        """
        如为华为设备且未处理过风险提示弹窗，则处理该弹窗
        """
        device = self.get_device()
        brand = device.brand or ""
        if brand.lower() != "huawei" or self.huawei_risk_alert_flag:
            return

        logger.info("Handling Huawei risk alert...")
        from common.tiktok.android.panels.huawei_alert import HuaweiRiskAlert
        self.system_app = HuaweiSystemUIApp(device)
        huawei_alert = HuaweiRiskAlert(root=self.system_app)
        self.huawei_risk_alert_flag = huawei_alert.click_huawei_risk_alert()

    def before_launch_app(self):
        self.disable_sso_auth()

    def get_uid(self) -> str:
        """
        获取用户uid
        """
        uid = self.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="getUid")   # 7132733316667819051
        logger.debug(f"uid result: {uid}")
        return uid
    
    def get_did(self):
        """获取设备did
        :return:
        """
        return self.get_device_did()
    
    def on_app_popup(self):
        """
        处理应用中的弹窗，以避免影响自动化流程的稳定性。

        该方法最多尝试处理 3 次弹窗：
        - 如果某次尝试中未检测到可处理的弹窗，则提前终止；
        - 如果检测到无法自动处理的弹窗（抛出 AppPopupNotHandled 异常），也会立即终止。

        注意事项：
        1. Android 平台上，大多数弹窗会被控件的 `wait_for_visible` 自动识别并触发处理逻辑；
        2. iOS 平台存在差异：即使有弹窗遮挡，控件的 `wait_for_visible` 仍可能返回 True,
        导致控件可见性判断失效，因此需要额外调用 `on_app_popup` 主动处理弹窗。
        """

        logger.debug("操作前先主动处理一波弹窗")
        
        for attempt in range(3):
            try:
                if not super().on_app_popup():
                    logger.debug(f"No pop-up handled on attempt {attempt + 1}")
                    break
            except Exception as e:
                break
    
    def open_scheme(self, schema_url, activity=None, timeout=10):
        self.on_app_popup()
        logger.info(f"Open scheme URL: {schema_url}")
        return super().open_scheme(schema_url, activity, timeout)


    def login(
            self,
            phone: str,
            sm_code: str,
            region: int = 86,
            retries: int = 3
        ) -> str:
        """
        使用ttmock进行登录，支持登录态检测与自动重试。 

        为了避免账号重复登录，该方法会先判断是否已处于登录状态，若未登录则执行登录流程。
        登录过程中会自动进行失败重试，最多尝试指定次数（默认为 3 次）。
        登录成功后返回当前登录用户的 UID，失败则抛出异常。

        Tips:
            1. 自动登录后,需要重启应用,否则登录态无法生效

        Args:
            phone (str): 登录手机号
            sm_code (str): 短信验证码
            region (int): 区号，默认 86（中国）
            retries (int): 登录最大重试次数（外层 Retry 循环）

        Returns:
            bool: 登录成功返回 True，否则返回 False
        """

        logger.debug(f"login... {phone}/{sm_code}")
        attempt_count = 0

        for _ in Retry(limit=retries, raise_error=False):

            # Call login method
            self.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="autoLogin", args=[str(phone), str(sm_code), int(region)])
            logger.info(f"等待登录是否完成, 20s")
            time.sleep(20)  # Wait for login to take effect

            if self.is_login():
                return True
                        
            logger.warning(f'Attempt #{attempt_count + 1} to login: {phone}/{sm_code}/{region}')
            attempt_count += 1

            self.restart()

        logger.error(f"Failed to auto login after {retries} attempts")
        return False

    def logout(self, retries=3, interval=5):
        """
        使用ttmock退出登录
        """
        logger.debug("logout...")
        for _ in Retry(limit=retries, raise_error=False):
            self.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="logout")

            time.sleep(interval)  # important 等待登出

            if not self.is_login():
                return True

            logger.warning("logout failed, try again...")

        return False

    def is_login(self) -> bool:
        """
        使用ttmock判断是否登录
        """
        result = self.call_method(class_name="com.ss.android.ugc.aweme.local_test.impl.ttmock.TTMockOpenApi", method="isLogin")  # true
        logger.debug(f"is_login result: {result}")
        return result == "true"

class AndroidSystemUIApp(AccessibilityApp, AndroidAppMixin):
    app_spec = {
        "package_name": "com.android.systemui",  # app多个包名，从左到右查找已安装的包
        "android_init_device": False,  # 是否唤醒设备
        "process_name": "",  # 应用的主进程名称
        "start_activity": "",  # 将其留空以便自动检测
        "grant_all_permissions": False,  # 在启动应用程序之前授予所有权限
        "clear_data": False,  # 清除应用数据
        "kill_process": False  # 是否终止以前启动的应用
    }
    popup_rules = []

    def before_launch_app(self):
        self.disable_sso_auth()

class AndroidSystemSettingsApp(AccessibilityApp, AndroidAppMixin):
    app_spec = {
        "package_name": "com.android.settings",  # app多个包名，从左到右查找已安装的包
        "android_init_device": True,  # 是否唤醒设备
        "process_name": "",  # 应用的主进程名称
        "start_activity": "",  # 将其留空以便自动检测
        "grant_all_permissions": True,  # 在启动应用程序之前授予所有权限
        "clear_data": False,  # 清除应用数据
        "kill_process": False  # 是否终止以前启动的应用
    }
    popup_rules = []

    def before_launch_app(self):
        self.disable_sso_auth()


class HuaweiSystemUIApp(AccessibilityApp, AndroidAppMixin):
    app_spec = {
        "package_name": "com.huawei.systemmanager",  # app多个包名，从左到右查找已安装的包
        "android_init_device": False,  # 是否唤醒设备
        "process_name": "",  # 应用的主进程名称
        "start_activity": "",  # 将其留空以便自动检测
        "grant_all_permissions": False,  # 在启动应用程序之前授予所有权限
        "clear_data": False,  # 清除应用数据
        "kill_process": False  # 是否终止以前启动的应用
    }