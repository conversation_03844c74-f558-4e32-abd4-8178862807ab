import time
from shoots_android.control import *
from uibase.upath import text_, UPath
from utils.common.log_utils import logger

class AndroidDebugPanel(Window):
    window_spec = {

    }
    
    def get_locators(self):
        return {
            "主页": {"path": UPath(text_ == "主页")},
            "Kit": {"path": UPath(text_ == "Kit")},
            "Enable_ENV_Tools": {"path": UPath(text_ == "Env Tools(Enable BOE&PPE)")},
            "Link_to_ET_Platform": {"path": UPath(text_ == "Link to ET Platform")},
            # DebugToast 相关元素
            'ET_toast': {"type": Control, "path": UPath(text_ == "Set host successfully")},
        }

    def check_ET_toast(self):
        """
        检查ET toast是否出现
        """
        return self['ET_toast'].wait_for_existing(timeout=5, interval=0.1, raise_error=False)

    def _enter_debug_panel(self, max_retries=2):
        for attempt in range(1, max_retries + 1):
            self.app.open_scheme("snssdk1180://feed")  # 进入首页

            if self["主页"].wait_for_visible(timeout=5):
                self["主页"].long_click(duration=3)

            time.sleep(5)

            if self["Kit"].wait_for_visible(timeout=3, raise_error=False):
                logger.info(f"[{self.device.udid}]成功进入Debug页")
                return

            if attempt < max_retries:
                logger.warning(f"[{self.device.udid}][{attempt}/{max_retries}] 未进入Kit页, 准备重试...")
            else:
                logger.error(f"[{self.device.udid}][{attempt}/{max_retries}] 最后一次尝试仍未进入Kit页, 终止")
                raise RuntimeError("进入Debug页失败")

    def enable_link_to_ET(self):

        def _click_ET_btn():
            """
            点击ET按钮, 需要一定的偏移量, 防止点击了上层的一些浮窗(比如环境浮窗)
            """
            btn = self["Link_to_ET_Platform"]

            from uibase.common import Rectangle
            screen_rect: Rectangle = self.device.screen_rect
            logger.debug(f"screen_rect: {screen_rect}")

            rect: Rectangle = btn.rect
            y = rect.center[1]
            x = screen_rect.width - 100
            logger.debug(f"x: {x}, y: {y}")
            self.device.click(x, y)

        logger.info(f"[{self.device.udid}]进入Debug页...")
        self._enter_debug_panel()

        for attempt in range(5):
            logger.debug(f"[{self.device.udid}]第 {attempt + 1} 次尝试查找 Link_to_ET_Platform 按钮...")
            if self["Link_to_ET_Platform"].wait_for_visible(timeout=3, raise_error=False):

                for i in range(4):
                    _click_ET_btn()
                    if self.check_ET_toast():
                        logger.info(f"[{self.device.udid}]成功开启Link_to_ET_Platform")

                        time.sleep(1)
                        logger.info(f"[{self.device.udid}]返回主页...")
                        self.device.back()
                        return

            logger.debug(f"[{self.device.udid}]未找到 Link_to_ET_Platform, 执行第 {attempt + 1} 次滑动后重试...")
            self.scroll(coefficient_y=0.5)

        logger.error(f"[{self.device.udid}]开启Link_to_ET_Platform失败")
        raise RuntimeError("开启Link_to_ET_Platform失败")
    

    def enable_env_tools(self, lane: str):
        logger.info(f"[{self.device.udid}]进入Debug页...")
        self._enter_debug_panel()

        for _ in range(4):
            if self["Enable_ENV_Tools"].wait_for_visible(timeout=3, raise_error=False):

                setattr(self, "lane", lane)
                for i in range(4):
                    self["Enable_ENV_Tools"].click()

                    if self.lane.wait_for_visible(timeout=3, raise_error=False):
                        logger.info(f"[{self.device.udid}]成功开启Enable_ENV_Tools")
                        return

            self.scroll(coefficient_y=0.5)

        logger.error(f"[{self.device.udid}]开启Enable_ENV_Tools失败")
        raise RuntimeError("开启Enable_ENV_Tools失败")
