"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/android/panels/anchor_host.py
Description: 
"""
import time

from shoots_android.control import *
from uibase.upath import *
from utils.common.log_utils import logger


class AndroidAnchorHostPanel(Window):
    """
    主播连麦页面
    """

    window_spec = {
        "activity": (
            "com.ss.android.ugc.aweme.live.LiveBroadcastSceneWrapperActivity|"
            "com.ss.android.ugc.aweme.live.LiveBroadcastSceneWrapperActivity#1|"
            "com.ss.android.ugc.aweme.live.LiveBroadcastSceneWrapperActivity#2|"
            "com.ss.android.ugc.aweme.live.LiveBroadcastActivity|"
            "com.ss.android.ugc.aweme.live.LiveBroadcastActivity#1|"
            "com.ss.android.ugc.aweme.live.LiveBroadcastActivity#2|"
            "com.ss.android.ugc.aweme.adaptation.saa.SAAActivity"
        )
    }

    def get_locators(self):
        return {
            "与主播连麦按钮_1": {"path": UPath(text_ == "+主播")},
            "与主播连麦按钮_2": {"path": UPath(id_ == "left_container") / 0},
            "搜索连麦按钮": {"path": UPath(text_ == "搜索")},
            "搜索连麦对象输入框": {"path": UPath(type_ == "LiveEditText")},
            "邀请主播连麦按钮": {"path": UPath(text_ == "邀请")},
            "接受主播连麦按钮": {"path": UPath(text_ == "接受")},
            "断开主播连麦按钮": {"path": UPath(text_ == "断开连线")},
            "PK按钮_1": {"path": UPath(id_ == "iv_icon")},
            "PK按钮_2": {"path": UPath(text_ == "PK")},
            "与主播直播连麦": {"path": UPath(text_ == "与主播直播连麦")}
        }

    def click_more_btn(self):
        """
        通过与主播直播连麦按钮, 点击更多按钮
        """
        btn = self["与主播直播连麦"]
        if btn.wait_for_visible(timeout=5):
            from uibase.common import Rectangle
            screen_rect: Rectangle = self.device.screen_rect
            logger.debug(f"screen_rect: {screen_rect}")

            rect: Rectangle = btn.rect
            y = btn.rect.center[1]
            x = screen_rect.width - rect.left - 30
            logger.debug(f"x: {x}, y: {y}")
            self.device.click(x, y)
            time.sleep(1)


    def invite_anchor_connect(self, anchor_name):
        """
        邀请主播连麦
        """
        elements = [self["与主播连麦按钮_1"], self["与主播连麦按钮_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][连麦] 点击与主播连麦按钮")
                time.sleep(2)
                break

        logger.info(f"[{self.device.udid}]点击更多按钮")
        self.click_more_btn()

        if self["搜索连麦按钮"].wait_for_visible(timeout=5):
            self["搜索连麦按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击搜索图标")

        if self["搜索连麦对象输入框"].wait_for_visible(timeout=5):
            self["搜索连麦对象输入框"].input(anchor_name)
            logger.info(f"[{self.device.udid}][连麦] 输入主播名称:{anchor_name}")

        time.sleep(6)
        if self["邀请主播连麦按钮"].wait_for_visible(timeout=8):
            self["邀请主播连麦按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击邀请连麦按钮")

    def accept_anchor_connect(self):
        """
        接受主播连麦
        """
        if self["接受主播连麦按钮"].wait_for_visible(timeout=10):
            self["接受主播连麦按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击接受连麦按钮")

    def reject_anchor_connect(self):
        """
        拒绝主播连麦
        """
        # TODO: 实现拒绝连麦逻辑

    def disconnect_anchor_host(self):
        """
        断开直播连麦
        """
        elements = [self["与主播连麦按钮_1"], self["与主播连麦按钮_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][连麦] 点击与主播连麦按钮")
                time.sleep(2)
                break

        if self["断开主播连麦按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["断开主播连麦按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击断开连麦按钮")
        if self["断开连线按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["断开连线按钮"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击确认断开按钮")

    def assert_anchor_host_connect_1v1(self):
        """
        断言主播连麦成功
        """
        time.sleep(5)
        elements = [self["PK按钮_1"], self["PK按钮_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                logger.info(f"[{self.device.udid}][连麦] 断言主播连麦成功")
                return True
        logger.info(f"[{self.device.udid}][连麦] 断言主播连麦失败")        
        return False

    def assert_anchor_host_connect_1v3(self):
        """
        断言主播连麦成功
        """
        # TODO: 实现主播连麦成功断言
        return True

    def assert_anchor_host_disconnected(self):
        """
        断言主播连麦断开
        """
        success = not self["PK按钮"].wait_for_visible(timeout=5, raise_error=False)
        logger.info(f"[{self.device.udid}][连麦] 断言主播连麦断开: {success}")
        return success
