"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-13 15:56:44
FilePath: /global_business_perf/common/tiktok/android/panels/login.py
Description: 
"""
from shoots_android.control import *
from uibase.upath import id_, text_, type_, UPath, visible_

from utils.common.log_utils import logger

class AndroidLoginPanel(Window):
    """
    Android登录页面
    """
    window_spec = {}

    def get_locators(self):
        return {
            "绑定电子邮箱地址-暂时不要": {"path": UPath(text_ == "暂时不要")},
            "保存登录信息-暂时不要": {"path": UPath(text_ == "暂时不要")},
            "访问你的Facebook好友-不允许": {"path": UPath(text_ == "不允许")},
            "让我们快速做个安全检查-关闭": {"path": UPath(desc_ == "关闭")},
            "主页": {"path": UPath(text_ == "主页")},
            "同意并继续": {"path": UPath(text_ == "同意并继续")},
            "使用手机/邮箱/用户名": {'path': UPath(text_ == "使用手机号码/电子邮箱/用户名登录")},
            "下拉": {'path': UPath(desc_ == "向下")},
            "搜索国家和地区": {'path': UPath(id_ == "search_edit_text")},
            "选择国家": {'path': UPath(id_ == "tv_name")},
            "手机号码_1": {'path': UPath(id_ == "inputWithIndicatorEditText", visible_ == True)},
            "手机号码_2": {'path': UPath(type_ == "TuxEditText", visible_ == True)},
            "保存登录信息": {'path': UPath(id_ == "ocl_checkbox_record", visible_ == True)},
            "继续_1": {'path': UPath(text_ == "继续", visible_ == True)},
            "继续_2": {'path': UPath(id_ == "cta_primary", visible_ == True)},
            "短信验证码_1": {'path': UPath(id_ == "inputCodeView")},
            "短信验证码_2": {'path': UPath(type_ == "TuxPinFieldEditText")},
            "发送验证码": {'path': UPath(id_ == "loading_button_text", visible_ == True)},
            "切换账号_1": {'path': UPath(id_ == "preview_img")},
            "切换账号_2": {'path': UPath(id_ == "nav_bar_title")},
            "头像_1": {'path': UPath(id_ == "banner_image")},
            "头像_2": {'path': UPath(id_ == "avatar_layout_view")},
            "添加账号": {'path': UPath(text_ == "添加账号")},
            "已有账号？登录": {'path': UPath(id_ == "tv_footer")},
            "上滑查看更多视频": {'path': UPath(id_ == "tv_strengthen_swipe_up_guide")},
            "登录异常结果指示器": {'path': UPath(id_ == "result_indicator_group_text", visible_ == True)}
        }
    
    def start_step_popup_handle(self):
        """
        开始登录后的弹窗处理
        """
        popup_actions = {
            "保存登录信息-暂时不要": "点击暂时不要保存登录信息",
            "让我们快速做个安全检查-关闭": "点击关闭让我们快速做个安全检查", 
            "访问你的Facebook好友-不允许": "点击不允许访问你的Facebook好友",
            "绑定电子邮箱地址-暂时不要": "点击暂时不要绑定电子邮箱地址",
        }

        # 点击首页前处理弹窗
        while True:
            found_popup = False
            for element_key, log_msg in popup_actions.items():
                if self[element_key].wait_for_visible(timeout=1, raise_error=False):
                    self[element_key].click()
                    logger.info(f"[{self.device.udid}]{log_msg}")
                    found_popup = True
                    break
            if not found_popup:
                break

    def is_login(self):
        """
        判断是否登录
        """
        self.start_step_popup_handle()
        if self["主页"].wait_for_visible(timeout=3):
            self["主页"].click()
        logger.info(f"[{self.device.udid}][登录] 点击主页")

        for account_img in ["头像_1", "头像_2"]:
            if self[account_img].wait_for_visible(timeout=5, raise_error=False):
                logger.info(f"[{self.device.udid}][登录] 检测到已登录状态")
                return True
        else:
            logger.info(f"[{self.device.udid}][登录] 未检测到已登录状态")
            return False
    
    def auto_login(self, udid, account, code, country):
        """
        自动登录，只使用于local_test包
        Args:
            account: 手机号
            code: 验证码
            country: 国家代码(如86)
        Returns:
            bool: 登录是否成功
        """
        import subprocess
        
        cmd = [
            "adb", "-s", udid, "shell", "am", "broadcast",
            "-a", "com.bytedance.ttmock.action_cmd",
            "--es", "cmd", "auto_login",
            "--es", "phone", account,
            "--es", "sms_code", code,
            "--ei", "country", str(country)
        ]
        
        try:
            result = subprocess.run(cmd, shell=False, capture_output=True, text=True, check=True)                        
            if not "success" in result.stdout:
                return False
            time.sleep(6)
            if self["上滑查看更多视频"].wait_for_visible(timeout=3, raise_error=False):
                for _ in range(3):
                    self.scroll(coefficient_y=0.5)
                    time.sleep(1)
                logger.info(f"[{self.device.udid}][登录] 上滑查看更多视频")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"[{self.device.udid}][登录] 自动登录出现异常: {e.stderr}")
            return False

    def is_account_login_correct(self, nick):
        """
        判断登录的账号是否正确
        """
        # 检查两种可能的账号显示位置
        for account_locator in ["切换账号_1", "切换账号_2"]:
            if self[account_locator].wait_for_visible(timeout=3, raise_error=False):
                current_nick = self[account_locator].text
                is_match = current_nick == nick
                if is_match:
                    logger.info(f"[{self.device.udid}][登录] 当前登录账号正确")
                else:
                    logger.info(f"[{self.device.udid}][登录] 当前登录账号[{current_nick}]与目标账号[{nick}]不匹配")
                return is_match
        return False

    def add_account(self):
        """
        添加账号
        """
        for account_locator in ["切换账号_1", "切换账号_2"]:
            if self[account_locator].wait_for_visible(timeout=5, raise_error=False):
                self[account_locator].click()
                logger.info(f"[{self.device.udid}][登录] 点击账号，跳转到添加账号页面")
                break
        self["添加账号"].click()
        logger.info(f"[{self.device.udid}][登录] 点击添加账号按钮")
        self["已有账号？登录"].click()
        logger.info(f"[{self.device.udid}][登录] 点击已有账号登录按钮")

    def login_by_phone_code(self, country, account, code):
        """
        使用手机号+验证码登录
        """ 
        if self["使用手机/邮箱/用户名"].wait_for_visible(timeout=3):
            self["使用手机/邮箱/用户名"].click()
            logger.info(f"[{self.device.udid}][登录] 选择手机号登录方式")

        if self["下拉"].wait_for_visible(timeout=3):
            self["下拉"].click()
            logger.info(f"[{self.device.udid}][登录] 展开国家/地区选择")

        self["搜索国家和地区"].input(country)
        logger.info(f"[{self.device.udid}][登录] 搜索{country}")
        self["选择国家"].click()
        logger.info(f"[{self.device.udid}][登录] 选择国家")

        iphone_login_locators = [self["手机号码_1"], self["手机号码_2"]]
        for locator in iphone_login_locators:
            if locator.wait_for_visible(timeout=5, raise_error=False):
                locator.input(account)
                logger.info(f"[{self.device.udid}][登录] 输入手机号:{account}")
                break

        if self["保存登录信息"].wait_for_visible(timeout=5, raise_error=False):
            self["保存登录信息"].click()
            logger.info(f"[{self.device.udid}][登录] 点击保存登录信息")

        for _ in range(3):
            continue_locators = [self["继续_1"], self["继续_2"], self["发送验证码"]]
            for locator in continue_locators:
                if locator.wait_for_visible(timeout=5, raise_error=False):
                    locator.click()
                    logger.info(f"[{self.device.udid}][登录] 点击继续按钮发送验证码")
                    break
            if not self["登录异常结果指示器"].wait_for_visible(timeout=5, raise_error=False):
                break

        code_locators = [self["短信验证码_1"], self["短信验证码_2"]]
        for locator in code_locators:
            if locator.wait_for_visible(timeout=5, raise_error=False):
                locator.input(code)
                logger.info(f"[{self.device.udid}][登录] 输入验证码:{code}")
                break

        if self["上滑查看更多视频"].wait_for_visible(timeout=10, raise_error=False):
            self.scroll(coefficient_y=0.5)
            logger.info(f"[{self.device.udid}][登录] 上滑查看更多视频")
    
    