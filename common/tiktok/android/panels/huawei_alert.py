
import time

from shoots_android.control import *
from utils.common.log_utils import logger


class HuaweiRiskAlert(Window):
    """
    华为风险提示弹窗,属于系统弹窗,但是on_system_popup无法处理,所以需要单独处理
    重新定义了一个HuaweiRiskAlert, 作为应用的UI来处理
    """
    window_spec = {}

    def get_locators(self):
        return {
            "不再提示": {"path": UPath(id_ == "virus_checkbox")},
            "继续使用": {"path": UPath(text_ == "继续使用")},
            "取消": {"path": UPath(text_ == "取消")}

        }
    
    def click_huawei_risk_alert(self):
        """
        点击华为风险提示弹窗
        """
        if self["不再提示"].wait_for_visible(timeout=5, raise_error=False):
            logger.info(f"[{self.device.udid}]点击华为风险提示弹窗")
            self["不再提示"].click()

            if self["继续使用"].wait_for_visible(timeout=5, raise_error=False):
                self["继续使用"].click()

                if self["取消"].wait_for_visible(timeout=5, raise_error=False):
                    self["取消"].click()

            return True

        return False