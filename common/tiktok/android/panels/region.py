"""
Author: he<PERSON><PERSON><PERSON>.oxep
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/android/panels/region.py
Description: 
"""
import time

from shoots_android.control import *
from utils.common.log_utils import logger


class AndroidRegionPanel(Window):
    """
    区域选择页面
    """
    
    window_spec = {}

    def get_locators(self):
        return {
            "首页区域按钮": {"path": UPath(id_ == "region_button")},
            "区域列表搜索框": {"path": UPath(id_ == "search_edit")},
            "区域列表第一个区域": {"path": UPath(id_ == "recyclerview") / 1},
          }
    
    def switch_region(self, region: str):
        if self["首页区域按钮"].wait_for_visible(timeout=10):
            self["首页区域按钮"].click()
            logger.info(f"[{self.device.udid}][区域] 点击首页区域按钮")
        else:
            logger.info(f"[{self.device.udid}][区域] 未检测到首页区域按钮")
            return
        if self["区域列表搜索框"].wait_for_visible(timeout=5):
            self["区域列表搜索框"].input(region)
            logger.info(f"[{self.device.udid}][区域] 输入区域名称:{region}")
        else:
            logger.info(f"[{self.device.udid}][区域] 未检测到区域列表搜索框")
            return
        if self["区域列表第一个区域"].wait_for_visible(timeout=5):
            self["区域列表第一个区域"].click()
            logger.info(f"[{self.device.udid}][区域] 点击区域列表第一个区域")
        else:
            logger.info(f"[{self.device.udid}][区域] 未检测到区域列表第一个区域")
            return
        time.sleep(5)