"""
Author: hejiabe<PERSON>.oxep <EMAIL>
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/android/panels/live.py
Description: 直播相关页面操作
"""

import time
from shoots_android.control import *
from uibase.upath import id_, text_, UPath
from common.tiktok.android.app import AndroidSystemSettingsApp, AndroidSystemUIApp
from utils.common.log_utils import logger


class AndroidLivePanel(Window):
    """
    直播页面
    """
    window_spec = {}

    def get_locators(self):
        return {
            "上滑查看更多视频": {'path': UPath(text_ == "上滑查看更多视频")},
            "绑定电子邮箱地址-暂时不要": {"path": UPath(text_ == "暂时不要")},
            "保存登录信息-暂时不要": {"path": UPath(text_ == "暂时不要")},
            "访问你的Facebook好友-不允许": {"path": UPath(text_ == "不允许")},
            "让我们快速做个安全检查-关闭": {"path": UPath(desc_ == "关闭")},
            "首页": {"path": UPath(id_ == "main_bottom_button_home")},
            "首页搜索按钮": {"path": UPath(id_ == "rl_tab_container") / 2},
            "清除搜索": {"path": UPath(id_ == "btn_clear_new")},
            "搜索输入框": {"path": UPath(id_ == "et_search_middle")},
            "搜索页搜索按钮": {"path": UPath(id_ == "tv_search_textview")},
            "用户": {"path": UPath(desc_ == "用户")},
            "观看直播": {"path": UPath(desc_ == "观看直播")},
            "主菜单": {"path": UPath(id_ == "main_bottom_button_add") / UPath(type_ == "ImageView", depth=5)},
            "直播标签": {"path": UPath(text_ == "直播")},
            "关闭直播工作室现已上线_1": {"path": UPath(text_ == "暂时不要")},
            "关闭直播工作室现已上线_2": {"path": UPath(id_ == "ttlive_dialog_game_content_close")},
            "确认直播内容不包含敏感信息确认弹窗": {"path": UPath(text_ == "确认")},
            "结束另一台设备上的直播": {"path": UPath(id_ == "positive_button")},
            "语音聊天标签": {"path": UPath(text_ == "语音聊天")},
            "相机标签": {"path": UPath(text_ == "设备相机")},
            "游戏标签": {"path": UPath(text_ == "手机游戏")},
            "恢复直播_1": {"path": UPath(text_ == "恢复")},
            "恢复直播_2": {"path": UPath(id_ == "positive_button")},
            "取消恢复直播_1": {"path": UPath(text_ == "取消")},
            "取消恢复直播_2": {"path": UPath(id_ == "negative_button")},
            "开启直播": {"path": UPath(text_ == "开启直播")},
            "观众关闭直播": {"path": UPath(desc_ == "关闭")},
            "主播关闭直播_1": {"path": UPath(id_ == "live_close_widget_container")},
            "主播关闭直播_2": {"path": UPath(id_ == "lynx_top_layer")},
            "立即结束": {"path": UPath(text_ == "立即结束")},
            "在设置中允许": {"path": UPath(text_ == "在设置中允许")},
            "搜索游戏": {"path": UPath(id_ == "et_search_game")},
            "游戏列表第一个": {"path": UPath(text_ == "王者荣耀")},
            "开始录制并开启直播": {"path": UPath(text_ == "开始录制并开启直播")},
            "游戏直播开始按钮": {"path": UPath(text_ == "开启直播")},
            "屏幕共享设置按钮": {"path": UPath(id_ == "ttlive_dialog_screen_share_setting_button")},
            "直播预览页设置按钮": {"path": UPath(text_ == "设置")},
            "视频质量": {"path": UPath(text_ == "视频质量")},
            "1080p": {"path": UPath(text_ == "1080p")},
            "720p": {"path": UPath(text_ == "720p")},
            "540p": {"path": UPath(text_ == "540p")},
            "480p": {"path": UPath(text_ == "480p")},
            "1080p60": {"path": UPath(text_ == "1080p60")},
            "720p60": {"path": UPath(text_ == "720p60")},
            "720p30": {"path": UPath(text_ == "720p30")},
            "直播预览页美颜按钮": {"path": UPath(text_ == "美颜")},
            "直播预览页增强按钮": {"path": UPath(text_ == "增强")},
            "直播预览页增强美颜状态": {"path": UPath(id_ == "beauty_switch_text")},
            "直播预览页增强美颜开关": {"path": UPath(id_ == "beauty_switch_op2")},
            "手游视频质量选择退出": {"path": UPath(id_ == "icon_close")},
            "屏蔽不受欢迎的评论弹窗-关闭": {"path": UPath(text_ == "关闭")}
        }
        
    def start_step_popup_handle(self):
        """
        开始直播后的弹窗处理
        """
        popup_actions = {
            "保存登录信息-暂时不要": "点击暂时不要保存登录信息",
            "让我们快速做个安全检查-关闭": "点击关闭让我们快速做个安全检查", 
            "访问你的Facebook好友-不允许": "点击不允许访问你的Facebook好友",
            "绑定电子邮箱地址-暂时不要": "点击暂时不要绑定电子邮箱地址",
        }

        # 点击首页前处理弹窗
        while True:
            found_popup = False
            for element_key, log_msg in popup_actions.items():
                if self[element_key].wait_for_visible(timeout=2, raise_error=False):
                    self[element_key].click()
                    logger.info(f"[{self.device.udid}]{log_msg}")
                    found_popup = True
                    break
            if not found_popup:
                break

        if self["首页"].wait_for_visible(timeout=5, raise_error=False):
            self["首页"].click()
            logger.info(f"[{self.device.udid}][直播] 点击首页按钮")

        # 点击首页后再次处理弹窗
        while True:
            found_popup = False
            for element_key, log_msg in popup_actions.items():
                if self[element_key].wait_for_visible(timeout=2, raise_error=False):
                    self[element_key].click()
                    logger.info(f"[{self.device.udid}]{log_msg}")
                    found_popup = True
                    break
            if not found_popup:
                break

    def enter_prepare_live_page(self):
        """
        1. 进入直播准备页面
        2. 处理恢复直播弹窗 (这个弹窗只能在这里处理, 如果点击了恢复, 就无法设置视频质量)
        """
        
        self.app.open_scheme("snssdk1180://openRecord?tab=live&recordOrigin=system")
        logger.info(f"[{self.device.udid}][直播] 等待直播准备页面加载完成")
        time.sleep(6)

        # # 处理恢复直播弹窗
        element_keys = ["取消恢复直播_1", "取消恢复直播_2"]
        for element_key in element_keys:
            if self[element_key].wait_for_visible(timeout=5, raise_error=False):
                self[element_key].click()
                logger.info(f"[{self.device.udid}][直播] 点击{element_key}")
    
    def open_beaty(self):
        """
        开启美颜
        Args:
            enable (bool): 是否开启美颜
        """
        if self["直播预览页美颜按钮"].wait_for_visible(timeout=5, raise_error=False):
            logger.info(f"[{self.device.udid}][直播] 美颜已开启")
            return
        if self["直播预览页增强按钮"].wait_for_visible(timeout=5, raise_error=False):
            self["直播预览页增强按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击增强按钮")
        if self["直播预览页增强美颜状态"].wait_for_visible(timeout=5, raise_error=False):
            if self["直播预览页增强美颜状态"].text == "关闭":
                self["直播预览页增强美颜开关"].click()
                logger.info(f"[{self.device.udid}][直播] 开启增强美颜")

        self.app.get_device().back()
        logger.info(f"[{self.device.udid}][直播] 返回到直播预览页")


    def select_video_quality(self, video_quality):
        """
        选择视频质量
        
        Args:
            quality_type (str, optional): 视频质量，默认1080p. Defaults to "1080p".
        """
        if self["直播预览页设置按钮"].wait_for_visible(timeout=5):
            self["直播预览页设置按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击直播预览页设置按钮")

        for _ in range(3):
            if self["视频质量"].wait_for_visible(timeout=5):
                self["视频质量"].click()
                logger.info(f"[{self.device.udid}][直播] 点击视频质量")
                break
            self.scroll(coefficient_y=0.2)
            logger.info(f"[{self.device.udid}][直播] 未找到视频质量，上滑")

        if self[video_quality].wait_for_visible(timeout=5, raise_error=False):
            self[video_quality].click()
            logger.info(f"[{self.device.udid}][直播] 点击视频质量: {video_quality}")
        self.app.get_device().back()
        logger.info(f"[{self.device.udid}][直播] 点击视频质量返回按钮")
        self.app.get_device().back()
        logger.info(f"[{self.device.udid}][直播] 直播设置返回到预览页")

    def _click_start_live_button(self):
        """
        点击开始直播按钮的通用方法,包含重试逻辑
        
        Returns:
            bool: 是否成功点击开始直播按钮
        """

        # 网络问题经常点击没反应
        for _ in range(3):  # 最多尝试2次
            if self["开启直播"].wait_for_visible(timeout=5, raise_error=False):
                self["开启直播"].click()
                logger.info(f"[{self.device.udid}][直播] 点击开启直播按钮")

                # 处理另一台设备正在直播的情况
                if self["结束另一台设备上的直播"].wait_for_visible(timeout=5, raise_error=False):
                    self["结束另一台设备上的直播"].click()
                    logger.info(f"[{self.device.udid}][直播] 结束另一台设备的直播")
                    continue

                logger.info(f"[{self.device.udid}][直播] 等待直播加载完成: 5s")
                time.sleep(5)

                return True

            logger.info(f"[{self.device.udid}][直播] 未找到开始直播按钮，重试")
            time.sleep(3)
          
        return False

    def audio_live(self):
        """
        语音直播
        """
        self.enter_prepare_live_page()

        if self["语音聊天标签"].wait_for_visible(timeout=5):
            self["语音聊天标签"].click()
            logger.info(f"[{self.device.udid}][直播] 点击语音聊天标签")

        self._click_start_live_button()


    def camera_live(self, video_quality=None, enable_beauty=False):
        """
        相机直播
        Args:
            video_quality (str): 视频质量, 可选值: 1080p, 720p, 540p, 480p
            enable_beauty (bool): 是否开启美颜
        """
        self.enter_prepare_live_page()

        if self["相机标签"].wait_for_visible(timeout=5):
            self["相机标签"].click()
            logger.info(f"[{self.device.udid}][直播] 点击相机标签")

        if video_quality:
            self.select_video_quality(video_quality)
        
        if enable_beauty:
            self.open_beaty()

        self._click_start_live_button()

    def game_live(self, video_quality):
        """
        游戏直播
        
        处理游戏直播的启动流程,包括:
        1. 通过Scheme URL进入准备直播页面
        2. 点击游戏直播标签
        1. 权限设置
        2. 小米机型特殊处理
        3. 系统UI交互
        """
        self.enter_prepare_live_page()

        if self["游戏标签"].wait_for_visible(timeout=5):
            self["游戏标签"].click()
            logger.info(f"[{self.device.udid}][直播] 点击手机游戏标签")
            
        self._click_start_live_button()
        
        device = self.app.get_device()

        # 处理录制权限和系统设置
        # 是否出现了开始录制弹窗
        if not self.is_game_live_start_record_dialog_show(device):
            if not self.is_game_live_setting_allow():
                # 去设置中允许悬浮窗
                self._handle_xiaomi_settings(device)
                
        # 处理系统UI相关设置        
        if not self.is_game_live_start_record_dialog_show(device):
            self.game_live_setting_allow_after_systemui_start_before()
            
        # 启动系统UI并开始直播
        self._start_system_ui(device)
        self.game_live_systemui_after(video_quality)
        
    def _handle_xiaomi_settings(self, device):
        """处理小米机型的特殊设置"""
        system_settings_app = AndroidSystemSettingsApp(device)
        device.freeze_rotation(0)
        
        current_activity = device.current_activity
        if current_activity == "com.android.settings.Settings$OverlaySettingsActivity":
            # 小米高端机设置
            overlay_activity = XiaoMiSettingsOverlaySettingsActivity(root=system_settings_app)
            sub_settings = XiaoMiSubSettingsActivity(root=system_settings_app)
            
            overlay_activity.click_btn_tiktok()
            sub_settings.click_btn_checkbox()
            overlay_activity.click_btn_back()
            overlay_activity.click_btn_back()
            
        elif current_activity == "com.android.settings.Settings$AppDrawOverlaySettingsActivity":
            # 小米中端机设置
            draw_overlay_activity = XiaoMiSettingsAppDrawOverlaySettingsActivity(root=system_settings_app)
            draw_overlay_activity.click_btn_checkbox()
            draw_overlay_activity.click_btn_back()
            
    def _start_system_ui(self, device):
        """启动系统UI"""
        system_ui_app = AndroidSystemUIApp(device)
        system_ui_panel = AndroidSystemUIPanel(root=system_ui_app)
        system_ui_panel.click_btn_start()

    def assert_anchor_live_broadcast(self) -> bool:
        """
        断言主播开启直播成功
        """
        # 手动处理一波弹窗
        self.app.on_app_popup()
        elements = [self["主播关闭直播_1"], self["主播关闭直播_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                logger.info(f"[{self.device.udid}][直播] 断言主播开启直播成功")
                return True
        logger.info(f"[{self.device.udid}][直播] 断言主播开启直播失败")
        return False

    def is_game_live_setting_allow(self):
        """
        是否已经在设置中允许
        """
        if self["在设置中允许"].wait_for_visible(timeout=5, raise_error=False):
            self["在设置中允许"].click()
            logger.info(f"[{self.device.udid}][直播] 点击设置游戏直播权限")
            return False
        return True

    def is_game_live_start_record_dialog_show(self, device):
        """
        是否出现了开始录制弹窗
        """
        success = device.current_activity in ["com.android.systemui.media.MediaProjectionPermissionActivity", "com.android.systemui"]
        logger.info(f"[{self.device.udid}][直播] 是否出现开始录制弹窗: {success}")
        return success

    def game_live_setting_allow_after_systemui_start_before(self):
        """
        游戏直播-设置允许后-系统UI开始前
        """
        
        if self["游戏列表第一个"].wait_for_visible(timeout=10, raise_error=False):
            self["游戏列表第一个"].click()
            logger.info(f"[{self.device.udid}][直播] 选择游戏")
        if self["开始录制并开启直播"].wait_for_visible(timeout=5, raise_error=False):
            self["开始录制并开启直播"].click()
            logger.info(f"[{self.device.udid}][直播] 点击开始录制并开启直播")
        

    def game_live_systemui_after(self, video_quality):
        """
        游戏直播-系统UI开始后
        """
        if video_quality:
            if self["视频质量"].wait_for_visible(timeout=5, raise_error=False):
                self["视频质量"].click()
                logger.info(f"[{self.device.udid}][直播] 点击视频质量")
            if self[video_quality].wait_for_visible(timeout=5, raise_error=False):
                    self[video_quality].click()
                    logger.info(f"[{self.device.udid}][直播] 点击视频质量: {video_quality}")
            if self["手游视频质量选择退出"].wait_for_visible(timeout=5, raise_error=False):
                self["手游视频质量选择退出"].click()
                logger.info(f"[{self.device.udid}][直播] 点击手游视频质量选择退出")

        logger.info(f"[{self.device.udid}][直播] 开始查找[开启直播]按钮...")
        if self["游戏直播开始按钮"].wait_for_visible(timeout=5):   # 这里有坑,被遮挡了但是还是可见的
            time.sleep(5)  # 等待5秒, 确保按钮可见
            self["游戏直播开始按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 已点击[开启直播]按钮")

    def close_live(self):
        """
        关闭直播
        """
        elements = [self["主播关闭直播_1"], self["主播关闭直播_2"]]
        for element in elements:
            if element.wait_for_visible(timeout=5, raise_error=False):
                element.click()
                logger.info(f"[{self.device.udid}][直播] 点击主播关闭直播按钮")
                break

        if self["立即结束"].wait_for_visible(timeout=5):
            self["立即结束"].click()
            logger.info(f"[{self.device.udid}][直播] 点击立即结束按钮")


    def search_anchor_name_enter_anchor_room(self, anchor_name):
        """
        搜索名称进入主播间
        """
        self.app.open_scheme("snssdk1180://feed")  # 进入首页
        for _ in range(2):
            if self["上滑查看更多视频"].existing:
                self.scroll(coefficient_y=0.5)
                logger.info(f"[{self.device.udid}][直播] 上滑查看更多视频")

        if self["首页搜索按钮"].wait_for_visible(timeout=5):
            self["首页搜索按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击搜索按钮")
        if self["搜索输入框"].wait_for_visible(timeout=5):
            self["搜索输入框"].input(anchor_name)
            logger.info(f"[{self.device.udid}][直播] 输入主播名称:{anchor_name}")
        if self["搜索页搜索按钮"].wait_for_visible(timeout=5):
            self["搜索页搜索按钮"].click()
            logger.info(f"[{self.device.udid}][直播] 点击搜索")
        time.sleep(3)

        if self["用户"].wait_for_visible(timeout=5):
            self["用户"].click()
            logger.info(f"[{self.device.udid}][直播] 点击用户标签")
        if self["观看直播"].wait_for_visible(timeout=10):
            self["观看直播"].click()
            logger.info(f"[{self.device.udid}][直播] 点击观看直播")
        time.sleep(5)

    def assert_audience_enter_anchor_room(self):
        """
        断言观众进入主播直播间
        """
        success = self["观众关闭直播"].wait_for_visible(timeout=20, raise_error=False)
        logger.info(f"[{self.device.udid}][直播] 断言观众进入主播直播间: {success}")
        return success

class AndroidSystemUIPanel(Window):
    """
    系统UI页面
    """
    window_spec = {
        "activity": (
            "com.android.systemui.media.MediaProjectionPermissionActivity|"
            "com.android.systemui"
        )
    }

    def get_locators(self):
        return {
            "立即开始": {"path": UPath(id_ == "button1")}
        }

    def click_btn_start(self):
        """
        点击立即开始
        """
        if self["立即开始"].wait_for_visible(timeout=5, raise_error=False):
            self["立即开始"].click()


class XiaoMiSettingsOverlaySettingsActivity(Window):
    """
    小米高端机允许显示在其他应用上层系统设置
    """
    window_spec = {"activity": "com.android.settings.Settings$OverlaySettingsActivity"}

    def get_locators(self):
        return {
            "抖音按钮": {"path": UPath(text_ == "TikTok")},
            "返回按钮": {"path": UPath(desc_ == "返回")},
            "复选框": {"path": UPath(id_ == "checkbox")},
        }

    def click_btn_tiktok(self):
        for i in range(5):
            if self["抖音按钮"].wait_for_visible(timeout=3, raise_error=False):
                self["抖音按钮"].click()
                break
            self.scroll(coefficient_y=0.5)

    def click_btn_back(self):
        if self["返回按钮"].wait_for_visible(timeout=10, raise_error=False):
            self["返回按钮"].click()

    def click_btn_checkbox(self):
        if self["复选框"].wait_for_visible(timeout=10, raise_error=False):
            self["复选框"].click()


class XiaoMiSubSettingsActivity(Window):
    """
    小米高端机允许显示在其他应用上层系统设置
    """
    window_spec = {"activity": "com.android.settings.SubSettings"}

    def get_locators(self):
        return {
            "复选框": {"path": UPath(type_ == "Switch")},
        }

    def click_btn_checkbox(self):
        if self["复选框"].wait_for_visible(timeout=10, raise_error=False):
            self["复选框"].click()


class XiaoMiSettingsAppDrawOverlaySettingsActivity(Window):
    """
    小米中端机允许显示在其他应用上层系统设置
    """
    window_spec = {"activity": "com.android.settings.Settings$AppDrawOverlaySettingsActivity"}

    def get_locators(self):
        return {
            "返回按钮": {"path": UPath(desc_ == "返回")},
            "复选框": {"path": UPath(id_ == "checkbox")},
        }

    def click_btn_back(self):
        if self["返回按钮"].wait_for_visible(timeout=10, raise_error=False):
            self["返回按钮"].click()

    def click_btn_checkbox(self):
        if self["复选框"].wait_for_visible(timeout=10, raise_error=False):
            self["复选框"].click()
