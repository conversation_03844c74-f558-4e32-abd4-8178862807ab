"""
Author: hejiabei.oxep <EMAIL>
Date: 2024-12-15 15:56:44
FilePath: /global_business_perf/common/tiktok/android/panels/guest_host.py
Description: 
"""
import time

from shoots_android.control import *
from utils.common.log_utils import logger


class AndroidGuestHostPanel(Window):
    """
    嘉宾连麦页面
    """
    guest_name_a = ""
    guest_name_b = ""
    guest_name_c = ""
    
    window_spec = {
            "activity": (
                "com.ss.android.ugc.aweme.splash.SplashActivity|"
                "com.ss.android.ugc.aweme.live.LivePlayActivity|"
                "com.ss.android.ugc.aweme.live.LivePlayActivity#1|"
                "com.ss.android.ugc.aweme.live.LivePlayActivity#2|"
                "com.ss.android.ugc.aweme.live.LiveBroadcastSceneWrapperActivity|"
                "com.ss.android.ugc.aweme.live.LiveBroadcastSceneWrapperActivity#1|"
                "com.ss.android.ugc.aweme.live.LiveBroadcastSceneWrapperActivity#2|"
                "com.ss.android.ugc.aweme.live.LiveBroadcastActivity|"
                "com.ss.android.ugc.aweme.live.LiveBroadcastActivity#1|"
                "com.ss.android.ugc.aweme.live.LiveBroadcastActivity#2|"
                "com.ss.android.ugc.aweme.adaptation.saa.SAAActivity"
            )
        }

    def get_locators(self):
        return {
            "嘉宾连麦_1": {"path": UPath(id_ == "fl_request_enhanced_container")},
            "嘉宾连麦_2": {"path": UPath(id_ == "iv_anim")},
            "嘉宾请求上麦": {"path": UPath(text_ == "请求")},
            "+嘉宾_1": {"path": UPath(id_ == "left_container") / 1},
            "+嘉宾_2": {"path": UPath(text_ == "+嘉宾")},
            "接受连麦": {"path": UPath(text_ == "接受")},
            "相机_1": {"path": UPath(text_ == "相机")},
            "相机_2": {"path": UPath(id_ == "right_toolbar_container") / 3},
            "保存_1": {"path": UPath(text_ == "保存")}, 
            "保存_2": {"path": UPath(id_ == "ttlive_text")},
            "互动组件": {"path": UPath(id_ == "right_container") / 1},
            "嘉宾A1": {"path": UPath(text_ == self.guest_name_a)},
            "嘉宾A2": {"path": UPath(type_ == "LayoutView") / 0 / 7 / UPath(id_ == "online_container", depth=5)},
            "嘉宾A3": {"path": UPath(id_ == "ttlive_link_layout_layoutView_interactLayer") / 7 / UPath(id_ == "online_container", depth=5)},
            "嘉宾B1": {"path": UPath(text_ == self.guest_name_b)},
            "嘉宾B2": {"path": UPath(type_ == "LayoutView") / 0 / 9 / UPath(id_ == "online_container", depth=5)},
            "嘉宾B3": {"path": UPath(id_ == "ttlive_link_layout_layoutView_interactLayer") / 9 / UPath(id_ == "online_container", depth=5)},
            "嘉宾C1": {"path": UPath(text_ == self.guest_name_c)},
            "嘉宾C2": {"path": UPath(type_ == "LayoutView") / 0 / 11 / UPath(id_ == "online_container", depth=5)},
            "嘉宾C3": {"path": UPath(id_ == "ttlive_link_layout_layoutView_interactLayer") / 11 / UPath(id_ == "online_container", depth=5)},
        }

    def request_to_host(self):
        """
        嘉宾请求上麦
        """
        guests = [self["嘉宾连麦_1"], self["嘉宾连麦_2"]]
        for guest in guests:
            if guest.wait_for_visible(timeout=5, raise_error=False):
                guest.click()
                logger.info(f"[{self.device.udid}][连麦] 点击嘉宾连麦按钮")
                break

        if self["嘉宾请求上麦"].wait_for_visible(timeout=5):
            self["嘉宾请求上麦"].click()
            logger.info(f"[{self.device.udid}][连麦] 点击请求上麦按钮")

    def accept_guest_connect(self):
        """
        接受嘉宾连麦
        """
        # 最多尝试3次点击+嘉宾按钮
        for _ in range(3):
            elements = [self["+嘉宾_1"], self["+嘉宾_2"]]
            for element in elements:
                if element.wait_for_visible(timeout=5, raise_error=False):
                    element.click()
                    logger.info(f"[{self.device.udid}][连麦] 点击+嘉宾按钮")
                    break

            for _ in range(3):
                if self["接受连麦"].wait_for_visible(timeout=5, raise_error=False):
                    self["接受连麦"].click()
                    logger.info(f"[{self.device.udid}][连麦] 点击接受连麦按钮")
                    return
                self.scroll(coefficient_y=-0.2)

    def open_guest_camera(self):
        """
        嘉宾打开摄像头
        """
        time.sleep(10)
        elments = [self["相机_1"], self["相机_2"]]
        for elment in elments:
            if elment.wait_for_visible(timeout=5, raise_error=False):
                elment.click()
                logger.info(f"[{self.device.udid}][连麦] 点击相机按钮")
                time.sleep(2)
                break

        elments = [self["保存_1"], self["保存_2"]]
        for elment in elments:
            if elment.wait_for_visible(timeout=5, raise_error=False):
                elment.click()
                logger.info(f"[{self.device.udid}][连麦] 点击保存按钮")
                break

    def reject_guest_connect(self):
        """
        拒绝嘉宾连麦
        """
        logger.info(f"[{self.device.udid}][连麦] 开始拒绝嘉宾连麦")
        # TODO: 实现拒绝连麦逻辑
        logger.info(f"[{self.device.udid}][连麦] 已拒绝嘉宾连麦")

    def disconnect_guest_connect(self):
        """
        断开嘉宾连麦
        """
        logger.info(f"[{self.device.udid}][连麦] 开始断开嘉宾连麦")
        # TODO: 实现断开连麦逻辑
        logger.info(f"[{self.device.udid}][连麦] 已断开嘉宾连麦")

    def assert_guest_connect_1v1(self):
        """
        断言嘉宾连麦成功
        """
        success = self["互动组件"].wait_for_visible(timeout=5, raise_error=False)
        logger.info(f"[{self.device.udid}][连麦] 断言嘉宾连麦1v1: {success}")
        return success
    
    def assert_guest_connect_1v3(self):
        """
        断言嘉宾连麦成功
        """
        if not self["互动组件"].wait_for_visible(timeout=10, raise_error=False):
            logger.info(f"[{self.device.udid}][连麦] 互动组件未出现")
            return False

        guests = {
            'A': (self["嘉宾A1"], self["嘉宾A2"], self["嘉宾A3"]),
            'B': (self["嘉宾B1"], self["嘉宾B2"], self["嘉宾B3"]),
            'C': (self["嘉宾C1"], self["嘉宾C2"], self["嘉宾C3"])
        }

        guest_statuses = [
            any(guest.wait_for_visible(timeout=5, raise_error=False) for guest in guest_group)
            for guest_group in guests.values()
        ]

        success = all(guest_statuses)
        logger.info(f"[{self.device.udid}][连麦] 断言嘉宾连麦1v3: {success}")
        return success

    def assert_guest_disconnect(self):
        """
        断言嘉宾连麦断开
        """
        success = not self["互动组件"].wait_for_visible(timeout=5, raise_error=False)
        logger.info(f"[{self.device.udid}][连麦] 断言嘉宾连麦断开: {success}")
        return success


