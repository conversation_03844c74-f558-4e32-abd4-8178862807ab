"""
Author: hejiabei.oxep <EMAIL>
Date: 2025-2-06 15:56:44
FilePath: /global_business_perf/common/tiktok/android/panels/vod.py
Description: 点播安卓面板
"""

import time

from shoots_android.control import *
from uibase.upath import id_, UPath

from utils.common.log_utils import logger


class AndroidVodPanel(Window):
    """
    点播安卓面板
    """
    window_spec = {

    }

    def get_locators(self):
        return {
            "首页": {"path": UPath(text_ == "首页")},
            "主页": {"path": UPath(text_ == "主页")},
            "profile_first_video": {
                "path": UPath(id_ == "feed_list", visible_ == True) / 1 / UPath(id_ == "cover", depth=5)},
            "收藏": {"path": UPath(id_ == "icon", desc_ == "收藏")},
            "收藏-作品": {"path": UPath(~text_ == "作品.*")},
            "favorite_first_video": {"path": UPath(id_ == "feed_list", visible_ == True) / 0 / UPath(id_ == "cover", depth=5)},
            "favorite_2nd_video": {
                "path": UPath(id_ == "feed_list", visible_ == True) / 1 / UPath(id_ == "cover", depth=5)},
            "exit_video": {"path": UPath(id_ == "back_btn")},
            "exit_profile_visitor": {"path": UPath(id_ == "nav_start") / UPath(type_ == "TuxIconView", depth=5)},
            'play_icon': {'type': Control, 'path': UPath(id_ == 'play_iv', visible_ == True)},
            "访问你的Facebook好友-不允许": {"path": UPath(text_ == "不允许")},
            "让我们快速做个安全检查-关闭": {"path": UPath(desc_ == "关闭")},
            "绑定电子邮箱地址-暂时不要": {"path": UPath(text_ == "暂时不要")},
            "保存登录信息-暂时不要": {"path": UPath(text_ == "暂时不要")},
        }

    # def start_step_popup_handle(self):
    #     """
    #     登陆后的弹窗处理
    #     """
    #     popup_actions = {
    #         "保存登录信息-暂时不要": "点击暂时不要保存登录信息",
    #         "让我们快速做个安全检查-关闭": "点击关闭让我们快速做个安全检查",
    #         "访问你的Facebook好友-不允许": "点击不允许访问你的Facebook好友",
    #         "绑定电子邮箱地址-暂时不要": "点击暂时不要绑定电子邮箱地址",
    #     }
    #
    #     # 点击首页前处理弹窗
    #     while True:
    #         found_popup = False
    #         for element_key, log_msg in popup_actions.items():
    #             if self[element_key].wait_for_visible(timeout=2, raise_error=False):
    #                 self[element_key].click()
    #                 logger.info(log_msg)
    #                 found_popup = True
    #                 break
    #         if not found_popup:
    #             break
    #
    #     if self["首页"].wait_for_visible(timeout=5, raise_error=False):
    #         self["首页"].click()
    #         logger.info("[点播] 点击首页按钮")
    #
    #     # 点击首页后再次处理弹窗
    #     while True:
    #         found_popup = False
    #         for element_key, log_msg in popup_actions.items():
    #             if self[element_key].wait_for_visible(timeout=2, raise_error=False):
    #                 self[element_key].click()
    #                 logger.info(log_msg)
    #                 found_popup = True
    #                 break
    #         if not found_popup:
    #             break
    def start_step_popup_handle(self):
        self.app.on_app_popup()
        if self["首页"].wait_for_visible(timeout=5, raise_error=True):
            self["首页"].click()
            logger.info(f"[{self.device.udid}][点播] 点击首页按钮")

    def slide_home_times(self, times=100):
        """
        首页上滑指定次数
        """
        if self["首页"].wait_for_visible(timeout=5, raise_error=False):
            self["首页"].click()
            logger.info(f"[{self.device.udid}][点播] 点击首页按钮")
        for i in range(times):
            self.scroll(coefficient_y=0.5)
            time.sleep(1)

    def open_home_tab(self):
        """
        进入首页并处理弹窗
        """

        self.start_step_popup_handle()
        self.scroll(coefficient_y=0.5)

    def open_profile_tab(self):
        """
        进入主页并处理弹窗
        """

        self.scroll(coefficient_y=0.5)
        if self["主页"].wait_for_visible(timeout=5, raise_error=False):
            self["主页"].click()
            logger.info(f"[{self.device.udid}][点播] 点击主页按钮")

    def profile_first_video(self):
        """
        个人页点击的第一个视频
        """

        if self["profile_first_video"].wait_for_visible(timeout=5, raise_error=False):
            self["profile_first_video"].click()
        else:
            logger.debug(f"[{self.device.udid}][点播] 找不到profile_first_video")

    def favorite_tab(self):
        """
        个人页点击收藏
        """

        if self["收藏"].wait_for_visible(timeout=5, raise_error=False):
            self["收藏"].click()
            logger.info(f"[{self.device.udid}][点播] 点击收藏按钮")
        else:
            logger.debug(f"[{self.device.udid}][点播] 找不到收藏按钮")

        if self["收藏-作品"].wait_for_visible(timeout=5, raise_error=False):
            self["收藏-作品"].click()
            logger.info(f"[{self.device.udid}][点播] 点击收藏-作品")
        else:
            logger.debug(f"[{self.device.udid}][点播] 找不到收藏-作品按钮")

    def favorite_first_video(self):
        """
        个人页点击收藏的第一个视频
        """

        if self["favorite_first_video"].wait_for_visible(timeout=5, raise_error=False):
            self["favorite_first_video"].click()
        else:
            logger.debug(f"[{self.device.udid}][点播] 找不到favorite_first_video")

    def favorite_2nd_video(self):
        """
        个人页点击收藏的第2个视频
        """

        if self["favorite_2nd_video"].wait_for_visible(timeout=5, raise_error=False):
            self["favorite_2nd_video"].click()
        else:
            logger.debug(f"[{self.device.udid}][点播] 找不到favorite_2nd_video")

    def exit_profile_video(self):
        """
        退出个人页视频播放
        """

        if self["exit_video"].wait_for_visible(timeout=5, raise_error=False):
            self["exit_video"].click()
        else:
            logger.debug(f"[{self.device.udid}][点播] 找不到exit_video")

        if not self["收藏"].wait_for_visible(timeout=3, raise_error=False):
            self.open_profile_tab()
            self.favorite_tab()
            logger.info(f"[{self.device.udid}][点播] 重新进入收藏页")



    def swipe_up_times(self, times=1, duration=0):
        """
        首页上滑指定次数
        """
        logger.info(f"[{self.device.udid}][点播] 开始上划 %d 次" % times)
        logger.info(f"[{self.device.udid}][点播] 每次上划后等待 %d 秒" % duration)
        count = 1
        self.app.on_app_popup()
        while count <= times:
            self.scroll(coefficient_y=0.5)
            if duration > 0:
                time.sleep(duration)
            count += 1
            logger.info(f"[{self.device.udid}][点播] 上划第 %d 次" % (count - 1))

    def swipe_down_times(self, times=1, duration=0):
        """
        首页下滑指定次数
        """
        logger.info(f"[{self.device.udid}][点播] 开始下划 %d 次" % times)
        logger.info(f"[{self.device.udid}][点播] 每次下划后等待 %d 秒" % duration)
        count = 1
        while count <= times:
            self.app.on_app_popup()
            self.scroll(coefficient_y=-0.6)
            if duration > 0:
                time.sleep(duration)
            count += 1
            logger.info(f"[{self.device.udid}][点播] 下划第 %d 次" % (count - 1))

    def vod_pause(self):
        """
        暂停播放
        """
        logger.info(f"[{self.device.udid}][点播] 暂停播放")
        if not self['play_icon'].wait_for_visible(timeout=5, raise_error=False):
            self.click()
